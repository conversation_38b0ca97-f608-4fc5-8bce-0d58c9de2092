'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useAuth } from '@/contexts/AuthContext';

export default function JobsPage() {
  const { user, isAuthenticated } = useAuth();
  
  // تحديد نوع المحتوى بناءً على نوع المستخدم
  const isEmployer = user?.userType === 'business' || user?.userType === 'real-estate-office';

  // إعادة توجيه بناءً على نوع المستخدم
  useEffect(() => {
    if (isAuthenticated) {
      if (isEmployer) {
        window.location.href = '/jobs/employers';
      } else {
        window.location.href = '/jobs/individuals';
      }
    }
  }, [isAuthenticated, isEmployer]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-yellow-50/30 relative overflow-hidden">
      {/* لمسات صفراء خفيفة */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute top-20 right-20 w-32 h-32 bg-yellow-200/10 rounded-full blur-xl"></div>
        <div className="absolute bottom-40 left-20 w-48 h-48 bg-yellow-300/8 rounded-full blur-2xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-yellow-100/12 rounded-full blur-3xl"></div>
      </div>

      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* زر العودة */}
        <div className="mb-6">
          <button
            onClick={() => window.location.href = '/'}
            className="flex items-center gap-2 text-primary-600 hover:text-primary-700 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            العودة إلى الصفحة الرئيسية
          </button>
        </div>

        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold text-gray-800 mb-6 flex items-center justify-center gap-4">
            <span
              className="text-6xl transition-all duration-300 hover:scale-110 cursor-pointer"
              style={{
                filter: 'grayscale(1) opacity(0.7) drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.filter = 'grayscale(0) opacity(1) drop-shadow(0 0 12px rgba(34, 197, 94, 0.8))';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.filter = 'grayscale(1) opacity(0.7) drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))';
              }}
            >
              💼
            </span>
            منصة الوظائف
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-12">
            اكتشف آلاف الفرص الوظيفية من أفضل الشركات، أو انشر وظائفك للعثور على أفضل المواهب
          </p>
        </div>

        {/* أزرار التوجيه السريع */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16 relative z-10">
          {/* للباحثين عن عمل */}
          <div className="bg-white/20 backdrop-blur-md rounded-3xl shadow-2xl p-12 hover:shadow-3xl transition-all duration-300 border border-white/30 hover:bg-white/30 group">
            <div className="text-center">
              <div className="w-32 h-32 bg-gradient-to-br from-blue-500 to-blue-600 rounded-3xl flex items-center justify-center mx-auto mb-8 group-hover:scale-110 transition-transform duration-300">
                <span
                  className="text-6xl text-white transition-all duration-300 hover:scale-110 cursor-pointer"
                  style={{
                    filter: 'grayscale(1) opacity(0.4) drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.filter = 'grayscale(0) opacity(1) drop-shadow(0 0 20px rgba(34, 197, 94, 1)) brightness(1.3)';
                    e.currentTarget.style.animation = 'greenGlow 1s infinite alternate';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.filter = 'grayscale(1) opacity(0.4) drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))';
                    e.currentTarget.style.animation = 'none';
                  }}
                >
                  👤
                </span>
              </div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4">للباحثين عن عمل</h2>
              <p className="text-gray-600 mb-8 text-lg">أنشئ سيرتك الذاتية الاحترافية وابحث عن وظيفة أحلامك</p>

              <div className="grid grid-cols-2 gap-4 mb-8 text-sm">
                <div className="bg-blue-50 rounded-lg p-3 hover:bg-blue-100 transition-colors cursor-pointer">
                  <span className="text-blue-600 flex items-center gap-2">
                    <span
                      className="transition-all duration-300 hover:scale-110"
                      style={{
                        filter: 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.filter = 'grayscale(0) opacity(1) drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.filter = 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))';
                      }}
                    >
                      📄
                    </span>
                    إنشاء السيرة الذاتية
                  </span>
                </div>
                <div className="bg-blue-50 rounded-lg p-3 hover:bg-blue-100 transition-colors cursor-pointer">
                  <span className="text-blue-600 flex items-center gap-2">
                    <span
                      className="transition-all duration-300 hover:scale-110"
                      style={{
                        filter: 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.filter = 'grayscale(0) opacity(1) drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.filter = 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))';
                      }}
                    >
                      ⭐
                    </span>
                    الوظائف المميزة
                  </span>
                </div>
                <div className="bg-blue-50 rounded-lg p-3 hover:bg-blue-100 transition-colors cursor-pointer">
                  <span className="text-blue-600 flex items-center gap-2">
                    <span
                      className="transition-all duration-300 hover:scale-110"
                      style={{
                        filter: 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.filter = 'grayscale(0) opacity(1) drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.filter = 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))';
                      }}
                    >
                      🔍
                    </span>
                    البحث المتقدم
                  </span>
                </div>
                <div className="bg-blue-50 rounded-lg p-3 hover:bg-blue-100 transition-colors cursor-pointer">
                  <span className="text-blue-600 flex items-center gap-2">
                    <span
                      className="transition-all duration-300 hover:scale-110"
                      style={{
                        filter: 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.filter = 'grayscale(0) opacity(1) drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.filter = 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))';
                      }}
                    >
                      🎨
                    </span>
                    قوالب متنوعة
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                <Link
                  href="/jobs/all"
                  className="inline-block w-full py-4 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-colors font-bold text-lg"
                >
                  ابدأ البحث عن وظيفة
                </Link>
                <Link
                  href="/jobs/dashboard"
                  className="inline-block w-full py-3 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-colors font-medium text-center"
                >
                  📊 لوحة تحكم الوظائف
                </Link>
              </div>
            </div>
          </div>

          {/* للشركات وأصحاب العمل */}
          <div className="bg-white/20 backdrop-blur-md rounded-3xl shadow-2xl p-12 hover:shadow-3xl transition-all duration-300 border border-white/30 hover:bg-white/30 group">
            <div className="text-center">
              <div className="w-32 h-32 bg-gradient-to-br from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-8 group-hover:scale-110 transition-transform duration-300">
                <span
                  className="text-6xl text-white transition-all duration-300 hover:scale-110 cursor-pointer"
                  style={{
                    filter: 'grayscale(1) opacity(0.4) drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))',
                    transition: 'all 0.3s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.filter = 'grayscale(0) opacity(1) drop-shadow(0 0 20px rgba(34, 197, 94, 1)) brightness(1.3)';
                    e.currentTarget.style.animation = 'greenGlow 1s infinite alternate';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.filter = 'grayscale(1) opacity(0.4) drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))';
                    e.currentTarget.style.animation = 'none';
                  }}
                >
                  🏢
                </span>
              </div>
              <h2 className="text-3xl font-bold text-gray-800 mb-4">للشركات وأصحاب العمل</h2>
              <p className="text-gray-600 mb-8 text-lg">انشر وظائفك واعثر على أفضل المواهب والكفاءات</p>

              <div className="grid grid-cols-2 gap-4 mb-8 text-sm">
                <div className="bg-green-50 rounded-lg p-3 hover:bg-green-100 transition-colors cursor-pointer">
                  <span className="text-green-600 flex items-center gap-2">
                    <span
                      className="transition-all duration-300 hover:scale-110"
                      style={{
                        filter: 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.filter = 'grayscale(0) opacity(1) drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.filter = 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))';
                      }}
                    >
                      ➕
                    </span>
                    نشر الوظائف
                  </span>
                </div>
                <div className="bg-green-50 rounded-lg p-3 hover:bg-green-100 transition-colors cursor-pointer">
                  <span className="text-green-600 flex items-center gap-2">
                    <span
                      className="transition-all duration-300 hover:scale-110"
                      style={{
                        filter: 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.filter = 'grayscale(0) opacity(1) drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.filter = 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))';
                      }}
                    >
                      📊
                    </span>
                    لوحة التحكم
                  </span>
                </div>
                <div className="bg-green-50 rounded-lg p-3 hover:bg-green-100 transition-colors cursor-pointer">
                  <span className="text-green-600 flex items-center gap-2">
                    <span
                      className="transition-all duration-300 hover:scale-110"
                      style={{
                        filter: 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.filter = 'grayscale(0) opacity(1) drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.filter = 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))';
                      }}
                    >
                      📈
                    </span>
                    تحليلات متقدمة
                  </span>
                </div>
                <div className="bg-green-50 rounded-lg p-3 hover:bg-green-100 transition-colors cursor-pointer">
                  <span className="text-green-600 flex items-center gap-2">
                    <span
                      className="transition-all duration-300 hover:scale-110"
                      style={{
                        filter: 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))',
                        transition: 'all 0.3s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.filter = 'grayscale(0) opacity(1) drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.filter = 'grayscale(1) opacity(0.7) drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))';
                      }}
                    >
                      👥
                    </span>
                    إدارة الطلبات
                  </span>
                </div>
              </div>

              <button
                onClick={() => {
                  alert('يرجى تسجيل الدخول أولاً للوصول إلى خدمات الشركات');
                  // يمكن إضافة توجيه لصفحة تسجيل الدخول هنا
                }}
                className="inline-block w-full py-4 bg-green-600 text-white rounded-xl hover:bg-green-700 transition-colors font-bold text-lg"
              >
                ابدأ نشر الوظائف
              </button>
            </div>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white rounded-lg p-4 text-center shadow-sm">
            <div className="text-2xl font-bold text-primary-600">500+</div>
            <div className="text-gray-600 text-sm">وظيفة متاحة</div>
          </div>
          <div className="bg-white rounded-lg p-4 text-center shadow-sm">
            <div className="text-2xl font-bold text-green-600">50+</div>
            <div className="text-gray-600 text-sm">وظيفة مميزة</div>
          </div>
          <div className="bg-white rounded-lg p-4 text-center shadow-sm">
            <div className="text-2xl font-bold text-blue-600">100+</div>
            <div className="text-gray-600 text-sm">شركة</div>
          </div>
          <div className="bg-white rounded-lg p-4 text-center shadow-sm">
            <div className="text-2xl font-bold text-purple-600">2000+</div>
            <div className="text-gray-600 text-sm">مرشح</div>
          </div>
        </div>

        {/* الميزات الرئيسية */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🎯</span>
            </div>
            <h3 className="text-xl font-bold text-gray-800 mb-2">بحث متقدم</h3>
            <p className="text-gray-600">ابحث عن الوظائف المناسبة باستخدام فلاتر متقدمة</p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">📄</span>
            </div>
            <h3 className="text-xl font-bold text-gray-800 mb-2">سيرة ذاتية احترافية</h3>
            <p className="text-gray-600">أنشئ سيرتك الذاتية بسهولة باستخدام أدواتنا المتقدمة</p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">🤝</span>
            </div>
            <h3 className="text-xl font-bold text-gray-800 mb-2">تواصل مباشر</h3>
            <p className="text-gray-600">تواصل مباشرة مع أصحاب العمل والمرشحين</p>
          </div>
        </div>

        {/* MyCv Logo */}
        <div className="mt-16 text-center relative z-10">
          <div className="bg-white/20 backdrop-blur-sm rounded-xl p-6 border border-white/30 inline-block">
            <div className="inline-flex items-center gap-3 text-gray-700">
              <span className="font-medium">مدعوم من قبل</span>
              <div className="bg-white/30 backdrop-blur-sm rounded-lg p-2">
                <img
                  src="/images/mycv logo/MyCv.png"
                  alt="MyCv"
                  className="h-8 w-auto transition-all duration-1000 hover:scale-110"
                  style={{
                    filter: 'drop-shadow(0 0 15px rgba(251, 191, 36, 0.6))',
                    animation: 'pulse 2s infinite'
                  }}
                />
              </div>
              <span className="font-medium">MyCv - منصة متكاملة للسير الذاتية والتوظيف</span>
            </div>
          </div>
        </div>

        <style jsx>{`
          @keyframes pulse {
            0%, 100% {
              filter: drop-shadow(0 0 20px rgba(251, 146, 60, 0.6));
            }
            50% {
              filter: drop-shadow(0 0 30px rgba(251, 146, 60, 0.9));
            }
          }

          @keyframes greenGlow {
            0% {
              filter: grayscale(0) drop-shadow(0 0 20px rgba(34, 197, 94, 1)) brightness(1.2);
            }
            100% {
              filter: grayscale(0) drop-shadow(0 0 30px rgba(34, 197, 94, 1)) brightness(1.4);
            }
          }
        `}</style>
      </main>

      <Footer />
    </div>
  );
}
