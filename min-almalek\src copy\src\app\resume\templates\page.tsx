'use client';

import { useState } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ContactButtons from '@/components/ContactButtons';
import TemplatePreview from '@/components/TemplatePreview';

interface Template {
  id: string;
  name: string;
  description: string;
  preview: string;
  category: 'modern' | 'classic' | 'creative' | 'minimal';
  color: string;
  features: string[];
  suitable: string[];
  premium: boolean;
}

interface TemplateCustomization {
  colorScheme: string[];
  fonts: string[];
  layouts: string[];
  sections: string[];
  languages: string[];
}

interface ExtendedTemplate extends Template {
  customization: TemplateCustomization;
  atsCompatible: boolean;
  multiLanguage: boolean;
  rating: number;
  downloads: number;
  lastUpdated: string;
}

const templates: ExtendedTemplate[] = [
  {
    id: 'executive-pro',
    name: 'التنفيذي المحترف',
    description: 'قالب متقدم للمناصب التنفيذية والإدارية العليا مع تصميم فاخر وأنيق',
    preview: '/templates/executive-pro.jpg',
    category: 'modern',
    color: 'bg-slate-800',
    features: ['تصميم فاخر', 'مناسب للمناصب العليا', 'متوافق مع ATS', 'متعدد اللغات', 'قابل للتخصيص بالكامل'],
    suitable: ['المدراء التنفيذيين', 'كبار المسؤولين', 'الاستشاريين', 'رؤساء الأقسام'],
    premium: true,
    customization: {
      colorScheme: ['أزرق داكن', 'رمادي أنثراسيت', 'أسود كلاسيكي', 'كحلي ملكي'],
      fonts: ['Playfair Display', 'Merriweather', 'Source Sans Pro', 'Roboto'],
      layouts: ['عمود واحد', 'عمودين', 'تخطيط هجين'],
      sections: ['ملخص تنفيذي', 'الإنجازات الرئيسية', 'القيادة والإدارة', 'الجوائز والتقديرات'],
      languages: ['العربية', 'الإنجليزية', 'الفرنسية']
    },
    atsCompatible: true,
    multiLanguage: true,
    rating: 4.9,
    downloads: 2847,
    lastUpdated: '2024-02-15'
  },
  {
    id: 'tech-innovator',
    name: 'المبتكر التقني',
    description: 'قالب حديث مصمم خصيصاً للمطورين والمهندسين والمتخصصين في التكنولوجيا',
    preview: '/templates/tech-innovator.jpg',
    category: 'modern',
    color: 'bg-blue-600',
    features: ['تصميم تقني حديث', 'عرض المشاريع', 'روابط GitHub', 'مخططات المهارات', 'قسم للشهادات التقنية'],
    suitable: ['المطورين', 'مهندسي البرمجيات', 'مصممي UX/UI', 'محللي البيانات', 'مهندسي DevOps'],
    premium: true,
    customization: {
      colorScheme: ['أزرق تقني', 'أخضر برمجي', 'بنفسجي حديث', 'برتقالي ديناميكي'],
      fonts: ['Fira Code', 'Source Code Pro', 'Inter', 'JetBrains Mono'],
      layouts: ['شبكة تقنية', 'تخطيط كود', 'تصميم API'],
      sections: ['المشاريع التقنية', 'المساهمات مفتوحة المصدر', 'الشهادات التقنية', 'المدونة التقنية'],
      languages: ['العربية', 'الإنجليزية']
    },
    atsCompatible: true,
    multiLanguage: true,
    rating: 4.8,
    downloads: 3521,
    lastUpdated: '2024-02-10'
  },
  {
    id: 'creative-artist',
    name: 'الفنان المبدع',
    description: 'قالب إبداعي مذهل للمصممين والفنانين مع معرض أعمال مدمج',
    preview: '/templates/creative-artist.jpg',
    category: 'creative',
    color: 'bg-purple-600',
    features: ['معرض أعمال مدمج', 'تصميم إبداعي فريد', 'ألوان جريئة', 'تخطيط مرن', 'عرض المشاريع الفنية'],
    suitable: ['المصممين الجرافيك', 'الفنانين', 'المصورين', 'مصممي الويب', 'المعماريين'],
    premium: true,
    customization: {
      colorScheme: ['بنفسجي إبداعي', 'وردي فني', 'برتقالي دافئ', 'أخضر طبيعي', 'متدرج ملون'],
      fonts: ['Playfair Display', 'Montserrat', 'Dancing Script', 'Oswald'],
      layouts: ['معرض شبكي', 'تخطيط مجلة', 'تصميم بورتفوليو'],
      sections: ['معرض الأعمال', 'العملاء والمشاريع', 'المعارض والجوائز', 'الأسلوب الفني'],
      languages: ['العربية', 'الإنجليزية', 'الفرنسية']
    },
    atsCompatible: false,
    multiLanguage: true,
    rating: 4.7,
    downloads: 1892,
    lastUpdated: '2024-02-08'
  },
  {
    id: 'medical-professional',
    name: 'المهني الطبي',
    description: 'قالب متخصص للأطباء والممرضين والمهنيين الطبيين مع تركيز على الخبرة السريرية',
    preview: '/templates/medical-professional.jpg',
    category: 'classic',
    color: 'bg-teal-600',
    features: ['تصميم طبي محترف', 'قسم للتخصصات', 'الخبرة السريرية', 'الشهادات الطبية', 'المؤتمرات والأبحاث'],
    suitable: ['الأطباء', 'الممرضين', 'الصيادلة', 'أطباء الأسنان', 'المعالجين الطبيعيين'],
    premium: true,
    customization: {
      colorScheme: ['أزرق طبي', 'أخضر صحي', 'رمادي محايد', 'أبيض نظيف'],
      fonts: ['Source Sans Pro', 'Open Sans', 'Lato', 'Roboto'],
      layouts: ['تخطيط سريري', 'تصميم مستشفى', 'نموذج عيادة'],
      sections: ['التخصص الطبي', 'الخبرة السريرية', 'الأبحاث والمنشورات', 'المؤتمرات الطبية'],
      languages: ['العربية', 'الإنجليزية', 'الفرنسية']
    },
    atsCompatible: true,
    multiLanguage: true,
    rating: 4.8,
    downloads: 2156,
    lastUpdated: '2024-02-12'
  },
  {
    id: 'academic-scholar',
    name: 'الأكاديمي الباحث',
    description: 'قالب أكاديمي شامل للأساتذة والباحثين مع تركيز على المنشورات والأبحاث',
    preview: '/templates/academic-scholar.jpg',
    category: 'classic',
    color: 'bg-indigo-700',
    features: ['تصميم أكاديمي', 'قائمة المنشورات', 'المؤتمرات العلمية', 'المنح البحثية', 'التدريس والإشراف'],
    suitable: ['الأساتذة الجامعيين', 'الباحثين', 'طلاب الدكتوراه', 'العلماء', 'المحاضرين'],
    premium: true,
    customization: {
      colorScheme: ['أزرق أكاديمي', 'رمادي علمي', 'أخضر بحثي', 'كحلي جامعي'],
      fonts: ['Times New Roman', 'Georgia', 'Crimson Text', 'Libre Baskerville'],
      layouts: ['تخطيط أكاديمي', 'نموذج بحثي', 'تصميم جامعي'],
      sections: ['المنشورات العلمية', 'المؤتمرات والندوات', 'المنح البحثية', 'الإشراف الأكاديمي'],
      languages: ['العربية', 'الإنجليزية', 'الفرنسية', 'الألمانية']
    },
    atsCompatible: true,
    multiLanguage: true,
    rating: 4.6,
    downloads: 1543,
    lastUpdated: '2024-02-05'
  },
  {
    id: 'sales-champion',
    name: 'بطل المبيعات',
    description: 'قالب ديناميكي للمختصين في المبيعات والتسويق مع تركيز على الأرقام والإنجازات',
    preview: '/templates/sales-champion.jpg',
    category: 'modern',
    color: 'bg-orange-500',
    features: ['عرض الأرقام والإنجازات', 'مخططات الأداء', 'تصميم ديناميكي', 'قسم العملاء', 'الجوائز والتقديرات'],
    suitable: ['مندوبي المبيعات', 'مديري التسويق', 'مطوري الأعمال', 'مديري الحسابات', 'خبراء CRM'],
    premium: true,
    customization: {
      colorScheme: ['برتقالي نشط', 'أحمر قوي', 'أخضر نجاح', 'أزرق ثقة'],
      fonts: ['Montserrat', 'Open Sans', 'Roboto', 'Lato'],
      layouts: ['تخطيط مبيعات', 'نموذج أداء', 'تصميم نتائج'],
      sections: ['الأرقام والإنجازات', 'العملاء الرئيسيين', 'الجوائز والتقديرات', 'استراتيجيات المبيعات'],
      languages: ['العربية', 'الإنجليزية']
    },
    atsCompatible: true,
    multiLanguage: true,
    rating: 4.7,
    downloads: 2934,
    lastUpdated: '2024-02-14'
  },
  {
    id: 'startup-founder',
    name: 'مؤسس الشركات الناشئة',
    description: 'قالب عصري للمؤسسين ورجال الأعمال مع تركيز على الرؤية والابتكار',
    preview: '/templates/startup-founder.jpg',
    category: 'modern',
    color: 'bg-emerald-600',
    features: ['تصميم ريادي', 'عرض الشركات المؤسسة', 'الاستثمارات والتمويل', 'الرؤية والمهمة', 'الشراكات الاستراتيجية'],
    suitable: ['مؤسسي الشركات', 'رجال الأعمال', 'المستثمرين', 'مديري المنتجات', 'خبراء الابتكار'],
    premium: true,
    customization: {
      colorScheme: ['أخضر ريادي', 'أزرق ابتكار', 'بنفسجي إبداع', 'رمادي عصري'],
      fonts: ['Inter', 'Poppins', 'Nunito Sans', 'Work Sans'],
      layouts: ['تخطيط ريادي', 'نموذج شركة ناشئة', 'تصميم مؤسس'],
      sections: ['الشركات المؤسسة', 'الاستثمارات والتمويل', 'الرؤية والاستراتيجية', 'الشراكات'],
      languages: ['العربية', 'الإنجليزية']
    },
    atsCompatible: true,
    multiLanguage: true,
    rating: 4.8,
    downloads: 1876,
    lastUpdated: '2024-02-11'
  },
  {
    id: 'international-executive',
    name: 'التنفيذي الدولي',
    description: 'قالب عالمي للمهنيين الذين يعملون في بيئات دولية متعددة الثقافات',
    preview: '/templates/international-executive.jpg',
    category: 'modern',
    color: 'bg-blue-800',
    features: ['تصميم عالمي', 'متعدد اللغات', 'الخبرة الدولية', 'الشهادات العالمية', 'المشاريع متعددة الجنسيات'],
    suitable: ['المديرين الدوليين', 'الدبلوماسيين', 'خبراء التجارة الدولية', 'مستشاري الأعمال', 'مديري المشاريع الدولية'],
    premium: true,
    customization: {
      colorScheme: ['أزرق دولي', 'رمادي عالمي', 'أخضر دبلوماسي', 'كحلي رسمي'],
      fonts: ['Source Sans Pro', 'Open Sans', 'Lato', 'Roboto'],
      layouts: ['تخطيط دولي', 'نموذج عالمي', 'تصميم متعدد الثقافات'],
      sections: ['الخبرة الدولية', 'المشاريع العالمية', 'الشهادات الدولية', 'اللغات والثقافات'],
      languages: ['العربية', 'الإنجليزية', 'الفرنسية', 'الألمانية', 'الإسبانية', 'الصينية']
    },
    atsCompatible: true,
    multiLanguage: true,
    rating: 4.9,
    downloads: 1234,
    lastUpdated: '2024-02-13'
  },
  {
    id: 'minimalist-pro',
    name: 'البسيط المحترف',
    description: 'قالب بسيط وأنيق يركز على المحتوى مع لمسة احترافية عصرية',
    preview: '/templates/minimalist-pro.jpg',
    category: 'minimal',
    color: 'bg-gray-700',
    features: ['تصميم نظيف جداً', 'تركيز على المحتوى', 'سهولة القراءة', 'مرونة عالية', 'متوافق مع جميع المجالات'],
    suitable: ['جميع المجالات', 'المبتدئين', 'المهنيين الشباب', 'المغيرين للمهنة', 'الباحثين عن البساطة'],
    premium: false,
    customization: {
      colorScheme: ['رمادي كلاسيكي', 'أسود أنيق', 'أزرق هادئ', 'أخضر طبيعي'],
      fonts: ['Inter', 'Source Sans Pro', 'Open Sans', 'Lato'],
      layouts: ['عمود واحد', 'تخطيط بسيط', 'نموذج نظيف'],
      sections: ['الأساسيات فقط', 'ملخص مختصر', 'المهارات الأساسية'],
      languages: ['العربية', 'الإنجليزية']
    },
    atsCompatible: true,
    multiLanguage: true,
    rating: 4.5,
    downloads: 5672,
    lastUpdated: '2024-02-09'
  },
  {
    id: 'luxury-premium',
    name: 'الفاخر المميز',
    description: 'قالب فاخر للمهنيين في المجالات الراقية مع تصميم أنيق ومتطور',
    preview: '/templates/luxury-premium.jpg',
    category: 'classic',
    color: 'bg-amber-600',
    features: ['تصميم فاخر', 'ألوان ذهبية', 'خطوط أنيقة', 'تخطيط راقي', 'مناسب للمجالات الفاخرة'],
    suitable: ['مديري الفنادق الفاخرة', 'مستشاري الموضة', 'خبراء الجواهر', 'مديري العلامات التجارية الفاخرة', 'المهندسين المعماريين'],
    premium: true,
    customization: {
      colorScheme: ['ذهبي فاخر', 'أسود أنيق', 'بني راقي', 'رمادي بلاتيني'],
      fonts: ['Playfair Display', 'Cormorant Garamond', 'Crimson Text', 'Libre Baskerville'],
      layouts: ['تخطيط فاخر', 'نموذج راقي', 'تصميم أنيق'],
      sections: ['العلامات التجارية الفاخرة', 'المشاريع الراقية', 'الجوائز المرموقة', 'العضويات الحصرية'],
      languages: ['العربية', 'الإنجليزية', 'الفرنسية', 'الإيطالية']
    },
    atsCompatible: true,
    multiLanguage: true,
    rating: 4.6,
    downloads: 987,
    lastUpdated: '2024-02-07'
  }
];

const categories = [
  { id: 'all', name: 'جميع القوالب', icon: '📄' },
  { id: 'modern', name: 'حديث', icon: '🚀' },
  { id: 'classic', name: 'كلاسيكي', icon: '📋' },
  { id: 'creative', name: 'إبداعي', icon: '🎨' },
  { id: 'minimal', name: 'بسيط', icon: '✨' }
];

export default function TemplatesPage() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showPremiumOnly, setShowPremiumOnly] = useState(false);
  const [showATSOnly, setShowATSOnly] = useState(false);
  const [sortBy, setSortBy] = useState<'rating' | 'downloads' | 'newest'>('rating');
  const [previewTemplate, setPreviewTemplate] = useState<ExtendedTemplate | null>(null);

  const filteredTemplates = templates
    .filter(template => {
      if (selectedCategory !== 'all' && template.category !== selectedCategory) {
        return false;
      }
      if (showPremiumOnly && !template.premium) {
        return false;
      }
      if (showATSOnly && !template.atsCompatible) {
        return false;
      }
      return true;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'downloads':
          return b.downloads - a.downloads;
        case 'newest':
          return new Date(b.lastUpdated).getTime() - new Date(a.lastUpdated).getTime();
        default:
          return 0;
      }
    });

  const TemplateCard = ({ template }: { template: ExtendedTemplate }) => (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
      {/* Preview Image */}
      <div className="relative h-64 bg-gray-100 overflow-hidden">
        <div className={`w-full h-full ${template.color} opacity-20`}></div>
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-6xl opacity-50">📄</div>
        </div>

        {/* Premium Badge */}
        {template.premium && (
          <div className="absolute top-4 right-4 bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-xs font-bold">
            ⭐ مميز
          </div>
        )}

        {/* Overlay */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <button
              onClick={() => setPreviewTemplate(template)}
              className="bg-white text-gray-800 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors"
            >
              👁️ معاينة
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-xl font-bold text-gray-800">{template.name}</h3>
          <div className="flex items-center gap-2">
            <span className={`w-4 h-4 ${template.color} rounded-full`}></span>
            {template.atsCompatible && (
              <span className="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full font-medium">
                ATS
              </span>
            )}
          </div>
        </div>

        <p className="text-gray-600 text-sm mb-4">{template.description}</p>

        {/* Rating and Stats */}
        <div className="flex items-center justify-between mb-4 text-sm">
          <div className="flex items-center gap-1">
            <span className="text-yellow-500">⭐</span>
            <span className="font-medium">{template.rating}</span>
            <span className="text-gray-500">({template.downloads.toLocaleString()} تحميل)</span>
          </div>
          <span className="text-gray-500">محدث: {template.lastUpdated}</span>
        </div>

        {/* Features */}
        <div className="mb-4">
          <h4 className="text-sm font-semibold text-gray-700 mb-2">المميزات:</h4>
          <div className="flex flex-wrap gap-1">
            {template.features.slice(0, 3).map((feature, index) => (
              <span
                key={index}
                className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
              >
                {feature}
              </span>
            ))}
            {template.features.length > 3 && (
              <span className="text-xs text-gray-400 px-2 py-1">
                +{template.features.length - 3}
              </span>
            )}
          </div>
        </div>

        {/* Suitable For */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-700 mb-2">مناسب لـ:</h4>
          <div className="flex flex-wrap gap-1">
            {template.suitable.slice(0, 2).map((field, index) => (
              <span
                key={index}
                className={`text-xs ${template.color} text-white px-2 py-1 rounded-full`}
              >
                {field}
              </span>
            ))}
            {template.suitable.length > 2 && (
              <span className="text-xs text-gray-400 px-2 py-1">
                +{template.suitable.length - 2}
              </span>
            )}
          </div>
        </div>

        {/* Customization Options */}
        <div className="mb-4">
          <h4 className="text-sm font-semibold text-gray-700 mb-2">خيارات التخصيص:</h4>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="flex items-center gap-1">
              <span>🎨</span>
              <span>{template.customization.colorScheme.length} ألوان</span>
            </div>
            <div className="flex items-center gap-1">
              <span>📝</span>
              <span>{template.customization.fonts.length} خطوط</span>
            </div>
            <div className="flex items-center gap-1">
              <span>📐</span>
              <span>{template.customization.layouts.length} تخطيطات</span>
            </div>
            <div className="flex items-center gap-1">
              <span>🌍</span>
              <span>{template.customization.languages.length} لغات</span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <Link
            href={`/resume/create?template=${template.id}`}
            className={`flex-1 ${template.color} text-white px-3 py-2 rounded-lg font-medium hover:opacity-90 transition-opacity text-center text-sm`}
          >
            استخدم القالب
          </Link>
          <button
            onClick={() => setPreviewTemplate(template)}
            className="px-3 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-colors"
            title="معاينة القالب"
          >
            👁️
          </button>
          <Link
            href={`/resume/customize?template=${template.id}`}
            className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            title="تخصيص القالب"
          >
            ⚙️
          </Link>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            🎨 قوالب السيرة الذاتية
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            اختر من مجموعة متنوعة من القوالب الاحترافية المصممة خصيصاً لمختلف المجالات والتخصصات
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-primary-600 mb-2">{templates.length}</div>
            <div className="text-gray-600">قالب متاح</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-green-600 mb-2">4</div>
            <div className="text-gray-600">فئات مختلفة</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-blue-600 mb-2">100%</div>
            <div className="text-gray-600">قابل للتخصيص</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-purple-600 mb-2">ATS</div>
            <div className="text-gray-600">متوافق مع أنظمة التوظيف</div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-xl shadow-md p-6 mb-8">
          <div className="space-y-6">
            {/* Categories */}
            <div>
              <h3 className="text-lg font-semibold text-gray-800 mb-3">الفئات</h3>
              <div className="flex flex-wrap gap-2">
                {categories.map(category => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {category.icon} {category.name}
                  </button>
                ))}
              </div>
            </div>

            {/* Advanced Filters */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Sort By */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">ترتيب حسب</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as 'rating' | 'downloads' | 'newest')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="rating">التقييم الأعلى</option>
                  <option value="downloads">الأكثر تحميلاً</option>
                  <option value="newest">الأحدث</option>
                </select>
              </div>

              {/* Filters */}
              <div className="space-y-3">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={showPremiumOnly}
                    onChange={(e) => setShowPremiumOnly(e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="text-sm text-gray-700">القوالب المميزة فقط ⭐</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={showATSOnly}
                    onChange={(e) => setShowATSOnly(e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="text-sm text-gray-700">متوافق مع ATS فقط ✅</span>
                </label>
              </div>

              {/* Stats */}
              <div className="text-sm text-gray-600">
                <div className="mb-1">
                  <span className="font-medium">{filteredTemplates.length}</span> قالب متاح
                </div>
                <div className="mb-1">
                  <span className="font-medium">{templates.filter(t => t.premium).length}</span> قالب مميز
                </div>
                <div>
                  <span className="font-medium">{templates.filter(t => t.atsCompatible).length}</span> متوافق مع ATS
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {filteredTemplates.map(template => (
            <TemplateCard key={template.id} template={template} />
          ))}
        </div>

        {/* No Results */}
        {filteredTemplates.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد قوالب مطابقة</h3>
            <p className="text-gray-600 mb-4">جرب تغيير الفلاتر أو تصفح جميع القوالب</p>
            <button
              onClick={() => {
                setSelectedCategory('all');
                setShowPremiumOnly(false);
                setShowATSOnly(false);
                setSortBy('rating');
              }}
              className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
            >
              إعادة تعيين جميع الفلاتر
            </button>
          </div>
        )}

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-primary-600 to-blue-600 rounded-2xl text-white p-8 text-center">
          <h2 className="text-3xl font-bold mb-4">لم تجد القالب المناسب؟</h2>
          <p className="text-xl mb-6 text-primary-100">
            ابدأ بقالب فارغ وخصص سيرتك الذاتية بالطريقة التي تريدها
          </p>
          <Link
            href="/resume/create"
            className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-flex items-center gap-2"
          >
            📄 ابدأ من الصفر
          </Link>
        </div>
      </main>

      <Footer />

      {/* Template Preview Modal */}
      {previewTemplate && (
        <TemplatePreview
          template={previewTemplate}
          isOpen={!!previewTemplate}
          onClose={() => setPreviewTemplate(null)}
        />
      )}
    </div>
  );
}
