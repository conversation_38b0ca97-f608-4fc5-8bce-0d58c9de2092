'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { AdAlgorithm, subscriptionTiers } from '@/lib/adAlgorithm';
import { AdBadge } from '@/components/VerificationBadge';
import { determineUserBadge } from '@/lib/verification';
import StarRating from './StarRating';

// بيانات تجريبية للإعلانات مع أنواع اشتراكات مختلفة
const sampleAds = [
  {
    id: 1,
    title: 'شقة فاخرة في المزة الفيلات الغربية',
    price: 150000000,
    location: { governorate: 'دمشق', area: 'المزة' },
    category: 'شقق',
    images: ['image1.jpg', 'image2.jpg', 'image3.jpg'],
    description: 'شقة فاخرة مع إطلالة رائعة وتشطيبات عالية الجودة',
    createdAt: new Date().toISOString(),
    views: 250,
    favoritesCount: 15,
    messagesCount: 8,
    seller: {
      name: 'أحمد محمد',
      subscription: 'diamond',
      rating: 4.9,
      phone: '+963988123456',
      isVerified: true
    }
  },
  {
    id: 2,
    title: 'فيلا مستقلة في دمشق الجديدة',
    price: 300000000,
    location: { governorate: 'دمشق', area: 'دمشق الجديدة' },
    category: 'فلل',
    images: ['image1.jpg', 'image2.jpg'],
    description: 'فيلا مستقلة بحديقة واسعة',
    createdAt: new Date(Date.now() - 86400000).toISOString(),
    views: 180,
    favoritesCount: 12,
    messagesCount: 5,
    seller: {
      name: 'سارة أحمد',
      subscription: 'platinum',
      rating: 4.7,
      phone: '+963988654321',
      isVerified: true
    }
  },
  {
    id: 3,
    title: 'محل تجاري في شارع الحمرا',
    price: 80000000,
    location: { governorate: 'دمشق', area: 'الحمرا' },
    category: 'محلات',
    images: ['image1.jpg'],
    description: 'محل تجاري في موقع ممتاز',
    createdAt: new Date(Date.now() - 172800000).toISOString(),
    views: 120,
    favoritesCount: 8,
    messagesCount: 3,
    seller: {
      name: 'محمد علي',
      subscription: 'gold',
      rating: 4.5,
      phone: '+963988789123',
      isVerified: false
    }
  },
  {
    id: 4,
    title: 'شقة للبيع في الميدان',
    price: 75000000,
    location: { governorate: 'دمشق', area: 'الميدان' },
    category: 'شقق',
    images: ['image1.jpg', 'image2.jpg'],
    description: 'شقة في منطقة حيوية',
    createdAt: new Date(Date.now() - 259200000).toISOString(),
    views: 90,
    favoritesCount: 5,
    messagesCount: 2,
    seller: {
      name: 'فاطمة حسن',
      subscription: 'silver',
      rating: 4.2,
      phone: '+963988456789',
      isVerified: false
    }
  },
  {
    id: 5,
    title: 'شقة اقتصادية في جرمانا',
    price: 45000000,
    location: { governorate: 'ريف دمشق', area: 'جرمانا' },
    category: 'شقق',
    images: ['image1.jpg'],
    description: 'شقة بسعر مناسب',
    createdAt: new Date(Date.now() - 345600000).toISOString(),
    views: 60,
    favoritesCount: 3,
    messagesCount: 1,
    seller: {
      name: 'خالد يوسف',
      subscription: 'free',
      rating: 4.0,
      phone: '+963988321654',
      isVerified: false
    }
  }
];

export default function PriorityAdsSection() {
  const [sortedAds, setSortedAds] = useState<any[]>([]);
  const [selectedTier, setSelectedTier] = useState<string>('all');
  const [showStats, setShowStats] = useState(false);

  useEffect(() => {
    // ترتيب الإعلانات باستخدام الخوارزمية
    let adsToSort = sampleAds;
    
    if (selectedTier !== 'all') {
      adsToSort = sampleAds.filter(ad => ad.seller.subscription === selectedTier);
    }
    
    const algorithSortedAds = AdAlgorithm.sortAdsByAlgorithm(adsToSort);
    setSortedAds(algorithSortedAds);
  }, [selectedTier]);

  const getSubscriptionIcon = (subscription: string) => {
    const icons = {
      diamond: '💎',
      platinum: '🥈',
      gold: '🥇',
      silver: '🥈',
      bronze: '🥉',
      free: '📝'
    };
    return icons[subscription as keyof typeof icons] || '📝';
  };

  const getSubscriptionColor = (subscription: string) => {
    const colors = {
      diamond: 'from-purple-600 to-purple-800',
      platinum: 'from-gray-600 to-gray-800',
      gold: 'from-yellow-600 to-yellow-800',
      silver: 'from-gray-400 to-gray-600',
      bronze: 'from-orange-600 to-orange-800',
      free: 'from-gray-300 to-gray-500'
    };
    return colors[subscription as keyof typeof colors] || 'from-gray-300 to-gray-500';
  };

  const stats = AdAlgorithm.getAlgorithmStats(sampleAds);

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">
            الإعلانات مرتبة حسب الأولوية
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            نظام ذكي لترتيب الإعلانات حسب نوع الاشتراك وجودة الإعلان وتفاعل المستخدمين
          </p>
        </div>

        {/* Subscription Filters */}
        <div className="flex flex-wrap justify-center gap-3 mb-8">
          <button
            onClick={() => setSelectedTier('all')}
            className={`px-4 py-2 rounded-full font-medium transition-colors ${
              selectedTier === 'all'
                ? 'bg-blue-600 text-white'
                : 'bg-white text-gray-600 hover:bg-gray-100'
            }`}
          >
            جميع الإعلانات
          </button>
          
          {subscriptionTiers.map((tier) => (
            <button
              key={tier.id}
              onClick={() => setSelectedTier(tier.id)}
              className={`px-4 py-2 rounded-full font-medium transition-colors flex items-center gap-2 ${
                selectedTier === tier.id
                  ? `bg-gradient-to-r ${getSubscriptionColor(tier.id)} text-white`
                  : 'bg-white text-gray-600 hover:bg-gray-100'
              }`}
            >
              <span>{getSubscriptionIcon(tier.id)}</span>
              {tier.name}
            </button>
          ))}
        </div>

        {/* Stats Toggle */}
        <div className="text-center mb-8">
          <button
            onClick={() => setShowStats(!showStats)}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            {showStats ? 'إخفاء الإحصائيات' : 'عرض إحصائيات الخوارزمية'}
          </button>
        </div>

        {/* Algorithm Stats */}
        {showStats && (
          <div className="bg-white rounded-xl p-6 mb-8 shadow-lg">
            <h3 className="text-xl font-bold text-gray-800 mb-4">إحصائيات الخوارزمية</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.totalAds}</div>
                <div className="text-gray-600">إجمالي الإعلانات</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{stats.averageScore}</div>
                <div className="text-gray-600">متوسط النقاط</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {stats.subscriptionDistribution.filter(s => s.tier !== 'مجاني').reduce((sum, s) => sum + s.count, 0)}
                </div>
                <div className="text-gray-600">اشتراكات مدفوعة</div>
              </div>
            </div>
          </div>
        )}

        {/* Ads Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {sortedAds.map((ad, index) => (
            <div
              key={ad.id}
              className={`bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border-2 ${
                index === 0 ? 'border-yellow-400 ring-2 ring-yellow-200' :
                index === 1 ? 'border-gray-400 ring-2 ring-gray-200' :
                index === 2 ? 'border-orange-400 ring-2 ring-orange-200' :
                'border-gray-200'
              }`}
            >
              {/* Priority Badge */}
              {index < 3 && (
                <div className="absolute top-2 left-2 z-10">
                  <div className={`px-2 py-1 rounded-full text-white text-xs font-bold ${
                    index === 0 ? 'bg-yellow-500' :
                    index === 1 ? 'bg-gray-500' :
                    'bg-orange-500'
                  }`}>
                    {index === 0 ? '🥇 الأول' : index === 1 ? '🥈 الثاني' : '🥉 الثالث'}
                  </div>
                </div>
              )}

              {/* Image */}
              <div className="relative h-48 bg-gray-200 flex items-center justify-center">
                <span className="text-gray-400 text-4xl">🏠</span>
                
                {/* Subscription Badge */}
                <div className="absolute top-3 right-3">
                  <span className={`px-2 py-1 rounded-full text-white text-xs font-medium bg-gradient-to-r ${getSubscriptionColor(ad.seller.subscription)}`}>
                    {getSubscriptionIcon(ad.seller.subscription)} {subscriptionTiers.find(t => t.id === ad.seller.subscription)?.name || 'مجاني'}
                  </span>
                </div>

                {/* Priority Score */}
                {ad.priority && (
                  <div className="absolute bottom-3 left-3">
                    <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full font-medium">
                      ⭐ {ad.priority.score.toFixed(1)}
                    </span>
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="p-4">
                <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2">
                  {ad.title}
                </h3>
                
                <div className="text-2xl font-bold text-green-600 mb-2">
                  {ad.price.toLocaleString()} ل.س
                </div>
                
                <div className="text-sm text-gray-600 mb-3">
                  📍 {ad.location.governorate} - {ad.location.area}
                </div>

                {/* Seller Info */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">{ad.seller.name}</span>
                    <AdBadge
                      userBadges={determineUserBadge(
                        ad.seller.subscription,
                        5,
                        ad.seller.rating,
                        12,
                        ad.seller.isVerified
                      )}
                      size="xs"
                    />
                  </div>
                  <StarRating rating={ad.seller.rating} size="xs" showValue={true} />
                </div>

                {/* Stats */}
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>👁️ {ad.views}</span>
                  <span>❤️ {ad.favoritesCount}</span>
                  <span>💬 {ad.messagesCount}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* View More */}
        <div className="text-center mt-10">
          <Link
            href="/ads"
            className="inline-flex items-center px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            عرض جميع الإعلانات
            <span className="mr-2">→</span>
          </Link>
        </div>
      </div>
    </section>
  );
}
