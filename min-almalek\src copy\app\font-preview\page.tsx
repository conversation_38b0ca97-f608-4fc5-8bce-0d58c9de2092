'use client';

import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function FontPreviewPage() {
  const fontOptions = [
    {
      name: 'Cairo - عصري نظيف',
      fontFamily: 'Cairo, sans-serif',
      weight: '800',
      color: '#059669',
      style: { letterSpacing: '1px' }
    },
    {
      name: '<PERSON><PERSON> - <PERSON>لاسيكي أنيق',
      fontFamily: 'Amiri, serif',
      weight: '700',
      color: '#1e40af',
      style: { textShadow: '0 2px 4px rgba(0,0,0,0.1)' }
    },
    {
      name: '<PERSON><PERSON><PERSON> - حديث',
      fontFamily: 'Tajawal, sans-serif',
      weight: '700',
      color: '#7c3aed',
      style: {}
    },
    {
      name: 'Almarai - بسيط',
      fontFamily: 'Almarai, sans-serif',
      weight: '800',
      color: '#dc2626',
      style: {}
    },
    {
      name: '<PERSON><PERSON> كوفي عصري',
      fontFamily: '<PERSON>em <PERSON>, sans-serif',
      weight: '600',
      color: '#ea580c',
      style: {}
    },
    {
      name: 'Scheherazade - تقليدي',
      fontFamily: 'Scheherazade New, serif',
      weight: '700',
      color: '#0891b2',
      style: {}
    }
  ];

  const textVariations = [
    'من المالك',
    'مِن المالِك',
    'مِنَ المالِكِ',
    'من | المالك',
    'من • المالك',
    'من ◆ المالك',
    'من ★ المالك'
  ];

  const colorCombinations = [
    { text1: '#fbbf24', text2: '#10b981', name: 'أصفر + أخضر' },
    { text1: '#3b82f6', text2: '#1e40af', name: 'أزرق فاتح + داكن' },
    { text1: '#ef4444', text2: '#dc2626', name: 'أحمر فاتح + داكن' },
    { text1: '#8b5cf6', text2: '#7c3aed', name: 'بنفسجي فاتح + داكن' },
    { text1: '#10b981', text2: '#059669', name: 'أخضر فاتح + داكن' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">معاينة خطوط "من المالك"</h1>
            <p className="text-gray-600">اختر الخط والتنسيق المناسب لشعار الموقع</p>
          </div>

          {/* أنواع الخطوط */}
          <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">أنواع الخطوط</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {fontOptions.map((font, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                  <h3 className="text-sm text-gray-600 mb-3">{font.name}</h3>
                  <div 
                    className="text-3xl font-bold mb-2"
                    style={{
                      fontFamily: font.fontFamily,
                      fontWeight: font.weight,
                      color: font.color,
                      ...font.style
                    }}
                  >
                    من المالك
                  </div>
                  <div className="text-xs text-gray-500">
                    {font.fontFamily} • {font.weight} • {font.color}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* طرق الكتابة */}
          <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">طرق الكتابة</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {textVariations.map((text, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                  <div className="text-2xl font-bold text-primary-600 mb-2" style={{ fontFamily: 'Cairo, sans-serif' }}>
                    {text}
                  </div>
                  <div className="text-xs text-gray-500">
                    نمط {index + 1}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* تركيبات الألوان */}
          <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">تركيبات الألوان</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {colorCombinations.map((combo, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-6 text-center hover:shadow-md transition-shadow">
                  <div className="text-2xl font-bold mb-2" style={{ fontFamily: 'Cairo, sans-serif' }}>
                    <span style={{ color: combo.text1 }}>من</span>
                    {' '}
                    <span style={{ color: combo.text2 }}>المالك</span>
                  </div>
                  <div className="text-xs text-gray-500">
                    {combo.name}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* تأثيرات خاصة */}
          <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">تأثيرات خاصة</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              
              {/* تدرج لوني */}
              <div className="border border-gray-200 rounded-lg p-6 text-center">
                <h3 className="text-sm text-gray-600 mb-3">تدرج لوني</h3>
                <div 
                  className="text-3xl font-bold mb-2"
                  style={{
                    fontFamily: 'Cairo, sans-serif',
                    background: 'linear-gradient(45deg, #fbbf24, #10b981)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    backgroundClip: 'text'
                  }}
                >
                  من المالك
                </div>
              </div>

              {/* ظل ملون */}
              <div className="border border-gray-200 rounded-lg p-6 text-center">
                <h3 className="text-sm text-gray-600 mb-3">ظل ملون</h3>
                <div 
                  className="text-3xl font-bold mb-2"
                  style={{
                    fontFamily: 'Cairo, sans-serif',
                    color: '#1e40af',
                    textShadow: '2px 2px 4px rgba(16, 185, 129, 0.5)'
                  }}
                >
                  من المالك
                </div>
              </div>

              {/* حدود */}
              <div className="border border-gray-200 rounded-lg p-6 text-center">
                <h3 className="text-sm text-gray-600 mb-3">حدود ملونة</h3>
                <div 
                  className="text-3xl font-bold mb-2"
                  style={{
                    fontFamily: 'Cairo, sans-serif',
                    color: '#ffffff',
                    WebkitTextStroke: '2px #10b981'
                  }}
                >
                  من المالك
                </div>
              </div>

              {/* توهج */}
              <div className="border border-gray-200 rounded-lg p-6 text-center bg-gray-900">
                <h3 className="text-sm text-gray-300 mb-3">توهج</h3>
                <div 
                  className="text-3xl font-bold mb-2"
                  style={{
                    fontFamily: 'Cairo, sans-serif',
                    color: '#ffffff',
                    textShadow: '0 0 10px #10b981, 0 0 20px #10b981, 0 0 30px #10b981'
                  }}
                >
                  من المالك
                </div>
              </div>
            </div>
          </div>

          {/* التوصيات */}
          <div className="bg-gradient-to-r from-primary-50 to-green-50 rounded-xl p-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">🏆 التوصيات</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              
              <div className="bg-white rounded-lg p-6 border-2 border-primary-200">
                <h3 className="font-bold text-primary-600 mb-3">للاستخدام العام</h3>
                <div className="text-2xl font-bold text-primary-600 mb-2" style={{ fontFamily: 'Cairo, sans-serif' }}>
                  من المالك
                </div>
                <p className="text-sm text-gray-600">خط Cairo - واضح وعصري</p>
              </div>

              <div className="bg-white rounded-lg p-6 border-2 border-green-200">
                <h3 className="font-bold text-green-600 mb-3">للمناسبات الرسمية</h3>
                <div className="text-2xl font-bold text-green-600 mb-2" style={{ fontFamily: 'Amiri, serif' }}>
                  مِن المالِك
                </div>
                <p className="text-sm text-gray-600">خط Amiri - كلاسيكي أنيق</p>
              </div>

              <div className="bg-white rounded-lg p-6 border-2 border-yellow-200">
                <h3 className="font-bold text-yellow-600 mb-3">للتميز والإبداع</h3>
                <div 
                  className="text-2xl font-bold mb-2"
                  style={{
                    fontFamily: 'Cairo, sans-serif',
                    background: 'linear-gradient(45deg, #fbbf24, #10b981)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent'
                  }}
                >
                  من • المالك
                </div>
                <p className="text-sm text-gray-600">تدرج لوني مع فاصل</p>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
