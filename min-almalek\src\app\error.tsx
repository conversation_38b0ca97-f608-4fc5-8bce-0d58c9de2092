'use client';

import { useEffect } from 'react';
import Link from 'next/link';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    console.error('خطأ في التطبيق:', error);
  }, [error]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md mx-auto text-center px-4">
        <div className="text-8xl mb-8">⚠️</div>
        
        <h1 className="text-3xl font-bold text-gray-800 mb-4">
          حدث خطأ غير متوقع
        </h1>
        
        <p className="text-gray-600 mb-8 leading-relaxed">
          نعتذر، حدث خطأ أثناء تحميل هذه الصفحة. 
          يرجى المحاولة مرة أخرى أو العودة إلى الصفحة الرئيسية.
        </p>
        
        <div className="space-y-4">
          <button
            onClick={reset}
            className="block w-full bg-primary-600 text-white py-3 px-6 rounded-lg hover:bg-primary-700 transition-colors font-semibold"
          >
            المحاولة مرة أخرى
          </button>
          
          <Link
            href="/"
            className="block w-full border border-primary-600 text-primary-600 py-3 px-6 rounded-lg hover:bg-primary-50 transition-colors font-semibold"
          >
            العودة للصفحة الرئيسية
          </Link>
        </div>
        
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-8 text-left">
            <summary className="cursor-pointer text-sm text-gray-500 mb-2">
              تفاصيل الخطأ (للمطورين)
            </summary>
            <pre className="bg-gray-100 p-4 rounded-lg text-xs overflow-auto">
              {error.message}
              {error.stack && (
                <>
                  {'\n\n'}
                  {error.stack}
                </>
              )}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
}
