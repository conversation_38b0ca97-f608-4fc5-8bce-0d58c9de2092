import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';
import { SupportService } from '@/lib/support';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const query = searchParams.get('query');
    const category = searchParams.get('category');

    // الحصول على الأسئلة الشائعة (لا يحتاج مصادقة)
    if (action === 'faqs') {
      const faqs = SupportService.getFAQs(category || undefined);
      return NextResponse.json({
        success: true,
        data: faqs
      });
    }

    // البحث في الأسئلة الشائعة (لا يحتاج مصادقة)
    if (action === 'search_faqs' && query) {
      const results = SupportService.searchFAQs(query);
      return NextResponse.json({
        success: true,
        data: results
      });
    }

    // باقي العمليات تحتاج مصادقة
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    switch (action) {
      case 'tickets':
        const tickets = SupportService.getUserTickets(sessionResult.data.id);
        return NextResponse.json({
          success: true,
          data: tickets
        });

      case 'ticket':
        const ticketId = searchParams.get('ticketId');
        if (!ticketId) {
          return NextResponse.json(
            { success: false, error: 'معرف التذكرة مطلوب' },
            { status: 400 }
          );
        }

        const ticket = SupportService.getTicketById(ticketId, sessionResult.data.id);
        if (!ticket) {
          return NextResponse.json(
            { success: false, error: 'التذكرة غير موجودة' },
            { status: 404 }
          );
        }

        const messages = SupportService.getTicketMessages(ticketId);
        return NextResponse.json({
          success: true,
          data: {
            ticket,
            messages
          }
        });

      case 'search_tickets':
        if (!query) {
          return NextResponse.json(
            { success: false, error: 'نص البحث مطلوب' },
            { status: 400 }
          );
        }

        const status = searchParams.get('status') as any;
        const priority = searchParams.get('priority') as any;
        const ticketCategory = searchParams.get('category') as any;

        const searchResults = SupportService.searchTickets(query, {
          category: ticketCategory,
          status,
          priority,
          userId: sessionResult.data.id
        });

        return NextResponse.json({
          success: true,
          data: searchResults
        });

      case 'stats':
        // التحقق من صلاحيات الإدارة
        if (sessionResult.data.type !== 'business') {
          return NextResponse.json(
            { success: false, error: 'غير مصرح لعرض الإحصائيات' },
            { status: 403 }
          );
        }

        const stats = SupportService.getSupportStats();
        return NextResponse.json({
          success: true,
          data: stats
        });

      default:
        return NextResponse.json(
          { success: false, error: 'إجراء غير صحيح' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('خطأ في جلب بيانات الدعم:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    // تقييم الأسئلة الشائعة (لا يحتاج مصادقة)
    if (action === 'rate_faq') {
      const { faqId, helpful } = await request.json();

      if (!faqId || helpful === undefined) {
        return NextResponse.json(
          { success: false, error: 'معرف السؤال والتقييم مطلوبان' },
          { status: 400 }
        );
      }

      const result = await SupportService.rateFAQ(faqId, helpful);

      if (result.success) {
        return NextResponse.json({
          success: true,
          message: 'تم تسجيل تقييمك'
        });
      } else {
        return NextResponse.json(
          { success: false, error: result.error },
          { status: 400 }
        );
      }
    }

    // باقي العمليات تحتاج مصادقة
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    switch (action) {
      case 'create_ticket':
        const { subject, description, category, priority, attachments } = await request.json();

        if (!subject || !description || !category) {
          return NextResponse.json(
            { success: false, error: 'الموضوع والوصف والتصنيف مطلوبة' },
            { status: 400 }
          );
        }

        const ticketResult = await SupportService.createTicket(
          sessionResult.data.id,
          subject,
          description,
          category,
          priority || 'medium',
          attachments || []
        );

        if (ticketResult.success) {
          return NextResponse.json({
            success: true,
            data: ticketResult.data,
            message: 'تم إنشاء التذكرة بنجاح'
          });
        } else {
          return NextResponse.json(
            { success: false, error: ticketResult.error },
            { status: 400 }
          );
        }

      case 'add_message':
        const { ticketId, message, attachments: msgAttachments } = await request.json();

        if (!ticketId || !message) {
          return NextResponse.json(
            { success: false, error: 'معرف التذكرة والرسالة مطلوبان' },
            { status: 400 }
          );
        }

        const messageResult = await SupportService.addMessage(
          ticketId,
          sessionResult.data.id,
          message,
          false,
          msgAttachments || []
        );

        if (messageResult.success) {
          return NextResponse.json({
            success: true,
            data: messageResult.data,
            message: 'تم إضافة الرسالة بنجاح'
          });
        } else {
          return NextResponse.json(
            { success: false, error: messageResult.error },
            { status: 400 }
          );
        }

      case 'update_status':
        const { ticketId: statusTicketId, status } = await request.json();

        if (!statusTicketId || !status) {
          return NextResponse.json(
            { success: false, error: 'معرف التذكرة والحالة مطلوبان' },
            { status: 400 }
          );
        }

        const statusResult = await SupportService.updateTicketStatus(
          statusTicketId,
          status,
          sessionResult.data.id
        );

        if (statusResult.success) {
          return NextResponse.json({
            success: true,
            message: 'تم تحديث حالة التذكرة'
          });
        } else {
          return NextResponse.json(
            { success: false, error: statusResult.error },
            { status: 400 }
          );
        }

      case 'assign_ticket':
        // التحقق من صلاحيات الإدارة
        if (sessionResult.data.type !== 'business') {
          return NextResponse.json(
            { success: false, error: 'غير مصرح لتعيين التذاكر' },
            { status: 403 }
          );
        }

        const { ticketId: assignTicketId, staffId } = await request.json();

        if (!assignTicketId || !staffId) {
          return NextResponse.json(
            { success: false, error: 'معرف التذكرة والموظف مطلوبان' },
            { status: 400 }
          );
        }

        const assignResult = await SupportService.assignTicket(assignTicketId, staffId);

        if (assignResult.success) {
          return NextResponse.json({
            success: true,
            message: 'تم تعيين التذكرة بنجاح'
          });
        } else {
          return NextResponse.json(
            { success: false, error: assignResult.error },
            { status: 400 }
          );
        }

      case 'staff_reply':
        // التحقق من صلاحيات الموظفين
        if (sessionResult.data.type !== 'business') {
          return NextResponse.json(
            { success: false, error: 'غير مصرح للرد كموظف' },
            { status: 403 }
          );
        }

        const { ticketId: replyTicketId, message: replyMessage, attachments: replyAttachments } = await request.json();

        if (!replyTicketId || !replyMessage) {
          return NextResponse.json(
            { success: false, error: 'معرف التذكرة والرسالة مطلوبان' },
            { status: 400 }
          );
        }

        const replyResult = await SupportService.addMessage(
          replyTicketId,
          sessionResult.data.id,
          replyMessage,
          true,
          replyAttachments || []
        );

        if (replyResult.success) {
          return NextResponse.json({
            success: true,
            data: replyResult.data,
            message: 'تم إضافة الرد بنجاح'
          });
        } else {
          return NextResponse.json(
            { success: false, error: replyResult.error },
            { status: 400 }
          );
        }

      default:
        return NextResponse.json(
          { success: false, error: 'إجراء غير صحيح' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('خطأ في معالجة طلب الدعم:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
