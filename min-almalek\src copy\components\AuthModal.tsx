'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNotificationHelpers } from '@/hooks/useNotificationHelpers';
import Logo from './Logo';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: 'login' | 'register';
}

const AuthModal = ({ isOpen, onClose, defaultTab = 'login' }: AuthModalProps) => {
  const { login, register, isLoading, error } = useAuth();
  const { notifyWelcome } = useNotificationHelpers();

  const [activeTab, setActiveTab] = useState<'login' | 'register'>(defaultTab);
  const [userType, setUserType] = useState<'individual' | 'business' | 'real-estate-office'>('individual');
  const [loginMethod, setLoginMethod] = useState<'email' | 'phone'>('email');
  const [rememberMe, setRememberMe] = useState(false);
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [companyType, setCompanyType] = useState('');
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [isFacebookLoading, setIsFacebookLoading] = useState(false);

  // بيانات النموذج
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    phone: '',
    firstName: '',
    lastName: '',
    companyName: '',
  });

  // أنواع الشركات المتاحة
  const companyTypes = [
    { value: 'real-estate', label: 'شركة عقارية' },
    { value: 'commercial', label: 'شركة تجارية' },
    { value: 'organization', label: 'منظمة' },
    { value: 'institution', label: 'مؤسسة' },
    { value: 'government', label: 'مؤسسة حكومية' },
    { value: 'non-profit', label: 'مؤسسة غير ربحية' },
    { value: 'marketing', label: 'شركة تسويق' },
    { value: 'public-company', label: 'شركة مساهمة عامة' },
    { value: 'limited-company', label: 'شركة محدودة المسؤولية' },
    { value: 'partnership', label: 'شركة تضامن' },
    { value: 'holding', label: 'شركة قابضة' },
    { value: 'consulting', label: 'شركة استشارية' },
    { value: 'manufacturing', label: 'شركة تصنيع' },
    { value: 'services', label: 'شركة خدمات' },
    { value: 'technology', label: 'شركة تقنية' },
    { value: 'other', label: 'أخرى' }
  ];

  // تحديث التبويب عند تغيير defaultTab
  useEffect(() => {
    setActiveTab(defaultTab);
  }, [defaultTab]);

  // إعادة تعيين نوع الشركة عند تغيير نوع المستخدم
  useEffect(() => {
    if (userType === 'individual') {
      setCompanyType('');
    }
  }, [userType]);

  // معالج تسجيل الدخول
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await login({
        email: formData.email,
        password: formData.password,
        rememberMe,
      });
      onClose();
    } catch (error) {
      console.error('Login error:', error);
    }
  };

  // معالج التسجيل
  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await register({
        email: formData.email,
        password: formData.password,
        confirmPassword: formData.confirmPassword,
        phone: formData.phone,
        name: `${formData.firstName} ${formData.lastName}`,
        userType,
        acceptTerms: agreeToTerms,
        acceptPrivacy: agreeToTerms,
        individualInfo: userType === 'individual' ? {
          firstName: formData.firstName,
          lastName: formData.lastName,
        } : undefined,
        businessInfo: userType === 'business' ? {
          companyName: formData.companyName,
          businessType: companyType,
          contactPerson: {
            name: `${formData.firstName} ${formData.lastName}`,
            position: 'مدير',
            phone: formData.phone,
            email: formData.email,
          },
          address: {
            governorate: '',
            city: '',
            area: '',
            street: '',
          },
        } : undefined,
        realEstateOfficeInfo: userType === 'real-estate-office' ? {
          officeName: formData.companyName,
          licenseNumber: '',
          licenseIssueDate: new Date(),
          licenseExpiryDate: new Date(),
          ownerName: `${formData.firstName} ${formData.lastName}`,
          specializations: [],
          serviceAreas: [],
          yearsOfExperience: 0,
          teamSize: 1,
          address: {
            governorate: '',
            city: '',
            area: '',
            street: '',
            building: '',
          },
          workingHours: {
            sunday: { open: '09:00', close: '17:00', isOpen: true },
            monday: { open: '09:00', close: '17:00', isOpen: true },
            tuesday: { open: '09:00', close: '17:00', isOpen: true },
            wednesday: { open: '09:00', close: '17:00', isOpen: true },
            thursday: { open: '09:00', close: '17:00', isOpen: true },
            friday: { open: '09:00', close: '12:00', isOpen: true },
            saturday: { open: '09:00', close: '17:00', isOpen: true },
          },
        } : undefined,
      });

      notifyWelcome(`${formData.firstName} ${formData.lastName}`);
      onClose();
    } catch (error) {
      console.error('Register error:', error);
    }
  };

  // معالج تسجيل الدخول بـ Google
  const handleGoogleAuth = async () => {
    setIsGoogleLoading(true);
    try {
      // TODO: Implement Google OAuth integration
      console.log('Google Auth initiated');

      // محاكاة عملية التحقق
      await new Promise(resolve => setTimeout(resolve, 2000));

      // هنا سيتم إضافة منطق Google OAuth الفعلي
      alert('سيتم تفعيل تسجيل الدخول بـ Google قريباً');

    } catch (error) {
      console.error('Google Auth Error:', error);
      alert('حدث خطأ في تسجيل الدخول بـ Google');
    } finally {
      setIsGoogleLoading(false);
    }
  };

  // معالج تسجيل الدخول بـ Facebook
  const handleFacebookAuth = async () => {
    setIsFacebookLoading(true);
    try {
      // TODO: Implement Facebook OAuth integration
      console.log('Facebook Auth initiated');

      // محاكاة عملية التحقق
      await new Promise(resolve => setTimeout(resolve, 2000));

      // هنا سيتم إضافة منطق Facebook OAuth الفعلي
      alert('سيتم تفعيل تسجيل الدخول بـ Facebook قريباً');

    } catch (error) {
      console.error('Facebook Auth Error:', error);
      alert('حدث خطأ في تسجيل الدخول بـ Facebook');
    } finally {
      setIsFacebookLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b">
          <div className="flex items-center justify-between mb-4">
            <Logo variant="transparent" size="md" showText={false} />
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ×
            </button>
          </div>
          <h2 className="text-2xl font-bold text-gray-800 text-center">
            {activeTab === 'login' ? 'تسجيل الدخول' : 'إنشاء حساب جديد'}
          </h2>
        </div>

        {/* Tabs */}
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('login')}
            className={`flex-1 py-3 px-4 text-center font-medium ${
              activeTab === 'login'
                ? 'text-primary-600 border-b-2 border-primary-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            تسجيل الدخول
          </button>
          <button
            onClick={() => setActiveTab('register')}
            className={`flex-1 py-3 px-4 text-center font-medium ${
              activeTab === 'register'
                ? 'text-primary-600 border-b-2 border-primary-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            إنشاء حساب
          </button>
        </div>

        <div className="p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          )}

          {activeTab === 'login' ? (
            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  البريد الإلكتروني
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({...formData, email: e.target.value})}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كلمة المرور
                </label>
                <input
                  type="password"
                  value={formData.password}
                  onChange={(e) => setFormData({...formData, password: e.target.value})}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="••••••••"
                  required
                />
              </div>

              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={rememberMe}
                    onChange={(e) => setRememberMe(e.target.checked)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="mr-2 text-sm text-gray-600">تذكرني</span>
                </label>
                <a href="#" className="text-sm text-primary-600 hover:text-primary-700">
                  نسيت كلمة المرور؟
                </a>
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
              </button>
            </form>
          ) : (
            <div className="space-y-4">
              {/* User Type Selection */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  نوع الحساب
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    onClick={() => setUserType('individual')}
                    className={`p-4 border-2 rounded-lg text-center ${
                      userType === 'individual'
                        ? 'border-primary-600 bg-primary-50 text-primary-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="text-2xl mb-2">👤</div>
                    <div className="font-medium">فرد</div>
                    <div className="text-xs text-gray-500">حساب شخصي</div>
                  </button>
                  <button
                    onClick={() => setUserType('business')}
                    className={`p-4 border-2 rounded-lg text-center ${
                      userType === 'business'
                        ? 'border-primary-600 bg-primary-50 text-primary-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="text-2xl mb-2">🏢</div>
                    <div className="font-medium">شركة</div>
                    <div className="text-xs text-gray-500">حساب تجاري</div>
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الاسم الأول
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="أحمد"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الاسم الأخير
                  </label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="محمد"
                  />
                </div>
              </div>

              {userType === 'business' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      اسم الشركة
                    </label>
                    <input
                      type="text"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      placeholder="اسم الشركة"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نوع الشركة <span className="text-red-500">*</span>
                    </label>
                    <select
                      value={companyType}
                      onChange={(e) => setCompanyType(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 bg-white"
                      required
                    >
                      <option value="">اختر نوع الشركة</option>
                      {companyTypes.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                    {companyType === '' && (
                      <p className="mt-1 text-sm text-gray-500">
                        يرجى اختيار نوع الشركة لإكمال التسجيل
                      </p>
                    )}
                  </div>
                </>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  البريد الإلكتروني
                </label>
                <input
                  type="email"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  رقم الموبايل السوري
                </label>
                <div className="flex">
                  <span className="inline-flex items-center px-3 border border-l-0 border-gray-300 bg-gray-50 text-gray-500 rounded-r-lg">
                    +963
                  </span>
                  <input
                    type="tel"
                    className="flex-1 px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    placeholder="9X XXX XXXX"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  كلمة المرور
                </label>
                <input
                  type="password"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="••••••••"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  تأكيد كلمة المرور
                </label>
                <input
                  type="password"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="••••••••"
                />
              </div>

              <div className="flex items-start">
                <input
                  type="checkbox"
                  checked={agreeToTerms}
                  onChange={(e) => setAgreeToTerms(e.target.checked)}
                  className="mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                />
                <span className="mr-2 text-sm text-gray-600">
                  أوافق على{' '}
                  <a href="/terms" target="_blank" className="text-primary-600 hover:text-primary-700 underline">شروط الاستخدام</a>
                  {' '}و{' '}
                  <a href="/privacy" target="_blank" className="text-primary-600 hover:text-primary-700 underline">سياسة الخصوصية</a>
                </span>
              </div>

              {/* رسالة تنبيه للشركات */}
              {userType === 'business' && companyType === '' && agreeToTerms && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <div className="flex items-center gap-2">
                    <span className="text-yellow-600">⚠️</span>
                    <p className="text-sm text-yellow-800">
                      يرجى اختيار نوع الشركة لإكمال عملية التسجيل
                    </p>
                  </div>
                </div>
              )}

              <button
                disabled={!agreeToTerms || (userType === 'business' && companyType === '')}
                className={`w-full py-3 rounded-lg transition-colors font-medium ${
                  agreeToTerms && (userType === 'individual' || companyType !== '')
                    ? 'bg-primary-600 text-white hover:bg-primary-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                إنشاء حساب
              </button>
            </div>
          )}

          {/* Social Login */}
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">أو</span>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-1 gap-3">
              {/* Google Sign In */}
              <button
                onClick={handleGoogleAuth}
                disabled={isGoogleLoading || isFacebookLoading}
                className={`w-full inline-flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg bg-white text-sm font-medium transition-all ${
                  isGoogleLoading || isFacebookLoading
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-50 hover:border-gray-400'
                }`}
              >
                {isGoogleLoading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-400 mr-3"></div>
                ) : (
                  <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                  </svg>
                )}
                {isGoogleLoading
                  ? 'جاري التحقق...'
                  : activeTab === 'login'
                    ? 'تسجيل الدخول بـ Google'
                    : 'إنشاء حساب بـ Google'
                }
              </button>

              {/* Facebook Sign In */}
              <button
                onClick={handleFacebookAuth}
                disabled={isGoogleLoading || isFacebookLoading}
                className={`w-full inline-flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg bg-white text-sm font-medium transition-all ${
                  isGoogleLoading || isFacebookLoading
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-50 hover:border-gray-400'
                }`}
              >
                {isFacebookLoading ? (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-400 mr-3"></div>
                ) : (
                  <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
                    <path fill="#1877F2" d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                )}
                {isFacebookLoading
                  ? 'جاري التحقق...'
                  : activeTab === 'login'
                    ? 'تسجيل الدخول بـ Facebook'
                    : 'إنشاء حساب بـ Facebook'
                }
              </button>
            </div>

            {/* رسالة تنبيه للتسجيل الاجتماعي */}
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start gap-2">
                <span className="text-blue-600 mt-0.5">ℹ️</span>
                <div className="text-sm text-blue-800">
                  <p className="font-medium mb-1">تسجيل الدخول الاجتماعي</p>
                  <p>
                    عند التسجيل بـ Google أو Facebook، ستتمكن من الوصول السريع لحسابك
                    وستحتفظ بجميع إعلاناتك وتفضيلاتك.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthModal;
