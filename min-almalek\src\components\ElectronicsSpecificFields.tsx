'use client';

import { useState, useEffect } from 'react';

interface ElectronicsData {
  category?: string;
  subcategory?: string;
  brand?: string;
  model?: string;
  condition?: 'new' | 'used' | 'refurbished' | 'open-box';
  warranty?: string;
  features?: string[];
  specifications?: {
    [key: string]: string;
  };
  accessories?: string[];
  originalBox?: boolean;
  purchaseDate?: string;
  reason?: string;
}

interface ElectronicsSpecificFieldsProps {
  data: ElectronicsData;
  onChange: (data: ElectronicsData) => void;
}

const ElectronicsSpecificFields = ({ data, onChange }: ElectronicsSpecificFieldsProps) => {
  const [selectedCategory, setSelectedCategory] = useState(data.category || '');
  const [selectedSubcategory, setSelectedSubcategory] = useState(data.subcategory || '');

  const electronicsCategories = {
    'smartphones': {
      name: 'الهواتف الذكية',
      icon: '📱',
      subcategories: {
        'iphone': {
          name: 'آيفون',
          models: [
            'iPhone 15 Pro Max', 'iPhone 15 Pro', 'iPhone 15 Plus', 'iPhone 15',
            'iPhone 14 Pro Max', 'iPhone 14 Pro', 'iPhone 14 Plus', 'iPhone 14',
            'iPhone 13 Pro Max', 'iPhone 13 Pro', 'iPhone 13 mini', 'iPhone 13',
            'iPhone 12 Pro Max', 'iPhone 12 Pro', 'iPhone 12 mini', 'iPhone 12',
            'iPhone 11 Pro Max', 'iPhone 11 Pro', 'iPhone 11',
            'iPhone XS Max', 'iPhone XS', 'iPhone XR', 'iPhone X',
            'iPhone 8 Plus', 'iPhone 8', 'iPhone 7 Plus', 'iPhone 7',
            'iPhone 6s Plus', 'iPhone 6s', 'iPhone 6 Plus', 'iPhone 6',
            'iPhone SE (الجيل الثالث)', 'iPhone SE (الجيل الثاني)', 'iPhone SE (الجيل الأول)'
          ]
        },
        'samsung': { name: 'سامسونغ', brands: ['Samsung'] },
        'huawei': { name: 'هواوي', brands: ['Huawei'] },
        'xiaomi': { name: 'شاومي', brands: ['Xiaomi', 'Redmi', 'POCO'] },
        'oppo': { name: 'أوبو', brands: ['OPPO', 'OnePlus'] },
        'other-phones': { name: 'هواتف أخرى', brands: ['Nokia', 'Sony', 'LG', 'Motorola', 'Realme', 'Vivo'] }
      },
      specifications: ['الذاكرة الداخلية (RAM)', 'ذاكرة التخزين', 'المعالج', 'البطارية', 'الشريحة']
    },
    'laptops': {
      name: 'أجهزة الكمبيوتر المحمولة',
      icon: '💻',
      subcategories: {
        'gaming': { name: 'ألعاب', brands: ['ASUS ROG', 'MSI', 'Alienware', 'Razer', 'HP Omen', 'Acer Predator'] },
        'business': { name: 'أعمال', brands: ['ThinkPad', 'Dell Latitude', 'HP EliteBook', 'Surface'] },
        'ultrabook': { name: 'ألترابوك', brands: ['MacBook', 'Dell XPS', 'HP Spectre', 'ASUS ZenBook'] },
        'budget': { name: 'اقتصادية', brands: ['Acer Aspire', 'HP Pavilion', 'Lenovo IdeaPad', 'ASUS VivoBook'] }
      },
      specifications: ['المعالج', 'ذاكرة الوصول العشوائي', 'التخزين', 'كرت الرسوميات', 'حجم الشاشة', 'دقة الشاشة', 'نظام التشغيل', 'البطارية']
    },
    'tablets': {
      name: 'الأجهزة اللوحية',
      icon: '📱',
      subcategories: {
        'ipad': { name: 'آيباد', brands: ['Apple'] },
        'android': { name: 'أندرويد', brands: ['Samsung', 'Huawei', 'Lenovo', 'Xiaomi'] },
        'windows': { name: 'ويندوز', brands: ['Microsoft Surface', 'HP', 'Dell'] }
      },
      specifications: ['حجم الشاشة', 'دقة الشاشة', 'التخزين', 'ذاكرة الوصول العشوائي', 'المعالج', 'الكاميرا', 'البطارية', 'نظام التشغيل']
    },
    'computers': {
      name: 'أجهزة الكمبيوتر المكتبية',
      icon: '🖥️',
      subcategories: {
        'gaming-pc': { name: 'ألعاب', brands: ['Custom Build', 'Alienware', 'HP Omen', 'ASUS ROG'] },
        'office-pc': { name: 'مكتبية', brands: ['Dell OptiPlex', 'HP EliteDesk', 'Lenovo ThinkCentre'] },
        'workstation': { name: 'محطات عمل', brands: ['Dell Precision', 'HP Z Series', 'Lenovo ThinkStation'] },
        'all-in-one': { name: 'الكل في واحد', brands: ['iMac', 'HP All-in-One', 'Dell Inspiron AIO'] }
      },
      specifications: ['المعالج', 'ذاكرة الوصول العشوائي', 'التخزين', 'كرت الرسوميات', 'اللوحة الأم', 'مزود الطاقة', 'نظام التشغيل']
    },
    'accessories': {
      name: 'الإكسسوارات',
      icon: '🎧',
      subcategories: {
        'headphones': { name: 'سماعات', brands: ['Apple', 'Sony', 'Bose', 'JBL', 'Beats', 'Sennheiser'] },
        'keyboards': { name: 'لوحات مفاتيح', brands: ['Logitech', 'Razer', 'Corsair', 'SteelSeries'] },
        'mice': { name: 'فأرات', brands: ['Logitech', 'Razer', 'SteelSeries', 'Corsair'] },
        'monitors': { name: 'شاشات', brands: ['Samsung', 'LG', 'Dell', 'ASUS', 'Acer', 'BenQ'] },
        'speakers': { name: 'مكبرات صوت', brands: ['JBL', 'Bose', 'Sony', 'Harman Kardon'] },
        'chargers': { name: 'شواحن', brands: ['Anker', 'Belkin', 'Apple', 'Samsung'] }
      },
      specifications: ['النوع', 'التوافق', 'الاتصال', 'المواصفات التقنية']
    },
    'gaming': {
      name: 'الألعاب والترفيه',
      icon: '🎮',
      subcategories: {
        'consoles': { name: 'أجهزة الألعاب', brands: ['PlayStation', 'Xbox', 'Nintendo Switch'] },
        'controllers': { name: 'أذرع التحكم', brands: ['Sony', 'Microsoft', 'Nintendo', 'Razer'] },
        'games': { name: 'الألعاب', brands: ['PlayStation', 'Xbox', 'Nintendo', 'PC Games'] }
      },
      specifications: ['النوع', 'التوافق', 'الحالة', 'الإصدار']
    }
  };

  const conditionOptions = [
    { id: 'new', name: 'جديد', description: 'لم يستخدم من قبل، في العلبة الأصلية' },
    { id: 'open-box', name: 'علبة مفتوحة', description: 'جديد لكن العلبة مفتوحة، لم يستخدم' },
    { id: 'used', name: 'مستعمل', description: 'مستعمل بحالة جيدة' },
    { id: 'refurbished', name: 'مجدد', description: 'تم تجديده من قبل الشركة المصنعة' }
  ];

  const warrantyOptions = [
    'لا يوجد ضمان',
    'ضمان الوكيل - سنة واحدة',
    'ضمان الوكيل - سنتان',
    'ضمان الوكيل - 3 سنوات',
    'ضمان المحل - 6 أشهر',
    'ضمان المحل - سنة واحدة',
    'ضمان دولي',
    'ضمان منتهي الصلاحية'
  ];

  // خيارات الذاكرة والمواصفات للهواتف
  const ramOptions = ['2 غيغا', '3 غيغا', '4 غيغا', '6 غيغا', '8 غيغا', '12 غيغا'];
  const storageOptions = ['8 غيغا', '32 غيغا', '64 غيغا', '128 غيغا', '256 غيغا', '512 غيغا', '1 تيرا بايت'];
  const batteryOptions = ['ممتازة', 'جيدة جداً', 'جيدة'];
  const simOptions = ['SIM 1', 'SIM 2', 'e-SIM', 'SIM 1 + SIM 2', 'SIM 1 + e-SIM', 'SIM 2 + e-SIM', 'SIM 1 + SIM 2 + e-SIM'];

  useEffect(() => {
    onChange({
      ...data,
      category: selectedCategory,
      subcategory: selectedSubcategory
    });
  }, [selectedCategory, selectedSubcategory]);

  const handleSpecificationChange = (key: string, value: string) => {
    onChange({
      ...data,
      specifications: {
        ...data.specifications,
        [key]: value
      }
    });
  };

  const handleFeatureToggle = (feature: string) => {
    const currentFeatures = data.features || [];
    const updatedFeatures = currentFeatures.includes(feature)
      ? currentFeatures.filter(f => f !== feature)
      : [...currentFeatures, feature];
    
    onChange({
      ...data,
      features: updatedFeatures
    });
  };

  const handleAccessoryToggle = (accessory: string) => {
    const currentAccessories = data.accessories || [];
    const updatedAccessories = currentAccessories.includes(accessory)
      ? currentAccessories.filter(a => a !== accessory)
      : [...currentAccessories, accessory];
    
    onChange({
      ...data,
      accessories: updatedAccessories
    });
  };

  const getCurrentCategory = () => {
    return electronicsCategories[selectedCategory as keyof typeof electronicsCategories];
  };

  const getCurrentSubcategory = () => {
    const category = getCurrentCategory();
    if (!category || !selectedSubcategory) return null;
    return category.subcategories[selectedSubcategory as keyof typeof category.subcategories];
  };

  const getCommonAccessories = () => {
    switch (selectedCategory) {
      case 'smartphones':
        return ['الشاحن الأصلي', 'سماعات الأذن', 'كابل USB', 'العلبة الأصلية', 'كتيب التعليمات', 'أداة إخراج الشريحة'];
      case 'laptops':
        return ['الشاحن الأصلي', 'العلبة الأصلية', 'كتيب التعليمات', 'فأرة', 'حقيبة اللابتوب', 'قرص التشغيل'];
      case 'tablets':
        return ['الشاحن الأصلي', 'العلبة الأصلية', 'كتيب التعليمات', 'قلم اللمس', 'لوحة المفاتيح', 'الحافظة'];
      case 'computers':
        return ['لوحة المفاتيح', 'الفأرة', 'الشاشة', 'مكبرات الصوت', 'كابلات التوصيل', 'أقراص التشغيل'];
      default:
        return ['العلبة الأصلية', 'كتيب التعليمات', 'الشاحن', 'الكابلات'];
    }
  };

  return (
    <div className="space-y-6">
      {/* اختيار الفئة */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          فئة الإلكترونيات *
        </label>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {Object.entries(electronicsCategories).map(([key, category]) => (
            <button
              key={key}
              type="button"
              onClick={() => {
                setSelectedCategory(key);
                setSelectedSubcategory('');
              }}
              className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                selectedCategory === key
                  ? 'border-primary-500 bg-primary-50 shadow-md'
                  : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
              }`}
            >
              <div className="text-2xl mb-2 opacity-80">{category.icon}</div>
              <div className="text-sm font-medium text-gray-800">{category.name}</div>
            </button>
          ))}
        </div>
      </div>

      {/* اختيار الفئة الفرعية */}
      {selectedCategory && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            النوع المحدد *
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {Object.entries(getCurrentCategory()?.subcategories || {}).map(([key, subcategory]) => (
              <button
                key={key}
                type="button"
                onClick={() => setSelectedSubcategory(key)}
                className={`p-3 rounded-lg border-2 text-sm transition-all ${
                  selectedSubcategory === key
                    ? 'border-primary-500 bg-primary-50 text-primary-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                {subcategory.name}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* اختيار الماركة/الموديل */}
      {selectedSubcategory && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {selectedSubcategory === 'iphone' ? 'موديل الآيفون *' : 'الماركة *'}
            </label>
            {selectedSubcategory === 'iphone' ? (
              <select
                value={data.model || ''}
                onChange={(e) => onChange({ ...data, model: e.target.value, brand: 'Apple' })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="">اختر موديل الآيفون</option>
                {getCurrentSubcategory()?.models?.map((model) => (
                  <option key={model} value={model}>{model}</option>
                ))}
              </select>
            ) : (
              <select
                value={data.brand || ''}
                onChange={(e) => onChange({ ...data, brand: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="">اختر الماركة</option>
                {getCurrentSubcategory()?.brands?.map((brand) => (
                  <option key={brand} value={brand}>{brand}</option>
                ))}
                <option value="other">أخرى</option>
              </select>
            )}
          </div>

          {selectedSubcategory !== 'iphone' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الموديل
              </label>
              <input
                type="text"
                value={data.model || ''}
                onChange={(e) => onChange({ ...data, model: e.target.value })}
                placeholder="مثال: Galaxy S24 Ultra"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          )}
        </div>
      )}

      {/* حالة الجهاز */}
      {selectedCategory && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            حالة الجهاز *
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {conditionOptions.map((condition) => (
              <button
                key={condition.id}
                type="button"
                onClick={() => onChange({ ...data, condition: condition.id as any })}
                className={`p-4 rounded-lg border-2 text-left transition-all ${
                  data.condition === condition.id
                    ? 'border-primary-500 bg-primary-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="font-medium text-gray-800">{condition.name}</div>
                <div className="text-sm text-gray-600 mt-1">{condition.description}</div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* الضمان */}
      {selectedCategory && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الضمان
            </label>
            <select
              value={data.warranty || ''}
              onChange={(e) => onChange({ ...data, warranty: e.target.value })}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">اختر نوع الضمان</option>
              {warrantyOptions.map((warranty) => (
                <option key={warranty} value={warranty}>{warranty}</option>
              ))}
            </select>
          </div>


        </div>
      )}

      {/* المواصفات التقنية */}
      {selectedCategory && getCurrentCategory()?.specifications && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            المواصفات التقنية
          </label>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {getCurrentCategory()?.specifications.map((spec) => (
              <div key={spec}>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  {spec}
                </label>
                {spec === 'الذاكرة الداخلية (RAM)' ? (
                  <select
                    value={data.specifications?.[spec] || ''}
                    onChange={(e) => handleSpecificationChange(spec, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                  >
                    <option value="">اختر الذاكرة الداخلية</option>
                    {ramOptions.map((option) => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                ) : spec === 'ذاكرة التخزين' ? (
                  <select
                    value={data.specifications?.[spec] || ''}
                    onChange={(e) => handleSpecificationChange(spec, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                  >
                    <option value="">اختر ذاكرة التخزين</option>
                    {storageOptions.map((option) => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                ) : spec === 'البطارية' ? (
                  <select
                    value={data.specifications?.[spec] || ''}
                    onChange={(e) => handleSpecificationChange(spec, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                  >
                    <option value="">اختر حالة البطارية</option>
                    {batteryOptions.map((option) => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                ) : spec === 'الشريحة' ? (
                  <select
                    value={data.specifications?.[spec] || ''}
                    onChange={(e) => handleSpecificationChange(spec, e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                  >
                    <option value="">اختر نوع الشريحة</option>
                    {simOptions.map((option) => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                ) : (
                  <input
                    type="text"
                    value={data.specifications?.[spec] || ''}
                    onChange={(e) => handleSpecificationChange(spec, e.target.value)}
                    placeholder={`أدخل ${spec}`}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* الإكسسوارات المرفقة */}
      {selectedCategory && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            الإكسسوارات المرفقة
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {getCommonAccessories().map((accessory) => (
              <button
                key={accessory}
                type="button"
                onClick={() => handleAccessoryToggle(accessory)}
                className={`p-3 rounded-lg border-2 text-sm transition-all ${
                  data.accessories?.includes(accessory)
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <span className="mr-2">
                  {data.accessories?.includes(accessory) ? '✅' : '⬜'}
                </span>
                {accessory}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* العلبة الأصلية */}
      {selectedCategory && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            العلبة الأصلية
          </label>
          <div className="flex gap-4">
            <button
              type="button"
              onClick={() => onChange({ ...data, originalBox: true })}
              className={`px-6 py-3 rounded-lg border-2 transition-all ${
                data.originalBox === true
                  ? 'border-green-500 bg-green-50 text-green-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              ✅ متوفرة
            </button>
            <button
              type="button"
              onClick={() => onChange({ ...data, originalBox: false })}
              className={`px-6 py-3 rounded-lg border-2 transition-all ${
                data.originalBox === false
                  ? 'border-red-500 bg-red-50 text-red-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              ❌ غير متوفرة
            </button>
          </div>
        </div>
      )}

      {/* سبب البيع */}
      {data.condition === 'used' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            سبب البيع (اختياري)
          </label>
          <textarea
            value={data.reason || ''}
            onChange={(e) => onChange({ ...data, reason: e.target.value })}
            placeholder="مثال: ترقية لجهاز أحدث، عدم الحاجة، تغيير في الاستخدام..."
            rows={3}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>
      )}
    </div>
  );
};

export default ElectronicsSpecificFields;
