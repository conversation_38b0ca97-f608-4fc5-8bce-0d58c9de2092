'use client';

import { useState } from 'react';
import AIAssistant from './AIAssistant';

export default function FloatingAIButton() {
  const [showAI, setShowAI] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  return (
    <>
      {/* الزر العائم */}
      <div className="fixed bottom-6 left-6 z-40">
        <button
          onClick={() => setShowAI(true)}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          className="group relative bg-gradient-to-r from-purple-600 to-blue-600 text-white p-4 rounded-full shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 transform hover:scale-110"
          style={{
            filter: 'drop-shadow(0 0 20px rgba(147, 51, 234, 0.4))',
            animation: 'pulse 2s infinite'
          }}
        >
          {/* أيقونة الروبوت */}
          <svg
            className="w-6 h-6 transition-transform duration-300 group-hover:rotate-12"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v-.07zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
          </svg>

          {/* مؤشر النشاط */}
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-ping"></div>
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full"></div>

          {/* النص التوضيحي */}
          {isHovered && (
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-sm rounded-lg whitespace-nowrap opacity-0 animate-fadeIn">
              المساعد الذكي
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-800"></div>
            </div>
          )}
        </button>

        {/* رسائل ترحيبية متحركة */}
        <div className="absolute bottom-full left-0 mb-4 space-y-2">
          {/* رسالة ترحيبية */}
          <div className="bg-white rounded-lg shadow-lg p-3 max-w-xs transform transition-all duration-500 hover:scale-105 border border-purple-100">
            <div className="flex items-start gap-2">
              <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center text-white text-sm">
                🤖
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-800">مرحباً! 👋</div>
                <div className="text-xs text-gray-600 mt-1">
                  أنا مساعدك الذكي. كيف يمكنني مساعدتك في العثور على العقار المثالي؟
                </div>
                <button
                  onClick={() => setShowAI(true)}
                  className="text-xs text-purple-600 hover:text-purple-700 font-medium mt-2 block"
                >
                  ابدأ المحادثة ←
                </button>
              </div>
            </div>
          </div>

          {/* اقتراحات سريعة */}
          <div className="bg-white rounded-lg shadow-lg p-3 max-w-xs border border-blue-100">
            <div className="text-xs font-medium text-gray-800 mb-2">اقتراحات سريعة:</div>
            <div className="space-y-1">
              {[
                '🏠 أبحث عن شقة',
                '💰 ما هي الأسعار؟',
                '📍 أفضل المناطق',
                '🔍 مساعدة في البحث'
              ].map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => setShowAI(true)}
                  className="block w-full text-left text-xs text-gray-600 hover:text-purple-600 hover:bg-purple-50 p-1 rounded transition-colors"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* مكون المساعد الذكي */}
      <AIAssistant
        isOpen={showAI}
        onClose={() => setShowAI(false)}
      />

      {/* الأنماط المخصصة */}
      <style jsx>{`
        @keyframes pulse {
          0%, 100% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.05);
          }
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out forwards;
        }

        /* تأثيرات إضافية للزر */
        .group:hover {
          box-shadow: 0 20px 40px rgba(147, 51, 234, 0.3);
        }

        /* تأثير الموجات */
        .group::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 100%;
          height: 100%;
          border-radius: 50%;
          background: rgba(147, 51, 234, 0.3);
          transform: translate(-50%, -50%) scale(0);
          animation: ripple 2s infinite;
        }

        @keyframes ripple {
          0% {
            transform: translate(-50%, -50%) scale(0);
            opacity: 1;
          }
          100% {
            transform: translate(-50%, -50%) scale(2);
            opacity: 0;
          }
        }

        /* إخفاء الرسائل على الشاشات الصغيرة */
        @media (max-width: 768px) {
          .absolute.bottom-full {
            display: none;
          }
        }
      `}</style>
    </>
  );
}
