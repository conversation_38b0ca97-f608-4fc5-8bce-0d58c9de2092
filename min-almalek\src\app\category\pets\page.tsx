'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import CategoryIcon from '@/components/CategoryIcon';
import AdvancedSearch from '@/components/AdvancedSearch';
import FilterModal from '@/components/FilterModal';
import { Ad, DataService } from '@/lib/data';

const PetsPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedSubCategory, setSelectedSubCategory] = useState('');
  const [selectedAge, setSelectedAge] = useState('');
  const [selectedGender, setSelectedGender] = useState('');
  const [selectedSize, setSelectedSize] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [priceRange, setPriceRange] = useState([0, 2000000]);
  const [sortBy, setSortBy] = useState('newest');
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);

  // فلاتر للـ FilterModal
  const [filters, setFilters] = useState({
    mainCategory: '',
    subCategory: '',
    age: '',
    gender: '',
    size: '',
    breed: '',
    vaccinated: '',
    trained: '',
    priceFrom: '',
    priceTo: '',
    location: ''
  });

  // بيانات وهمية للإعلانات
  const mockAds = [
    {
      id: '1',
      title: 'كلب جيرمان شيبرد - ذكر مدرب',
      price: 500000,
      currency: 'SYP',
      location: 'دمشق - المزة',
      images: ['/images/pets/german-shepherd.jpg'],
      category: 'pets',
      subCategory: 'animals',
      animalType: 'كلاب',
      breed: 'جيرمان',
      age: '1-2 سنة',
      gender: 'ذكر',
      size: 'كبير',
      vaccinated: true,
      trained: true,
      views: 245,
      isFavorite: false,
      isPromoted: true,
      postedAt: '2024-01-15',
      description: 'كلب جيرمان شيبرد أصيل، مدرب ومطعم بالكامل'
    },
    {
      id: '2',
      title: 'قطة شيرازي - أنثى جميلة',
      price: 150000,
      currency: 'SYP',
      location: 'دمشق - أبو رمانة',
      images: ['/images/pets/persian-cat.jpg'],
      category: 'pets',
      subCategory: 'animals',
      animalType: 'قطط',
      breed: 'شيرازي',
      age: '4-6 أشهر',
      gender: 'أنثى',
      size: 'صغير',
      vaccinated: true,
      trained: false,
      views: 189,
      isFavorite: true,
      isPromoted: false,
      postedAt: '2024-01-14',
      description: 'قطة شيرازي جميلة ولعوبة، مطعمة ونظيفة'
    },
    {
      id: '3',
      title: 'طعام كلاب - جودة عالية',
      price: 25000,
      currency: 'SYP',
      location: 'دمشق - الشعلان',
      images: ['/images/pets/dog-food.jpg'],
      category: 'pets',
      subCategory: 'supplies',
      animalType: 'طعام كلاب جاف',
      views: 156,
      isFavorite: false,
      isPromoted: false,
      postedAt: '2024-01-13',
      description: 'طعام كلاب عالي الجودة، غني بالبروتين والفيتامينات'
    }
  ];

  const categories = [
    { id: 'animals', name: 'الحيوانات' },
    { id: 'supplies', name: 'مستلزمات الحيوانات' },
    { id: 'services', name: 'خدمات الحيوانات' },
    { id: 'accessories', name: 'إكسسوارات وأدوات' },
    { id: 'additional-services', name: 'خدمات إضافية' }
  ];

  const popularPets = [
    { type: 'قطط شيرازي', icon: '🐱', count: 45, price: '150,000 - 500,000' },
    { type: 'كلاب جولدن', icon: '🐕', count: 23, price: '300,000 - 800,000' },
    { type: 'طيور الحب', icon: '🦜', count: 67, price: '50,000 - 150,000' },
    { type: 'أسماك زينة', icon: '🐠', count: 89, price: '10,000 - 100,000' },
    { type: 'أرانب هولندية', icon: '🐰', count: 34, price: '75,000 - 200,000' },
    { type: 'هامستر سوري', icon: '🐹', count: 56, price: '25,000 - 75,000' }
  ];

  const petSupplies = [
    { item: 'طعام حيوانات', category: 'تغذية', popular: true },
    { item: 'أقفاص وبيوت', category: 'سكن', popular: true },
    { item: 'ألعاب حيوانات', category: 'ترفيه', popular: false },
    { item: 'أدوية بيطرية', category: 'صحة', popular: true },
    { item: 'مستلزمات نظافة', category: 'عناية', popular: false },
    { item: 'أحواض أسماك', category: 'معدات', popular: true }
  ];

  const subCategories = {
    'animals': [
      { id: 'dogs', name: 'كلاب' },
      { id: 'cats', name: 'قطط' },
      { id: 'birds', name: 'طيور زينة' },
      { id: 'fish', name: 'أسماك زينة' },
      { id: 'rabbits', name: 'أرانب' },
      { id: 'turtles', name: 'سلاحف' },
      { id: 'hamsters', name: 'هامستر وخنازير غينيا' },
      { id: 'reptiles', name: 'زواحف' }
    ],
    'supplies': [
      { id: 'food', name: 'طعام الحيوانات' },
      { id: 'toys', name: 'ألعاب وإكسسوارات' },
      { id: 'aquarium', name: 'أحواض أسماك وإكسسوارات' },
      { id: 'cages', name: 'أقفاص وبيوت' },
      { id: 'grooming', name: 'أدوات العناية' }
    ],
    'services': [
      { id: 'veterinary', name: 'خدمات بيطرية' },
      { id: 'grooming-services', name: 'حمامات وتجميل' },
      { id: 'training', name: 'تدريب الحيوانات' },
      { id: 'breeding', name: 'تربية وتهجين' },
      { id: 'photography', name: 'تصوير الحيوانات' }
    ],
    'accessories': [
      { id: 'clothing', name: 'ملابس الحيوانات' },
      { id: 'smart-devices', name: 'أجهزة ذكية' },
      { id: 'cleaning', name: 'أدوات التنظيف' }
    ],
    'additional-services': [
      { id: 'hotels', name: 'فنادق ومبيت الحيوانات' },
      { id: 'transport', name: 'خدمات النقل' },
      { id: 'rescue', name: 'جمعيات الإنقاذ والرعاية' },
      { id: 'training-courses', name: 'دورات تدريبية' },
      { id: 'markets', name: 'أسواق وتبادل' }
    ]
  };

  const ages = ['أقل من شهر', '1-3 أشهر', '4-6 أشهر', '7-12 شهر', '1-2 سنة', '3-5 سنوات', 'أكثر من 5 سنوات'];
  const genders = ['ذكر', 'أنثى'];
  const sizes = ['صغير', 'متوسط', 'كبير', 'كبير جداً'];

  const locations = [
    'دمشق - المزة',
    'دمشق - أبو رمانة',
    'دمشق - الشعلان',
    'دمشق - المالكي',
    'دمشق - الصالحية',
    'حلب - الفرقان',
    'حلب - الجميلية',
    'حمص - الوعر',
    'حمص - الخالدية'
  ];

  // دوال للفلاتر
  const handleFilterChange = (key: string, value: string | string[]) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      mainCategory: '',
      subCategory: '',
      age: '',
      gender: '',
      size: '',
      breed: '',
      vaccinated: '',
      trained: '',
      priceFrom: '',
      priceTo: '',
      location: ''
    });
  };

  const filteredAds = mockAds.filter(ad => {
    const matchesSearch = ad.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         ad.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || ad.subCategory === selectedCategory;
    const matchesSubCategory = !selectedSubCategory || ad.animalType?.includes(selectedSubCategory);
    const matchesAge = !selectedAge || ad.age === selectedAge;
    const matchesGender = !selectedGender || ad.gender === selectedGender;
    const matchesSize = !selectedSize || ad.size === selectedSize;
    const matchesLocation = !selectedLocation || ad.location.includes(selectedLocation);
    const matchesPrice = ad.price >= priceRange[0] && ad.price <= priceRange[1];

    return matchesSearch && matchesCategory && matchesSubCategory && matchesAge && matchesGender && matchesSize && matchesLocation && matchesPrice;
  });

  const sortedAds = [...filteredAds].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'popular':
        return b.views - a.views;
      case 'newest':
      default:
        return new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime();
    }
  });

  const loadAds = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    const result = DataService.getAds({
      category: 'pets',
      page: currentPage,
      limit: 12,
      sortBy: sortBy as any
    });
    setAds(result.ads);
    setTotalPages(result.totalPages);
    setLoading(false);
  };

  const handleSearch = (filters: any) => {
    console.log('تطبيق فلاتر البحث:', filters);
    loadAds();
  };

  const stats = {
    totalAds: 334,
    avgPrice: '185,000',
    newToday: 8,
    featuredAds: 23
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-white">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          {/* العنوان الرئيسي */}
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 flex items-center justify-center">
              <CategoryIcon
                category="pets"
                className="w-10 h-10"
                color="#f59e0b"
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">الحيوانات الأليفة</h1>
              <p className="text-gray-600">اكتشف أفضل الحيوانات الأليفة ومستلزماتها</p>
            </div>
          </div>
        </div>

        {/* زر فتح الفلاتر على الموبايل */}
        <div className="lg:hidden mb-6">
          <button
            onClick={() => setIsFilterModalOpen(true)}
            className="w-full bg-white rounded-xl shadow-lg p-4 flex items-center justify-center gap-3 border border-gray-200 hover:bg-gray-50 transition-colors"
          >
            <img
              src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
              alt="فلاتر"
              className="w-6 h-6 opacity-80"
              style={{
                filter: 'drop-shadow(0 0 4px rgba(168, 85, 247, 0.6))'
              }}
            />
            <span className="text-lg font-semibold text-gray-800">البحث والفلاتر</span>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* فلاتر الحيوانات الأليفة */}
          <div className="lg:col-span-1 hidden lg:block">
            <div className="bg-white rounded-xl shadow-lg p-6 sticky top-8 border border-gray-200">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                <img
                  src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="فلاتر"
                  className="w-6 h-6 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(245, 158, 11, 0.6))'
                  }}
                />
                فلاتر البحث
              </h2>

              {/* البحث */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="ابحث عن حيوان أو منتج..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                />
              </div>

              {/* الفئة الرئيسية */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الرئيسية</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => {
                    setSelectedCategory(e.target.value);
                    setSelectedSubCategory('');
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                >
                  <option value="">جميع الفئات</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* الفئة الفرعية */}
              {selectedCategory && subCategories[selectedCategory as keyof typeof subCategories] && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الفرعية</label>
                  <select
                    value={selectedSubCategory}
                    onChange={(e) => setSelectedSubCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  >
                    <option value="">جميع الفئات الفرعية</option>
                    {subCategories[selectedCategory as keyof typeof subCategories].map(subCategory => (
                      <option key={subCategory.id} value={subCategory.name}>
                        {subCategory.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* العمر - للحيوانات فقط */}
              {selectedCategory === 'animals' && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">العمر</label>
                  <select
                    value={selectedAge}
                    onChange={(e) => setSelectedAge(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  >
                    <option value="">جميع الأعمار</option>
                    {ages.map(age => (
                      <option key={age} value={age}>
                        {age}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* الجنس - للحيوانات فقط */}
              {selectedCategory === 'animals' && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">الجنس</label>
                  <select
                    value={selectedGender}
                    onChange={(e) => setSelectedGender(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  >
                    <option value="">الجميع</option>
                    {genders.map(gender => (
                      <option key={gender} value={gender}>
                        {gender}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* الحجم - للحيوانات فقط */}
              {selectedCategory === 'animals' && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">الحجم</label>
                  <select
                    value={selectedSize}
                    onChange={(e) => setSelectedSize(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  >
                    <option value="">جميع الأحجام</option>
                    {sizes.map(size => (
                      <option key={size} value={size}>
                        {size}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* المحافظة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">المحافظة</label>
                <select
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                >
                  <option value="">جميع المحافظات</option>
                  <option value="دمشق">دمشق</option>
                  <option value="ريف دمشق">ريف دمشق</option>
                  <option value="حلب">حلب</option>
                  <option value="حمص">حمص</option>
                  <option value="حماة">حماة</option>
                  <option value="اللاذقية">اللاذقية</option>
                  <option value="طرطوس">طرطوس</option>
                  <option value="إدلب">إدلب</option>
                  <option value="درعا">درعا</option>
                  <option value="السويداء">السويداء</option>
                  <option value="القنيطرة">القنيطرة</option>
                  <option value="دير الزور">دير الزور</option>
                  <option value="الرقة">الرقة</option>
                  <option value="الحسكة">الحسكة</option>
                </select>
              </div>

              {/* نطاق السعر */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    value={priceRange[0]}
                    onChange={(e) => setPriceRange([parseInt(e.target.value) || 0, priceRange[1]])}
                    placeholder="السعر من"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  />
                  <input
                    type="number"
                    value={priceRange[1]}
                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value) || 2000000])}
                    placeholder="السعر إلى"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  />
                </div>
              </div>

              {/* إعادة تعيين الفلاتر */}
              <button
                onClick={() => {
                  setSelectedCategory('');
                  setSelectedSubCategory('');
                  setSelectedAge('');
                  setSelectedGender('');
                  setSelectedSize('');
                  setSelectedLocation('');
                  setPriceRange([0, 2000000]);
                  setSearchQuery('');
                }}
                className="w-full bg-gradient-to-r from-amber-500 to-amber-700 text-white py-3 px-4 rounded-lg hover:from-amber-600 hover:to-amber-800 transition-all duration-300 font-medium"
              >
                إعادة تعيين الفلاتر
              </button>
            </div>
          </div>

          {/* النتائج */}
          <div className="lg:col-span-3">
            {/* شريط الترتيب */}
            <div className="bg-white rounded-xl shadow-lg p-4 mb-6 border border-gray-200">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="text-gray-600">
                  عرض <span className="font-semibold text-amber-600">{sortedAds.length}</span> من أصل {sortedAds.length} إعلان
                </div>

                <div className="flex items-center gap-4">
                  {/* طريقة العرض */}
                  <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'grid'
                          ? 'bg-amber-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      🔲 شبكة
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'list'
                          ? 'bg-amber-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      📋 قائمة
                    </button>
                  </div>

                  {/* الترتيب */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="popular">الأكثر مشاهدة</option>
                    <option value="price-low">السعر: من الأقل للأعلى</option>
                    <option value="price-high">السعر: من الأعلى للأقل</option>
                  </select>
                </div>
              </div>
            </div>

            {/* الإعلانات */}
            <div className={
              viewMode === 'grid'
                ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                : 'space-y-4'
            }>
              {sortedAds.map(ad => (
                <AdCard
                  key={ad.id}
                  ad={ad}
                  viewMode={viewMode}
                />
              ))}
            </div>

            {/* رسالة عدم وجود نتائج */}
            {sortedAds.length === 0 && (
              <div className="text-center py-12">
                <div className="w-24 h-24 flex items-center justify-center mx-auto mb-4">
                  <CategoryIcon
                    category="pets"
                    className="w-20 h-20"
                    color="#f59e0b"
                  />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد نتائج</h3>
                <p className="text-gray-600">جرب تعديل الفلاتر للعثور على المزيد من النتائج</p>
              </div>
            )}
          </div>
        </div>
        {/* Modal الفلاتر للموبايل */}
        <FilterModal
          isOpen={isFilterModalOpen}
          onClose={() => setIsFilterModalOpen(false)}
          filters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={clearFilters}
          category="pets"
        />
      </main>

      <Footer />
    </div>
  );
};

export default PetsPage;
