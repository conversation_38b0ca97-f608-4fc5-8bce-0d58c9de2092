'use client';

import { useState, useEffect } from 'react';

interface Notification {
  id: string;
  type: 'new_job' | 'application_update' | 'interview' | 'message' | 'skill_test';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  actionUrl?: string;
  priority: 'low' | 'medium' | 'high';
}

interface JobAlert {
  id: string;
  keywords: string[];
  location: string;
  jobType: string;
  salaryMin?: number;
  isActive: boolean;
  createdDate: string;
}

interface JobNotificationsProps {
  userId: string;
}

export default function JobNotifications({ userId }: JobNotificationsProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [jobAlerts, setJobAlerts] = useState<JobAlert[]>([]);
  const [showAlertForm, setShowAlertForm] = useState(false);
  const [newAlert, setNewAlert] = useState({
    keywords: '',
    location: '',
    jobType: '',
    salaryMin: ''
  });

  // بيانات وهمية للإشعارات
  useEffect(() => {
    const sampleNotifications: Notification[] = [
      {
        id: '1',
        type: 'new_job',
        title: 'وظيفة جديدة تطابق اهتماماتك',
        message: 'تم نشر وظيفة "مطور React" في شركة التقنيات المتطورة',
        timestamp: '2024-01-22T10:30:00',
        isRead: false,
        priority: 'high',
        actionUrl: '/jobs/123'
      },
      {
        id: '2',
        type: 'application_update',
        title: 'تحديث على طلب التوظيف',
        message: 'تم مراجعة طلبك لوظيفة مصمم جرافيك في وكالة الإبداع',
        timestamp: '2024-01-22T09:15:00',
        isRead: false,
        priority: 'medium'
      },
      {
        id: '3',
        type: 'interview',
        title: 'دعوة لمقابلة شخصية',
        message: 'تم تحديد موعد مقابلة شخصية يوم الأحد الساعة 10:00 صباحاً',
        timestamp: '2024-01-21T16:45:00',
        isRead: true,
        priority: 'high'
      },
      {
        id: '4',
        type: 'skill_test',
        title: 'اختبار مهارات مطلوب',
        message: 'يرجى إكمال اختبار المهارات التقنية خلال 48 ساعة',
        timestamp: '2024-01-21T14:20:00',
        isRead: false,
        priority: 'high'
      }
    ];

    const sampleAlerts: JobAlert[] = [
      {
        id: '1',
        keywords: ['مطور', 'React', 'JavaScript'],
        location: 'دمشق',
        jobType: 'دوام كامل',
        salaryMin: 800000,
        isActive: true,
        createdDate: '2024-01-15'
      },
      {
        id: '2',
        keywords: ['مصمم', 'جرافيك', 'Photoshop'],
        location: 'حلب',
        jobType: 'دوام جزئي',
        isActive: true,
        createdDate: '2024-01-10'
      }
    ];

    setNotifications(sampleNotifications);
    setJobAlerts(sampleAlerts);
  }, [userId]);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new_job': return '💼';
      case 'application_update': return '📝';
      case 'interview': return '🤝';
      case 'message': return '💬';
      case 'skill_test': return '🧠';
      default: return '🔔';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-l-red-500 bg-red-50';
      case 'medium': return 'border-l-yellow-500 bg-yellow-50';
      case 'low': return 'border-l-blue-500 bg-blue-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === notificationId ? { ...notif, isRead: true } : notif
      )
    );
  };

  const addJobAlert = () => {
    if (!newAlert.keywords.trim()) return;

    const alert: JobAlert = {
      id: Date.now().toString(),
      keywords: newAlert.keywords.split(',').map(k => k.trim()),
      location: newAlert.location,
      jobType: newAlert.jobType,
      salaryMin: newAlert.salaryMin ? parseInt(newAlert.salaryMin) : undefined,
      isActive: true,
      createdDate: new Date().toISOString().split('T')[0]
    };

    setJobAlerts(prev => [...prev, alert]);
    setNewAlert({ keywords: '', location: '', jobType: '', salaryMin: '' });
    setShowAlertForm(false);
  };

  const toggleAlert = (alertId: string) => {
    setJobAlerts(prev =>
      prev.map(alert =>
        alert.id === alertId ? { ...alert, isActive: !alert.isActive } : alert
      )
    );
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  return (
    <div className="space-y-6">
      {/* الإشعارات */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
            🔔 الإشعارات
            {unreadCount > 0 && (
              <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                {unreadCount}
              </span>
            )}
          </h2>
          <button
            onClick={() => setNotifications(prev => prev.map(n => ({ ...n, isRead: true })))}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            تحديد الكل كمقروء
          </button>
        </div>

        <div className="space-y-3">
          {notifications.length > 0 ? (
            notifications.map((notification) => (
              <div
                key={notification.id}
                className={`border-l-4 p-4 rounded-lg cursor-pointer transition-all hover:shadow-md ${
                  getPriorityColor(notification.priority)
                } ${!notification.isRead ? 'font-medium' : 'opacity-75'}`}
                onClick={() => markAsRead(notification.id)}
              >
                <div className="flex items-start gap-3">
                  <span className="text-2xl">{getNotificationIcon(notification.type)}</span>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-800 mb-1">
                      {notification.title}
                    </h3>
                    <p className="text-gray-600 mb-2">{notification.message}</p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">
                        {new Date(notification.timestamp).toLocaleString('ar-SY')}
                      </span>
                      {!notification.isRead && (
                        <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <div className="text-6xl mb-4">🔔</div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد إشعارات</h3>
              <p className="text-gray-600">ستظهر الإشعارات الجديدة هنا</p>
            </div>
          )}
        </div>
      </div>

      {/* تنبيهات الوظائف */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
            🚨 تنبيهات الوظائف
          </h2>
          <button
            onClick={() => setShowAlertForm(!showAlertForm)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            إضافة تنبيه جديد
          </button>
        </div>

        {/* نموذج إضافة تنبيه */}
        {showAlertForm && (
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-semibold mb-4">إضافة تنبيه وظيفة جديد</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الكلمات المفتاحية (مفصولة بفاصلة)
                </label>
                <input
                  type="text"
                  value={newAlert.keywords}
                  onChange={(e) => setNewAlert(prev => ({ ...prev, keywords: e.target.value }))}
                  placeholder="مطور، React، JavaScript"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المحافظة</label>
                <select
                  value={newAlert.location}
                  onChange={(e) => setNewAlert(prev => ({ ...prev, location: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">جميع المحافظات</option>
                  <option value="damascus">دمشق</option>
                  <option value="aleppo">حلب</option>
                  <option value="homs">حمص</option>
                  <option value="hama">حماة</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع الوظيفة</label>
                <select
                  value={newAlert.jobType}
                  onChange={(e) => setNewAlert(prev => ({ ...prev, jobType: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">جميع الأنواع</option>
                  <option value="دوام كامل">دوام كامل</option>
                  <option value="دوام جزئي">دوام جزئي</option>
                  <option value="عقد">عقد</option>
                  <option value="تدريب">تدريب</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الحد الأدنى للراتب</label>
                <input
                  type="number"
                  value={newAlert.salaryMin}
                  onChange={(e) => setNewAlert(prev => ({ ...prev, salaryMin: e.target.value }))}
                  placeholder="500000"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="flex gap-2 mt-4">
              <button
                onClick={addJobAlert}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
              >
                إضافة التنبيه
              </button>
              <button
                onClick={() => setShowAlertForm(false)}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        )}

        {/* قائمة التنبيهات */}
        <div className="space-y-3">
          {jobAlerts.length > 0 ? (
            jobAlerts.map((alert) => (
              <div key={alert.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="text-lg">🔍</span>
                      <span className="font-semibold text-gray-800">
                        {alert.keywords.join(', ')}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        alert.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {alert.isActive ? 'نشط' : 'متوقف'}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600">
                      📍 {alert.location || 'جميع المحافظات'} • 
                      💼 {alert.jobType || 'جميع الأنواع'} • 
                      💰 {alert.salaryMin ? `${alert.salaryMin.toLocaleString()} ل.س فما فوق` : 'أي راتب'}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      تم الإنشاء: {alert.createdDate}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => toggleAlert(alert.id)}
                      className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                        alert.isActive 
                          ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                          : 'bg-green-100 text-green-700 hover:bg-green-200'
                      }`}
                    >
                      {alert.isActive ? 'إيقاف' : 'تفعيل'}
                    </button>
                    <button className="text-red-600 hover:text-red-800 text-sm">
                      حذف
                    </button>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <div className="text-6xl mb-4">🚨</div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد تنبيهات</h3>
              <p className="text-gray-600">أضف تنبيهات للحصول على إشعارات عن الوظائف المناسبة</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
