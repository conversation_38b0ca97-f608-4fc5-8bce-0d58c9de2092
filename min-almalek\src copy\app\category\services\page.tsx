'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import AdvancedSearch from '@/components/AdvancedSearch';
import { Ad, DataService } from '@/lib/data';

export default function ServicesPage() {
  const [ads, setAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const subcategories = [
    'خدمات تنظيف',
    'صيانة وإصلاح',
    'خدمات تعليمية',
    'خدمات طبية',
    'خدمات قانونية',
    'خدمات تقنية',
    'خدمات توصيل',
    'خدمات تصميم',
    'خدمات ترجمة',
    'خدمات أخرى'
  ];

  const popularServices = [
    { name: 'تنظيف منازل', icon: '🧹', providers: 45 },
    { name: 'صيانة كهرباء', icon: '⚡', providers: 38 },
    { name: 'دروس خصوصية', icon: '📚', providers: 67 },
    { name: 'خدمات توصيل', icon: '🚚', providers: 29 },
    { name: 'تصميم جرافيك', icon: '🎨', providers: 52 },
    { name: 'ترجمة', icon: '🌐', providers: 34 }
  ];

  useEffect(() => {
    loadAds();
  }, [sortBy, currentPage]);

  const loadAds = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    const result = DataService.getAds({
      category: 'services',
      page: currentPage,
      limit: 12,
      sortBy: sortBy as any
    });
    setAds(result.ads);
    setTotalPages(result.totalPages);
    setLoading(false);
  };

  const handleSearch = (filters: any) => {
    console.log('تطبيق فلاتر البحث:', filters);
    loadAds();
  };

  const stats = {
    totalAds: 567,
    avgPrice: '75,000',
    newToday: 15,
    featuredAds: 34
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <span className="text-4xl">🛠️</span>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">الخدمات</h1>
              <p className="text-gray-600">اعثر على مقدمي الخدمات المحترفين في منطقتك</p>
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white rounded-lg p-4 text-center shadow-sm">
              <div className="text-2xl font-bold text-primary-600">{stats.totalAds}</div>
              <div className="text-sm text-gray-600">إجمالي الخدمات</div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center shadow-sm">
              <div className="text-2xl font-bold text-green-600">{stats.avgPrice}</div>
              <div className="text-sm text-gray-600">متوسط السعر</div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center shadow-sm">
              <div className="text-2xl font-bold text-blue-600">{stats.newToday}</div>
              <div className="text-sm text-gray-600">جديد اليوم</div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center shadow-sm">
              <div className="text-2xl font-bold text-yellow-600">{stats.featuredAds}</div>
              <div className="text-sm text-gray-600">خدمات مميزة</div>
            </div>
          </div>
        </div>

        {/* الخدمات الشائعة */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">الخدمات الشائعة</h2>
          <div className="grid grid-cols-2 md:grid-cols-6 gap-3">
            {popularServices.map((service, index) => (
              <button
                key={index}
                className="bg-white border border-gray-200 rounded-lg p-3 hover:border-primary-500 hover:bg-primary-50 transition-colors text-center"
              >
                <div className="text-2xl mb-1">{service.icon}</div>
                <div className="text-sm font-medium text-gray-800">{service.name}</div>
                <div className="text-xs text-gray-500">{service.providers} مقدم خدمة</div>
              </button>
            ))}
          </div>
        </div>

        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">جميع الفئات</h2>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
            {subcategories.map((sub, index) => (
              <button
                key={index}
                className="bg-white border border-gray-200 rounded-lg p-3 text-sm hover:border-primary-500 hover:bg-primary-50 transition-colors text-center"
              >
                {sub}
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-lg p-6 sticky top-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">البحث المتقدم</h3>
              <AdvancedSearch onSearch={handleSearch} category="services" />
            </div>
          </div>

          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="text-gray-600">
                  عرض {ads.length} من أصل {stats.totalAds} خدمة
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'grid' 
                          ? 'bg-primary-600 text-white' 
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      🔲 شبكة
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'list' 
                          ? 'bg-primary-600 text-white' 
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      📋 قائمة
                    </button>
                  </div>

                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="oldest">الأقدم</option>
                    <option value="price_low">السعر: من الأقل للأعلى</option>
                    <option value="price_high">السعر: من الأعلى للأقل</option>
                    <option value="most_viewed">الأكثر مشاهدة</option>
                    <option value="rating">الأعلى تقييماً</option>
                  </select>
                </div>
              </div>
            </div>

            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                    <div className="h-48 bg-gray-200"></div>
                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : ads.length > 0 ? (
              <>
                <div className={
                  viewMode === 'grid' 
                    ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                    : 'space-y-4'
                }>
                  {ads.map((ad) => (
                    <AdCard 
                      key={ad.id} 
                      ad={ad} 
                      viewMode={viewMode}
                    />
                  ))}
                </div>

                {totalPages > 1 && (
                  <div className="flex justify-center mt-8">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                        className="px-3 py-2 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        السابق
                      </button>
                      
                      {[...Array(totalPages)].map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentPage(index + 1)}
                          className={`px-3 py-2 border rounded-lg text-sm ${
                            currentPage === index + 1
                              ? 'bg-primary-600 text-white border-primary-600'
                              : 'border-gray-200 hover:bg-gray-50'
                          }`}
                        >
                          {index + 1}
                        </button>
                      ))}
                      
                      <button
                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                        disabled={currentPage === totalPages}
                        className="px-3 py-2 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        التالي
                      </button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🛠️</div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد خدمات</h3>
                <p className="text-gray-600 mb-6">لم نجد أي خدمات تطابق معايير البحث</p>
                <button
                  onClick={() => window.location.reload()}
                  className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  إعادة تحميل
                </button>
              </div>
            )}
          </div>
        </div>

        {/* نصائح لاختيار مقدم الخدمة */}
        <div className="mt-16 bg-gradient-to-r from-teal-600 to-teal-800 rounded-xl text-white p-8">
          <h2 className="text-2xl font-bold mb-6 text-center">نصائح لاختيار مقدم الخدمة المناسب</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="text-3xl mb-3">⭐</div>
              <h3 className="font-semibold mb-2">تحقق من التقييمات</h3>
              <p className="text-teal-100 text-sm">اقرأ تقييمات العملاء السابقين</p>
            </div>
            <div className="text-center">
              <div className="text-3xl mb-3">📋</div>
              <h3 className="font-semibold mb-2">اطلب عينات أعمال</h3>
              <p className="text-teal-100 text-sm">شاهد أمثلة من أعمال سابقة</p>
            </div>
            <div className="text-center">
              <div className="text-3xl mb-3">💰</div>
              <h3 className="font-semibold mb-2">قارن الأسعار</h3>
              <p className="text-teal-100 text-sm">احصل على عروض من عدة مقدمين</p>
            </div>
            <div className="text-center">
              <div className="text-3xl mb-3">📞</div>
              <h3 className="font-semibold mb-2">تواصل مباشر</h3>
              <p className="text-teal-100 text-sm">ناقش التفاصيل قبل الاتفاق</p>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
