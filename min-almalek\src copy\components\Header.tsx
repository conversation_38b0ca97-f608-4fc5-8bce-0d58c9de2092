'use client';

import { useState } from 'react';
import Link from 'next/link';
import AuthModal from './AuthModal';
import MyCvLogo from './MyCvLogo';
import Logo from './Logo';
import SafeNavigationButton from './SafeNavigationButton';
import ClientOnlyNotifications from './ClientOnlyNotifications';
import ClientOnlyAccount from './ClientOnlyAccount';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [authModalTab, setAuthModalTab] = useState<'login' | 'register'>('login');

  return (
    <header className="bg-white shadow-lg border-b border-primary-100">


      {/* Main Header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Logo variant="transparent" size="xl" showText={true} href="/" />



          {/* Auth Buttons */}
          <div className="flex items-center gap-3">
            {/* زر الوظائف السريع */}
            <Link
              href="/jobs"
              className="hidden lg:flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors font-medium"
              title="الوظائف والسير الذاتية - مدعوم من قبل تطبيق MyCv"
            >
              <MyCvLogo size="xs" variant="square" />
              <span>الوظائف</span>
            </Link>

            {/* الإشعارات */}
            <ClientOnlyNotifications />

            {/* حسابي */}
            <ClientOnlyAccount />

            <button
              onClick={() => {
                setAuthModalTab('login');
                setIsAuthModalOpen(true);
              }}
              className="px-4 py-2 text-primary-600 border border-primary-600 rounded-lg hover:bg-primary-50 transition-colors"
            >
              تسجيل الدخول
            </button>
            <button
              onClick={() => {
                setAuthModalTab('register');
                setIsAuthModalOpen(true);
              }}
              className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              إنشاء حساب
            </button>
            <SafeNavigationButton
              href="/add-ad"
              className="px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-semibold"
            >
              أضف إعلانك
            </SafeNavigationButton>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 text-gray-600"
          >
            ☰
          </button>
        </div>

        {/* Navigation */}
        <nav className="mt-4 border-t pt-4">
          <ul className="flex flex-wrap gap-6 text-gray-700">
            <li><SafeNavigationButton href="/" className="hover:text-primary-600 font-medium bg-transparent border-0 p-0">الرئيسية</SafeNavigationButton></li>
            <li><SafeNavigationButton href="/ads" className="hover:text-primary-600 bg-transparent border-0 p-0">جميع الإعلانات</SafeNavigationButton></li>
            <li><SafeNavigationButton href="/jobs" className="hover:text-primary-600 font-medium flex items-center gap-1 bg-transparent border-0 p-0">
              <MyCvLogo size="xs" variant="square" />
              <span>الوظائف</span>
            </SafeNavigationButton></li>
            <li><SafeNavigationButton href="/notifications" className="hover:text-primary-600 bg-transparent border-0 p-0">الإشعارات</SafeNavigationButton></li>
            <li><SafeNavigationButton href="/map" className="hover:text-primary-600 bg-transparent border-0 p-0">الخريطة</SafeNavigationButton></li>
            <li><SafeNavigationButton href="/compare" className="hover:text-primary-600 bg-transparent border-0 p-0">المقارنة</SafeNavigationButton></li>
            <li><SafeNavigationButton href="/category/real-estate" className="hover:text-primary-600 bg-transparent border-0 p-0">العقارات</SafeNavigationButton></li>
            <li><SafeNavigationButton href="/category/cars" className="hover:text-primary-600 bg-transparent border-0 p-0">السيارات</SafeNavigationButton></li>
            <li><SafeNavigationButton href="/category/electronics" className="hover:text-primary-600 bg-transparent border-0 p-0">الإلكترونيات</SafeNavigationButton></li>
            <li><SafeNavigationButton href="/subscription" className="hover:text-primary-600 font-medium bg-transparent border-0 p-0">💎 الاشتراكات</SafeNavigationButton></li>
          </ul>
        </nav>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white border-t">
          <div className="container mx-auto px-4 py-4">
            <div className="flex flex-col gap-4">
              {/* روابط التنقل للموبايل */}
              <div className="border-t border-gray-200 pt-4 mt-4">
                <h3 className="font-semibold text-gray-800 mb-3">التنقل</h3>
                <div className="grid grid-cols-2 gap-2">
                  <SafeNavigationButton href="/" className="p-3 text-center bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div className="text-lg mb-1">🏠</div>
                    <div className="text-sm">الرئيسية</div>
                  </SafeNavigationButton>
                  <SafeNavigationButton href="/ads" className="p-3 text-center bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div className="text-lg mb-1">📋</div>
                    <div className="text-sm">الإعلانات</div>
                  </SafeNavigationButton>
                  <SafeNavigationButton href="/jobs" className="p-3 text-center bg-green-50 rounded-lg hover:bg-green-100 transition-colors border border-green-200">
                    <div className="flex justify-center mb-1">
                      <MyCvLogo size="sm" variant="square" />
                    </div>
                    <div className="text-sm font-medium text-green-700">الوظائف</div>
                  </SafeNavigationButton>
                  <SafeNavigationButton href="/test-page" className="p-3 text-center bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors border border-blue-200">
                    <div className="text-lg mb-1">🧪</div>
                    <div className="text-sm font-medium text-blue-700">اختبار</div>
                  </SafeNavigationButton>
                  <SafeNavigationButton href="/map" className="p-3 text-center bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div className="text-lg mb-1">🗺️</div>
                    <div className="text-sm">الخريطة</div>
                  </SafeNavigationButton>
                  <SafeNavigationButton href="/category/real-estate" className="p-3 text-center bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div className="text-lg mb-1">🏘️</div>
                    <div className="text-sm">العقارات</div>
                  </SafeNavigationButton>
                  <SafeNavigationButton href="/category/cars" className="p-3 text-center bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div className="text-lg mb-1">🚗</div>
                    <div className="text-sm">السيارات</div>
                  </SafeNavigationButton>
                  <SafeNavigationButton href="/subscription" className="p-3 text-center bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors border border-purple-200">
                    <div className="text-lg mb-1">💎</div>
                    <div className="text-sm font-medium text-purple-700">الاشتراكات</div>
                  </SafeNavigationButton>
                </div>
              </div>

              <div className="flex flex-col gap-2 mt-4">
                <button
                  onClick={() => {
                    setAuthModalTab('login');
                    setIsAuthModalOpen(true);
                  }}
                  className="px-4 py-2 text-primary-600 border border-primary-600 rounded-lg"
                >
                  تسجيل الدخول
                </button>
                <button
                  onClick={() => {
                    setAuthModalTab('register');
                    setIsAuthModalOpen(true);
                  }}
                  className="px-4 py-2 bg-primary-600 text-white rounded-lg"
                >
                  إنشاء حساب
                </button>
                <SafeNavigationButton
                  href="/add-ad"
                  className="px-4 py-2 bg-orange-500 text-white rounded-lg text-center"
                >
                  أضف إعلانك
                </SafeNavigationButton>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        defaultTab={authModalTab}
      />
    </header>
  );
};

export default Header;
