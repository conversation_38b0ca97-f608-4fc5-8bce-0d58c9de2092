'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import FilterModal from '@/components/FilterModal';
import CategoryIcon from '@/components/CategoryIcon';
import { Ad, DataService } from '@/lib/data';

export default function ElectronicsPage() {
  const [ads, setAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);

  // فلاتر البحث
  const [filters, setFilters] = useState({
    category: '',
    subcategory: '',
    brand: '',
    model: '',
    condition: '',
    priceFrom: '',
    priceTo: '',
    warranty: '',
    location: '',
    features: [] as string[]
  });

  // قوائم الخيارات
  const electronicsCategories = [
    { value: 'smartphones', label: 'الهواتف الذكية' },
    { value: 'laptops', label: 'أجهزة الكمبيوتر المحمولة' },
    { value: 'tablets', label: 'الأجهزة اللوحية' },
    { value: 'computers', label: 'أجهزة الكمبيوتر المكتبية' },
    { value: 'accessories', label: 'الإكسسوارات' },
    { value: 'gaming', label: 'الألعاب والترفيه' }
  ];

  const subcategoriesByCategory = {
    smartphones: [
      { value: 'iphone', label: 'آيفون' },
      { value: 'samsung', label: 'سامسونغ' },
      { value: 'huawei', label: 'هواوي' },
      { value: 'xiaomi', label: 'شاومي' },
      { value: 'oppo', label: 'أوبو' },
      { value: 'other-phones', label: 'هواتف أخرى' }
    ],
    laptops: [
      { value: 'gaming', label: 'ألعاب' },
      { value: 'business', label: 'أعمال' },
      { value: 'ultrabook', label: 'ألترابوك' },
      { value: 'budget', label: 'اقتصادية' }
    ],
    tablets: [
      { value: 'ipad', label: 'آيباد' },
      { value: 'android', label: 'أندرويد' },
      { value: 'windows', label: 'ويندوز' }
    ],
    computers: [
      { value: 'gaming-pc', label: 'ألعاب' },
      { value: 'office-pc', label: 'مكتبية' },
      { value: 'workstation', label: 'محطات عمل' },
      { value: 'all-in-one', label: 'الكل في واحد' }
    ],
    accessories: [
      { value: 'headphones', label: 'سماعات' },
      { value: 'keyboards', label: 'لوحات مفاتيح' },
      { value: 'mice', label: 'فأرات' },
      { value: 'monitors', label: 'شاشات' },
      { value: 'speakers', label: 'مكبرات صوت' },
      { value: 'chargers', label: 'شواحن' }
    ],
    gaming: [
      { value: 'consoles', label: 'أجهزة الألعاب' },
      { value: 'controllers', label: 'أذرع التحكم' },
      { value: 'games', label: 'الألعاب' }
    ]
  };

  const brands = [
    'Apple', 'Samsung', 'Huawei', 'Xiaomi', 'OPPO', 'OnePlus', 'Nokia', 'Sony', 'LG', 'Motorola',
    'ASUS', 'MSI', 'Alienware', 'Razer', 'HP', 'Acer', 'Dell', 'Lenovo', 'ThinkPad', 'Surface',
    'MacBook', 'Microsoft', 'Logitech', 'Corsair', 'SteelSeries', 'JBL', 'Bose', 'Beats',
    'Sennheiser', 'BenQ', 'Harman Kardon', 'Anker', 'Belkin', 'PlayStation', 'Xbox', 'Nintendo'
  ];

  const conditionOptions = [
    { value: 'new', label: 'جديد' },
    { value: 'open-box', label: 'علبة مفتوحة' },
    { value: 'used', label: 'مستعمل' },
    { value: 'refurbished', label: 'مجدد' }
  ];

  const warrantyOptions = [
    'لا يوجد ضمان',
    'ضمان الوكيل - سنة واحدة',
    'ضمان الوكيل - سنتان',
    'ضمان الوكيل - 3 سنوات',
    'ضمان المحل - 6 أشهر',
    'ضمان المحل - سنة واحدة',
    'ضمان دولي',
    'ضمان منتهي الصلاحية'
  ];

  const locations = [
    'دمشق',
    'حلب',
    'حمص',
    'حماة',
    'اللاذقية',
    'طرطوس',
    'درعا',
    'السويداء',
    'القنيطرة',
    'دير الزور',
    'الرقة',
    'الحسكة',
    'إدلب',
    'ريف دمشق'
  ];



  useEffect(() => {
    loadAds();
  }, [sortBy, currentPage, filters]);

  const loadAds = async () => {
    setLoading(true);
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const result = DataService.getAds({
      category: 'electronics',
      page: currentPage,
      limit: 12,
      sortBy: sortBy as any
    });
    
    setAds(result.ads);
    setTotalPages(result.totalPages);
    setLoading(false);
  };

  const handleSearch = (newFilters?: any) => {
    if (newFilters) {
      setFilters(newFilters);
    }
    setCurrentPage(1);
    loadAds();
  };

  const handleFilterChange = (key: string, value: string | string[]) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      category: '',
      subcategory: '',
      brand: '',
      model: '',
      condition: '',
      priceFrom: '',
      priceTo: '',
      warranty: '',
      location: '',
      features: []
    });
  };

  const stats = {
    totalAds: 1456,
    avgPrice: '850,000',
    newToday: 34,
    featuredAds: 89
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* العنوان والإحصائيات */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 flex items-center justify-center">
              <CategoryIcon
                category="electronics"
                className="w-10 h-10"
                color="#8b5cf6"
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">الإلكترونيات</h1>
              <p className="text-gray-600">أحدث الأجهزة الإلكترونية بأفضل الأسعار</p>
            </div>
          </div>


        </div>



        <div className="flex flex-col lg:flex-row gap-8">
          {/* فلاتر الإلكترونيات */}
          <div className="lg:w-1/4">
            <div className="bg-white/30 backdrop-blur-sm rounded-xl shadow-lg p-6 sticky top-8 border border-white/40">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                <img
                  src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="فلاتر"
                  className="w-6 h-6 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(139, 92, 246, 0.6))'
                  }}
                />
                البحث والفلاتر
              </h2>

              {/* Search */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="ابحث عن جهاز..."
                    className="w-full px-3 py-2 pr-10 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                  />
                  <img
                    src="/images/jobs/Jobs -Personals/icons/search_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                    alt="بحث"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 opacity-60"
                  />
                </div>
                <button
                  onClick={() => handleSearch(filters)}
                  className="mt-2 w-full py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium flex items-center justify-center gap-2"
                  type="button"
                >
                  <img
                    src="/images/jobs/Jobs -Personals/icons/search_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
                    alt="تطبيق"
                    className="w-5 h-5 opacity-80"
                    style={{
                      filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.6))'
                    }}
                  />
                  تطبيق البحث
                </button>
              </div>

              {/* فئة الجهاز */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">فئة الجهاز</label>
                <select
                  value={filters.category}
                  onChange={(e) => handleFilterChange('category', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  <option value="">جميع الفئات</option>
                  {electronicsCategories.map(category => (
                    <option key={category.value} value={category.value}>{category.label}</option>
                  ))}
                </select>
              </div>

              {/* الفئة الفرعية */}
              {filters.category && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الفرعية</label>
                  <select
                    value={filters.subcategory}
                    onChange={(e) => handleFilterChange('subcategory', e.target.value)}
                    className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                  >
                    <option value="">جميع الفئات الفرعية</option>
                    {subcategoriesByCategory[filters.category as keyof typeof subcategoriesByCategory]?.map(sub => (
                      <option key={sub.value} value={sub.value}>{sub.label}</option>
                    ))}
                  </select>
                </div>
              )}

              {/* الماركة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الماركة</label>
                <select
                  value={filters.brand}
                  onChange={(e) => handleFilterChange('brand', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  <option value="">جميع الماركات</option>
                  {brands.map(brand => (
                    <option key={brand} value={brand}>{brand}</option>
                  ))}
                </select>
              </div>

              {/* الموديل */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الموديل</label>
                <input
                  type="text"
                  value={filters.model}
                  onChange={(e) => handleFilterChange('model', e.target.value)}
                  placeholder="اكتب الموديل..."
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                />
              </div>

              {/* حالة الجهاز */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">حالة الجهاز</label>
                <select
                  value={filters.condition}
                  onChange={(e) => handleFilterChange('condition', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  <option value="">جميع الحالات</option>
                  {conditionOptions.map(condition => (
                    <option key={condition.value} value={condition.value}>{condition.label}</option>
                  ))}
                </select>
              </div>

              {/* نطاق السعر */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    value={filters.priceFrom}
                    onChange={(e) => handleFilterChange('priceFrom', e.target.value)}
                    placeholder="السعر من"
                    className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                  />
                  <input
                    type="number"
                    value={filters.priceTo}
                    onChange={(e) => handleFilterChange('priceTo', e.target.value)}
                    placeholder="السعر إلى"
                    className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                  />
                </div>
              </div>

              {/* الضمان */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الضمان</label>
                <select
                  value={filters.warranty}
                  onChange={(e) => handleFilterChange('warranty', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  <option value="">جميع أنواع الضمان</option>
                  {warrantyOptions.map(warranty => (
                    <option key={warranty} value={warranty}>{warranty}</option>
                  ))}
                </select>
              </div>

              {/* الموقع */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                <select
                  value={filters.location}
                  onChange={(e) => handleFilterChange('location', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  <option value="">جميع المحافظات</option>
                  {locations.map(location => (
                    <option key={location} value={location}>{location}</option>
                  ))}
                </select>
              </div>

              {/* زر مسح الفلاتر */}
              <button
                onClick={clearFilters}
                className="w-full py-2 bg-gray-200/80 backdrop-blur-sm text-gray-700 rounded-lg hover:bg-gray-300/80 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 border border-gray-300/40"
              >
                مسح جميع الفلاتر
              </button>
            </div>
          </div>

          {/* النتائج */}
          <div className="lg:w-3/4">
            {/* زر الفلاتر للموبايل */}
            <div className="lg:hidden mb-4">
              <button
                onClick={() => setIsFilterModalOpen(true)}
                className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700 transition-colors flex items-center justify-center gap-2"
              >
                <img
                  src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="فلاتر"
                  className="w-5 h-5 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.6))'
                  }}
                />
                البحث والفلاتر
              </button>
            </div>

            {/* شريط التحكم */}
            <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="text-gray-600">
                  عرض {ads.length} من أصل {stats.totalAds} إعلان
                </div>
                
                <div className="flex items-center gap-4">
                  {/* طريقة العرض */}
                  <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'grid' 
                          ? 'bg-primary-600 text-white' 
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      🔲 شبكة
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'list' 
                          ? 'bg-primary-600 text-white' 
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      📋 قائمة
                    </button>
                  </div>

                  {/* الترتيب */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="oldest">الأقدم</option>
                    <option value="price_low">السعر: من الأقل للأعلى</option>
                    <option value="price_high">السعر: من الأعلى للأقل</option>
                    <option value="most_viewed">الأكثر مشاهدة</option>
                  </select>
                </div>
              </div>
            </div>

            {/* الإعلانات */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                    <div className="h-48 bg-gray-200"></div>
                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : ads.length > 0 ? (
              <>
                <div className={
                  viewMode === 'grid' 
                    ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                    : 'space-y-4'
                }>
                  {ads.map((ad) => (
                    <AdCard 
                      key={ad.id} 
                      ad={ad} 
                      viewMode={viewMode}
                    />
                  ))}
                </div>

                {/* الصفحات */}
                {totalPages > 1 && (
                  <div className="flex justify-center mt-8">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                        className="px-3 py-2 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        السابق
                      </button>
                      
                      {[...Array(totalPages)].map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentPage(index + 1)}
                          className={`px-3 py-2 border rounded-lg text-sm ${
                            currentPage === index + 1
                              ? 'bg-primary-600 text-white border-primary-600'
                              : 'border-gray-200 hover:bg-gray-50'
                          }`}
                        >
                          {index + 1}
                        </button>
                      ))}
                      
                      <button
                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                        disabled={currentPage === totalPages}
                        className="px-3 py-2 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        التالي
                      </button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <CategoryIcon
                    category="electronics"
                    className="w-14 h-14"
                    color="#8b5cf6"
                  />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد إلكترونيات</h3>
                <p className="text-gray-600 mb-6">لم نجد أي أجهزة إلكترونية تطابق معايير البحث</p>
                <button
                  onClick={() => window.location.reload()}
                  className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  إعادة تحميل
                </button>
              </div>
            )}
          </div>
        </div>

        {/* نصائح لشراء الإلكترونيات */}
        <div className="mt-12 bg-gradient-to-r from-purple-100 to-purple-200 rounded-lg text-purple-800 p-6">
          <h2 className="text-lg font-bold mb-4 text-center">نصائح لشراء الإلكترونيات</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl mb-2">🔍</div>
              <h3 className="font-semibold mb-1 text-sm">تحقق من المواصفات</h3>
              <p className="text-purple-600 text-xs">قارن المواصفات مع الموقع الرسمي</p>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">🛡️</div>
              <h3 className="font-semibold mb-1 text-sm">الضمان</h3>
              <p className="text-purple-600 text-xs">تأكد من وجود ضمان ساري المفعول</p>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">🔋</div>
              <h3 className="font-semibold mb-1 text-sm">اختبر الجهاز</h3>
              <p className="text-purple-600 text-xs">جرب جميع الوظائف قبل الشراء</p>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">💰</div>
              <h3 className="font-semibold mb-1 text-sm">قارن الأسعار</h3>
              <p className="text-purple-600 text-xs">ابحث عن أفضل سعر في السوق</p>
            </div>
          </div>
        </div>
      </main>

      <Footer />

      {/* Modal الفلاتر للموبايل */}
      <FilterModal
        isOpen={isFilterModalOpen}
        onClose={() => setIsFilterModalOpen(false)}
        filters={filters}
        onFilterChange={handleFilterChange}
        onClearFilters={clearFilters}
        category="electronics"
        electronicsCategories={electronicsCategories}
        subcategoriesByCategory={subcategoriesByCategory}
        brands={brands}
        warrantyOptions={warrantyOptions}
        locations={locations}
      />
    </div>
  );
}
