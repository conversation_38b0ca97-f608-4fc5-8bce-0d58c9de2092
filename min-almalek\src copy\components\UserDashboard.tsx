'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import Logo from './Logo';
import VerificationBadge from './VerificationBadge';
import { determineUserBadge } from '@/lib/verification';
import ClientOnlyWrapper from './ClientOnlyWrapper';

const UserDashboard = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  if (!isAuthenticated || !user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto bg-white rounded-xl shadow-lg p-8 text-center">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">تسجيل الدخول مطلوب</h2>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى لوحة التحكم</p>
          <button
            onClick={() => window.location.href = '/'}
            className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            العودة للرئيسية
          </button>
        </div>
      </div>
    );
  }

  const userBadge = determineUserBadge(user.userType, user.subscription?.planId);
  const userStats = user.stats;

  const userAds = [
    {
      id: 1,
      title: 'شقة للبيع في دمشق - المالكي',
      price: '85,000,000 ل.س',
      status: 'نشط',
      views: 245,
      messages: 12,
      postedDate: '2024-01-15',
      expiryDate: '2024-02-15',
      featured: true
    },
    {
      id: 2,
      title: 'سيارة BMW للبيع',
      price: '45,000 $',
      status: 'نشط',
      views: 189,
      messages: 8,
      postedDate: '2024-01-10',
      expiryDate: '2024-02-10',
      featured: false
    },
    {
      id: 3,
      title: 'لابتوب Dell Gaming',
      price: '850 $',
      status: 'منتهي',
      views: 87,
      messages: 3,
      postedDate: '2023-12-20',
      expiryDate: '2024-01-20',
      featured: false
    }
  ];

  const messages = [
    {
      id: 1,
      adTitle: 'شقة للبيع في دمشق - المالكي',
      senderName: 'محمد أحمد',
      message: 'مرحباً، هل الشقة ما زالت متاحة؟',
      date: '2024-01-20',
      read: false
    },
    {
      id: 2,
      adTitle: 'سيارة BMW للبيع',
      senderName: 'سارة علي',
      message: 'هل يمكن معاينة السيارة غداً؟',
      date: '2024-01-19',
      read: true
    }
  ];

  const tabs = [
    { id: 'overview', name: 'نظرة عامة', icon: '📊' },
    { id: 'ads', name: 'إعلاناتي', icon: '📝' },
    { id: 'messages', name: 'الرسائل', icon: '💬' },
    { id: 'favorites', name: 'المفضلة', icon: '❤️' },
    { id: 'settings', name: 'الإعدادات', icon: '⚙️' }
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col lg:flex-row gap-8">
        {/* الشريط الجانبي */}
        <div className="lg:w-1/4">
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            <div className="text-center mb-6">
              <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">
                  {user.userType === 'individual' && '👤'}
                  {user.userType === 'business' && '🏢'}
                  {user.userType === 'real-estate-office' && '🏘️'}
                </span>
              </div>
              <h2 className="text-xl font-bold text-gray-800">{user.name}</h2>
              <p className="text-gray-600">
                {user.userType === 'individual' && 'مستخدم فردي'}
                {user.userType === 'business' && 'حساب شركة'}
                {user.userType === 'real-estate-office' && 'مكتب عقاري'}
              </p>
              <p className="text-sm text-gray-500">
                عضو منذ {new Date(user.createdAt).toLocaleDateString('en-GB', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit'
                }).split('/').reverse().join('/')}
              </p>
              <div className="mt-2">
                <VerificationBadge type={userBadge.type} size="sm" />
              </div>
            </div>

            <nav className="space-y-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-right transition-colors ${
                    activeTab === tab.id
                      ? 'bg-primary-600 text-white'
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <span>{tab.icon}</span>
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="font-semibold text-gray-800 mb-4">الاشتراك الحالي</h3>
            <div className="text-center">
              {user.subscription ? (
                <>
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <div className="text-lg font-bold text-primary-600">{user.subscription.planName}</div>
                    <VerificationBadge type={userBadge.type} size="xs" />
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    ينتهي في {new Date(user.subscription.endDate).toLocaleDateString('en-GB', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit'
                    }).split('/').reverse().join('/')}
                  </p>
                  <p className="text-xs text-gray-500 mb-4">
                    {user.subscription.isActive ? '✅ نشط' : '❌ غير نشط'}
                  </p>
                  <Link
                    href="/subscription"
                    className="block bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors text-sm"
                  >
                    إدارة الاشتراك
                  </Link>
                </>
              ) : (
                <>
                  <div className="text-lg font-bold text-gray-600 mb-2">الخطة المجانية</div>
                  <p className="text-sm text-gray-600 mb-4">5 إعلانات مجانية شهرياً</p>
                  <Link
                    href="/subscription"
                    className="block bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors text-sm"
                  >
                    ترقية الاشتراك
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>

        {/* المحتوى الرئيسي */}
        <div className="lg:w-3/4">
          {/* نظرة عامة */}
          {activeTab === 'overview' && (
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-8">لوحة التحكم</h1>

              {/* الإحصائيات */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="bg-white rounded-xl shadow-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-gray-800">{userStats.totalAds}</div>
                      <div className="text-gray-600">إجمالي الإعلانات</div>
                    </div>
                    <div className="text-3xl">📝</div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-gray-800">{userStats.totalViews}</div>
                      <div className="text-gray-600">إجمالي المشاهدات</div>
                    </div>
                    <div className="text-3xl">👁️</div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-gray-800">{userStats.totalMessages}</div>
                      <div className="text-gray-600">الرسائل الواردة</div>
                    </div>
                    <div className="text-3xl">💬</div>
                  </div>
                </div>
              </div>

              {/* الإعلانات الحديثة */}
              <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-gray-800">آخر الإعلانات</h2>
                  <Link href="#" onClick={() => setActiveTab('ads')} className="text-primary-600 hover:text-primary-700">
                    عرض الكل
                  </Link>
                </div>
                <div className="space-y-4">
                  {userAds.slice(0, 3).map((ad) => (
                    <div key={ad.id} className="flex items-center justify-between p-4 border border-gray-100 rounded-lg">
                      <div>
                        <h3 className="font-semibold text-gray-800">{ad.title}</h3>
                        <div className="text-sm text-gray-600">{ad.price} • {ad.views} مشاهدة</div>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-xs ${
                        ad.status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {ad.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* الرسائل الحديثة */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-gray-800">آخر الرسائل</h2>
                  <Link href="#" onClick={() => setActiveTab('messages')} className="text-primary-600 hover:text-primary-700">
                    عرض الكل
                  </Link>
                </div>
                <div className="space-y-4">
                  {messages.slice(0, 3).map((message) => (
                    <div key={message.id} className={`p-4 border border-gray-100 rounded-lg ${!message.read ? 'bg-blue-50' : ''}`}>
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-semibold text-gray-800">{message.senderName}</h4>
                          <p className="text-sm text-gray-600 mb-1">{message.adTitle}</p>
                          <p className="text-gray-700">{message.message}</p>
                        </div>
                        <span className="text-xs text-gray-500">{message.date}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* إعلاناتي */}
          {activeTab === 'ads' && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <h1 className="text-3xl font-bold text-gray-800">إعلاناتي</h1>
                <Link
                  href="/add-ad"
                  className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  إضافة إعلان جديد
                </Link>
              </div>

              <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإعلان</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">السعر</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المشاهدات</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الرسائل</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {userAds.map((ad) => (
                        <tr key={ad.id}>
                          <td className="px-6 py-4">
                            <div>
                              <div className="font-semibold text-gray-800">{ad.title}</div>
                              <div className="text-sm text-gray-600">
                                نُشر في {ad.postedDate} • ينتهي في {ad.expiryDate}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 text-gray-800">{ad.price}</td>
                          <td className="px-6 py-4">
                            <span className={`px-3 py-1 rounded-full text-xs ${
                              ad.status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {ad.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-gray-800">{ad.views}</td>
                          <td className="px-6 py-4 text-gray-800">{ad.messages}</td>
                          <td className="px-6 py-4">
                            <div className="flex gap-2">
                              <button className="text-blue-600 hover:text-blue-700 text-sm">تعديل</button>
                              <button className="text-green-600 hover:text-green-700 text-sm">تجديد</button>
                              <button className="text-red-600 hover:text-red-700 text-sm">حذف</button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* الرسائل */}
          {activeTab === 'messages' && (
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-6">الرسائل</h1>

              <div className="bg-white rounded-xl shadow-lg">
                <div className="p-6 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-gray-800">صندوق الوارد</h2>
                    <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm">
                      {messages.filter(m => !m.read).length} رسالة جديدة
                    </span>
                  </div>
                </div>

                <div className="divide-y divide-gray-200">
                  {messages.map((message) => (
                    <div key={message.id} className={`p-6 hover:bg-gray-50 cursor-pointer ${!message.read ? 'bg-blue-50' : ''}`}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-gray-800">{message.senderName}</h3>
                            {!message.read && <span className="w-2 h-2 bg-blue-500 rounded-full"></span>}
                          </div>
                          <p className="text-sm text-gray-600 mb-2">بخصوص: {message.adTitle}</p>
                          <p className="text-gray-700">{message.message}</p>
                        </div>
                        <span className="text-sm text-gray-500">{message.date}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* المفضلة */}
          {activeTab === 'favorites' && (
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-6">الإعلانات المفضلة</h1>
              <div className="bg-white rounded-xl shadow-lg p-8 text-center">
                <div className="text-6xl mb-4">❤️</div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد إعلانات مفضلة</h3>
                <p className="text-gray-600 mb-6">ابدأ بإضافة الإعلانات التي تعجبك إلى المفضلة</p>
                <Link
                  href="/ads"
                  className="inline-block bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  تصفح الإعلانات
                </Link>
              </div>
            </div>
          )}

          {/* الإعدادات */}
          {activeTab === 'settings' && (
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-6">إعدادات الحساب</h1>

              <div className="space-y-6">
                <div className="bg-white rounded-xl shadow-lg p-6">
                  <h2 className="text-xl font-semibold text-gray-800 mb-4">المعلومات الشخصية</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الأول</label>
                      <input type="text" defaultValue="أحمد" className="w-full px-4 py-3 border border-gray-300 rounded-lg" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الأخير</label>
                      <input type="text" defaultValue="محمد" className="w-full px-4 py-3 border border-gray-300 rounded-lg" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                      <input type="email" defaultValue="<EMAIL>" className="w-full px-4 py-3 border border-gray-300 rounded-lg" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                      <input type="tel" defaultValue="+963 944 123 456" className="w-full px-4 py-3 border border-gray-300 rounded-lg" />
                    </div>
                  </div>
                  <button className="mt-4 bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors">
                    حفظ التغييرات
                  </button>
                </div>

                <div className="bg-white rounded-xl shadow-lg p-6">
                  <h2 className="text-xl font-semibold text-gray-800 mb-4">إعدادات الإشعارات</h2>
                  <div className="space-y-4">
                    <label className="flex items-center">
                      <input type="checkbox" defaultChecked className="rounded border-gray-300 text-primary-600" />
                      <span className="mr-3">إشعارات الرسائل الجديدة</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" defaultChecked className="rounded border-gray-300 text-primary-600" />
                      <span className="mr-3">إشعارات انتهاء صلاحية الإعلانات</span>
                    </label>
                    <label className="flex items-center">
                      <input type="checkbox" className="rounded border-gray-300 text-primary-600" />
                      <span className="mr-3">إشعارات العروض الخاصة</span>
                    </label>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-lg p-6">
                  <h2 className="text-xl font-semibold text-gray-800 mb-4">تغيير كلمة المرور</h2>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الحالية</label>
                      <input type="password" className="w-full px-4 py-3 border border-gray-300 rounded-lg" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الجديدة</label>
                      <input type="password" className="w-full px-4 py-3 border border-gray-300 rounded-lg" />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور الجديدة</label>
                      <input type="password" className="w-full px-4 py-3 border border-gray-300 rounded-lg" />
                    </div>
                  </div>
                  <button className="mt-4 bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors">
                    تحديث كلمة المرور
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserDashboard;
