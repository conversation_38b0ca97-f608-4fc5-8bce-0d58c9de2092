'use client';

interface ElectronicsData {
  category?: string;
  subcategory?: string;
  brand?: string;
  model?: string;
  condition?: 'new' | 'used' | 'refurbished' | 'open-box';
  warranty?: string;
  features?: string[];
  specifications?: {
    [key: string]: string;
  };
  accessories?: string[];
  originalBox?: boolean;
  purchaseDate?: string;
  reason?: string;
}

interface ElectronicsSummaryProps {
  data: ElectronicsData;
  title: string;
  price: string;
  currency: string;
}

const ElectronicsSummary = ({ data, title, price, currency }: ElectronicsSummaryProps) => {
  const getConditionDisplay = () => {
    switch (data.condition) {
      case 'new': return { text: 'جديد', color: 'text-green-600', bg: 'bg-green-50', icon: '🆕' };
      case 'open-box': return { text: 'علبة مفتوحة', color: 'text-blue-600', bg: 'bg-blue-50', icon: '📦' };
      case 'used': return { text: 'مستعمل', color: 'text-orange-600', bg: 'bg-orange-50', icon: '🔄' };
      case 'refurbished': return { text: 'مجدد', color: 'text-purple-600', bg: 'bg-purple-50', icon: '🔧' };
      default: return { text: 'غير محدد', color: 'text-gray-600', bg: 'bg-gray-50', icon: '❓' };
    }
  };

  const getCategoryDisplay = () => {
    const categories = {
      'smartphones': { name: 'الهواتف الذكية', icon: '📱' },
      'laptops': { name: 'أجهزة الكمبيوتر المحمولة', icon: '💻' },
      'tablets': { name: 'الأجهزة اللوحية', icon: '📱' },
      'computers': { name: 'أجهزة الكمبيوتر المكتبية', icon: '🖥️' },
      'accessories': { name: 'الإكسسوارات', icon: '🎧' },
      'gaming': { name: 'الألعاب والترفيه', icon: '🎮' }
    };
    
    return categories[data.category as keyof typeof categories] || { name: 'إلكترونيات', icon: '📱' };
  };

  const condition = getConditionDisplay();
  const category = getCategoryDisplay();

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 space-y-6">
      {/* العنوان والسعر */}
      <div className="text-center border-b pb-4">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">{title}</h2>
        <div className="text-3xl font-bold text-primary-600">
          {price} {currency}
        </div>
      </div>

      {/* معلومات أساسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-xl">{category.icon}</span>
            <span className="font-semibold text-gray-700">الفئة</span>
          </div>
          <div className="text-gray-800">{category.name}</div>
        </div>

        <div className={`${condition.bg} rounded-lg p-4`}>
          <div className="flex items-center gap-2 mb-2">
            <span className="text-xl">{condition.icon}</span>
            <span className="font-semibold text-gray-700">الحالة</span>
          </div>
          <div className={`${condition.color} font-medium`}>{condition.text}</div>
        </div>
      </div>

      {/* الماركة والموديل */}
      {(data.brand || data.model) && (
        <div className="bg-blue-50 rounded-lg p-4">
          <h3 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
            <span>🏷️</span>
            معلومات المنتج
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {data.brand && (
              <div>
                <span className="text-sm text-gray-600">الماركة:</span>
                <div className="font-medium text-gray-800">{data.brand}</div>
              </div>
            )}
            {data.model && (
              <div>
                <span className="text-sm text-gray-600">الموديل:</span>
                <div className="font-medium text-gray-800">{data.model}</div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* المواصفات التقنية */}
      {data.specifications && Object.keys(data.specifications).length > 0 && (
        <div className="bg-purple-50 rounded-lg p-4">
          <h3 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
            <span>⚙️</span>
            المواصفات التقنية
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {Object.entries(data.specifications).map(([key, value]) => (
              value && (
                <div key={key} className="flex justify-between">
                  <span className="text-sm text-gray-600">{key}:</span>
                  <span className="font-medium text-gray-800">{value}</span>
                </div>
              )
            ))}
          </div>
        </div>
      )}

      {/* الضمان وتاريخ الشراء */}
      {(data.warranty || data.purchaseDate) && (
        <div className="bg-green-50 rounded-lg p-4">
          <h3 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
            <span>🛡️</span>
            معلومات الضمان
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {data.warranty && (
              <div>
                <span className="text-sm text-gray-600">الضمان:</span>
                <div className="font-medium text-gray-800">{data.warranty}</div>
              </div>
            )}
            {data.purchaseDate && (
              <div>
                <span className="text-sm text-gray-600">تاريخ الشراء:</span>
                <div className="font-medium text-gray-800">
                  {new Date(data.purchaseDate).toLocaleDateString('ar-SA')}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* الإكسسوارات */}
      {data.accessories && data.accessories.length > 0 && (
        <div className="bg-yellow-50 rounded-lg p-4">
          <h3 className="font-semibold text-gray-700 mb-3 flex items-center gap-2">
            <span>📦</span>
            الإكسسوارات المرفقة
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {data.accessories.map((accessory, index) => (
              <div key={index} className="flex items-center gap-2 text-sm">
                <span className="text-green-500">✅</span>
                <span className="text-gray-700">{accessory}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* العلبة الأصلية */}
      {data.originalBox !== undefined && (
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center gap-2">
            <span className="text-xl">📦</span>
            <span className="font-semibold text-gray-700">العلبة الأصلية:</span>
            <span className={`font-medium ${data.originalBox ? 'text-green-600' : 'text-red-600'}`}>
              {data.originalBox ? '✅ متوفرة' : '❌ غير متوفرة'}
            </span>
          </div>
        </div>
      )}

      {/* سبب البيع */}
      {data.reason && (
        <div className="bg-orange-50 rounded-lg p-4">
          <h3 className="font-semibold text-gray-700 mb-2 flex items-center gap-2">
            <span>💭</span>
            سبب البيع
          </h3>
          <p className="text-gray-700">{data.reason}</p>
        </div>
      )}

      {/* ملاحظة مهمة */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start gap-2">
          <span className="text-blue-500 text-xl">ℹ️</span>
          <div>
            <h4 className="font-semibold text-blue-800 mb-1">ملاحظة مهمة</h4>
            <p className="text-blue-700 text-sm">
              يرجى التأكد من جميع المواصفات والتفاصيل قبل الشراء. يُنصح بفحص الجهاز شخصياً قبل إتمام الصفقة.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ElectronicsSummary;
