import { useNotifications } from '@/components/NotificationSystem';

export const useNotificationHelpers = () => {
  const { addNotification } = useNotifications();

  // إشعارات الإعلانات
  const notifyAdPublished = (adTitle: string, adId: string) => {
    addNotification({
      type: 'success',
      title: 'تم نشر إعلانك بنجاح!',
      message: `تم نشر إعلان "${adTitle}" وهو الآن مرئي للمستخدمين.`,
      category: 'ad',
      icon: '🎉',
      actionUrl: `/ad/${adId}`,
      actionText: 'عرض الإعلان'
    });
  };

  const notifyAdUnderReview = (adTitle: string, adId: string) => {
    addNotification({
      type: 'warning',
      title: 'إعلانك قيد المراجعة',
      message: `إعلان "${adTitle}" قيد المراجعة من قبل فريقنا. سيتم نشره خلال 24 ساعة.`,
      category: 'ad',
      icon: '⏳',
      actionUrl: `/ad/${adId}`,
      actionText: 'عرض الإعلان'
    });
  };

  const notifyAdRejected = (adTitle: string, reason: string, adId: string) => {
    addNotification({
      type: 'error',
      title: 'تم رفض إعلانك',
      message: `إعلان "${adTitle}" تم رفضه. السبب: ${reason}`,
      category: 'ad',
      icon: '❌',
      actionUrl: `/ad/edit/${adId}`,
      actionText: 'تعديل الإعلان'
    });
  };

  // إشعارات الدفع
  const notifyPaymentSuccess = (amount: number, currency: string, planName: string) => {
    addNotification({
      type: 'success',
      title: 'تم الدفع بنجاح',
      message: `تم استلام دفعة بقيمة ${amount.toLocaleString()} ${currency} لباقة ${planName}.`,
      category: 'payment',
      icon: '💳',
      actionUrl: '/subscription',
      actionText: 'عرض الاشتراكات'
    });
  };

  const notifyPaymentFailed = (reason: string) => {
    addNotification({
      type: 'error',
      title: 'فشل في الدفع',
      message: `لم نتمكن من إتمام عملية الدفع. ${reason}`,
      category: 'payment',
      icon: '❌',
      actionUrl: '/subscription',
      actionText: 'إعادة المحاولة'
    });
  };

  const notifySubscriptionExpiring = (planName: string, daysLeft: number) => {
    addNotification({
      type: 'warning',
      title: 'اشتراكك ينتهي قريباً',
      message: `باقة ${planName} ستنتهي خلال ${daysLeft} أيام. جدد اشتراكك لتجنب انقطاع الخدمة.`,
      category: 'payment',
      icon: '⏰',
      actionUrl: '/subscription',
      actionText: 'تجديد الاشتراك'
    });
  };

  // إشعارات النظام
  const notifySystemMaintenance = (startTime: string, duration: string) => {
    addNotification({
      type: 'info',
      title: 'صيانة مجدولة للنظام',
      message: `سيتم إجراء صيانة للنظام في ${startTime} لمدة ${duration}. قد تواجه انقطاع مؤقت في الخدمة.`,
      category: 'system',
      icon: '🔧'
    });
  };

  const notifySystemUpdate = (features: string[]) => {
    addNotification({
      type: 'info',
      title: 'تحديث جديد للنظام',
      message: `تم إضافة ميزات جديدة: ${features.join(', ')}. استكشف التحديثات الجديدة!`,
      category: 'system',
      icon: '🆕',
      actionUrl: '/about',
      actionText: 'تعرف على الجديد'
    });
  };

  // إشعارات المستخدم
  const notifyProfileUpdated = () => {
    addNotification({
      type: 'success',
      title: 'تم تحديث ملفك الشخصي',
      message: 'تم حفظ التغييرات على ملفك الشخصي بنجاح.',
      category: 'user',
      icon: '👤'
    });
  };

  const notifyPasswordChanged = () => {
    addNotification({
      type: 'success',
      title: 'تم تغيير كلمة المرور',
      message: 'تم تغيير كلمة المرور بنجاح. إذا لم تقم بهذا التغيير، يرجى التواصل معنا فوراً.',
      category: 'user',
      icon: '🔒'
    });
  };

  const notifyLoginFromNewDevice = (deviceInfo: string, location: string) => {
    addNotification({
      type: 'warning',
      title: 'تسجيل دخول من جهاز جديد',
      message: `تم تسجيل الدخول من ${deviceInfo} في ${location}. إذا لم تكن أنت، يرجى تغيير كلمة المرور فوراً.`,
      category: 'user',
      icon: '🔐'
    });
  };

  // إشعارات عامة
  const notifyWelcome = (userName: string) => {
    addNotification({
      type: 'success',
      title: `🌟 أهلاً وسهلاً ${userName}!`,
      message: 'مرحباً بك في منصة من المالك - منصتك المفضلة للإعلانات المبوبة. نتمنى لك تجربة مميزة واستكشاف جميع الميزات الاحترافية المتاحة.',
      category: 'general',
      icon: '✨',
      actionUrl: '/dashboard',
      actionText: 'استكشف لوحة التحكم'
    });
  };

  const notifyNewFeature = (featureName: string, description: string) => {
    addNotification({
      type: 'info',
      title: `🚀 ميزة جديدة: ${featureName}`,
      message: `${description} - نحن نعمل باستمرار على تطوير منصة من المالك لتقديم أفضل تجربة لك.`,
      category: 'general',
      icon: '🎉',
      actionUrl: '/features',
      actionText: 'اكتشف المزيد'
    });
  };

  // إشعارات الأخطاء
  const notifyError = (title: string, message: string) => {
    addNotification({
      type: 'error',
      title: `⚠️ ${title}`,
      message: `${message} - إذا استمرت المشكلة، يرجى التواصل مع فريق الدعم.`,
      category: 'system',
      icon: '🚨'
    });
  };

  const notifySuccess = (title: string, message: string) => {
    addNotification({
      type: 'success',
      title: `🎉 ${title}`,
      message: `${message} - شكراً لاستخدامك منصة من المالك!`,
      category: 'general',
      icon: '✨'
    });
  };

  return {
    // إشعارات الإعلانات
    notifyAdPublished,
    notifyAdUnderReview,
    notifyAdRejected,
    
    // إشعارات الدفع
    notifyPaymentSuccess,
    notifyPaymentFailed,
    notifySubscriptionExpiring,
    
    // إشعارات النظام
    notifySystemMaintenance,
    notifySystemUpdate,
    
    // إشعارات المستخدم
    notifyProfileUpdated,
    notifyPasswordChanged,
    notifyLoginFromNewDevice,
    
    // إشعارات عامة
    notifyWelcome,
    notifyNewFeature,
    
    // إشعارات عامة للأخطاء والنجاح
    notifyError,
    notifySuccess
  };
};
