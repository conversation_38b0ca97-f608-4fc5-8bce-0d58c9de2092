'use client';

import { useState } from 'react';

interface FashionData {
  mainCategory?: string;
  subCategory?: string;
  condition?: string;
  size?: string;
  color?: string;
  brand?: string;
  gender?: string;
  ageGroup?: string;
  material?: string;
  season?: string;
}

interface FashionSpecificFieldsProps {
  data: FashionData;
  onChange: (data: FashionData) => void;
}

const fashionCategories = {
  'women-clothing': {
    name: 'ملابس نسائية',
    subCategories: [
      'فساتين (سهرة، كاجوال، عمل)',
      'عبايات وجلابيات',
      'بلوزات وقمصان',
      'تونيكات',
      'بناطيل وجينز',
      'تنانير',
      'جاكيتات ومعاطف',
      'ملابس بيت',
      'ملابس داخلية',
      'بيجامات',
      'ملابس سباحة'
    ]
  },
  'men-clothing': {
    name: 'ملابس رجالية',
    subCategories: [
      'قمصان',
      'تيشيرتات',
      'بناطيل وجينز',
      'شورتات',
      'بدلات رسمية',
      'جاكيتات ومعاطف',
      'ملابس داخلية',
      'بيجامات',
      'ملابس رياضية',
      'ملابس سباحة'
    ]
  },
  'kids-clothing': {
    name: 'ملابس أطفال',
    subCategories: [
      'ملابس أولاد',
      'ملابس بنات',
      'ملابس مواليد',
      'بيجامات',
      'ملابس مدارس',
      'ملابس سباحة'
    ]
  },
  'shoes': {
    name: 'أحذية',
    subCategories: [
      'أحذية نسائية (كعب عالي، مسطحة، رياضية، صندل، شتوية)',
      'أحذية رجالية (رسمية، كاجوال، رياضية، صندل، شتوية)',
      'أحذية أطفال (مدرسية، رياضية، شتوية، صيفية)'
    ]
  },
  'accessories': {
    name: 'إكسسوارات',
    subCategories: [
      'نظارات شمسية وطبية',
      'أحزمة',
      'قبعات وكابات',
      'أوشحة وشالات',
      'قفازات',
      'محافظ'
    ]
  },
  'bags': {
    name: 'حقائب وأمتعة',
    subCategories: [
      'حقائب يد نسائية',
      'حقائب ظهر',
      'شنط سفر',
      'حقائب رجالية',
      'حقائب أطفال'
    ]
  },
  'watches-jewelry': {
    name: 'ساعات ومجوهرات',
    subCategories: [
      'ساعات نسائية',
      'ساعات رجالية',
      'أساور',
      'خواتم',
      'عقود',
      'أقراط',
      'مجوهرات تقليدية',
      'مجوهرات فاخرة'
    ]
  },
  'sportswear': {
    name: 'ملابس رياضية',
    subCategories: [
      'ملابس جيم',
      'أحذية رياضية',
      'ملابس ركض',
      'ملابس يوجا',
      'ملابس كرة القدم/كرة السلة/الرياضات الأخرى'
    ]
  },
  'formal-wear': {
    name: 'ملابس المناسبات والأعراس',
    subCategories: [
      'فساتين زفاف',
      'فساتين خطوبة',
      'بدلات رسمية للرجال',
      'فساتين سهرة فاخرة',
      'إكسسوارات الزفاف'
    ]
  }
};

export default function FashionSpecificFields({ data, onChange }: FashionSpecificFieldsProps) {
  const handleChange = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    onChange(newData);
  };

  const conditionOptions = ['جديد', 'مستعمل - ممتاز', 'مستعمل - جيد', 'مستعمل - مقبول'];
  const sizeOptions = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45'];
  const colorOptions = ['أسود', 'أبيض', 'رمادي', 'أزرق', 'أحمر', 'أخضر', 'أصفر', 'بني', 'بنفسجي', 'وردي', 'برتقالي', 'بيج'];
  const brandOptions = ['Zara', 'H&M', 'Nike', 'Adidas', 'Gucci', 'Prada', 'Louis Vuitton', 'Chanel', 'Dior', 'Versace', 'أخرى'];

  return (
    <div className="space-y-6">
      <div className="bg-pink-50 border border-pink-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-pink-800 mb-4 flex items-center gap-2">
          <img
            src="/images/clothes/transparent-Photoroom (4).v2.png"
            alt="الأزياء والموضة"
            className="w-6 h-6"
            style={{
              filter: 'drop-shadow(0 0 4px rgba(236, 72, 153, 0.6))'
            }}
          />
          تفاصيل الأزياء والموضة
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* الفئة الرئيسية */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الفئة الرئيسية *
            </label>
            <select
              value={data.mainCategory || ''}
              onChange={(e) => handleChange('mainCategory', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
              required
            >
              <option value="">اختر الفئة الرئيسية</option>
              {Object.entries(fashionCategories).map(([key, category]) => (
                <option key={key} value={key}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* الفئة الفرعية */}
          {data.mainCategory && fashionCategories[data.mainCategory as keyof typeof fashionCategories] && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الفئة الفرعية *
              </label>
              <select
                value={data.subCategory || ''}
                onChange={(e) => handleChange('subCategory', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
                required
              >
                <option value="">اختر الفئة الفرعية</option>
                {fashionCategories[data.mainCategory as keyof typeof fashionCategories].subCategories.map((sub, index) => (
                  <option key={index} value={sub}>
                    {sub}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* الحالة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الحالة *
            </label>
            <select
              value={data.condition || ''}
              onChange={(e) => handleChange('condition', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
              required
            >
              <option value="">اختر الحالة</option>
              {conditionOptions.map((condition) => (
                <option key={condition} value={condition}>
                  {condition}
                </option>
              ))}
            </select>
          </div>

          {/* المقاس */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              المقاس
            </label>
            <select
              value={data.size || ''}
              onChange={(e) => handleChange('size', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              <option value="">اختر المقاس</option>
              {sizeOptions.map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
          </div>

          {/* اللون */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اللون
            </label>
            <select
              value={data.color || ''}
              onChange={(e) => handleChange('color', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              <option value="">اختر اللون</option>
              {colorOptions.map((color) => (
                <option key={color} value={color}>
                  {color}
                </option>
              ))}
            </select>
          </div>

          {/* الماركة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الماركة
            </label>
            <select
              value={data.brand || ''}
              onChange={(e) => handleChange('brand', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              <option value="">اختر الماركة</option>
              {brandOptions.map((brand) => (
                <option key={brand} value={brand}>
                  {brand}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  );
}
