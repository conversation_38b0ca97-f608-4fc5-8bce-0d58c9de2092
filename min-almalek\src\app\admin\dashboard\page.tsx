'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminLayout from '@/components/admin/AdminLayout';
import { formatNumber } from '@/utils/numberUtils';

interface DashboardStats {
  totalAds: number;
  pendingAds: number;
  activeAds: number;
  totalUsers: number;
  totalRevenue: number;
  todayViews: number;
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalAds: 0,
    pendingAds: 0,
    activeAds: 0,
    totalUsers: 0,
    totalRevenue: 0,
    todayViews: 0
  });
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // التحقق من الجلسة الإدارية
    const adminSession = localStorage.getItem('admin-session');
    if (!adminSession) {
      router.push('/admin/login');
      return;
    }

    // تحميل الإحصائيات
    loadStats();
  }, [router]);

  const loadStats = async () => {
    try {
      // محاكاة تحميل الإحصائيات
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStats({
        totalAds: 1247,
        pendingAds: 23,
        activeAds: 1156,
        totalUsers: 3456,
        totalRevenue: 45600000, // بالليرة السورية
        todayViews: 8934
      });
    } catch (error) {
      console.error('خطأ في تحميل الإحصائيات:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SY', {
      style: 'currency',
      currency: 'SYP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const getStatIcon = (iconType: string, color: string) => {
    const iconClass = `w-6 h-6 ${color}`;
    switch (iconType) {
      case 'chart':
        return (
          <svg className={iconClass} fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
          </svg>
        );
      case 'clock':
        return (
          <svg className={iconClass} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
          </svg>
        );
      case 'check':
        return (
          <svg className={iconClass} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
        );
      case 'users':
        return (
          <svg className={iconClass} fill="currentColor" viewBox="0 0 20 20">
            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
          </svg>
        );
      case 'money':
        return (
          <svg className={iconClass} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
          </svg>
        );
      case 'eye':
        return (
          <svg className={`${iconClass} opacity-70`} fill="currentColor" viewBox="0 0 20 20" style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}>
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
            <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
          </svg>
        );
      default:
        return <span className="text-xl">📊</span>;
    }
  };

  const StatCard = ({ title, value, icon, color, change }: {
    title: string;
    value: string | number;
    icon: string;
    color: string;
    change?: string;
  }) => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className={`text-2xl font-bold ${color}`}>{value}</p>
          {change && (
            <p className="text-xs text-green-600 mt-1">
              ↗️ {change}
            </p>
          )}
        </div>
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${color.replace('text-', 'bg-').replace('-600', '-100')}`}>
          {getStatIcon(icon, color)}
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="w-full space-y-4">
        {/* العنوان */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'Cairo, sans-serif' }}>
            لوحة التحكم الرئيسية
          </h1>
          <p className="text-gray-600 mt-1">
            مرحباً بك في لوحة إدارة موقع من المالك
          </p>
        </div>

        {/* الإحصائيات الرئيسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <StatCard
            title="إجمالي الإعلانات"
            value={formatNumber(stats.totalAds)}
            icon="chart"
            color="text-blue-600"
            change="+12% هذا الشهر"
          />
          <StatCard
            title="إعلانات في الانتظار"
            value={formatNumber(stats.pendingAds)}
            icon="clock"
            color="text-orange-600"
            change="تحتاج مراجعة"
          />
          <StatCard
            title="إعلانات نشطة"
            value={formatNumber(stats.activeAds)}
            icon="check"
            color="text-green-600"
            change="+8% هذا الأسبوع"
          />
          <StatCard
            title="إجمالي المستخدمين"
            value={formatNumber(stats.totalUsers)}
            icon="users"
            color="text-purple-600"
            change="+15% هذا الشهر"
          />
          <StatCard
            title="الإيرادات الشهرية"
            value={formatCurrency(stats.totalRevenue)}
            icon="money"
            color="text-emerald-600"
            change="+23% من الشهر الماضي"
          />
          <StatCard
            title="مشاهدات اليوم"
            value={stats.todayViews.toLocaleString()}
            icon="eye"
            color="text-indigo-600"
            change="+5% من أمس"
          />
        </div>

        {/* الإجراءات السريعة */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">الإجراءات السريعة</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button
              onClick={() => router.push('/admin/ads/pending')}
              className="flex items-center gap-3 p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors"
            >
              <div className="w-8 h-8 flex items-center justify-center">
                <svg className="w-6 h-6 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="text-right">
                <p className="font-medium text-gray-900">مراجعة الإعلانات</p>
                <p className="text-sm text-gray-600">{stats.pendingAds} في الانتظار</p>
              </div>
            </button>

            <button
              onClick={() => router.push('/admin/users')}
              className="flex items-center gap-3 p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
            >
              <div className="w-8 h-8 flex items-center justify-center">
                <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                </svg>
              </div>
              <div className="text-right">
                <p className="font-medium text-gray-900">إدارة المستخدمين</p>
                <p className="text-sm text-gray-600">{stats.totalUsers} مستخدم</p>
              </div>
            </button>

            <button
              onClick={() => router.push('/admin/reports')}
              className="flex items-center gap-3 p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors"
            >
              <div className="w-8 h-8 flex items-center justify-center">
                <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                </svg>
              </div>
              <div className="text-right">
                <p className="font-medium text-gray-900">التقارير</p>
                <p className="text-sm text-gray-600">إحصائيات مفصلة</p>
              </div>
            </button>

            <button
              onClick={() => router.push('/admin/settings')}
              className="flex items-center gap-3 p-4 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <div className="w-8 h-8 flex items-center justify-center">
                <svg className="w-6 h-6 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="text-right">
                <p className="font-medium text-gray-900">الإعدادات</p>
                <p className="text-sm text-gray-600">إعدادات الموقع</p>
              </div>
            </button>
          </div>
        </div>

        {/* الرسوم البيانية والتحليلات */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          {/* رسم بياني للإعلانات الشهرية */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">الإعلانات الشهرية</h3>
              <select className="text-sm border border-gray-300 rounded-lg px-3 py-1">
                <option>آخر 6 أشهر</option>
                <option>آخر 12 شهر</option>
              </select>
            </div>
            <div className="grid grid-cols-6 gap-2 h-40 items-end">
              {[85, 92, 78, 105, 87, 98].map((height, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div
                    className="bg-primary-500 rounded-t-sm transition-all duration-1000 hover:bg-primary-600 cursor-pointer w-full"
                    style={{ height: `${height}%` }}
                    title={`الشهر ${index + 1}: ${Math.round(height * 12)} إعلان`}
                  ></div>
                  <span className="text-xs text-gray-500 mt-2">
                    {['ديسمبر', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو'][index]}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* توزيع الإعلانات حسب التصنيف */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">توزيع الإعلانات حسب التصنيف</h3>
            <div className="space-y-4">
              {[
                { category: 'العقارات', count: 456, percentage: 36.6, color: 'bg-blue-500' },
                { category: 'السيارات', count: 312, percentage: 25.0, color: 'bg-red-500' },
                { category: 'الإلكترونيات', count: 189, percentage: 15.2, color: 'bg-purple-500' },
                { category: 'الوظائف', count: 156, percentage: 12.5, color: 'bg-green-500' },
                { category: 'أخرى', count: 134, percentage: 10.7, color: 'bg-gray-500' }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 ${item.color} rounded-full`}></div>
                    <span className="text-sm text-gray-700">{item.category}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div className={`${item.color} h-2 rounded-full`} style={{ width: `${item.percentage}%` }}></div>
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-8">{item.count}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* النشاط الأخير */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-900">النشاط الأخير</h2>
              <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                عرض الكل
              </button>
            </div>
            <div className="space-y-3">
              {[
                { action: 'إعلان جديد تم إرساله للمراجعة', time: 'منذ 5 دقائق', type: 'pending', user: 'أحمد محمد' },
                { action: 'مستخدم جديد انضم للموقع', time: 'منذ 15 دقيقة', type: 'user', user: 'سارة أحمد' },
                { action: 'تم قبول إعلان عقار في دمشق', time: 'منذ 30 دقيقة', type: 'approved', user: 'محمد علي' },
                { action: 'دفعة جديدة تم استلامها', time: 'منذ ساعة', type: 'payment', user: 'شركة الأمل' },
                { action: 'تم رفض إعلان مخالف', time: 'منذ ساعتين', type: 'rejected', user: 'خالد يوسف' },
              ].map((activity, index) => (
                <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                    activity.type === 'pending' ? 'bg-orange-100 text-orange-600' :
                    activity.type === 'user' ? 'bg-blue-100 text-blue-600' :
                    activity.type === 'approved' ? 'bg-green-100 text-green-600' :
                    activity.type === 'payment' ? 'bg-purple-100 text-purple-600' :
                    'bg-red-100 text-red-600'
                  }`}>
                    {activity.type === 'pending' ? '⏳' :
                     activity.type === 'user' ? '👤' :
                     activity.type === 'approved' ? '✅' :
                     activity.type === 'payment' ? '💰' : '❌'}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                    <p className="text-xs text-gray-500">بواسطة {activity.user} • {activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* الإشعارات والتنبيهات */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-900">التنبيهات والإشعارات</h2>
              <span className="bg-red-100 text-red-600 text-xs px-2 py-1 rounded-full">3 جديد</span>
            </div>
            <div className="space-y-3">
              {[
                {
                  title: 'إعلانات تحتاج مراجعة',
                  message: '23 إعلان في انتظار المراجعة',
                  type: 'warning',
                  action: 'مراجعة الآن'
                },
                {
                  title: 'مساحة التخزين',
                  message: 'تم استخدام 85% من مساحة التخزين',
                  type: 'info',
                  action: 'إدارة الملفات'
                },
                {
                  title: 'نسخة احتياطية',
                  message: 'آخر نسخة احتياطية منذ 3 أيام',
                  type: 'error',
                  action: 'إنشاء نسخة'
                },
                {
                  title: 'تحديث النظام',
                  message: 'يتوفر تحديث جديد للنظام',
                  type: 'success',
                  action: 'تحديث'
                }
              ].map((alert, index) => (
                <div key={index} className={`p-4 rounded-lg border ${
                  alert.type === 'warning' ? 'bg-yellow-50 border-yellow-200' :
                  alert.type === 'info' ? 'bg-blue-50 border-blue-200' :
                  alert.type === 'error' ? 'bg-red-50 border-red-200' :
                  'bg-green-50 border-green-200'
                }`}>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h4 className={`font-medium text-sm ${
                        alert.type === 'warning' ? 'text-yellow-800' :
                        alert.type === 'info' ? 'text-blue-800' :
                        alert.type === 'error' ? 'text-red-800' :
                        'text-green-800'
                      }`}>{alert.title}</h4>
                      <p className={`text-xs mt-1 ${
                        alert.type === 'warning' ? 'text-yellow-700' :
                        alert.type === 'info' ? 'text-blue-700' :
                        alert.type === 'error' ? 'text-red-700' :
                        'text-green-700'
                      }`}>{alert.message}</p>
                    </div>
                    <button className={`text-xs px-2 py-1 rounded ${
                      alert.type === 'warning' ? 'bg-yellow-200 text-yellow-800 hover:bg-yellow-300' :
                      alert.type === 'info' ? 'bg-blue-200 text-blue-800 hover:bg-blue-300' :
                      alert.type === 'error' ? 'bg-red-200 text-red-800 hover:bg-red-300' :
                      'bg-green-200 text-green-800 hover:bg-green-300'
                    } transition-colors`}>
                      {alert.action}
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* أدوات سريعة إضافية */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">أدوات إدارية سريعة</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {[
              { name: 'نسخ احتياطي', icon: '💾', color: 'bg-blue-100 text-blue-600' },
              { name: 'تنظيف الملفات', icon: '🧹', color: 'bg-green-100 text-green-600' },
              { name: 'فحص الأمان', icon: '🔒', color: 'bg-red-100 text-red-600' },
              { name: 'تحسين الأداء', icon: '⚡', color: 'bg-yellow-100 text-yellow-600' },
              { name: 'إرسال إشعار', icon: '📢', color: 'bg-purple-100 text-purple-600' },
              { name: 'تقرير شامل', icon: '📊', color: 'bg-indigo-100 text-indigo-600' }
            ].map((tool, index) => (
              <button key={index} className={`p-4 rounded-lg ${tool.color} hover:opacity-80 transition-opacity`}>
                <div className="text-2xl mb-2">{tool.icon}</div>
                <div className="text-sm font-medium">{tool.name}</div>
              </button>
            ))}
          </div>
        </div>
        </div>
      </div>
    </AdminLayout>
  );
}
