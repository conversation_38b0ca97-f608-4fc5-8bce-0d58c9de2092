'use client';

import { useState } from 'react';
import Link from 'next/link';

const syrianGovernorates = [
  {
    id: 'damascus',
    name: 'دمشق',
    arabicName: 'دمشق',
    adsCount: 15420,
    mainAreas: ['المالكي', 'أبو رمانة', 'المزة', 'كفر سوسة', 'القصاع']
  },
  {
    id: 'damascus-countryside',
    name: 'ريف دمشق',
    arabicName: 'ريف دمشق',
    adsCount: 8930,
    mainAreas: ['جرمانا', 'صحنايا', 'دوما', 'قطنا', 'التل']
  },
  {
    id: 'aleppo',
    name: 'حلب',
    arabicName: 'حلب',
    adsCount: 12650,
    mainAreas: ['الفرقان', 'الحمدانية', 'السليمانية', 'العزيزية', 'الشهباء']
  },
  {
    id: 'homs',
    name: 'حمص',
    arabicName: 'حمص',
    adsCount: 6780,
    mainAreas: ['الوعر', 'الإنشاءات', 'الغوطة', 'الخالدية', 'باب السباع']
  },
  {
    id: 'hama',
    name: 'حماة',
    arabicName: 'حماة',
    adsCount: 4520,
    mainAreas: ['الحميدية', 'الصابونية', 'كازو', 'الشريعة', 'الأربعين']
  },
  {
    id: 'lattakia',
    name: 'اللاذقية',
    arabicName: 'اللاذقية',
    adsCount: 5680,
    mainAreas: ['الزراعة', 'الرمل الشمالي', 'الرمل الجنوبي', 'الصليبة', 'الشاطئ الأزرق']
  },
  {
    id: 'tartous',
    name: 'طرطوس',
    arabicName: 'طرطوس',
    adsCount: 3240,
    mainAreas: ['وسط المدينة', 'الكورنيش', 'الثورة', 'الوحدة', 'الشهداء']
  },
  {
    id: 'idlib',
    name: 'إدلب',
    arabicName: 'إدلب',
    adsCount: 2150,
    mainAreas: ['وسط المدينة', 'الشمال', 'الجنوب', 'الشرق', 'الغرب']
  },
  {
    id: 'daraa',
    name: 'درعا',
    arabicName: 'درعا',
    adsCount: 2890,
    mainAreas: ['درعا البلد', 'درعا المحطة', 'الصنمين', 'إزرع', 'بصرى الشام']
  },
  {
    id: 'sweida',
    name: 'السويداء',
    arabicName: 'السويداء',
    adsCount: 1980,
    mainAreas: ['وسط المدينة', 'الكرامة', 'الثورة', 'صلخد', 'شهبا']
  },
  {
    id: 'quneitra',
    name: 'القنيطرة',
    arabicName: 'القنيطرة',
    adsCount: 890,
    mainAreas: ['القنيطرة', 'خان أرنبة', 'فيق', 'مسعدة', 'عين التينة']
  },
  {
    id: 'deir-ez-zor',
    name: 'دير الزور',
    arabicName: 'دير الزور',
    adsCount: 3450,
    mainAreas: ['وسط المدينة', 'الحويقة', 'الجورة', 'الميادين', 'البوكمال']
  },
  {
    id: 'raqqa',
    name: 'الرقة',
    arabicName: 'الرقة',
    adsCount: 2760,
    mainAreas: ['وسط المدينة', 'الرشيد', 'الثورة', 'تل أبيض', 'عين عيسى']
  },
  {
    id: 'hasaka',
    name: 'الحسكة',
    arabicName: 'الحسكة',
    adsCount: 3120,
    mainAreas: ['وسط المدينة', 'غويران', 'الناصرة', 'القامشلي', 'رأس العين']
  },
  {
    id: 'latakia',
    name: 'اللاذقية',
    arabicName: 'اللاذقية',
    adsCount: 5890,
    mainAreas: ['الزراعة', 'الرمل الشمالي', 'الرمل الجنوبي', 'الصليبة', 'الشاطئ الأزرق']
  },
  {
    id: 'tartus',
    name: 'طرطوس',
    arabicName: 'طرطوس',
    adsCount: 3240,
    mainAreas: ['المشتل', 'الثورة', 'الكورنيش', 'المنطقة الصناعية', 'الحميدية']
  },
  {
    id: 'daraa',
    name: 'درعا',
    arabicName: 'درعا',
    adsCount: 2180,
    mainAreas: ['درعا البلد', 'درعا المحطة', 'الصنمين', 'إنخل', 'نوى']
  },
  {
    id: 'as-suwayda',
    name: 'السويداء',
    arabicName: 'السويداء',
    adsCount: 1560,
    mainAreas: ['السويداء', 'صلخد', 'شهبا', 'قنوات', 'المزرعة']
  },
  {
    id: 'quneitra',
    name: 'القنيطرة',
    arabicName: 'القنيطرة',
    adsCount: 890,
    mainAreas: ['القنيطرة', 'خان أرنبة', 'فيق', 'مسعدة', 'جباتا الخشب']
  },
  {
    id: 'deir-ez-zor',
    name: 'دير الزور',
    arabicName: 'دير الزور',
    adsCount: 2340,
    mainAreas: ['دير الزور', 'الميادين', 'البوكمال', 'الحسكة', 'الشحيل']
  },
  {
    id: 'ar-raqqah',
    name: 'الرقة',
    arabicName: 'الرقة',
    adsCount: 1780,
    mainAreas: ['الرقة', 'تل أبيض', 'الثورة', 'سلوك', 'الكرامة']
  },
  {
    id: 'al-hasakah',
    name: 'الحسكة',
    arabicName: 'الحسكة',
    adsCount: 2890,
    mainAreas: ['الحسكة', 'القامشلي', 'رأس العين', 'المالكية', 'عامودا']
  },
  {
    id: 'idlib',
    name: 'إدلب',
    arabicName: 'إدلب',
    adsCount: 1920,
    mainAreas: ['إدلب', 'معرة النعمان', 'سراقب', 'أريحا', 'جسر الشغور']
  }
];

const LocationFilter = () => {
  const [selectedGovernorate, setSelectedGovernorate] = useState<string | null>(null);
  const [showAllGovernorates, setShowAllGovernorates] = useState(false);

  const displayedGovernorates = showAllGovernorates
    ? syrianGovernorates
    : syrianGovernorates.slice(0, 8);

  return (
    <section className="py-12 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">تصفح حسب المحافظة</h2>
          <p className="text-gray-600 text-lg">اختر محافظتك للعثور على الإعلانات القريبة منك</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {displayedGovernorates.map((governorate) => (
            <div
              key={governorate.id}
              className="bg-white rounded-lg shadow-md hover:shadow-lg transition-all duration-300 border border-gray-100 hover:border-primary-200 overflow-hidden group cursor-pointer"
              onClick={() => setSelectedGovernorate(
                selectedGovernorate === governorate.id ? null : governorate.id
              )}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-semibold text-gray-800 group-hover:text-primary-600 transition-colors">
                    {governorate.arabicName}
                  </h3>
                  <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                    {governorate.adsCount.toLocaleString()} إعلان
                  </span>
                </div>

                <div className="space-y-2">
                  {governorate.mainAreas.slice(0, 3).map((area, index) => (
                    <div key={index} className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">{area}</span>
                      <span className="text-gray-400">•</span>
                    </div>
                  ))}
                  {governorate.mainAreas.length > 3 && (
                    <div className="text-sm text-gray-400">
                      +{governorate.mainAreas.length - 3} منطقة أخرى
                    </div>
                  )}
                </div>

                {selectedGovernorate === governorate.id && (
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <div className="grid grid-cols-1 gap-2">
                      {governorate.mainAreas.map((area, index) => (
                        <Link
                          key={index}
                          href={`/location/${governorate.id}/${area}`}
                          className="text-sm text-primary-600 hover:text-primary-700 hover:bg-primary-50 px-2 py-1 rounded transition-colors"
                        >
                          {area}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="bg-gray-50 px-6 py-3 group-hover:bg-primary-50 transition-colors">
                <Link
                  href={`/location/${governorate.id}`}
                  className="text-sm text-gray-600 group-hover:text-primary-600 font-medium flex items-center justify-between"
                >
                  <span>تصفح الإعلانات</span>
                  <span>←</span>
                </Link>
              </div>
            </div>
          ))}
        </div>

        {!showAllGovernorates && syrianGovernorates.length > 8 && (
          <div className="text-center mt-8">
            <button
              onClick={() => setShowAllGovernorates(true)}
              className="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              عرض جميع المحافظات ({syrianGovernorates.length})
            </button>
          </div>
        )}

        {showAllGovernorates && (
          <div className="text-center mt-8">
            <button
              onClick={() => setShowAllGovernorates(false)}
              className="px-6 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
            >
              عرض أقل
            </button>
          </div>
        )}

        {/* Quick Stats */}
        <div className="mt-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl p-8 text-white">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold mb-2">
                {syrianGovernorates.reduce((sum, gov) => sum + gov.adsCount, 0).toLocaleString()}
              </div>
              <div className="text-primary-100">إجمالي الإعلانات</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">{syrianGovernorates.length}</div>
              <div className="text-primary-100">محافظة</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">150+</div>
              <div className="text-primary-100">منطقة</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">24/7</div>
              <div className="text-primary-100">خدمة مستمرة</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LocationFilter;
