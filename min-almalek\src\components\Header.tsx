'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import AuthModal from './AuthModal';
import MyCvLogo from './MyCvLogo';
import Logo from './Logo';
import SafeNavigationButton from './SafeNavigationButton';
import ClientOnlyNotifications from './ClientOnlyNotifications';
import ClientOnlyAccount from './ClientOnlyAccount';
import { useToast } from '@/components/ToastManager';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const toast = useToast();

  // التحقق من حالة تسجيل الدخول عند تحميل المكون
  useEffect(() => {
    const checkLoginStatus = () => {
      const loginStatus = localStorage.getItem('isLoggedIn');
      const userData = localStorage.getItem('currentUser');

      if (loginStatus === 'true' && userData) {
        setIsLoggedIn(true);
        setCurrentUser(JSON.parse(userData));
      }
    };

    checkLoginStatus();
  }, []);

  // دالة تسجيل الخروج
  const handleLogout = () => {
    // إظهار رسالة تسجيل الخروج
    toast.showLogout();

    // إزالة بيانات المستخدم
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('currentUser');
    setIsLoggedIn(false);
    setCurrentUser(null);

    // إعادة تحميل الصفحة بعد تأخير قصير
    setTimeout(() => {
      window.location.reload();
    }, 1500);
  };
  const [authModalTab, setAuthModalTab] = useState<'login' | 'register'>('login');

  return (
    <header className="bg-white shadow-lg border-b border-primary-100">


      {/* Main Header */}
      <div className="container mx-auto px-4 py-2">
        <div className="flex items-center justify-between">
          {/* Logo with Syria Badge - Mobile Optimized */}
          <div className="flex items-center gap-2">
            <Logo variant="transparent" size={isMenuOpen ? "lg" : "xl"} showText={true} href="/" />
            <div className="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold">
              سوريا
            </div>
          </div>

          {/* Desktop Auth Buttons */}
          <div className="hidden md:flex items-center gap-0.5">
            {!isLoggedIn ? (
              // للمستخدمين غير المسجلين
              <>
                {/* الوظائف */}
                <Link
                  href="/jobs"
                  className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors font-medium"
                  title="الوظائف والسير الذاتية - مدعوم من قبل تطبيق MyCv"
                >
                  <MyCvLogo size="xs" variant="square" />
                  <span>الوظائف</span>
                </Link>

                {/* أضف إعلانك - ينقل لتسجيل الدخول */}
                <button
                  onClick={() => {
                    setAuthModalTab('login');
                    setIsAuthModalOpen(true);
                  }}
                  className="px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-semibold"
                >
                  أضف إعلانك
                </button>

                {/* تسجيل الدخول */}
                <button
                  onClick={() => {
                    setAuthModalTab('login');
                    setIsAuthModalOpen(true);
                  }}
                  className="px-4 py-2 text-primary-600 border border-primary-600 rounded-lg hover:bg-primary-50 transition-colors"
                >
                  تسجيل الدخول
                </button>

                {/* إنشاء حساب */}
                <button
                  onClick={() => {
                    setAuthModalTab('register');
                    setIsAuthModalOpen(true);
                  }}
                  className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                >
                  إنشاء حساب
                </button>
              </>
            ) : (
              // للمستخدمين المسجلين
              <>
                {/* الوظائف */}
                <Link
                  href="/jobs"
                  className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors font-medium"
                  title="الوظائف والسير الذاتية - مدعوم من قبل تطبيق MyCv"
                >
                  <MyCvLogo size="xs" variant="square" />
                  <span>الوظائف</span>
                </Link>

                {/* أضف إعلانك */}
                <SafeNavigationButton
                  href="/add-ad"
                  className="px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-semibold"
                >
                  أضف إعلانك
                </SafeNavigationButton>

                {/* المفضلة */}
                <Link
                  href="/favorites"
                  className="relative p-3 text-gray-600 hover:text-green-600 rounded-full hover:bg-gray-100 transition-all duration-300 group"
                  title="المفضلة"
                >
                  <svg
                    className="w-6 h-6 transition-all duration-300 group-hover:scale-110"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    style={{
                      filter: 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.filter = 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))';
                      e.currentTarget.style.color = '#22c55e';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))';
                      e.currentTarget.style.color = '';
                    }}
                  >
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                  </svg>
                </Link>

                {/* الرسائل */}
                <Link
                  href="/messages"
                  className="relative p-3 text-gray-600 hover:text-blue-600 rounded-full hover:bg-gray-100 transition-all duration-300 group"
                  title="الرسائل"
                >
                  <svg
                    className="w-6 h-6 transition-all duration-300 group-hover:scale-110"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                    style={{
                      filter: 'drop-shadow(0 0 0px rgba(59, 130, 246, 0.6))',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.filter = 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.8))';
                      e.currentTarget.style.color = '#3b82f6';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(59, 130, 246, 0.6))';
                      e.currentTarget.style.color = '';
                    }}
                  >
                    <path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                  </svg>

                  {/* عداد الرسائل غير المقروءة */}
                  <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold">
                    2
                  </span>
                </Link>

                {/* الإشعارات */}
                <ClientOnlyNotifications />

                {/* حسابي */}
                <ClientOnlyAccount />

                {/* تسجيل الخروج */}
                <button
                  onClick={handleLogout}
                  className="text-red-600 hover:text-red-700 px-4 py-2 rounded-lg hover:bg-red-50 transition-colors"
                  title="تسجيل الخروج"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M16 17v-3H9v-4h7V7l5 5-5 5M14 2a2 2 0 012 2v2h-2V4H4v16h10v-2h2v2a2 2 0 01-2 2H4a2 2 0 01-2-2V4a2 2 0 012-2h10z"/>
                  </svg>
                </button>
              </>
            )}
          </div>

          {/* Mobile Right Side */}
          <div className="md:hidden flex items-center gap-2">
            {/* المفضلة للموبايل */}
            <Link
              href="/favorites"
              className="relative p-2 text-gray-600 hover:text-green-600 rounded-full hover:bg-gray-100 transition-all duration-300"
              title="المفضلة"
            >
              <svg
                className="w-5 h-5"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
              </svg>
            </Link>

            {/* الرسائل للموبايل */}
            <Link
              href="/messages"
              className="relative p-2 text-gray-600 hover:text-blue-600 rounded-full hover:bg-gray-100 transition-all duration-300"
              title="الرسائل"
            >
              <svg
                className="w-5 h-5"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
              </svg>

              {/* عداد الرسائل غير المقروءة للموبايل */}
              <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-bold text-[10px]">
                2
              </span>
            </Link>

            {/* الإشعارات للموبايل */}
            <ClientOnlyNotifications />

            {/* حسابي للموبايل */}
            <ClientOnlyAccount />

            {/* زر إضافة إعلان للموبايل */}
            <SafeNavigationButton
              href="/add-ad"
              className="px-3 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-medium text-sm"
            >
              + إعلان
            </SafeNavigationButton>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <span className="text-xl">{isMenuOpen ? '✕' : '☰'}</span>
            </button>
          </div>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:block mt-4 border-t pt-4">
          <ul className="flex flex-wrap gap-6 text-gray-700">
            <li><SafeNavigationButton href="/" className="hover:text-primary-600 font-medium bg-transparent border-0 p-0">الرئيسية</SafeNavigationButton></li>
            <li><SafeNavigationButton href="/ads" className="hover:text-primary-600 bg-transparent border-0 p-0">جميع الإعلانات</SafeNavigationButton></li>
            <li><SafeNavigationButton href="/jobs" className="hover:text-primary-600 font-medium flex items-center gap-1 bg-transparent border-0 p-0">
              <MyCvLogo size="xs" variant="square" />
              <span>الوظائف</span>
            </SafeNavigationButton></li>
            {isLoggedIn && (
              <>
                <li><SafeNavigationButton href="/notifications" className="hover:text-primary-600 bg-transparent border-0 p-0">الإشعارات</SafeNavigationButton></li>
                <li><SafeNavigationButton href="/map" className="hover:text-primary-600 bg-transparent border-0 p-0">الخريطة</SafeNavigationButton></li>
                <li><SafeNavigationButton href="/compare" className="hover:text-primary-600 bg-transparent border-0 p-0">المقارنة</SafeNavigationButton></li>
              </>
            )}
            <li><SafeNavigationButton href="/category/real-estate" className="hover:text-primary-600 bg-transparent border-0 p-0">العقارات</SafeNavigationButton></li>
            <li><SafeNavigationButton href="/category/cars" className="hover:text-primary-600 bg-transparent border-0 p-0">السيارات</SafeNavigationButton></li>
            <li><SafeNavigationButton href="/category/electronics" className="hover:text-primary-600 bg-transparent border-0 p-0">الإلكترونيات</SafeNavigationButton></li>
            <li><SafeNavigationButton href="/category/services" className="hover:text-primary-600 bg-transparent border-0 p-0">الخدمات</SafeNavigationButton></li>
            <li><SafeNavigationButton href="/subscription" className="hover:text-primary-600 font-medium bg-transparent border-0 p-0">💎 الاشتراكات</SafeNavigationButton></li>
          </ul>
        </nav>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white border-t shadow-lg">
          <div className="container mx-auto px-4 py-4 max-h-screen overflow-y-auto">
            {/* أزرار المصادقة للموبايل - تظهر فقط للمستخدمين غير المسجلين */}
            {!isLoggedIn && (
              <div className="flex flex-col gap-3 mb-6">
                <button
                  onClick={() => {
                    setAuthModalTab('login');
                    setIsAuthModalOpen(true);
                    setIsMenuOpen(false);
                  }}
                  className="w-full px-4 py-3 text-primary-600 border border-primary-600 rounded-lg hover:bg-primary-50 transition-colors font-medium"
                >
                  تسجيل الدخول
                </button>
                <button
                  onClick={() => {
                    setAuthModalTab('register');
                    setIsAuthModalOpen(true);
                    setIsMenuOpen(false);
                  }}
                  className="w-full px-4 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium"
                >
                  إنشاء حساب جديد
                </button>
              </div>
            )}

            {/* روابط التنقل الرئيسية */}
            <div className="mb-6">
              <h3 className="font-semibold text-gray-800 mb-4 text-lg">التنقل السريع</h3>
              <div className="grid grid-cols-2 gap-3">
                <SafeNavigationButton
                  href="/"
                  className="p-4 text-center bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors border border-gray-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className="text-2xl mb-2">🏠</div>
                  <div className="text-sm font-medium">الرئيسية</div>
                </SafeNavigationButton>

                <SafeNavigationButton
                  href="/ads"
                  className="p-4 text-center bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors border border-blue-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className="text-2xl mb-2">📋</div>
                  <div className="text-sm font-medium text-blue-700">جميع الإعلانات</div>
                </SafeNavigationButton>

                <SafeNavigationButton
                  href="/jobs"
                  className="p-4 text-center bg-green-50 rounded-xl hover:bg-green-100 transition-colors border border-green-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className="flex justify-center mb-2">
                    <MyCvLogo size="sm" variant="square" />
                  </div>
                  <div className="text-sm font-medium text-green-700">الوظائف</div>
                </SafeNavigationButton>

                <SafeNavigationButton
                  href="/map"
                  className="p-4 text-center bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors border border-gray-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className="text-2xl mb-2">🗺️</div>
                  <div className="text-sm font-medium">الخريطة</div>
                </SafeNavigationButton>
              </div>
            </div>

            {/* التصنيفات الرئيسية */}
            <div className="mb-6">
              <h3 className="font-semibold text-gray-800 mb-4 text-lg">التصنيفات الرئيسية</h3>
              <div className="grid grid-cols-2 gap-3">
                <SafeNavigationButton
                  href="/category/real-estate"
                  className="p-4 text-center bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors border border-blue-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className="text-2xl mb-2">🏘️</div>
                  <div className="text-sm font-medium text-blue-700">العقارات</div>
                </SafeNavigationButton>

                <SafeNavigationButton
                  href="/category/cars"
                  className="p-4 text-center bg-red-50 rounded-xl hover:bg-red-100 transition-colors border border-red-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className="text-2xl mb-2">🚗</div>
                  <div className="text-sm font-medium text-red-700">السيارات</div>
                </SafeNavigationButton>

                <SafeNavigationButton
                  href="/category/electronics"
                  className="p-4 text-center bg-purple-50 rounded-xl hover:bg-purple-100 transition-colors border border-purple-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className="text-2xl mb-2">📱</div>
                  <div className="text-sm font-medium text-purple-700">الإلكترونيات</div>
                </SafeNavigationButton>

                <SafeNavigationButton
                  href="/category/services"
                  className="p-4 text-center bg-orange-50 rounded-xl hover:bg-orange-100 transition-colors border border-orange-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className="text-2xl mb-2">🛠️</div>
                  <div className="text-sm font-medium text-orange-700">الخدمات</div>
                </SafeNavigationButton>

                <SafeNavigationButton
                  href="/subscription"
                  className="p-4 text-center bg-yellow-50 rounded-xl hover:bg-yellow-100 transition-colors border border-yellow-200"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <div className="text-2xl mb-2">💎</div>
                  <div className="text-sm font-medium text-yellow-700">الاشتراكات</div>
                </SafeNavigationButton>
              </div>
            </div>

            {/* روابط إضافية */}
            <div className="border-t border-gray-200 pt-4">
              <div className="flex flex-col gap-2">
                <SafeNavigationButton
                  href="/notifications"
                  className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <span className="text-xl">🔔</span>
                  <span className="font-medium">الإشعارات</span>
                </SafeNavigationButton>

                <SafeNavigationButton
                  href="/compare"
                  className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  <span className="text-xl">⚖️</span>
                  <span className="font-medium">المقارنة</span>
                </SafeNavigationButton>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        defaultTab={authModalTab}
      />
    </header>
  );
};

export default Header;
