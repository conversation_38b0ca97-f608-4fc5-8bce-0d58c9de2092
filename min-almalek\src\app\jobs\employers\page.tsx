'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useAuth } from '@/contexts/AuthContext';
import { JobPosting } from '@/lib/jobs';

export default function EmployersJobsPage() {
  const { user, isAuthenticated } = useAuth();
  const [myJobs, setMyJobs] = useState<JobPosting[]>([]);
  const [stats, setStats] = useState({
    totalJobs: 0,
    activeJobs: 0,
    totalApplications: 0,
    viewsThisMonth: 0
  });

  // التحقق من صلاحية الوصول
  const isEmployer = user?.userType === 'business' || user?.userType === 'real-estate-office';

  if (!isAuthenticated || !isEmployer) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="py-16">
          <div className="container mx-auto px-4 text-center">
            <div
              className="text-6xl mb-4 transition-all duration-300 hover:scale-110 cursor-pointer inline-block"
              style={{
                filter: 'grayscale(1) opacity(0.7) drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.filter = 'grayscale(0) opacity(1) drop-shadow(0 0 12px rgba(34, 197, 94, 0.8))';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.filter = 'grayscale(1) opacity(0.7) drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))';
              }}
            >
              🔒
            </div>
            <h1 className="text-3xl font-bold text-gray-800 mb-4">الوصول مقيد</h1>
            <p className="text-xl text-gray-600 mb-8">
              هذه الصفحة متاحة فقط للشركات والمكاتب العقارية
            </p>
            <Link
              href="/jobs"
              className="inline-flex items-center gap-2 px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              العودة للوظائف
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="py-8">
        <div className="container mx-auto px-4">
          {/* العنوان الرئيسي */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">🏢 إدارة الوظائف</h1>
            <p className="text-xl text-gray-600">انشر وظائفك واعثر على أفضل المواهب</p>
          </div>

          {/* الإحصائيات */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white/30 backdrop-blur-sm rounded-xl shadow-lg p-6 text-center group hover:bg-white/40 transition-all duration-300 border border-white/40">
              <div
                className="text-3xl mb-2 transition-all duration-300 hover:scale-110 cursor-pointer"
                style={{
                  filter: 'grayscale(1) opacity(0.7) drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.filter = 'grayscale(0) opacity(1) drop-shadow(0 0 12px rgba(34, 197, 94, 0.8))';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.filter = 'grayscale(1) opacity(0.7) drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))';
                }}
              >
                📊
              </div>
              <div className="text-2xl font-bold text-gray-800">{stats.totalJobs}</div>
              <div className="text-gray-600">إجمالي الوظائف</div>
            </div>
            <div className="bg-white rounded-xl shadow-lg p-6 text-center">
              <div className="text-3xl mb-2 glassy-icon group-active:glow" style={{opacity:0.7,filter:'drop-shadow(0 0 16px #22c55e)'}}>✅</div>
              <div className="text-2xl font-bold text-green-600">{stats.activeJobs}</div>
              <div className="text-gray-600">وظائف نشطة</div>
            </div>
            <div className="bg-white rounded-xl shadow-lg p-6 text-center">
              <div className="text-3xl mb-2 glassy-icon group-active:glow" style={{opacity:0.7,filter:'drop-shadow(0 0 16px #6366f1)'}}>📝</div>
              <div className="text-2xl font-bold text-blue-600">{stats.totalApplications}</div>
              <div className="text-gray-600">إجمالي الطلبات</div>
            </div>
            <div className="bg-white rounded-xl shadow-lg p-6 text-center">
              <div className="text-3xl mb-2 glassy-icon group-active:glow" style={{opacity:0.7,filter:'drop-shadow(0 0 16px #a78bfa)'}}>👁️</div>
              <div className="text-2xl font-bold text-purple-600">{stats.viewsThisMonth}</div>
              <div className="text-gray-600">مشاهدات هذا الشهر</div>
            </div>
          </div>

          {/* أزرار الإجراءات السريعة */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {/* نشر وظيفة جديدة */}
            <div className="bg-white/30 backdrop-blur-sm rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 border border-white/40 group hover:bg-white/40">
              <div className="text-center">
                <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-4xl glassy-icon group-active:glow" style={{opacity:0.3,filter:'drop-shadow(0 0 22px #93c5fd)'}}>➕</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-3">انشر وظيفة جديدة</h3>
                <p className="text-gray-600 mb-6">أضف وظيفة جديدة واعثر على أفضل المرشحين</p>
                <Link
                  href="/jobs/post"
                  className="inline-block w-full py-4 glassy-bg text-blue-700 rounded-lg hover:bg-blue-200 active:glow active:scale-110 transition-all duration-200 font-semibold text-lg border border-blue-200"
                >
                  نشر وظيفة
                </Link>
              </div>
            </div>

            {/* إدارة الوظائف */}
            <div className="bg-white rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 border border-gray-100 group">
              <div className="text-center">
            <div className="w-24 h-24 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-4xl glassy-icon group-active:glow" style={{opacity:0.3,filter:'drop-shadow(0 0 22px #6ee7b7)'}}>⚙️</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-3">إدارة الوظائف</h3>
                <p className="text-gray-600 mb-6">عرض وتعديل الوظائف المنشورة</p>
                <Link
                  href="/jobs/employers/manage"
                  className="inline-block w-full py-4 glassy-bg text-green-700 rounded-lg hover:bg-green-200 active:glow active:scale-110 transition-all duration-200 font-semibold text-lg border border-green-200"
                >
                  إدارة الوظائف
                </Link>
              </div>
            </div>

            {/* طلبات التوظيف */}
            <div className="bg-white rounded-2xl shadow-xl p-8 hover:shadow-2xl transition-all duration-300 border border-gray-100 group">
              <div className="text-center">
            <div className="w-24 h-24 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-4xl glassy-icon group-active:glow" style={{opacity:0.3,filter:'drop-shadow(0 0 22px #ddd6fe)'}}>📋</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-3">طلبات التوظيف</h3>
                <p className="text-gray-600 mb-6">مراجعة طلبات المرشحين</p>
                <Link
                  href="/jobs/employers/applications"
                  className="inline-block w-full py-4 glassy-bg text-purple-700 rounded-lg hover:bg-purple-200 active:glow active:scale-110 transition-all duration-200 font-semibold text-lg border border-purple-200"
                >
                  مراجعة الطلبات
                </Link>
              </div>
            </div>
          </div>

          {/* الوظائف الحديثة */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-800">الوظائف المنشورة مؤخراً</h2>
              <Link
                href="/jobs/employers/manage"
                className="text-primary-600 hover:text-primary-700 font-medium"
              >
                عرض الكل →
              </Link>
            </div>

            {myJobs.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📝</div>
                <h3 className="text-xl font-bold text-gray-600 mb-2">لم تنشر أي وظائف بعد</h3>
                <p className="text-gray-500 mb-6">ابدأ بنشر وظيفتك الأولى للعثور على أفضل المواهب</p>
                <Link
                  href="/jobs/post"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                >
                  <span>➕</span>
                  انشر وظيفة جديدة
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {myJobs.slice(0, 3).map((job) => (
                  <div key={job.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-bold text-lg text-gray-800">{job.title}</h3>
                        <p className="text-gray-600">{job.department} • {job.location}</p>
                        <div className="flex items-center gap-4 mt-2 text-sm text-gray-500">
                          <span>📅 {new Date(job.postedDate).toLocaleDateString('ar-SA')}</span>
                          <span>👥 {job.applicationsCount} طلب</span>
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            job.status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {job.status}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Link
                          href={`/jobs/employers/edit/${job.id}`}
                          className="px-4 py-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        >
                          تعديل
                        </Link>
                        <Link
                          href={`/jobs/${job.id}`}
                          className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                        >
                          عرض
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* ميزات إضافية للشركات */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="bg-white rounded-xl shadow-lg p-6 text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl" style={{opacity:1,filter:'drop-shadow(0 0 16px #fb923c)'}}>📊</span>
              </div>
              <h3 className="text-lg font-bold text-gray-800 mb-2">لوحة التحكم</h3>
              <p className="text-gray-600 mb-4">تحكم كامل في إعلاناتك وإحصائياتك</p>
              <Link
                href="/dashboard"
                className="text-orange-600 hover:text-orange-700 font-medium"
              >
                الدخول للوحة →
              </Link>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6 text-center">
              <div className="w-16 h-16 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl" style={{opacity:1,filter:'drop-shadow(0 0 16px #2dd4bf)'}}>📈</span>
              </div>
              <h3 className="text-lg font-bold text-gray-800 mb-2">التقارير والتحليلات</h3>
              <p className="text-gray-600 mb-4">تقارير مفصلة عن أداء إعلاناتك</p>
              <Link
                href="/reports"
                className="text-teal-600 hover:text-teal-700 font-medium"
              >
                عرض التقارير →
              </Link>
            </div>

            <div className="bg-white rounded-xl shadow-lg p-6 text-center">
              <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl" style={{opacity:1,filter:'drop-shadow(0 0 16px #818cf8)'}}>👥</span>
              </div>
              <h3 className="text-lg font-bold text-gray-800 mb-2">إدارة الفريق</h3>
              <p className="text-gray-600 mb-4">إضافة وإدارة أعضاء فريق العمل</p>
              <Link
                href="/team"
                className="text-indigo-600 hover:text-indigo-700 font-medium"
              >
                إدارة الفريق →
              </Link>
            </div>
          </div>

          {/* نصائح للموظفين */}
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-8 border border-blue-200">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">💡 نصائح لجذب أفضل المرشحين</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <span className="text-blue-600">✓</span>
                  <span>اكتب وصف وظيفة واضح ومفصل</span>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-blue-600">✓</span>
                  <span>حدد المتطلبات والمهارات المطلوبة بدقة</span>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-blue-600">✓</span>
                  <span>اذكر المزايا والحوافز المقدمة</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <span className="text-blue-600">✓</span>
                  <span>استخدم الكلمات المفتاحية المناسبة</span>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-blue-600">✓</span>
                  <span>حدث إعلانات الوظائف بانتظام</span>
                </div>
                <div className="flex items-start gap-3">
                  <span className="text-blue-600">✓</span>
                  <span>رد على طلبات المرشحين بسرعة</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* شعار MyCv */}
        <div className="text-center py-8 border-t border-gray-200">
          <div className="flex items-center justify-center gap-3 text-gray-600">
            <span>مدعوم من قبل</span>
            <div className="flex items-center gap-2">
              <img
                src="/images/MyCv.png"
                alt="MyCv"
                className="h-8 w-auto"
              />
              <span className="font-semibold">MyCv - منصة متكاملة للسير الذاتية والتوظيف</span>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
