'use client';

import { useState, useEffect, createContext, useContext, ReactNode } from 'react';

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  actionText?: string;
  icon?: string;
  category?: 'ad' | 'payment' | 'system' | 'user' | 'general';
}

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  clearAll: () => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider = ({ children }: NotificationProviderProps) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [isClient, setIsClient] = useState(false);

  // تحديد أننا في العميل
  useEffect(() => {
    setIsClient(true);
  }, []);

  // تحميل الإشعارات من localStorage عند بدء التطبيق
  useEffect(() => {
    if (!isClient) return;

    const savedNotifications = localStorage.getItem('notifications');
    if (savedNotifications) {
      try {
        const parsed = JSON.parse(savedNotifications);
        // التأكد من أن parsed هو مصفوفة
        if (Array.isArray(parsed)) {
          setNotifications(parsed.map((n: any) => ({
            ...n,
            timestamp: new Date(n.timestamp)
          })));
        } else {
          // إذا لم يكن مصفوفة، قم بمسح البيانات المعطوبة
          localStorage.removeItem('notifications');
          setNotifications([]);
        }
      } catch (error) {
        console.error('Error loading notifications:', error);
        // مسح البيانات المعطوبة
        localStorage.removeItem('notifications');
        setNotifications([]);
      }
    }
  }, [isClient]);

  // حفظ الإشعارات في localStorage عند تغييرها
  useEffect(() => {
    if (!isClient) return;
    localStorage.setItem('notifications', JSON.stringify(notifications));
  }, [notifications, isClient]);

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      read: false,
    };

    setNotifications(prev => [newNotification, ...prev]);

    // إزالة الإشعارات القديمة (أكثر من 50 إشعار)
    setNotifications(prev => prev.slice(0, 50));
  };

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  const clearAll = () => {
    setNotifications([]);
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        addNotification,
        markAsRead,
        markAllAsRead,
        removeNotification,
        clearAll,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

// مكون عرض الإشعار الفردي
interface NotificationItemProps {
  notification: Notification;
  onMarkAsRead: (id: string) => void;
  onRemove: (id: string) => void;
}

export const NotificationItem = ({ notification, onMarkAsRead, onRemove }: NotificationItemProps) => {
  const getTypeStyles = () => {
    switch (notification.type) {
      case 'success':
        return 'bg-gradient-to-br from-green-50/80 via-green-100/60 to-green-50/80 backdrop-blur-sm border border-green-200/50 text-green-800 shadow-lg';
      case 'error':
        return 'bg-gradient-to-br from-red-50/80 via-red-100/60 to-red-50/80 backdrop-blur-sm border border-red-200/50 text-red-800 shadow-lg';
      case 'warning':
        return 'bg-gradient-to-br from-yellow-50/80 via-yellow-100/60 to-yellow-50/80 backdrop-blur-sm border border-yellow-200/50 text-yellow-800 shadow-lg';
      case 'info':
        return 'bg-gradient-to-br from-blue-50/80 via-blue-100/60 to-blue-50/80 backdrop-blur-sm border border-blue-200/50 text-blue-800 shadow-lg';
      default:
        return 'bg-gradient-to-br from-gray-50/80 via-gray-100/60 to-gray-50/80 backdrop-blur-sm border border-gray-200/50 text-gray-800 shadow-lg';
    }
  };

  const getTypeIcon = () => {
    if (notification.icon) return notification.icon;

    switch (notification.type) {
      case 'success':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '📢';
    }
  };

  const getIconGlow = () => {
    switch (notification.type) {
      case 'success':
        return 'drop-shadow-[0_0_8px_rgba(34,197,94,0.6)]';
      case 'error':
        return 'drop-shadow-[0_0_8px_rgba(239,68,68,0.6)]';
      case 'warning':
        return 'drop-shadow-[0_0_8px_rgba(245,158,11,0.6)]';
      case 'info':
        return 'drop-shadow-[0_0_8px_rgba(59,130,246,0.6)]';
      default:
        return 'drop-shadow-[0_0_8px_rgba(107,114,128,0.6)]';
    }
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'الآن';
    if (minutes < 60) return `منذ ${minutes} دقيقة`;
    if (hours < 24) return `منذ ${hours} ساعة`;
    if (days < 7) return `منذ ${days} يوم`;
    return date.toLocaleDateString('ar-SA');
  };

  return (
    <div
      className={`p-4 rounded-xl transition-all duration-300 hover:scale-[1.02] ${getTypeStyles()} ${
        !notification.read ? 'ring-2 ring-primary-300/50 shadow-xl' : 'shadow-md'
      }`}
    >
      <div className="flex items-start gap-3">
        <div className={`text-2xl flex-shrink-0 ${getIconGlow()} opacity-90`}>{getTypeIcon()}</div>

        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <h4 className="font-semibold text-sm">{notification.title}</h4>
            <div className="flex items-center gap-2 flex-shrink-0">
              <span className="text-xs opacity-70">{formatTime(notification.timestamp)}</span>
              <button
                onClick={() => onRemove(notification.id)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
                title="حذف الإشعار"
              >
                ×
              </button>
            </div>
          </div>

          <p className="text-sm mt-1 opacity-90">{notification.message}</p>

          <div className="flex items-center gap-3 mt-3">
            {!notification.read && (
              <button
                onClick={() => onMarkAsRead(notification.id)}
                className="text-xs bg-white/70 backdrop-blur-sm hover:bg-white/90 px-3 py-1.5 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md border border-white/30"
              >
                تم القراءة
              </button>
            )}

            {notification.actionUrl && notification.actionText && (
              <a
                href={notification.actionUrl}
                className="text-xs bg-white/70 backdrop-blur-sm hover:bg-white/90 px-3 py-1.5 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md border border-white/30"
              >
                {notification.actionText}
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
