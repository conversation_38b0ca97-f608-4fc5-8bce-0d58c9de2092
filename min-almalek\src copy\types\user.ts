export interface User {
  id: string;
  email: string;
  phone: string;
  name: string;
  avatar?: string;
  userType: 'individual' | 'business' | 'real-estate-office';
  isVerified: boolean;
  createdAt: Date;
  lastLogin: Date;
  
  // معلومات الاشتراك
  subscription?: {
    planId: string;
    planName: string;
    planType: 'individual' | 'business';
    startDate: Date;
    endDate: Date;
    isActive: boolean;
    autoRenew: boolean;
    features: string[];
  };

  // إحصائيات المستخدم
  stats: {
    totalAds: number;
    activeAds: number;
    expiredAds: number;
    totalViews: number;
    totalContacts: number;
    successfulDeals: number;
  };

  // معلومات إضافية للأفراد
  individualInfo?: {
    firstName: string;
    lastName: string;
    dateOfBirth?: Date;
    gender?: 'male' | 'female';
    nationalId?: string;
    address?: {
      governorate: string;
      city: string;
      area: string;
      street?: string;
    };
  };

  // معلومات إضافية للشركات
  businessInfo?: {
    companyName: string;
    businessType: string;
    registrationNumber: string;
    taxNumber?: string;
    establishedYear?: number;
    employeeCount?: string;
    website?: string;
    description?: string;
    address: {
      governorate: string;
      city: string;
      area: string;
      street: string;
      building?: string;
    };
    contactPerson: {
      name: string;
      position: string;
      phone: string;
      email: string;
    };
  };

  // معلومات إضافية للمكاتب العقارية
  realEstateOfficeInfo?: {
    officeName: string;
    licenseNumber: string;
    licenseIssueDate: Date;
    licenseExpiryDate: Date;
    ownerName: string;
    managerName?: string;
    specializations: string[]; // أنواع العقارات المتخصصة
    serviceAreas: string[]; // المناطق التي يخدمها المكتب
    yearsOfExperience: number;
    teamSize: number;
    website?: string;
    socialMedia?: {
      facebook?: string;
      instagram?: string;
      twitter?: string;
      linkedin?: string;
    };
    address: {
      governorate: string;
      city: string;
      area: string;
      street: string;
      building: string;
      floor?: string;
      office?: string;
    };
    workingHours: {
      [key: string]: {
        open: string;
        close: string;
        isOpen: boolean;
      };
    };
    certifications?: string[];
    awards?: string[];
  };

  // الإعدادات
  settings: {
    language: 'ar' | 'en';
    notifications: {
      email: boolean;
      sms: boolean;
      push: boolean;
      marketing: boolean;
    };
    privacy: {
      showPhone: boolean;
      showEmail: boolean;
      allowMessages: boolean;
      showOnlineStatus: boolean;
    };
    preferences: {
      currency: 'SYP' | 'USD' | 'EUR';
      theme: 'light' | 'dark' | 'auto';
      autoSave: boolean;
    };
  };
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  confirmPassword: string;
  phone: string;
  name: string;
  userType: 'individual' | 'business' | 'real-estate-office';
  acceptTerms: boolean;
  acceptPrivacy: boolean;
  
  // بيانات إضافية حسب نوع المستخدم
  individualInfo?: Partial<User['individualInfo']>;
  businessInfo?: Partial<User['businessInfo']>;
  realEstateOfficeInfo?: Partial<User['realEstateOfficeInfo']>;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface UserSession {
  token: string;
  refreshToken: string;
  expiresAt: Date;
  user: User;
}
