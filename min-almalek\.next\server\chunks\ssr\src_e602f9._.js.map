{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/hooks/useNotificationHelpers.ts"], "sourcesContent": ["import { useNotifications } from '@/components/NotificationSystem';\n\nexport const useNotificationHelpers = () => {\n  const { addNotification } = useNotifications();\n\n  // إشعارات الإعلانات\n  const notifyAdPublished = (adTitle: string, adId: string) => {\n    addNotification({\n      type: 'success',\n      title: 'تم نشر إعلانك بنجاح!',\n      message: `تم نشر إعلان \"${adTitle}\" وهو الآن مرئي للمستخدمين.`,\n      category: 'ad',\n      icon: '🎉',\n      actionUrl: `/ad/${adId}`,\n      actionText: 'عرض الإعلان'\n    });\n  };\n\n  const notifyAdUnderReview = (adTitle: string, adId: string) => {\n    addNotification({\n      type: 'warning',\n      title: 'إعلانك قيد المراجعة',\n      message: `إعلان \"${adTitle}\" قيد المراجعة من قبل فريقنا. سيتم نشره خلال 24 ساعة.`,\n      category: 'ad',\n      icon: '⏳',\n      actionUrl: `/ad/${adId}`,\n      actionText: 'عرض الإعلان'\n    });\n  };\n\n  const notifyAdRejected = (adTitle: string, reason: string, adId: string) => {\n    addNotification({\n      type: 'error',\n      title: 'تم رفض إعلانك',\n      message: `إعلان \"${adTitle}\" تم رفضه. السبب: ${reason}`,\n      category: 'ad',\n      icon: '❌',\n      actionUrl: `/ad/edit/${adId}`,\n      actionText: 'تعديل الإعلان'\n    });\n  };\n\n  // إشعارات الدفع\n  const notifyPaymentSuccess = (amount: number, currency: string, planName: string) => {\n    addNotification({\n      type: 'success',\n      title: 'تم الدفع بنجاح',\n      message: `تم استلام دفعة بقيمة ${amount.toLocaleString()} ${currency} لباقة ${planName}.`,\n      category: 'payment',\n      icon: '💳',\n      actionUrl: '/subscription',\n      actionText: 'عرض الاشتراكات'\n    });\n  };\n\n  const notifyPaymentFailed = (reason: string) => {\n    addNotification({\n      type: 'error',\n      title: 'فشل في الدفع',\n      message: `لم نتمكن من إتمام عملية الدفع. ${reason}`,\n      category: 'payment',\n      icon: '❌',\n      actionUrl: '/subscription',\n      actionText: 'إعادة المحاولة'\n    });\n  };\n\n  const notifySubscriptionExpiring = (planName: string, daysLeft: number) => {\n    addNotification({\n      type: 'warning',\n      title: 'اشتراكك ينتهي قريباً',\n      message: `باقة ${planName} ستنتهي خلال ${daysLeft} أيام. جدد اشتراكك لتجنب انقطاع الخدمة.`,\n      category: 'payment',\n      icon: '⏰',\n      actionUrl: '/subscription',\n      actionText: 'تجديد الاشتراك'\n    });\n  };\n\n  // إشعارات النظام\n  const notifySystemMaintenance = (startTime: string, duration: string) => {\n    addNotification({\n      type: 'info',\n      title: 'صيانة مجدولة للنظام',\n      message: `سيتم إجراء صيانة للنظام في ${startTime} لمدة ${duration}. قد تواجه انقطاع مؤقت في الخدمة.`,\n      category: 'system',\n      icon: '🔧'\n    });\n  };\n\n  const notifySystemUpdate = (features: string[]) => {\n    addNotification({\n      type: 'info',\n      title: 'تحديث جديد للنظام',\n      message: `تم إضافة ميزات جديدة: ${features.join(', ')}. استكشف التحديثات الجديدة!`,\n      category: 'system',\n      icon: '🆕',\n      actionUrl: '/about',\n      actionText: 'تعرف على الجديد'\n    });\n  };\n\n  // إشعارات المستخدم\n  const notifyProfileUpdated = () => {\n    addNotification({\n      type: 'success',\n      title: 'تم تحديث ملفك الشخصي',\n      message: 'تم حفظ التغييرات على ملفك الشخصي بنجاح.',\n      category: 'user',\n      icon: '👤'\n    });\n  };\n\n  const notifyPasswordChanged = () => {\n    addNotification({\n      type: 'success',\n      title: 'تم تغيير كلمة المرور',\n      message: 'تم تغيير كلمة المرور بنجاح. إذا لم تقم بهذا التغيير، يرجى التواصل معنا فوراً.',\n      category: 'user',\n      icon: '🔒'\n    });\n  };\n\n  const notifyLoginFromNewDevice = (deviceInfo: string, location: string) => {\n    addNotification({\n      type: 'warning',\n      title: 'تسجيل دخول من جهاز جديد',\n      message: `تم تسجيل الدخول من ${deviceInfo} في ${location}. إذا لم تكن أنت، يرجى تغيير كلمة المرور فوراً.`,\n      category: 'user',\n      icon: '🔐'\n    });\n  };\n\n  // إشعارات عامة\n  const notifyWelcome = (userName: string) => {\n    addNotification({\n      type: 'success',\n      title: `🌟 أهلاً وسهلاً ${userName}!`,\n      message: 'مرحباً بك في منصة من المالك - منصتك المفضلة للإعلانات المبوبة. نتمنى لك تجربة مميزة واستكشاف جميع الميزات الاحترافية المتاحة.',\n      category: 'general',\n      icon: '✨',\n      actionUrl: '/dashboard',\n      actionText: 'استكشف لوحة التحكم'\n    });\n  };\n\n  const notifyNewFeature = (featureName: string, description: string) => {\n    addNotification({\n      type: 'info',\n      title: `🚀 ميزة جديدة: ${featureName}`,\n      message: `${description} - نحن نعمل باستمرار على تطوير منصة من المالك لتقديم أفضل تجربة لك.`,\n      category: 'general',\n      icon: '🎉',\n      actionUrl: '/features',\n      actionText: 'اكتشف المزيد'\n    });\n  };\n\n  // إشعارات الأخطاء\n  const notifyError = (title: string, message: string) => {\n    addNotification({\n      type: 'error',\n      title: `⚠️ ${title}`,\n      message: `${message} - إذا استمرت المشكلة، يرجى التواصل مع فريق الدعم.`,\n      category: 'system',\n      icon: '🚨'\n    });\n  };\n\n  const notifySuccess = (title: string, message: string) => {\n    addNotification({\n      type: 'success',\n      title: `🎉 ${title}`,\n      message: `${message} - شكراً لاستخدامك منصة من المالك!`,\n      category: 'general',\n      icon: '✨'\n    });\n  };\n\n  return {\n    // إشعارات الإعلانات\n    notifyAdPublished,\n    notifyAdUnderReview,\n    notifyAdRejected,\n    \n    // إشعارات الدفع\n    notifyPaymentSuccess,\n    notifyPaymentFailed,\n    notifySubscriptionExpiring,\n    \n    // إشعارات النظام\n    notifySystemMaintenance,\n    notifySystemUpdate,\n    \n    // إشعارات المستخدم\n    notifyProfileUpdated,\n    notifyPasswordChanged,\n    notifyLoginFromNewDevice,\n    \n    // إشعارات عامة\n    notifyWelcome,\n    notifyNewFeature,\n    \n    // إشعارات عامة للأخطاء والنجاح\n    notifyError,\n    notifySuccess\n  };\n};\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,yBAAyB;IACpC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD;IAE3C,oBAAoB;IACpB,MAAM,oBAAoB,CAAC,SAAiB;QAC1C,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,cAAc,EAAE,QAAQ,2BAA2B,CAAC;YAC9D,UAAU;YACV,MAAM;YACN,WAAW,CAAC,IAAI,EAAE,MAAM;YACxB,YAAY;QACd;IACF;IAEA,MAAM,sBAAsB,CAAC,SAAiB;QAC5C,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,OAAO,EAAE,QAAQ,qDAAqD,CAAC;YACjF,UAAU;YACV,MAAM;YACN,WAAW,CAAC,IAAI,EAAE,MAAM;YACxB,YAAY;QACd;IACF;IAEA,MAAM,mBAAmB,CAAC,SAAiB,QAAgB;QACzD,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,OAAO,EAAE,QAAQ,kBAAkB,EAAE,QAAQ;YACvD,UAAU;YACV,MAAM;YACN,WAAW,CAAC,SAAS,EAAE,MAAM;YAC7B,YAAY;QACd;IACF;IAEA,gBAAgB;IAChB,MAAM,uBAAuB,CAAC,QAAgB,UAAkB;QAC9D,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,qBAAqB,EAAE,OAAO,cAAc,GAAG,CAAC,EAAE,SAAS,OAAO,EAAE,SAAS,CAAC,CAAC;YACzF,UAAU;YACV,MAAM;YACN,WAAW;YACX,YAAY;QACd;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,+BAA+B,EAAE,QAAQ;YACnD,UAAU;YACV,MAAM;YACN,WAAW;YACX,YAAY;QACd;IACF;IAEA,MAAM,6BAA6B,CAAC,UAAkB;QACpD,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,SAAS,aAAa,EAAE,SAAS,uCAAuC,CAAC;YAC1F,UAAU;YACV,MAAM;YACN,WAAW;YACX,YAAY;QACd;IACF;IAEA,iBAAiB;IACjB,MAAM,0BAA0B,CAAC,WAAmB;QAClD,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,2BAA2B,EAAE,UAAU,MAAM,EAAE,SAAS,iCAAiC,CAAC;YACpG,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,sBAAsB,EAAE,SAAS,IAAI,CAAC,MAAM,2BAA2B,CAAC;YAClF,UAAU;YACV,MAAM;YACN,WAAW;YACX,YAAY;QACd;IACF;IAEA,mBAAmB;IACnB,MAAM,uBAAuB;QAC3B,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,wBAAwB;QAC5B,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,2BAA2B,CAAC,YAAoB;QACpD,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,mBAAmB,EAAE,WAAW,IAAI,EAAE,SAAS,+CAA+C,CAAC;YACzG,UAAU;YACV,MAAM;QACR;IACF;IAEA,eAAe;IACf,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;YACd,MAAM;YACN,OAAO,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;YACrC,SAAS;YACT,UAAU;YACV,MAAM;YACN,WAAW;YACX,YAAY;QACd;IACF;IAEA,MAAM,mBAAmB,CAAC,aAAqB;QAC7C,gBAAgB;YACd,MAAM;YACN,OAAO,CAAC,eAAe,EAAE,aAAa;YACtC,SAAS,GAAG,YAAY,mEAAmE,CAAC;YAC5F,UAAU;YACV,MAAM;YACN,WAAW;YACX,YAAY;QACd;IACF;IAEA,kBAAkB;IAClB,MAAM,cAAc,CAAC,OAAe;QAClC,gBAAgB;YACd,MAAM;YACN,OAAO,CAAC,GAAG,EAAE,OAAO;YACpB,SAAS,GAAG,QAAQ,kDAAkD,CAAC;YACvE,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,CAAC,OAAe;QACpC,gBAAgB;YACd,MAAM;YACN,OAAO,CAAC,GAAG,EAAE,OAAO;YACpB,SAAS,GAAG,QAAQ,kCAAkC,CAAC;YACvD,UAAU;YACV,MAAM;QACR;IACF;IAEA,OAAO;QACL,oBAAoB;QACpB;QACA;QACA;QAEA,gBAAgB;QAChB;QACA;QACA;QAEA,iBAAiB;QACjB;QACA;QAEA,mBAAmB;QACnB;QACA;QACA;QAEA,eAAe;QACf;QACA;QAEA,+BAA+B;QAC/B;QACA;IACF;AACF"}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/AuthModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useNotificationHelpers } from '@/hooks/useNotificationHelpers';\nimport { useToast } from '@/components/ToastManager';\nimport Logo from './Logo';\n\ninterface AuthModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  defaultTab?: 'login' | 'register';\n}\n\nconst AuthModal = ({ isOpen, onClose, defaultTab = 'login' }: AuthModalProps) => {\n  const { login, register, isLoading, error } = useAuth();\n  const { notifyWelcome } = useNotificationHelpers();\n  const toast = useToast();\n\n  const [activeTab, setActiveTab] = useState<'login' | 'register'>(defaultTab);\n  const [userType, setUserType] = useState<'individual' | 'business' | 'real-estate-office'>('individual');\n  const [loginMethod, setLoginMethod] = useState<'email' | 'phone'>('email');\n  const [rememberMe, setRememberMe] = useState(false);\n  const [agreeToTerms, setAgreeToTerms] = useState(false);\n  const [companyType, setCompanyType] = useState('');\n  const [isGoogleLoading, setIsGoogleLoading] = useState(false);\n  const [isFacebookLoading, setIsFacebookLoading] = useState(false);\n\n  // بيانات النموذج\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    firstName: '',\n    lastName: '',\n    companyName: '',\n  });\n\n  // أنواع الشركات المتاحة\n  const companyTypes = [\n    { value: 'real-estate', label: 'شركة عقارية' },\n    { value: 'commercial', label: 'شركة تجارية' },\n    { value: 'organization', label: 'منظمة' },\n    { value: 'institution', label: 'مؤسسة' },\n    { value: 'government', label: 'مؤسسة حكومية' },\n    { value: 'non-profit', label: 'مؤسسة غير ربحية' },\n    { value: 'marketing', label: 'شركة تسويق' },\n    { value: 'public-company', label: 'شركة مساهمة عامة' },\n    { value: 'limited-company', label: 'شركة محدودة المسؤولية' },\n    { value: 'partnership', label: 'شركة تضامن' },\n    { value: 'holding', label: 'شركة قابضة' },\n    { value: 'consulting', label: 'شركة استشارية' },\n    { value: 'manufacturing', label: 'شركة تصنيع' },\n    { value: 'services', label: 'شركة خدمات' },\n    { value: 'technology', label: 'شركة تقنية' },\n    { value: 'other', label: 'أخرى' }\n  ];\n\n  // تحديث التبويب عند تغيير defaultTab\n  useEffect(() => {\n    setActiveTab(defaultTab);\n  }, [defaultTab]);\n\n  // إعادة تعيين نوع الشركة عند تغيير نوع المستخدم\n  useEffect(() => {\n    if (userType === 'individual') {\n      setCompanyType('');\n    }\n  }, [userType]);\n\n  // معالج تسجيل الدخول\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // التحقق من بيانات Mahmut Madenli\n    if (formData.email === '<EMAIL>' && formData.password === 'Ma123456') {\n      // تسجيل دخول ناجح\n      const userData = {\n        id: 'user-mahmut-001',\n        name: 'Mahmut Madenli',\n        email: '<EMAIL>',\n        userType: 'individual',\n        individualInfo: {\n          firstName: 'Mahmut',\n          lastName: 'Madenli'\n        },\n        membershipId: 'MIN-2024-001247',\n        createdAt: '2024-01-15',\n        subscriptionPlan: 'premium',\n        verificationBadge: 'silver'\n      };\n\n      // حفظ بيانات المستخدم في localStorage\n      localStorage.setItem('currentUser', JSON.stringify(userData));\n      localStorage.setItem('isLoggedIn', 'true');\n\n      // إظهار رسالة ترحيب\n      toast.showWelcome(userData.name);\n\n      // إظهار رسالة نجاح تسجيل الدخول\n      setTimeout(() => {\n        toast.showSuccess('تم تسجيل الدخول بنجاح!', 'مرحباً بك مرة أخرى في منصة من المالك');\n      }, 1000);\n\n      onClose();\n\n      // إعادة تحميل الصفحة لتحديث الحالة\n      setTimeout(() => {\n        window.location.reload();\n      }, 2000);\n    } else {\n      alert('بيانات تسجيل الدخول غير صحيحة');\n    }\n  };\n\n  // معالج التسجيل\n  const handleRegister = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      await register({\n        email: formData.email,\n        password: formData.password,\n        confirmPassword: formData.confirmPassword,\n        phone: formData.phone,\n        name: `${formData.firstName} ${formData.lastName}`,\n        userType,\n        acceptTerms: agreeToTerms,\n        acceptPrivacy: agreeToTerms,\n        individualInfo: userType === 'individual' ? {\n          firstName: formData.firstName,\n          lastName: formData.lastName,\n        } : undefined,\n        businessInfo: userType === 'business' ? {\n          companyName: formData.companyName,\n          businessType: companyType,\n          contactPerson: {\n            name: `${formData.firstName} ${formData.lastName}`,\n            position: 'مدير',\n            phone: formData.phone,\n            email: formData.email,\n          },\n          address: {\n            governorate: '',\n            city: '',\n            area: '',\n            street: '',\n          },\n        } : undefined,\n        realEstateOfficeInfo: userType === 'real-estate-office' ? {\n          officeName: formData.companyName,\n          licenseNumber: '',\n          licenseIssueDate: new Date(),\n          licenseExpiryDate: new Date(),\n          ownerName: `${formData.firstName} ${formData.lastName}`,\n          specializations: [],\n          serviceAreas: [],\n          yearsOfExperience: 0,\n          teamSize: 1,\n          address: {\n            governorate: '',\n            city: '',\n            area: '',\n            street: '',\n            building: '',\n          },\n          workingHours: {\n            sunday: { open: '09:00', close: '17:00', isOpen: true },\n            monday: { open: '09:00', close: '17:00', isOpen: true },\n            tuesday: { open: '09:00', close: '17:00', isOpen: true },\n            wednesday: { open: '09:00', close: '17:00', isOpen: true },\n            thursday: { open: '09:00', close: '17:00', isOpen: true },\n            friday: { open: '09:00', close: '12:00', isOpen: true },\n            saturday: { open: '09:00', close: '17:00', isOpen: true },\n          },\n        } : undefined,\n      });\n\n      // إظهار رسالة ترحيب للمستخدم الجديد\n      toast.showWelcome(`${formData.firstName} ${formData.lastName}`);\n\n      // إظهار رسالة نجاح التسجيل\n      setTimeout(() => {\n        toast.showSuccess('تم إنشاء حسابك بنجاح!', 'مرحباً بك في منصة من المالك. يمكنك الآن الاستفادة من جميع الخدمات');\n      }, 1500);\n\n      onClose();\n    } catch (error) {\n      console.error('Register error:', error);\n    }\n  };\n\n  // معالج تسجيل الدخول بـ Google\n  const handleGoogleAuth = async () => {\n    setIsGoogleLoading(true);\n    try {\n      // TODO: Implement Google OAuth integration\n      console.log('Google Auth initiated');\n\n      // محاكاة عملية التحقق\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // هنا سيتم إضافة منطق Google OAuth الفعلي\n      alert('سيتم تفعيل تسجيل الدخول بـ Google قريباً');\n\n    } catch (error) {\n      console.error('Google Auth Error:', error);\n      alert('حدث خطأ في تسجيل الدخول بـ Google');\n    } finally {\n      setIsGoogleLoading(false);\n    }\n  };\n\n  // معالج تسجيل الدخول بـ Facebook\n  const handleFacebookAuth = async () => {\n    setIsFacebookLoading(true);\n    try {\n      // TODO: Implement Facebook OAuth integration\n      console.log('Facebook Auth initiated');\n\n      // محاكاة عملية التحقق\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // هنا سيتم إضافة منطق Facebook OAuth الفعلي\n      alert('سيتم تفعيل تسجيل الدخول بـ Facebook قريباً');\n\n    } catch (error) {\n      console.error('Facebook Auth Error:', error);\n      alert('حدث خطأ في تسجيل الدخول بـ Facebook');\n    } finally {\n      setIsFacebookLoading(false);\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"p-6 border-b\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <Logo variant=\"transparent\" size=\"md\" showText={false} />\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n            >\n              ×\n            </button>\n          </div>\n          <h2 className=\"text-2xl font-bold text-gray-800 text-center\">\n            {activeTab === 'login' ? 'تسجيل الدخول' : 'إنشاء حساب جديد'}\n          </h2>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex border-b\">\n          <button\n            onClick={() => setActiveTab('login')}\n            className={`flex-1 py-3 px-4 text-center font-medium ${\n              activeTab === 'login'\n                ? 'text-primary-600 border-b-2 border-primary-600'\n                : 'text-gray-500 hover:text-gray-700'\n            }`}\n          >\n            تسجيل الدخول\n          </button>\n          <button\n            onClick={() => setActiveTab('register')}\n            className={`flex-1 py-3 px-4 text-center font-medium ${\n              activeTab === 'register'\n                ? 'text-primary-600 border-b-2 border-primary-600'\n                : 'text-gray-500 hover:text-gray-700'\n            }`}\n          >\n            إنشاء حساب\n          </button>\n        </div>\n\n        <div className=\"p-6\">\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\n              <p className=\"text-sm text-red-800\">{error}</p>\n            </div>\n          )}\n\n          {activeTab === 'login' ? (\n            <form onSubmit={handleLogin} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  البريد الإلكتروني\n                </label>\n                <input\n                  type=\"email\"\n                  value={formData.email}\n                  onChange={(e) => setFormData({...formData, email: e.target.value})}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                  placeholder=\"<EMAIL>\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  كلمة المرور\n                </label>\n                <input\n                  type=\"password\"\n                  value={formData.password}\n                  onChange={(e) => setFormData({...formData, password: e.target.value})}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                  placeholder=\"••••••••\"\n                  required\n                />\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={rememberMe}\n                    onChange={(e) => setRememberMe(e.target.checked)}\n                    className=\"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                  />\n                  <span className=\"mr-2 text-sm text-gray-600\">تذكرني</span>\n                </label>\n                <a href=\"#\" className=\"text-sm text-primary-600 hover:text-primary-700\">\n                  نسيت كلمة المرور؟\n                </a>\n              </div>\n\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"w-full bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}\n              </button>\n            </form>\n          ) : (\n            <div className=\"space-y-4\">\n              {/* User Type Selection */}\n              <div className=\"mb-6\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                  نوع الحساب\n                </label>\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <button\n                    onClick={() => setUserType('individual')}\n                    className={`p-4 border-2 rounded-lg text-center ${\n                      userType === 'individual'\n                        ? 'border-primary-600 bg-primary-50 text-primary-700'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                  >\n                    <div className=\"text-2xl mb-2\">👤</div>\n                    <div className=\"font-medium\">فرد</div>\n                    <div className=\"text-xs text-gray-500\">حساب شخصي</div>\n                  </button>\n                  <button\n                    onClick={() => setUserType('business')}\n                    className={`p-4 border-2 rounded-lg text-center ${\n                      userType === 'business'\n                        ? 'border-primary-600 bg-primary-50 text-primary-700'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                  >\n                    <div className=\"text-2xl mb-2\">🏢</div>\n                    <div className=\"font-medium\">شركة</div>\n                    <div className=\"text-xs text-gray-500\">حساب تجاري</div>\n                  </button>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    الاسم الأول\n                  </label>\n                  <input\n                    type=\"text\"\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                    placeholder=\"أحمد\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    الاسم الأخير\n                  </label>\n                  <input\n                    type=\"text\"\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                    placeholder=\"محمد\"\n                  />\n                </div>\n              </div>\n\n              {userType === 'business' && (\n                <>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      اسم الشركة\n                    </label>\n                    <input\n                      type=\"text\"\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                      placeholder=\"اسم الشركة\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      نوع الشركة <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      value={companyType}\n                      onChange={(e) => setCompanyType(e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 bg-white\"\n                      required\n                    >\n                      <option value=\"\">اختر نوع الشركة</option>\n                      {companyTypes.map((type) => (\n                        <option key={type.value} value={type.value}>\n                          {type.label}\n                        </option>\n                      ))}\n                    </select>\n                    {companyType === '' && (\n                      <p className=\"mt-1 text-sm text-gray-500\">\n                        يرجى اختيار نوع الشركة لإكمال التسجيل\n                      </p>\n                    )}\n                  </div>\n                </>\n              )}\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  البريد الإلكتروني\n                </label>\n                <input\n                  type=\"email\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  رقم الموبايل السوري\n                </label>\n                <div className=\"flex\">\n                  <span className=\"inline-flex items-center px-3 border border-l-0 border-gray-300 bg-gray-50 text-gray-500 rounded-r-lg\">\n                    +963\n                  </span>\n                  <input\n                    type=\"tel\"\n                    className=\"flex-1 px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                    placeholder=\"9X XXX XXXX\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  كلمة المرور\n                </label>\n                <input\n                  type=\"password\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                  placeholder=\"••••••••\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  تأكيد كلمة المرور\n                </label>\n                <input\n                  type=\"password\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                  placeholder=\"••••••••\"\n                />\n              </div>\n\n              <div className=\"flex items-start\">\n                <input\n                  type=\"checkbox\"\n                  checked={agreeToTerms}\n                  onChange={(e) => setAgreeToTerms(e.target.checked)}\n                  className=\"mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                />\n                <span className=\"mr-2 text-sm text-gray-600\">\n                  أوافق على{' '}\n                  <a href=\"/terms\" target=\"_blank\" className=\"text-primary-600 hover:text-primary-700 underline\">شروط الاستخدام</a>\n                  {' '}و{' '}\n                  <a href=\"/privacy\" target=\"_blank\" className=\"text-primary-600 hover:text-primary-700 underline\">سياسة الخصوصية</a>\n                </span>\n              </div>\n\n              {/* رسالة تنبيه للشركات */}\n              {userType === 'business' && companyType === '' && agreeToTerms && (\n                <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-3\">\n                  <div className=\"flex items-center gap-2\">\n                    <span className=\"text-yellow-600\">⚠️</span>\n                    <p className=\"text-sm text-yellow-800\">\n                      يرجى اختيار نوع الشركة لإكمال عملية التسجيل\n                    </p>\n                  </div>\n                </div>\n              )}\n\n              <button\n                disabled={!agreeToTerms || (userType === 'business' && companyType === '')}\n                className={`w-full py-3 rounded-lg transition-colors font-medium ${\n                  agreeToTerms && (userType === 'individual' || companyType !== '')\n                    ? 'bg-primary-600 text-white hover:bg-primary-700'\n                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                }`}\n              >\n                إنشاء حساب\n              </button>\n            </div>\n          )}\n\n          {/* Social Login */}\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">أو</span>\n              </div>\n            </div>\n\n            <div className=\"mt-6 grid grid-cols-1 gap-3\">\n              {/* Google Sign In */}\n              <button\n                onClick={handleGoogleAuth}\n                disabled={isGoogleLoading || isFacebookLoading}\n                className={`w-full inline-flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg bg-white text-sm font-medium transition-all ${\n                  isGoogleLoading || isFacebookLoading\n                    ? 'text-gray-400 cursor-not-allowed'\n                    : 'text-gray-700 hover:bg-gray-50 hover:border-gray-400'\n                }`}\n              >\n                {isGoogleLoading ? (\n                  <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-gray-400 mr-3\"></div>\n                ) : (\n                  <svg className=\"w-5 h-5 mr-3\" viewBox=\"0 0 24 24\">\n                    <path fill=\"#4285F4\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n                    <path fill=\"#34A853\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n                    <path fill=\"#FBBC05\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n                    <path fill=\"#EA4335\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n                  </svg>\n                )}\n                {isGoogleLoading\n                  ? 'جاري التحقق...'\n                  : activeTab === 'login'\n                    ? 'تسجيل الدخول بـ Google'\n                    : 'إنشاء حساب بـ Google'\n                }\n              </button>\n\n              {/* Facebook Sign In */}\n              <button\n                onClick={handleFacebookAuth}\n                disabled={isGoogleLoading || isFacebookLoading}\n                className={`w-full inline-flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg bg-white text-sm font-medium transition-all ${\n                  isGoogleLoading || isFacebookLoading\n                    ? 'text-gray-400 cursor-not-allowed'\n                    : 'text-gray-700 hover:bg-gray-50 hover:border-gray-400'\n                }`}\n              >\n                {isFacebookLoading ? (\n                  <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-gray-400 mr-3\"></div>\n                ) : (\n                  <svg className=\"w-5 h-5 mr-3\" viewBox=\"0 0 24 24\">\n                    <path fill=\"#1877F2\" d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                  </svg>\n                )}\n                {isFacebookLoading\n                  ? 'جاري التحقق...'\n                  : activeTab === 'login'\n                    ? 'تسجيل الدخول بـ Facebook'\n                    : 'إنشاء حساب بـ Facebook'\n                }\n              </button>\n            </div>\n\n            {/* رسالة تنبيه للتسجيل الاجتماعي */}\n            <div className=\"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n              <div className=\"flex items-start gap-2\">\n                <span className=\"text-blue-600 mt-0.5\">ℹ️</span>\n                <div className=\"text-sm text-blue-800\">\n                  <p className=\"font-medium mb-1\">تسجيل الدخول الاجتماعي</p>\n                  <p>\n                    عند التسجيل بـ Google أو Facebook، ستتمكن من الوصول السريع لحسابك\n                    وستحتفظ بجميع إعلاناتك وتفضيلاتك.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AuthModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAcA,MAAM,YAAY,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,OAAO,EAAkB;IAC1E,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACpD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,yBAAsB,AAAD;IAC/C,MAAM,QAAQ,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IAErB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoD;IAC3F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,iBAAiB;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,OAAO;QACP,WAAW;QACX,UAAU;QACV,aAAa;IACf;IAEA,wBAAwB;IACxB,MAAM,eAAe;QACnB;YAAE,OAAO;YAAe,OAAO;QAAc;QAC7C;YAAE,OAAO;YAAc,OAAO;QAAc;QAC5C;YAAE,OAAO;YAAgB,OAAO;QAAQ;QACxC;YAAE,OAAO;YAAe,OAAO;QAAQ;QACvC;YAAE,OAAO;YAAc,OAAO;QAAe;QAC7C;YAAE,OAAO;YAAc,OAAO;QAAkB;QAChD;YAAE,OAAO;YAAa,OAAO;QAAa;QAC1C;YAAE,OAAO;YAAkB,OAAO;QAAmB;QACrD;YAAE,OAAO;YAAmB,OAAO;QAAwB;QAC3D;YAAE,OAAO;YAAe,OAAO;QAAa;QAC5C;YAAE,OAAO;YAAW,OAAO;QAAa;QACxC;YAAE,OAAO;YAAc,OAAO;QAAgB;QAC9C;YAAE,OAAO;YAAiB,OAAO;QAAa;QAC9C;YAAE,OAAO;YAAY,OAAO;QAAa;QACzC;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAAS,OAAO;QAAO;KACjC;IAED,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG;QAAC;KAAW;IAEf,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,cAAc;YAC7B,eAAe;QACjB;IACF,GAAG;QAAC;KAAS;IAEb,qBAAqB;IACrB,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAEhB,kCAAkC;QAClC,IAAI,SAAS,KAAK,KAAK,6BAA6B,SAAS,QAAQ,KAAK,YAAY;YACpF,kBAAkB;YAClB,MAAM,WAAW;gBACf,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,UAAU;gBACV,gBAAgB;oBACd,WAAW;oBACX,UAAU;gBACZ;gBACA,cAAc;gBACd,WAAW;gBACX,kBAAkB;gBAClB,mBAAmB;YACrB;YAEA,sCAAsC;YACtC,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;YACnD,aAAa,OAAO,CAAC,cAAc;YAEnC,oBAAoB;YACpB,MAAM,WAAW,CAAC,SAAS,IAAI;YAE/B,gCAAgC;YAChC,WAAW;gBACT,MAAM,WAAW,CAAC,0BAA0B;YAC9C,GAAG;YAEH;YAEA,mCAAmC;YACnC,WAAW;gBACT,OAAO,QAAQ,CAAC,MAAM;YACxB,GAAG;QACL,OAAO;YACL,MAAM;QACR;IACF;IAEA,gBAAgB;IAChB,MAAM,iBAAiB,OAAO;QAC5B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,SAAS;gBACb,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,iBAAiB,SAAS,eAAe;gBACzC,OAAO,SAAS,KAAK;gBACrB,MAAM,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;gBAClD;gBACA,aAAa;gBACb,eAAe;gBACf,gBAAgB,aAAa,eAAe;oBAC1C,WAAW,SAAS,SAAS;oBAC7B,UAAU,SAAS,QAAQ;gBAC7B,IAAI;gBACJ,cAAc,aAAa,aAAa;oBACtC,aAAa,SAAS,WAAW;oBACjC,cAAc;oBACd,eAAe;wBACb,MAAM,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;wBAClD,UAAU;wBACV,OAAO,SAAS,KAAK;wBACrB,OAAO,SAAS,KAAK;oBACvB;oBACA,SAAS;wBACP,aAAa;wBACb,MAAM;wBACN,MAAM;wBACN,QAAQ;oBACV;gBACF,IAAI;gBACJ,sBAAsB,aAAa,uBAAuB;oBACxD,YAAY,SAAS,WAAW;oBAChC,eAAe;oBACf,kBAAkB,IAAI;oBACtB,mBAAmB,IAAI;oBACvB,WAAW,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;oBACvD,iBAAiB,EAAE;oBACnB,cAAc,EAAE;oBAChB,mBAAmB;oBACnB,UAAU;oBACV,SAAS;wBACP,aAAa;wBACb,MAAM;wBACN,MAAM;wBACN,QAAQ;wBACR,UAAU;oBACZ;oBACA,cAAc;wBACZ,QAAQ;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACtD,QAAQ;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACtD,SAAS;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACvD,WAAW;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACzD,UAAU;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACxD,QAAQ;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACtD,UAAU;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;oBAC1D;gBACF,IAAI;YACN;YAEA,oCAAoC;YACpC,MAAM,WAAW,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;YAE9D,2BAA2B;YAC3B,WAAW;gBACT,MAAM,WAAW,CAAC,yBAAyB;YAC7C,GAAG;YAEH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB;QACvB,mBAAmB;QACnB,IAAI;YACF,2CAA2C;YAC3C,QAAQ,GAAG,CAAC;YAEZ,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,0CAA0C;YAC1C,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,iCAAiC;IACjC,MAAM,qBAAqB;QACzB,qBAAqB;QACrB,IAAI;YACF,6CAA6C;YAC7C,QAAQ,GAAG,CAAC;YAEZ,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,4CAA4C;YAC5C,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,UAAI;oCAAC,SAAQ;oCAAc,MAAK;oCAAK,UAAU;;;;;;8CAChD,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAIH,8OAAC;4BAAG,WAAU;sCACX,cAAc,UAAU,iBAAiB;;;;;;;;;;;;8BAK9C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,UACV,mDACA,qCACJ;sCACH;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,mDACA,qCACJ;sCACH;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;;wBACZ,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;wBAIxC,cAAc,wBACb,8OAAC;4BAAK,UAAU;4BAAa,WAAU;;8CACrC,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,YAAY;oDAAC,GAAG,QAAQ;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAA;4CAChE,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,YAAY;oDAAC,GAAG,QAAQ;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAA;4CACnE,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,OAAO;oDAC/C,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAE/C,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDAAkD;;;;;;;;;;;;8CAK1E,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,YAAY,yBAAyB;;;;;;;;;;;iDAI1C,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,YAAY;oDAC3B,WAAW,CAAC,oCAAoC,EAC9C,aAAa,eACT,sDACA,yCACJ;;sEAEF,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,8OAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDACC,SAAS,IAAM,YAAY;oDAC3B,WAAW,CAAC,oCAAoC,EAC9C,aAAa,aACT,sDACA,yCACJ;;sEAEF,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,8OAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAK7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAGhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;gCAKjB,aAAa,4BACZ;;sDACE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;;wDAA+C;sEACnD,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAE5C,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;oDACV,QAAQ;;sEAER,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;gEAAwB,OAAO,KAAK,KAAK;0EACvC,KAAK,KAAK;+DADA,KAAK,KAAK;;;;;;;;;;;gDAK1B,gBAAgB,oBACf,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;8CAQlD,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAwG;;;;;;8DAGxH,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;4CACjD,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;;gDAA6B;gDACjC;8DACV,8OAAC;oDAAE,MAAK;oDAAS,QAAO;oDAAS,WAAU;8DAAoD;;;;;;gDAC9F;gDAAI;gDAAE;8DACP,8OAAC;oDAAE,MAAK;oDAAW,QAAO;oDAAS,WAAU;8DAAoD;;;;;;;;;;;;;;;;;;gCAKpG,aAAa,cAAc,gBAAgB,MAAM,8BAChD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAkB;;;;;;0DAClC,8OAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;;;;;;8CAO7C,8OAAC;oCACC,UAAU,CAAC,gBAAiB,aAAa,cAAc,gBAAgB;oCACvE,WAAW,CAAC,qDAAqD,EAC/D,gBAAgB,CAAC,aAAa,gBAAgB,gBAAgB,EAAE,IAC5D,mDACA,gDACJ;8CACH;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA8B;;;;;;;;;;;;;;;;;8CAIlD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,SAAS;4CACT,UAAU,mBAAmB;4CAC7B,WAAW,CAAC,uIAAuI,EACjJ,mBAAmB,oBACf,qCACA,wDACJ;;gDAED,gCACC,8OAAC;oDAAI,WAAU;;;;;yEAEf,8OAAC;oDAAI,WAAU;oDAAe,SAAQ;;sEACpC,8OAAC;4DAAK,MAAK;4DAAU,GAAE;;;;;;sEACvB,8OAAC;4DAAK,MAAK;4DAAU,GAAE;;;;;;sEACvB,8OAAC;4DAAK,MAAK;4DAAU,GAAE;;;;;;sEACvB,8OAAC;4DAAK,MAAK;4DAAU,GAAE;;;;;;;;;;;;gDAG1B,kBACG,mBACA,cAAc,UACZ,2BACA;;;;;;;sDAKR,8OAAC;4CACC,SAAS;4CACT,UAAU,mBAAmB;4CAC7B,WAAW,CAAC,uIAAuI,EACjJ,mBAAmB,oBACf,qCACA,wDACJ;;gDAED,kCACC,8OAAC;oDAAI,WAAU;;;;;yEAEf,8OAAC;oDAAI,WAAU;oDAAe,SAAQ;8DACpC,cAAA,8OAAC;wDAAK,MAAK;wDAAU,GAAE;;;;;;;;;;;gDAG1B,oBACG,mBACA,cAAc,UACZ,6BACA;;;;;;;;;;;;;8CAMV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAmB;;;;;;kEAChC,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;uCAEe"}}, {"offset": {"line": 1396, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1402, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/MyCvLogo.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\n\ninterface MyCvLogoProps {\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';\n  variant?: 'icon' | 'text' | 'full' | 'square';\n  className?: string;\n  showText?: boolean;\n}\n\nconst MyCvLogo: React.FC<MyCvLogoProps> = ({\n  size = 'md',\n  variant = 'full',\n  className = '',\n  showText = true\n}) => {\n  const sizeClasses = {\n    xs: 'w-4 h-4',\n    sm: 'w-6 h-6',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12',\n    xl: 'w-16 h-16'\n  };\n\n  const textSizes = {\n    xs: 'text-xs',\n    sm: 'text-sm',\n    md: 'text-base',\n    lg: 'text-lg',\n    xl: 'text-xl'\n  };\n\n  const LogoImage = ({ isSquare = false }: { isSquare?: boolean }) => (\n    <div className={`${sizeClasses[size]} relative ${isSquare ? 'rounded-lg overflow-hidden' : ''} ${className}`}>\n      <Image\n        src=\"/images/MyCV (1).jpg\"\n        alt=\"MyCv - منصة متكاملة للسير الذاتية والتوظيف\"\n        fill\n        className=\"object-contain\"\n        priority\n        sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n      />\n    </div>\n  );\n\n  const TextComponent = () => (\n    <span className={`${textSizes[size]} font-bold text-gray-800 ${className}`}>\n      MyCv\n    </span>\n  );\n\n  const FullComponent = () => (\n    <div className={`flex items-center gap-2 ${className}`}>\n      <LogoImage />\n      {showText && <TextComponent />}\n    </div>\n  );\n\n  switch (variant) {\n    case 'icon':\n      return <LogoImage />;\n    case 'square':\n      return <LogoImage isSquare={true} />;\n    case 'text':\n      return <TextComponent />;\n    case 'full':\n    default:\n      return <FullComponent />;\n  }\n};\n\nexport default MyCvLogo;\n"], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,WAAoC,CAAC,EACzC,OAAO,IAAI,EACX,UAAU,MAAM,EAChB,YAAY,EAAE,EACd,WAAW,IAAI,EAChB;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,YAAY;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,YAAY,CAAC,EAAE,WAAW,KAAK,EAA0B,iBAC7D,8OAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,+BAA+B,GAAG,CAAC,EAAE,WAAW;sBAC1G,cAAA,8OAAC,6HAAA,CAAA,UAAK;gBACJ,KAAI;gBACJ,KAAI;gBACJ,IAAI;gBACJ,WAAU;gBACV,QAAQ;gBACR,OAAM;;;;;;;;;;;IAKZ,MAAM,gBAAgB,kBACpB,8OAAC;YAAK,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,yBAAyB,EAAE,WAAW;sBAAE;;;;;;IAK9E,MAAM,gBAAgB,kBACpB,8OAAC;YAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;8BACpD,8OAAC;;;;;gBACA,0BAAY,8OAAC;;;;;;;;;;;IAIlB,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC;;;;;QACV,KAAK;YACH,qBAAO,8OAAC;gBAAU,UAAU;;;;;;QAC9B,KAAK;YACH,qBAAO,8OAAC;;;;;QACV,KAAK;QACL;YACE,qBAAO,8OAAC;;;;;IACZ;AACF;uCAEe"}}, {"offset": {"line": 1501, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1507, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/SafeNavigationButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { useRouter } from 'next/navigation';\n\ninterface SafeNavigationButtonProps {\n  href: string;\n  children: React.ReactNode;\n  className?: string;\n  onClick?: (e: React.MouseEvent) => void;\n  disabled?: boolean;\n  title?: string;\n  [key: string]: any;\n}\n\nconst SafeNavigationButton = ({\n  href,\n  children,\n  className,\n  onClick,\n  disabled = false,\n  title,\n  ...props\n}: SafeNavigationButtonProps) => {\n  const router = useRouter();\n  const [isClient, setIsClient] = useState(false);\n  const [isNavigating, setIsNavigating] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  const handleClick = useCallback((e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (disabled || isNavigating) return;\n\n    if (onClick) {\n      onClick(e);\n    }\n\n    if (isClient && href) {\n      setIsNavigating(true);\n\n      try {\n        // التوجيه المباشر بدون setTimeout\n        router.push(href);\n      } catch (error) {\n        console.error('Navigation error:', error);\n      } finally {\n        // إعادة تعيين حالة التنقل بعد فترة قصيرة\n        setTimeout(() => {\n          setIsNavigating(false);\n        }, 500);\n      }\n    }\n  }, [disabled, isNavigating, onClick, isClient, href, router]);\n\n  // عرض زر عادي قبل الـ hydration\n  if (!isClient) {\n    return (\n      <button\n        className={`${className} relative z-20`}\n        disabled={disabled}\n        title={title}\n        {...props}\n      >\n        {children}\n      </button>\n    );\n  }\n\n  return (\n    <button\n      onClick={handleClick}\n      className={`${className} relative z-20 ${isNavigating ? 'opacity-75 cursor-wait' : ''}`}\n      disabled={disabled || isNavigating}\n      title={title}\n      {...props}\n    >\n      {isNavigating ? (\n        <div className=\"flex items-center gap-2\">\n          <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n          {children}\n        </div>\n      ) : (\n        children\n      )}\n    </button>\n  );\n};\n\nexport default SafeNavigationButton;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAeA,MAAM,uBAAuB,CAAC,EAC5B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,WAAW,KAAK,EAChB,KAAK,EACL,GAAG,OACuB;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,IAAI,YAAY,cAAc;QAE9B,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,IAAI,YAAY,MAAM;YACpB,gBAAgB;YAEhB,IAAI;gBACF,kCAAkC;gBAClC,OAAO,IAAI,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;YACrC,SAAU;gBACR,yCAAyC;gBACzC,WAAW;oBACT,gBAAgB;gBAClB,GAAG;YACL;QACF;IACF,GAAG;QAAC;QAAU;QAAc;QAAS;QAAU;QAAM;KAAO;IAE5D,gCAAgC;IAChC,IAAI,CAAC,UAAU;QACb,qBACE,8OAAC;YACC,WAAW,GAAG,UAAU,cAAc,CAAC;YACvC,UAAU;YACV,OAAO;YACN,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAW,GAAG,UAAU,eAAe,EAAE,eAAe,2BAA2B,IAAI;QACvF,UAAU,YAAY;QACtB,OAAO;QACN,GAAG,KAAK;kBAER,6BACC,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;gBACd;;;;;;mBAGH;;;;;;AAIR;uCAEe"}}, {"offset": {"line": 1597, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1603, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/NotificationBell.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { useAdvancedNotifications } from './AdvancedNotificationSystem';\n\nexport default function NotificationBell() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [filter, setFilter] = useState<string>('all');\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const { \n    notifications, \n    getUnreadCount, \n    markAsRead, \n    markAllAsRead, \n    removeNotification,\n    getNotificationsByCategory \n  } = useAdvancedNotifications();\n\n  const unreadCount = getUnreadCount();\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const filteredNotifications = filter === 'all' \n    ? notifications \n    : getNotificationsByCategory(filter);\n\n  const getTimeAgo = (timestamp: Date) => {\n    const now = new Date();\n    const diff = now.getTime() - timestamp.getTime();\n    const minutes = Math.floor(diff / 60000);\n    const hours = Math.floor(diff / 3600000);\n    const days = Math.floor(diff / 86400000);\n\n    if (minutes < 1) return 'الآن';\n    if (minutes < 60) return `منذ ${minutes} دقيقة`;\n    if (hours < 24) return `منذ ${hours} ساعة`;\n    return `منذ ${days} يوم`;\n  };\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'success': return '✅';\n      case 'error': return '❌';\n      case 'warning': return '⚠️';\n      case 'welcome': return '🎉';\n      case 'payment': return '💳';\n      case 'message': return '💬';\n      case 'favorite': return '❤️';\n      case 'ad_posted': return '📢';\n      case 'search_alert': return '🔍';\n      case 'logout': return '👋';\n      default: return '🔔';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'urgent': return 'border-l-red-500 bg-red-50/50';\n      case 'high': return 'border-l-orange-500 bg-orange-50/50';\n      case 'medium': return 'border-l-blue-500 bg-blue-50/50';\n      case 'low': return 'border-l-gray-500 bg-gray-50/50';\n      default: return 'border-l-gray-500 bg-gray-50/50';\n    }\n  };\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      <style jsx>{`\n        @keyframes bellGlow {\n          0% {\n            filter: grayscale(1) opacity(0.7) brightness(1) drop-shadow(0 0 4px rgba(251, 191, 36, 0.4));\n          }\n          100% {\n            filter: grayscale(0) opacity(1) brightness(1.5) drop-shadow(0 0 8px rgba(251, 191, 36, 0.8)) hue-rotate(45deg) saturate(1.5);\n          }\n        }\n      `}</style>\n      {/* زر الجرس */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-3 text-gray-600 hover:text-gray-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-2 rounded-full hover:bg-gray-100\"\n        aria-label=\"الإشعارات\"\n        style={{\n          filter: isOpen\n            ? 'drop-shadow(0 0 8px rgba(251, 191, 36, 0.8)) brightness(1.3)'\n            : 'none',\n          animation: unreadCount > 0 ? 'bellGlow 2s infinite alternate' : 'none'\n        }}\n      >\n        <span\n          className=\"text-xl transition-all duration-300\"\n          style={{\n            filter: isOpen\n              ? 'brightness(1.5) drop-shadow(0 0 6px rgba(251, 191, 36, 0.8)) hue-rotate(45deg) saturate(1.5)'\n              : 'grayscale(1) opacity(0.7)',\n            textShadow: isOpen\n              ? '0 0 6px rgba(251, 191, 36, 0.6)'\n              : 'none'\n          }}\n        >\n          🔔\n        </span>\n        \n        {/* عداد الإشعارات غير المقروءة */}\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </button>\n\n      {/* قائمة الإشعارات */}\n      {isOpen && (\n        <div className=\"absolute left-0 mt-2 w-80 md:w-96 bg-white/95 backdrop-blur-md rounded-xl shadow-2xl border border-gray-200/50 z-50 max-h-[500px] overflow-hidden\">\n          {/* الرأس */}\n          <div className=\"p-4 border-b border-gray-200/50\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h3 className=\"text-lg font-semibold text-gray-800\">الإشعارات</h3>\n              <div className=\"flex items-center gap-2\">\n                {unreadCount > 0 && (\n                  <button\n                    onClick={markAllAsRead}\n                    className=\"text-sm text-blue-600 hover:text-blue-800 font-medium\"\n                  >\n                    تحديد الكل كمقروء\n                  </button>\n                )}\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n\n            {/* فلاتر */}\n            <div className=\"flex gap-2 overflow-x-auto\">\n              {[\n                { key: 'all', label: 'الكل', count: notifications.length },\n                { key: 'system', label: 'النظام', count: getNotificationsByCategory('system').length },\n                { key: 'social', label: 'اجتماعي', count: getNotificationsByCategory('social').length },\n                { key: 'commerce', label: 'تجاري', count: getNotificationsByCategory('commerce').length },\n                { key: 'user_action', label: 'إجراءات', count: getNotificationsByCategory('user_action').length }\n              ].map(({ key, label, count }) => (\n                <button\n                  key={key}\n                  onClick={() => setFilter(key)}\n                  className={`\n                    px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap transition-colors\n                    ${filter === key \n                      ? 'bg-blue-100 text-blue-800 border border-blue-200' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                    }\n                  `}\n                >\n                  {label} {count > 0 && `(${count})`}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* قائمة الإشعارات */}\n          <div className=\"max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100\">\n            {filteredNotifications.length > 0 ? (\n              filteredNotifications.slice(0, 15).map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`\n                    p-3 border-l-4 hover:bg-gray-50/70 transition-all duration-200 cursor-pointer group\n                    ${getPriorityColor(notification.priority)}\n                    ${!notification.isRead ? 'bg-blue-50/40 shadow-sm' : 'bg-white/20'}\n                    border-b border-gray-100/50 last:border-b-0\n                  `}\n                  onClick={() => {\n                    if (!notification.isRead) {\n                      markAsRead(notification.id);\n                    }\n                    if (notification.actionUrl) {\n                      setIsOpen(false);\n                      window.location.href = notification.actionUrl;\n                    }\n                  }}\n                >\n                  <div className=\"flex items-start gap-3\">\n                    {/* الأيقونة */}\n                    <div className=\"flex-shrink-0 mt-0.5\">\n                      <div className=\"w-8 h-8 rounded-full bg-gray-100/50 flex items-center justify-center\">\n                        <span className=\"text-lg\">\n                          {notification.icon || getNotificationIcon(notification.type)}\n                        </span>\n                      </div>\n                    </div>\n\n                    {/* المحتوى */}\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-start justify-between mb-1\">\n                        <h4 className={`\n                          text-sm leading-tight text-gray-800\n                          ${!notification.isRead ? 'font-semibold' : 'font-medium'}\n                        `}>\n                          {notification.title}\n                        </h4>\n                        <div className=\"flex items-center gap-1 ml-2 flex-shrink-0\">\n                          {!notification.isRead && (\n                            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                          )}\n                          <button\n                            onClick={(e) => {\n                              e.stopPropagation();\n                              removeNotification(notification.id);\n                            }}\n                            className=\"text-gray-400 hover:text-red-500 opacity-0 group-hover:opacity-100 transition-all duration-200 p-1 rounded hover:bg-red-50\"\n                            title=\"حذف الإشعار\"\n                          >\n                            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                            </svg>\n                          </button>\n                        </div>\n                      </div>\n\n                      <p className=\"text-xs text-gray-600 mb-2 leading-relaxed line-clamp-2\">\n                        {notification.message}\n                      </p>\n\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-xs text-gray-500 font-medium\">\n                          {getTimeAgo(notification.timestamp)}\n                        </span>\n\n                        {notification.actionText && (\n                          <span className=\"text-xs text-green-600 font-medium bg-green-50 px-2 py-1 rounded-full\">\n                            {notification.actionText} ←\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"p-8 text-center\">\n                <div className=\"text-4xl mb-4\">🔔</div>\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">لا توجد إشعارات</h3>\n                <p className=\"text-gray-600\">\n                  {filter === 'all' \n                    ? 'ستظهر الإشعارات الجديدة هنا' \n                    : `لا توجد إشعارات في فئة ${filter === 'system' ? 'النظام' : filter === 'social' ? 'الاجتماعي' : filter === 'commerce' ? 'التجاري' : 'الإجراءات'}`\n                  }\n                </p>\n              </div>\n            )}\n          </div>\n\n          {/* الذيل */}\n          {filteredNotifications.length > 0 && (\n            <div className=\"p-3 border-t border-gray-200/50 bg-gray-50/50\">\n              <button\n                onClick={() => {\n                  window.location.href = '/notifications';\n                  setIsOpen(false);\n                }}\n                className=\"w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium\"\n              >\n                عرض جميع الإشعارات\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;;AAKe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,EACJ,aAAa,EACb,cAAc,EACd,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,0BAA0B,EAC3B,GAAG,CAAA,GAAA,gJAAA,CAAA,2BAAwB,AAAD;IAE3B,MAAM,cAAc;IAEpB,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,wBAAwB,WAAW,QACrC,gBACA,2BAA2B;IAE/B,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,OAAO,KAAK,UAAU,OAAO;QAC9C,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO;QAChC,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO;QAE/B,IAAI,UAAU,GAAG,OAAO;QACxB,IAAI,UAAU,IAAI,OAAO,CAAC,IAAI,EAAE,QAAQ,MAAM,CAAC;QAC/C,IAAI,QAAQ,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,KAAK,CAAC;QAC1C,OAAO,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC;IAC1B;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAyB,KAAK;kDAAhB;;;;;;0BAYb,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAE1B,cAAW;gBACX,OAAO;oBACL,QAAQ,SACJ,iEACA;oBACJ,WAAW,cAAc,IAAI,mCAAmC;gBAClE;0DAPU;;kCASV,8OAAC;wBAEC,OAAO;4BACL,QAAQ,SACJ,iGACA;4BACJ,YAAY,SACR,oCACA;wBACN;kEARU;kCASX;;;;;;oBAKA,cAAc,mBACb,8OAAC;kEAAe;kCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;YAMjC,wBACC,8OAAC;0DAAc;;kCAEb,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;kDACb,8OAAC;kFAAa;kDAAsC;;;;;;kDACpD,8OAAC;kFAAc;;4CACZ,cAAc,mBACb,8OAAC;gDACC,SAAS;0FACC;0DACX;;;;;;0DAIH,8OAAC;gDACC,SAAS,IAAM,UAAU;0FACf;0DAEV,cAAA,8OAAC;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8FAApD;8DACb,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO7E,8OAAC;0EAAc;0CACZ;oCACC;wCAAE,KAAK;wCAAO,OAAO;wCAAQ,OAAO,cAAc,MAAM;oCAAC;oCACzD;wCAAE,KAAK;wCAAU,OAAO;wCAAU,OAAO,2BAA2B,UAAU,MAAM;oCAAC;oCACrF;wCAAE,KAAK;wCAAU,OAAO;wCAAW,OAAO,2BAA2B,UAAU,MAAM;oCAAC;oCACtF;wCAAE,KAAK;wCAAY,OAAO;wCAAS,OAAO,2BAA2B,YAAY,MAAM;oCAAC;oCACxF;wCAAE,KAAK;wCAAe,OAAO;wCAAW,OAAO,2BAA2B,eAAe,MAAM;oCAAC;iCACjG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,iBAC1B,8OAAC;wCAEC,SAAS,IAAM,UAAU;kFACd,CAAC;;oBAEV,EAAE,WAAW,MACT,qDACA,8CACH;kBACH,CAAC;;4CAEA;4CAAM;4CAAE,QAAQ,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;;uCAV7B;;;;;;;;;;;;;;;;kCAiBb,8OAAC;kEAAc;kCACZ,sBAAsB,MAAM,GAAG,IAC9B,sBAAsB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,6BACtC,8OAAC;gCAQC,SAAS;oCACP,IAAI,CAAC,aAAa,MAAM,EAAE;wCACxB,WAAW,aAAa,EAAE;oCAC5B;oCACA,IAAI,aAAa,SAAS,EAAE;wCAC1B,UAAU;wCACV,OAAO,QAAQ,CAAC,IAAI,GAAG,aAAa,SAAS;oCAC/C;gCACF;0EAdW,CAAC;;oBAEV,EAAE,iBAAiB,aAAa,QAAQ,EAAE;oBAC1C,EAAE,CAAC,aAAa,MAAM,GAAG,4BAA4B,cAAc;;kBAErE,CAAC;0CAWD,cAAA,8OAAC;8EAAc;;sDAEb,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAc;0DACb,cAAA,8OAAC;8FAAe;8DACb,aAAa,IAAI,IAAI,oBAAoB,aAAa,IAAI;;;;;;;;;;;;;;;;sDAMjE,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;;sEACb,8OAAC;sGAAc,CAAC;;0BAEd,EAAE,CAAC,aAAa,MAAM,GAAG,kBAAkB,cAAc;wBAC3D,CAAC;sEACE,aAAa,KAAK;;;;;;sEAErB,8OAAC;sGAAc;;gEACZ,CAAC,aAAa,MAAM,kBACnB,8OAAC;8GAAc;;;;;;8EAEjB,8OAAC;oEACC,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,mBAAmB,aAAa,EAAE;oEACpC;oEAEA,OAAM;8GADI;8EAGV,cAAA,8OAAC;wEAAwB,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kHAApD;kFACb,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAM7E,8OAAC;8FAAY;8DACV,aAAa,OAAO;;;;;;8DAGvB,8OAAC;8FAAc;;sEACb,8OAAC;sGAAe;sEACb,WAAW,aAAa,SAAS;;;;;;wDAGnC,aAAa,UAAU,kBACtB,8OAAC;sGAAe;;gEACb,aAAa,UAAU;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;+BAlE9B,aAAa,EAAE;;;;sDA2ExB,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;8CAAgB;;;;;;8CAC/B,8OAAC;8EAAa;8CAA2C;;;;;;8CACzD,8OAAC;8EAAY;8CACV,WAAW,QACR,gCACA,CAAC,uBAAuB,EAAE,WAAW,WAAW,WAAW,WAAW,WAAW,cAAc,WAAW,aAAa,YAAY,aAAa;;;;;;;;;;;;;;;;;oBAQ3J,sBAAsB,MAAM,GAAG,mBAC9B,8OAAC;kEAAc;kCACb,cAAA,8OAAC;4BACC,SAAS;gCACP,OAAO,QAAQ,CAAC,IAAI,GAAG;gCACvB,UAAU;4BACZ;sEACU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AASf"}}, {"offset": {"line": 2088, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2094, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/ClientOnlyNotifications.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport NotificationBell from './NotificationBell';\n\nconst ClientOnlyNotifications = () => {\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  if (!isClient) {\n    // عرض placeholder أثناء التحميل\n    return (\n      <div className=\"relative p-3 text-gray-600 rounded-full\">\n        <svg\n          className=\"w-6 h-6\"\n          fill=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 19V20H3V19L5 17V11C5 7.9 7 5.2 10 4.3V4C10 2.9 10.9 2 12 2S14 2.9 14 4V4.3C17 5.2 19 7.9 19 11V17L21 19ZM12 22C10.9 22 10 21.1 10 20H14C14 21.1 13.1 22 12 22Z\"/>\n        </svg>\n      </div>\n    );\n  }\n\n  return <NotificationBell />;\n};\n\nexport default ClientOnlyNotifications;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,0BAA0B;IAC9B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,IAAI,CAAC,UAAU;QACb,gCAAgC;QAChC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBACC,WAAU;gBACV,MAAK;gBACL,SAAQ;0BAER,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;IAIhB;IAEA,qBAAO,8OAAC,sIAAA,CAAA,UAAgB;;;;;AAC1B;uCAEe"}}, {"offset": {"line": 2142, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2148, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/lib/verification.ts"], "sourcesContent": ["// نظام علامات التوثيق والشارات للمستخدمين والإعلانات\n\nexport interface VerificationBadge {\n  id: string;\n  name: string;\n  nameEn: string;\n  description: string;\n  icon: string;\n  color: string;\n  bgColor: string;\n  borderColor: string;\n  textColor: string;\n  priority: number; // أولوية العرض (أعلى رقم = أولوية أكبر)\n  requirements: string[];\n  benefits: string[];\n}\n\n// علامات التوثيق للأفراد\nexport const INDIVIDUAL_BADGES: VerificationBadge[] = [\n  {\n    id: 'verified-basic',\n    name: 'موثق أساسي',\n    nameEn: 'Basic Verified',\n    description: 'مستخدم موثق مع الباقة الأساسية',\n    icon: '✓',\n    color: '#3B82F6', // أزرق\n    bgColor: 'bg-blue-100',\n    borderColor: 'border-blue-300',\n    textColor: 'text-blue-700',\n    priority: 1,\n    requirements: [\n      'اشتراك في الباقة الأساسية',\n      'تأكيد رقم الهاتف',\n      'تأكيد البريد الإلكتروني'\n    ],\n    benefits: [\n      'شارة التوثيق الأساسي',\n      'ثقة أكبر من المشترين',\n      'أولوية في البحث'\n    ]\n  },\n  {\n    id: 'verified-premium',\n    name: 'موثق مميز',\n    nameEn: 'Premium Verified',\n    description: 'مستخدم موثق مع الباقة المميزة',\n    icon: '✓',\n    color: '#9CA3AF', // فضي\n    bgColor: 'bg-gray-100',\n    borderColor: 'border-gray-400',\n    textColor: 'text-gray-700',\n    priority: 2,\n    requirements: [\n      'اشتراك في الباقة المميزة',\n      'تأكيد الهوية',\n      'تقييم إيجابي 4+ نجوم',\n      'عدم وجود شكاوى'\n    ],\n    benefits: [\n      'شارة التوثيق المميز',\n      'ظهور مميز في النتائج',\n      'ثقة عالية من المشترين',\n      'دعم فني أولوية'\n    ]\n  },\n  {\n    id: 'verified-gold',\n    name: 'موثق ذهبي',\n    nameEn: 'Gold Verified',\n    description: 'مستخدم موثق مع باقة الأعمال - الأكثر إعلاناً',\n    icon: '✓',\n    color: '#F59E0B', // ذهبي\n    bgColor: 'bg-yellow-100',\n    borderColor: 'border-yellow-400',\n    textColor: 'text-yellow-700',\n    priority: 3,\n    requirements: [\n      'اشتراك في باقة الأعمال',\n      'أكثر من 50 إعلان منشور',\n      'تقييم ممتاز 4.5+ نجوم',\n      'عضوية أكثر من 6 أشهر',\n      'تأكيد الهوية والعنوان'\n    ],\n    benefits: [\n      'شارة التوثيق الذهبي',\n      'أولوية قصوى في النتائج',\n      'ثقة استثنائية',\n      'دعم VIP',\n      'إحصائيات متقدمة'\n    ]\n  }\n];\n\n// علامات التوثيق للشركات\nexport const BUSINESS_BADGES: VerificationBadge[] = [\n  {\n    id: 'business-verified',\n    name: 'شركة موثقة',\n    nameEn: 'Verified Business',\n    description: 'شركة موثقة رسمياً - باقة البداية',\n    icon: '✓',\n    color: '#3B82F6', // أزرق\n    bgColor: 'bg-blue-100',\n    borderColor: 'border-blue-300',\n    textColor: 'text-blue-700',\n    priority: 4,\n    requirements: [\n      'اشتراك في باقة الشركات',\n      'تأكيد السجل التجاري',\n      'تأكيد عنوان الشركة',\n      'تأكيد رقم الضريبة'\n    ],\n    benefits: [\n      'شارة الشركة الموثقة',\n      'ثقة عالية من العملاء',\n      'ظهور في قسم الشركات',\n      'دعم فني للشركات'\n    ]\n  },\n  {\n    id: 'real-estate-office',\n    name: 'مكتب عقاري موثق',\n    nameEn: 'Verified Real Estate Office',\n    description: 'مكتب عقاري موثق ومرخص - باقة مخصصة',\n    icon: '✓',\n    color: '#9CA3AF', // فضي\n    bgColor: 'bg-gray-100',\n    borderColor: 'border-gray-400',\n    textColor: 'text-gray-700',\n    priority: 5,\n    requirements: [\n      'اشتراك في باقة المكاتب العقارية',\n      'ترخيص مكتب عقاري ساري',\n      'تأكيد عنوان المكتب',\n      'تأكيد الهوية المهنية',\n      'السجل التجاري ساري المفعول'\n    ],\n    benefits: [\n      'شارة المكتب العقاري الموثق',\n      'أولوية في البحث العقاري',\n      'ثقة عالية من العملاء',\n      'أدوات تقييم العقارات',\n      'تقارير السوق العقاري',\n      'نظام إدارة العملاء',\n      'دعم فني متخصص'\n    ]\n  },\n  {\n    id: 'business-premium',\n    name: 'شركة مميزة',\n    nameEn: 'Premium Business',\n    description: 'شركة مميزة مع الخطة المهنية',\n    icon: '✓',\n    color: '#F59E0B', // ذهبي\n    bgColor: 'bg-yellow-100',\n    borderColor: 'border-yellow-400',\n    textColor: 'text-yellow-700',\n    priority: 6,\n    requirements: [\n      'اشتراك في الخطة المهنية',\n      'أكثر من 100 إعلان',\n      'تقييم ممتاز من العملاء',\n      'عضوية أكثر من سنة'\n    ],\n    benefits: [\n      'شارة الشركة المميزة',\n      'أولوية عالية في النتائج',\n      'تقارير متقدمة',\n      'دعم مخصص'\n    ]\n  }\n];\n\n// علامات خاصة إضافية\nexport const SPECIAL_BADGES: VerificationBadge[] = [\n  {\n    id: 'top-seller',\n    name: 'أفضل بائع',\n    nameEn: 'Top Seller',\n    description: 'من أفضل البائعين على المنصة',\n    icon: '✓',\n    color: '#F59E0B', // ذهبي\n    bgColor: 'bg-yellow-50',\n    borderColor: 'border-yellow-200',\n    textColor: 'text-yellow-800',\n    priority: 7,\n    requirements: [\n      'أكثر من 100 عملية بيع ناجحة',\n      'تقييم 4.8+ نجوم',\n      'معدل استجابة 95%+',\n      'عدم وجود شكاوى جدية'\n    ],\n    benefits: [\n      'شارة أفضل بائع',\n      'ظهور في قائمة الأفضل',\n      'ثقة استثنائية',\n      'مكافآت خاصة'\n    ]\n  },\n  {\n    id: 'new-member',\n    name: 'عضو جديد',\n    nameEn: 'New Member',\n    description: 'عضو جديد على المنصة',\n    icon: '✓',\n    color: '#06B6D4', // سماوي\n    bgColor: 'bg-cyan-50',\n    borderColor: 'border-cyan-200',\n    textColor: 'text-cyan-700',\n    priority: 0,\n    requirements: [\n      'عضوية أقل من شهر',\n      'تأكيد البريد الإلكتروني'\n    ],\n    benefits: [\n      'ترحيب خاص',\n      'دعم للمبتدئين',\n      'نصائح مفيدة'\n    ]\n  }\n];\n\n// جميع الشارات\nexport const ALL_BADGES = [\n  ...INDIVIDUAL_BADGES,\n  ...BUSINESS_BADGES,\n  ...SPECIAL_BADGES\n];\n\n// دوال مساعدة\nexport const BadgeUtils = {\n  // الحصول على شارة بالمعرف\n  getBadgeById: (id: string): VerificationBadge | undefined => {\n    return ALL_BADGES.find(badge => badge.id === id);\n  },\n\n  // الحصول على أعلى شارة للمستخدم\n  getHighestBadge: (userBadges: string[]): VerificationBadge | undefined => {\n    const badges = userBadges\n      .map(id => BadgeUtils.getBadgeById(id))\n      .filter(Boolean) as VerificationBadge[];\n\n    return badges.sort((a, b) => b.priority - a.priority)[0];\n  },\n\n  // فلترة الشارات حسب النوع\n  getIndividualBadges: () => INDIVIDUAL_BADGES,\n  getBusinessBadges: () => BUSINESS_BADGES,\n  getSpecialBadges: () => SPECIAL_BADGES,\n\n  // تحديد نوع المستخدم من الشارة\n  getUserType: (badgeId: string): 'individual' | 'business' | 'special' => {\n    if (INDIVIDUAL_BADGES.find(b => b.id === badgeId)) return 'individual';\n    if (BUSINESS_BADGES.find(b => b.id === badgeId)) return 'business';\n    return 'special';\n  },\n\n  // تنسيق عرض الشارة\n  formatBadgeDisplay: (badge: VerificationBadge, size: 'sm' | 'md' | 'lg' = 'md') => {\n    const sizes = {\n      sm: 'text-xs px-2 py-1',\n      md: 'text-sm px-3 py-1',\n      lg: 'text-base px-4 py-2'\n    };\n\n    return {\n      className: `inline-flex items-center gap-1 rounded-full font-medium ${badge.bgColor} ${badge.borderColor} ${badge.textColor} border ${sizes[size]}`,\n      content: `${badge.icon} ${badge.name}`\n    };\n  }\n};\n\n// تحديد شارة المستخدم بناءً على اشتراكه\nexport const determineUserBadge = (\n  subscriptionType: string,\n  adsCount: number = 0,\n  rating: number = 0,\n  membershipMonths: number = 0,\n  isBusinessVerified: boolean = false\n): string[] => {\n  const badges: string[] = [];\n\n  // شارات الأفراد\n  if (subscriptionType === 'basic') {\n    badges.push('verified-basic');\n  } else if (subscriptionType === 'premium') {\n    badges.push('verified-premium');\n  } else if (subscriptionType === 'business' || subscriptionType === 'individual-business') {\n    badges.push('verified-gold');\n  }\n\n  // شارات الشركات\n  if (subscriptionType.startsWith('business-')) {\n    if (subscriptionType === 'business-starter') {\n      // خطة البداية - شارة زرقاء\n      if (isBusinessVerified) {\n        badges.push('business-verified');\n      }\n    } else if (subscriptionType === 'real-estate-office') {\n      // باقة المكاتب العقارية - شارة فضية\n      badges.push('real-estate-office');\n    } else if (subscriptionType === 'business-professional') {\n      // الخطة المهنية - شارة ذهبية\n      badges.push('business-premium');\n    }\n  }\n\n  // شارة المكاتب العقارية (يمكن أن تكون منفصلة)\n  if (subscriptionType === 'real-estate-office') {\n    badges.push('real-estate-office');\n  }\n\n  // شارات خاصة\n  if (membershipMonths < 1) {\n    badges.push('new-member');\n  }\n\n  if (adsCount >= 100 && rating >= 4.8) {\n    badges.push('top-seller');\n  }\n\n  return badges;\n};\n\nexport default {\n  INDIVIDUAL_BADGES,\n  BUSINESS_BADGES,\n  SPECIAL_BADGES,\n  ALL_BADGES,\n  BadgeUtils,\n  determineUserBadge\n};\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;;;;;AAkB9C,MAAM,oBAAyC;IACpD;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;CACD;AAGM,MAAM,kBAAuC;IAClD;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;CACD;AAGM,MAAM,iBAAsC;IACjD;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;SACD;IACH;CACD;AAGM,MAAM,aAAa;OACrB;OACA;OACA;CACJ;AAGM,MAAM,aAAa;IACxB,0BAA0B;IAC1B,cAAc,CAAC;QACb,OAAO,WAAW,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC/C;IAEA,gCAAgC;IAChC,iBAAiB,CAAC;QAChB,MAAM,SAAS,WACZ,GAAG,CAAC,CAAA,KAAM,WAAW,YAAY,CAAC,KAClC,MAAM,CAAC;QAEV,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE;IAC1D;IAEA,0BAA0B;IAC1B,qBAAqB,IAAM;IAC3B,mBAAmB,IAAM;IACzB,kBAAkB,IAAM;IAExB,+BAA+B;IAC/B,aAAa,CAAC;QACZ,IAAI,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU,OAAO;QAC1D,IAAI,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU,OAAO;QACxD,OAAO;IACT;IAEA,mBAAmB;IACnB,oBAAoB,CAAC,OAA0B,OAA2B,IAAI;QAC5E,MAAM,QAAQ;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QAEA,OAAO;YACL,WAAW,CAAC,wDAAwD,EAAE,MAAM,OAAO,CAAC,CAAC,EAAE,MAAM,WAAW,CAAC,CAAC,EAAE,MAAM,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;YACnJ,SAAS,GAAG,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE;QACxC;IACF;AACF;AAGO,MAAM,qBAAqB,CAChC,kBACA,WAAmB,CAAC,EACpB,SAAiB,CAAC,EAClB,mBAA2B,CAAC,EAC5B,qBAA8B,KAAK;IAEnC,MAAM,SAAmB,EAAE;IAE3B,gBAAgB;IAChB,IAAI,qBAAqB,SAAS;QAChC,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,qBAAqB,WAAW;QACzC,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,qBAAqB,cAAc,qBAAqB,uBAAuB;QACxF,OAAO,IAAI,CAAC;IACd;IAEA,gBAAgB;IAChB,IAAI,iBAAiB,UAAU,CAAC,cAAc;QAC5C,IAAI,qBAAqB,oBAAoB;YAC3C,2BAA2B;YAC3B,IAAI,oBAAoB;gBACtB,OAAO,IAAI,CAAC;YACd;QACF,OAAO,IAAI,qBAAqB,sBAAsB;YACpD,oCAAoC;YACpC,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,qBAAqB,yBAAyB;YACvD,6BAA6B;YAC7B,OAAO,IAAI,CAAC;QACd;IACF;IAEA,8CAA8C;IAC9C,IAAI,qBAAqB,sBAAsB;QAC7C,OAAO,IAAI,CAAC;IACd;IAEA,aAAa;IACb,IAAI,mBAAmB,GAAG;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,YAAY,OAAO,UAAU,KAAK;QACpC,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;AACT;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;AACF"}}, {"offset": {"line": 2441, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2447, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/VerificationBadge.tsx"], "sourcesContent": ["import React from 'react';\nimport { VerificationBadge as BadgeType, BadgeUtils } from '@/lib/verification';\n\ninterface VerificationBadgeProps {\n  badgeId?: string;\n  badge?: BadgeType;\n  size?: 'xs' | 'sm' | 'md' | 'lg';\n  showTooltip?: boolean;\n  className?: string;\n}\n\ninterface BadgeDisplayProps {\n  badges: string[];\n  size?: 'xs' | 'sm' | 'md' | 'lg';\n  maxDisplay?: number;\n  showTooltip?: boolean;\n  className?: string;\n}\n\n// مكون عرض شارة واحدة\nconst VerificationBadge: React.FC<VerificationBadgeProps> = ({\n  badgeId,\n  badge,\n  size = 'md',\n  showTooltip = true,\n  className = ''\n}) => {\n  const badgeData = badge || (badgeId ? BadgeUtils.getBadgeById(badgeId) : undefined);\n\n  if (!badgeData) return null;\n\n  const sizeClasses = {\n    xs: 'text-xs px-1.5 py-0.5 gap-1',\n    sm: 'text-xs px-2 py-1 gap-1',\n    md: 'text-sm px-3 py-1 gap-1.5',\n    lg: 'text-base px-4 py-2 gap-2'\n  };\n\n  const iconSizes = {\n    xs: 'w-3 h-3',\n    sm: 'w-4 h-4',\n    md: 'w-5 h-5',\n    lg: 'w-6 h-6'\n  };\n\n  return (\n    <div className=\"relative inline-block\">\n      <span\n        className={`\n          inline-flex items-center rounded-full font-medium border\n          ${badgeData.bgColor} ${badgeData.borderColor} ${badgeData.textColor}\n          ${sizeClasses[size]} ${className}\n          transition-all duration-200 hover:scale-105 cursor-default\n        `}\n        title={showTooltip ? badgeData.description : undefined}\n      >\n        <div\n          className={`\n            ${iconSizes[size]} rounded-full flex items-center justify-center\n            font-bold text-white shadow-sm\n          `}\n          style={{ backgroundColor: badgeData.color }}\n        >\n          ✓\n        </div>\n        <span className=\"font-semibold\">{badgeData.name}</span>\n      </span>\n\n      {showTooltip && (\n        <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-10\">\n          {badgeData.description}\n          <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900\"></div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// مكون عرض عدة شارات\nexport const BadgeDisplay: React.FC<BadgeDisplayProps> = ({\n  badges,\n  size = 'md',\n  maxDisplay = 2,\n  showTooltip = true,\n  className = ''\n}) => {\n  if (!badges || badges.length === 0) return null;\n\n  // ترتيب الشارات حسب الأولوية\n  const sortedBadges = badges\n    .map(id => BadgeUtils.getBadgeById(id))\n    .filter(Boolean)\n    .sort((a, b) => (b?.priority || 0) - (a?.priority || 0)) as BadgeType[];\n\n  const displayBadges = sortedBadges.slice(0, maxDisplay);\n  const remainingCount = sortedBadges.length - maxDisplay;\n\n  return (\n    <div className={`flex items-center gap-2 ${className}`}>\n      {displayBadges.map((badge) => (\n        <VerificationBadge\n          key={badge.id}\n          badge={badge}\n          size={size}\n          showTooltip={showTooltip}\n        />\n      ))}\n\n      {remainingCount > 0 && (\n        <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full\">\n          +{remainingCount}\n        </span>\n      )}\n    </div>\n  );\n};\n\n// مكون شارة مبسطة للإعلانات\nexport const AdBadge: React.FC<{\n  userBadges: string[];\n  size?: 'xs' | 'sm' | 'md';\n  className?: string;\n}> = ({ userBadges, size = 'sm', className = '' }) => {\n  const highestBadge = BadgeUtils.getHighestBadge(userBadges);\n\n  if (!highestBadge) return null;\n\n  return (\n    <VerificationBadge\n      badge={highestBadge}\n      size={size}\n      showTooltip={true}\n      className={className}\n    />\n  );\n};\n\n// مكون شارة للملف الشخصي\nexport const ProfileBadge: React.FC<{\n  userBadges: string[];\n  showAll?: boolean;\n  className?: string;\n}> = ({ userBadges, showAll = false, className = '' }) => {\n  if (!userBadges || userBadges.length === 0) return null;\n\n  if (showAll) {\n    return (\n      <BadgeDisplay\n        badges={userBadges}\n        size=\"md\"\n        maxDisplay={userBadges.length}\n        className={className}\n      />\n    );\n  }\n\n  const highestBadge = BadgeUtils.getHighestBadge(userBadges);\n\n  if (!highestBadge) return null;\n\n  return (\n    <div className={`flex items-center gap-3 ${className}`}>\n      <VerificationBadge\n        badge={highestBadge}\n        size=\"lg\"\n        showTooltip={true}\n      />\n      <div className=\"text-sm text-gray-600\">\n        <div className=\"font-medium\">{highestBadge.description}</div>\n        <div className=\"text-xs text-gray-500\">\n          {highestBadge.benefits.slice(0, 2).join(' • ')}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// مكون معلومات الشارة المفصلة\nexport const BadgeInfo: React.FC<{\n  badgeId: string;\n  className?: string;\n}> = ({ badgeId, className = '' }) => {\n  const badge = BadgeUtils.getBadgeById(badgeId);\n\n  if (!badge) return null;\n\n  return (\n    <div className={`bg-white rounded-lg border p-4 ${className}`}>\n      <div className=\"flex items-center gap-3 mb-3\">\n        <div className={`w-12 h-12 rounded-full ${badge.bgColor} ${badge.borderColor} border-2 flex items-center justify-center relative`}>\n          <div\n            className=\"w-8 h-8 rounded-full flex items-center justify-center font-bold text-white shadow-sm text-lg\"\n            style={{ backgroundColor: badge.color }}\n          >\n            ✓\n          </div>\n        </div>\n        <div>\n          <h3 className=\"font-semibold text-gray-800\">{badge.name}</h3>\n          <p className=\"text-sm text-gray-600\">{badge.description}</p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div>\n          <h4 className=\"font-medium text-gray-700 mb-2\">متطلبات الحصول:</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            {badge.requirements.map((req, index) => (\n              <li key={index} className=\"flex items-start gap-2\">\n                <span className=\"text-green-500 mt-0.5\">✓</span>\n                <span>{req}</span>\n              </li>\n            ))}\n          </ul>\n        </div>\n\n        <div>\n          <h4 className=\"font-medium text-gray-700 mb-2\">المميزات:</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            {badge.benefits.map((benefit, index) => (\n              <li key={index} className=\"flex items-start gap-2\">\n                <span className=\"text-blue-500 mt-0.5\">★</span>\n                <span>{benefit}</span>\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VerificationBadge;\n"], "names": [], "mappings": ";;;;;;;;AACA;;;AAkBA,sBAAsB;AACtB,MAAM,oBAAsD,CAAC,EAC3D,OAAO,EACP,KAAK,EACL,OAAO,IAAI,EACX,cAAc,IAAI,EAClB,YAAY,EAAE,EACf;IACC,MAAM,YAAY,SAAS,CAAC,UAAU,0HAAA,CAAA,aAAU,CAAC,YAAY,CAAC,WAAW,SAAS;IAElF,IAAI,CAAC,WAAW,OAAO;IAEvB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,YAAY;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAW,CAAC;;UAEV,EAAE,UAAU,OAAO,CAAC,CAAC,EAAE,UAAU,WAAW,CAAC,CAAC,EAAE,UAAU,SAAS,CAAC;UACpE,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU;;QAEnC,CAAC;gBACD,OAAO,cAAc,UAAU,WAAW,GAAG;;kCAE7C,8OAAC;wBACC,WAAW,CAAC;YACV,EAAE,SAAS,CAAC,KAAK,CAAC;;UAEpB,CAAC;wBACD,OAAO;4BAAE,iBAAiB,UAAU,KAAK;wBAAC;kCAC3C;;;;;;kCAGD,8OAAC;wBAAK,WAAU;kCAAiB,UAAU,IAAI;;;;;;;;;;;;YAGhD,6BACC,8OAAC;gBAAI,WAAU;;oBACZ,UAAU,WAAW;kCACtB,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAKzB;AAGO,MAAM,eAA4C,CAAC,EACxD,MAAM,EACN,OAAO,IAAI,EACX,aAAa,CAAC,EACd,cAAc,IAAI,EAClB,YAAY,EAAE,EACf;IACC,IAAI,CAAC,UAAU,OAAO,MAAM,KAAK,GAAG,OAAO;IAE3C,6BAA6B;IAC7B,MAAM,eAAe,OAClB,GAAG,CAAC,CAAA,KAAM,0HAAA,CAAA,aAAU,CAAC,YAAY,CAAC,KAClC,MAAM,CAAC,SACP,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC;IAExD,MAAM,gBAAgB,aAAa,KAAK,CAAC,GAAG;IAC5C,MAAM,iBAAiB,aAAa,MAAM,GAAG;IAE7C,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;YACnD,cAAc,GAAG,CAAC,CAAC,sBAClB,8OAAC;oBAEC,OAAO;oBACP,MAAM;oBACN,aAAa;mBAHR,MAAM,EAAE;;;;;YAOhB,iBAAiB,mBAChB,8OAAC;gBAAK,WAAU;;oBAA2D;oBACvE;;;;;;;;;;;;;AAKZ;AAGO,MAAM,UAIR,CAAC,EAAE,UAAU,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAE;IAC/C,MAAM,eAAe,0HAAA,CAAA,aAAU,CAAC,eAAe,CAAC;IAEhD,IAAI,CAAC,cAAc,OAAO;IAE1B,qBACE,8OAAC;QACC,OAAO;QACP,MAAM;QACN,aAAa;QACb,WAAW;;;;;;AAGjB;AAGO,MAAM,eAIR,CAAC,EAAE,UAAU,EAAE,UAAU,KAAK,EAAE,YAAY,EAAE,EAAE;IACnD,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG,OAAO;IAEnD,IAAI,SAAS;QACX,qBACE,8OAAC;YACC,QAAQ;YACR,MAAK;YACL,YAAY,WAAW,MAAM;YAC7B,WAAW;;;;;;IAGjB;IAEA,MAAM,eAAe,0HAAA,CAAA,aAAU,CAAC,eAAe,CAAC;IAEhD,IAAI,CAAC,cAAc,OAAO;IAE1B,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BACpD,8OAAC;gBACC,OAAO;gBACP,MAAK;gBACL,aAAa;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAe,aAAa,WAAW;;;;;;kCACtD,8OAAC;wBAAI,WAAU;kCACZ,aAAa,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;;;;;;;;;;;;;AAKlD;AAGO,MAAM,YAGR,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE;IAC/B,MAAM,QAAQ,0HAAA,CAAA,aAAU,CAAC,YAAY,CAAC;IAEtC,IAAI,CAAC,OAAO,OAAO;IAEnB,qBACE,8OAAC;QAAI,WAAW,CAAC,+BAA+B,EAAE,WAAW;;0BAC3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,CAAC,EAAE,MAAM,WAAW,CAAC,mDAAmD,CAAC;kCAC/H,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,iBAAiB,MAAM,KAAK;4BAAC;sCACvC;;;;;;;;;;;kCAIH,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA+B,MAAM,IAAI;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAyB,MAAM,WAAW;;;;;;;;;;;;;;;;;;0BAI3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAG,WAAU;0CACX,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC5B,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;0DAAM;;;;;;;uCAFA;;;;;;;;;;;;;;;;kCAQf,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAG,WAAU;0CACX,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,8OAAC;0DAAM;;;;;;;uCAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvB;uCAEe"}}, {"offset": {"line": 2816, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2822, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/AccountDropdown.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport VerificationBadge from './VerificationBadge';\nimport { determineUserBadge } from '@/lib/verification';\n\ninterface AccountDropdownProps {\n  className?: string;\n}\n\nconst AccountDropdown = ({ className = '' }: AccountDropdownProps) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const { user, isAuthenticated, logout } = useAuth();\n  const router = useRouter();\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleToggle = () => {\n    setIsOpen(!isOpen);\n  };\n\n  const handleLogout = () => {\n    logout();\n    setIsOpen(false);\n\n    // إعادة توجيه فورية للصفحة الرئيسية\n    setTimeout(() => {\n      router.push('/');\n    }, 100);\n  };\n\n  if (!isAuthenticated || !user) {\n    // عرض أيقونة تسجيل الدخول للمستخدمين غير المسجلين\n    return (\n      <button\n        onClick={() => router.push('/')}\n        className=\"relative p-3 text-gray-600 hover:text-primary-600 transition-all duration-300 group rounded-full focus:outline-none focus:ring-0\"\n        title=\"تسجيل الدخول\"\n      >\n        <svg\n          className=\"w-6 h-6 transition-all duration-300 group-hover:text-primary-600 group-hover:scale-110\"\n          fill=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path d=\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"/>\n        </svg>\n      </button>\n    );\n  }\n\n  const userBadge = determineUserBadge(user.userType, user.subscription?.planId);\n\n  const getMenuIcon = (iconType: string) => {\n    const iconClass = \"w-5 h-5 transition-all duration-300\";\n    const iconStyle = {\n      filter: 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))',\n      transition: 'all 0.3s ease'\n    };\n\n    const handleIconHover = (e: React.MouseEvent, isEnter: boolean) => {\n      if (isEnter) {\n        e.currentTarget.style.filter = 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))';\n        e.currentTarget.style.color = '#22c55e';\n      } else {\n        e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))';\n        e.currentTarget.style.color = '';\n      }\n    };\n\n    switch (iconType) {\n      case 'dashboard':\n        return (\n          <svg\n            className={iconClass}\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            style={iconStyle}\n            onMouseEnter={(e) => handleIconHover(e, true)}\n            onMouseLeave={(e) => handleIconHover(e, false)}\n          >\n            <path d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z\" />\n          </svg>\n        );\n      case 'ads':\n        return (\n          <svg\n            className={iconClass}\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            style={iconStyle}\n            onMouseEnter={(e) => handleIconHover(e, true)}\n            onMouseLeave={(e) => handleIconHover(e, false)}\n          >\n            <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'subscription':\n        return (\n          <svg\n            className={iconClass}\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            style={iconStyle}\n            onMouseEnter={(e) => handleIconHover(e, true)}\n            onMouseLeave={(e) => handleIconHover(e, false)}\n          >\n            <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n        );\n      case 'profile':\n        return (\n          <svg\n            className={iconClass}\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            style={iconStyle}\n            onMouseEnter={(e) => handleIconHover(e, true)}\n            onMouseLeave={(e) => handleIconHover(e, false)}\n          >\n            <path fillRule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'messages':\n        return (\n          <svg\n            className={iconClass}\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            style={iconStyle}\n            onMouseEnter={(e) => handleIconHover(e, true)}\n            onMouseLeave={(e) => handleIconHover(e, false)}\n          >\n            <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"/>\n            <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"/>\n          </svg>\n        );\n      case 'settings':\n        return (\n          <svg\n            className={iconClass}\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            style={iconStyle}\n            onMouseEnter={(e) => handleIconHover(e, true)}\n            onMouseLeave={(e) => handleIconHover(e, false)}\n          >\n            <path fillRule=\"evenodd\" d=\"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      default:\n        return <span className=\"text-lg\">📋</span>;\n    }\n  };\n\n  const menuItems = [\n    {\n      icon: 'dashboard',\n      label: 'لوحة التحكم',\n      href: '/dashboard',\n      description: 'نظرة عامة على حسابك'\n    },\n    {\n      icon: 'messages',\n      label: 'الرسائل',\n      href: '/messages',\n      description: 'إدارة رسائلك والردود'\n    },\n    {\n      icon: 'subscription',\n      label: 'الاشتراكات',\n      href: '/my-subscription',\n      description: 'إدارة باقاتك والدفع'\n    },\n    {\n      icon: 'profile',\n      label: 'الملف الشخصي',\n      href: '/profile',\n      description: 'تحديث معلوماتك الشخصية'\n    },\n    {\n      icon: 'settings',\n      label: 'الإعدادات',\n      href: '/settings',\n      description: 'إعدادات الحساب والخصوصية'\n    },\n  ];\n\n  return (\n    <div className={`relative ${className}`} ref={dropdownRef}>\n      {/* زر الحساب */}\n      <button\n        onClick={handleToggle}\n        className={`relative p-3 text-gray-600 hover:text-primary-600 transition-all duration-300 group rounded-full focus:outline-none focus:ring-0 ${\n          isOpen ? 'shadow-lg shadow-green-500/30 bg-green-50' : ''\n        }`}\n        title=\"حسابي\"\n      >\n        <svg\n          className=\"w-6 h-6 transition-all duration-300 group-hover:text-primary-600 group-hover:scale-110\"\n          fill=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path d=\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"/>\n        </svg>\n\n        {/* نقطة الحالة */}\n        <span className=\"absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></span>\n      </button>\n\n      {/* قائمة الحساب */}\n      {isOpen && (\n        <div className=\"absolute left-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden\">\n          {/* رأس القائمة - معلومات المستخدم */}\n          <div className=\"p-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n                <span className=\"text-xl\">\n                  {user.userType === 'individual' && '👤'}\n                  {user.userType === 'business' && '🏢'}\n                  {user.userType === 'real-estate-office' && '🏘️'}\n                </span>\n              </div>\n              <div className=\"flex-1\">\n                <div className=\"flex items-center gap-2\">\n                  <h3 className=\"font-semibold text-white\">{user.name}</h3>\n                  <VerificationBadge type={userBadge.type} size=\"xs\" />\n                </div>\n                <p className=\"text-sm opacity-90\">{user.email}</p>\n                <p className=\"text-xs opacity-75\">\n                  {user.userType === 'individual' && 'مستخدم فردي'}\n                  {user.userType === 'business' && 'حساب شركة'}\n                  {user.userType === 'real-estate-office' && 'مكتب عقاري'}\n                  {' • '}\n                  عضو منذ {new Date(user.createdAt).toLocaleDateString('en-GB', {\n                    year: 'numeric',\n                    month: '2-digit',\n                    day: '2-digit'\n                  }).split('/').reverse().join('/')}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* إحصائيات سريعة */}\n          <div className=\"p-4 bg-gray-50 border-b border-gray-200\">\n            <div className=\"grid grid-cols-3 gap-4 text-center\">\n              <div>\n                <div className=\"text-lg font-bold text-primary-600\">{user.stats.activeAds}</div>\n                <div className=\"text-xs text-gray-600\">إعلانات نشطة</div>\n              </div>\n              <div>\n                <div className=\"flex items-center justify-center gap-1\">\n                  <svg\n                    className=\"w-4 h-4 text-green-600 transition-all duration-300\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                    style={{\n                      filter: 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 6px rgba(34, 197, 94, 0.8))';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))';\n                    }}\n                  >\n                    <path d=\"M10 12a2 2 0 100-4 2 2 0 000 4z\"/>\n                    <path fillRule=\"evenodd\" d=\"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\" clipRule=\"evenodd\"/>\n                  </svg>\n                  <div className=\"text-lg font-bold text-green-600\">{user.stats.totalViews}</div>\n                </div>\n                <div className=\"text-xs text-gray-600\">مشاهدات</div>\n              </div>\n              <div>\n                <div className=\"flex items-center justify-center gap-1\">\n                  <svg\n                    className=\"w-4 h-4 text-orange-600 transition-all duration-300\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                    style={{\n                      filter: 'drop-shadow(0 0 0px rgba(249, 115, 22, 0.6))',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 6px rgba(249, 115, 22, 0.8))';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(249, 115, 22, 0.6))';\n                    }}\n                  >\n                    <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"/>\n                    <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"/>\n                  </svg>\n                  <div className=\"text-lg font-bold text-orange-600\">{user.stats.totalContacts}</div>\n                </div>\n                <div className=\"text-xs text-gray-600\">استفسارات</div>\n              </div>\n            </div>\n          </div>\n\n          {/* عناصر القائمة */}\n          <div className=\"py-2\">\n            {menuItems.map((item, index) => (\n              <Link\n                key={index}\n                href={item.href}\n                className=\"flex items-center gap-3 px-4 py-3 hover:bg-gray-50 transition-colors\"\n                onClick={() => setIsOpen(false)}\n              >\n                <span className=\"text-primary-600\">{getMenuIcon(item.icon)}</span>\n                <div className=\"flex-1\">\n                  <div className=\"font-medium text-gray-800\">{item.label}</div>\n                  <div className=\"text-xs text-gray-500\">{item.description}</div>\n                </div>\n                <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                </svg>\n              </Link>\n            ))}\n          </div>\n\n          {/* معلومات الاشتراك */}\n          {user.subscription && (\n            <div className=\"p-4 bg-primary-50 border-t border-gray-200\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"text-sm font-medium text-primary-800\">{user.subscription.planName}</span>\n                  <VerificationBadge type={userBadge.type} size=\"xs\" />\n                </div>\n                <span className={`text-xs px-2 py-1 rounded-full ${\n                  user.subscription.isActive\n                    ? 'bg-green-100 text-green-800'\n                    : 'bg-red-100 text-red-800'\n                }`}>\n                  {user.subscription.isActive ? 'نشط' : 'غير نشط'}\n                </span>\n              </div>\n              <div className=\"text-xs text-primary-600\">\n                ينتهي في {new Date(user.subscription.endDate).toLocaleDateString('en-GB', {\n                  year: 'numeric',\n                  month: '2-digit',\n                  day: '2-digit'\n                }).split('/').reverse().join('/')}\n              </div>\n            </div>\n          )}\n\n          {/* أزرار التحكم */}\n          <div className=\"p-4 border-t border-gray-200 bg-gray-50\">\n            <div className=\"flex gap-2\">\n              <button\n                onClick={() => {\n                  router.push('/add-ad');\n                  setIsOpen(false);\n                }}\n                className=\"flex-1 px-3 py-2 text-sm bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n              >\n                إضافة إعلان\n              </button>\n              <button\n                onClick={handleLogout}\n                className=\"flex-1 px-3 py-2 text-sm bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors\"\n              >\n                تسجيل الخروج\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AccountDropdown;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAaA,MAAM,kBAAkB,CAAC,EAAE,YAAY,EAAE,EAAwB;IAC/D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,UAAU,CAAC;IACb;IAEA,MAAM,eAAe;QACnB;QACA,UAAU;QAEV,oCAAoC;QACpC,WAAW;YACT,OAAO,IAAI,CAAC;QACd,GAAG;IACL;IAEA,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAC7B,kDAAkD;QAClD,qBACE,8OAAC;YACC,SAAS,IAAM,OAAO,IAAI,CAAC;YAC3B,WAAU;YACV,OAAM;sBAEN,cAAA,8OAAC;gBACC,WAAU;gBACV,MAAK;gBACL,SAAQ;0BAER,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;IAIhB;IAEA,MAAM,YAAY,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,QAAQ,EAAE,KAAK,YAAY,EAAE;IAEvE,MAAM,cAAc,CAAC;QACnB,MAAM,YAAY;QAClB,MAAM,YAAY;YAChB,QAAQ;YACR,YAAY;QACd;QAEA,MAAM,kBAAkB,CAAC,GAAqB;YAC5C,IAAI,SAAS;gBACX,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;YAChC,OAAO;gBACL,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;YAChC;QACF;QAEA,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAW;oBACX,MAAK;oBACL,SAAQ;oBACR,OAAO;oBACP,cAAc,CAAC,IAAM,gBAAgB,GAAG;oBACxC,cAAc,CAAC,IAAM,gBAAgB,GAAG;8BAExC,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAW;oBACX,MAAK;oBACL,SAAQ;oBACR,OAAO;oBACP,cAAc,CAAC,IAAM,gBAAgB,GAAG;oBACxC,cAAc,CAAC,IAAM,gBAAgB,GAAG;8BAExC,cAAA,8OAAC;wBAAK,UAAS;wBAAU,GAAE;wBAAoM,UAAS;;;;;;;;;;;YAG9O,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAW;oBACX,MAAK;oBACL,SAAQ;oBACR,OAAO;oBACP,cAAc,CAAC,IAAM,gBAAgB,GAAG;oBACxC,cAAc,CAAC,IAAM,gBAAgB,GAAG;8BAExC,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAW;oBACX,MAAK;oBACL,SAAQ;oBACR,OAAO;oBACP,cAAc,CAAC,IAAM,gBAAgB,GAAG;oBACxC,cAAc,CAAC,IAAM,gBAAgB,GAAG;8BAExC,cAAA,8OAAC;wBAAK,UAAS;wBAAU,GAAE;wBAAsD,UAAS;;;;;;;;;;;YAGhG,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAW;oBACX,MAAK;oBACL,SAAQ;oBACR,OAAO;oBACP,cAAc,CAAC,IAAM,gBAAgB,GAAG;oBACxC,cAAc,CAAC,IAAM,gBAAgB,GAAG;;sCAExC,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAK,GAAE;;;;;;;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAW;oBACX,MAAK;oBACL,SAAQ;oBACR,OAAO;oBACP,cAAc,CAAC,IAAM,gBAAgB,GAAG;oBACxC,cAAc,CAAC,IAAM,gBAAgB,GAAG;8BAExC,cAAA,8OAAC;wBAAK,UAAS;wBAAU,GAAE;wBAA+f,UAAS;;;;;;;;;;;YAGziB;gBACE,qBAAO,8OAAC;oBAAK,WAAU;8BAAU;;;;;;QACrC;IACF;IAEA,MAAM,YAAY;QAChB;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;QAAE,KAAK;;0BAE5C,8OAAC;gBACC,SAAS;gBACT,WAAW,CAAC,iIAAiI,EAC3I,SAAS,8CAA8C,IACvD;gBACF,OAAM;;kCAEN,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,SAAQ;kCAER,cAAA,8OAAC;4BAAK,GAAE;;;;;;;;;;;kCAIV,8OAAC;wBAAK,WAAU;;;;;;;;;;;;YAIjB,wBACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;;4CACb,KAAK,QAAQ,KAAK,gBAAgB;4CAClC,KAAK,QAAQ,KAAK,cAAc;4CAChC,KAAK,QAAQ,KAAK,wBAAwB;;;;;;;;;;;;8CAG/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4B,KAAK,IAAI;;;;;;8DACnD,8OAAC,uIAAA,CAAA,UAAiB;oDAAC,MAAM,UAAU,IAAI;oDAAE,MAAK;;;;;;;;;;;;sDAEhD,8OAAC;4CAAE,WAAU;sDAAsB,KAAK,KAAK;;;;;;sDAC7C,8OAAC;4CAAE,WAAU;;gDACV,KAAK,QAAQ,KAAK,gBAAgB;gDAClC,KAAK,QAAQ,KAAK,cAAc;gDAChC,KAAK,QAAQ,KAAK,wBAAwB;gDAC1C;gDAAM;gDACE,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC,SAAS;oDAC5D,MAAM;oDACN,OAAO;oDACP,KAAK;gDACP,GAAG,KAAK,CAAC,KAAK,OAAO,GAAG,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAOrC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAsC,KAAK,KAAK,CAAC,SAAS;;;;;;sDACzE,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,OAAO;wDACL,QAAQ;wDACR,YAAY;oDACd;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDACjC;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDACjC;;sEAEA,8OAAC;4DAAK,GAAE;;;;;;sEACR,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAA0I,UAAS;;;;;;;;;;;;8DAEhL,8OAAC;oDAAI,WAAU;8DAAoC,KAAK,KAAK,CAAC,UAAU;;;;;;;;;;;;sDAE1E,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,OAAO;wDACL,QAAQ;wDACR,YAAY;oDACd;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDACjC;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDACjC;;sEAEA,8OAAC;4DAAK,GAAE;;;;;;sEACR,8OAAC;4DAAK,GAAE;;;;;;;;;;;;8DAEV,8OAAC;oDAAI,WAAU;8DAAqC,KAAK,KAAK,CAAC,aAAa;;;;;;;;;;;;sDAE9E,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,8OAAC;wCAAK,WAAU;kDAAoB,YAAY,KAAK,IAAI;;;;;;kDACzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6B,KAAK,KAAK;;;;;;0DACtD,8OAAC;gDAAI,WAAU;0DAAyB,KAAK,WAAW;;;;;;;;;;;;kDAE1D,8OAAC;wCAAI,WAAU;wCAAwB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC/E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;+BAXlE;;;;;;;;;;oBAkBV,KAAK,YAAY,kBAChB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwC,KAAK,YAAY,CAAC,QAAQ;;;;;;0DAClF,8OAAC,uIAAA,CAAA,UAAiB;gDAAC,MAAM,UAAU,IAAI;gDAAE,MAAK;;;;;;;;;;;;kDAEhD,8OAAC;wCAAK,WAAW,CAAC,+BAA+B,EAC/C,KAAK,YAAY,CAAC,QAAQ,GACtB,gCACA,2BACJ;kDACC,KAAK,YAAY,CAAC,QAAQ,GAAG,QAAQ;;;;;;;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;;oCAA2B;oCAC9B,IAAI,KAAK,KAAK,YAAY,CAAC,OAAO,EAAE,kBAAkB,CAAC,SAAS;wCACxE,MAAM;wCACN,OAAO;wCACP,KAAK;oCACP,GAAG,KAAK,CAAC,KAAK,OAAO,GAAG,IAAI,CAAC;;;;;;;;;;;;;kCAMnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;wCACP,OAAO,IAAI,CAAC;wCACZ,UAAU;oCACZ;oCACA,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe"}}, {"offset": {"line": 3592, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3598, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/ClientOnlyAccount.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport AccountDropdown from './AccountDropdown';\n\nconst ClientOnlyAccount = () => {\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  if (!isClient) {\n    // عرض placeholder أثناء التحميل\n    return (\n      <div className=\"relative p-3 text-gray-600 rounded-full\">\n        <svg\n          className=\"w-6 h-6\"\n          fill=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path d=\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"/>\n        </svg>\n      </div>\n    );\n  }\n\n  return <AccountDropdown />;\n};\n\nexport default ClientOnlyAccount;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,oBAAoB;IACxB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,IAAI,CAAC,UAAU;QACb,gCAAgC;QAChC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBACC,WAAU;gBACV,MAAK;gBACL,SAAQ;0BAER,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;IAIhB;IAEA,qBAAO,8OAAC,qIAAA,CAAA,UAAe;;;;;AACzB;uCAEe"}}, {"offset": {"line": 3646, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3652, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport AuthModal from './AuthModal';\nimport MyCvLogo from './MyCvLogo';\nimport Logo from './Logo';\nimport SafeNavigationButton from './SafeNavigationButton';\nimport ClientOnlyNotifications from './ClientOnlyNotifications';\nimport ClientOnlyAccount from './ClientOnlyAccount';\nimport { useToast } from '@/components/ToastManager';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [currentUser, setCurrentUser] = useState<any>(null);\n  const toast = useToast();\n\n  // التحقق من حالة تسجيل الدخول عند تحميل المكون\n  useEffect(() => {\n    const checkLoginStatus = () => {\n      const loginStatus = localStorage.getItem('isLoggedIn');\n      const userData = localStorage.getItem('currentUser');\n\n      if (loginStatus === 'true' && userData) {\n        setIsLoggedIn(true);\n        setCurrentUser(JSON.parse(userData));\n      }\n    };\n\n    checkLoginStatus();\n  }, []);\n\n  // دالة تسجيل الخروج المحسنة\n  const handleLogout = () => {\n    // إظهار رسالة تسجيل الخروج\n    toast.showLogout();\n\n    // إزالة جميع بيانات المستخدم\n    localStorage.removeItem('isLoggedIn');\n    localStorage.removeItem('currentUser');\n    localStorage.removeItem('userSession');\n    localStorage.removeItem('userSettings');\n    localStorage.removeItem('userPreferences');\n\n    // تحديث الحالة المحلية\n    setIsLoggedIn(false);\n    setCurrentUser(null);\n\n    // التحقق من الصفحة الحالية وإعادة التوجيه\n    const currentPath = window.location.pathname;\n    const protectedPaths = ['/settings', '/profile', '/dashboard', '/add-ad', '/my-ads', '/favorites', '/messages'];\n    const isOnProtectedPage = protectedPaths.some(path => currentPath.startsWith(path));\n\n    // إعادة توجيه بعد تأخير قصير\n    setTimeout(() => {\n      if (isOnProtectedPage) {\n        // إعادة توجيه للصفحة الرئيسية إذا كان في صفحة محمية\n        window.location.href = '/';\n      } else {\n        // إعادة تحميل الصفحة الحالية إذا لم تكن محمية\n        window.location.reload();\n      }\n    }, 1500);\n  };\n  const [authModalTab, setAuthModalTab] = useState<'login' | 'register'>('login');\n\n  return (\n    <header className=\"bg-white shadow-lg border-b border-primary-100\">\n\n\n      {/* Main Header */}\n      <div className=\"container mx-auto px-4 py-2\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo with Syria Badge - Mobile Optimized */}\n          <div className=\"flex items-center gap-2\">\n            <Logo variant=\"transparent\" size={isMenuOpen ? \"lg\" : \"xl\"} showText={true} href=\"/\" />\n            <div className=\"bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold\">\n              سوريا\n            </div>\n          </div>\n\n          {/* Desktop Auth Buttons */}\n          <div className=\"hidden md:flex items-center gap-0.5\">\n            {!isLoggedIn ? (\n              // للمستخدمين غير المسجلين\n              <>\n                {/* الوظائف */}\n                <Link\n                  href=\"/jobs\"\n                  className=\"flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors font-medium\"\n                  title=\"الوظائف والسير الذاتية - مدعوم من قبل تطبيق MyCv\"\n                >\n                  <MyCvLogo size=\"xs\" variant=\"square\" />\n                  <span>الوظائف</span>\n                </Link>\n\n                {/* أضف إعلانك - ينقل لتسجيل الدخول */}\n                <button\n                  onClick={() => {\n                    setAuthModalTab('login');\n                    setIsAuthModalOpen(true);\n                  }}\n                  className=\"px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-semibold\"\n                >\n                  أضف إعلانك\n                </button>\n\n                {/* تسجيل الدخول */}\n                <button\n                  onClick={() => {\n                    setAuthModalTab('login');\n                    setIsAuthModalOpen(true);\n                  }}\n                  className=\"px-4 py-2 text-primary-600 border border-primary-600 rounded-lg hover:bg-primary-50 transition-colors\"\n                >\n                  تسجيل الدخول\n                </button>\n\n                {/* إنشاء حساب */}\n                <button\n                  onClick={() => {\n                    setAuthModalTab('register');\n                    setIsAuthModalOpen(true);\n                  }}\n                  className=\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n                >\n                  إنشاء حساب\n                </button>\n              </>\n            ) : (\n              // للمستخدمين المسجلين\n              <>\n                {/* الوظائف */}\n                <Link\n                  href=\"/jobs\"\n                  className=\"flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors font-medium\"\n                  title=\"الوظائف والسير الذاتية - مدعوم من قبل تطبيق MyCv\"\n                >\n                  <MyCvLogo size=\"xs\" variant=\"square\" />\n                  <span>الوظائف</span>\n                </Link>\n\n                {/* أضف إعلانك */}\n                <SafeNavigationButton\n                  href=\"/add-ad\"\n                  className=\"px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-semibold\"\n                >\n                  أضف إعلانك\n                </SafeNavigationButton>\n\n                {/* المفضلة */}\n                <Link\n                  href=\"/favorites\"\n                  className=\"relative p-3 text-gray-600 hover:text-green-600 rounded-full hover:bg-gray-100 transition-all duration-300 group\"\n                  title=\"المفضلة\"\n                >\n                  <svg\n                    className=\"w-6 h-6 transition-all duration-300 group-hover:scale-110\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                    style={{\n                      filter: 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))';\n                      e.currentTarget.style.color = '#22c55e';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))';\n                      e.currentTarget.style.color = '';\n                    }}\n                  >\n                    <path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"/>\n                  </svg>\n                </Link>\n\n                {/* الرسائل */}\n                <Link\n                  href=\"/messages\"\n                  className=\"relative p-3 text-gray-600 hover:text-blue-600 rounded-full hover:bg-gray-100 transition-all duration-300 group\"\n                  title=\"الرسائل\"\n                >\n                  <svg\n                    className=\"w-6 h-6 transition-all duration-300 group-hover:scale-110\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                    style={{\n                      filter: 'drop-shadow(0 0 0px rgba(59, 130, 246, 0.6))',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.8))';\n                      e.currentTarget.style.color = '#3b82f6';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(59, 130, 246, 0.6))';\n                      e.currentTarget.style.color = '';\n                    }}\n                  >\n                    <path d=\"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z\"/>\n                  </svg>\n\n                  {/* عداد الرسائل غير المقروءة */}\n                  <span className=\"absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold\">\n                    2\n                  </span>\n                </Link>\n\n                {/* الإشعارات */}\n                <ClientOnlyNotifications />\n\n                {/* حسابي */}\n                <ClientOnlyAccount />\n\n                {/* تسجيل الخروج */}\n                <button\n                  onClick={handleLogout}\n                  className=\"text-red-600 hover:text-red-700 px-4 py-2 rounded-lg hover:bg-red-50 transition-colors\"\n                  title=\"تسجيل الخروج\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M16 17v-3H9v-4h7V7l5 5-5 5M14 2a2 2 0 012 2v2h-2V4H4v16h10v-2h2v2a2 2 0 01-2 2H4a2 2 0 01-2-2V4a2 2 0 012-2h10z\"/>\n                  </svg>\n                </button>\n              </>\n            )}\n          </div>\n\n          {/* Mobile Right Side */}\n          <div className=\"md:hidden flex items-center gap-2\">\n            {/* المفضلة للموبايل */}\n            <Link\n              href=\"/favorites\"\n              className=\"relative p-2 text-gray-600 hover:text-green-600 rounded-full hover:bg-gray-100 transition-all duration-300\"\n              title=\"المفضلة\"\n            >\n              <svg\n                className=\"w-5 h-5\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"/>\n              </svg>\n            </Link>\n\n            {/* الرسائل للموبايل */}\n            <Link\n              href=\"/messages\"\n              className=\"relative p-2 text-gray-600 hover:text-blue-600 rounded-full hover:bg-gray-100 transition-all duration-300\"\n              title=\"الرسائل\"\n            >\n              <svg\n                className=\"w-5 h-5\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path d=\"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z\"/>\n              </svg>\n\n              {/* عداد الرسائل غير المقروءة للموبايل */}\n              <span className=\"absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-bold text-[10px]\">\n                2\n              </span>\n            </Link>\n\n            {/* الإشعارات للموبايل */}\n            <ClientOnlyNotifications />\n\n            {/* حسابي للموبايل */}\n            <ClientOnlyAccount />\n\n            {/* زر إضافة إعلان للموبايل */}\n            <SafeNavigationButton\n              href=\"/add-ad\"\n              className=\"px-3 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-medium text-sm\"\n            >\n              + إعلان\n            </SafeNavigationButton>\n\n            {/* Mobile Menu Button */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\"\n            >\n              <span className=\"text-xl\">{isMenuOpen ? '✕' : '☰'}</span>\n            </button>\n          </div>\n        </div>\n\n        {/* Desktop Navigation */}\n        <nav className=\"hidden md:block mt-4 border-t pt-4\">\n          <ul className=\"flex flex-wrap gap-6 text-gray-700\">\n            <li><SafeNavigationButton href=\"/\" className=\"hover:text-primary-600 font-medium bg-transparent border-0 p-0\">الرئيسية</SafeNavigationButton></li>\n            <li><SafeNavigationButton href=\"/ads\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">جميع الإعلانات</SafeNavigationButton></li>\n            <li><SafeNavigationButton href=\"/jobs\" className=\"hover:text-primary-600 font-medium flex items-center gap-1 bg-transparent border-0 p-0\">\n              <MyCvLogo size=\"xs\" variant=\"square\" />\n              <span>الوظائف</span>\n            </SafeNavigationButton></li>\n            {isLoggedIn && (\n              <>\n                <li><SafeNavigationButton href=\"/notifications\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">الإشعارات</SafeNavigationButton></li>\n                <li><SafeNavigationButton href=\"/map\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">الخريطة</SafeNavigationButton></li>\n                <li><SafeNavigationButton href=\"/compare\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">المقارنة</SafeNavigationButton></li>\n              </>\n            )}\n            <li><SafeNavigationButton href=\"/category/real-estate\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">العقارات</SafeNavigationButton></li>\n            <li><SafeNavigationButton href=\"/category/cars\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">السيارات</SafeNavigationButton></li>\n            <li><SafeNavigationButton href=\"/category/electronics\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">الإلكترونيات</SafeNavigationButton></li>\n            <li><SafeNavigationButton href=\"/category/services\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">الخدمات</SafeNavigationButton></li>\n            <li><SafeNavigationButton href=\"/subscription\" className=\"hover:text-primary-600 font-medium bg-transparent border-0 p-0\">💎 الاشتراكات</SafeNavigationButton></li>\n          </ul>\n        </nav>\n      </div>\n\n      {/* Mobile Menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden bg-white border-t shadow-lg\">\n          <div className=\"container mx-auto px-4 py-4 max-h-screen overflow-y-auto\">\n            {/* أزرار المصادقة للموبايل - تظهر فقط للمستخدمين غير المسجلين */}\n            {!isLoggedIn && (\n              <div className=\"flex flex-col gap-3 mb-6\">\n                <button\n                  onClick={() => {\n                    setAuthModalTab('login');\n                    setIsAuthModalOpen(true);\n                    setIsMenuOpen(false);\n                  }}\n                  className=\"w-full px-4 py-3 text-primary-600 border border-primary-600 rounded-lg hover:bg-primary-50 transition-colors font-medium\"\n                >\n                  تسجيل الدخول\n                </button>\n                <button\n                  onClick={() => {\n                    setAuthModalTab('register');\n                    setIsAuthModalOpen(true);\n                    setIsMenuOpen(false);\n                  }}\n                  className=\"w-full px-4 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium\"\n                >\n                  إنشاء حساب جديد\n                </button>\n              </div>\n            )}\n\n            {/* روابط التنقل الرئيسية */}\n            <div className=\"mb-6\">\n              <h3 className=\"font-semibold text-gray-800 mb-4 text-lg\">التنقل السريع</h3>\n              <div className=\"grid grid-cols-2 gap-3\">\n                <SafeNavigationButton\n                  href=\"/\"\n                  className=\"p-4 text-center bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors border border-gray-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">🏠</div>\n                  <div className=\"text-sm font-medium\">الرئيسية</div>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/ads\"\n                  className=\"p-4 text-center bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors border border-blue-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">📋</div>\n                  <div className=\"text-sm font-medium text-blue-700\">جميع الإعلانات</div>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/jobs\"\n                  className=\"p-4 text-center bg-green-50 rounded-xl hover:bg-green-100 transition-colors border border-green-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"flex justify-center mb-2\">\n                    <MyCvLogo size=\"sm\" variant=\"square\" />\n                  </div>\n                  <div className=\"text-sm font-medium text-green-700\">الوظائف</div>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/map\"\n                  className=\"p-4 text-center bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors border border-gray-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">🗺️</div>\n                  <div className=\"text-sm font-medium\">الخريطة</div>\n                </SafeNavigationButton>\n              </div>\n            </div>\n\n            {/* التصنيفات الرئيسية */}\n            <div className=\"mb-6\">\n              <h3 className=\"font-semibold text-gray-800 mb-4 text-lg\">التصنيفات الرئيسية</h3>\n              <div className=\"grid grid-cols-2 gap-3\">\n                <SafeNavigationButton\n                  href=\"/category/real-estate\"\n                  className=\"p-4 text-center bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors border border-blue-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">🏘️</div>\n                  <div className=\"text-sm font-medium text-blue-700\">العقارات</div>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/category/cars\"\n                  className=\"p-4 text-center bg-red-50 rounded-xl hover:bg-red-100 transition-colors border border-red-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">🚗</div>\n                  <div className=\"text-sm font-medium text-red-700\">السيارات</div>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/category/electronics\"\n                  className=\"p-4 text-center bg-purple-50 rounded-xl hover:bg-purple-100 transition-colors border border-purple-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">📱</div>\n                  <div className=\"text-sm font-medium text-purple-700\">الإلكترونيات</div>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/category/services\"\n                  className=\"p-4 text-center bg-orange-50 rounded-xl hover:bg-orange-100 transition-colors border border-orange-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">🛠️</div>\n                  <div className=\"text-sm font-medium text-orange-700\">الخدمات</div>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/subscription\"\n                  className=\"p-4 text-center bg-yellow-50 rounded-xl hover:bg-yellow-100 transition-colors border border-yellow-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">💎</div>\n                  <div className=\"text-sm font-medium text-yellow-700\">الاشتراكات</div>\n                </SafeNavigationButton>\n              </div>\n            </div>\n\n            {/* روابط إضافية */}\n            <div className=\"border-t border-gray-200 pt-4\">\n              <div className=\"flex flex-col gap-2\">\n                <SafeNavigationButton\n                  href=\"/notifications\"\n                  className=\"flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <span className=\"text-xl\">🔔</span>\n                  <span className=\"font-medium\">الإشعارات</span>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/compare\"\n                  className=\"flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <span className=\"text-xl\">⚖️</span>\n                  <span className=\"font-medium\">المقارنة</span>\n                </SafeNavigationButton>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Auth Modal */}\n      <AuthModal\n        isOpen={isAuthModalOpen}\n        onClose={() => setIsAuthModalOpen(false)}\n        defaultTab={authModalTab}\n      />\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAYA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,QAAQ,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IAErB,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,MAAM,WAAW,aAAa,OAAO,CAAC;YAEtC,IAAI,gBAAgB,UAAU,UAAU;gBACtC,cAAc;gBACd,eAAe,KAAK,KAAK,CAAC;YAC5B;QACF;QAEA;IACF,GAAG,EAAE;IAEL,4BAA4B;IAC5B,MAAM,eAAe;QACnB,2BAA2B;QAC3B,MAAM,UAAU;QAEhB,6BAA6B;QAC7B,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,uBAAuB;QACvB,cAAc;QACd,eAAe;QAEf,0CAA0C;QAC1C,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;QAC5C,MAAM,iBAAiB;YAAC;YAAa;YAAY;YAAc;YAAW;YAAW;YAAc;SAAY;QAC/G,MAAM,oBAAoB,eAAe,IAAI,CAAC,CAAA,OAAQ,YAAY,UAAU,CAAC;QAE7E,6BAA6B;QAC7B,WAAW;YACT,IAAI,mBAAmB;gBACrB,oDAAoD;gBACpD,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO;gBACL,8CAA8C;gBAC9C,OAAO,QAAQ,CAAC,MAAM;YACxB;QACF,GAAG;IACL;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAEvE,qBACE,8OAAC;QAAO,WAAU;;0BAIhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,UAAI;wCAAC,SAAQ;wCAAc,MAAM,aAAa,OAAO;wCAAM,UAAU;wCAAM,MAAK;;;;;;kDACjF,8OAAC;wCAAI,WAAU;kDAA+D;;;;;;;;;;;;0CAMhF,8OAAC;gCAAI,WAAU;0CACZ,CAAC,aACA,0BAA0B;8CAC1B;;sDAEE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,OAAM;;8DAEN,8OAAC,8HAAA,CAAA,UAAQ;oDAAC,MAAK;oDAAK,SAAQ;;;;;;8DAC5B,8OAAC;8DAAK;;;;;;;;;;;;sDAIR,8OAAC;4CACC,SAAS;gDACP,gBAAgB;gDAChB,mBAAmB;4CACrB;4CACA,WAAU;sDACX;;;;;;sDAKD,8OAAC;4CACC,SAAS;gDACP,gBAAgB;gDAChB,mBAAmB;4CACrB;4CACA,WAAU;sDACX;;;;;;sDAKD,8OAAC;4CACC,SAAS;gDACP,gBAAgB;gDAChB,mBAAmB;4CACrB;4CACA,WAAU;sDACX;;;;;;;mDAKH,sBAAsB;8CACtB;;sDAEE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,OAAM;;8DAEN,8OAAC,8HAAA,CAAA,UAAQ;oDAAC,MAAK;oDAAK,SAAQ;;;;;;8DAC5B,8OAAC;8DAAK;;;;;;;;;;;;sDAIR,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;sDACX;;;;;;sDAKD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC;gDACC,WAAU;gDACV,MAAK;gDACL,SAAQ;gDACR,OAAO;oDACL,QAAQ;oDACR,YAAY;gDACd;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gDAChC;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gDAChC;0DAEA,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAKZ,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,OAAM;;8DAEN,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,OAAO;wDACL,QAAQ;wDACR,YAAY;oDACd;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;wDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;oDAChC;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;wDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;oDAChC;8DAEA,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;8DAIV,8OAAC;oDAAK,WAAU;8DAA0H;;;;;;;;;;;;sDAM5I,8OAAC,6IAAA,CAAA,UAAuB;;;;;sDAGxB,8OAAC,uIAAA,CAAA,UAAiB;;;;;sDAGlB,8OAAC;4CACC,SAAS;4CACT,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;0CAQlB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,SAAQ;sDAER,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAKZ,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,OAAM;;0DAEN,8OAAC;gDACC,WAAU;gDACV,MAAK;gDACL,SAAQ;0DAER,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;0DAIV,8OAAC;gDAAK,WAAU;0DAAsI;;;;;;;;;;;;kDAMxJ,8OAAC,6IAAA,CAAA,UAAuB;;;;;kDAGxB,8OAAC,uIAAA,CAAA,UAAiB;;;;;kDAGlB,8OAAC,0IAAA,CAAA,UAAoB;wCACnB,MAAK;wCACL,WAAU;kDACX;;;;;;kDAKD,8OAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDAAW,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMpD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAI,WAAU;kDAAiE;;;;;;;;;;;8CAC9G,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAO,WAAU;kDAAqD;;;;;;;;;;;8CACrG,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAQ,WAAU;;0DAC/C,8OAAC,8HAAA,CAAA,UAAQ;gDAAC,MAAK;gDAAK,SAAQ;;;;;;0DAC5B,8OAAC;0DAAK;;;;;;;;;;;;;;;;;gCAEP,4BACC;;sDACE,8OAAC;sDAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;gDAAC,MAAK;gDAAiB,WAAU;0DAAqD;;;;;;;;;;;sDAC/G,8OAAC;sDAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;gDAAC,MAAK;gDAAO,WAAU;0DAAqD;;;;;;;;;;;sDACrG,8OAAC;sDAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;gDAAC,MAAK;gDAAW,WAAU;0DAAqD;;;;;;;;;;;;;8CAG7G,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAwB,WAAU;kDAAqD;;;;;;;;;;;8CACtH,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAiB,WAAU;kDAAqD;;;;;;;;;;;8CAC/G,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAwB,WAAU;kDAAqD;;;;;;;;;;;8CACtH,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAqB,WAAU;kDAAqD;;;;;;;;;;;8CACnH,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAgB,WAAU;kDAAiE;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAM/H,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBAEZ,CAAC,4BACA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;wCACP,gBAAgB;wCAChB,mBAAmB;wCACnB,cAAc;oCAChB;oCACA,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;wCACP,gBAAgB;wCAChB,mBAAmB;wCACnB,cAAc;oCAChB;oCACA,WAAU;8CACX;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAsB;;;;;;;;;;;;sDAGvC,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;;;;;;;sDAGrD,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8HAAA,CAAA,UAAQ;wDAAC,MAAK;wDAAK,SAAQ;;;;;;;;;;;8DAE9B,8OAAC;oDAAI,WAAU;8DAAqC;;;;;;;;;;;;sDAGtD,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;;;;;;;sCAM3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;;;;;;;sDAGrD,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAmC;;;;;;;;;;;;sDAGpD,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAsC;;;;;;;;;;;;sDAGvD,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAsC;;;;;;;;;;;;sDAGvD,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAsC;;;;;;;;;;;;;;;;;;;;;;;;sCAM3D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0IAAA,CAAA,UAAoB;wCACnB,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;;0DAE7B,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAGhC,8OAAC,0IAAA,CAAA,UAAoB;wCACnB,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;;0DAE7B,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1C,8OAAC,+HAAA,CAAA,UAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,YAAY;;;;;;;;;;;;AAIpB;uCAEe"}}, {"offset": {"line": 4756, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4762, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/LiveSearch.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\n\ninterface SearchResult {\n  id: number;\n  title: string;\n  price: string;\n  currency: string;\n  location: string;\n  category: string;\n  image?: string;\n  featured?: boolean;\n}\n\nconst LiveSearch = () => {\n  const [query, setQuery] = useState('');\n  const [results, setResults] = useState<SearchResult[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [showResults, setShowResults] = useState(false);\n  const router = useRouter();\n\n  // بيانات تجريبية للبحث\n  const sampleResults: SearchResult[] = [\n    {\n      id: 1,\n      title: 'شقة للبيع في دمشق - المالكي',\n      price: '85,000,000',\n      currency: 'ل.س',\n      location: 'دمشق - المالكي',\n      category: 'عقارات',\n      featured: true\n    },\n    {\n      id: 2,\n      title: 'BMW X5 2020 فل أوبشن',\n      price: '45,000',\n      currency: '$',\n      location: 'حلب - الفرقان',\n      category: 'سيارات',\n      featured: false\n    },\n    {\n      id: 3,\n      title: 'iPhone 15 Pro Max جديد',\n      price: '1,200',\n      currency: '$',\n      location: 'دمشق - أبو رمانة',\n      category: 'إلكترونيات',\n      featured: true\n    },\n    {\n      id: 4,\n      title: 'مطلوب مطور ويب',\n      price: '800,000',\n      currency: 'ل.س',\n      location: 'دمشق - المزة',\n      category: 'وظائف',\n      featured: false\n    },\n    {\n      id: 5,\n      title: 'فيلا للإيجار في حمص',\n      price: '500,000',\n      currency: 'ل.س',\n      location: 'حمص - الوعر',\n      category: 'عقارات',\n      featured: false\n    }\n  ];\n\n  const popularSearches = [\n    'شقة للبيع دمشق',\n    'سيارة مستعملة',\n    'iPhone 15',\n    'وظيفة مطور',\n    'فيلا للإيجار',\n    'لابتوب Dell',\n    'محل تجاري',\n    'دراجة نارية'\n  ];\n\n  useEffect(() => {\n    if (query.length > 2) {\n      setIsLoading(true);\n      // محاكاة استدعاء API\n      const timer = setTimeout(() => {\n        const filteredResults = sampleResults.filter(item =>\n          item.title.toLowerCase().includes(query.toLowerCase()) ||\n          item.category.toLowerCase().includes(query.toLowerCase()) ||\n          item.location.toLowerCase().includes(query.toLowerCase())\n        );\n        setResults(filteredResults);\n        setIsLoading(false);\n        setShowResults(true);\n      }, 300);\n\n      return () => clearTimeout(timer);\n    } else {\n      setResults([]);\n      setShowResults(false);\n      setIsLoading(false);\n    }\n  }, [query]);\n\n  const handleSearchSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (query.trim()) {\n      // توجيه إلى صفحة النتائج\n      router.push(`/search?q=${encodeURIComponent(query)}`);\n    }\n  };\n\n  const handlePopularSearch = (searchTerm: string) => {\n    setQuery(searchTerm);\n  };\n\n  return (\n    <div className=\"relative w-full max-w-2xl mx-auto\">\n      <form onSubmit={handleSearchSubmit} className=\"relative\">\n        <div className=\"relative\">\n          <input\n            type=\"text\"\n            value={query}\n            onChange={(e) => setQuery(e.target.value)}\n            placeholder=\"ابحث عن أي شيء... (عقارات، سيارات، وظائف، إلخ)\"\n            className=\"w-full px-4 py-4 pr-12 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-lg\"\n            onFocus={() => setShowResults(query.length > 2)}\n            onBlur={() => setTimeout(() => setShowResults(false), 200)}\n          />\n          <button\n            type=\"submit\"\n            className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary-600 transition-all duration-300\"\n          >\n            {isLoading ? (\n              <div className=\"animate-spin w-6 h-6 border-2 border-primary-600 border-t-transparent rounded-full\"></div>\n            ) : (\n              <span\n                className=\"text-2xl transition-all duration-300 hover:scale-110\"\n                style={{\n                  filter: 'grayscale(1) opacity(0.6) drop-shadow(0 0 4px rgba(59, 130, 246, 0.3))',\n                  transition: 'all 0.3s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.filter = 'grayscale(0) opacity(1) drop-shadow(0 0 8px rgba(59, 130, 246, 0.8))';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.filter = 'grayscale(1) opacity(0.6) drop-shadow(0 0 4px rgba(59, 130, 246, 0.3))';\n                }}\n              >\n                🔍\n              </span>\n            )}\n          </button>\n        </div>\n\n        {/* نتائج البحث المباشر */}\n        {showResults && (\n          <div className=\"absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-xl z-50 max-h-96 overflow-y-auto\">\n            {results.length > 0 ? (\n              <div>\n                <div className=\"p-4 border-b border-gray-100\">\n                  <h3 className=\"text-sm font-semibold text-gray-700\">\n                    نتائج البحث ({results.length})\n                  </h3>\n                </div>\n                <div className=\"divide-y divide-gray-100\">\n                  {results.map((result) => (\n                    <Link\n                      key={result.id}\n                      href={`/ad/${result.id}`}\n                      className=\"block p-4 hover:bg-gray-50 transition-colors\"\n                    >\n                      <div className=\"flex items-center gap-4\">\n                        <div className=\"w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0\">\n                          <span className=\"text-gray-400 text-xl\">🖼️</span>\n                        </div>\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex items-center gap-2 mb-1\">\n                            <h4 className=\"font-medium text-gray-800 truncate\">\n                              {result.title}\n                            </h4>\n                            {result.featured && (\n                              <span className=\"bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full\">\n                                مميز\n                              </span>\n                            )}\n                          </div>\n                          <div className=\"flex items-center justify-between\">\n                            <div className=\"text-sm text-gray-600\">\n                              📍 {result.location} • 📂 {result.category}\n                            </div>\n                            <div className=\"font-semibold text-primary-600\">\n                              {result.price} {result.currency}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n                    </Link>\n                  ))}\n                </div>\n                <div className=\"p-4 border-t border-gray-100\">\n                  <button\n                    onClick={handleSearchSubmit}\n                    className=\"w-full text-center text-primary-600 hover:text-primary-700 font-medium\"\n                  >\n                    عرض جميع النتائج ({results.length}) ←\n                  </button>\n                </div>\n              </div>\n            ) : query.length > 2 ? (\n              <div className=\"p-8 text-center\">\n                <div className=\"text-4xl mb-4\">🔍</div>\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n                  لا توجد نتائج\n                </h3>\n                <p className=\"text-gray-600 mb-4\">\n                  جرب كلمات مختلفة أو تحقق من الإملاء\n                </p>\n                <div className=\"text-sm text-gray-500\">\n                  البحث عن: \"{query}\"\n                </div>\n              </div>\n            ) : (\n              <div className=\"p-6\">\n                <h3 className=\"text-sm font-semibold text-gray-700 mb-4\">\n                  عمليات البحث الشائعة\n                </h3>\n                <div className=\"flex flex-wrap gap-2\">\n                  {popularSearches.map((search, index) => (\n                    <button\n                      key={index}\n                      onClick={() => handlePopularSearch(search)}\n                      className=\"text-sm bg-gray-100 text-gray-700 px-3 py-2 rounded-full hover:bg-primary-100 hover:text-primary-700 transition-colors\"\n                    >\n                      {search}\n                    </button>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        )}\n      </form>\n\n      {/* اقتراحات سريعة */}\n      {!showResults && query.length === 0 && (\n        <div className=\"mt-4\">\n          <div className=\"flex flex-wrap gap-2 justify-center\">\n            <span className=\"text-sm text-gray-600\">بحث سريع:</span>\n            {popularSearches.slice(0, 4).map((search, index) => (\n              <button\n                key={index}\n                onClick={() => handlePopularSearch(search)}\n                className=\"text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-full hover:bg-primary-100 hover:text-primary-700 transition-colors\"\n              >\n                {search}\n              </button>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default LiveSearch;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAiBA,MAAM,aAAa;IACjB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,uBAAuB;IACvB,MAAM,gBAAgC;QACpC;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;QACZ;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;QACZ;KACD;IAED,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,aAAa;YACb,qBAAqB;YACrB,MAAM,QAAQ,WAAW;gBACvB,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAA,OAC3C,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OACnD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OACtD,KAAK,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW;gBAExD,WAAW;gBACX,aAAa;gBACb,eAAe;YACjB,GAAG;YAEH,OAAO,IAAM,aAAa;QAC5B,OAAO;YACL,WAAW,EAAE;YACb,eAAe;YACf,aAAa;QACf;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,qBAAqB,CAAC;QAC1B,EAAE,cAAc;QAChB,IAAI,MAAM,IAAI,IAAI;YAChB,yBAAyB;YACzB,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,QAAQ;QACtD;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,SAAS;IACX;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAK,UAAU;gBAAoB,WAAU;;kCAC5C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,aAAY;gCACZ,WAAU;gCACV,SAAS,IAAM,eAAe,MAAM,MAAM,GAAG;gCAC7C,QAAQ,IAAM,WAAW,IAAM,eAAe,QAAQ;;;;;;0CAExD,8OAAC;gCACC,MAAK;gCACL,WAAU;0CAET,0BACC,8OAAC;oCAAI,WAAU;;;;;yDAEf,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,QAAQ;wCACR,YAAY;oCACd;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oCACjC;oCACA,cAAc,CAAC;wCACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oCACjC;8CACD;;;;;;;;;;;;;;;;;oBAQN,6BACC,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,MAAM,GAAG,kBAChB,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;;4CAAsC;4CACpC,QAAQ,MAAM;4CAAC;;;;;;;;;;;;8CAGjC,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE;4CACxB,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAwB;;;;;;;;;;;kEAE1C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFACX,OAAO,KAAK;;;;;;oEAEd,OAAO,QAAQ,kBACd,8OAAC;wEAAK,WAAU;kFAA+D;;;;;;;;;;;;0EAKnF,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;4EAAwB;4EACjC,OAAO,QAAQ;4EAAC;4EAAO,OAAO,QAAQ;;;;;;;kFAE5C,8OAAC;wEAAI,WAAU;;4EACZ,OAAO,KAAK;4EAAC;4EAAE,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;2CAxBlC,OAAO,EAAE;;;;;;;;;;8CAgCpB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,SAAS;wCACT,WAAU;;4CACX;4CACoB,QAAQ,MAAM;4CAAC;;;;;;;;;;;;;;;;;mCAItC,MAAM,MAAM,GAAG,kBACjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;wCAAwB;wCACzB;wCAAM;;;;;;;;;;;;iDAItB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CAGzD,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC;4CAEC,SAAS,IAAM,oBAAoB;4CACnC,WAAU;sDAET;2CAJI;;;;;;;;;;;;;;;;;;;;;;;;;;;YAepB,CAAC,eAAe,MAAM,MAAM,KAAK,mBAChC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAwB;;;;;;wBACvC,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBACxC,8OAAC;gCAEC,SAAS,IAAM,oBAAoB;gCACnC,WAAU;0CAET;+BAJI;;;;;;;;;;;;;;;;;;;;;;AAYrB;uCAEe"}}, {"offset": {"line": 5212, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5218, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/CategoryIcon.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface CategoryIconProps {\n  category: string;\n  className?: string;\n  color?: string;\n  isHovered?: boolean;\n  isPressed?: boolean;\n}\n\nconst CategoryIcon: React.FC<CategoryIconProps> = ({\n  category,\n  className = \"w-6 h-6\",\n  color = \"currentColor\",\n  isHovered = false,\n  isPressed = false\n}) => {\n\n  // تحديد ألوان الإضاءة لكل فئة\n  const glowColors: { [key: string]: string } = {\n    'real-estate': '#3b82f6', // أزرق\n    'cars': '#ef4444', // أحمر\n    'electronics': '#8b5cf6', // بنفسجي\n    'jobs': '#10b981', // أخضر\n    'offers-discounts': '#ea580c', // برتقالي محمر\n    'business-plans': '#f59e0b', // أصفر\n    'services': '#f97316', // برتقالي\n    'fashion': '#ec4899', // وردي\n    'furniture': '#eab308', // أصفر\n    'sports': '#6366f1', // نيلي\n    'books': '#14b8a6', // تركوازي\n    'pets': '#f59e0b', // كهرماني\n    'health': '#10b981', // أخضر زمردي\n    'food': '#f43f5e', // وردي أحمر\n    'tourism': '#0ea5e9' // أزرق سماوي\n  };\n\n  const currentGlowColor = glowColors[category] || '#10b981';\n\n  // تحديد الشفافية والتأثيرات - تحسين للموبايل\n  const opacity = isPressed ? '1' : isHovered ? '0.9' : '0.8';\n  const glowIntensity = isPressed ? '15px' : isHovered ? '8px' : '3px';\n  const glowSpread = isPressed ? '5px' : isHovered ? '2px' : '1px';\n\n  const iconStyle = {\n    opacity,\n    filter: `drop-shadow(0 0 ${glowIntensity} ${currentGlowColor}) drop-shadow(0 0 ${glowSpread} ${currentGlowColor}40) brightness(1.1) contrast(1.1)`,\n    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',\n    transform: isPressed ? 'scale(0.9)' : isHovered ? 'scale(1.1)' : 'scale(1)',\n    WebkitFilter: `drop-shadow(0 0 ${glowIntensity} ${currentGlowColor}) drop-shadow(0 0 ${glowSpread} ${currentGlowColor}40) brightness(1.1) contrast(1.1)`,\n    // تحسينات إضافية للموبايل\n    imageRendering: 'crisp-edges',\n    WebkitImageRendering: 'crisp-edges'\n  };\n  const iconMap: { [key: string]: JSX.Element } = {\n    'real-estate': (\n      <svg className={className} fill={color} viewBox=\"0 0 24 24\" style={iconStyle}>\n        <path d=\"M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z\" fillOpacity=\"0.4\"/>\n        <path d=\"M12 7.69l4 3.09V18h-2v-4H10v4H8v-7.22l4-3.09z\" fillOpacity=\"0.3\"/>\n      </svg>\n    ),\n    'cars': (\n      <svg className={className} fill={color} viewBox=\"0 0 24 24\" style={iconStyle}>\n        <path d=\"M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z\" fillOpacity=\"0.4\"/>\n      </svg>\n    ),\n    'electronics': (\n      <svg className={className} fill={color} viewBox=\"0 0 24 24\" style={iconStyle}>\n        <path d=\"M17 1.01L7 1c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM17 19H7V5h10v14z\" fillOpacity=\"0.4\"/>\n        <circle cx=\"12\" cy=\"17\" r=\"1\" fillOpacity=\"0.5\"/>\n        <rect x=\"9\" y=\"7\" width=\"6\" height=\"8\" rx=\"1\" fillOpacity=\"0.3\"/>\n      </svg>\n    ),\n    'jobs': (\n      <svg className={className} fill={color} viewBox=\"0 0 24 24\" style={iconStyle}>\n        <path d=\"M20 6h-2.5l-1.5-1.5h-5L9.5 6H7c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h13c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-8 3h4v2h-4V9z\" fillOpacity=\"0.4\"/>\n        <path d=\"M10 4V2h4v2h-4z\" fillOpacity=\"0.5\"/>\n      </svg>\n    ),\n    'offers-discounts': (\n      <img\n        src=\"/images/الخدمات/الخدمات.png\"\n        alt=\"العروض والتخفيضات\"\n        className={className}\n        style={iconStyle}\n      />\n    ),\n    'business-plans': (\n      <svg className={className} fill={color} viewBox=\"0 0 24 24\" style={iconStyle}>\n        <path d=\"M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z\" fillOpacity=\"0.4\"/>\n        <circle cx=\"12\" cy=\"12\" r=\"3\" fillOpacity=\"0.5\"/>\n      </svg>\n    ),\n    'services': (\n      <svg className={className} fill={color} viewBox=\"0 0 24 24\" style={iconStyle}>\n        <path d=\"M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z\" fillOpacity=\"0.4\"/>\n      </svg>\n    ),\n    'fashion': (\n      <img\n        src=\"/images/clothes/transparent-Photoroom (4).v2.png\"\n        alt=\"الأزياء والموضة\"\n        className={className}\n        style={iconStyle}\n      />\n    ),\n    'fashion-shirt-mirror': (\n      <div className=\"flex items-center justify-center gap-0.5\" style={iconStyle}>\n        {/* قميص */}\n        <svg className=\"w-3 h-3\" fill={color} viewBox=\"0 0 24 24\">\n          <path d=\"M16,4H15V2H9V4H8A1,1 0 0,0 7,5V11L9,13V22H15V13L17,11V5A1,1 0 0,0 16,4Z\" fillOpacity=\"0.8\"/>\n        </svg>\n        {/* مرآة */}\n        <svg className=\"w-3 h-3\" fill={color} viewBox=\"0 0 24 24\">\n          <path d=\"M12,2A7,7 0 0,1 19,9V15A7,7 0 0,1 12,22A7,7 0 0,1 5,15V9A7,7 0 0,1 12,2M12,4A5,5 0 0,0 7,9V15A5,5 0 0,0 12,20A5,5 0 0,0 17,15V9A5,5 0 0,0 12,4M12,6A3,3 0 0,1 15,9V15A3,3 0 0,1 12,18A3,3 0 0,1 9,15V9A3,3 0 0,1 12,6Z\" fillOpacity=\"0.6\"/>\n        </svg>\n      </div>\n    ),\n    'furniture': (\n      <svg className={className} fill={color} viewBox=\"0 0 24 24\" style={iconStyle}>\n        <path d=\"M7 13c1.66 0 3-1.34 3-3S8.66 7 7 7s-3 1.34-3 3 1.34 3 3 3zm12-6h-8v7H3V6H1v15h2v-3h18v3h2v-9c0-2.21-1.79-4-4-4z\" fillOpacity=\"0.4\"/>\n        <rect x=\"5\" y=\"15\" width=\"14\" height=\"2\" fillOpacity=\"0.5\"/>\n      </svg>\n    ),\n    'sports': (\n      <svg className={className} fill={color} viewBox=\"0 0 24 24\" style={iconStyle}>\n        <circle cx=\"12\" cy=\"12\" r=\"10\" fillOpacity=\"0.4\"/>\n        <path d=\"M8 8c2.5 2.5 5.5 2.5 8 0\" fill=\"none\" stroke=\"white\" strokeWidth=\"1\" opacity=\"0.5\"/>\n        <path d=\"M8 16c2.5-2.5 5.5-2.5 8 0\" fill=\"none\" stroke=\"white\" strokeWidth=\"1\" opacity=\"0.5\"/>\n        <path d=\"M8 8c0 2.5 0 5.5 0 8\" fill=\"none\" stroke=\"white\" strokeWidth=\"1\" opacity=\"0.5\"/>\n        <path d=\"M16 8c0 2.5 0 5.5 0 8\" fill=\"none\" stroke=\"white\" strokeWidth=\"1\" opacity=\"0.5\"/>\n        <path d=\"M12 6v12\" fill=\"none\" stroke=\"white\" strokeWidth=\"1\" opacity=\"0.5\"/>\n        <path d=\"M6 12h12\" fill=\"none\" stroke=\"white\" strokeWidth=\"1\" opacity=\"0.5\"/>\n      </svg>\n    ),\n    'books': (\n      <svg className={className} fill={color} viewBox=\"0 0 24 24\" style={iconStyle}>\n        <path d=\"M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z\" fillOpacity=\"0.3\"/>\n        <rect x=\"8\" y=\"14\" width=\"8\" height=\"1\" fillOpacity=\"0.4\"/>\n        <rect x=\"8\" y=\"16\" width=\"6\" height=\"1\" fillOpacity=\"0.4\"/>\n      </svg>\n    ),\n    'pets': (\n      <svg className={className} fill={color} viewBox=\"0 0 24 24\" style={iconStyle}>\n        <circle cx=\"4.5\" cy=\"9.5\" r=\"2.5\" fillOpacity=\"0.4\"/>\n        <circle cx=\"9\" cy=\"5.5\" r=\"2.5\" fillOpacity=\"0.4\"/>\n        <circle cx=\"15\" cy=\"5.5\" r=\"2.5\" fillOpacity=\"0.4\"/>\n        <circle cx=\"19.5\" cy=\"9.5\" r=\"2.5\" fillOpacity=\"0.4\"/>\n        <path d=\"M17.34 14.86c-.87-1.02-1.6-1.89-2.48-2.91-.46-.54-1.05-1.08-1.75-1.32-.11-.04-.22-.07-.33-.09-.25-.04-.52-.04-.77-.01-.11.02-.22.05-.33.09-.7.24-1.29.78-1.75 1.32-.87 1.02-1.6 1.89-2.48 2.91-1.31 1.31-2.92 2.76-2.62 4.79.29 1.02 1.02 2.03 2.33 2.32.73.15 3.06-.44 5.54-.44h.18c2.48 0 4.81.59 5.54.44 1.31-.29 2.04-1.31 2.33-2.32.3-2.03-1.31-3.48-2.62-4.79z\" fillOpacity=\"0.3\"/>\n      </svg>\n    ),\n    'health': (\n      <img\n        src=\"/images/Beautiful and Health/transparent-Photoroom-Photoroom (1).png\"\n        alt=\"الصحة والجمال\"\n        className={className}\n        style={iconStyle}\n      />\n    ),\n    'food': (\n      <svg className={className} fill={color} viewBox=\"0 0 24 24\" style={iconStyle}>\n        <path d=\"M11 9H9V2H7v7H5V2H3v7c0 2.12 1.66 3.84 3.75 3.97V22h2.5v-9.03C11.34 12.84 13 11.12 13 9V2h-2v7z\" fillOpacity=\"0.4\"/>\n        <path d=\"M16 6v8h2.5v8H21V2c-2.76 0-5 2.24-5 4z\" fillOpacity=\"0.4\"/>\n      </svg>\n    ),\n    'tourism': (\n      <img\n        src=\"/images/السياحة/Tourism.png\"\n        alt=\"السياحة والسفر\"\n        className={className}\n        style={iconStyle}\n      />\n    )\n  };\n\n  return iconMap[category] || (\n    <svg className={className} fill={color} viewBox=\"0 0 24 24\" style={iconStyle}>\n      <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\" fillOpacity=\"0.4\"/>\n    </svg>\n  );\n};\n\nexport default CategoryIcon;\n"], "names": [], "mappings": ";;;;AAAA;;AAYA,MAAM,eAA4C,CAAC,EACjD,QAAQ,EACR,YAAY,SAAS,EACrB,QAAQ,cAAc,EACtB,YAAY,KAAK,EACjB,YAAY,KAAK,EAClB;IAEC,8BAA8B;IAC9B,MAAM,aAAwC;QAC5C,eAAe;QACf,QAAQ;QACR,eAAe;QACf,QAAQ;QACR,oBAAoB;QACpB,kBAAkB;QAClB,YAAY;QACZ,WAAW;QACX,aAAa;QACb,UAAU;QACV,SAAS;QACT,QAAQ;QACR,UAAU;QACV,QAAQ;QACR,WAAW,UAAU,aAAa;IACpC;IAEA,MAAM,mBAAmB,UAAU,CAAC,SAAS,IAAI;IAEjD,6CAA6C;IAC7C,MAAM,UAAU,YAAY,MAAM,YAAY,QAAQ;IACtD,MAAM,gBAAgB,YAAY,SAAS,YAAY,QAAQ;IAC/D,MAAM,aAAa,YAAY,QAAQ,YAAY,QAAQ;IAE3D,MAAM,YAAY;QAChB;QACA,QAAQ,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE,iBAAiB,kBAAkB,EAAE,WAAW,CAAC,EAAE,iBAAiB,iCAAiC,CAAC;QAClJ,YAAY;QACZ,WAAW,YAAY,eAAe,YAAY,eAAe;QACjE,cAAc,CAAC,gBAAgB,EAAE,cAAc,CAAC,EAAE,iBAAiB,kBAAkB,EAAE,WAAW,CAAC,EAAE,iBAAiB,iCAAiC,CAAC;QACxJ,0BAA0B;QAC1B,gBAAgB;QAChB,sBAAsB;IACxB;IACA,MAAM,UAA0C;QAC9C,6BACE,8OAAC;YAAI,WAAW;YAAW,MAAM;YAAO,SAAQ;YAAY,OAAO;;8BACjE,8OAAC;oBAAK,GAAE;oBAAsC,aAAY;;;;;;8BAC1D,8OAAC;oBAAK,GAAE;oBAAgD,aAAY;;;;;;;;;;;;QAGxE,sBACE,8OAAC;YAAI,WAAW;YAAW,MAAM;YAAO,SAAQ;YAAY,OAAO;sBACjE,cAAA,8OAAC;gBAAK,GAAE;gBAAsV,aAAY;;;;;;;;;;;QAG9W,6BACE,8OAAC;YAAI,WAAW;YAAW,MAAM;YAAO,SAAQ;YAAY,OAAO;;8BACjE,8OAAC;oBAAK,GAAE;oBAA2G,aAAY;;;;;;8BAC/H,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAK,GAAE;oBAAI,aAAY;;;;;;8BAC1C,8OAAC;oBAAK,GAAE;oBAAI,GAAE;oBAAI,OAAM;oBAAI,QAAO;oBAAI,IAAG;oBAAI,aAAY;;;;;;;;;;;;QAG9D,sBACE,8OAAC;YAAI,WAAW;YAAW,MAAM;YAAO,SAAQ;YAAY,OAAO;;8BACjE,8OAAC;oBAAK,GAAE;oBAAqH,aAAY;;;;;;8BACzI,8OAAC;oBAAK,GAAE;oBAAkB,aAAY;;;;;;;;;;;;QAG1C,kCACE,8OAAC;YACC,KAAI;YACJ,KAAI;YACJ,WAAW;YACX,OAAO;;;;;;QAGX,gCACE,8OAAC;YAAI,WAAW;YAAW,MAAM;YAAO,SAAQ;YAAY,OAAO;;8BACjE,8OAAC;oBAAK,GAAE;oBAAoF,aAAY;;;;;;8BACxG,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAK,GAAE;oBAAI,aAAY;;;;;;;;;;;;QAG9C,0BACE,8OAAC;YAAI,WAAW;YAAW,MAAM;YAAO,SAAQ;YAAY,OAAO;sBACjE,cAAA,8OAAC;gBAAK,GAAE;gBAA2K,aAAY;;;;;;;;;;;QAGnM,yBACE,8OAAC;YACC,KAAI;YACJ,KAAI;YACJ,WAAW;YACX,OAAO;;;;;;QAGX,sCACE,8OAAC;YAAI,WAAU;YAA2C,OAAO;;8BAE/D,8OAAC;oBAAI,WAAU;oBAAU,MAAM;oBAAO,SAAQ;8BAC5C,cAAA,8OAAC;wBAAK,GAAE;wBAA0E,aAAY;;;;;;;;;;;8BAGhG,8OAAC;oBAAI,WAAU;oBAAU,MAAM;oBAAO,SAAQ;8BAC5C,cAAA,8OAAC;wBAAK,GAAE;wBAAyN,aAAY;;;;;;;;;;;;;;;;;QAInP,2BACE,8OAAC;YAAI,WAAW;YAAW,MAAM;YAAO,SAAQ;YAAY,OAAO;;8BACjE,8OAAC;oBAAK,GAAE;oBAAkH,aAAY;;;;;;8BACtI,8OAAC;oBAAK,GAAE;oBAAI,GAAE;oBAAK,OAAM;oBAAK,QAAO;oBAAI,aAAY;;;;;;;;;;;;QAGzD,wBACE,8OAAC;YAAI,WAAW;YAAW,MAAM;YAAO,SAAQ;YAAY,OAAO;;8BACjE,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAK,GAAE;oBAAK,aAAY;;;;;;8BAC3C,8OAAC;oBAAK,GAAE;oBAA2B,MAAK;oBAAO,QAAO;oBAAQ,aAAY;oBAAI,SAAQ;;;;;;8BACtF,8OAAC;oBAAK,GAAE;oBAA4B,MAAK;oBAAO,QAAO;oBAAQ,aAAY;oBAAI,SAAQ;;;;;;8BACvF,8OAAC;oBAAK,GAAE;oBAAuB,MAAK;oBAAO,QAAO;oBAAQ,aAAY;oBAAI,SAAQ;;;;;;8BAClF,8OAAC;oBAAK,GAAE;oBAAwB,MAAK;oBAAO,QAAO;oBAAQ,aAAY;oBAAI,SAAQ;;;;;;8BACnF,8OAAC;oBAAK,GAAE;oBAAW,MAAK;oBAAO,QAAO;oBAAQ,aAAY;oBAAI,SAAQ;;;;;;8BACtE,8OAAC;oBAAK,GAAE;oBAAW,MAAK;oBAAO,QAAO;oBAAQ,aAAY;oBAAI,SAAQ;;;;;;;;;;;;QAG1E,uBACE,8OAAC;YAAI,WAAW;YAAW,MAAM;YAAO,SAAQ;YAAY,OAAO;;8BACjE,8OAAC;oBAAK,GAAE;oBAAwG,aAAY;;;;;;8BAC5H,8OAAC;oBAAK,GAAE;oBAAI,GAAE;oBAAK,OAAM;oBAAI,QAAO;oBAAI,aAAY;;;;;;8BACpD,8OAAC;oBAAK,GAAE;oBAAI,GAAE;oBAAK,OAAM;oBAAI,QAAO;oBAAI,aAAY;;;;;;;;;;;;QAGxD,sBACE,8OAAC;YAAI,WAAW;YAAW,MAAM;YAAO,SAAQ;YAAY,OAAO;;8BACjE,8OAAC;oBAAO,IAAG;oBAAM,IAAG;oBAAM,GAAE;oBAAM,aAAY;;;;;;8BAC9C,8OAAC;oBAAO,IAAG;oBAAI,IAAG;oBAAM,GAAE;oBAAM,aAAY;;;;;;8BAC5C,8OAAC;oBAAO,IAAG;oBAAK,IAAG;oBAAM,GAAE;oBAAM,aAAY;;;;;;8BAC7C,8OAAC;oBAAO,IAAG;oBAAO,IAAG;oBAAM,GAAE;oBAAM,aAAY;;;;;;8BAC/C,8OAAC;oBAAK,GAAE;oBAAuW,aAAY;;;;;;;;;;;;QAG/X,wBACE,8OAAC;YACC,KAAI;YACJ,KAAI;YACJ,WAAW;YACX,OAAO;;;;;;QAGX,sBACE,8OAAC;YAAI,WAAW;YAAW,MAAM;YAAO,SAAQ;YAAY,OAAO;;8BACjE,8OAAC;oBAAK,GAAE;oBAAkG,aAAY;;;;;;8BACtH,8OAAC;oBAAK,GAAE;oBAAyC,aAAY;;;;;;;;;;;;QAGjE,yBACE,8OAAC;YACC,KAAI;YACJ,KAAI;YACJ,WAAW;YACX,OAAO;;;;;;IAGb;IAEA,OAAO,OAAO,CAAC,SAAS,kBACtB,8OAAC;QAAI,WAAW;QAAW,MAAM;QAAO,SAAQ;QAAY,OAAO;kBACjE,cAAA,8OAAC;YAAK,GAAE;YAAwH,aAAY;;;;;;;;;;;AAGlJ;uCAEe"}}, {"offset": {"line": 5777, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5783, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/HeroSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport SafeNavigationButton from './SafeNavigationButton';\nimport LiveSearch from './LiveSearch';\nimport CategoryIcon from './CategoryIcon';\n\nconst HeroSection = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedLocation, setSelectedLocation] = useState('all');\n\n  const quickCategories = [\n    { id: 'real-estate', name: 'عقارات', icon: 'real-estate', color: '#3b82f6' },\n    { id: 'cars', name: 'سيارات', icon: 'cars', color: '#ef4444' },\n    { id: 'electronics', name: 'إلكترونيات', icon: 'electronics', color: '#8b5cf6' },\n    { id: 'jobs', name: 'وظائف', icon: 'jobs', color: '#10b981' },\n  ];\n\n  const popularSearches = [\n    'شقة للبيع دمشق',\n    'سيارة مستعملة',\n    'iPhone 15',\n    'وظيفة مطور',\n    'فيلا للإيجار',\n    'لابتوب Dell'\n  ];\n\n  return (\n    <section className=\"relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n        }} />\n      </div>\n\n      <div className=\"container mx-auto px-4 py-16 relative\">\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-6\">\n            مرحباً بك في\n            <span className=\"block text-yellow-300\">من المالك</span>\n          </h1>\n          <p className=\"text-xl md:text-2xl text-primary-100 mb-8 max-w-3xl mx-auto\">\n            أكبر موقع للإعلانات المبوبة في سوريا - ابحث، اشتري، بع بكل سهولة وأمان\n          </p>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 mb-12\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold text-yellow-300 mb-2\">50K+</div>\n              <div className=\"text-primary-200 text-sm md:text-base\">إعلان نشط</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold text-yellow-300 mb-2\">25K+</div>\n              <div className=\"text-primary-200 text-sm md:text-base\">مستخدم</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold text-yellow-300 mb-2\">14</div>\n              <div className=\"text-primary-200 text-sm md:text-base\">محافظة</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl md:text-4xl font-bold text-yellow-300 mb-2\">24/7</div>\n              <div className=\"text-primary-200 text-sm md:text-base\">دعم فني</div>\n            </div>\n          </div>\n        </div>\n\n        {/* Search Section */}\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"bg-white rounded-2xl shadow-2xl p-6 md:p-8\">\n            <h2 className=\"text-2xl font-bold text-gray-800 text-center mb-6\">\n              ابحث عن أي شيء تريده\n            </h2>\n\n            {/* Live Search */}\n            <LiveSearch />\n\n            {/* Quick Categories */}\n            <div className=\"mt-8\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4 text-center\">\n                تصنيفات سريعة\n              </h3>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n                {quickCategories.map((category) => (\n                  <Link\n                    key={category.id}\n                    href={\n                      category.id === 'jobs' ? '/jobs' :\n                      `/category/${category.id}`\n                    }\n                    className=\"flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-primary-50 hover:text-primary-600 transition-colors group\"\n                  >\n                    <div className=\"w-8 h-8 flex items-center justify-center group-hover:scale-110 transition-transform\">\n                      <CategoryIcon\n                        category={category.icon}\n                        className=\"w-6 h-6\"\n                        color={category.color}\n                      />\n                    </div>\n                    <span className=\"font-medium text-gray-700 group-hover:text-primary-600\">\n                      {category.name}\n                    </span>\n                  </Link>\n                ))}\n              </div>\n            </div>\n\n            {/* Popular Searches */}\n            <div className=\"mt-6\">\n              <h4 className=\"text-sm font-medium text-gray-600 mb-3 text-center\">\n                عمليات البحث الشائعة:\n              </h4>\n              <div className=\"flex flex-wrap justify-center gap-2\">\n                {popularSearches.map((search, index) => (\n                  <button\n                    key={index}\n                    className=\"text-sm bg-gray-100 text-gray-600 px-3 py-1 rounded-full hover:bg-primary-100 hover:text-primary-700 transition-colors\"\n                  >\n                    {search}\n                  </button>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* CTA Buttons */}\n        <div className=\"flex flex-col md:flex-row gap-4 justify-center mt-12\">\n          <SafeNavigationButton\n            href=\"/jobs\"\n            className=\"bg-orange-500 text-white px-8 py-4 rounded-lg hover:bg-orange-600 transition-colors font-semibold text-lg text-center\"\n          >\n            تصفح الوظائف\n          </SafeNavigationButton>\n          <Link\n            href=\"/jobs\"\n            className=\"bg-white text-primary-600 px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors font-semibold text-lg text-center border-2 border-white\"\n          >\n            تصفح الإعلانات\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,cAAc;IAClB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,kBAAkB;QACtB;YAAE,IAAI;YAAe,MAAM;YAAU,MAAM;YAAe,OAAO;QAAU;QAC3E;YAAE,IAAI;YAAQ,MAAM;YAAU,MAAM;YAAQ,OAAO;QAAU;QAC7D;YAAE,IAAI;YAAe,MAAM;YAAc,MAAM;YAAe,OAAO;QAAU;QAC/E;YAAE,IAAI;YAAQ,MAAM;YAAS,MAAM;YAAQ,OAAO;QAAU;KAC7D;IAED,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB,CAAC,gQAAgQ,CAAC;oBACrR;;;;;;;;;;;0BAGF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAAsC;kDAElD,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAE1C,8OAAC;gCAAE,WAAU;0CAA8D;;;;;;0CAK3E,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsD;;;;;;0DACrE,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;;;;;;;kDAEzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsD;;;;;;0DACrE,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;;;;;;;kDAEzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsD;;;;;;0DACrE,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;;;;;;;kDAEzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsD;;;;;;0DACrE,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;;;;;;;;;;;;;;;;;;;kCAM7D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoD;;;;;;8CAKlE,8OAAC,gIAAA,CAAA,UAAU;;;;;8CAGX,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuD;;;;;;sDAGrE,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAC,yBACpB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MACE,SAAS,EAAE,KAAK,SAAS,UACzB,CAAC,UAAU,EAAE,SAAS,EAAE,EAAE;oDAE5B,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kIAAA,CAAA,UAAY;gEACX,UAAU,SAAS,IAAI;gEACvB,WAAU;gEACV,OAAO,SAAS,KAAK;;;;;;;;;;;sEAGzB,8OAAC;4DAAK,WAAU;sEACb,SAAS,IAAI;;;;;;;mDAfX,SAAS,EAAE;;;;;;;;;;;;;;;;8CAuBxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqD;;;;;;sDAGnE,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC5B,8OAAC;oDAEC,WAAU;8DAET;mDAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0IAAA,CAAA,UAAoB;gCACnB,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe"}}, {"offset": {"line": 6164, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6170, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/CategoryGrid.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { useState } from 'react';\nimport SafeNavigationButton from './SafeNavigationButton';\nimport MyCvLogo from './MyCvLogo';\nimport CategoryIcon from './CategoryIcon';\n\n\n\nconst categories = [\n  {\n    id: 'real-estate',\n    name: 'العقارات',\n    icon: 'real-estate',\n    description: 'شقق، فيلات، أراضي',\n    count: '12,450',\n    subcategories: ['للبيع', 'للإيجار', 'تجاري', 'أراضي'],\n    color: 'bg-blue-100/60 border border-blue-200/50',\n    iconColor: '#3b82f6'\n  },\n  {\n    id: 'cars',\n    name: 'السيارات',\n    icon: 'cars',\n    description: 'سيارات جديدة ومستعملة',\n    count: '8,230',\n    subcategories: ['جديدة', 'مستعملة', 'دراجات', 'قطع غيار'],\n    color: 'bg-red-100/60 border border-red-200/50',\n    iconColor: '#ef4444'\n  },\n  {\n    id: 'electronics',\n    name: 'الإلكترونيات',\n    icon: 'electronics',\n    description: 'هواتف، حاسوب، أجهزة',\n    count: '15,670',\n    subcategories: ['هواتف', 'حاسوب', 'تلفزيون', 'ألعاب'],\n    color: 'bg-purple-100/60 border border-purple-200/50',\n    iconColor: '#8b5cf6'\n  },\n  {\n    id: 'services',\n    name: 'الخدمات',\n    icon: 'services',\n    description: 'خدمات متنوعة',\n    count: '5,420',\n    subcategories: ['صيانة', 'تنظيف', 'توصيل', 'تعليم'],\n    color: 'bg-orange-100/60 border border-orange-200/50',\n    iconColor: '#f97316'\n  },\n  {\n    id: 'fashion',\n    name: 'الأزياء والموضة',\n    icon: 'fashion',\n    description: 'ملابس، أحذية، إكسسوارات',\n    count: '7,650',\n    subcategories: ['رجالي', 'نسائي', 'أطفال', 'أحذية'],\n    color: 'bg-pink-100/60 border border-pink-200/50',\n    iconColor: '#ec4899'\n  },\n  {\n    id: 'furniture',\n    name: 'الأثاث والمنزل',\n    icon: 'furniture',\n    description: 'أثاث، ديكور، أدوات منزلية',\n    count: '4,320',\n    subcategories: ['غرف نوم', 'صالونات', 'مطبخ', 'ديكور'],\n    color: 'bg-yellow-100/60 border border-yellow-200/50',\n    iconColor: '#eab308'\n  },\n  {\n    id: 'sports',\n    name: 'الرياضة والترفيه',\n    icon: 'sports',\n    description: 'معدات رياضية، ألعاب',\n    count: '2,180',\n    subcategories: ['كرة قدم', 'لياقة', 'ألعاب', 'رحلات'],\n    color: 'bg-indigo-100/60 border border-indigo-200/50',\n    iconColor: '#6366f1'\n  },\n  {\n    id: 'books',\n    name: 'الكتب والتعليم',\n    icon: 'books',\n    description: 'كتب، دورات، تعليم',\n    count: '1,890',\n    subcategories: ['كتب', 'دورات', 'جامعة', 'مدرسة'],\n    color: 'bg-teal-100/60 border border-teal-200/50',\n    iconColor: '#14b8a6'\n  },\n  {\n    id: 'pets',\n    name: 'الحيوانات الأليفة',\n    icon: 'pets',\n    description: 'حيوانات، مستلزمات',\n    count: '980',\n    subcategories: ['قطط', 'كلاب', 'طيور', 'مستلزمات'],\n    color: 'bg-amber-100/60 border border-amber-200/50',\n    iconColor: '#f59e0b'\n  },\n  {\n    id: 'health',\n    name: 'الصحة والجمال',\n    icon: 'health',\n    description: 'منتجات صحية وتجميل',\n    count: '1,560',\n    subcategories: ['أدوية', 'تجميل', 'عطور', 'صحة'],\n    color: 'bg-emerald-100/60 border border-emerald-200/50',\n    iconColor: '#10b981'\n  },\n  {\n    id: 'food',\n    name: 'الطعام والمشروبات',\n    icon: 'food',\n    description: 'مطاعم، طعام، مشروبات',\n    count: '2,340',\n    subcategories: ['مطاعم', 'حلويات', 'مشروبات', 'طبخ'],\n    color: 'bg-rose-100/60 border border-rose-200/50',\n    iconColor: '#f43f5e'\n  },\n  {\n    id: 'tourism',\n    name: 'السياحة والسفر',\n    icon: 'tourism',\n    description: 'فنادق، رحلات، خدمات سياحية',\n    count: '1,890',\n    subcategories: ['فنادق', 'رحلات', 'نقل', 'مرشدين'],\n    color: 'bg-sky-100/60 border border-sky-200/50',\n    iconColor: '#0ea5e9'\n  },\n  {\n    id: 'jobs',\n    name: 'الوظائف',\n    icon: 'jobs',\n    description: 'فرص عمل متنوعة',\n    count: '3,890',\n    subcategories: ['دوام كامل', 'دوام جزئي', 'عمل حر', 'تدريب'],\n    color: 'bg-green-100/60 border border-green-200/50',\n    iconColor: '#10b981'\n  },\n  {\n    id: 'offers-discounts',\n    name: 'العروض والتخفيضات',\n    icon: 'offers-discounts',\n    description: 'عروض المطاعم والتخفيضات الخاصة',\n    count: '2,150',\n    subcategories: ['مطاعم', 'تسوق', 'خدمات', 'ترفيه', 'سفر', 'تعليم'],\n    color: 'bg-gradient-to-r from-orange-100/60 to-red-100/60 border border-orange-200/50',\n    iconColor: '#ea580c',\n    isSpecial: true\n  },\n  {\n    id: 'business-plans',\n    name: 'خطط الشركات',\n    icon: 'business-plans',\n    description: 'باقات النشر والترقية للشركات',\n    count: '3 باقات',\n    subcategories: ['أساسية', 'مميزة', 'أعمال'],\n    color: 'bg-gradient-to-r from-yellow-100/60 to-amber-100/60 border border-yellow-200/50',\n    iconColor: '#f59e0b',\n    isSpecial: true,\n    isCompact: true\n  }\n];\n\nconst CategoryGrid = () => {\n  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);\n  const [pressedCategory, setPressedCategory] = useState<string | null>(null);\n\n  return (\n    <section className=\"py-12 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-10\">\n          <h2 className=\"text-3xl font-bold text-gray-800 mb-4\">تصفح حسب التصنيف</h2>\n          <p className=\"text-gray-600 text-lg\">اختر التصنيف المناسب لك وابدأ البحث أو أضف إعلانك الخاص</p>\n        </div>\n\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6\">\n          {categories.map((category) => (\n            <div\n              key={category.id}\n              className={`group bg-white rounded-lg md:rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border overflow-hidden relative ${\n                category.isSpecial\n                  ? 'border-yellow-300 hover:border-yellow-400 ring-2 ring-yellow-100'\n                  : 'border-gray-100 hover:border-primary-200'\n              }`}\n              onMouseEnter={() => setHoveredCategory(category.id)}\n              onMouseLeave={() => setHoveredCategory(null)}\n              onMouseDown={() => setPressedCategory(category.id)}\n              onMouseUp={() => setPressedCategory(null)}\n              onTouchStart={() => setPressedCategory(category.id)}\n              onTouchEnd={() => setPressedCategory(null)}\n            >\n              <div className=\"p-4 md:p-6\">\n                <div className=\"flex items-center justify-between mb-3 md:mb-4\">\n                  <div\n                    className={`category-container w-12 h-12 md:w-14 md:h-14 ${category.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-all duration-300 cursor-pointer backdrop-blur-sm`}\n                    style={{\n                      // تحسينات إضافية للموبايل\n                      minWidth: '48px',\n                      minHeight: '48px',\n                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n                    }}\n                  >\n                    {category.id === 'jobs' ? (\n                      <MyCvLogo size=\"sm\" variant=\"square\" style={{ color: category.iconColor }} />\n                    ) : (\n                      <CategoryIcon\n                        category={category.icon}\n                        className=\"category-icon w-7 h-7 md:w-8 md:h-8\"\n                        color={category.iconColor}\n                        isHovered={hoveredCategory === category.id}\n                        isPressed={pressedCategory === category.id}\n                      />\n                    )}\n                  </div>\n                  <span className=\"text-xs md:text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full\">\n                    {category.count}\n                  </span>\n                </div>\n\n                <h3 className=\"text-base md:text-lg font-semibold text-gray-800 mb-2 group-hover:text-primary-600 transition-colors\">\n                  {category.name}\n                </h3>\n\n                <p className=\"text-gray-600 text-xs md:text-sm mb-3 md:mb-4\">\n                  {category.description}\n                </p>\n\n                {/* Special content for Jobs category */}\n                {category.id === 'jobs' && (\n                  <div className=\"mb-4 p-3 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-lg border border-yellow-200 shadow-sm\">\n                    <div className=\"flex items-center gap-2 mb-2\">\n                      <MyCvLogo size=\"sm\" variant=\"square\" />\n                      <span className=\"text-xs text-amber-800 font-medium\">\n                        مدعوم من قبل تطبيق MyCv\n                      </span>\n                    </div>\n                    <p className=\"text-xs text-amber-700\">\n                      منصة متكاملة للسير الذاتية والتوظيف\n                    </p>\n                  </div>\n                )}\n\n                {/* Special content for Business Plans category */}\n                {category.id === 'business-plans' && (\n                  <div className=\"mb-3 space-y-2\">\n                    {/* Compact Pricing Preview */}\n                    <div className=\"p-2 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200\">\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-xs font-semibold text-green-800\">💰 من</span>\n                        <div className=\"flex items-center gap-1\">\n                          <span className=\"text-sm font-bold text-green-700\">25,000 ل.س</span>\n                          <span className=\"text-xs text-green-600\">/شهر</span>\n                        </div>\n                      </div>\n                    </div>\n\n                    {/* Compact Features Preview */}\n                    <div className=\"p-2 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200\">\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-xs font-semibold text-purple-800\">⭐ مميز</span>\n                        <span className=\"text-xs text-purple-700\">إعلانات + إحصائيات</span>\n                      </div>\n                    </div>\n                  </div>\n                )}\n\n                <div className=\"flex flex-wrap gap-1\">\n                  {category.subcategories.slice(0, 3).map((sub, index) => (\n                    <span\n                      key={index}\n                      className=\"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full\"\n                    >\n                      {sub}\n                    </span>\n                  ))}\n                  {category.subcategories.length > 3 && (\n                    <span className=\"text-xs text-gray-400 px-2 py-1\">\n                      +{category.subcategories.length - 3}\n                    </span>\n                  )}\n                </div>\n              </div>\n\n              {/* أيقونة إضافة إعلان */}\n              {hoveredCategory === category.id && category.id !== 'business-plans' && (\n                <div className=\"absolute top-4 left-4\">\n                  <SafeNavigationButton\n                    href={\n                      category.id === 'jobs' ? '/jobs' :\n                      `/add-ad?category=${category.id}`\n                    }\n                    className=\"bg-green-500 hover:bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-lg transition-all duration-200 shadow-lg hover:shadow-xl\"\n                    title={category.id === 'jobs' ? 'تصفح الوظائف' : 'إضافة إعلان'}\n                  >\n                    +\n                  </SafeNavigationButton>\n                </div>\n              )}\n\n              {/* أزرار التصفح والإضافة */}\n              <div className={`px-6 py-3 transition-colors ${\n                category.isSpecial\n                  ? 'bg-gradient-to-r from-yellow-50 to-amber-50 group-hover:from-yellow-100 group-hover:to-amber-100'\n                  : 'bg-gray-50 group-hover:bg-primary-50'\n              }`}>\n                {category.id === 'business-plans' ? (\n                  <Link\n                    href=\"/pricing\"\n                    className={`block text-sm font-medium ${\n                      category.isSpecial\n                        ? 'text-amber-700 group-hover:text-amber-800'\n                        : 'text-gray-600 group-hover:text-primary-600'\n                    }`}\n                  >\n                    اشترك الآن ←\n                  </Link>\n                ) : (\n                  <div className=\"flex items-center justify-between\">\n                    <Link\n                      href={\n                        category.id === 'jobs' ? '/jobs' :\n                        `/category/${category.id}`\n                      }\n                      className={`text-sm font-medium ${\n                        category.isSpecial\n                          ? 'text-amber-700 group-hover:text-amber-800'\n                          : 'text-gray-600 group-hover:text-primary-600'\n                      }`}\n                    >\n                      تصفح الإعلانات ←\n                    </Link>\n                    <SafeNavigationButton\n                      href={\n                        category.id === 'jobs' ? '/jobs' :\n                        `/add-ad?category=${category.id}`\n                      }\n                      className=\"bg-primary-600 hover:bg-primary-700 text-white text-xs px-3 py-1 rounded-full transition-colors\"\n                    >\n                      + إضافة\n                    </SafeNavigationButton>\n                  </div>\n                )}\n              </div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"text-center mt-10\">\n          <Link\n            href=\"/categories\"\n            className=\"inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium\"\n          >\n            عرض جميع التصنيفات\n            <span className=\"mr-2\">→</span>\n          </Link>\n        </div>\n      </div>\n\n      {/* CSS إضافي لتحسين عرض الأيقونات على الموبايل */}\n      <style jsx>{`\n        @media (max-width: 768px) {\n          :global(.category-icon) {\n            filter: brightness(1.2) contrast(1.1) !important;\n            opacity: 0.9 !important;\n          }\n          :global(.category-container) {\n            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;\n          }\n        }\n      `}</style>\n    </section>\n  );\n};\n\nexport default CategoryGrid;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAUA,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAS;YAAW;YAAS;SAAQ;QACrD,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAS;YAAW;YAAU;SAAW;QACzD,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAS;YAAS;YAAW;SAAQ;QACrD,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAS;YAAS;YAAS;SAAQ;QACnD,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAS;YAAS;YAAS;SAAQ;QACnD,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAW;YAAW;YAAQ;SAAQ;QACtD,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAW;YAAS;YAAS;SAAQ;QACrD,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAO;YAAS;YAAS;SAAQ;QACjD,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAO;YAAQ;YAAQ;SAAW;QAClD,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAS;YAAS;YAAQ;SAAM;QAChD,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAS;YAAU;YAAW;SAAM;QACpD,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAS;YAAS;YAAO;SAAS;QAClD,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAa;YAAa;YAAU;SAAQ;QAC5D,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAS;YAAQ;YAAS;YAAS;YAAO;SAAQ;QAClE,OAAO;QACP,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;YAAC;YAAU;YAAS;SAAQ;QAC3C,OAAO;QACP,WAAW;QACX,WAAW;QACX,WAAW;IACb;CACD;AAED,MAAM,eAAe;IACnB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,qBACE,8OAAC;kDAAkB;;0BACjB,8OAAC;0DAAc;;kCACb,8OAAC;kEAAc;;0CACb,8OAAC;0EAAa;0CAAwC;;;;;;0CACtD,8OAAC;0EAAY;0CAAwB;;;;;;;;;;;;kCAGvC,8OAAC;kEAAc;kCACZ,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC;gCAOC,cAAc,IAAM,mBAAmB,SAAS,EAAE;gCAClD,cAAc,IAAM,mBAAmB;gCACvC,aAAa,IAAM,mBAAmB,SAAS,EAAE;gCACjD,WAAW,IAAM,mBAAmB;gCACpC,cAAc,IAAM,mBAAmB,SAAS,EAAE;gCAClD,YAAY,IAAM,mBAAmB;0EAV1B,CAAC,8HAA8H,EACxI,SAAS,SAAS,GACd,qEACA,4CACJ;;kDAQF,8OAAC;kFAAc;;0DACb,8OAAC;0FAAc;;kEACb,8OAAC;wDAEC,OAAO;4DACL,0BAA0B;4DAC1B,UAAU;4DACV,WAAW;4DACX,WAAW;wDACb;kGANW,CAAC,6CAA6C,EAAE,SAAS,KAAK,CAAC,8HAA8H,CAAC;kEAQxM,SAAS,EAAE,KAAK,uBACf,8OAAC,8HAAA,CAAA,UAAQ;4DAAC,MAAK;4DAAK,SAAQ;4DAAS,OAAO;gEAAE,OAAO,SAAS,SAAS;4DAAC;;;;;iFAExE,8OAAC,kIAAA,CAAA,UAAY;4DACX,UAAU,SAAS,IAAI;4DACvB,WAAU;4DACV,OAAO,SAAS,SAAS;4DACzB,WAAW,oBAAoB,SAAS,EAAE;4DAC1C,WAAW,oBAAoB,SAAS,EAAE;;;;;;;;;;;kEAIhD,8OAAC;kGAAe;kEACb,SAAS,KAAK;;;;;;;;;;;;0DAInB,8OAAC;0FAAa;0DACX,SAAS,IAAI;;;;;;0DAGhB,8OAAC;0FAAY;0DACV,SAAS,WAAW;;;;;;4CAItB,SAAS,EAAE,KAAK,wBACf,8OAAC;0FAAc;;kEACb,8OAAC;kGAAc;;0EACb,8OAAC,8HAAA,CAAA,UAAQ;gEAAC,MAAK;gEAAK,SAAQ;;;;;;0EAC5B,8OAAC;0GAAe;0EAAqC;;;;;;;;;;;;kEAIvD,8OAAC;kGAAY;kEAAyB;;;;;;;;;;;;4CAOzC,SAAS,EAAE,KAAK,kCACf,8OAAC;0FAAc;;kEAEb,8OAAC;kGAAc;kEACb,cAAA,8OAAC;sGAAc;;8EACb,8OAAC;8GAAe;8EAAuC;;;;;;8EACvD,8OAAC;8GAAc;;sFACb,8OAAC;sHAAe;sFAAmC;;;;;;sFACnD,8OAAC;sHAAe;sFAAyB;;;;;;;;;;;;;;;;;;;;;;;kEAM/C,8OAAC;kGAAc;kEACb,cAAA,8OAAC;sGAAc;;8EACb,8OAAC;8GAAe;8EAAwC;;;;;;8EACxD,8OAAC;8GAAe;8EAA0B;;;;;;;;;;;;;;;;;;;;;;;0DAMlD,8OAAC;0FAAc;;oDACZ,SAAS,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC5C,8OAAC;sGAEW;sEAET;2DAHI;;;;;oDAMR,SAAS,aAAa,CAAC,MAAM,GAAG,mBAC/B,8OAAC;kGAAe;;4DAAkC;4DAC9C,SAAS,aAAa,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;oCAOzC,oBAAoB,SAAS,EAAE,IAAI,SAAS,EAAE,KAAK,kCAClD,8OAAC;kFAAc;kDACb,cAAA,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MACE,SAAS,EAAE,KAAK,SAAS,UACzB,CAAC,iBAAiB,EAAE,SAAS,EAAE,EAAE;4CAEnC,WAAU;4CACV,OAAO,SAAS,EAAE,KAAK,SAAS,iBAAiB;sDAClD;;;;;;;;;;;kDAOL,8OAAC;kFAAe,CAAC,4BAA4B,EAC3C,SAAS,SAAS,GACd,qGACA,wCACJ;kDACC,SAAS,EAAE,KAAK,iCACf,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAW,CAAC,0BAA0B,EACpC,SAAS,SAAS,GACd,8CACA,8CACJ;sDACH;;;;;iEAID,8OAAC;sFAAc;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDACH,MACE,SAAS,EAAE,KAAK,SAAS,UACzB,CAAC,UAAU,EAAE,SAAS,EAAE,EAAE;oDAE5B,WAAW,CAAC,oBAAoB,EAC9B,SAAS,SAAS,GACd,8CACA,8CACJ;8DACH;;;;;;8DAGD,8OAAC,0IAAA,CAAA,UAAoB;oDACnB,MACE,SAAS,EAAE,KAAK,SAAS,UACzB,CAAC,iBAAiB,EAAE,SAAS,EAAE,EAAE;oDAEnC,WAAU;8DACX;;;;;;;;;;;;;;;;;;+BA/JF,SAAS,EAAE;;;;;;;;;;kCAyKtB,8OAAC;kEAAc;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;gCACX;8CAEC,8OAAC;8EAAe;8CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBnC;uCAEe"}}, {"offset": {"line": 6814, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6820, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/StarRating.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface StarRatingProps {\n  rating: number;\n  maxRating?: number;\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';\n  interactive?: boolean;\n  onChange?: (rating: number) => void;\n  showValue?: boolean;\n  className?: string;\n}\n\nconst StarRating = ({\n  rating,\n  maxRating = 5,\n  size = 'md',\n  interactive = false,\n  onChange,\n  showValue = false,\n  className = ''\n}: StarRatingProps) => {\n  const [hoverRating, setHoverRating] = useState(0);\n\n  const sizeClasses = {\n    xs: 'w-3 h-3',\n    sm: 'w-4 h-4',\n    md: 'w-5 h-5',\n    lg: 'w-6 h-6',\n    xl: 'w-8 h-8'\n  };\n\n  const textSizes = {\n    xs: 'text-xs',\n    sm: 'text-sm',\n    md: 'text-base',\n    lg: 'text-lg',\n    xl: 'text-xl'\n  };\n\n  const handleClick = (value: number) => {\n    if (interactive && onChange) {\n      onChange(value);\n    }\n  };\n\n  const handleMouseEnter = (value: number) => {\n    if (interactive) {\n      setHoverRating(value);\n    }\n  };\n\n  const handleMouseLeave = () => {\n    if (interactive) {\n      setHoverRating(0);\n    }\n  };\n\n  const getStarFill = (starIndex: number) => {\n    const currentRating = hoverRating || rating;\n    const fillPercentage = Math.min(Math.max(currentRating - starIndex, 0), 1) * 100;\n    return fillPercentage;\n  };\n\n  const StarIcon = ({ index, fillPercentage }: { index: number; fillPercentage: number }) => {\n    const isActive = fillPercentage > 0;\n    const isHovered = interactive && hoverRating > 0;\n    \n    return (\n      <div\n        className={`relative ${sizeClasses[size]} ${interactive ? 'cursor-pointer hover:scale-110' : ''} transition-all duration-300 ease-in-out`}\n        onClick={() => handleClick(index + 1)}\n        onMouseEnter={() => handleMouseEnter(index + 1)}\n        onMouseLeave={handleMouseLeave}\n      >\n        {/* النجمة الخلفية */}\n        <svg\n          className={`absolute inset-0 ${sizeClasses[size]} text-gray-300 transition-all duration-300`}\n          fill=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\" />\n        </svg>\n\n        {/* النجمة المملوءة مع التدرج */}\n        <div\n          className=\"absolute inset-0 overflow-hidden\"\n          style={{ width: `${fillPercentage}%` }}\n        >\n          <svg\n            className={`${sizeClasses[size]} transition-all duration-300 ${\n              isActive\n                ? isHovered\n                  ? 'text-yellow-500'\n                  : 'text-yellow-400'\n                : 'text-gray-300'\n            }`}\n            fill=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            style={{\n              filter: isActive\n                ? isHovered\n                  ? 'drop-shadow(0 0 6px rgba(251, 191, 36, 0.8)) brightness(1.15)'\n                  : 'drop-shadow(0 0 3px rgba(251, 191, 36, 0.5))'\n                : 'none'\n            }}\n          >\n            <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\" />\n          </svg>\n        </div>\n\n        {/* تأثير التوهج عند التفاعل */}\n        {interactive && isHovered && (\n          <div className=\"absolute inset-0 animate-pulse\">\n            <svg\n              className={`${sizeClasses[size]} text-yellow-300 opacity-40`}\n              fill=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n              xmlns=\"http://www.w3.org/2000/svg\"\n              style={{ filter: 'drop-shadow(0 0 8px rgba(251, 191, 36, 0.9))' }}\n            >\n              <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\" />\n            </svg>\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  return (\n    <div className={`flex items-center gap-1 ${className}`}>\n      <div className=\"flex items-center gap-0.5\">\n        {Array.from({ length: maxRating }, (_, index) => (\n          <StarIcon\n            key={index}\n            index={index}\n            fillPercentage={getStarFill(index)}\n          />\n        ))}\n      </div>\n      \n      {showValue && (\n        <span className={`ml-2 font-medium text-gray-600 ${textSizes[size]}`}>\n          {rating.toFixed(1)}\n        </span>\n      )}\n    </div>\n  );\n};\n\nexport default StarRating;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAcA,MAAM,aAAa,CAAC,EAClB,MAAM,EACN,YAAY,CAAC,EACb,OAAO,IAAI,EACX,cAAc,KAAK,EACnB,QAAQ,EACR,YAAY,KAAK,EACjB,YAAY,EAAE,EACE;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,YAAY;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,eAAe,UAAU;YAC3B,SAAS;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,aAAa;YACf,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,aAAa;YACf,eAAe;QACjB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,gBAAgB,eAAe;QACrC,MAAM,iBAAiB,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,gBAAgB,WAAW,IAAI,KAAK;QAC7E,OAAO;IACT;IAEA,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,cAAc,EAA6C;QACpF,MAAM,WAAW,iBAAiB;QAClC,MAAM,YAAY,eAAe,cAAc;QAE/C,qBACE,8OAAC;YACC,WAAW,CAAC,SAAS,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,mCAAmC,GAAG,wCAAwC,CAAC;YACzI,SAAS,IAAM,YAAY,QAAQ;YACnC,cAAc,IAAM,iBAAiB,QAAQ;YAC7C,cAAc;;8BAGd,8OAAC;oBACC,WAAW,CAAC,iBAAiB,EAAE,WAAW,CAAC,KAAK,CAAC,0CAA0C,CAAC;oBAC5F,MAAK;oBACL,SAAQ;oBACR,OAAM;8BAEN,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;8BAIV,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,eAAe,CAAC,CAAC;oBAAC;8BAErC,cAAA,8OAAC;wBACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,6BAA6B,EAC3D,WACI,YACE,oBACA,oBACF,iBACJ;wBACF,MAAK;wBACL,SAAQ;wBACR,OAAM;wBACN,OAAO;4BACL,QAAQ,WACJ,YACE,kEACA,iDACF;wBACN;kCAEA,cAAA,8OAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;gBAKX,eAAe,2BACd,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,2BAA2B,CAAC;wBAC5D,MAAK;wBACL,SAAQ;wBACR,OAAM;wBACN,OAAO;4BAAE,QAAQ;wBAA+C;kCAEhE,cAAA,8OAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;IAMpB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BACpD,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAU,GAAG,CAAC,GAAG,sBACrC,8OAAC;wBAEC,OAAO;wBACP,gBAAgB,YAAY;uBAFvB;;;;;;;;;;YAOV,2BACC,8OAAC;gBAAK,WAAW,CAAC,+BAA+B,EAAE,SAAS,CAAC,KAAK,EAAE;0BACjE,OAAO,OAAO,CAAC;;;;;;;;;;;;AAK1B;uCAEe"}}, {"offset": {"line": 6990, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6996, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/hooks/useToast.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback } from 'react';\n\nexport type ToastType = 'success' | 'error' | 'warning' | 'info';\n\nexport interface Toast {\n  id: string;\n  message: string;\n  type: ToastType;\n  duration?: number;\n}\n\nexport const useToast = () => {\n  const [toasts, setToasts] = useState<Toast[]>([]);\n\n  const showToast = useCallback((message: string, type: ToastType = 'info', duration: number = 3000) => {\n    const id = Math.random().toString(36).substr(2, 9);\n    const toast: Toast = { id, message, type, duration };\n    \n    setToasts(prev => [...prev, toast]);\n\n    // Auto remove toast after duration\n    setTimeout(() => {\n      setToasts(prev => prev.filter(t => t.id !== id));\n    }, duration);\n\n    return id;\n  }, []);\n\n  const removeToast = useCallback((id: string) => {\n    setToasts(prev => prev.filter(t => t.id !== id));\n  }, []);\n\n  const clearAllToasts = useCallback(() => {\n    setToasts([]);\n  }, []);\n\n  return {\n    toasts,\n    showToast,\n    removeToast,\n    clearAllToasts\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAaO,MAAM,WAAW;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,SAAiB,OAAkB,MAAM,EAAE,WAAmB,IAAI;QAC/F,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,QAAe;YAAE;YAAI;YAAS;YAAM;QAAS;QAEnD,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAM;QAElC,mCAAmC;QACnC,WAAW;YACT,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC9C,GAAG;QAEH,OAAO;IACT,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC9C,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,UAAU,EAAE;IACd,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF"}}, {"offset": {"line": 7035, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7041, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/hooks/useAuthAction.ts"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/contexts/AuthContext';\nimport { toast } from 'react-hot-toast';\nimport { useState } from 'react';\n\ninterface AuthActionOptions {\n  requireAuth?: boolean;\n  showLoginPrompt?: boolean;\n  loginMessage?: string;\n  onAuthRequired?: () => void;\n}\n\nexport function useAuthAction(options: AuthActionOptions = {}) {\n  const { \n    requireAuth = true, \n    showLoginPrompt = true, \n    loginMessage = 'يجب تسجيل الدخول لاستخدام هذه الميزة',\n    onAuthRequired \n  } = options;\n  \n  const { user, isAuthenticated } = useAuth();\n  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);\n\n  const executeAction = (action: () => void | Promise<void>) => {\n    if (requireAuth && !isAuthenticated) {\n      // إظهار رسالة تسجيل الدخول\n      if (showLoginPrompt) {\n        toast.error(loginMessage);\n      }\n      \n      // تنفيذ callback مخصص إذا كان متاحاً\n      if (onAuthRequired) {\n        onAuthRequired();\n      } else {\n        // فتح modal تسجيل الدخول (يمكن تخصيصه لاحقاً)\n        setIsAuthModalOpen(true);\n      }\n      \n      return false;\n    }\n\n    // تنفيذ الإجراء إذا كان المستخدم مصادق عليه\n    try {\n      action();\n      return true;\n    } catch (error) {\n      console.error('خطأ في تنفيذ الإجراء:', error);\n      toast.error('حدث خطأ أثناء تنفيذ العملية');\n      return false;\n    }\n  };\n\n  const executeAsyncAction = async (action: () => Promise<void>) => {\n    if (requireAuth && !isAuthenticated) {\n      if (showLoginPrompt) {\n        toast.error(loginMessage);\n      }\n      \n      if (onAuthRequired) {\n        onAuthRequired();\n      } else {\n        setIsAuthModalOpen(true);\n      }\n      \n      return false;\n    }\n\n    try {\n      await action();\n      return true;\n    } catch (error) {\n      console.error('خطأ في تنفيذ الإجراء:', error);\n      toast.error('حدث خطأ أثناء تنفيذ العملية');\n      return false;\n    }\n  };\n\n  return {\n    executeAction,\n    executeAsyncAction,\n    isAuthenticated,\n    user,\n    isAuthModalOpen,\n    setIsAuthModalOpen,\n    canExecute: !requireAuth || isAuthenticated\n  };\n}\n\n// Hook مخصص للرسائل\nexport function useMessageAction() {\n  return useAuthAction({\n    requireAuth: true,\n    showLoginPrompt: true,\n    loginMessage: 'يجب تسجيل الدخول لإرسال الرسائل 📨'\n  });\n}\n\n// Hook مخصص للمفضلة\nexport function useFavoriteAction() {\n  return useAuthAction({\n    requireAuth: true,\n    showLoginPrompt: true,\n    loginMessage: 'يجب تسجيل الدخول لإضافة الإعلانات للمفضلة ❤️'\n  });\n}\n\n// Hook مخصص لإضافة الإعلانات\nexport function useAddAdAction() {\n  return useAuthAction({\n    requireAuth: true,\n    showLoginPrompt: true,\n    loginMessage: 'يجب تسجيل الدخول لإضافة إعلان جديد 📝'\n  });\n}\n\n// Hook مخصص للتقييمات\nexport function useRatingAction() {\n  return useAuthAction({\n    requireAuth: true,\n    showLoginPrompt: true,\n    loginMessage: 'يجب تسجيل الدخول لإضافة تقييم ⭐'\n  });\n}\n\n// Hook مخصص للتعليقات\nexport function useCommentAction() {\n  return useAuthAction({\n    requireAuth: true,\n    showLoginPrompt: true,\n    loginMessage: 'يجب تسجيل الدخول لإضافة تعليق 💬'\n  });\n}\n\n// Hook مخصص للمتابعة\nexport function useFollowAction() {\n  return useAuthAction({\n    requireAuth: true,\n    showLoginPrompt: true,\n    loginMessage: 'يجب تسجيل الدخول لمتابعة البائعين 👥'\n  });\n}\n\n// Hook مخصص للإشعارات\nexport function useNotificationAction() {\n  return useAuthAction({\n    requireAuth: true,\n    showLoginPrompt: true,\n    loginMessage: 'يجب تسجيل الدخول لإدارة الإشعارات 🔔'\n  });\n}\n\n// Hook مخصص للدفع والاشتراكات\nexport function usePaymentAction() {\n  return useAuthAction({\n    requireAuth: true,\n    showLoginPrompt: true,\n    loginMessage: 'يجب تسجيل الدخول للوصول لخدمات الدفع 💳'\n  });\n}\n\n// Hook مخصص للبحث المحفوظ\nexport function useSavedSearchAction() {\n  return useAuthAction({\n    requireAuth: true,\n    showLoginPrompt: true,\n    loginMessage: 'يجب تسجيل الدخول لحفظ عمليات البحث 🔍'\n  });\n}\n\n// Hook مخصص للإعدادات\nexport function useSettingsAction() {\n  return useAuthAction({\n    requireAuth: true,\n    showLoginPrompt: true,\n    loginMessage: 'يجب تسجيل الدخول للوصول للإعدادات ⚙️'\n  });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAJA;;;;AAaO,SAAS,cAAc,UAA6B,CAAC,CAAC;IAC3D,MAAM,EACJ,cAAc,IAAI,EAClB,kBAAkB,IAAI,EACtB,eAAe,sCAAsC,EACrD,cAAc,EACf,GAAG;IAEJ,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACxC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,gBAAgB,CAAC;QACrB,IAAI,eAAe,CAAC,iBAAiB;YACnC,2BAA2B;YAC3B,IAAI,iBAAiB;gBACnB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;YAEA,qCAAqC;YACrC,IAAI,gBAAgB;gBAClB;YACF,OAAO;gBACL,8CAA8C;gBAC9C,mBAAmB;YACrB;YAEA,OAAO;QACT;QAEA,4CAA4C;QAC5C,IAAI;YACF;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI,eAAe,CAAC,iBAAiB;YACnC,IAAI,iBAAiB;gBACnB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;YAEA,IAAI,gBAAgB;gBAClB;YACF,OAAO;gBACL,mBAAmB;YACrB;YAEA,OAAO;QACT;QAEA,IAAI;YACF,MAAM;YACN,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,OAAO;QACT;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA,YAAY,CAAC,eAAe;IAC9B;AACF;AAGO,SAAS;IACd,OAAO,cAAc;QACnB,aAAa;QACb,iBAAiB;QACjB,cAAc;IAChB;AACF;AAGO,SAAS;IACd,OAAO,cAAc;QACnB,aAAa;QACb,iBAAiB;QACjB,cAAc;IAChB;AACF;AAGO,SAAS;IACd,OAAO,cAAc;QACnB,aAAa;QACb,iBAAiB;QACjB,cAAc;IAChB;AACF;AAGO,SAAS;IACd,OAAO,cAAc;QACnB,aAAa;QACb,iBAAiB;QACjB,cAAc;IAChB;AACF;AAGO,SAAS;IACd,OAAO,cAAc;QACnB,aAAa;QACb,iBAAiB;QACjB,cAAc;IAChB;AACF;AAGO,SAAS;IACd,OAAO,cAAc;QACnB,aAAa;QACb,iBAAiB;QACjB,cAAc;IAChB;AACF;AAGO,SAAS;IACd,OAAO,cAAc;QACnB,aAAa;QACb,iBAAiB;QACjB,cAAc;IAChB;AACF;AAGO,SAAS;IACd,OAAO,cAAc;QACnB,aAAa;QACb,iBAAiB;QACjB,cAAc;IAChB;AACF;AAGO,SAAS;IACd,OAAO,cAAc;QACnB,aAAa;QACb,iBAAiB;QACjB,cAAc;IAChB;AACF;AAGO,SAAS;IACd,OAAO,cAAc;QACnB,aAAa;QACb,iBAAiB;QACjB,cAAc;IAChB;AACF"}}, {"offset": {"line": 7191, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7197, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/MessageModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useToast } from '@/hooks/useToast';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useMessageAction } from '@/hooks/useAuthAction';\n\ninterface MessageModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  recipientName: string;\n  recipientPhone: string;\n  adTitle: string;\n  adId: number;\n}\n\nconst MessageModal = ({\n  isOpen,\n  onClose,\n  recipientName,\n  recipientPhone,\n  adTitle,\n  adId\n}: MessageModalProps) => {\n  const { showSuccess, showError } = useToast();\n  const { isAuthenticated } = useAuth();\n  const { executeAsyncAction } = useMessageAction();\n  const [message, setMessage] = useState('');\n  const [isSending, setIsSending] = useState(false);\n  const [messageType, setMessageType] = useState<'inquiry' | 'offer' | 'custom'>('inquiry');\n  const [offerAmount, setOfferAmount] = useState('');\n  const [phoneNumber, setPhoneNumber] = useState('');\n  const [includePhone, setIncludePhone] = useState(false);\n\n  // رسائل جاهزة\n  const predefinedMessages = {\n    inquiry: [\n      'مرحباً، أرغب في معرفة المزيد من التفاصيل حول هذا الإعلان.',\n      'هل هذا العرض ما زال متاحاً؟',\n      'يرجى إرسال المزيد من الصور والتفاصيل.',\n      'متى يمكنني المعاينة؟',\n      'ما هي شروط الدفع المتاحة؟'\n    ],\n    offer: [\n      'أرغب في تقديم عرض لشراء هذا العقار/المنتج.',\n      'هل تقبل التفاوض على السعر؟',\n      'أرغب في الشراء نقداً، هل يوجد خصم؟'\n    ],\n    custom: []\n  };\n\n  useEffect(() => {\n    if (isOpen) {\n      setMessage('');\n      setMessageType('inquiry');\n      setOfferAmount('');\n      setPhoneNumber('');\n      setIncludePhone(false);\n    }\n  }, [isOpen]);\n\n  const handleSendMessage = async () => {\n    // التحقق من المصادقة أولاً\n    const success = await executeAsyncAction(async () => {\n      if (!message.trim()) {\n        showError('يرجى كتابة رسالة');\n        return;\n      }\n\n      if (messageType === 'offer' && !offerAmount.trim()) {\n        showError('يرجى إدخال مبلغ العرض');\n        return;\n      }\n\n    setIsSending(true);\n    try {\n      // محاكاة إرسال الرسالة\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      let finalMessage = message;\n      if (messageType === 'offer' && offerAmount) {\n        finalMessage += `\\n\\nالعرض المقترح: ${offerAmount} ل.س`;\n      }\n      if (includePhone && phoneNumber) {\n        finalMessage += `\\n\\nرقم الهاتف: ${phoneNumber}`;\n      }\n\n      console.log('إرسال رسالة:', {\n        to: recipientName,\n        adId,\n        adTitle,\n        message: finalMessage,\n        type: messageType\n      });\n\n      showSuccess('تم إرسال الرسالة بنجاح! 📨');\n      onClose();\n    } catch (error) {\n      showError('حدث خطأ أثناء إرسال الرسالة');\n      } finally {\n        setIsSending(false);\n      }\n    });\n\n    // إذا لم يكن المستخدم مصادق عليه، إغلاق النافذة\n    if (!success) {\n      onClose();\n    }\n  };\n\n  const handleWhatsAppMessage = () => {\n    let finalMessage = message;\n    if (messageType === 'offer' && offerAmount) {\n      finalMessage += `\\n\\nالعرض المقترح: ${offerAmount} ل.س`;\n    }\n    if (includePhone && phoneNumber) {\n      finalMessage += `\\n\\nرقم الهاتف: ${phoneNumber}`;\n    }\n    \n    const whatsappMessage = `مرحباً ${recipientName}،\\n\\nبخصوص إعلان: ${adTitle}\\n\\n${finalMessage}`;\n    const phoneFormatted = recipientPhone.replace(/[^0-9]/g, '');\n    const whatsappUrl = `https://wa.me/${phoneFormatted}?text=${encodeURIComponent(whatsappMessage)}`;\n    window.open(whatsappUrl, '_blank');\n  };\n\n  const selectPredefinedMessage = (msg: string) => {\n    setMessage(msg);\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-t-2xl\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-10 h-10 bg-white/20 rounded-full flex items-center justify-center\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z\"/>\n                </svg>\n              </div>\n              <div>\n                <h3 className=\"text-xl font-bold\">إرسال رسالة</h3>\n                <p className=\"text-blue-100 text-sm\">إلى: {recipientName}</p>\n              </div>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"w-8 h-8 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z\"/>\n              </svg>\n            </button>\n          </div>\n          <div className=\"mt-3 p-3 bg-white/10 rounded-lg\">\n            <p className=\"text-blue-100 text-sm font-medium\">الإعلان: {adTitle}</p>\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6\">\n          {/* Message Type Selection */}\n          <div className=\"mb-6\">\n            <h4 className=\"font-semibold text-gray-800 mb-3\">نوع الرسالة</h4>\n            <div className=\"flex gap-3\">\n              {[\n                { key: 'inquiry', label: 'استفسار', icon: '❓' },\n                { key: 'offer', label: 'عرض سعر', icon: '💰' },\n                { key: 'custom', label: 'رسالة مخصصة', icon: '✏️' }\n              ].map(({ key, label, icon }) => (\n                <button\n                  key={key}\n                  onClick={() => setMessageType(key as any)}\n                  className={`flex-1 p-3 rounded-lg border-2 transition-all duration-300 ${\n                    messageType === key\n                      ? 'border-blue-500 bg-blue-50 text-blue-700'\n                      : 'border-gray-200 hover:border-gray-300'\n                  }`}\n                >\n                  <div className=\"text-center\">\n                    <div\n                      className={`text-2xl mb-1 transition-all duration-300 ${\n                        messageType === key\n                          ? 'opacity-100 filter-none'\n                          : 'opacity-50 grayscale'\n                      }`}\n                      style={{\n                        filter: messageType === key\n                          ? 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.5))'\n                          : 'grayscale(1) opacity(0.5)'\n                      }}\n                    >\n                      {icon}\n                    </div>\n                    <div className=\"text-sm font-medium\">{label}</div>\n                  </div>\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Predefined Messages */}\n          {messageType !== 'custom' && predefinedMessages[messageType].length > 0 && (\n            <div className=\"mb-6\">\n              <h4 className=\"font-semibold text-gray-800 mb-3\">رسائل جاهزة</h4>\n              <div className=\"space-y-2\">\n                {predefinedMessages[messageType].map((msg, index) => (\n                  <button\n                    key={index}\n                    onClick={() => selectPredefinedMessage(msg)}\n                    className=\"w-full text-right p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors text-sm\"\n                  >\n                    {msg}\n                  </button>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Offer Amount */}\n          {messageType === 'offer' && (\n            <div className=\"mb-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                مبلغ العرض (ل.س)\n              </label>\n              <input\n                type=\"number\"\n                value={offerAmount}\n                onChange={(e) => setOfferAmount(e.target.value)}\n                placeholder=\"أدخل مبلغ العرض\"\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n          )}\n\n          {/* Message Input */}\n          <div className=\"mb-6\">\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              نص الرسالة\n            </label>\n            <textarea\n              value={message}\n              onChange={(e) => setMessage(e.target.value)}\n              placeholder=\"اكتب رسالتك هنا...\"\n              rows={5}\n              className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none\"\n            />\n          </div>\n\n          {/* Phone Number Option */}\n          <div className=\"mb-6\">\n            <div className=\"flex items-center gap-3 mb-3\">\n              <input\n                type=\"checkbox\"\n                id=\"includePhone\"\n                checked={includePhone}\n                onChange={(e) => setIncludePhone(e.target.checked)}\n                className=\"w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\n              />\n              <label htmlFor=\"includePhone\" className=\"text-sm font-medium text-gray-700\">\n                إضافة رقم الهاتف للرسالة\n              </label>\n            </div>\n            {includePhone && (\n              <input\n                type=\"tel\"\n                value={phoneNumber}\n                onChange={(e) => setPhoneNumber(e.target.value)}\n                placeholder=\"رقم الهاتف\"\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            )}\n          </div>\n\n          {/* Action Buttons */}\n          <div className=\"flex gap-3 pt-4\">\n            <button\n              onClick={handleSendMessage}\n              disabled={isSending}\n              className=\"flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2\"\n            >\n              {isSending ? (\n                <>\n                  <div className=\"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin\"></div>\n                  جاري الإرسال...\n                </>\n              ) : (\n                <>\n                  <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M2.01 21L23 12 2.01 3 2 10l15 2-15 2z\"/>\n                  </svg>\n                  إرسال الرسالة\n                </>\n              )}\n            </button>\n            \n            <button\n              onClick={handleWhatsAppMessage}\n              className=\"px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.785\"/>\n              </svg>\n              واتساب\n            </button>\n            \n            <button\n              onClick={onClose}\n              className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n            >\n              إلغاء\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default MessageModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAgBA,MAAM,eAAe,CAAC,EACpB,MAAM,EACN,OAAO,EACP,aAAa,EACb,cAAc,EACd,OAAO,EACP,IAAI,EACc;IAClB,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IAC1C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAClC,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,mBAAgB,AAAD;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkC;IAC/E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,cAAc;IACd,MAAM,qBAAqB;QACzB,SAAS;YACP;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;YACL;YACA;YACA;SACD;QACD,QAAQ,EAAE;IACZ;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,WAAW;YACX,eAAe;YACf,eAAe;YACf,eAAe;YACf,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,oBAAoB;QACxB,2BAA2B;QAC3B,MAAM,UAAU,MAAM,mBAAmB;YACvC,IAAI,CAAC,QAAQ,IAAI,IAAI;gBACnB,UAAU;gBACV;YACF;YAEA,IAAI,gBAAgB,WAAW,CAAC,YAAY,IAAI,IAAI;gBAClD,UAAU;gBACV;YACF;YAEF,aAAa;YACb,IAAI;gBACF,uBAAuB;gBACvB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,IAAI,eAAe;gBACnB,IAAI,gBAAgB,WAAW,aAAa;oBAC1C,gBAAgB,CAAC,mBAAmB,EAAE,YAAY,IAAI,CAAC;gBACzD;gBACA,IAAI,gBAAgB,aAAa;oBAC/B,gBAAgB,CAAC,gBAAgB,EAAE,aAAa;gBAClD;gBAEA,QAAQ,GAAG,CAAC,gBAAgB;oBAC1B,IAAI;oBACJ;oBACA;oBACA,SAAS;oBACT,MAAM;gBACR;gBAEA,YAAY;gBACZ;YACF,EAAE,OAAO,OAAO;gBACd,UAAU;YACV,SAAU;gBACR,aAAa;YACf;QACF;QAEA,gDAAgD;QAChD,IAAI,CAAC,SAAS;YACZ;QACF;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,eAAe;QACnB,IAAI,gBAAgB,WAAW,aAAa;YAC1C,gBAAgB,CAAC,mBAAmB,EAAE,YAAY,IAAI,CAAC;QACzD;QACA,IAAI,gBAAgB,aAAa;YAC/B,gBAAgB,CAAC,gBAAgB,EAAE,aAAa;QAClD;QAEA,MAAM,kBAAkB,CAAC,OAAO,EAAE,cAAc,kBAAkB,EAAE,QAAQ,IAAI,EAAE,cAAc;QAChG,MAAM,iBAAiB,eAAe,OAAO,CAAC,WAAW;QACzD,MAAM,cAAc,CAAC,cAAc,EAAE,eAAe,MAAM,EAAE,mBAAmB,kBAAkB;QACjG,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA,MAAM,0BAA0B,CAAC;QAC/B,WAAW;IACb;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAE,WAAU;;wDAAwB;wDAAM;;;;;;;;;;;;;;;;;;;8CAG/C,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAe,SAAQ;kDACnD,cAAA,8OAAC;4CAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAoC;oCAAU;;;;;;;;;;;;;;;;;;8BAK/D,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,KAAK;4CAAW,OAAO;4CAAW,MAAM;wCAAI;wCAC9C;4CAAE,KAAK;4CAAS,OAAO;4CAAW,MAAM;wCAAK;wCAC7C;4CAAE,KAAK;4CAAU,OAAO;4CAAe,MAAM;wCAAK;qCACnD,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,iBACzB,8OAAC;4CAEC,SAAS,IAAM,eAAe;4CAC9B,WAAW,CAAC,2DAA2D,EACrE,gBAAgB,MACZ,6CACA,yCACJ;sDAEF,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,WAAW,CAAC,0CAA0C,EACpD,gBAAgB,MACZ,4BACA,wBACJ;wDACF,OAAO;4DACL,QAAQ,gBAAgB,MACpB,iDACA;wDACN;kEAEC;;;;;;kEAEH,8OAAC;wDAAI,WAAU;kEAAuB;;;;;;;;;;;;2CAvBnC;;;;;;;;;;;;;;;;wBA+BZ,gBAAgB,YAAY,kBAAkB,CAAC,YAAY,CAAC,MAAM,GAAG,mBACpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAI,WAAU;8CACZ,kBAAkB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,sBACzC,8OAAC;4CAEC,SAAS,IAAM,wBAAwB;4CACvC,WAAU;sDAET;2CAJI;;;;;;;;;;;;;;;;wBAYd,gBAAgB,yBACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC1C,aAAY;oCACZ,MAAM;oCACN,WAAU;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,SAAS;4CACT,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;4CACjD,WAAU;;;;;;sDAEZ,8OAAC;4CAAM,SAAQ;4CAAe,WAAU;sDAAoC;;;;;;;;;;;;gCAI7E,8BACC,8OAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,0BACC;;0DACE,8OAAC;gDAAI,WAAU;;;;;;4CAAkF;;qEAInG;;0DACE,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;4CACJ;;;;;;;;8CAMZ,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;wCACJ;;;;;;;8CAIR,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe"}}, {"offset": {"line": 7753, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7768, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/LocationFilter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\n\nconst syrianGovernorates = [\n  {\n    id: 'damascus',\n    name: 'دمشق',\n    arabicName: 'دمشق',\n    adsCount: 15420\n  },\n  {\n    id: 'damascus-countryside',\n    name: 'ريف دمشق',\n    arabicName: 'ريف دمشق',\n    adsCount: 8930\n  },\n  {\n    id: 'aleppo',\n    name: 'حلب',\n    arabicName: 'حلب',\n    adsCount: 12650\n  },\n  {\n    id: 'homs',\n    name: 'حمص',\n    arabicName: 'حمص',\n    adsCount: 6780\n  },\n  {\n    id: 'hama',\n    name: 'حماة',\n    arabicName: 'حماة',\n    adsCount: 4520\n  },\n  {\n    id: 'lattakia',\n    name: 'اللاذقية',\n    arabicName: 'اللاذقية',\n    adsCount: 5680\n  },\n  {\n    id: 'tartous',\n    name: 'طرطوس',\n    arabicName: 'طرطوس',\n    adsCount: 3240\n  },\n  {\n    id: 'idlib',\n    name: 'إدل<PERSON>',\n    arabicName: 'إدلب',\n    adsCount: 2150\n  },\n  {\n    id: 'daraa',\n    name: 'درعا',\n    arabicName: 'درعا',\n    adsCount: 2890\n  },\n  {\n    id: 'sweida',\n    name: 'السويداء',\n    arabicName: 'السويداء',\n    adsCount: 1980\n  },\n  {\n    id: 'quneitra',\n    name: 'القنيطرة',\n    arabicName: 'القنيطرة',\n    adsCount: 890\n  },\n  {\n    id: 'deir-ez-zor',\n    name: 'دير الزور',\n    arabicName: 'دير الزور',\n    adsCount: 3450\n  },\n  {\n    id: 'raqqa',\n    name: 'الرقة',\n    arabicName: 'الرقة',\n    adsCount: 2760\n  },\n  {\n    id: 'hasaka',\n    name: 'الحسكة',\n    arabicName: 'الحسكة',\n    adsCount: 3120\n  }\n];\n\nconst LocationFilter = () => {\n  const [pressedGovernorate, setPressedGovernorate] = useState<string | null>(null);\n\n  return (\n    <section className=\"py-12 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-10\">\n          <h2 className=\"text-3xl font-bold text-gray-800 mb-4\">تصفح حسب المحافظة</h2>\n          <p className=\"text-gray-600 text-lg\">اختر محافظتك للعثور على الإعلانات القريبة منك</p>\n        </div>\n\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-4\">\n          {syrianGovernorates.map((governorate) => (\n            <Link\n              key={governorate.id}\n              href={`/location/${governorate.id}`}\n              className={`bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-primary-300 overflow-hidden group cursor-pointer transform hover:scale-105 ${\n                pressedGovernorate === governorate.id\n                  ? 'shadow-[0_0_20px_rgba(59,130,246,0.6)] border-primary-400 scale-105'\n                  : ''\n              }`}\n              onMouseDown={() => setPressedGovernorate(governorate.id)}\n              onMouseUp={() => setPressedGovernorate(null)}\n              onMouseLeave={() => setPressedGovernorate(null)}\n              onTouchStart={() => setPressedGovernorate(governorate.id)}\n              onTouchEnd={() => setPressedGovernorate(null)}\n            >\n              <div className=\"p-4 text-center\">\n                <h3 className=\"text-lg font-semibold text-gray-800 group-hover:text-primary-600 transition-colors mb-2\">\n                  {governorate.arabicName}\n                </h3>\n                <span className=\"text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full\">\n                  {governorate.adsCount.toLocaleString()}\n                </span>\n              </div>\n            </Link>\n          ))}\n        </div>\n\n\n\n        {/* Quick Stats */}\n        <div className=\"mt-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl p-8 text-white\">\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 text-center\">\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">\n                {syrianGovernorates.reduce((sum, gov) => sum + gov.adsCount, 0).toLocaleString()}\n              </div>\n              <div className=\"text-primary-100\">إجمالي الإعلانات</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">{syrianGovernorates.length}</div>\n              <div className=\"text-primary-100\">محافظة مغطاة</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">50,000+</div>\n              <div className=\"text-primary-100\">مستخدم نشط</div>\n            </div>\n            <div>\n              <div className=\"text-3xl font-bold mb-2\">24/7</div>\n              <div className=\"text-primary-100\">خدمة مستمرة</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default LocationFilter;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,qBAAqB;IACzB;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,YAAY;QACZ,UAAU;IACZ;CACD;AAED,MAAM,iBAAiB;IACrB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE5E,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAGvC,8OAAC;oBAAI,WAAU;8BACZ,mBAAmB,GAAG,CAAC,CAAC,4BACvB,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE;4BACnC,WAAW,CAAC,yLAAyL,EACnM,uBAAuB,YAAY,EAAE,GACjC,wEACA,IACJ;4BACF,aAAa,IAAM,sBAAsB,YAAY,EAAE;4BACvD,WAAW,IAAM,sBAAsB;4BACvC,cAAc,IAAM,sBAAsB;4BAC1C,cAAc,IAAM,sBAAsB,YAAY,EAAE;4BACxD,YAAY,IAAM,sBAAsB;sCAExC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,YAAY,UAAU;;;;;;kDAEzB,8OAAC;wCAAK,WAAU;kDACb,YAAY,QAAQ,CAAC,cAAc;;;;;;;;;;;;2BAlBnC,YAAY,EAAE;;;;;;;;;;8BA4BzB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDACZ,mBAAmB,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,IAAI,QAAQ,EAAE,GAAG,cAAc;;;;;;kDAEhF,8OAAC;wCAAI,WAAU;kDAAmB;;;;;;;;;;;;0CAEpC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA2B,mBAAmB,MAAM;;;;;;kDACnE,8OAAC;wCAAI,WAAU;kDAAmB;;;;;;;;;;;;0CAEpC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDAAmB;;;;;;;;;;;;0CAEpC,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;kDAA0B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOhD;uCAEe"}}, {"offset": {"line": 8066, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8072, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/LatestAds.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport SafeNavigationButton from './SafeNavigationButton';\nimport { AdBadge } from '@/components/VerificationBadge';\nimport { determineUserBadge } from '@/lib/verification';\n\nconst latestAds = [\n  {\n    id: 101,\n    title: 'محل تجاري للإيجار في دمشق',\n    price: '300,000',\n    currency: 'ل.س',\n    location: 'دمشق - الشعلان',\n    category: 'عقارات',\n    postedDate: 'منذ 15 دقيقة',\n    isNew: true,\n    seller: {\n      name: 'شركة العقارات الذهبية',\n      subscription: 'business-enterprise',\n      adsCount: 180,\n      rating: 4.9,\n      membershipMonths: 20,\n      isBusinessVerified: true\n    }\n  },\n  {\n    id: 102,\n    title: 'Toyota Camry 2019 نظيفة جداً',\n    price: '28,000',\n    currency: '$',\n    location: 'حلب - الحمدانية',\n    category: 'سيارات',\n    postedDate: 'منذ 30 دقيقة',\n    isNew: true,\n    seller: {\n      name: 'أحمد الخليل',\n      subscription: 'premium',\n      adsCount: 12,\n      rating: 4.4,\n      membershipMonths: 6,\n      isBusinessVerified: false\n    }\n  },\n  {\n    id: 103,\n    title: 'مدرس رياضيات خصوصي',\n    price: '15,000',\n    currency: 'ل.س',\n    location: 'دمشق - المزة',\n    category: 'خدمات',\n    postedDate: 'منذ 45 دقيقة',\n    isNew: true,\n    seller: {\n      name: 'محمد الأستاذ',\n      subscription: 'basic',\n      adsCount: 5,\n      rating: 4.3,\n      membershipMonths: 2,\n      isBusinessVerified: false\n    }\n  },\n  {\n    id: 104,\n    title: 'Samsung Galaxy S24 Ultra',\n    price: '950',\n    currency: '$',\n    location: 'حمص - الوعر',\n    category: 'إلكترونيات',\n    postedDate: 'منذ ساعة',\n    isNew: true,\n    seller: {\n      name: 'متجر التقنية الحديثة',\n      subscription: 'business-professional',\n      adsCount: 95,\n      rating: 4.7,\n      membershipMonths: 14,\n      isBusinessVerified: true\n    }\n  },\n  {\n    id: 105,\n    title: 'غرفة نوم تركية جديدة',\n    price: '450,000',\n    currency: 'ل.س',\n    location: 'اللاذقية - الزراعة',\n    category: 'أثاث',\n    postedDate: 'منذ ساعة ونصف',\n    isNew: false,\n    seller: {\n      name: 'معرض الأثاث التركي',\n      subscription: 'business-starter',\n      adsCount: 35,\n      rating: 4.5,\n      membershipMonths: 8,\n      isBusinessVerified: true\n    }\n  },\n  {\n    id: 106,\n    title: 'مطلوب سكرتيرة طبية',\n    price: '400,000',\n    currency: 'ل.س',\n    location: 'دمشق - أبو رمانة',\n    category: 'وظائف',\n    postedDate: 'منذ ساعتين',\n    isNew: false,\n    seller: {\n      name: 'عيادة الدكتور أحمد',\n      subscription: 'premium',\n      adsCount: 8,\n      rating: 4.6,\n      membershipMonths: 4,\n      isBusinessVerified: false\n    }\n  },\n  {\n    id: 107,\n    title: 'دراجة هوائية جبلية',\n    price: '180,000',\n    currency: 'ل.س',\n    location: 'حماة - الحميدية',\n    category: 'رياضة',\n    postedDate: 'منذ ساعتين',\n    isNew: false,\n    seller: {\n      name: 'سارة محمد',\n      subscription: 'basic',\n      adsCount: 3,\n      rating: 4.1,\n      membershipMonths: 1,\n      isBusinessVerified: false\n    }\n  },\n  {\n    id: 108,\n    title: 'كتب جامعية - كلية الطب',\n    price: '120,000',\n    currency: 'ل.س',\n    location: 'دمشق - المزة',\n    category: 'كتب',\n    postedDate: 'منذ 3 ساعات',\n    isNew: false,\n    seller: {\n      name: 'طالب طب',\n      subscription: 'basic',\n      adsCount: 2,\n      rating: 4.0,\n      membershipMonths: 0.5,\n      isBusinessVerified: false\n    }\n  }\n];\n\nconst LatestAds = () => {\n\n  return (\n    <section className=\"py-12 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between mb-8\">\n          <div>\n            <h2 className=\"text-3xl font-bold text-gray-800 mb-2\">أحدث الإعلانات</h2>\n            <p className=\"text-gray-600\">آخر الإعلانات المضافة إلى الموقع</p>\n          </div>\n          <Link\n            href=\"/latest\"\n            className=\"text-primary-600 hover:text-primary-700 font-medium flex items-center gap-2\"\n          >\n            عرض الكل\n            <span>←</span>\n          </Link>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          {latestAds.map((ad) => (\n            <Link\n              key={ad.id}\n              href={`/ad/${ad.id}`}\n              className=\"bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 p-4 border border-gray-100 hover:border-primary-200 group\"\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center gap-2 mb-2\">\n                    <h3 className=\"font-semibold text-gray-800 group-hover:text-primary-600 transition-colors line-clamp-1\">\n                      {ad.title}\n                    </h3>\n                    {ad.isNew && (\n                      <span className=\"bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium\">\n                        جديد\n                      </span>\n                    )}\n                  </div>\n\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <div className=\"text-lg font-bold text-primary-600\">\n                      {ad.price.toLocaleString()} {ad.currency}\n                    </div>\n                    <span className=\"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full\">\n                      {ad.category}\n                    </span>\n                  </div>\n\n                  <div className=\"flex items-center justify-between text-sm text-gray-500 mb-2\">\n                    <div className=\"flex items-center gap-1\">\n                      <span>📍</span>\n                      <span>{ad.location}</span>\n                    </div>\n                    <span>{ad.postedDate}</span>\n                  </div>\n\n                  {/* معلومات البائع مع شارة التوثيق */}\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-2\">\n                      <span className=\"text-xs text-gray-500\">بواسطة:</span>\n                      <span className=\"text-xs font-medium text-gray-700\">{ad.seller.name}</span>\n                    </div>\n                    <AdBadge\n                      userBadges={determineUserBadge(\n                        ad.seller.subscription,\n                        ad.seller.adsCount,\n                        ad.seller.rating,\n                        ad.seller.membershipMonths,\n                        ad.seller.isBusinessVerified\n                      )}\n                      size=\"xs\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"mr-4 flex-shrink-0\">\n                  <div className=\"w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center\">\n                    <span className=\"text-gray-400 text-2xl\">🖼️</span>\n                  </div>\n                </div>\n              </div>\n            </Link>\n          ))}\n        </div>\n\n        {/* Live Updates Banner */}\n        <div className=\"mt-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-3 h-3 bg-white rounded-full animate-pulse\"></div>\n              <div>\n                <h3 className=\"font-semibold text-lg\">تحديثات مباشرة</h3>\n                <p className=\"text-green-100\">يتم إضافة إعلانات جديدة كل دقيقة</p>\n              </div>\n            </div>\n            <Link\n              href=\"/live-updates\"\n              className=\"bg-white text-green-600 px-4 py-2 rounded-lg font-medium hover:bg-green-50 transition-colors\"\n            >\n              مشاهدة مباشرة\n            </Link>\n          </div>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"mt-8 grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <SafeNavigationButton\n            href=\"/add-ad\"\n            className=\"bg-orange-500 text-white p-6 rounded-lg hover:bg-orange-600 transition-colors text-center group\"\n          >\n            <div className=\"text-3xl mb-2\">\n              <svg className=\"w-8 h-8 mx-auto\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z\" fillOpacity=\"0.8\"/>\n                <path d=\"M15,13H13V11H11V13H9V15H11V17H13V15H15V13Z\" fillOpacity=\"1\"/>\n              </svg>\n            </div>\n            <h3 className=\"font-semibold text-lg mb-1\">أضف إعلانك</h3>\n            <p className=\"text-orange-100 text-sm\">انشر إعلانك بسهولة</p>\n          </SafeNavigationButton>\n\n          <Link\n            href=\"/search\"\n            className=\"bg-blue-500 text-white p-6 rounded-lg hover:bg-blue-600 transition-colors text-center group\"\n          >\n            <div className=\"text-3xl mb-2\">\n              <svg className=\"w-8 h-8 mx-auto\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z\" fillOpacity=\"0.8\"/>\n              </svg>\n            </div>\n            <h3 className=\"font-semibold text-lg mb-1\">بحث متقدم</h3>\n            <p className=\"text-blue-100 text-sm\">ابحث بدقة أكبر</p>\n          </Link>\n\n          <Link\n            href=\"/alerts\"\n            className=\"bg-purple-500 text-white p-6 rounded-lg hover:bg-purple-600 transition-colors text-center group\"\n          >\n            <div className=\"text-3xl mb-2\">🔔</div>\n            <h3 className=\"font-semibold text-lg mb-1\">تنبيهات</h3>\n            <p className=\"text-purple-100 text-sm\">احصل على تنبيهات</p>\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default LatestAds;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,YAAY;IAChB;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,OAAO;QACP,QAAQ;YACN,MAAM;YACN,cAAc;YACd,UAAU;YACV,QAAQ;YACR,kBAAkB;YAClB,oBAAoB;QACtB;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,OAAO;QACP,QAAQ;YACN,MAAM;YACN,cAAc;YACd,UAAU;YACV,QAAQ;YACR,kBAAkB;YAClB,oBAAoB;QACtB;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,OAAO;QACP,QAAQ;YACN,MAAM;YACN,cAAc;YACd,UAAU;YACV,QAAQ;YACR,kBAAkB;YAClB,oBAAoB;QACtB;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,OAAO;QACP,QAAQ;YACN,MAAM;YACN,cAAc;YACd,UAAU;YACV,QAAQ;YACR,kBAAkB;YAClB,oBAAoB;QACtB;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,OAAO;QACP,QAAQ;YACN,MAAM;YACN,cAAc;YACd,UAAU;YACV,QAAQ;YACR,kBAAkB;YAClB,oBAAoB;QACtB;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,OAAO;QACP,QAAQ;YACN,MAAM;YACN,cAAc;YACd,UAAU;YACV,QAAQ;YACR,kBAAkB;YAClB,oBAAoB;QACtB;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,OAAO;QACP,QAAQ;YACN,MAAM;YACN,cAAc;YACd,UAAU;YACV,QAAQ;YACR,kBAAkB;YAClB,oBAAoB;QACtB;IACF;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,OAAO;QACP,QAAQ;YACN,MAAM;YACN,cAAc;YACd,UAAU;YACV,QAAQ;YACR,kBAAkB;YAClB,oBAAoB;QACtB;IACF;CACD;AAED,MAAM,YAAY;IAEhB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAE/B,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;gCACX;8CAEC,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;8BAIV,8OAAC;oBAAI,WAAU;8BACZ,UAAU,GAAG,CAAC,CAAC,mBACd,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE,EAAE;4BACpB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,GAAG,KAAK;;;;;;oDAEV,GAAG,KAAK,kBACP,8OAAC;wDAAK,WAAU;kEAAqE;;;;;;;;;;;;0DAMzF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,GAAG,KAAK,CAAC,cAAc;4DAAG;4DAAE,GAAG,QAAQ;;;;;;;kEAE1C,8OAAC;wDAAK,WAAU;kEACb,GAAG,QAAQ;;;;;;;;;;;;0DAIhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;0EAAM,GAAG,QAAQ;;;;;;;;;;;;kEAEpB,8OAAC;kEAAM,GAAG,UAAU;;;;;;;;;;;;0DAItB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,8OAAC;gEAAK,WAAU;0EAAqC,GAAG,MAAM,CAAC,IAAI;;;;;;;;;;;;kEAErE,8OAAC,uIAAA,CAAA,UAAO;wDACN,YAAY,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAC3B,GAAG,MAAM,CAAC,YAAY,EACtB,GAAG,MAAM,CAAC,QAAQ,EAClB,GAAG,MAAM,CAAC,MAAM,EAChB,GAAG,MAAM,CAAC,gBAAgB,EAC1B,GAAG,MAAM,CAAC,kBAAkB;wDAE9B,MAAK;;;;;;;;;;;;;;;;;;kDAKX,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;;;;;2BAvD1C,GAAG,EAAE;;;;;;;;;;8BAgEhB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAwB;;;;;;0DACtC,8OAAC;gDAAE,WAAU;0DAAiB;;;;;;;;;;;;;;;;;;0CAGlC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAOL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0IAAA,CAAA,UAAoB;4BACnB,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAkB,MAAK;wCAAe,SAAQ;;0DAC3D,8OAAC;gDAAK,GAAE;gDAA0F,aAAY;;;;;;0DAC9G,8OAAC;gDAAK,GAAE;gDAA6C,aAAY;;;;;;;;;;;;;;;;;8CAGrE,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAA0B;;;;;;;;;;;;sCAGzC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAkB,MAAK;wCAAe,SAAQ;kDAC3D,cAAA,8OAAC;4CAAK,GAAE;4CAAoQ,aAAY;;;;;;;;;;;;;;;;8CAG5R,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnD;uCAEe"}}, {"offset": {"line": 8710, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8716, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/SiteStats.tsx"], "sourcesContent": ["import StarRating from './StarRating';\n\nconst SiteStats = () => {\n  const stats = [\n    {\n      id: 1,\n      title: 'إجمالي الإعلانات',\n      value: '125,430',\n      icon: '📝',\n      color: 'bg-blue-100/60 border border-blue-200/50',\n      iconColor: '#3b82f6',\n      change: '+12%',\n      changeType: 'increase'\n    },\n    {\n      id: 2,\n      title: 'المستخدمين النشطين',\n      value: '45,280',\n      icon: '👥',\n      color: 'bg-green-100/60 border border-green-200/50',\n      iconColor: '#10b981',\n      change: '+8%',\n      changeType: 'increase'\n    },\n    {\n      id: 3,\n      title: 'الإعلانات المميزة',\n      value: '8,920',\n      icon: '💎',\n      color: 'bg-yellow-100/60 border border-yellow-200/50',\n      iconColor: '#f59e0b',\n      change: '+15%',\n      changeType: 'increase'\n    },\n    {\n      id: 4,\n      title: 'المعاملات اليومية',\n      value: '2,340',\n      icon: '💰',\n      color: 'bg-purple-100/60 border border-purple-200/50',\n      iconColor: '#8b5cf6',\n      change: '+5%',\n      changeType: 'increase'\n    }\n  ];\n\n  const categoryStats = [\n    { name: 'العقارات', count: 45230, percentage: 36 },\n    { name: 'السيارات', count: 28450, percentage: 23 },\n    { name: 'الإلكترونيات', count: 22180, percentage: 18 },\n    { name: 'الوظائف', count: 15670, percentage: 12 },\n    { name: 'الخدمات', count: 8920, percentage: 7 },\n    { name: 'أخرى', count: 4980, percentage: 4 }\n  ];\n\n  const locationStats = [\n    { name: 'دمشق', count: 38450, percentage: 31 },\n    { name: 'حلب', count: 28230, percentage: 22 },\n    { name: 'حمص', count: 18670, percentage: 15 },\n    { name: 'حماة', count: 12340, percentage: 10 },\n    { name: 'اللاذقية', count: 15890, percentage: 13 },\n    { name: 'أخرى', count: 11850, percentage: 9 }\n  ];\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-800 mb-4\">إحصائيات الموقع</h2>\n          <p className=\"text-lg text-gray-600\">أرقام حقيقية تعكس نشاط منصتنا</p>\n        </div>\n\n        {/* الإحصائيات الرئيسية */}\n        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4 mb-8 md:mb-12\">\n          {stats.map((stat) => (\n            <div key={stat.id} className=\"bg-white rounded-lg shadow-md p-3 md:p-4 hover:shadow-lg transition-shadow\">\n              <div className=\"flex items-center justify-between mb-2 md:mb-3\">\n                <div className={`w-7 h-7 md:w-8 md:h-8 ${stat.color} rounded-lg flex items-center justify-center text-sm backdrop-blur-sm`}>\n                  <span style={{ color: stat.iconColor, fontSize: '0.875rem' }}>{stat.icon}</span>\n                </div>\n                <span className={`text-xs font-medium px-1.5 py-0.5 md:px-2 md:py-1 rounded-full ${\n                  stat.changeType === 'increase'\n                    ? 'bg-green-100 text-green-800'\n                    : 'bg-red-100 text-red-800'\n                }`}>\n                  {stat.change}\n                </span>\n              </div>\n              <div className=\"text-lg md:text-2xl font-bold text-gray-800 mb-1\">{stat.value}</div>\n              <div className=\"text-gray-600 text-xs md:text-sm leading-tight\">{stat.title}</div>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* إحصائيات التصنيفات */}\n          <div className=\"bg-white rounded-xl shadow-lg p-6\">\n            <h3 className=\"text-xl font-bold text-gray-800 mb-6\">الإعلانات حسب التصنيف</h3>\n            <div className=\"space-y-4\">\n              {categoryStats.map((category, index) => (\n                <div key={index} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-4 h-4 bg-primary-500 rounded-full\"></div>\n                    <span className=\"font-medium text-gray-800\">{category.name}</span>\n                  </div>\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-32 bg-gray-200 rounded-full h-2\">\n                      <div \n                        className=\"bg-primary-500 h-2 rounded-full transition-all duration-500\"\n                        style={{ width: `${category.percentage}%` }}\n                      ></div>\n                    </div>\n                    <span className=\"text-sm text-gray-600 w-16 text-left\">\n                      {category.count.toLocaleString()}\n                    </span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          {/* إحصائيات المحافظات */}\n          <div className=\"bg-white rounded-xl shadow-lg p-6\">\n            <h3 className=\"text-xl font-bold text-gray-800 mb-6\">الإعلانات حسب المحافظة</h3>\n            <div className=\"space-y-4\">\n              {locationStats.map((location, index) => (\n                <div key={index} className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-4 h-4 bg-blue-500 rounded-full\"></div>\n                    <span className=\"font-medium text-gray-800\">{location.name}</span>\n                  </div>\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-32 bg-gray-200 rounded-full h-2\">\n                      <div \n                        className=\"bg-blue-500 h-2 rounded-full transition-all duration-500\"\n                        style={{ width: `${location.percentage}%` }}\n                      ></div>\n                    </div>\n                    <span className=\"text-sm text-gray-600 w-16 text-left\">\n                      {location.count.toLocaleString()}\n                    </span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n\n\n\n\n        {/* شهادات المستخدمين */}\n        <div className=\"mt-12\">\n          <h3 className=\"text-2xl font-bold text-gray-800 text-center mb-8\">ماذا يقول مستخدمونا</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            {[\n              {\n                name: 'أحمد محمد',\n                role: 'مستخدم فردي',\n                comment: 'منصة رائعة وسهلة الاستخدام، تمكنت من بيع سيارتي خلال أسبوع واحد!',\n                rating: 5\n              },\n              {\n                name: 'شركة العقارات الذهبية',\n                role: 'شركة عقارات',\n                comment: 'أفضل منصة للإعلانات العقارية في سوريا، عملاؤنا يجدوننا بسهولة.',\n                rating: 5\n              },\n              {\n                name: 'سارة أحمد',\n                role: 'مستخدمة فردية',\n                comment: 'وجدت الوظيفة المناسبة من خلال الموقع، خدمة ممتازة ودعم فني رائع.',\n                rating: 5\n              }\n            ].map((testimonial, index) => (\n              <div key={index} className=\"bg-white rounded-xl shadow-lg p-6\">\n                <div className=\"flex items-center mb-4\">\n                  <StarRating rating={testimonial.rating} size=\"sm\" showValue={false} />\n                </div>\n                <p className=\"text-gray-700 mb-4 italic\">\"{testimonial.comment}\"</p>\n                <div>\n                  <div className=\"font-semibold text-gray-800\">{testimonial.name}</div>\n                  <div className=\"text-sm text-gray-600\">{testimonial.role}</div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default SiteStats;\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,YAAY;IAChB,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,MAAM;YACN,OAAO;YACP,WAAW;YACX,QAAQ;YACR,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,MAAM;YACN,OAAO;YACP,WAAW;YACX,QAAQ;YACR,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,MAAM;YACN,OAAO;YACP,WAAW;YACX,QAAQ;YACR,YAAY;QACd;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,MAAM;YACN,OAAO;YACP,WAAW;YACX,QAAQ;YACR,YAAY;QACd;KACD;IAED,MAAM,gBAAgB;QACpB;YAAE,MAAM;YAAY,OAAO;YAAO,YAAY;QAAG;QACjD;YAAE,MAAM;YAAY,OAAO;YAAO,YAAY;QAAG;QACjD;YAAE,MAAM;YAAgB,OAAO;YAAO,YAAY;QAAG;QACrD;YAAE,MAAM;YAAW,OAAO;YAAO,YAAY;QAAG;QAChD;YAAE,MAAM;YAAW,OAAO;YAAM,YAAY;QAAE;QAC9C;YAAE,MAAM;YAAQ,OAAO;YAAM,YAAY;QAAE;KAC5C;IAED,MAAM,gBAAgB;QACpB;YAAE,MAAM;YAAQ,OAAO;YAAO,YAAY;QAAG;QAC7C;YAAE,MAAM;YAAO,OAAO;YAAO,YAAY;QAAG;QAC5C;YAAE,MAAM;YAAO,OAAO;YAAO,YAAY;QAAG;QAC5C;YAAE,MAAM;YAAQ,OAAO;YAAO,YAAY;QAAG;QAC7C;YAAE,MAAM;YAAY,OAAO;YAAO,YAAY;QAAG;QACjD;YAAE,MAAM;YAAQ,OAAO;YAAO,YAAY;QAAE;KAC7C;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAIvC,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4BAAkB,WAAU;;8CAC3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,sBAAsB,EAAE,KAAK,KAAK,CAAC,qEAAqE,CAAC;sDACxH,cAAA,8OAAC;gDAAK,OAAO;oDAAE,OAAO,KAAK,SAAS;oDAAE,UAAU;gDAAW;0DAAI,KAAK,IAAI;;;;;;;;;;;sDAE1E,8OAAC;4CAAK,WAAW,CAAC,+DAA+D,EAC/E,KAAK,UAAU,KAAK,aAChB,gCACA,2BACJ;sDACC,KAAK,MAAM;;;;;;;;;;;;8CAGhB,8OAAC;oCAAI,WAAU;8CAAoD,KAAK,KAAK;;;;;;8CAC7E,8OAAC;oCAAI,WAAU;8CAAkD,KAAK,KAAK;;;;;;;2BAdnE,KAAK,EAAE;;;;;;;;;;8BAmBrB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,8OAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,UAAU,sBAC5B,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAA6B,SAAS,IAAI;;;;;;;;;;;;8DAE5D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,SAAS,UAAU,CAAC,CAAC,CAAC;gEAAC;;;;;;;;;;;sEAG9C,8OAAC;4DAAK,WAAU;sEACb,SAAS,KAAK,CAAC,cAAc;;;;;;;;;;;;;2CAb1B;;;;;;;;;;;;;;;;sCAsBhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,8OAAC;oCAAI,WAAU;8CACZ,cAAc,GAAG,CAAC,CAAC,UAAU,sBAC5B,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAA6B,SAAS,IAAI;;;;;;;;;;;;8DAE5D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,SAAS,UAAU,CAAC,CAAC,CAAC;gEAAC;;;;;;;;;;;sEAG9C,8OAAC;4DAAK,WAAU;sEACb,SAAS,KAAK,CAAC,cAAc;;;;;;;;;;;;;2CAb1B;;;;;;;;;;;;;;;;;;;;;;8BA2BlB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoD;;;;;;sCAClE,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,MAAM;oCACN,MAAM;oCACN,SAAS;oCACT,QAAQ;gCACV;gCACA;oCACE,MAAM;oCACN,MAAM;oCACN,SAAS;oCACT,QAAQ;gCACV;gCACA;oCACE,MAAM;oCACN,MAAM;oCACN,SAAS;oCACT,QAAQ;gCACV;6BACD,CAAC,GAAG,CAAC,CAAC,aAAa,sBAClB,8OAAC;oCAAgB,WAAU;;sDACzB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gIAAA,CAAA,UAAU;gDAAC,QAAQ,YAAY,MAAM;gDAAE,MAAK;gDAAK,WAAW;;;;;;;;;;;sDAE/D,8OAAC;4CAAE,WAAU;;gDAA4B;gDAAE,YAAY,OAAO;gDAAC;;;;;;;sDAC/D,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAA+B,YAAY,IAAI;;;;;;8DAC9D,8OAAC;oDAAI,WAAU;8DAAyB,YAAY,IAAI;;;;;;;;;;;;;mCAPlD;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBxB;uCAEe"}}, {"offset": {"line": 9235, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9241, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/SiteReview.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport StarRating from './StarRating';\n\nconst SiteReview = () => {\n  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);\n  const [userRating, setUserRating] = useState(0);\n  const [userComment, setUserComment] = useState('');\n\n  const siteStats = {\n    averageRating: 4.6,\n    totalReviews: 2847,\n    ratingDistribution: [\n      { stars: 5, count: 1823, percentage: 64 },\n      { stars: 4, count: 682, percentage: 24 },\n      { stars: 3, count: 227, percentage: 8 },\n      { stars: 2, count: 85, percentage: 3 },\n      { stars: 1, count: 30, percentage: 1 }\n    ]\n  };\n\n  const recentReviews = [\n    {\n      id: 1,\n      userName: 'أحمد محمد',\n      rating: 5,\n      comment: 'موقع ممتاز وسهل الاستخدام. تمكنت من بيع سيارتي خلال أسبوع واحد!',\n      date: '2024-01-20',\n      verified: true,\n      helpful: 23\n    },\n    {\n      id: 2,\n      userName: 'سارة أحمد',\n      rating: 4,\n      comment: 'موقع جيد جداً، لكن أتمنى لو كان هناك المزيد من خيارات الفلترة.',\n      date: '2024-01-18',\n      verified: true,\n      helpful: 15\n    },\n    {\n      id: 3,\n      userName: 'محمد علي',\n      rating: 5,\n      comment: 'أفضل موقع للإعلانات في سوريا. خدمة عملاء ممتازة ودعم فني سريع.',\n      date: '2024-01-15',\n      verified: false,\n      helpful: 31\n    },\n    {\n      id: 4,\n      userName: 'فاطمة حسن',\n      rating: 4,\n      comment: 'وجدت الشقة التي أبحث عنها بسهولة. الموقع منظم وواضح.',\n      date: '2024-01-12',\n      verified: true,\n      helpful: 18\n    }\n  ];\n\n  const features = [\n    { name: 'سهولة الاستخدام', rating: 4.7 },\n    { name: 'جودة الإعلانات', rating: 4.5 },\n    { name: 'سرعة الموقع', rating: 4.6 },\n    { name: 'خدمة العملاء', rating: 4.4 },\n    { name: 'الأمان', rating: 4.8 },\n    { name: 'التصميم', rating: 4.6 }\n  ];\n\n\n\n  const handleSubmitReview = (e: React.FormEvent) => {\n    e.preventDefault();\n    console.log('تقييم جديد:', { rating: userRating, comment: userComment });\n    setIsReviewModalOpen(false);\n    setUserRating(0);\n    setUserComment('');\n  };\n\n  return (\n    <section className=\"py-16 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"text-center mb-12\">\n          <h2 className=\"text-3xl font-bold text-gray-800 mb-4\">تقييمات المستخدمين</h2>\n          <p className=\"text-lg text-gray-600\">ماذا يقول مستخدمونا عن موقع من المالك</p>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* ملخص التقييمات */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white rounded-xl shadow-lg p-6 sticky top-6\">\n              <div className=\"text-center mb-6\">\n                <div className=\"text-5xl font-bold text-primary-600 mb-2\">\n                  {siteStats.averageRating}\n                </div>\n                <StarRating rating={siteStats.averageRating} size=\"lg\" showValue={false} />\n                <div className=\"text-gray-600 mt-2\">\n                  من أصل {siteStats.totalReviews.toLocaleString()} تقييم\n                </div>\n              </div>\n\n              {/* توزيع التقييمات */}\n              <div className=\"space-y-3 mb-6\">\n                {siteStats.ratingDistribution.map((item) => (\n                  <div key={item.stars} className=\"flex items-center gap-3\">\n                    <div className=\"flex items-center gap-1 w-8\">\n                      <span className=\"text-sm text-gray-600\">{item.stars}</span>\n                      <StarRating rating={1} size=\"xs\" showValue={false} />\n                    </div>\n                    <div className=\"flex-1 bg-gray-200 rounded-full h-2\">\n                      <div\n                        className=\"bg-yellow-400 h-2 rounded-full transition-all duration-500\"\n                        style={{ width: `${item.percentage}%` }}\n                      />\n                    </div>\n                    <span className=\"text-sm text-gray-600 w-12\">\n                      {item.count}\n                    </span>\n                  </div>\n                ))}\n              </div>\n\n              {/* تقييم المميزات */}\n              <div className=\"mb-6\">\n                <h4 className=\"font-semibold text-gray-800 mb-4\">تقييم المميزات</h4>\n                <div className=\"space-y-3\">\n                  {features.map((feature, index) => (\n                    <div key={index} className=\"flex items-center justify-between\">\n                      <span className=\"text-sm text-gray-700\">{feature.name}</span>\n                      <div className=\"flex items-center gap-2\">\n                        <span className=\"text-sm font-medium text-gray-800\">\n                          {feature.rating}\n                        </span>\n                        <StarRating rating={feature.rating} size=\"sm\" showValue={false} />\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              <button\n                onClick={() => setIsReviewModalOpen(true)}\n                className=\"w-full bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 transition-colors font-semibold\"\n              >\n                أضف تقييمك\n              </button>\n            </div>\n          </div>\n\n          {/* قائمة التقييمات */}\n          <div className=\"lg:col-span-2\">\n            <div className=\"space-y-6\">\n              {recentReviews.map((review) => (\n                <div key={review.id} className=\"bg-white rounded-xl shadow-md p-6\">\n                  <div className=\"flex items-start justify-between mb-4\">\n                    <div className=\"flex items-center gap-3\">\n                      <div className=\"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center\">\n                        <span className=\"text-primary-600 font-semibold\">\n                          {review.userName.charAt(0)}\n                        </span>\n                      </div>\n                      <div>\n                        <div className=\"flex items-center gap-2\">\n                          <span className=\"font-semibold text-gray-800\">\n                            {review.userName}\n                          </span>\n                          {review.verified && (\n                            <span className=\"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full\">\n                              ✓ موثق\n                            </span>\n                          )}\n                        </div>\n                        <div className=\"text-sm text-gray-600\">{review.date}</div>\n                      </div>\n                    </div>\n                    <StarRating rating={review.rating} size=\"md\" showValue={false} />\n                  </div>\n\n                  <p className=\"text-gray-700 leading-relaxed mb-4\">\n                    {review.comment}\n                  </p>\n\n                  <div className=\"flex items-center justify-between\">\n                    <button className=\"flex items-center gap-2 text-sm text-gray-600 hover:text-primary-600 transition-colors\">\n                      <span>👍</span>\n                      <span>مفيد ({review.helpful})</span>\n                    </button>\n                    <button className=\"text-sm text-gray-600 hover:text-primary-600 transition-colors\">\n                      إبلاغ\n                    </button>\n                  </div>\n                </div>\n              ))}\n\n              <div className=\"text-center\">\n                <button className=\"bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors\">\n                  عرض المزيد من التقييمات\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* إحصائيات إضافية */}\n        <div className=\"mt-16 grid grid-cols-1 md:grid-cols-4 gap-6\">\n          <div className=\"bg-white rounded-xl shadow-lg p-6 text-center\">\n            <div className=\"text-3xl font-bold text-green-600 mb-2\">96%</div>\n            <div className=\"text-gray-600\">معدل الرضا</div>\n          </div>\n          <div className=\"bg-white rounded-xl shadow-lg p-6 text-center\">\n            <div className=\"text-3xl font-bold text-blue-600 mb-2\">4.6</div>\n            <div className=\"text-gray-600\">متوسط التقييم</div>\n          </div>\n          <div className=\"bg-white rounded-xl shadow-lg p-6 text-center\">\n            <div className=\"text-3xl font-bold text-purple-600 mb-2\">2.8K</div>\n            <div className=\"text-gray-600\">إجمالي التقييمات</div>\n          </div>\n          <div className=\"bg-white rounded-xl shadow-lg p-6 text-center\">\n            <div className=\"text-3xl font-bold text-orange-600 mb-2\">89%</div>\n            <div className=\"text-gray-600\">يوصون بالموقع</div>\n          </div>\n        </div>\n      </div>\n\n      {/* نافذة إضافة تقييم */}\n      {isReviewModalOpen && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-white rounded-xl max-w-md w-full p-6\">\n            <div className=\"flex items-center justify-between mb-6\">\n              <h3 className=\"text-xl font-bold text-gray-800\">أضف تقييمك</h3>\n              <button\n                onClick={() => setIsReviewModalOpen(false)}\n                className=\"text-gray-400 hover:text-gray-600\"\n              >\n                ×\n              </button>\n            </div>\n\n            <form onSubmit={handleSubmitReview} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  التقييم\n                </label>\n                <StarRating\n                  rating={userRating}\n                  size=\"lg\"\n                  interactive={true}\n                  onChange={setUserRating}\n                  showValue={false}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  التعليق\n                </label>\n                <textarea\n                  value={userComment}\n                  onChange={(e) => setUserComment(e.target.value)}\n                  rows={4}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                  placeholder=\"شاركنا تجربتك مع موقع من المالك...\"\n                  required\n                />\n              </div>\n\n              <div className=\"flex gap-3\">\n                <button\n                  type=\"submit\"\n                  disabled={userRating === 0}\n                  className=\"flex-1 bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n                >\n                  إرسال التقييم\n                </button>\n                <button\n                  type=\"button\"\n                  onClick={() => setIsReviewModalOpen(false)}\n                  className=\"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors\"\n                >\n                  إلغاء\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </section>\n  );\n};\n\nexport default SiteReview;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,aAAa;IACjB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,YAAY;QAChB,eAAe;QACf,cAAc;QACd,oBAAoB;YAClB;gBAAE,OAAO;gBAAG,OAAO;gBAAM,YAAY;YAAG;YACxC;gBAAE,OAAO;gBAAG,OAAO;gBAAK,YAAY;YAAG;YACvC;gBAAE,OAAO;gBAAG,OAAO;gBAAK,YAAY;YAAE;YACtC;gBAAE,OAAO;gBAAG,OAAO;gBAAI,YAAY;YAAE;YACrC;gBAAE,OAAO;gBAAG,OAAO;gBAAI,YAAY;YAAE;SACtC;IACH;IAEA,MAAM,gBAAgB;QACpB;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,SAAS;YACT,MAAM;YACN,UAAU;YACV,SAAS;QACX;QACA;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,SAAS;YACT,MAAM;YACN,UAAU;YACV,SAAS;QACX;QACA;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,SAAS;YACT,MAAM;YACN,UAAU;YACV,SAAS;QACX;QACA;YACE,IAAI;YACJ,UAAU;YACV,QAAQ;YACR,SAAS;YACT,MAAM;YACN,UAAU;YACV,SAAS;QACX;KACD;IAED,MAAM,WAAW;QACf;YAAE,MAAM;YAAmB,QAAQ;QAAI;QACvC;YAAE,MAAM;YAAkB,QAAQ;QAAI;QACtC;YAAE,MAAM;YAAe,QAAQ;QAAI;QACnC;YAAE,MAAM;YAAgB,QAAQ;QAAI;QACpC;YAAE,MAAM;YAAU,QAAQ;QAAI;QAC9B;YAAE,MAAM;YAAW,QAAQ;QAAI;KAChC;IAID,MAAM,qBAAqB,CAAC;QAC1B,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC,eAAe;YAAE,QAAQ;YAAY,SAAS;QAAY;QACtE,qBAAqB;QACrB,cAAc;QACd,eAAe;IACjB;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAGvC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,UAAU,aAAa;;;;;;8DAE1B,8OAAC,gIAAA,CAAA,UAAU;oDAAC,QAAQ,UAAU,aAAa;oDAAE,MAAK;oDAAK,WAAW;;;;;;8DAClE,8OAAC;oDAAI,WAAU;;wDAAqB;wDAC1B,UAAU,YAAY,CAAC,cAAc;wDAAG;;;;;;;;;;;;;sDAKpD,8OAAC;4CAAI,WAAU;sDACZ,UAAU,kBAAkB,CAAC,GAAG,CAAC,CAAC,qBACjC,8OAAC;oDAAqB,WAAU;;sEAC9B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAyB,KAAK,KAAK;;;;;;8EACnD,8OAAC,gIAAA,CAAA,UAAU;oEAAC,QAAQ;oEAAG,MAAK;oEAAK,WAAW;;;;;;;;;;;;sEAE9C,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEACC,WAAU;gEACV,OAAO;oEAAE,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,CAAC;gEAAC;;;;;;;;;;;sEAG1C,8OAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;;mDAZL,KAAK,KAAK;;;;;;;;;;sDAmBxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;8DACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4DAAgB,WAAU;;8EACzB,8OAAC;oEAAK,WAAU;8EAAyB,QAAQ,IAAI;;;;;;8EACrD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFACb,QAAQ,MAAM;;;;;;sFAEjB,8OAAC,gIAAA,CAAA,UAAU;4EAAC,QAAQ,QAAQ,MAAM;4EAAE,MAAK;4EAAK,WAAW;;;;;;;;;;;;;2DANnD;;;;;;;;;;;;;;;;sDAahB,8OAAC;4CACC,SAAS,IAAM,qBAAqB;4CACpC,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAOL,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;wCACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,8OAAC;gDAAoB,WAAU;;kEAC7B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;sFACb,OAAO,QAAQ,CAAC,MAAM,CAAC;;;;;;;;;;;kFAG5B,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;kGACb,OAAO,QAAQ;;;;;;oFAEjB,OAAO,QAAQ,kBACd,8OAAC;wFAAK,WAAU;kGAA6D;;;;;;;;;;;;0FAKjF,8OAAC;gFAAI,WAAU;0FAAyB,OAAO,IAAI;;;;;;;;;;;;;;;;;;0EAGvD,8OAAC,gIAAA,CAAA,UAAU;gEAAC,QAAQ,OAAO,MAAM;gEAAE,MAAK;gEAAK,WAAW;;;;;;;;;;;;kEAG1D,8OAAC;wDAAE,WAAU;kEACV,OAAO,OAAO;;;;;;kEAGjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAO,WAAU;;kFAChB,8OAAC;kFAAK;;;;;;kFACN,8OAAC;;4EAAK;4EAAO,OAAO,OAAO;4EAAC;;;;;;;;;;;;;0EAE9B,8OAAC;gEAAO,WAAU;0EAAiE;;;;;;;;;;;;;+CAlC7E,OAAO,EAAE;;;;;sDAyCrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAO,WAAU;0DAAqF;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS/G,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAyC;;;;;;kDACxD,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAwC;;;;;;kDACvD,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA0C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA0C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;YAMpC,mCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkC;;;;;;8CAChD,8OAAC;oCACC,SAAS,IAAM,qBAAqB;oCACpC,WAAU;8CACX;;;;;;;;;;;;sCAKH,8OAAC;4BAAK,UAAU;4BAAoB,WAAU;;8CAC5C,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC,gIAAA,CAAA,UAAU;4CACT,QAAQ;4CACR,MAAK;4CACL,aAAa;4CACb,UAAU;4CACV,WAAW;;;;;;;;;;;;8CAIf,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,MAAM;4CACN,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,UAAU,eAAe;4CACzB,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,qBAAqB;4CACpC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;uCAEe"}}, {"offset": {"line": 10031, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 10037, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport Logo from './Logo';\nimport MyCvLogo from './MyCvLogo';\nimport ContactButtons, { ContactInfo } from '@/components/ContactButtons';\nimport { COMPANY_CONTACT } from '@/lib/contact';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Main Footer */}\n      <div className=\"container mx-auto px-4 py-8 md:py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"mb-6\">\n              <div className=\"flex items-center gap-3 mb-2\">\n                <Logo variant=\"transparent\" size=\"lg\" showText={true} textColor=\"yellow\" href=\"/\" isFooter={true} />\n                <div className=\"bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold\">\n                  سوريا\n                </div>\n              </div>\n            </div>\n            <p className=\"text-gray-400 mb-4\">\n              موقع من المالك هو أكبر موقع للإعلانات المبوبة في سوريا، يوفر منصة آمنة وسهلة للبيع والشراء.\n            </p>\n\n\n\n            {/* مواقع التواصل الاجتماعي */}\n            <div className=\"mt-6\">\n              <h5 className=\"font-medium mb-3 text-sm text-gray-300\">تابعنا على</h5>\n              <div className=\"flex gap-2\">\n                <a href=\"#\" className=\"w-8 h-8 bg-gray-800 hover:bg-blue-600 rounded-md flex items-center justify-center transition-all duration-300 group\" title=\"فيسبوك\">\n                  <svg viewBox=\"0 0 24 24\" className=\"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\" fill=\"currentColor\">\n                    <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"w-8 h-8 bg-gray-800 hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 rounded-md flex items-center justify-center transition-all duration-300 group\" title=\"انستغرام\">\n                  <svg viewBox=\"0 0 24 24\" className=\"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\" fill=\"currentColor\">\n                    <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"w-8 h-8 bg-gray-800 hover:bg-red-600 rounded-md flex items-center justify-center transition-all duration-300 group\" title=\"يوتيوب\">\n                  <svg viewBox=\"0 0 24 24\" className=\"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\" fill=\"currentColor\">\n                    <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"w-8 h-8 bg-gray-800 hover:bg-gray-900 rounded-md flex items-center justify-center transition-all duration-300 group\" title=\"X (تويتر)\">\n                  <svg viewBox=\"0 0 24 24\" className=\"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\" fill=\"currentColor\">\n                    <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"w-8 h-8 bg-gray-800 hover:bg-black rounded-md flex items-center justify-center transition-all duration-300 group\" title=\"تيك توك\">\n                  <svg viewBox=\"0 0 24 24\" className=\"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\" fill=\"currentColor\">\n                    <path d=\"M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"w-8 h-8 bg-gray-800 hover:bg-blue-700 rounded-md flex items-center justify-center transition-all duration-300 group\" title=\"لينكد إن\">\n                  <svg viewBox=\"0 0 24 24\" className=\"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\" fill=\"currentColor\">\n                    <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                  </svg>\n                </a>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">روابط سريعة</h4>\n            <ul className=\"space-y-3\">\n              <li><Link href=\"/about\" className=\"text-gray-400 hover:text-white transition-colors\">من نحن</Link></li>\n              <li><Link href=\"/pricing\" className=\"text-gray-400 hover:text-white transition-colors\">أسعار الإعلانات</Link></li>\n              <li><Link href=\"/safety\" className=\"text-gray-400 hover:text-white transition-colors\">نصائح الأمان</Link></li>\n              <li><Link href=\"/faq\" className=\"text-gray-400 hover:text-white transition-colors\">الأسئلة الشائعة</Link></li>\n              <li><Link href=\"/dashboard\" className=\"text-gray-400 hover:text-white transition-colors\">لوحة التحكم</Link></li>\n              <li><Link href=\"/contact\" className=\"text-gray-400 hover:text-white transition-colors\">اتصل بنا</Link></li>\n            </ul>\n          </div>\n\n          {/* Categories */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">التصنيفات الرئيسية</h4>\n            <ul className=\"space-y-3\">\n              <li><Link href=\"/category/real-estate\" className=\"text-gray-400 hover:text-white transition-colors\">العقارات</Link></li>\n              <li><Link href=\"/category/cars\" className=\"text-gray-400 hover:text-white transition-colors\">السيارات</Link></li>\n              <li><Link href=\"/category/electronics\" className=\"text-gray-400 hover:text-white transition-colors\">الإلكترونيات</Link></li>\n              <li><Link href=\"/jobs\" className=\"text-gray-400 hover:text-white transition-colors\">الوظائف</Link></li>\n              <li><Link href=\"/category/services\" className=\"text-gray-400 hover:text-white transition-colors\">الخدمات</Link></li>\n              <li><Link href=\"/category/fashion\" className=\"text-gray-400 hover:text-white transition-colors\">الأزياء</Link></li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">معلومات التواصل</h4>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-start gap-3\">\n                <img\n                  src=\"/images/الفوتر/دمشق، سوريا.png\"\n                  alt=\"الموقع\"\n                  className=\"w-5 h-5 mt-1 opacity-80\"\n                  style={{ filter: 'brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(200deg)' }}\n                />\n                <div>\n                  <p className=\"text-white font-semibold\">دمشق، سوريا</p>\n                  <p className=\"text-gray-400 text-sm\">العنوان الرئيسي</p>\n                </div>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <img\n                  src=\"/images/الفوتر/+963 988 652 401.png\"\n                  alt=\"الهاتف\"\n                  className=\"w-5 h-5 opacity-80\"\n                  style={{ filter: 'brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(200deg)' }}\n                />\n                <div>\n                  <p className=\"text-white font-semibold\">+963 988 652 401</p>\n                  <p className=\"text-gray-400 text-sm\">للاتصال والواتساب</p>\n                </div>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <img\n                  src=\"/images/الفوتر/<EMAIL>\"\n                  alt=\"البريد الإلكتروني\"\n                  className=\"w-5 h-5 opacity-80\"\n                  style={{ filter: 'brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(200deg)' }}\n                />\n                <p className=\"text-gray-400\"><EMAIL></p>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <img\n                  src=\"/images/الفوتر/من السبت إلى الخميس.png\"\n                  alt=\"أوقات العمل\"\n                  className=\"w-5 h-5 opacity-80\"\n                  style={{ filter: 'brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(200deg)' }}\n                />\n                <div>\n                  <p className=\"text-gray-400\">من السبت إلى الخميس</p>\n                  <p className=\"text-gray-400 text-sm\">وقت الرد: خلال 24 ساعة</p>\n                </div>\n              </div>\n            </div>\n\n\n          </div>\n        </div>\n      </div>\n\n      {/* Apps Download Section */}\n      <div className=\"border-t border-gray-800 bg-gray-800/50\">\n        <div className=\"container mx-auto px-4 py-4 md:py-6\">\n          {/* Apps and Payment Methods in One Row */}\n          <div className=\"flex flex-wrap items-center justify-center gap-4 md:gap-6\">\n            {/* تطبيق MyCv */}\n            <div className=\"flex items-center gap-3 bg-gray-900 rounded-lg p-3 min-w-[240px]\">\n              <MyCvLogo size=\"sm\" variant=\"square\" />\n              <div className=\"flex-1 min-w-0\">\n                <h6 className=\"font-medium text-white text-sm\">تطبيق MyCv</h6>\n                <p className=\"text-gray-400 text-xs mb-2\">منصة السير الذاتية والتوظيف</p>\n                <div className=\"flex gap-1\">\n                  <a href=\"#\" className=\"bg-black text-white px-2 py-1 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs\">\n                    <svg viewBox=\"0 0 24 24\" className=\"w-3 h-3 text-white\" fill=\"currentColor\">\n                      <path d=\"M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z\"/>\n                    </svg>\n                    <span className=\"hidden sm:inline\">AppStore</span>\n                  </a>\n                  <a href=\"#\" className=\"bg-black text-white px-2 py-1 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs\">\n                    <svg viewBox=\"0 0 24 24\" className=\"w-3 h-3 text-white\" fill=\"currentColor\">\n                      <path d=\"M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z\"/>\n                    </svg>\n                    <span className=\"hidden sm:inline\">Android</span>\n                  </a>\n                </div>\n              </div>\n            </div>\n\n            {/* تطبيق من المالك */}\n            <div className=\"flex items-center gap-3 bg-gray-900 rounded-lg p-3 min-w-[240px]\">\n              <Logo variant=\"transparent\" size=\"sm\" showText={false} />\n              <div className=\"flex-1 min-w-0\">\n                <h6 className=\"font-medium text-white text-sm\">تطبيق من المالك</h6>\n                <p className=\"text-gray-400 text-xs mb-2\">منصة الإعلانات المبوبة</p>\n                <div className=\"flex gap-1\">\n                  <a href=\"#\" className=\"bg-black text-white px-2 py-1 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs\">\n                    <svg viewBox=\"0 0 24 24\" className=\"w-3 h-3 text-white\" fill=\"currentColor\">\n                      <path d=\"M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z\"/>\n                    </svg>\n                    <span className=\"hidden sm:inline\">AppStore</span>\n                  </a>\n                  <a href=\"#\" className=\"bg-black text-white px-2 py-1 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs\">\n                    <svg viewBox=\"0 0 24 24\" className=\"w-3 h-3 text-white\" fill=\"currentColor\">\n                      <path d=\"M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z\"/>\n                    </svg>\n                    <span className=\"hidden sm:inline\">Android</span>\n                  </a>\n                </div>\n              </div>\n            </div>\n\n\n\n            {/* QR Code Section */}\n            <div className=\"flex items-center gap-4 bg-gray-900 rounded-lg p-4 min-w-[200px]\">\n              <div className=\"relative flex-shrink-0\">\n                {/* QR Code Background */}\n                <div className=\"w-20 h-20 bg-white rounded-md p-1 shadow-lg\">\n                  {/* Real QR Code for Min Almalek website */}\n                  <img\n                    src=\"https://api.qrserver.com/v1/create-qr-code/?size=80x80&data=https://min-almalek.com&bgcolor=ffffff&color=000000&margin=1\"\n                    alt=\"QR Code for Min Almalek\"\n                    className=\"w-full h-full rounded\"\n                  />\n\n                  {/* Logo overlay in center */}\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"w-4 h-4 bg-white rounded-sm flex items-center justify-center shadow-sm\">\n                      <Logo variant=\"dark\" size=\"xs\" showText={false} />\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <div className=\"flex-1\">\n                <h6 className=\"font-medium text-white text-sm\">موقع من المالك</h6>\n                <p className=\"text-gray-400 text-xs mb-1\">امسح للوصول السريع</p>\n                <p className=\"text-gray-500 text-xs\">min-almalek.com</p>\n              </div>\n            </div>\n\n            {/* Partner Logos and Payment Methods Section */}\n            <div className=\"flex items-center gap-4 bg-gray-900 rounded-lg p-4 min-w-[200px]\">\n              <div className=\"flex-1\">\n                {/* Partner Logos */}\n                <div className=\"flex items-center justify-center gap-4 mb-3\">\n                  {/* MADENLİ Group Logo */}\n                  <div className=\"flex items-center justify-center\">\n                    <img\n                      src=\"/images/madenli group LOGO.jpg\"\n                      alt=\"MADENLİ Group\"\n                      className=\"h-8 w-auto object-contain rounded-sm\"\n                    />\n                  </div>\n\n                  {/* MyCv Logo */}\n                  <div className=\"flex items-center justify-center\">\n                    <img\n                      src=\"/images/MyCV Logo.jpg\"\n                      alt=\"MyCv\"\n                      className=\"h-8 w-auto object-contain rounded-sm\"\n                    />\n                  </div>\n                </div>\n\n                {/* Payment Methods */}\n                <div className=\"flex flex-wrap items-center justify-center gap-2\">\n                  {/* Visa */}\n                  <img\n                    src=\"https://upload.wikimedia.org/wikipedia/commons/4/41/Visa_Logo.png\"\n                    alt=\"Visa\"\n                    className=\"h-4 w-auto object-contain\"\n                  />\n\n                  {/* MasterCard */}\n                  <img\n                    src=\"https://upload.wikimedia.org/wikipedia/commons/0/04/Mastercard-logo.png\"\n                    alt=\"MasterCard\"\n                    className=\"h-4 w-auto object-contain\"\n                  />\n\n                  {/* PayPal */}\n                  <img\n                    src=\"https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg\"\n                    alt=\"PayPal\"\n                    className=\"h-4 w-auto object-contain\"\n                  />\n\n                  {/* Cash App */}\n                  <img\n                    src=\"/images/cash-app-logo.svg\"\n                    alt=\"Cash App\"\n                    className=\"h-4 w-auto object-contain\"\n                  />\n\n                  {/* Apple Pay */}\n                  <div className=\"h-4 flex items-center gap-1\">\n                    <img\n                      src=\"https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg\"\n                      alt=\"Apple\"\n                      className=\"h-4 w-3 object-contain invert\"\n                    />\n                    <span className=\"text-xs font-semibold text-white\">Pay</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Bar */}\n      <div className=\"border-t border-gray-800\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between gap-4\">\n            <div className=\"text-gray-400 text-sm\">\n              © 2017-2025 من المالك. جميع الحقوق محفوظة.\n            </div>\n            <div className=\"flex flex-wrap gap-6 text-sm\">\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white transition-colors\">\n                سياسة الخصوصية\n              </Link>\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-white transition-colors\">\n                شروط الاستخدام\n              </Link>\n              <Link href=\"/cookies\" className=\"text-gray-400 hover:text-white transition-colors\">\n                سياسة الكوكيز\n              </Link>\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-white transition-colors\">\n                خريطة الموقع\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAIA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,UAAI;gDAAC,SAAQ;gDAAc,MAAK;gDAAK,UAAU;gDAAM,WAAU;gDAAS,MAAK;gDAAI,UAAU;;;;;;0DAC5F,8OAAC;gDAAI,WAAU;0DAA+D;;;;;;;;;;;;;;;;;8CAKlF,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAOlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,MAAK;oDAAI,WAAU;oDAAsH,OAAM;8DAChJ,cAAA,8OAAC;wDAAI,SAAQ;wDAAY,WAAU;wDAAiE,MAAK;kEACvG,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,MAAK;oDAAI,WAAU;oDAAmK,OAAM;8DAC7L,cAAA,8OAAC;wDAAI,SAAQ;wDAAY,WAAU;wDAAiE,MAAK;kEACvG,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,MAAK;oDAAI,WAAU;oDAAqH,OAAM;8DAC/I,cAAA,8OAAC;wDAAI,SAAQ;wDAAY,WAAU;wDAAiE,MAAK;kEACvG,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,MAAK;oDAAI,WAAU;oDAAsH,OAAM;8DAChJ,cAAA,8OAAC;wDAAI,SAAQ;wDAAY,WAAU;wDAAiE,MAAK;kEACvG,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,MAAK;oDAAI,WAAU;oDAAmH,OAAM;8DAC7I,cAAA,8OAAC;wDAAI,SAAQ;wDAAY,WAAU;wDAAiE,MAAK;kEACvG,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,MAAK;oDAAI,WAAU;oDAAsH,OAAM;8DAChJ,cAAA,8OAAC;wDAAI,SAAQ;wDAAY,WAAU;wDAAiE,MAAK;kEACvG,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDACrF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDACvF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmD;;;;;;;;;;;sDACtF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAmD;;;;;;;;;;;sDACnF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAmD;;;;;;;;;;;sDACzF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAK3F,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAwB,WAAU;0DAAmD;;;;;;;;;;;sDACpG,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAiB,WAAU;0DAAmD;;;;;;;;;;;sDAC7F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAwB,WAAU;0DAAmD;;;;;;;;;;;sDACpG,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmD;;;;;;;;;;;sDACpF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;0DAAmD;;;;;;;;;;;sDACjG,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAKpG,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;oDACV,OAAO;wDAAE,QAAQ;oDAAkE;;;;;;8DAErF,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA2B;;;;;;sEACxC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;oDACV,OAAO;wDAAE,QAAQ;oDAAkE;;;;;;8DAErF,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA2B;;;;;;sEACxC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;oDACV,OAAO;wDAAE,QAAQ;oDAAkE;;;;;;8DAErF,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAE/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;oDACV,OAAO;wDAAE,QAAQ;oDAAkE;;;;;;8DAErF,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8HAAA,CAAA,UAAQ;wCAAC,MAAK;wCAAK,SAAQ;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,MAAK;wDAAI,WAAU;;0EACpB,8OAAC;gEAAI,SAAQ;gEAAY,WAAU;gEAAqB,MAAK;0EAC3D,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;0EAEV,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;kEAErC,8OAAC;wDAAE,MAAK;wDAAI,WAAU;;0EACpB,8OAAC;gEAAI,SAAQ;gEAAY,WAAU;gEAAqB,MAAK;0EAC3D,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;0EAEV,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,UAAI;wCAAC,SAAQ;wCAAc,MAAK;wCAAK,UAAU;;;;;;kDAChD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,MAAK;wDAAI,WAAU;;0EACpB,8OAAC;gEAAI,SAAQ;gEAAY,WAAU;gEAAqB,MAAK;0EAC3D,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;0EAEV,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;kEAErC,8OAAC;wDAAE,MAAK;wDAAI,WAAU;;0EACpB,8OAAC;gEAAI,SAAQ;gEAAY,WAAU;gEAAqB,MAAK;0EAC3D,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;0EAEV,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAEb,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0HAAA,CAAA,UAAI;4DAAC,SAAQ;4DAAO,MAAK;4DAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAKzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;8DAKd,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;;;;;;;sDAMhB,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,KAAI;4DACJ,KAAI;4DACJ,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;0CAGvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAmD;;;;;;kDAGnF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmD;;;;;;kDAGjF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAmD;;;;;;kDAGnF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWjG;uCAEe"}}, {"offset": {"line": 11279, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 11285, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/Toast.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport interface ToastMessage {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  title: string;\n  message: string;\n  duration?: number;\n}\n\ninterface ToastProps {\n  message: ToastMessage;\n  onRemove: (id: string) => void;\n}\n\nconst Toast = ({ message, onRemove }: ToastProps) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isLeaving, setIsLeaving] = useState(false);\n\n  useEffect(() => {\n    // إظهار التوست\n    setTimeout(() => setIsVisible(true), 100);\n\n    // إخفاء التوست تلقائياً\n    const timer = setTimeout(() => {\n      handleClose();\n    }, message.duration || 5000);\n\n    return () => clearTimeout(timer);\n  }, [message.duration]);\n\n  const handleClose = () => {\n    setIsLeaving(true);\n    setTimeout(() => {\n      onRemove(message.id);\n    }, 300);\n  };\n\n  const getIcon = () => {\n    switch (message.type) {\n      case 'success':\n        return (\n          <svg className=\"w-6 h-6 text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n          </svg>\n        );\n      case 'error':\n        return (\n          <svg className=\"w-6 h-6 text-red-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n          </svg>\n        );\n      case 'warning':\n        return (\n          <svg className=\"w-6 h-6 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n          </svg>\n        );\n      case 'info':\n        return (\n          <svg className=\"w-6 h-6 text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n        );\n      default:\n        return null;\n    }\n  };\n\n  const getBackgroundClass = () => {\n    switch (message.type) {\n      case 'success':\n        return 'bg-gradient-to-br from-green-500/80 via-green-600/70 to-green-700/80 border-green-300/40 shadow-green-500/25';\n      case 'error':\n        return 'bg-gradient-to-br from-red-500/80 via-red-600/70 to-red-700/80 border-red-300/40 shadow-red-500/25';\n      case 'warning':\n        return 'bg-gradient-to-br from-yellow-500/80 via-yellow-600/70 to-yellow-700/80 border-yellow-300/40 shadow-yellow-500/25';\n      case 'info':\n        return 'bg-gradient-to-br from-blue-500/80 via-blue-600/70 to-blue-700/80 border-blue-300/40 shadow-blue-500/25';\n      default:\n        return 'bg-gradient-to-br from-green-500/80 via-green-600/70 to-green-700/80 border-green-300/40 shadow-green-500/25';\n    }\n  };\n\n  return (\n    <div\n      className={`\n        fixed top-4 right-4 z-50 max-w-sm w-full\n        transform transition-all duration-300 ease-in-out\n        ${isVisible && !isLeaving ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}\n        ${isLeaving ? 'scale-95' : 'scale-100'}\n      `}\n    >\n      <div\n        className={`\n          ${getBackgroundClass()}\n          backdrop-blur-lg border rounded-xl shadow-2xl p-4 text-white\n          relative overflow-hidden\n        `}\n        style={{\n          backdropFilter: 'blur(10px)',\n          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)'\n        }}\n      >\n        {/* تأثير الخلفية الزجاجية */}\n        <div className=\"absolute inset-0 bg-white/10 backdrop-blur-sm\"></div>\n        \n        {/* المحتوى */}\n        <div className=\"relative z-10 flex items-start gap-3\">\n          <div className=\"flex-shrink-0 mt-0.5\">\n            {getIcon()}\n          </div>\n          \n          <div className=\"flex-1 min-w-0\">\n            <h4 className=\"text-sm font-semibold text-white mb-1\">\n              {message.title}\n            </h4>\n            <p className=\"text-sm text-white/90 leading-relaxed\">\n              {message.message}\n            </p>\n          </div>\n          \n          <button\n            onClick={handleClose}\n            className=\"flex-shrink-0 text-white/70 hover:text-white transition-colors p-1 rounded-full hover:bg-white/10\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* شريط التقدم */}\n        <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-white/20 overflow-hidden\">\n          <div \n            className=\"h-full bg-white/40 transition-all ease-linear\"\n            style={{\n              animation: `shrink ${message.duration || 5000}ms linear forwards`\n            }}\n          />\n        </div>\n      </div>\n\n      <style jsx>{`\n        @keyframes shrink {\n          from { width: 100%; }\n          to { width: 0%; }\n        }\n      `}</style>\n    </div>\n  );\n};\n\n// مكون إدارة التوست\ninterface ToastContainerProps {\n  messages: ToastMessage[];\n  onRemove: (id: string) => void;\n}\n\nexport const ToastContainer = ({ messages, onRemove }: ToastContainerProps) => {\n  return (\n    <div className=\"fixed top-0 right-0 z-50 p-4 space-y-3\">\n      {messages.map((message, index) => (\n        <div\n          key={message.id}\n          style={{\n            transform: `translateY(${index * 10}px)`,\n            zIndex: 50 - index\n          }}\n        >\n          <Toast message={message} onRemove={onRemove} />\n        </div>\n      ))}\n    </div>\n  );\n};\n\n// Hook لاستخدام التوست\nexport const useToast = () => {\n  const [messages, setMessages] = useState<ToastMessage[]>([]);\n\n  const showToast = (toast: Omit<ToastMessage, 'id'>) => {\n    const id = Date.now().toString();\n    const newMessage: ToastMessage = { ...toast, id };\n    \n    setMessages(prev => [...prev, newMessage]);\n  };\n\n  const removeToast = (id: string) => {\n    setMessages(prev => prev.filter(msg => msg.id !== id));\n  };\n\n  const showSuccess = (title: string, message: string) => {\n    showToast({ type: 'success', title, message });\n  };\n\n  const showError = (title: string, message: string) => {\n    showToast({ type: 'error', title, message });\n  };\n\n  const showWarning = (title: string, message: string) => {\n    showToast({ type: 'warning', title, message });\n  };\n\n  const showInfo = (title: string, message: string) => {\n    showToast({ type: 'info', title, message });\n  };\n\n  return {\n    messages,\n    removeToast,\n    showToast,\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo\n  };\n};\n\nexport default Toast;\n"], "names": [], "mappings": ";;;;;;;AAEA;AAFA;;;;AAiBA,MAAM,QAAQ,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAc;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;QACf,WAAW,IAAM,aAAa,OAAO;QAErC,wBAAwB;QACxB,MAAM,QAAQ,WAAW;YACvB;QACF,GAAG,QAAQ,QAAQ,IAAI;QAEvB,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC,QAAQ,QAAQ;KAAC;IAErB,MAAM,cAAc;QAClB,aAAa;QACb,WAAW;YACT,SAAS,QAAQ,EAAE;QACrB,GAAG;IACL;IAEA,MAAM,UAAU;QACd,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;oBAAyB,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAChF,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;oBAAuB,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAC9E,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;oBAA0B,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjF,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;oBAAwB,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BAC/E,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;gBACE,OAAO;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAQ,QAAQ,IAAI;YAClB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;kDACY,CAAC;;;QAGV,EAAE,aAAa,CAAC,YAAY,8BAA8B,6BAA6B;QACvF,EAAE,YAAY,aAAa,YAAY;MACzC,CAAC;;0BAED,8OAAC;gBAMC,OAAO;oBACL,gBAAgB;oBAChB,WAAW;gBACb;0DARW,CAAC;UACV,EAAE,qBAAqB;;;QAGzB,CAAC;;kCAOD,8OAAC;kEAAc;;;;;;kCAGf,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;0CACZ;;;;;;0CAGH,8OAAC;0EAAc;;kDACb,8OAAC;kFAAa;kDACX,QAAQ,KAAK;;;;;;kDAEhB,8OAAC;kFAAY;kDACV,QAAQ,OAAO;;;;;;;;;;;;0CAIpB,8OAAC;gCACC,SAAS;0EACC;0CAEV,cAAA,8OAAC;oCAAwB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8EAApD;8CACb,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;kCAM3E,8OAAC;kEAAc;kCACb,cAAA,8OAAC;4BAEC,OAAO;gCACL,WAAW,CAAC,OAAO,EAAE,QAAQ,QAAQ,IAAI,KAAK,kBAAkB,CAAC;4BACnE;sEAHU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgBtB;AAQO,MAAM,iBAAiB,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAuB;IACxE,qBACE,8OAAC;QAAI,WAAU;kBACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;gBAEC,OAAO;oBACL,WAAW,CAAC,WAAW,EAAE,QAAQ,GAAG,GAAG,CAAC;oBACxC,QAAQ,KAAK;gBACf;0BAEA,cAAA,8OAAC;oBAAM,SAAS;oBAAS,UAAU;;;;;;eAN9B,QAAQ,EAAE;;;;;;;;;;AAWzB;AAGO,MAAM,WAAW;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAE3D,MAAM,YAAY,CAAC;QACjB,MAAM,KAAK,KAAK,GAAG,GAAG,QAAQ;QAC9B,MAAM,aAA2B;YAAE,GAAG,KAAK;YAAE;QAAG;QAEhD,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAW;IAC3C;IAEA,MAAM,cAAc,CAAC;QACnB,YAAY,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IACpD;IAEA,MAAM,cAAc,CAAC,OAAe;QAClC,UAAU;YAAE,MAAM;YAAW;YAAO;QAAQ;IAC9C;IAEA,MAAM,YAAY,CAAC,OAAe;QAChC,UAAU;YAAE,MAAM;YAAS;YAAO;QAAQ;IAC5C;IAEA,MAAM,cAAc,CAAC,OAAe;QAClC,UAAU;YAAE,MAAM;YAAW;YAAO;QAAQ;IAC9C;IAEA,MAAM,WAAW,CAAC,OAAe;QAC/B,UAAU;YAAE,MAAM;YAAQ;YAAO;QAAQ;IAC3C;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;uCAEe"}}, {"offset": {"line": 11634, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 11640, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport Header from '@/components/Header';\nimport HeroSection from '@/components/HeroSection';\nimport CategoryGrid from '@/components/CategoryGrid';\nimport FeaturedAds from '@/components/FeaturedAds';\nimport LocationFilter from '@/components/LocationFilter';\nimport LatestAds from '@/components/LatestAds';\nimport SiteStats from '@/components/SiteStats';\nimport SiteReview from '@/components/SiteReview';\nimport Footer from '@/components/Footer';\nimport { useToast, ToastContainer } from '@/components/Toast';\n\nexport default function Home() {\n  const { messages, removeToast, showSuccess } = useToast();\n\n  // التحقق من رسائل النجاح المحفوظة\n  useEffect(() => {\n    const showLoginSuccess = localStorage.getItem('showLoginSuccess');\n    if (showLoginSuccess === 'true') {\n      showSuccess('تم تسجيل الدخول بنجاح', '🌟 أهلاً وسهلاً بك في منصة من المالك! نتمنى لك تجربة مميزة واستكشاف جميع الميزات الاحترافية المتاحة.');\n      localStorage.removeItem('showLoginSuccess');\n    }\n  }, [showSuccess]);\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      <main>\n        <HeroSection />\n        <CategoryGrid />\n        <FeaturedAds />\n        <LocationFilter />\n        <LatestAds />\n        <SiteStats />\n        <SiteReview />\n      </main>\n      <Footer />\n\n      {/* نظام التوست */}\n      <ToastContainer messages={messages} onRemove={removeToast} />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAEtD,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB,aAAa,OAAO,CAAC;QAC9C,IAAI,qBAAqB,QAAQ;YAC/B,YAAY,yBAAyB;YACrC,aAAa,UAAU,CAAC;QAC1B;IACF,GAAG;QAAC;KAAY;IAChB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BACP,8OAAC;;kCACC,8OAAC,iIAAA,CAAA,UAAW;;;;;kCACZ,8OAAC,kIAAA,CAAA,UAAY;;;;;kCACb,8OAAC,iIAAA,CAAA,UAAW;;;;;kCACZ,8OAAC,oIAAA,CAAA,UAAc;;;;;kCACf,8OAAC,+HAAA,CAAA,UAAS;;;;;kCACV,8OAAC,+HAAA,CAAA,UAAS;;;;;kCACV,8OAAC,gIAAA,CAAA,UAAU;;;;;;;;;;;0BAEb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAGP,8OAAC,2HAAA,CAAA,iBAAc;gBAAC,UAAU;gBAAU,UAAU;;;;;;;;;;;;AAGpD"}}, {"offset": {"line": 11751, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}