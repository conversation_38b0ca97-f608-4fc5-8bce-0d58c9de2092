export default function Loading() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="text-center max-w-sm mx-auto">
        {/* شعار الموقع - محسن للموبايل */}
        <div className="mb-8">
          <h1 className="text-2xl md:text-3xl font-bold text-primary-600 mb-2">من المالك</h1>
          <p className="text-gray-600 text-sm md:text-base px-4">موقع الإعلانات المبوب الأول في سوريا</p>
        </div>

        {/* مؤشر التحميل - أصغر للموبايل */}
        <div className="relative mb-6">
          <div className="w-12 h-12 md:w-16 md:h-16 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto"></div>
        </div>

        {/* نص التحميل - محسن للموبايل */}
        <div className="space-y-2">
          <p className="text-base md:text-lg font-medium text-gray-800">جاري التحميل...</p>
          <p className="text-xs md:text-sm text-gray-600">يرجى الانتظار قليلاً</p>
        </div>

        {/* نقاط متحركة - أصغر للموبايل */}
        <div className="flex justify-center gap-1 mt-6">
          <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-primary-600 rounded-full animate-bounce"></div>
          <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-1.5 h-1.5 md:w-2 md:h-2 bg-primary-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      </div>
    </div>
  );
}
