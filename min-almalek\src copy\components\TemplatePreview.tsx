'use client';

import { useState } from 'react';
import Link from 'next/link';

interface Template {
  id: string;
  name: string;
  description: string;
  category: 'modern' | 'classic' | 'creative' | 'minimal';
  color: string;
  features: string[];
  suitable: string[];
  premium: boolean;
}

interface TemplatePreviewProps {
  template: Template;
  isOpen: boolean;
  onClose: () => void;
}

const TemplatePreview = ({ template, isOpen, onClose }: TemplatePreviewProps) => {
  const [currentView, setCurrentView] = useState<'desktop' | 'mobile'>('desktop');

  if (!isOpen) return null;

  // بيانات تجريبية للمعاينة
  const sampleData = {
    name: 'أحمد محمد علي',
    title: 'مطور ويب متقدم',
    email: '<EMAIL>',
    phone: '+*********** 456',
    location: 'دمشق، سوريا',
    summary: 'مطور ويب متخصص في React.js و Node.js مع خبرة 5 سنوات في تطوير التطبيقات الحديثة والمتجاوبة. شغوف بالتقنيات الحديثة وتطوير حلول مبتكرة.',
    experience: [
      {
        title: 'مطور ويب أول',
        company: 'شركة التقنيات المتقدمة',
        period: '2021 - الآن',
        description: 'تطوير وصيانة تطبيقات الويب باستخدام React.js و Node.js'
      },
      {
        title: 'مطور ويب',
        company: 'شركة الحلول الرقمية',
        period: '2019 - 2021',
        description: 'تطوير واجهات المستخدم التفاعلية وتطبيقات الويب المتجاوبة'
      }
    ],
    education: [
      {
        degree: 'بكالوريوس هندسة الحاسوب',
        school: 'جامعة دمشق',
        period: '2015 - 2019'
      }
    ],
    skills: ['React.js', 'Node.js', 'TypeScript', 'MongoDB', 'AWS'],
    languages: ['العربية (أصلي)', 'الإنجليزية (متقدم)', 'الفرنسية (متوسط)']
  };

  const renderModernTemplate = () => (
    <div className="bg-white h-full overflow-y-auto">
      {/* Header */}
      <div className={`${template.color} text-white p-8`}>
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl font-bold mb-2">{sampleData.name}</h1>
          <h2 className="text-xl opacity-90 mb-4">{sampleData.title}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>📧 {sampleData.email}</div>
            <div>📞 {sampleData.phone}</div>
            <div>📍 {sampleData.location}</div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto p-8">
        {/* Summary */}
        <section className="mb-8">
          <h3 className={`text-2xl font-bold mb-4 ${template.color.replace('bg-', 'text-')}`}>
            نبذة شخصية
          </h3>
          <p className="text-gray-700 leading-relaxed">{sampleData.summary}</p>
        </section>

        {/* Experience */}
        <section className="mb-8">
          <h3 className={`text-2xl font-bold mb-4 ${template.color.replace('bg-', 'text-')}`}>
            الخبرات العملية
          </h3>
          <div className="space-y-6">
            {sampleData.experience.map((exp, index) => (
              <div key={index} className="border-r-4 border-gray-200 pr-4">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="font-bold text-gray-800">{exp.title}</h4>
                    <p className={`${template.color.replace('bg-', 'text-')} font-medium`}>{exp.company}</p>
                  </div>
                  <span className="text-gray-500 text-sm">{exp.period}</span>
                </div>
                <p className="text-gray-600">{exp.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Education */}
        <section className="mb-8">
          <h3 className={`text-2xl font-bold mb-4 ${template.color.replace('bg-', 'text-')}`}>
            التعليم
          </h3>
          {sampleData.education.map((edu, index) => (
            <div key={index} className="border-r-4 border-gray-200 pr-4">
              <div className="flex justify-between items-start">
                <div>
                  <h4 className="font-bold text-gray-800">{edu.degree}</h4>
                  <p className={`${template.color.replace('bg-', 'text-')} font-medium`}>{edu.school}</p>
                </div>
                <span className="text-gray-500 text-sm">{edu.period}</span>
              </div>
            </div>
          ))}
        </section>

        {/* Skills & Languages */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <section>
            <h3 className={`text-2xl font-bold mb-4 ${template.color.replace('bg-', 'text-')}`}>
              المهارات
            </h3>
            <div className="flex flex-wrap gap-2">
              {sampleData.skills.map((skill, index) => (
                <span
                  key={index}
                  className={`${template.color} text-white px-3 py-1 rounded-full text-sm`}
                >
                  {skill}
                </span>
              ))}
            </div>
          </section>

          <section>
            <h3 className={`text-2xl font-bold mb-4 ${template.color.replace('bg-', 'text-')}`}>
              اللغات
            </h3>
            <div className="space-y-2">
              {sampleData.languages.map((lang, index) => (
                <div key={index} className="text-gray-700">{lang}</div>
              ))}
            </div>
          </section>
        </div>
      </div>
    </div>
  );

  const renderClassicTemplate = () => (
    <div className="bg-white h-full overflow-y-auto">
      <div className="max-w-4xl mx-auto p-8">
        {/* Header */}
        <div className="text-center border-b-2 border-gray-300 pb-6 mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">{sampleData.name}</h1>
          <h2 className="text-xl text-gray-600 mb-4">{sampleData.title}</h2>
          <div className="flex justify-center gap-6 text-sm text-gray-600">
            <span>📧 {sampleData.email}</span>
            <span>📞 {sampleData.phone}</span>
            <span>📍 {sampleData.location}</span>
          </div>
        </div>

        {/* Summary */}
        <section className="mb-8">
          <h3 className="text-xl font-bold text-gray-800 mb-3 border-b border-gray-200 pb-2">
            الملخص الشخصي
          </h3>
          <p className="text-gray-700 leading-relaxed">{sampleData.summary}</p>
        </section>

        {/* Experience */}
        <section className="mb-8">
          <h3 className="text-xl font-bold text-gray-800 mb-3 border-b border-gray-200 pb-2">
            الخبرة المهنية
          </h3>
          <div className="space-y-4">
            {sampleData.experience.map((exp, index) => (
              <div key={index}>
                <div className="flex justify-between items-start mb-1">
                  <div>
                    <h4 className="font-bold text-gray-800">{exp.title}</h4>
                    <p className="text-gray-600 font-medium">{exp.company}</p>
                  </div>
                  <span className="text-gray-500 text-sm">{exp.period}</span>
                </div>
                <p className="text-gray-600 text-sm">{exp.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Education */}
        <section className="mb-8">
          <h3 className="text-xl font-bold text-gray-800 mb-3 border-b border-gray-200 pb-2">
            التعليم
          </h3>
          {sampleData.education.map((edu, index) => (
            <div key={index} className="flex justify-between items-start">
              <div>
                <h4 className="font-bold text-gray-800">{edu.degree}</h4>
                <p className="text-gray-600">{edu.school}</p>
              </div>
              <span className="text-gray-500 text-sm">{edu.period}</span>
            </div>
          ))}
        </section>

        {/* Skills & Languages */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <section>
            <h3 className="text-xl font-bold text-gray-800 mb-3 border-b border-gray-200 pb-2">
              المهارات التقنية
            </h3>
            <div className="space-y-1">
              {sampleData.skills.map((skill, index) => (
                <div key={index} className="text-gray-700">• {skill}</div>
              ))}
            </div>
          </section>

          <section>
            <h3 className="text-xl font-bold text-gray-800 mb-3 border-b border-gray-200 pb-2">
              اللغات
            </h3>
            <div className="space-y-1">
              {sampleData.languages.map((lang, index) => (
                <div key={index} className="text-gray-700">• {lang}</div>
              ))}
            </div>
          </section>
        </div>
      </div>
    </div>
  );

  const renderCreativeTemplate = () => (
    <div className="bg-gradient-to-br from-purple-50 to-pink-50 h-full overflow-y-auto">
      <div className="max-w-4xl mx-auto p-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
          <div className="flex items-center gap-6">
            <div className={`w-24 h-24 ${template.color} rounded-full flex items-center justify-center text-white text-4xl`}>
              👤
            </div>
            <div>
              <h1 className="text-4xl font-bold text-gray-800 mb-2">{sampleData.name}</h1>
              <h2 className={`text-xl ${template.color.replace('bg-', 'text-')} mb-4`}>{sampleData.title}</h2>
              <div className="flex gap-4 text-sm text-gray-600">
                <span>📧 {sampleData.email}</span>
                <span>📞 {sampleData.phone}</span>
                <span>📍 {sampleData.location}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Summary */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
          <h3 className={`text-2xl font-bold mb-4 ${template.color.replace('bg-', 'text-')}`}>
            ✨ نبذة إبداعية
          </h3>
          <p className="text-gray-700 leading-relaxed">{sampleData.summary}</p>
        </div>

        {/* Experience */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
          <h3 className={`text-2xl font-bold mb-4 ${template.color.replace('bg-', 'text-')}`}>
            🚀 رحلتي المهنية
          </h3>
          <div className="space-y-6">
            {sampleData.experience.map((exp, index) => (
              <div key={index} className={`border-r-4 ${template.color.replace('bg-', 'border-')} pr-4`}>
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="font-bold text-gray-800">{exp.title}</h4>
                    <p className={`${template.color.replace('bg-', 'text-')} font-medium`}>{exp.company}</p>
                  </div>
                  <span className={`${template.color} text-white px-3 py-1 rounded-full text-sm`}>
                    {exp.period}
                  </span>
                </div>
                <p className="text-gray-600">{exp.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Skills & Education */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h3 className={`text-2xl font-bold mb-4 ${template.color.replace('bg-', 'text-')}`}>
              ⚡ قوتي الخارقة
            </h3>
            <div className="flex flex-wrap gap-2">
              {sampleData.skills.map((skill, index) => (
                <span
                  key={index}
                  className={`${template.color} text-white px-4 py-2 rounded-full text-sm font-medium`}
                >
                  {skill}
                </span>
              ))}
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-6">
            <h3 className={`text-2xl font-bold mb-4 ${template.color.replace('bg-', 'text-')}`}>
              🎓 التعلم والنمو
            </h3>
            {sampleData.education.map((edu, index) => (
              <div key={index} className="mb-4">
                <h4 className="font-bold text-gray-800">{edu.degree}</h4>
                <p className={`${template.color.replace('bg-', 'text-')} font-medium`}>{edu.school}</p>
                <span className="text-gray-500 text-sm">{edu.period}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderMinimalTemplate = () => (
    <div className="bg-white h-full overflow-y-auto">
      <div className="max-w-3xl mx-auto p-12">
        {/* Header */}
        <div className="mb-12">
          <h1 className="text-5xl font-light text-gray-800 mb-4">{sampleData.name}</h1>
          <h2 className="text-2xl text-gray-600 mb-6">{sampleData.title}</h2>
          <div className="flex gap-8 text-gray-600">
            <span>{sampleData.email}</span>
            <span>{sampleData.phone}</span>
            <span>{sampleData.location}</span>
          </div>
        </div>

        {/* Summary */}
        <section className="mb-12">
          <p className="text-gray-700 text-lg leading-relaxed">{sampleData.summary}</p>
        </section>

        {/* Experience */}
        <section className="mb-12">
          <h3 className="text-2xl font-light text-gray-800 mb-6">الخبرة</h3>
          <div className="space-y-8">
            {sampleData.experience.map((exp, index) => (
              <div key={index}>
                <div className="flex justify-between items-baseline mb-2">
                  <h4 className="text-xl font-medium text-gray-800">{exp.title}</h4>
                  <span className="text-gray-500">{exp.period}</span>
                </div>
                <p className="text-gray-600 mb-2">{exp.company}</p>
                <p className="text-gray-600">{exp.description}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Education */}
        <section className="mb-12">
          <h3 className="text-2xl font-light text-gray-800 mb-6">التعليم</h3>
          {sampleData.education.map((edu, index) => (
            <div key={index}>
              <div className="flex justify-between items-baseline">
                <h4 className="text-xl font-medium text-gray-800">{edu.degree}</h4>
                <span className="text-gray-500">{edu.period}</span>
              </div>
              <p className="text-gray-600">{edu.school}</p>
            </div>
          ))}
        </section>

        {/* Skills */}
        <section>
          <h3 className="text-2xl font-light text-gray-800 mb-6">المهارات</h3>
          <div className="flex flex-wrap gap-4">
            {sampleData.skills.map((skill, index) => (
              <span key={index} className="text-gray-700 border-b border-gray-300 pb-1">
                {skill}
              </span>
            ))}
          </div>
        </section>
      </div>
    </div>
  );

  const renderTemplate = () => {
    switch (template.category) {
      case 'modern':
        return renderModernTemplate();
      case 'classic':
        return renderClassicTemplate();
      case 'creative':
        return renderCreativeTemplate();
      case 'minimal':
        return renderMinimalTemplate();
      default:
        return renderModernTemplate();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-6xl h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center gap-4">
            <h2 className="text-2xl font-bold text-gray-800">معاينة: {template.name}</h2>
            {template.premium && (
              <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                ⭐ مميز
              </span>
            )}
          </div>

          <div className="flex items-center gap-4">
            {/* View Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setCurrentView('desktop')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  currentView === 'desktop'
                    ? 'bg-white text-gray-800 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                🖥️ حاسوب
              </button>
              <button
                onClick={() => setCurrentView('mobile')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  currentView === 'mobile'
                    ? 'bg-white text-gray-800 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                📱 هاتف
              </button>
            </div>

            {/* Actions */}
            <Link
              href={`/resume/create?template=${template.id}`}
              className={`${template.color} text-white px-6 py-2 rounded-lg font-medium hover:opacity-90 transition-opacity`}
            >
              استخدم هذا القالب
            </Link>

            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Preview Content */}
        <div className="flex-1 overflow-hidden">
          <div className={`h-full ${currentView === 'mobile' ? 'max-w-sm mx-auto' : ''}`}>
            <div className={`h-full ${currentView === 'mobile' ? 'scale-75 origin-top' : ''}`}>
              {renderTemplate()}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              <span className="font-medium">مناسب لـ:</span> {template.suitable.join(', ')}
            </div>
            <div className="flex gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-colors"
              >
                إغلاق
              </button>
              <Link
                href={`/resume/create?template=${template.id}`}
                className={`${template.color} text-white px-6 py-2 rounded-lg font-medium hover:opacity-90 transition-opacity`}
              >
                ابدأ الإنشاء
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemplatePreview;
