'use client';

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface LogoProps {
  variant?: 'white' | 'transparent';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'loading';
  className?: string;
  showText?: boolean;
  href?: string;
  textColor?: 'primary' | 'white' | 'gray';
}

const Logo = ({
  variant = 'transparent',
  size = 'md',
  className = '',
  showText = true,
  href = '/',
  textColor = 'primary'
}: LogoProps) => {
  const [imageError, setImageError] = useState(false);
  const sizeClasses = {
    xs: 'h-6 w-auto',
    sm: 'h-8 w-auto',
    md: 'h-10 w-auto',
    lg: 'h-12 w-auto',
    xl: 'h-24 w-auto',
    loading: 'h-32 w-auto'
  };

  const textSizeClasses = {
    xs: 'text-base',
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl',
    loading: 'text-4xl'
  };

  const textColorClasses = {
    primary: 'text-green-600',
    white: 'text-white',
    gray: 'text-gray-300',
    yellow: 'text-yellow-400'
  };

  // مسار الشعار حسب النوع
  const logoSrc = variant === 'white'
    ? '/images/Adsız tasarım (3)_page-0001-Photoroom.png'
    : '/images/Adsız tasarım (3)_page-0001-Photoroom.png';

  const LogoContent = () => (
    <div className={`flex items-center gap-3 ${className}`}>
      {!imageError ? (
        <Image
          src={logoSrc}
          alt="من المالك"
          width={120}
          height={60}
          className={`${sizeClasses[size]} object-contain`}
          priority
          onError={() => setImageError(true)}
        />
      ) : (
        // Fallback logo
        <div className={`${sizeClasses[size]} bg-primary-600 rounded-lg flex items-center justify-center text-white font-bold`}>
          <span className="text-lg">🤝</span>
        </div>
      )}
      {showText && (
        <span
          className={`font-bold ${textSizeClasses[size]} hidden sm:block`}
          style={{
            fontFamily: 'Cairo, sans-serif',
            color: textColor === 'white' ? '#fde047' :
                   textColor === 'yellow' ? '#fde047' : '#10b981'
          }}
        >
          من المالك
        </span>
      )}
    </div>
  );

  if (href) {
    return (
      <Link href={href} className="flex items-center">
        <LogoContent />
      </Link>
    );
  }

  return <LogoContent />;
};

export default Logo;
