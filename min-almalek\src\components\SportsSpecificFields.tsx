'use client';

import { useState } from 'react';

interface SportsData {
  mainCategory?: string;
  subCategory?: string;
  condition?: string;
  brand?: string;
  size?: string;
  type?: string;
}

interface SportsSpecificFieldsProps {
  data: SportsData;
  onChange: (data: SportsData) => void;
}

const sportsCategories = {
  'equipment': {
    name: 'معدات وأجهزة رياضية',
    subCategories: [
      'أجهزة جيم منزلية (مشايات، دراجات ثابتة، أجهزة أوزان)',
      'أوزان ودنابل',
      'أدوات تمارين مقاومة',
      'حصائر تمارين (يوغا، بيلاتس)',
      'كرات تمارين',
      'معدات تمارين البطن'
    ]
  },
  'clothing-shoes': {
    name: 'ملابس وأحذية رياضية',
    subCategories: [
      'تيشيرتات رياضية',
      'بناطيل/شورتات رياضية',
      'أحذية جري',
      'أحذية كرة قدم',
      'ملابس سباحة',
      'ملابس يوجا وبيلاتس'
    ]
  },
  'outdoor-indoor': {
    name: 'رياضات خارجية وداخلية',
    subCategories: [
      'كرة القدم',
      'كرة السلة',
      'كرة الطائرة',
      'التنس وتنس الطاولة',
      'الجولف',
      'البيسبول'
    ]
  },
  'camping': {
    name: 'معدات التخييم والرحلات',
    subCategories: [
      'خيام',
      'حقائب ظهر',
      'أكياس نوم',
      'أدوات طبخ للرحلات',
      'كراسي وطاولات تخييم',
      'مصابيح ومستلزمات إضاءة للرحلات'
    ]
  },
  'bicycles': {
    name: 'الدراجات',
    subCategories: [
      'دراجات هوائية للكبار',
      'دراجات هوائية للأطفال',
      'دراجات جبلية',
      'دراجات سباق',
      'إكسسوارات الدراجات (خوذات، أضواء، أقفال)'
    ]
  },
  'games-hobbies': {
    name: 'ألعاب وهوايات',
    subCategories: [
      'ألعاب إلكترونية',
      'ألعاب لوحية (Board Games)',
      'ألعاب تركيب (ليغو، بازل)',
      'سيارات وطائرات تحكم عن بعد',
      'مجسمات وهوايات تجميع'
    ]
  },
  'fishing': {
    name: 'مستلزمات الصيد',
    subCategories: [
      'صنارات صيد',
      'شباك صيد',
      'أدوات تغليف الطُعم',
      'صناديق تخزين معدات الصيد'
    ]
  },
  'water-sports': {
    name: 'مستلزمات السباحة والرياضات المائية',
    subCategories: [
      'معدات غوص',
      'نظارات سباحة',
      'زعانف',
      'ألواح تزلج مائي',
      'سترات نجاة'
    ]
  }
};

export default function SportsSpecificFields({ data, onChange }: SportsSpecificFieldsProps) {
  const handleChange = (field: string, value: any) => {
    const newData = {
      mainCategory: '',
      subCategory: '',
      condition: '',
      brand: '',
      size: '',
      type: '',
      ...data,
      [field]: value
    };
    onChange(newData);
  };

  const conditionOptions = ['جديد', 'مستعمل - ممتاز', 'مستعمل - جيد', 'مستعمل - مقبول'];
  const brandOptions = ['Nike', 'Adidas', 'Puma', 'Under Armour', 'Reebok', 'New Balance', 'Wilson', 'Spalding', 'Coleman', 'The North Face', 'أخرى'];
  const sizeOptions = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47'];

  return (
    <div className="space-y-6">
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-green-800 mb-4">
          تفاصيل الرياضة والترفيه
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* الفئة الرئيسية */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الفئة الرئيسية *
            </label>
            <select
              value={data?.mainCategory || ''}
              onChange={(e) => handleChange('mainCategory', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
              required
            >
              <option value="">اختر الفئة الرئيسية</option>
              {Object.entries(sportsCategories).map(([key, category]) => (
                <option key={key} value={key}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* الفئة الفرعية */}
          {data.mainCategory && sportsCategories[data.mainCategory as keyof typeof sportsCategories] && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الفئة الفرعية *
              </label>
              <select
                value={data?.subCategory || ''}
                onChange={(e) => handleChange('subCategory', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                required
              >
                <option value="">اختر الفئة الفرعية</option>
                {sportsCategories[data.mainCategory as keyof typeof sportsCategories].subCategories.map((sub, index) => (
                  <option key={index} value={sub}>
                    {sub}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* الحالة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الحالة *
            </label>
            <select
              value={data?.condition || ''}
              onChange={(e) => handleChange('condition', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
              required
            >
              <option value="">اختر الحالة</option>
              {conditionOptions.map((condition) => (
                <option key={condition} value={condition}>
                  {condition}
                </option>
              ))}
            </select>
          </div>

          {/* الماركة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الماركة
            </label>
            <select
              value={data?.brand || ''}
              onChange={(e) => handleChange('brand', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="">اختر الماركة</option>
              {brandOptions.map((brand) => (
                <option key={brand} value={brand}>
                  {brand}
                </option>
              ))}
            </select>
          </div>

          {/* المقاس */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              المقاس
            </label>
            <select
              value={data?.size || ''}
              onChange={(e) => handleChange('size', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="">اختر المقاس</option>
              {sizeOptions.map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
          </div>

          {/* النوع */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              النوع/التفاصيل الإضافية
            </label>
            <input
              type="text"
              value={data?.type || ''}
              onChange={(e) => handleChange('type', e.target.value)}
              placeholder="مثال: للرجال، للنساء، للأطفال، مقاوم للماء..."
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
