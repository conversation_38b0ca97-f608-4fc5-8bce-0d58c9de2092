'use client';

interface CarData {
  listingType?: 'sale' | 'rent';
  rentPeriod?: 'daily' | 'weekly' | 'monthly';
  carType?: 'private' | 'commercial';
  bodyType?: 'sedan' | 'coupe' | 'truck' | 'suv' | 'hatchback' | 'wagon';
  driveType?: 'front' | 'rear' | 'awd' | '4wd';
  engineSize?: string;
  fuelType?: 'gasoline' | 'diesel' | 'electric' | 'hybrid';
  transmission?: 'automatic' | 'manual';
  year?: number;
  brand?: string;
  model?: string;
  features?: string[];
  insurance?: 'full' | 'basic' | 'none';
  insuranceDetails?: string;
}

interface CarSummaryProps {
  carData: CarData;
}

export default function CarSummary({ carData }: CarSummaryProps) {
  const getListingTypeText = (listingType?: string) => {
    switch (listingType) {
      case 'sale': return { text: 'للبيع', icon: '💰', color: 'text-green-600' };
      case 'rent': return { text: 'للإيجار', icon: '🔄', color: 'text-blue-600' };
      default: return { text: 'غير محدد', icon: '❓', color: 'text-gray-400' };
    }
  };

  const getRentPeriodText = (rentPeriod?: string) => {
    switch (rentPeriod) {
      case 'daily': return { text: 'إيجار يومي', icon: '📅' };
      case 'weekly': return { text: 'إيجار أسبوعي', icon: '📆' };
      case 'monthly': return { text: 'إيجار شهري', icon: '🗓️' };
      default: return { text: 'غير محدد', icon: '❓' };
    }
  };

  const getCarTypeText = (carType?: string) => {
    switch (carType) {
      case 'private': return { text: 'خصوصي', icon: '🚗', color: 'text-blue-600' };
      case 'commercial': return { text: 'عمومي', icon: '🚕', color: 'text-orange-600' };
      default: return { text: 'غير محدد', icon: '❓', color: 'text-gray-400' };
    }
  };

  const getInsuranceText = (insurance?: string) => {
    switch (insurance) {
      case 'full': return { text: 'تأمين شامل', icon: '🛡️', color: 'text-green-600' };
      case 'basic': return { text: 'تأمين أساسي', icon: '🔰', color: 'text-blue-600' };
      case 'none': return { text: 'بدون تأمين', icon: '⚠️', color: 'text-red-600' };
      default: return { text: 'غير محدد', icon: '❓', color: 'text-gray-400' };
    }
  };



  const getBodyTypeText = (bodyType?: string) => {
    switch (bodyType) {
      case 'sedan': return { text: 'سيدان', icon: '🚗' };
      case 'coupe': return { text: 'كوبيه', icon: '🏎️' };
      case 'truck': return { text: 'شاحنة', icon: '🚚' };
      case 'suv': return { text: 'SUV', icon: '🚙' };
      case 'hatchback': return { text: 'هاتشباك', icon: '🚘' };
      case 'wagon': return { text: 'ستيشن واجن', icon: '🚐' };
      default: return { text: 'غير محدد', icon: '❓' };
    }
  };

  const getDriveTypeText = (driveType?: string) => {
    switch (driveType) {
      case 'front': return { text: 'دفع أمامي', icon: '⬆️' };
      case 'rear': return { text: 'دفع خلفي', icon: '⬇️' };
      case 'awd': return { text: 'دفع رباعي دائم', icon: '🔄' };
      case '4wd': return { text: 'دفع رباعي', icon: '🚜' };
      default: return { text: 'غير محدد', icon: '❓' };
    }
  };

  const getFuelTypeText = (fuelType?: string) => {
    switch (fuelType) {
      case 'gasoline': return { text: 'بنزين', icon: '⛽' };
      case 'diesel': return { text: 'ديزل', icon: '🛢️' };
      case 'electric': return { text: 'كهرباء', icon: '🔋' };
      case 'hybrid': return { text: 'هجين', icon: '🌱' };
      default: return { text: 'غير محدد', icon: '❓' };
    }
  };

  const getTransmissionText = (transmission?: string) => {
    switch (transmission) {
      case 'automatic': return { text: 'أوتوماتيك', icon: '🔄' };
      case 'manual': return { text: 'عادي', icon: '🎛️' };
      default: return { text: 'غير محدد', icon: '❓' };
    }
  };



  const listingType = getListingTypeText(carData.listingType);
  const rentPeriod = getRentPeriodText(carData.rentPeriod);
  const carType = getCarTypeText(carData.carType);
  const bodyType = getBodyTypeText(carData.bodyType);
  const driveType = getDriveTypeText(carData.driveType);
  const fuelType = getFuelTypeText(carData.fuelType);
  const transmission = getTransmissionText(carData.transmission);
  const insurance = getInsuranceText(carData.insurance);

  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
        <span className="text-2xl">🚗</span>
        ملخص تفاصيل السيارة
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* نوع الإعلان */}
        <div className="flex items-center gap-2">
          <span className="text-lg">{listingType.icon}</span>
          <div>
            <p className="text-sm text-gray-600">نوع الإعلان</p>
            <p className={`font-medium ${listingType.color}`}>{listingType.text}</p>
          </div>
        </div>

        {/* فترة الإيجار - تظهر فقط للإيجار */}
        {carData.listingType === 'rent' && carData.rentPeriod && (
          <div className="flex items-center gap-2">
            <span className="text-lg">{rentPeriod.icon}</span>
            <div>
              <p className="text-sm text-gray-600">فترة الإيجار</p>
              <p className="font-medium text-blue-600">{rentPeriod.text}</p>
            </div>
          </div>
        )}

        {/* نوع السيارة - يظهر فقط للبيع */}
        {carData.listingType === 'sale' && carData.carType && (
          <div className="flex items-center gap-2">
            <span className="text-lg">{carType.icon}</span>
            <div>
              <p className="text-sm text-gray-600">نوع السيارة</p>
              <p className={`font-medium ${carType.color}`}>{carType.text}</p>
            </div>
          </div>
        )}

        {/* التأمين - يظهر فقط للإيجار */}
        {carData.listingType === 'rent' && carData.insurance && (
          <div className="flex items-center gap-2">
            <span className="text-lg">{insurance.icon}</span>
            <div>
              <p className="text-sm text-gray-600">التأمين</p>
              <p className={`font-medium ${insurance.color}`}>{insurance.text}</p>
            </div>
          </div>
        )}

        {/* المعلومات الأساسية */}
        {carData.brand && (
          <div className="flex items-center gap-2">
            <span className="text-lg">🏷️</span>
            <div>
              <p className="text-sm text-gray-600">الماركة</p>
              <p className="font-medium">{carData.brand}</p>
            </div>
          </div>
        )}

        {carData.model && (
          <div className="flex items-center gap-2">
            <span className="text-lg">📝</span>
            <div>
              <p className="text-sm text-gray-600">الموديل</p>
              <p className="font-medium">{carData.model}</p>
            </div>
          </div>
        )}

        {carData.year && (
          <div className="flex items-center gap-2">
            <span className="text-lg">📅</span>
            <div>
              <p className="text-sm text-gray-600">سنة الصنع</p>
              <p className="font-medium">
                {carData.year}
                {carData.year < 1990 && (
                  <span className="text-amber-600 text-xs mr-2">🏆 كلاسيكية</span>
                )}
                {carData.year >= 2020 && (
                  <span className="text-green-600 text-xs mr-2">✨ حديثة</span>
                )}
              </p>
            </div>
          </div>
        )}





        {/* نوع الهيكل */}
        {carData.bodyType && (
          <div className="flex items-center gap-2">
            <span className="text-lg">{bodyType.icon}</span>
            <div>
              <p className="text-sm text-gray-600">نوع الهيكل</p>
              <p className="font-medium">{bodyType.text}</p>
            </div>
          </div>
        )}

        {/* نوع الدفع */}
        {carData.driveType && (
          <div className="flex items-center gap-2">
            <span className="text-lg">{driveType.icon}</span>
            <div>
              <p className="text-sm text-gray-600">نوع الدفع</p>
              <p className="font-medium">{driveType.text}</p>
            </div>
          </div>
        )}

        {/* سعة المحرك */}
        {carData.engineSize && (
          <div className="flex items-center gap-2">
            <span className="text-lg">🔧</span>
            <div>
              <p className="text-sm text-gray-600">سعة المحرك</p>
              <p className="font-medium">{carData.engineSize}</p>
            </div>
          </div>
        )}

        {/* نوع الوقود */}
        {carData.fuelType && (
          <div className="flex items-center gap-2">
            <span className="text-lg">{fuelType.icon}</span>
            <div>
              <p className="text-sm text-gray-600">نوع الوقود</p>
              <p className="font-medium">{fuelType.text}</p>
            </div>
          </div>
        )}

        {/* ناقل الحركة */}
        {carData.transmission && (
          <div className="flex items-center gap-2">
            <span className="text-lg">{transmission.icon}</span>
            <div>
              <p className="text-sm text-gray-600">ناقل الحركة</p>
              <p className="font-medium">{transmission.text}</p>
            </div>
          </div>
        )}




      </div>

      {/* المميزات */}
      {carData.features && carData.features.length > 0 && (
        <div className="mt-6">
          <h4 className="font-medium text-gray-800 mb-3 flex items-center gap-2">
            <span className="text-lg">⭐</span>
            المميزات الإضافية
          </h4>
          <div className="flex flex-wrap gap-2">
            {carData.features.map((feature, index) => (
              <span
                key={index}
                className="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full border border-green-200"
              >
                ✓ {feature}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* رسالة تشجيعية */}
      <div className="mt-6 p-4 bg-white rounded-lg border border-blue-200">
        <p className="text-sm text-blue-800 flex items-center gap-2">
          <span className="text-lg">💡</span>
          <span>
            {carData.listingType === 'rent'
              ? 'تم إضافة تفاصيل شاملة للسيارة! هذا سيساعد المستأجرين في اتخاذ قرار أفضل.'
              : 'تم إضافة تفاصيل شاملة للسيارة! هذا سيساعد المشترين في اتخاذ قرار أفضل.'
            }
          </span>
        </p>

        {carData.listingType === 'rent' && (
          <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-800 flex items-center gap-2">
              <span className="text-lg">🔄</span>
              <span>
                <strong>إيجار السيارات:</strong> تأكد من ذكر شروط الإيجار والتأمين في الوصف
              </span>
            </p>
          </div>
        )}

        {/* تفاصيل التأمين */}
        {carData.listingType === 'rent' && carData.insuranceDetails && (
          <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>تفاصيل التأمين:</strong> {carData.insuranceDetails}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
