'use client';

import { useState } from 'react';
import CashAppLogo from './CashAppLogo';
import { PAYMENT_METHODS } from '@/lib/pricing-constants';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  planName: string;
  planPrice: string;
  planCurrency: string;
  planType?: 'individual' | 'business';
  onSuccess?: () => void;
}

const PaymentModal = ({ isOpen, onClose, planName, planPrice, planCurrency, planType = 'individual', onSuccess }: PaymentModalProps) => {
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentStep, setPaymentStep] = useState<'method' | 'form' | 'processing' | 'success'>('method');
  const [processingMessage, setProcessingMessage] = useState('جاري معالجة الدفع...');
  const [formData, setFormData] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    email: '',
    phone: '',
    paypalEmail: '',
    applePayEmail: ''
  });

  if (!isOpen) return null;

  // استخدام وسائل الدفع الاحترافية من المكتبة
  const paymentMethods = PAYMENT_METHODS;

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCardNumber(e.target.value);
    handleInputChange('cardNumber', formatted);
  };

  const handleExpiryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatExpiryDate(e.target.value);
    handleInputChange('expiryDate', formatted);
  };

  const handleMethodSelect = (methodId: string) => {
    setSelectedMethod(methodId);
    // جميع طرق الدفع تحتاج نماذج الآن
    setPaymentStep('form');
  };

  const handlePayment = async () => {
    setPaymentStep('processing');
    setIsProcessing(true);

    // تحديد رسالة المعالجة حسب طريقة الدفع
    const paymentMethod = paymentMethods.find(m => m.id === selectedMethod);
    if (paymentMethod) {
      switch (selectedMethod) {
        case 'visa':
        case 'mastercard':
          setProcessingMessage('جاري معالجة الدفع عبر البطاقة الائتمانية...');
          break;
        case 'paypal':
          setProcessingMessage('جاري معالجة الدفع عبر PayPal...');
          break;
        case 'applepay':
          setProcessingMessage('جاري معالجة الدفع عبر Apple Pay...');
          break;
        case 'cashapp':
          setProcessingMessage('جاري معالجة الدفع عبر Cash App...');
          break;
        default:
          setProcessingMessage('جاري معالجة الدفع...');
      }
    }

    // محاكاة معالجة الدفع الفعلية مع خطوات متعددة
    try {
      // الخطوة 1: التحقق من البيانات
      setProcessingMessage('جاري التحقق من البيانات...');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // الخطوة 2: الاتصال ببوابة الدفع
      setProcessingMessage('جاري الاتصال ببوابة الدفع...');
      await new Promise(resolve => setTimeout(resolve, 1500));

      // الخطوة 3: معالجة الدفع
      setProcessingMessage('جاري معالجة عملية الدفع...');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // الخطوة 4: تأكيد العملية
      setProcessingMessage('جاري تأكيد العملية...');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // هنا سيتم إرسال البيانات إلى معالج الدفع الحقيقي
      console.log('Payment data:', {
        method: selectedMethod,
        planName,
        planPrice,
        planCurrency,
        formData,
        timestamp: new Date().toISOString()
      });

      // محاكاة نجاح الدفع
      setPaymentStep('success');
      setIsProcessing(false);

      // إغلاق النافذة بعد 4 ثوان
      setTimeout(() => {
        onSuccess?.();
        onClose();
        resetModal();
      }, 4000);

    } catch (error) {
      console.error('Payment failed:', error);
      alert('فشل في معالجة الدفع. يرجى المحاولة مرة أخرى.');
      setPaymentStep('form');
      setIsProcessing(false);
    }
  };

  const resetModal = () => {
    setPaymentStep('method');
    setSelectedMethod('');
    setIsProcessing(false);
    setProcessingMessage('جاري معالجة الدفع...');
    setFormData({
      cardNumber: '',
      expiryDate: '',
      cvv: '',
      cardholderName: '',
      email: '',
      phone: '',
      paypalEmail: '',
      applePayEmail: ''
    });
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold text-gray-800">
                {paymentStep === 'method' && '💳 اختر طريقة الدفع'}
                {paymentStep === 'form' && '📝 أدخل بيانات الدفع'}
                {paymentStep === 'processing' && '⏳ جاري المعالجة...'}
                {paymentStep === 'success' && '✅ تم بنجاح!'}
              </h3>
              <div className="flex items-center gap-2 mt-1">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  planType === 'business'
                    ? 'bg-purple-100 text-purple-700'
                    : 'bg-blue-100 text-blue-700'
                }`}>
                  {planType === 'business' ? '🏢 للشركات' : '👤 للأفراد'}
                </span>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
              disabled={isProcessing}
            >
              ×
            </button>
          </div>

          {/* Plan Info */}
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <h4 className="font-semibold text-gray-800">{planName}</h4>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-2xl font-bold text-primary-600">{planPrice}</span>
              <span className="text-gray-600">{planCurrency}</span>
            </div>
          </div>

          {/* Progress Steps */}
          {paymentStep !== 'method' && (
            <div className="flex items-center gap-2 mt-4">
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <div className="flex-1 h-1 bg-gray-200 rounded">
                <div className={`h-full bg-primary-500 rounded transition-all duration-300 ${
                  paymentStep === 'form' ? 'w-full' :
                  paymentStep === 'processing' ? 'w-full' :
                  paymentStep === 'success' ? 'w-full' : 'w-0'
                }`}></div>
              </div>
              <div className={`w-3 h-3 rounded-full ${
                paymentStep === 'form' ? 'bg-primary-500' :
                paymentStep === 'processing' ? 'bg-green-500' :
                paymentStep === 'success' ? 'bg-green-500' : 'bg-gray-300'
              }`}></div>
              <div className="flex-1 h-1 bg-gray-200 rounded">
                <div className={`h-full bg-primary-500 rounded transition-all duration-300 ${
                  paymentStep === 'processing' ? 'w-full' :
                  paymentStep === 'success' ? 'w-full' : 'w-0'
                }`}></div>
              </div>
              <div className={`w-3 h-3 rounded-full ${
                paymentStep === 'processing' ? 'bg-primary-500' :
                paymentStep === 'success' ? 'bg-green-500' : 'bg-gray-300'
              }`}></div>
              <div className="flex-1 h-1 bg-gray-200 rounded">
                <div className={`h-full bg-green-500 rounded transition-all duration-300 ${
                  paymentStep === 'success' ? 'w-full' : 'w-0'
                }`}></div>
              </div>
              <div className={`w-3 h-3 rounded-full ${paymentStep === 'success' ? 'bg-green-500' : 'bg-gray-300'}`}></div>
            </div>
          )}
        </div>

        {/* Content based on payment step */}
        <div className="p-6">
          {paymentStep === 'method' && (
            <div className="space-y-3">
              {paymentMethods.map((method) => (
                <button
                  key={method.id}
                  onClick={() => handleMethodSelect(method.id)}
                  disabled={isProcessing}
                  className={`w-full p-4 rounded-lg border-2 transition-all hover:shadow-md ${
                    selectedMethod === method.id && isProcessing
                      ? 'border-primary-500 bg-primary-50'
                      : `${method.borderColor} hover:border-gray-300`
                  } ${isProcessing ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'}`}
                >
                  <div className="flex items-center gap-4">
                    <div className={`w-16 h-10 ${method.bgColor} border ${method.borderColor} rounded-md flex items-center justify-center shadow-sm overflow-hidden`}>
                      {method.id === 'cashapp' ? (
                        <CashAppLogo size="sm" />
                      ) : method.logo ? (
                        <img
                          src={method.logo}
                          alt={method.name}
                          className="w-12 h-6 object-contain"
                        />
                      ) : (
                        <span className="text-lg font-bold text-gray-700">{method.nameAr}</span>
                      )}
                    </div>
                    <div className="flex-1 text-right">
                      <h5 className="font-semibold text-gray-800">{method.nameAr}</h5>
                      <p className="text-sm text-gray-600">{method.description}</p>
                    </div>
                    {selectedMethod === method.id && isProcessing && (
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
                    )}
                  </div>
                </button>
              ))}
            </div>
          )}

          {/* Payment Form Step */}
          {paymentStep === 'form' && (
            <div className="space-y-6">
              {/* Selected Method Header */}
              <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-lg">
                <div className={`w-12 h-8 ${paymentMethods.find(m => m.id === selectedMethod)?.bgColor} border ${paymentMethods.find(m => m.id === selectedMethod)?.borderColor} rounded-md flex items-center justify-center shadow-sm overflow-hidden`}>
                  {selectedMethod === 'cashapp' ? (
                    <CashAppLogo size="sm" />
                  ) : paymentMethods.find(m => m.id === selectedMethod)?.logo ? (
                    <img
                      src={paymentMethods.find(m => m.id === selectedMethod)?.logo}
                      alt={paymentMethods.find(m => m.id === selectedMethod)?.name}
                      className="w-10 h-5 object-contain"
                    />
                  ) : (
                    <span className="text-sm font-bold text-gray-700">{paymentMethods.find(m => m.id === selectedMethod)?.nameAr}</span>
                  )}
                </div>
                <div>
                  <h4 className="font-semibold text-gray-800">{paymentMethods.find(m => m.id === selectedMethod)?.nameAr}</h4>
                  <p className="text-sm text-gray-600">{paymentMethods.find(m => m.id === selectedMethod)?.description}</p>
                </div>
              </div>

              {/* Credit Card Forms (Visa & MasterCard) */}
              {(selectedMethod === 'visa' || selectedMethod === 'mastercard') && (
                <form onSubmit={(e) => { e.preventDefault(); handlePayment(); }} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      رقم البطاقة *
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={formData.cardNumber}
                        onChange={handleCardNumberChange}
                        placeholder="1234 5678 9012 3456"
                        maxLength={19}
                        className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-xl">
                        💳
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        تاريخ الانتهاء *
                      </label>
                      <input
                        type="text"
                        value={formData.expiryDate}
                        onChange={handleExpiryChange}
                        placeholder="MM/YY"
                        maxLength={5}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        CVV *
                      </label>
                      <input
                        type="text"
                        value={formData.cvv}
                        onChange={(e) => handleInputChange('cvv', e.target.value.replace(/\D/g, '').slice(0, 4))}
                        placeholder="123"
                        maxLength={4}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      اسم حامل البطاقة *
                    </label>
                    <input
                      type="text"
                      value={formData.cardholderName}
                      onChange={(e) => handleInputChange('cardholderName', e.target.value)}
                      placeholder="الاسم كما هو مكتوب على البطاقة"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        البريد الإلكتروني *
                      </label>
                      <input
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="<EMAIL>"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        رقم الهاتف *
                      </label>
                      <input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="+963 9XX XXX XXX"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>
                  </div>

                  <div className="flex gap-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setPaymentStep('method')}
                      className="flex-1 bg-gray-200 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
                    >
                      رجوع
                    </button>
                    <button
                      type="submit"
                      className="flex-1 bg-primary-600 text-white py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors"
                    >
                      💳 ادفع {planPrice} {planCurrency}
                    </button>
                  </div>
                </form>
              )}

              {/* PayPal Form */}
              {selectedMethod === 'paypal' && (
                <div className="space-y-4">
                  <div className="text-center py-6">
                    <div className="text-5xl mb-4">🅿️</div>
                    <h3 className="text-xl font-semibold mb-4">الدفع عبر PayPal</h3>
                    <p className="text-gray-600 mb-6">
                      أدخل بريدك الإلكتروني المرتبط بحساب PayPal
                    </p>
                  </div>

                  <form onSubmit={(e) => { e.preventDefault(); handlePayment(); }} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        البريد الإلكتروني لـ PayPal *
                      </label>
                      <input
                        type="email"
                        value={formData.paypalEmail}
                        onChange={(e) => handleInputChange('paypalEmail', e.target.value)}
                        placeholder="<EMAIL>"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>

                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <p className="text-sm text-blue-700">
                        💡 سيتم معالجة الدفع عبر PayPal بأمان تام
                      </p>
                    </div>

                    <div className="flex gap-3 pt-4">
                      <button
                        type="button"
                        onClick={() => setPaymentStep('method')}
                        className="flex-1 bg-gray-200 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
                      >
                        رجوع
                      </button>
                      <button
                        type="submit"
                        className="flex-1 bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
                      >
                        🅿️ ادفع عبر PayPal
                      </button>
                    </div>
                  </form>
                </div>
              )}

              {/* Apple Pay Form */}
              {selectedMethod === 'applepay' && (
                <div className="space-y-4">
                  <div className="text-center py-6">
                    <div className="text-5xl mb-4">🍎</div>
                    <h3 className="text-xl font-semibold mb-4">الدفع عبر Apple Pay</h3>
                    <p className="text-gray-600 mb-6">
                      تأكد من تفعيل Apple Pay على جهازك
                    </p>
                  </div>

                  <form onSubmit={(e) => { e.preventDefault(); handlePayment(); }} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        البريد الإلكتروني *
                      </label>
                      <input
                        type="email"
                        value={formData.applePayEmail}
                        onChange={(e) => handleInputChange('applePayEmail', e.target.value)}
                        placeholder="<EMAIL>"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>

                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                      <p className="text-sm text-gray-700">
                        💡 استخدم Touch ID أو Face ID لإتمام عملية الدفع
                      </p>
                    </div>

                    <div className="flex gap-3 pt-4">
                      <button
                        type="button"
                        onClick={() => setPaymentStep('method')}
                        className="flex-1 bg-gray-200 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
                      >
                        رجوع
                      </button>
                      <button
                        type="submit"
                        className="flex-1 bg-black text-white py-3 rounded-lg font-semibold hover:bg-gray-800 transition-colors"
                      >
                        🍎 ادفع عبر Apple Pay
                      </button>
                    </div>
                  </form>
                </div>
              )}

              {/* Cash App Form */}
              {selectedMethod === 'cashapp' && (
                <div className="space-y-4">
                  <div className="text-center py-6">
                    <div className="flex justify-center mb-4">
                      <CashAppLogo size="lg" />
                    </div>
                    <h3 className="text-xl font-semibold mb-4">الدفع عبر Cash App</h3>
                    <p className="text-gray-600 mb-6">
                      أدخل معرف Cash App الخاص بك
                    </p>
                  </div>

                  <form onSubmit={(e) => { e.preventDefault(); handlePayment(); }} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        معرف Cash App *
                      </label>
                      <input
                        type="text"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="$YourCashTag"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>

                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <p className="text-sm text-green-700">
                        💡 تأكد من تثبيت تطبيق Cash App على جهازك
                      </p>
                    </div>

                    <div className="flex gap-3 pt-4">
                      <button
                        type="button"
                        onClick={() => setPaymentStep('method')}
                        className="flex-1 bg-gray-200 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
                      >
                        رجوع
                      </button>
                      <button
                        type="submit"
                        className="flex-1 bg-green-600 text-white py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors"
                      >
                        💰 ادفع عبر Cash App
                      </button>
                    </div>
                  </form>
                </div>
              )}
            </div>
          )}

          {paymentStep === 'processing' && (
            <div className="text-center py-8">
              <div className="relative mb-6">
                <div className="animate-spin rounded-full h-20 w-20 border-4 border-gray-200 border-t-primary-600 mx-auto"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-2xl">💳</span>
                  </div>
                </div>
              </div>

              <h4 className="text-xl font-semibold text-gray-800 mb-3">⏳ جاري المعالجة...</h4>
              <p className="text-lg text-primary-600 font-medium mb-4">{processingMessage}</p>

              <div className="max-w-sm mx-auto mb-6">
                <div className="bg-gray-200 rounded-full h-2 overflow-hidden">
                  <div className="bg-gradient-to-r from-primary-500 to-primary-600 h-full rounded-full animate-pulse"></div>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-blue-600">🔒</span>
                  <span className="font-medium text-blue-800">معالجة آمنة</span>
                </div>
                <p className="text-sm text-blue-700">
                  يتم تشفير جميع البيانات وحمايتها بأعلى معايير الأمان
                </p>
              </div>

              <div className="mt-4 text-sm text-gray-500">
                ⚠️ يرجى عدم إغلاق هذه النافذة أو تحديث الصفحة
              </div>
            </div>
          )}

          {paymentStep === 'success' && (
            <div className="text-center py-8">
              <div className="relative mb-6">
                <div className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="text-4xl animate-bounce">✅</div>
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center animate-pulse">
                  <span className="text-lg">🎉</span>
                </div>
              </div>

              <h4 className="text-2xl font-bold text-green-600 mb-3">
                تم الدفع بنجاح!
              </h4>

              <div className="bg-white border border-green-200 rounded-lg p-4 mb-4 max-w-md mx-auto">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-600">المبلغ المدفوع:</span>
                  <span className="font-bold text-green-600">{planPrice} {planCurrency}</span>
                </div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-600">طريقة الدفع:</span>
                  <span className="font-medium">{paymentMethods.find(m => m.id === selectedMethod)?.nameAr}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">الحالة:</span>
                  <span className="text-green-600 font-medium">✅ مكتمل</span>
                </div>
              </div>

              <p className="text-gray-600 mb-4">
                {planName.includes('اشتراك') || planName.includes('باقة')
                  ? 'تم تفعيل اشتراكك بنجاح! يمكنك الآن الاستفادة من جميع المميزات.'
                  : 'تم تفعيل إعلانك بنجاح! سيظهر في الموقع خلال دقائق قليلة.'}
              </p>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-green-600">📧</span>
                  <span className="font-medium text-green-800">تأكيد العملية</span>
                </div>
                <p className="text-sm text-green-700">
                  سيتم إرسال تأكيد العملية إلى بريدك الإلكتروني خلال دقائق قليلة
                </p>
              </div>

              <div className="text-sm text-gray-500 mb-4">
                سيتم إغلاق هذه النافذة تلقائياً خلال <span className="font-medium">4 ثوان</span>...
              </div>

              <button
                onClick={() => {
                  onSuccess?.();
                  onClose();
                  resetModal();
                }}
                className="bg-primary-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
              >
                إغلاق
              </button>
            </div>
          )}

          {/* Security Info - only show in method selection */}
          {paymentStep === 'method' && (
            <>
              <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-green-600">🔒</span>
                  <h5 className="font-semibold text-green-800">دفع آمن 100%</h5>
                </div>
                <ul className="text-sm text-green-700 space-y-1">
                  <li>• تشفير SSL 256-bit</li>
                  <li>• حماية بيانات البطاقة</li>
                  <li>• ضمان استرداد المال</li>
                </ul>
              </div>

              <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-blue-600">💬</span>
                  <h5 className="font-semibold text-blue-800">تحتاج مساعدة؟</h5>
                </div>
                <p className="text-sm text-blue-700 mb-2">
                  تواصل معنا للحصول على المساعدة في عملية الدفع
                </p>
                <a
                  href="https://wa.me/963988652401"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  📱 +963 988 652 401
                </a>
              </div>
            </>
          )}
        </div>


      </div>
    </div>
  );
};

export default PaymentModal;
