'use client';

import { useState } from 'react';

const SiteReview = () => {
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [userRating, setUserRating] = useState(0);
  const [userComment, setUserComment] = useState('');

  const siteStats = {
    averageRating: 4.6,
    totalReviews: 2847,
    ratingDistribution: [
      { stars: 5, count: 1823, percentage: 64 },
      { stars: 4, count: 682, percentage: 24 },
      { stars: 3, count: 227, percentage: 8 },
      { stars: 2, count: 85, percentage: 3 },
      { stars: 1, count: 30, percentage: 1 }
    ]
  };

  const recentReviews = [
    {
      id: 1,
      userName: 'أحمد محمد',
      rating: 5,
      comment: 'موقع ممتاز وسهل الاستخدام. تمكنت من بيع سيارتي خلال أسبوع واحد!',
      date: '2024-01-20',
      verified: true,
      helpful: 23
    },
    {
      id: 2,
      userName: 'سارة أحمد',
      rating: 4,
      comment: 'موقع جيد جداً، لكن أتمنى لو كان هناك المزيد من خيارات الفلترة.',
      date: '2024-01-18',
      verified: true,
      helpful: 15
    },
    {
      id: 3,
      userName: 'محمد علي',
      rating: 5,
      comment: 'أفضل موقع للإعلانات في سوريا. خدمة عملاء ممتازة ودعم فني سريع.',
      date: '2024-01-15',
      verified: false,
      helpful: 31
    },
    {
      id: 4,
      userName: 'فاطمة حسن',
      rating: 4,
      comment: 'وجدت الشقة التي أبحث عنها بسهولة. الموقع منظم وواضح.',
      date: '2024-01-12',
      verified: true,
      helpful: 18
    }
  ];

  const features = [
    { name: 'سهولة الاستخدام', rating: 4.7 },
    { name: 'جودة الإعلانات', rating: 4.5 },
    { name: 'سرعة الموقع', rating: 4.6 },
    { name: 'خدمة العملاء', rating: 4.4 },
    { name: 'الأمان', rating: 4.8 },
    { name: 'التصميم', rating: 4.6 }
  ];

  const renderStars = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-xl'
    };

    return (
      <div className={`flex items-center gap-1 ${sizeClasses[size]}`}>
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={star <= rating ? 'text-yellow-400' : 'text-gray-300'}
          >
            ⭐
          </span>
        ))}
      </div>
    );
  };

  const handleSubmitReview = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('تقييم جديد:', { rating: userRating, comment: userComment });
    setIsReviewModalOpen(false);
    setUserRating(0);
    setUserComment('');
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">تقييمات المستخدمين</h2>
          <p className="text-lg text-gray-600">ماذا يقول مستخدمونا عن موقع من المالك</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* ملخص التقييمات */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-lg p-6 sticky top-6">
              <div className="text-center mb-6">
                <div className="text-5xl font-bold text-primary-600 mb-2">
                  {siteStats.averageRating}
                </div>
                {renderStars(siteStats.averageRating, 'lg')}
                <div className="text-gray-600 mt-2">
                  من أصل {siteStats.totalReviews.toLocaleString()} تقييم
                </div>
              </div>

              {/* توزيع التقييمات */}
              <div className="space-y-3 mb-6">
                {siteStats.ratingDistribution.map((item) => (
                  <div key={item.stars} className="flex items-center gap-3">
                    <span className="text-sm text-gray-600 w-8">
                      {item.stars} ⭐
                    </span>
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-yellow-400 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${item.percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 w-12">
                      {item.count}
                    </span>
                  </div>
                ))}
              </div>

              {/* تقييم المميزات */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-800 mb-4">تقييم المميزات</h4>
                <div className="space-y-3">
                  {features.map((feature, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-700">{feature.name}</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-gray-800">
                          {feature.rating}
                        </span>
                        {renderStars(feature.rating, 'sm')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <button
                onClick={() => setIsReviewModalOpen(true)}
                className="w-full bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 transition-colors font-semibold"
              >
                أضف تقييمك
              </button>
            </div>
          </div>

          {/* قائمة التقييمات */}
          <div className="lg:col-span-2">
            <div className="space-y-6">
              {recentReviews.map((review) => (
                <div key={review.id} className="bg-white rounded-xl shadow-md p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                        <span className="text-primary-600 font-semibold">
                          {review.userName.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-gray-800">
                            {review.userName}
                          </span>
                          {review.verified && (
                            <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                              ✓ موثق
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-gray-600">{review.date}</div>
                      </div>
                    </div>
                    {renderStars(review.rating)}
                  </div>

                  <p className="text-gray-700 leading-relaxed mb-4">
                    {review.comment}
                  </p>

                  <div className="flex items-center justify-between">
                    <button className="flex items-center gap-2 text-sm text-gray-600 hover:text-primary-600 transition-colors">
                      <span>👍</span>
                      <span>مفيد ({review.helpful})</span>
                    </button>
                    <button className="text-sm text-gray-600 hover:text-primary-600 transition-colors">
                      إبلاغ
                    </button>
                  </div>
                </div>
              ))}

              <div className="text-center">
                <button className="bg-gray-100 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-200 transition-colors">
                  عرض المزيد من التقييمات
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* إحصائيات إضافية */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-lg p-6 text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">96%</div>
            <div className="text-gray-600">معدل الرضا</div>
          </div>
          <div className="bg-white rounded-xl shadow-lg p-6 text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">4.6</div>
            <div className="text-gray-600">متوسط التقييم</div>
          </div>
          <div className="bg-white rounded-xl shadow-lg p-6 text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">2.8K</div>
            <div className="text-gray-600">إجمالي التقييمات</div>
          </div>
          <div className="bg-white rounded-xl shadow-lg p-6 text-center">
            <div className="text-3xl font-bold text-orange-600 mb-2">89%</div>
            <div className="text-gray-600">يوصون بالموقع</div>
          </div>
        </div>
      </div>

      {/* نافذة إضافة تقييم */}
      {isReviewModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-800">أضف تقييمك</h3>
              <button
                onClick={() => setIsReviewModalOpen(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>

            <form onSubmit={handleSubmitReview} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  التقييم
                </label>
                <div className="flex gap-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      type="button"
                      onClick={() => setUserRating(star)}
                      className={`text-2xl ${
                        star <= userRating ? 'text-yellow-400' : 'text-gray-300'
                      } hover:text-yellow-400 transition-colors`}
                    >
                      ⭐
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  التعليق
                </label>
                <textarea
                  value={userComment}
                  onChange={(e) => setUserComment(e.target.value)}
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="شاركنا تجربتك مع موقع من المالك..."
                  required
                />
              </div>

              <div className="flex gap-3">
                <button
                  type="submit"
                  disabled={userRating === 0}
                  className="flex-1 bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  إرسال التقييم
                </button>
                <button
                  type="button"
                  onClick={() => setIsReviewModalOpen(false)}
                  className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </section>
  );
};

export default SiteReview;
