'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { useNotificationHelpers } from '@/hooks/useNotificationHelpers';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ClientOnlyWrapper from '@/components/ClientOnlyWrapper';
import VerificationBadge from '@/components/VerificationBadge';
import { formatDate, getMinBirthDate } from '@/lib/dateUtils';
import { governorates } from '@/lib/data';
import MembershipBadge from '@/components/MembershipBadge';
import NewVerificationBadge from '@/components/NewVerificationBadge';

export default function ProfilePage() {
  const { user, isAuthenticated, updateProfile } = useAuth();
  const { notifyProfileUpdated } = useNotificationHelpers();

  // خيارات التخصصات للمكاتب العقارية
  const specializationOptions = [
    'العقارات السكنية',
    'العقارات التجارية',
    'العقارات الصناعية',
    'العقارات الزراعية',
    'الفيلات والقصور',
    'الشقق والمنازل',
    'المحلات التجارية',
    'المكاتب والمباني الإدارية',
    'المستودعات والمخازن',
    'الأراضي والمشاريع'
  ];

  // خيارات الخدمات للمكاتب العقارية
  const serviceOptions = [
    'بيع العقارات',
    'إيجار العقارات',
    'تقييم العقارات',
    'إدارة العقارات',
    'الاستشارات العقارية',
    'الاستشارات القانونية',
    'التمويل العقاري',
    'التأمين العقاري',
    'التسويق العقاري',
    'جولات افتراضية'
  ];

  // State variables
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const [showMembershipCard, setShowMembershipCard] = useState(false);
  const [uploadedDocuments, setUploadedDocuments] = useState<File[]>([]);
  const [isSubmittingDocuments, setIsSubmittingDocuments] = useState(false);

  // دالة للتعامل مع المصفوفات (التخصصات والخدمات)
  const handleArrayToggle = (field: string, value: string) => {
    setFormData(prev => {
      const currentArray = prev[field as keyof typeof prev] as string[];
      return {
        ...prev,
        [field]: currentArray.includes(value)
          ? currentArray.filter((item: string) => item !== value)
          : [...currentArray, value]
      };
    });
  };

  // دالة لرفع الملفات
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const newFiles = Array.from(e.target.files);
      setUploadedDocuments(prev => [...prev, ...newFiles]);
    }
  };

  // دالة لحذف ملف
  const removeDocument = (index: number) => {
    setUploadedDocuments(prev => prev.filter((_, i) => i !== index));
  };

  // دالة لإرسال الوثائق للمصادقة
  const handleSubmitDocuments = async () => {
    if (uploadedDocuments.length === 0) {
      alert('يرجى رفع الوثائق أولاً');
      return;
    }

    setIsSubmittingDocuments(true);
    try {
      // محاكاة رفع الملفات
      await new Promise(resolve => setTimeout(resolve, 2000));
      alert('تم إرسال الوثائق بنجاح! سيتم مراجعتها خلال 24-48 ساعة');
      setUploadedDocuments([]);
    } catch (error) {
      alert('حدث خطأ في إرسال الوثائق');
    } finally {
      setIsSubmittingDocuments(false);
    }
  };

  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || '',
    // معلومات فردية
    firstName: user?.individualInfo?.firstName || '',
    lastName: user?.individualInfo?.lastName || '',
    gender: user?.individualInfo?.gender || '',
    dateOfBirth: user?.individualInfo?.dateOfBirth ?
      (typeof user.individualInfo.dateOfBirth === 'string' ? user.individualInfo.dateOfBirth : user.individualInfo.dateOfBirth.toISOString().split('T')[0])
      : '',
    nationalId: user?.individualInfo?.nationalId || '',
    // معلومات الشركة
    companyName: user?.businessInfo?.companyName || '',
    businessType: user?.businessInfo?.businessType || '',
    registrationNumber: user?.businessInfo?.registrationNumber || '',
    taxNumber: user?.businessInfo?.taxNumber || '',
    establishedYear: user?.businessInfo?.establishedYear || '',
    employeeCount: user?.businessInfo?.employeeCount || '',
    website: user?.businessInfo?.website || '',
    description: user?.businessInfo?.description || '',
    // معلومات المكتب العقاري
    officeName: user?.realEstateOfficeInfo?.officeName || '',
    licenseNumber: user?.realEstateOfficeInfo?.licenseNumber || '',
    ownerName: user?.realEstateOfficeInfo?.ownerName || '',
    managerName: user?.realEstateOfficeInfo?.managerName || '',
    yearsOfExperience: user?.realEstateOfficeInfo?.yearsOfExperience || 0,
    teamSize: user?.realEstateOfficeInfo?.teamSize || 1,
    officeEstablishedYear: user?.realEstateOfficeInfo?.establishedYear || '',
    officeEmployeeCount: user?.realEstateOfficeInfo?.employeeCount || '',
    specialization: user?.realEstateOfficeInfo?.specializations || [],
    managerPhone: user?.realEstateOfficeInfo?.managerPhone || '',
    managerEmail: user?.realEstateOfficeInfo?.managerEmail || '',
    managerExperience: user?.realEstateOfficeInfo?.managerExperience || '',
    services: user?.realEstateOfficeInfo?.services || [],
    serviceAreas: user?.realEstateOfficeInfo?.serviceAreas || [],
    workingHours: user?.realEstateOfficeInfo?.workingHours || {
      sunday: { from: '09:00', to: '17:00', closed: false },
      monday: { from: '09:00', to: '17:00', closed: false },
      tuesday: { from: '09:00', to: '17:00', closed: false },
      wednesday: { from: '09:00', to: '17:00', closed: false },
      thursday: { from: '09:00', to: '17:00', closed: false },
      friday: { from: '09:00', to: '13:00', closed: false },
      saturday: { from: '', to: '', closed: true }
    },
    // العنوان
    governorate: user?.individualInfo?.address?.governorate || user?.businessInfo?.address?.governorate || user?.realEstateOfficeInfo?.address?.governorate || '',
    city: user?.individualInfo?.address?.city || user?.businessInfo?.address?.city || user?.realEstateOfficeInfo?.address?.city || '',
    area: user?.individualInfo?.address?.area || user?.businessInfo?.address?.area || user?.realEstateOfficeInfo?.address?.area || '',
    street: user?.businessInfo?.address?.street || user?.realEstateOfficeInfo?.address?.street || '',
    building: user?.realEstateOfficeInfo?.address?.building || '',
    // حقول إضافية للمكاتب العقارية
    licenseExpiry: user?.realEstateOfficeInfo?.licenseExpiryDate ?
      (user.realEstateOfficeInfo.licenseExpiryDate instanceof Date ?
        user.realEstateOfficeInfo.licenseExpiryDate.toISOString().split('T')[0] :
        user.realEstateOfficeInfo.licenseExpiryDate) : '',
    commercialRegister: '',
    commercialRegisterDate: '',
    industrialRegister: '',
    industrialRegisterDate: '',
    insuranceNumber: '',
    taxCertificate: ''
  });

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto bg-white rounded-xl shadow-lg p-8 text-center">
            <div className="text-6xl mb-4">🔒</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">تسجيل الدخول مطلوب</h2>
            <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى الملف الشخصي</p>
            <button
              onClick={() => window.location.href = '/'}
              className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              العودة للرئيسية
            </button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // تحديد شارات المستخدم بناءً على نوع الحساب والاشتراك
  const getUserBadges = () => {
    const subscriptionPlan = user.subscription?.planId || 'free';
    const userType = user.userType;

    if (userType === 'business') {
      if (subscriptionPlan === 'business-professional') {
        return ['business-premium']; // شارة ذهبية للشركات المهنية
      } else {
        return ['business-verified']; // شارة زرقاء للشركات العادية
      }
    } else if (userType === 'real-estate-office') {
      return ['real-estate-office']; // شارة فضية للمكاتب العقارية
    } else {
      // للأفراد
      if (subscriptionPlan === 'premium') {
        return ['verified-premium']; // شارة فضية للأفراد المميزين
      } else if (subscriptionPlan === 'business') {
        return ['verified-gold']; // شارة ذهبية للأفراد الأعمال
      } else {
        return ['verified-basic']; // شارة زرقاء للأفراد الأساسيين
      }
    }
  };

  const userBadges = getUserBadges();

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const updates: any = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
      };

      if (user.userType === 'individual') {
        updates.individualInfo = {
          ...user.individualInfo,
          firstName: formData.firstName,
          lastName: formData.lastName,
          gender: formData.gender,
          dateOfBirth: formData.dateOfBirth ? new Date(formData.dateOfBirth) : undefined,
          nationalId: formData.nationalId,
          address: {
            governorate: formData.governorate,
            city: formData.city,
            area: formData.area,
          },
        };
      } else if (user.userType === 'business') {
        updates.businessInfo = {
          ...user.businessInfo,
          companyName: formData.companyName,
          businessType: formData.businessType,
          registrationNumber: formData.registrationNumber,
          taxNumber: formData.taxNumber,
          establishedYear: formData.establishedYear ? parseInt(formData.establishedYear.toString()) : undefined,
          employeeCount: formData.employeeCount,
          website: formData.website,
          description: formData.description,
          address: {
            ...user.businessInfo?.address,
            governorate: formData.governorate,
            city: formData.city,
            area: formData.area,
            street: formData.street,
          },
        };
      } else if (user.userType === 'real-estate-office') {
        updates.realEstateOfficeInfo = {
          ...user.realEstateOfficeInfo,
          officeName: formData.officeName,
          licenseNumber: formData.licenseNumber,
          ownerName: formData.ownerName,
          managerName: formData.managerName,
          yearsOfExperience: parseInt(formData.yearsOfExperience.toString()),
          teamSize: parseInt(formData.teamSize.toString()),
          establishedYear: formData.establishedYear,
          employeeCount: formData.employeeCount,
          specializations: formData.specialization,
          managerPhone: formData.managerPhone,
          managerEmail: formData.managerEmail,
          managerExperience: formData.managerExperience,
          services: formData.services,
          serviceAreas: formData.serviceAreas,
          workingHours: formData.workingHours,
          address: {
            ...user.realEstateOfficeInfo?.address,
            governorate: formData.governorate,
            city: formData.city,
            area: formData.area,
            street: formData.street,
            building: formData.building,
          },
        };
      }

      await updateProfile(updates);
      notifyProfileUpdated();
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const getTabIcon = (iconType: string) => {
    switch (iconType) {
      case 'basic':
        return '👤';
      case 'contact':
        return '📞';
      case 'address':
        return '📍';
      case 'business':
        return '🏢';
      case 'office':
        return '🏘️';
      case 'specializations':
        return '⚙️';
      case 'documents':
        return '📄';
      case 'membership':
        return '🆔';
      default:
        return <span className="text-lg">📋</span>;
    }
  };

  const tabs = [
    { id: 'basic', name: 'المعلومات الأساسية', icon: 'basic' },
    { id: 'contact', name: 'معلومات الاتصال', icon: 'contact' },
    { id: 'address', name: 'العنوان', icon: 'address' },
    ...(user.userType === 'business' || user.userType === 'real-estate-office' ? [{ id: 'specializations', name: 'التخصص والخدمات', icon: 'specializations' }] : []),
    ...(user.userType === 'business' || user.userType === 'real-estate-office' ? [{ id: 'documents', name: 'الوثائق والتراخيص', icon: 'documents' }] : []),
    { id: 'membership', name: 'هوية العضوية', icon: 'membership' },
  ];

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-800">المعلومات الأساسية</h3>
          <div className="flex items-center gap-2">
            {/* الشارات الجديدة من مجلد images */}
            <NewVerificationBadge
              userType={user.userType}
              planId={user.subscription?.planId}
              size="sm"
              showTooltip={true}
            />
            <span className="text-sm text-gray-600">
              {user.userType === 'individual' && 'حساب فردي'}
              {user.userType === 'business' && 'حساب شركة'}
              {user.userType === 'real-estate-office' && 'مكتب عقاري'}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {user.userType === 'individual' ? (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الأول</label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الأخير</label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الجنس</label>
                <select
                  value={formData.gender}
                  onChange={(e) => setFormData({...formData, gender: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                >
                  <option value="">اختر الجنس</option>
                  <option value="male">ذكر</option>
                  <option value="female">أنثى</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الميلاد</label>
                <input
                  type="date"
                  value={formData.dateOfBirth}
                  max={getMinBirthDate()}
                  onChange={(e) => setFormData({...formData, dateOfBirth: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                />
              </div>
            </>
          ) : (
            <>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {user.userType === 'business' ? 'اسم الشركة' : 'اسم المكتب'}
                </label>
                <input
                  type="text"
                  value={user.userType === 'business' ? formData.companyName : formData.officeName}
                  onChange={(e) => setFormData({
                    ...formData,
                    [user.userType === 'business' ? 'companyName' : 'officeName']: e.target.value
                  })}
                  disabled={!isEditing}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                />
              </div>

              {/* معلومات إضافية للشركات */}
              {user.userType === 'business' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">نوع الشركة</label>
                    <select
                      value={formData.businessType}
                      onChange={(e) => setFormData({...formData, businessType: e.target.value})}
                      disabled={!isEditing}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                    >
                      <option value="">اختر نوع الشركة</option>
                      <option value="technology">تكنولوجيا</option>
                      <option value="construction">إنشاءات</option>
                      <option value="trading">تجارة</option>
                      <option value="services">خدمات</option>
                      <option value="manufacturing">تصنيع</option>
                      <option value="real-estate">عقارات</option>
                      <option value="restaurant">مطعم</option>
                      <option value="real-estate-office">مكتب عقاري</option>
                      <option value="other">أخرى</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">سنة التأسيس</label>
                    <input
                      type="number"
                      value={formData.establishedYear}
                      onChange={(e) => setFormData({...formData, establishedYear: e.target.value})}
                      disabled={!isEditing}
                      min="1900"
                      max={new Date().getFullYear()}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">عدد الموظفين</label>
                    <select
                      value={formData.employeeCount}
                      onChange={(e) => setFormData({...formData, employeeCount: e.target.value})}
                      disabled={!isEditing}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                    >
                      <option value="">اختر عدد الموظفين</option>
                      <option value="1-10">1-10 موظفين</option>
                      <option value="11-50">11-50 موظف</option>
                      <option value="51-200">51-200 موظف</option>
                      <option value="201-500">201-500 موظف</option>
                      <option value="500+">أكثر من 500 موظف</option>
                    </select>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">وصف الشركة</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      disabled={!isEditing}
                      rows={3}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                      placeholder="وصف مختصر عن الشركة وأنشطتها"
                    />
                  </div>
                </>
              )}

              {/* معلومات إضافية للمكاتب العقارية */}
              {user.userType === 'real-estate-office' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">سنة التأسيس</label>
                    <input
                      type="number"
                      value={formData.establishedYear}
                      onChange={(e) => setFormData({...formData, establishedYear: e.target.value})}
                      disabled={!isEditing}
                      min="1900"
                      max={new Date().getFullYear()}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">عدد الموظفين</label>
                    <select
                      value={formData.employeeCount}
                      onChange={(e) => setFormData({...formData, employeeCount: e.target.value})}
                      disabled={!isEditing}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                    >
                      <option value="">اختر عدد الموظفين</option>
                      <option value="1-5">1-5 موظفين</option>
                      <option value="6-15">6-15 موظف</option>
                      <option value="16-30">16-30 موظف</option>
                      <option value="31-50">31-50 موظف</option>
                      <option value="50+">أكثر من 50 موظف</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">اسم المدير</label>
                    <input
                      type="text"
                      value={formData.managerName}
                      onChange={(e) => setFormData({...formData, managerName: e.target.value})}
                      disabled={!isEditing}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">هاتف المدير</label>
                    <input
                      type="tel"
                      value={formData.managerPhone}
                      onChange={(e) => setFormData({...formData, managerPhone: e.target.value})}
                      disabled={!isEditing}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">بريد المدير الإلكتروني</label>
                    <input
                      type="email"
                      value={formData.managerEmail}
                      onChange={(e) => setFormData({...formData, managerEmail: e.target.value})}
                      disabled={!isEditing}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">سنوات خبرة المدير</label>
                    <select
                      value={formData.managerExperience}
                      onChange={(e) => setFormData({...formData, managerExperience: e.target.value})}
                      disabled={!isEditing}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                    >
                      <option value="">اختر سنوات الخبرة</option>
                      <option value="1-3">1-3 سنوات</option>
                      <option value="4-7">4-7 سنوات</option>
                      <option value="8-15">8-15 سنة</option>
                      <option value="15+">أكثر من 15 سنة</option>
                    </select>
                  </div>
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">وصف المكتب</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      disabled={!isEditing}
                      rows={3}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                      placeholder="وصف مختصر عن المكتب وخدماته"
                    />
                  </div>
                </>
              )}
            </>
          )}
        </div>

        <div className="mt-6 flex gap-4">
          {!isEditing ? (
            <button
              onClick={() => setIsEditing(true)}
              className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              تعديل المعلومات
            </button>
          ) : (
            <>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
              >
                {isSaving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </button>
              <button
                onClick={() => setIsEditing(false)}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                إلغاء
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <ClientOnlyWrapper
          fallback={
            <div className="max-w-4xl mx-auto">
              <div className="animate-pulse space-y-6">
                <div className="h-32 bg-gray-200 rounded-xl"></div>
                <div className="h-96 bg-gray-200 rounded-xl"></div>
              </div>
            </div>
          }
        >
          <div className="max-w-4xl mx-auto">
            {/* رأس الصفحة */}
            <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-4 md:p-6 mb-6">
              <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div className="text-center md:text-right">
                  <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2">الملف الشخصي</h1>
                  <p className="text-sm md:text-base text-gray-600">إدارة معلوماتك الشخصية وإعدادات الحساب</p>
                </div>
                <div className="flex flex-col md:flex-row items-center gap-3 md:gap-4">
                  <Link
                    href="/settings"
                    className="w-full md:w-auto bg-green-600 text-white px-3 md:px-4 py-2 rounded-lg hover:bg-green-700 transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-green-500/25 text-sm md:text-base"
                  >
                    <span
                      className="text-base md:text-lg transition-all duration-200"
                      style={{ filter: 'drop-shadow(0 0 6px rgba(255, 255, 255, 0.5))' }}
                    >
                      ⚙️
                    </span>
                    <span className="whitespace-nowrap">إعدادات الحساب</span>
                  </Link>
                  <div className="flex items-center justify-center">
                    <NewVerificationBadge
                      userType={user.userType}
                      planId={user.subscription?.planId}
                      size="md"
                      showTooltip={true}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* التبويبات */}
            <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 mb-6 p-3 md:p-6">
              <div className="flex items-center justify-between overflow-x-auto">
                {tabs.map((tab, index) => (
                  <div key={tab.id} className={`flex items-center ${index < tabs.length - 1 ? 'flex-1' : ''} min-w-0`}>
                    <div className="flex flex-col items-center min-w-0">
                      <div
                        className={`w-10 h-10 md:w-12 md:h-12 rounded-full flex items-center justify-center text-sm md:text-lg font-medium mb-1 md:mb-2 transition-all duration-300 cursor-pointer group ${
                          activeTab === tab.id
                            ? 'bg-transparent border-2 border-green-500 text-green-600 shadow-lg'
                            : 'bg-transparent border-2 border-gray-300 text-gray-500'
                        } hover:border-green-500 hover:text-green-600 hover:shadow-lg hover:shadow-green-500/30 hover:scale-105`}
                        onClick={() => setActiveTab(tab.id)}
                        style={activeTab === tab.id ? {
                          filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.6)) brightness(1.2)'
                        } : {}}
                      >
                        <span className={`text-lg md:text-xl transition-all duration-300 ${
                          activeTab === tab.id ? 'opacity-100' : 'opacity-50'
                        } group-hover:opacity-100`}
                        style={activeTab === tab.id ? {
                          filter: 'drop-shadow(0 0 8px rgba(255, 255, 255, 0.8)) grayscale(0)',
                          transform: 'scale(1.1)'
                        } : {
                          filter: 'grayscale(1)'
                        }}>
                          {getTabIcon(tab.icon)}
                        </span>
                      </div>
                      <div className="text-center max-w-16 md:max-w-24">
                        <div className={`text-xs font-medium leading-tight transition-colors duration-300 ${
                          activeTab === tab.id ? 'text-green-600' : 'text-gray-500'
                        } hover:text-green-600 truncate`}>
                          {tab.name}
                        </div>
                      </div>
                    </div>
                    {index < tabs.length - 1 && (
                      <div className={`flex-1 h-0.5 mx-1 md:mx-2 mt-4 md:mt-6 transition-colors duration-300 ${
                        tabs.findIndex(t => t.id === activeTab) > index ? 'bg-green-500' : 'bg-gray-200'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* محتوى التبويبات */}
            {activeTab === 'basic' && renderBasicInfo()}
            {activeTab === 'contact' && (
              <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-3">
                  <span className="text-2xl opacity-80 hover:opacity-100 transition-all duration-300"
                        style={{filter: 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.3))'}}>📞</span>
                  معلومات الاتصال
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                      disabled={!isEditing}
                      className="w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50 text-sm md:text-base"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      disabled={!isEditing}
                      placeholder="+963988652401"
                      className="w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50 text-sm md:text-base"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">رقم الواتساب</label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      disabled={!isEditing}
                      placeholder="+963988652401"
                      className="w-full px-3 md:px-4 py-2 md:py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50 text-sm md:text-base"
                    />
                    <p className="text-xs text-gray-500 mt-1">سيظهر هذا الرقم في خيارات الاتصال عند إضافة إعلان</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">رقم إضافي (اختياري)</label>
                    <input
                      type="tel"
                      disabled={!isEditing}
                      placeholder="رقم هاتف إضافي"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                    />
                  </div>
                </div>

                <div className="mt-6 flex gap-4">
                  {!isEditing ? (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center gap-2"
                    >
                      <span className="opacity-80" style={{filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.3))'}}>✏️</span>
                      تعديل معلومات الاتصال
                    </button>
                  ) : (
                    <>
                      <button
                        onClick={handleSave}
                        disabled={isSaving}
                        className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center gap-2"
                      >
                        <span className="opacity-80" style={{filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.3))'}}>💾</span>
                        {isSaving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                      </button>
                      <button
                        onClick={() => setIsEditing(false)}
                        className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center gap-2"
                      >
                        <span className="opacity-80" style={{filter: 'drop-shadow(0 0 4px rgba(107, 114, 128, 0.3))'}}>❌</span>
                        إلغاء
                      </button>
                    </>
                  )}
                </div>
              </div>
            )}
            {activeTab === 'address' && (
              <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-3">
                  <span className="text-2xl opacity-80 hover:opacity-100 transition-all duration-300"
                        style={{filter: 'drop-shadow(0 0 8px rgba(168, 85, 247, 0.3))'}}>📍</span>
                  العنوان
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">المحافظة</label>
                    <select
                      value={formData.governorate}
                      onChange={(e) => setFormData({...formData, governorate: e.target.value})}
                      disabled={!isEditing}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                    >
                      <option value="">اختر المحافظة</option>
                      <option value="دمشق">دمشق</option>
                      <option value="ريف دمشق">ريف دمشق</option>
                      <option value="حلب">حلب</option>
                      <option value="حمص">حمص</option>
                      <option value="حماة">حماة</option>
                      <option value="اللاذقية">اللاذقية</option>
                      <option value="طرطوس">طرطوس</option>
                      <option value="إدلب">إدلب</option>
                      <option value="درعا">درعا</option>
                      <option value="السويداء">السويداء</option>
                      <option value="القنيطرة">القنيطرة</option>
                      <option value="دير الزور">دير الزور</option>
                      <option value="الرقة">الرقة</option>
                      <option value="الحسكة">الحسكة</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">المدينة</label>
                    <input
                      type="text"
                      value={formData.city}
                      onChange={(e) => setFormData({...formData, city: e.target.value})}
                      disabled={!isEditing}
                      placeholder="اسم المدينة"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">المنطقة/الحي</label>
                    <input
                      type="text"
                      value={formData.area}
                      onChange={(e) => setFormData({...formData, area: e.target.value})}
                      disabled={!isEditing}
                      placeholder="اسم المنطقة أو الحي"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الشارع (اختياري)</label>
                    <input
                      type="text"
                      value={formData.street}
                      onChange={(e) => setFormData({...formData, street: e.target.value})}
                      disabled={!isEditing}
                      placeholder="اسم الشارع"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                    />
                  </div>
                </div>

                <div className="mt-6 flex gap-4">
                  {!isEditing ? (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                    >
                      تعديل العنوان
                    </button>
                  ) : (
                    <>
                      <button
                        onClick={handleSave}
                        disabled={isSaving}
                        className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                      >
                        {isSaving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                      </button>
                      <button
                        onClick={() => setIsEditing(false)}
                        className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        إلغاء
                      </button>
                    </>
                  )}
                </div>
              </div>
            )}
            {activeTab === 'specializations' && (user.userType === 'business' || user.userType === 'real-estate-office') && (
              <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-3">
                  <span className="text-2xl opacity-80 hover:opacity-100 transition-all duration-300"
                        style={{filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))'}}>⚙️</span>
                  التخصص والخدمات
                </h3>

                {user.userType === 'real-estate-office' && (
                  <>
                    {/* التخصصات */}
                    <div className="mb-8">
                      <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                        <span className="text-xl opacity-80 hover:opacity-100 transition-all duration-300"
                              style={{filter: 'drop-shadow(0 0 6px rgba(34, 197, 94, 0.3))'}}>🎯</span>
                        التخصصات
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {specializationOptions.map((spec) => (
                          <button
                            key={spec}
                            onClick={() => isEditing && handleArrayToggle('specialization', spec)}
                            disabled={!isEditing}
                            className={`p-3 rounded-lg border text-sm font-medium transition-all duration-300 ${
                              formData.specialization?.includes(spec)
                                ? 'bg-green-100 border-green-500 text-green-700'
                                : 'bg-gray-50 border-gray-300 text-gray-600 hover:bg-gray-100'
                            } ${!isEditing ? 'cursor-default' : 'cursor-pointer hover:scale-105'}`}
                          >
                            {spec}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* الخدمات المقدمة */}
                    <div className="mb-8">
                      <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                        <span className="text-xl opacity-80 hover:opacity-100 transition-all duration-300"
                              style={{filter: 'drop-shadow(0 0 6px rgba(59, 130, 246, 0.3))'}}>🛠️</span>
                        الخدمات المقدمة
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {serviceOptions.map((service) => (
                          <button
                            key={service}
                            onClick={() => isEditing && handleArrayToggle('services', service)}
                            disabled={!isEditing}
                            className={`p-3 rounded-lg border text-sm font-medium transition-all duration-300 ${
                              formData.services?.includes(service)
                                ? 'bg-blue-100 border-blue-500 text-blue-700'
                                : 'bg-gray-50 border-gray-300 text-gray-600 hover:bg-gray-100'
                            } ${!isEditing ? 'cursor-default' : 'cursor-pointer hover:scale-105'}`}
                          >
                            {service}
                          </button>
                        ))}
                      </div>
                    </div>

                    {/* المناطق المخدمة */}
                    <div className="mb-8">
                      <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                        <span className="text-xl opacity-80 hover:opacity-100 transition-all duration-300"
                              style={{filter: 'drop-shadow(0 0 6px rgba(168, 85, 247, 0.3))'}}>📍</span>
                        المناطق المخدمة
                      </h4>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {governorates.map((gov) => (
                          <button
                            key={gov}
                            onClick={() => isEditing && handleArrayToggle('serviceAreas', gov)}
                            disabled={!isEditing}
                            className={`p-3 rounded-lg border text-sm font-medium transition-all duration-300 ${
                              formData.serviceAreas?.includes(gov)
                                ? 'bg-purple-100 border-purple-500 text-purple-700'
                                : 'bg-gray-50 border-gray-300 text-gray-600 hover:bg-gray-100'
                            } ${!isEditing ? 'cursor-default' : 'cursor-pointer hover:scale-105'}`}
                          >
                            {gov}
                          </button>
                        ))}
                      </div>
                    </div>
                  </>
                )}

                {user.userType === 'business' && (
                  <div className="mb-8">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <span className="text-xl opacity-80 hover:opacity-100 transition-all duration-300"
                            style={{filter: 'drop-shadow(0 0 6px rgba(251, 191, 36, 0.3))'}}>🛠️</span>
                      الخدمات والتخصصات
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">التخصصات الرئيسية</label>
                        <textarea
                          value={formData.specialization?.join(', ') || ''}
                          onChange={(e) => setFormData({
                            ...formData,
                            specialization: e.target.value.split(',').map(s => s.trim()).filter(s => s)
                          })}
                          disabled={!isEditing}
                          rows={3}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                          placeholder="أدخل التخصصات مفصولة بفواصل"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">الخدمات المقدمة</label>
                        <textarea
                          value={formData.services?.join(', ') || ''}
                          onChange={(e) => setFormData({
                            ...formData,
                            services: e.target.value.split(',').map(s => s.trim()).filter(s => s)
                          })}
                          disabled={!isEditing}
                          rows={3}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                          placeholder="أدخل الخدمات مفصولة بفواصل"
                        />
                      </div>
                    </div>
                  </div>
                )}

                <div className="mt-6 flex gap-4">
                  {!isEditing ? (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                    >
                      تعديل التخصص والخدمات
                    </button>
                  ) : (
                    <>
                      <button
                        onClick={handleSave}
                        disabled={isSaving}
                        className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                      >
                        {isSaving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                      </button>
                      <button
                        onClick={() => setIsEditing(false)}
                        className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        إلغاء
                      </button>
                    </>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'documents' && (user.userType === 'business' || user.userType === 'real-estate-office') && (
              <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-3">
                  <span className="text-2xl opacity-80 hover:opacity-100 transition-all duration-300"
                        style={{filter: 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.3))'}}>📄</span>
                  الوثائق والتراخيص
                </h3>

                <div className="space-y-6">
                  {/* رخصة العمل */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <span className="text-xl opacity-80" style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}>📋</span>
                      {user.userType === 'business' ? 'رخصة الشركة' : 'رخصة المكتب العقاري'}
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">رقم الرخصة</label>
                        <input
                          type="text"
                          value={formData.licenseNumber || ''}
                          onChange={(e) => setFormData({...formData, licenseNumber: e.target.value})}
                          disabled={!isEditing}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                          placeholder="رقم الرخصة"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ انتهاء الرخصة</label>
                        <input
                          type="date"
                          value={formData.licenseExpiry || ''}
                          onChange={(e) => setFormData({...formData, licenseExpiry: e.target.value})}
                          disabled={!isEditing}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                        />
                      </div>
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">تحميل صورة الرخصة</label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-500 transition-colors">
                          <div className="text-gray-500">
                            <span className="text-3xl mb-2 block opacity-50" style={{filter: 'grayscale(1)'}}>📄</span>
                            <p className="text-sm">اسحب وأفلت الملف هنا أو انقر للتحديد</p>
                            <p className="text-xs text-gray-400 mt-1">PDF, JPG, PNG (حد أقصى 5MB)</p>
                          </div>
                          <input
                            type="file"
                            accept=".pdf,.jpg,.jpeg,.png"
                            multiple
                            onChange={handleFileUpload}
                            className="hidden"
                            id="license-upload"
                          />
                          <label
                            htmlFor="license-upload"
                            className="mt-3 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors cursor-pointer inline-block"
                          >
                            اختر ملف
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* السجل التجاري */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <span className="text-xl opacity-80" style={{filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.3))'}}>📊</span>
                      السجل التجاري
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">رقم السجل التجاري</label>
                        <input
                          type="text"
                          value={formData.commercialRegister || ''}
                          onChange={(e) => setFormData({...formData, commercialRegister: e.target.value})}
                          disabled={!isEditing}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                          placeholder="رقم السجل التجاري"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الإصدار</label>
                        <input
                          type="date"
                          value={formData.commercialRegisterDate || ''}
                          onChange={(e) => setFormData({...formData, commercialRegisterDate: e.target.value})}
                          disabled={!isEditing}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                        />
                      </div>
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">تحميل صورة السجل التجاري</label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-500 transition-colors cursor-pointer">
                          <div className="text-gray-500">
                            <span className="text-3xl mb-2 block opacity-50" style={{filter: 'grayscale(1)'}}>📊</span>
                            <p className="text-sm">اسحب وأفلت الملف هنا أو انقر للتحديد</p>
                            <p className="text-xs text-gray-400 mt-1">PDF, JPG, PNG (حد أقصى 5MB)</p>
                          </div>
                          <input
                            type="file"
                            accept=".pdf,.jpg,.jpeg,.png"
                            disabled={!isEditing}
                            className="hidden"
                          />
                          {isEditing && (
                            <button
                              type="button"
                              className="mt-3 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                            >
                              اختر ملف
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* السجل الصناعي */}
                  <div className="border border-gray-200 rounded-lg p-4">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <span className="text-xl opacity-50" style={{filter: 'grayscale(1)'}}>🏭</span>
                      السجل الصناعي
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">رقم السجل الصناعي</label>
                        <input
                          type="text"
                          value={formData.industrialRegister || ''}
                          onChange={(e) => setFormData({...formData, industrialRegister: e.target.value})}
                          disabled={!isEditing}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                          placeholder="رقم السجل الصناعي"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الإصدار</label>
                        <input
                          type="date"
                          value={formData.industrialRegisterDate || ''}
                          onChange={(e) => setFormData({...formData, industrialRegisterDate: e.target.value})}
                          disabled={!isEditing}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                        />
                      </div>
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">تحميل صورة السجل الصناعي</label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary-500 transition-colors cursor-pointer">
                          <div className="text-gray-500">
                            <span className="text-3xl mb-2 block opacity-50" style={{filter: 'grayscale(1)'}}>🏭</span>
                            <p className="text-sm">اسحب وأفلت الملف هنا أو انقر للتحديد</p>
                            <p className="text-xs text-gray-400 mt-1">PDF, JPG, PNG (حد أقصى 5MB)</p>
                          </div>
                          <input
                            type="file"
                            accept=".pdf,.jpg,.jpeg,.png"
                            disabled={!isEditing}
                            className="hidden"
                          />
                          {isEditing && (
                            <button
                              type="button"
                              className="mt-3 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                            >
                              اختر ملف
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* عرض الملفات المرفوعة */}
                {uploadedDocuments.length > 0 && (
                  <div className="mt-6 border border-gray-200 rounded-lg p-4">
                    <h4 className="text-lg font-semibold text-gray-800 mb-4">الملفات المرفوعة</h4>
                    <div className="space-y-2">
                      {uploadedDocuments.map((file, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                          <span className="text-sm text-gray-700">{file.name}</span>
                          <button
                            onClick={() => removeDocument(index)}
                            className="text-red-600 hover:text-red-700 text-sm"
                          >
                            حذف
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="mt-6 flex gap-4 flex-wrap">
                  {!isEditing ? (
                    <>
                      <button
                        onClick={() => setIsEditing(true)}
                        className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                      >
                        تعديل الوثائق
                      </button>
                      <button
                        className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
                        onClick={handleSubmitDocuments}
                        disabled={isSubmittingDocuments || uploadedDocuments.length === 0}
                      >
                        {isSubmittingDocuments ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            جاري الإرسال...
                          </>
                        ) : (
                          <>
                            <span className="opacity-50" style={{filter: 'grayscale(1)'}}>📤</span>
                            إرسال للمصادقة
                          </>
                        )}
                      </button>
                    </>
                  ) : (
                    <>
                      <button
                        onClick={handleSave}
                        disabled={isSaving}
                        className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                      >
                        {isSaving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                      </button>
                      <button
                        onClick={() => setIsEditing(false)}
                        className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        إلغاء
                      </button>
                    </>
                  )}
                </div>
              </div>
            )}







            {/* Membership Tab */}
            {activeTab === 'membership' && (
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-800">هوية العضوية الرقمية</h3>
                  <button
                    onClick={() => setShowMembershipCard(true)}
                    className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-all duration-300 opacity-80 hover:opacity-100"
                    style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}
                  >
                    <span className="flex items-center gap-2">
                      <img
                        src="/images/الملف الشخصي/ID.png"
                        alt="هوية العضوية"
                        className="w-4 h-4"
                      />
                      عرض الهوية
                    </span>
                  </button>
                </div>

                {/* عرض شارات التوثيق وهوية العضوية */}
                <div className="flex flex-col items-center gap-4 mb-6">
                  {/* شارة التوثيق */}
                  <NewVerificationBadge
                    userType={user.userType}
                    planId={user.subscription?.planId}
                    size="lg"
                    showTooltip={true}
                  />

                  {/* هوية العضوية - فقط للباقات المدفوعة */}
                  {user.subscription?.planId && user.subscription.planId !== 'individual-free' && (
                    <MembershipBadge
                      planId={user.subscription.planId}
                      size="lg"
                      showLabel={true}
                    />
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">رقم العضوية</label>
                      <div className="text-lg font-mono text-gray-800">MIN-{user.id?.slice(-6) || '000000'}</div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ الانضمام</label>
                      <div className="text-gray-800">{formatDate(user.createdAt || Date.now(), 'long')}</div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">نوع الحساب</label>
                      <div className="flex items-center gap-2">
                        <span className="text-lg" style={{filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.3)) opacity(0.8)'}}>
                          {user.userType === 'individual' && '👤'}
                          {user.userType === 'business' && '🏢'}
                          {user.userType === 'real-estate-office' && '🏘️'}
                        </span>
                        <span className="text-gray-800">
                          {user.userType === 'individual' && 'حساب فردي'}
                          {user.userType === 'business' && 'حساب شركة'}
                          {user.userType === 'real-estate-office' && 'مكتب عقاري'}
                        </span>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">شارات التحقق</label>
                      <div className="flex items-center gap-2 flex-wrap">
                        {userBadges.map((badgeId, index) => (
                          <VerificationBadge key={index} badgeId={badgeId} size="sm" showTooltip={true} />
                        ))}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-center">
                    <div className="text-center">
                      <div className="mb-4">
                        <img
                          src="/images/الملف الشخصي/ID.png"
                          alt="هوية العضوية"
                          className="w-16 h-16 mx-auto"
                        />
                      </div>
                      <p className="text-gray-600">اضغط على &quot;عرض الهوية&quot; لمشاهدة هويتك الرقمية</p>
                    </div>
                  </div>
                </div>
              </div>
            )}


          </div>
        </ClientOnlyWrapper>
      </main>

      {/* Password Change Modal */}
      {showPasswordModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-800">تغيير كلمة المرور</h3>
              <button
                onClick={() => setShowPasswordModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الحالية</label>
                <input
                  type="password"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الجديدة</label>
                <input
                  type="password"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور الجديدة</label>
                <input
                  type="password"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button className="flex-1 bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700 transition-colors">
                تغيير كلمة المرور
              </button>
              <button
                onClick={() => setShowPasswordModal(false)}
                className="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Membership Card Modal */}
      {showMembershipCard && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-800">هوية العضوية الرقمية</h3>
              <button
                onClick={() => setShowMembershipCard(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>

            {/* Membership Card */}
            <div className={`rounded-xl p-6 text-white relative overflow-hidden backdrop-blur-sm ${
              user.subscription?.planId === 'individual-basic' ? 'bg-gradient-to-br from-blue-500/80 to-blue-700/80 shadow-lg' :
              user.subscription?.planId === 'individual-premium' ? 'bg-gradient-to-br from-yellow-400/80 via-yellow-500/80 to-yellow-600/80 shadow-xl' :
              user.subscription?.planId === 'individual-vip' ? 'bg-gradient-to-br from-purple-500/80 to-purple-700/80 shadow-xl' :
              user.subscription?.planId === 'business-starter' ? 'bg-gradient-to-br from-green-500/80 to-green-700/80 shadow-lg' :
              user.subscription?.planId === 'business-professional' ? 'bg-gradient-to-br from-gray-400/80 via-gray-500/80 to-gray-600/80 shadow-lg' :
              user.subscription?.planId === 'business-gold' ? 'bg-gradient-to-br from-yellow-400/80 via-yellow-500/80 to-yellow-600/80 shadow-2xl' :
              user.subscription?.planId === 'real-estate-office' ? 'bg-gradient-to-br from-blue-600/80 to-blue-800/80 shadow-lg' :
              'bg-gradient-to-br from-gray-400/80 to-gray-600/80 shadow-lg'
            } ${
              user.subscription?.planId === 'business-professional' || user.subscription?.planId === 'real-estate-office' ? 'border border-white/30' : ''
            }`}
            style={{
              background: user.subscription?.planId === 'individual-basic' ? 'linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(29, 78, 216, 0.8) 100%)' :
                         user.subscription?.planId === 'individual-premium' ? 'linear-gradient(135deg, rgba(251, 191, 36, 0.8) 0%, rgba(217, 119, 6, 0.8) 100%)' :
                         user.subscription?.planId === 'individual-vip' ? 'linear-gradient(135deg, rgba(147, 51, 234, 0.8) 0%, rgba(109, 40, 217, 0.8) 100%)' :
                         user.subscription?.planId === 'business-starter' ? 'linear-gradient(135deg, rgba(34, 197, 94, 0.8) 0%, rgba(21, 128, 61, 0.8) 100%)' :
                         user.subscription?.planId === 'business-professional' ? 'linear-gradient(135deg, rgba(156, 163, 175, 0.8) 0%, rgba(75, 85, 99, 0.8) 100%)' :
                         user.subscription?.planId === 'business-gold' ? 'linear-gradient(135deg, rgba(251, 191, 36, 0.8) 0%, rgba(217, 119, 6, 0.8) 100%)' :
                         user.subscription?.planId === 'real-estate-office' ? 'linear-gradient(135deg, rgba(37, 99, 235, 0.8) 0%, rgba(30, 64, 175, 0.8) 100%)' :
                         'linear-gradient(135deg, rgba(156, 163, 175, 0.8) 0%, rgba(75, 85, 99, 0.8) 100%)',
              backdropFilter: 'blur(10px)',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)'
            }}>
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute top-0 right-0 w-32 h-32 bg-white rounded-full -translate-y-16 translate-x-16"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-white rounded-full translate-y-12 -translate-x-12"></div>
              </div>

              <div className="relative z-10">
                {/* Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <div className="w-10 h-10 flex items-center justify-center">
                      <img
                        src="/images/MinAlmalek Logo White.png"
                        alt="من المالك"
                        className="w-full h-full object-contain filter brightness-0 invert opacity-90"
                        style={{filter: 'brightness(0) invert(1) drop-shadow(0 0 4px rgba(255, 255, 255, 0.3))'}}
                      />
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xs opacity-80">رقم العضوية</div>
                    <div className="font-mono text-sm flex items-center gap-2">
                      <span className="text-white opacity-80" style={{filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.3))'}}>
                        🆔
                      </span>
                      MIN-{user.id?.slice(-6) || '000000'}
                    </div>
                  </div>
                </div>

                {/* Member Info */}
                <div className="mb-4">
                  <div className="text-xl font-bold">
                    {user.userType === 'individual'
                      ? `${user.individualInfo?.firstName || ''} ${user.individualInfo?.lastName || ''}`.trim() || user.name
                      : user.userType === 'business'
                      ? user.businessInfo?.companyName || user.name
                      : user.realEstateOfficeInfo?.officeName || user.name
                    }
                  </div>
                  <div className="text-sm opacity-80">
                    {user.userType === 'individual' && 'حساب فردي'}
                    {user.userType === 'business' && 'حساب شركة'}
                    {user.userType === 'real-estate-office' && 'مكتب عقاري'}
                  </div>
                </div>

                {/* QR Code and Badge */}
                <div className="flex items-end justify-between">
                  <div>
                    <div className="text-xs opacity-80 mb-1">عضو منذ</div>
                    <div className="text-sm">{formatDate(user.createdAt || Date.now(), 'long')}</div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="text-center">
                      {/* شارات التحقق الملونة */}
                      <div className="flex flex-col items-center gap-1">
                        {userBadges.map((badgeId, index) => (
                          <div key={index} className="transform scale-75">
                            <VerificationBadge badgeId={badgeId} size="xs" showTooltip={false} />
                          </div>
                        ))}
                      </div>
                      <div className="text-xs mt-1 opacity-90">
                        {user.userType === 'business' && user.subscription?.planId === 'business-professional' ? 'شركة ذهبية' :
                         user.userType === 'business' ? 'شركة موثقة' :
                         user.userType === 'real-estate-office' ? 'مكتب موثق' :
                         user.subscription?.planId === 'premium' ? 'عضو مميز' :
                         user.subscription?.planId === 'business' ? 'عضو ذهبي' :
                         'عضو موثق'}
                      </div>
                    </div>

                    <div className="bg-white/90 p-2 rounded-lg backdrop-blur-sm">
                      <img
                        src={`https://api.qrserver.com/v1/create-qr-code/?size=60x60&data=${encodeURIComponent(`MIN-ALMALEK-${user.id}-${user.name}`)}&bgcolor=ffffff&color=000000&margin=1`}
                        alt="QR Code"
                        className="w-12 h-12"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-4 text-center">
              <p className="text-sm text-gray-600">
                استخدم هذه الهوية للتحقق من عضويتك في موقع من المالك
              </p>
            </div>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
}
