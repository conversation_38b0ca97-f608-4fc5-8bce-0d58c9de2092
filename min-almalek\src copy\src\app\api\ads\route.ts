import { NextRequest, NextResponse } from 'next/server';
import { DataService, SearchFilters } from '@/lib/data';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);

  // استخراج معاملات البحث
  const filters: SearchFilters = {
    keyword: searchParams.get('keyword') || undefined,
    category: searchParams.get('category') || undefined,
    subcategory: searchParams.get('subcategory') || undefined,
    governorate: searchParams.get('governorate') || undefined,
    city: searchParams.get('city') || undefined,
    priceMin: searchParams.get('priceMin') ? Number(searchParams.get('priceMin')) : undefined,
    priceMax: searchParams.get('priceMax') ? Number(searchParams.get('priceMax')) : undefined,
    currency: (searchParams.get('currency') as 'SYP' | 'USD') || undefined,
    condition: (searchParams.get('condition') as 'new' | 'used' | 'refurbished' | 'renovated') || undefined,
    sellerType: (searchParams.get('sellerType') as 'individual' | 'business') || undefined,
    verified: searchParams.get('verified') === 'true',
    featured: searchParams.get('featured') === 'true',
    hasImages: searchParams.get('hasImages') === 'true',
    datePosted: (searchParams.get('datePosted') as 'today' | 'week' | 'month') || undefined,
    sortBy: (searchParams.get('sortBy') as 'newest' | 'oldest' | 'price_low' | 'price_high' | 'popular') || 'newest'
  };

  // معاملات الصفحة
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '12');

  try {
    // البحث في الإعلانات
    const allResults = DataService.searchAds(filters);

    // تطبيق الصفحات
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedResults = allResults.slice(startIndex, endIndex);

    // إحصائيات النتائج
    const stats = {
      total: allResults.length,
      page,
      limit,
      totalPages: Math.ceil(allResults.length / limit),
      hasNext: endIndex < allResults.length,
      hasPrev: page > 1
    };

    return NextResponse.json({
      success: true,
      data: paginatedResults,
      stats,
      filters
    });

  } catch (error) {
    console.error('خطأ في البحث:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في البحث' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const adData = await request.json();

    // في التطبيق الحقيقي، ستحفظ البيانات في قاعدة البيانات
    // هنا سنقوم بمحاكاة إنشاء إعلان جديد

    const newAd = {
      id: Date.now(), // ID مؤقت
      ...adData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'pending', // في انتظار المراجعة
      views: 0,
      favorites: 0
    };

    return NextResponse.json({
      success: true,
      data: newAd,
      message: 'تم إرسال الإعلان للمراجعة بنجاح'
    });

  } catch (error) {
    console.error('خطأ في إنشاء الإعلان:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في إنشاء الإعلان' },
      { status: 500 }
    );
  }
}
