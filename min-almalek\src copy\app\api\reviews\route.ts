import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';
import { ReviewService } from '@/lib/reviews';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const type = searchParams.get('type') as 'received' | 'given';
    const action = searchParams.get('action');
    const query = searchParams.get('query');

    // البحث في التقييمات (لا يحتاج مصادقة)
    if (action === 'search' && query) {
      const rating = searchParams.get('rating') ? parseInt(searchParams.get('rating')!) : undefined;
      const verified = searchParams.get('verified') === 'true';
      const status = searchParams.get('status') as any;

      const results = ReviewService.searchReviews(query, {
        rating,
        verified,
        status
      });

      return NextResponse.json({
        success: true,
        data: results
      });
    }

    // الحصول على ملخص التقييمات (لا يحتاج مصادقة)
    if (action === 'summary' && userId) {
      const summary = ReviewService.getReviewSummary(parseInt(userId));
      return NextResponse.json({
        success: true,
        data: summary
      });
    }

    // الحصول على تقييمات المستخدم (لا يحتاج مصادقة)
    if (userId) {
      const reviews = ReviewService.getUserReviews(parseInt(userId), type || 'received');
      return NextResponse.json({
        success: true,
        data: reviews
      });
    }

    // باقي العمليات تحتاج مصادقة
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    switch (action) {
      case 'stats':
        // التحقق من صلاحيات الإدارة
        if (sessionResult.data.type !== 'business') {
          return NextResponse.json(
            { success: false, error: 'غير مصرح لعرض الإحصائيات' },
            { status: 403 }
          );
        }

        const stats = ReviewService.getReviewStats();
        return NextResponse.json({
          success: true,
          data: stats
        });

      default:
        return NextResponse.json(
          { success: false, error: 'إجراء غير صحيح' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('خطأ في جلب التقييمات:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { 
      action, 
      revieweeId, 
      rating, 
      title, 
      comment, 
      adId, 
      pros, 
      cons,
      reviewId,
      helpful,
      response
    } = await request.json();

    switch (action) {
      case 'add_review':
        if (!revieweeId || !rating || !title || !comment) {
          return NextResponse.json(
            { success: false, error: 'جميع الحقول مطلوبة' },
            { status: 400 }
          );
        }

        if (revieweeId === sessionResult.data.id) {
          return NextResponse.json(
            { success: false, error: 'لا يمكنك تقييم نفسك' },
            { status: 400 }
          );
        }

        const reviewResult = await ReviewService.addReview(
          sessionResult.data.id,
          revieweeId,
          rating,
          title,
          comment,
          adId,
          pros,
          cons
        );

        if (reviewResult.success) {
          return NextResponse.json({
            success: true,
            data: reviewResult.data,
            message: 'تم إضافة التقييم بنجاح'
          });
        } else {
          return NextResponse.json(
            { success: false, error: reviewResult.error },
            { status: 400 }
          );
        }

      case 'rate_helpfulness':
        if (!reviewId || helpful === undefined) {
          return NextResponse.json(
            { success: false, error: 'معرف التقييم والتقييم مطلوبان' },
            { status: 400 }
          );
        }

        const helpfulResult = await ReviewService.rateReviewHelpfulness(
          reviewId,
          sessionResult.data.id,
          helpful
        );

        if (helpfulResult.success) {
          return NextResponse.json({
            success: true,
            message: 'تم تسجيل تقييمك'
          });
        } else {
          return NextResponse.json(
            { success: false, error: helpfulResult.error },
            { status: 400 }
          );
        }

      case 'add_response':
        if (!reviewId || !response) {
          return NextResponse.json(
            { success: false, error: 'معرف التقييم والرد مطلوبان' },
            { status: 400 }
          );
        }

        const responseResult = await ReviewService.addResponse(
          reviewId,
          sessionResult.data.id,
          response
        );

        if (responseResult.success) {
          return NextResponse.json({
            success: true,
            data: responseResult.data,
            message: 'تم إضافة الرد بنجاح'
          });
        } else {
          return NextResponse.json(
            { success: false, error: responseResult.error },
            { status: 400 }
          );
        }

      case 'approve_review':
        // التحقق من صلاحيات الإدارة
        if (sessionResult.data.type !== 'business') {
          return NextResponse.json(
            { success: false, error: 'غير مصرح للموافقة على التقييمات' },
            { status: 403 }
          );
        }

        if (!reviewId) {
          return NextResponse.json(
            { success: false, error: 'معرف التقييم مطلوب' },
            { status: 400 }
          );
        }

        const approveResult = await ReviewService.approveReview(reviewId);

        if (approveResult.success) {
          return NextResponse.json({
            success: true,
            message: 'تم الموافقة على التقييم'
          });
        } else {
          return NextResponse.json(
            { success: false, error: approveResult.error },
            { status: 400 }
          );
        }

      case 'reject_review':
        // التحقق من صلاحيات الإدارة
        if (sessionResult.data.type !== 'business') {
          return NextResponse.json(
            { success: false, error: 'غير مصرح لرفض التقييمات' },
            { status: 403 }
          );
        }

        if (!reviewId) {
          return NextResponse.json(
            { success: false, error: 'معرف التقييم مطلوب' },
            { status: 400 }
          );
        }

        const rejectResult = await ReviewService.rejectReview(reviewId, comment);

        if (rejectResult.success) {
          return NextResponse.json({
            success: true,
            message: 'تم رفض التقييم'
          });
        } else {
          return NextResponse.json(
            { success: false, error: rejectResult.error },
            { status: 400 }
          );
        }

      default:
        return NextResponse.json(
          { success: false, error: 'إجراء غير صحيح' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('خطأ في معالجة طلب التقييم:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { reviewId, ...updates } = await request.json();

    if (!reviewId) {
      return NextResponse.json(
        { success: false, error: 'معرف التقييم مطلوب' },
        { status: 400 }
      );
    }

    const result = await ReviewService.updateReview(
      reviewId,
      sessionResult.data.id,
      updates
    );

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data,
        message: 'تم تحديث التقييم بنجاح'
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('خطأ في تحديث التقييم:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { reviewId } = await request.json();

    if (!reviewId) {
      return NextResponse.json(
        { success: false, error: 'معرف التقييم مطلوب' },
        { status: 400 }
      );
    }

    const result = await ReviewService.deleteReview(reviewId, sessionResult.data.id);

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'تم حذف التقييم بنجاح'
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('خطأ في حذف التقييم:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
