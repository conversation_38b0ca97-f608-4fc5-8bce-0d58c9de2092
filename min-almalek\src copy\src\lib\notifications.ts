// نظام الإشعارات المتقدم
export interface Notification {
  id: number;
  userId: number;
  type: 'message' | 'ad_expired' | 'ad_approved' | 'ad_rejected' | 'payment' | 'system' | 'offer' | 'favorite_ad' | 'new_follower';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'account' | 'ads' | 'messages' | 'payments' | 'system';
  actionUrl?: string;
  actionText?: string;
  expiresAt?: string;
  createdAt: string;
  readAt?: string;
}

export interface NotificationSettings {
  userId: number;
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  categories: {
    messages: boolean;
    ads: boolean;
    payments: boolean;
    system: boolean;
    marketing: boolean;
  };
  frequency: 'instant' | 'hourly' | 'daily' | 'weekly';
  quietHours: {
    enabled: boolean;
    start: string; // HH:MM
    end: string; // HH:MM
  };
}

// بيانات تجريبية للإشعارات
const sampleNotifications: Notification[] = [
  {
    id: 1,
    userId: 1,
    type: 'message',
    title: 'رسالة جديدة',
    message: 'لديك رسالة جديدة من محمد أحمد بخصوص إعلان "شقة للبيع في دمشق"',
    data: { conversationId: 1, senderId: 2 },
    read: false,
    priority: 'high',
    category: 'messages',
    actionUrl: '/dashboard?tab=messages',
    actionText: 'عرض الرسالة',
    createdAt: '2024-01-20T14:30:00Z'
  },
  {
    id: 2,
    userId: 1,
    type: 'ad_approved',
    title: 'تم قبول إعلانك',
    message: 'تم قبول ونشر إعلان "شقة للبيع في دمشق - المالكي" وهو الآن متاح للمشاهدة',
    data: { adId: 1 },
    read: false,
    priority: 'medium',
    category: 'ads',
    actionUrl: '/ad/1',
    actionText: 'عرض الإعلان',
    createdAt: '2024-01-20T10:15:00Z'
  },
  {
    id: 3,
    userId: 1,
    type: 'offer',
    title: 'عرض جديد',
    message: 'تلقيت عرضاً بقيمة 80,000,000 ل.س على إعلان "شقة للبيع في دمشق"',
    data: { adId: 1, offerId: 1, amount: 80000000, currency: 'SYP' },
    read: true,
    priority: 'high',
    category: 'messages',
    actionUrl: '/dashboard?tab=offers',
    actionText: 'عرض التفاصيل',
    createdAt: '2024-01-19T16:45:00Z',
    readAt: '2024-01-19T17:00:00Z'
  },
  {
    id: 4,
    userId: 1,
    type: 'ad_expired',
    title: 'انتهاء صلاحية إعلان',
    message: 'انتهت صلاحية إعلان "لابتوب Dell Gaming". يمكنك تجديده الآن',
    data: { adId: 5 },
    read: false,
    priority: 'medium',
    category: 'ads',
    actionUrl: '/dashboard?tab=ads',
    actionText: 'تجديد الإعلان',
    expiresAt: '2024-01-25T23:59:59Z',
    createdAt: '2024-01-18T09:00:00Z'
  },
  {
    id: 5,
    userId: 1,
    type: 'payment',
    title: 'تم الدفع بنجاح',
    message: 'تم تأكيد دفع اشتراك الخطة المميزة. مميزاتك الجديدة متاحة الآن',
    data: { planId: 'premium', amount: 50000, currency: 'SYP' },
    read: true,
    priority: 'medium',
    category: 'payments',
    actionUrl: '/dashboard?tab=subscription',
    actionText: 'عرض الاشتراك',
    createdAt: '2024-01-17T11:30:00Z',
    readAt: '2024-01-17T12:00:00Z'
  }
];

const sampleNotificationSettings: NotificationSettings[] = [
  {
    userId: 1,
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    categories: {
      messages: true,
      ads: true,
      payments: true,
      system: true,
      marketing: false
    },
    frequency: 'instant',
    quietHours: {
      enabled: true,
      start: '22:00',
      end: '08:00'
    }
  }
];

export class NotificationService {
  // إنشاء إشعار جديد
  static createNotification(notification: Omit<Notification, 'id' | 'createdAt'>): Notification {
    const newNotification: Notification = {
      id: Date.now(),
      createdAt: new Date().toISOString(),
      ...notification
    };

    sampleNotifications.unshift(newNotification);
    return newNotification;
  }

  // الحصول على إشعارات المستخدم
  static getUserNotifications(
    userId: number,
    filters?: {
      category?: string;
      read?: boolean;
      priority?: string;
      limit?: number;
      offset?: number;
    }
  ): { notifications: Notification[]; total: number } {
    let userNotifications = sampleNotifications.filter(n => n.userId === userId);

    // تطبيق الفلاتر
    if (filters) {
      if (filters.category) {
        userNotifications = userNotifications.filter(n => n.category === filters.category);
      }
      if (filters.read !== undefined) {
        userNotifications = userNotifications.filter(n => n.read === filters.read);
      }
      if (filters.priority) {
        userNotifications = userNotifications.filter(n => n.priority === filters.priority);
      }
    }

    // ترتيب حسب التاريخ
    userNotifications.sort((a, b) =>
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    const total = userNotifications.length;

    // تطبيق الصفحات
    if (filters?.limit) {
      const offset = filters.offset || 0;
      userNotifications = userNotifications.slice(offset, offset + filters.limit);
    }

    return { notifications: userNotifications, total };
  }

  // تحديد إشعار كمقروء
  static markAsRead(notificationId: number, userId: number): boolean {
    const notification = sampleNotifications.find(n =>
      n.id === notificationId && n.userId === userId
    );

    if (notification && !notification.read) {
      notification.read = true;
      notification.readAt = new Date().toISOString();
      return true;
    }

    return false;
  }

  // تحديد جميع الإشعارات كمقروءة
  static markAllAsRead(userId: number): number {
    let count = 0;
    sampleNotifications.forEach(notification => {
      if (notification.userId === userId && !notification.read) {
        notification.read = true;
        notification.readAt = new Date().toISOString();
        count++;
      }
    });
    return count;
  }

  // حذف إشعار
  static deleteNotification(notificationId: number, userId: number): boolean {
    const index = sampleNotifications.findIndex(n =>
      n.id === notificationId && n.userId === userId
    );

    if (index !== -1) {
      sampleNotifications.splice(index, 1);
      return true;
    }

    return false;
  }

  // حذف جميع الإشعارات المقروءة
  static deleteReadNotifications(userId: number): number {
    const initialLength = sampleNotifications.length;

    for (let i = sampleNotifications.length - 1; i >= 0; i--) {
      const notification = sampleNotifications[i];
      if (notification.userId === userId && notification.read) {
        sampleNotifications.splice(i, 1);
      }
    }

    return initialLength - sampleNotifications.length;
  }

  // الحصول على عدد الإشعارات غير المقروءة
  static getUnreadCount(userId: number): number {
    return sampleNotifications.filter(n =>
      n.userId === userId && !n.read
    ).length;
  }

  // الحصول على إحصائيات الإشعارات
  static getNotificationStats(userId: number) {
    const userNotifications = sampleNotifications.filter(n => n.userId === userId);

    const stats = {
      total: userNotifications.length,
      unread: userNotifications.filter(n => !n.read).length,
      byCategory: {} as Record<string, number>,
      byPriority: {} as Record<string, number>,
      recent: userNotifications.filter(n =>
        new Date(n.createdAt).getTime() > Date.now() - (24 * 60 * 60 * 1000)
      ).length
    };

    // إحصائيات حسب التصنيف
    userNotifications.forEach(n => {
      stats.byCategory[n.category] = (stats.byCategory[n.category] || 0) + 1;
      stats.byPriority[n.priority] = (stats.byPriority[n.priority] || 0) + 1;
    });

    return stats;
  }

  // الحصول على إعدادات الإشعارات
  static getUserNotificationSettings(userId: number): NotificationSettings | null {
    return sampleNotificationSettings.find(s => s.userId === userId) || null;
  }

  // تحديث إعدادات الإشعارات
  static updateNotificationSettings(
    userId: number,
    settings: Partial<NotificationSettings>
  ): boolean {
    const index = sampleNotificationSettings.findIndex(s => s.userId === userId);

    if (index !== -1) {
      sampleNotificationSettings[index] = {
        ...sampleNotificationSettings[index],
        ...settings,
        userId // التأكد من عدم تغيير معرف المستخدم
      };
      return true;
    } else {
      // إنشاء إعدادات جديدة
      const newSettings: NotificationSettings = {
        userId,
        emailNotifications: true,
        pushNotifications: true,
        smsNotifications: false,
        categories: {
          messages: true,
          ads: true,
          payments: true,
          system: true,
          marketing: false
        },
        frequency: 'instant',
        quietHours: {
          enabled: false,
          start: '22:00',
          end: '08:00'
        },
        ...settings
      };

      sampleNotificationSettings.push(newSettings);
      return true;
    }
  }

  // إنشاء إشعارات تلقائية للأحداث المختلفة
  static createAdApprovedNotification(userId: number, adId: number, adTitle: string) {
    return this.createNotification({
      userId,
      type: 'ad_approved',
      title: 'تم قبول إعلانك',
      message: `تم قبول ونشر إعلان "${adTitle}" وهو الآن متاح للمشاهدة`,
      data: { adId },
      read: false,
      priority: 'medium',
      category: 'ads',
      actionUrl: `/ad/${adId}`,
      actionText: 'عرض الإعلان'
    });
  }

  static createNewMessageNotification(
    userId: number,
    senderName: string,
    adTitle: string,
    conversationId: number
  ) {
    return this.createNotification({
      userId,
      type: 'message',
      title: 'رسالة جديدة',
      message: `لديك رسالة جديدة من ${senderName} بخصوص إعلان "${adTitle}"`,
      data: { conversationId },
      read: false,
      priority: 'high',
      category: 'messages',
      actionUrl: '/dashboard?tab=messages',
      actionText: 'عرض الرسالة'
    });
  }

  static createOfferNotification(
    userId: number,
    amount: number,
    currency: string,
    adTitle: string,
    offerId: number
  ) {
    return this.createNotification({
      userId,
      type: 'offer',
      title: 'عرض جديد',
      message: `تلقيت عرضاً بقيمة ${amount.toLocaleString()} ${currency === 'SYP' ? 'ل.س' : '$'} على إعلان "${adTitle}"`,
      data: { offerId, amount, currency },
      read: false,
      priority: 'high',
      category: 'messages',
      actionUrl: '/dashboard?tab=offers',
      actionText: 'عرض التفاصيل'
    });
  }

  // تنظيف الإشعارات القديمة
  static cleanupOldNotifications(userId: number, daysOld: number = 30): number {
    const cutoffDate = new Date(Date.now() - (daysOld * 24 * 60 * 60 * 1000));
    const initialLength = sampleNotifications.length;

    for (let i = sampleNotifications.length - 1; i >= 0; i--) {
      const notification = sampleNotifications[i];
      if (notification.userId === userId &&
          new Date(notification.createdAt) < cutoffDate) {
        sampleNotifications.splice(i, 1);
      }
    }

    return initialLength - sampleNotifications.length;
  }

  // التحقق من الساعات الهادئة
  static isQuietHours(userId: number): boolean {
    const settings = this.getUserNotificationSettings(userId);

    if (!settings || !settings.quietHours.enabled) {
      return false;
    }

    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();

    const [startHour, startMin] = settings.quietHours.start.split(':').map(Number);
    const [endHour, endMin] = settings.quietHours.end.split(':').map(Number);

    const startTime = startHour * 60 + startMin;
    const endTime = endHour * 60 + endMin;

    if (startTime <= endTime) {
      return currentTime >= startTime && currentTime <= endTime;
    } else {
      // الساعات الهادئة تمتد عبر منتصف الليل
      return currentTime >= startTime || currentTime <= endTime;
    }
  }
}
