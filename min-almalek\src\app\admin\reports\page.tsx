'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';

interface ReportData {
  totalRevenue: number;
  monthlyRevenue: number;
  totalAds: number;
  monthlyAds: number;
  totalUsers: number;
  monthlyUsers: number;
  totalViews: number;
  monthlyViews: number;
  categoryStats: { category: string; count: number; percentage: number }[];
  locationStats: { location: string; count: number; percentage: number }[];
  subscriptionStats: { plan: string; count: number; revenue: number }[];
  dailyStats: { date: string; ads: number; users: number; revenue: number }[];
}

export default function ReportsPage() {
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  useEffect(() => {
    loadReportData();
  }, [selectedPeriod]);

  const loadReportData = async () => {
    try {
      setLoading(true);
      // محاكاة تحميل بيانات التقارير
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockData: ReportData = {
        totalRevenue: 125000000, // بالليرة السورية
        monthlyRevenue: 15600000,
        totalAds: 1247,
        monthlyAds: 156,
        totalUsers: 3456,
        monthlyUsers: 234,
        totalViews: 89340,
        monthlyViews: 12450,
        categoryStats: [
          { category: 'عقارات', count: 567, percentage: 45.5 },
          { category: 'سيارات', count: 234, percentage: 18.8 },
          { category: 'وظائف', count: 189, percentage: 15.2 },
          { category: 'إلكترونيات', count: 145, percentage: 11.6 },
          { category: 'أخرى', count: 112, percentage: 8.9 }
        ],
        locationStats: [
          { location: 'دمشق', count: 456, percentage: 36.6 },
          { location: 'حلب', count: 234, percentage: 18.8 },
          { location: 'حمص', count: 178, percentage: 14.3 },
          { location: 'اللاذقية', count: 123, percentage: 9.9 },
          { location: 'أخرى', count: 256, percentage: 20.5 }
        ],
        subscriptionStats: [
          { plan: 'مجاني', count: 2100, revenue: 0 },
          { plan: 'مميز', count: 890, revenue: 45000000 },
          { plan: 'تجاري', count: 345, revenue: 65000000 },
          { plan: 'مكتب عقاري', count: 121, revenue: 15000000 }
        ],
        dailyStats: [
          { date: '2024-01-15', ads: 23, users: 45, revenue: 2300000 },
          { date: '2024-01-16', ads: 18, users: 38, revenue: 1800000 },
          { date: '2024-01-17', ads: 31, users: 52, revenue: 3100000 },
          { date: '2024-01-18', ads: 27, users: 41, revenue: 2700000 },
          { date: '2024-01-19', ads: 35, users: 58, revenue: 3500000 },
          { date: '2024-01-20', ads: 22, users: 36, revenue: 2200000 }
        ]
      };

      setReportData(mockData);
    } catch (error) {
      console.error('خطأ في تحميل التقارير:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SY', {
      style: 'currency',
      currency: 'SYP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const StatCard = ({ title, value, change, icon, color }: {
    title: string;
    value: string | number;
    change: string;
    icon: string;
    color: string;
  }) => (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className={`text-2xl font-bold ${color}`}>{value}</p>
          <p className="text-xs text-green-600 mt-1">
            ↗️ {change}
          </p>
        </div>
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${color.replace('text-', 'bg-').replace('-600', '-100')}`}>
          <span className="text-xl">{icon}</span>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    );
  }

  if (!reportData) {
    return (
      <AdminLayout>
        <div className="text-center py-12">
          <p className="text-gray-500">حدث خطأ في تحميل التقارير</p>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* العنوان والفلاتر */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'Cairo, sans-serif' }}>
              التقارير والإحصائيات
            </h1>
            <p className="text-gray-600 mt-1">
              تحليل شامل لأداء الموقع والإيرادات
            </p>
          </div>
          
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          >
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
            <option value="quarter">هذا الربع</option>
            <option value="year">هذا العام</option>
          </select>
        </div>

        {/* الإحصائيات الرئيسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="إجمالي الإيرادات"
            value={formatCurrency(reportData.totalRevenue)}
            change="+23% من الشهر الماضي"
            icon="💰"
            color="text-green-600"
          />
          <StatCard
            title="إيرادات الشهر"
            value={formatCurrency(reportData.monthlyRevenue)}
            change="+15% من الشهر الماضي"
            icon="📈"
            color="text-blue-600"
          />
          <StatCard
            title="إجمالي الإعلانات"
            value={reportData.totalAds.toLocaleString()}
            change="+12% من الشهر الماضي"
            icon="📋"
            color="text-purple-600"
          />
          <StatCard
            title="إجمالي المستخدمين"
            value={reportData.totalUsers.toLocaleString()}
            change="+18% من الشهر الماضي"
            icon="👥"
            color="text-orange-600"
          />
        </div>

        {/* الرسوم البيانية */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* إحصائيات التصنيفات */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">الإعلانات حسب التصنيف</h3>
            <div className="space-y-3">
              {reportData.categoryStats.map((stat, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-4 h-4 rounded-full bg-primary-500"></div>
                    <span className="text-sm font-medium text-gray-700">{stat.category}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">{stat.count}</span>
                    <span className="text-xs text-gray-500">({stat.percentage}%)</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* إحصائيات المواقع */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">الإعلانات حسب الموقع</h3>
            <div className="space-y-3">
              {reportData.locationStats.map((stat, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-4 h-4 rounded-full bg-blue-500"></div>
                    <span className="text-sm font-medium text-gray-700">{stat.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">{stat.count}</span>
                    <span className="text-xs text-gray-500">({stat.percentage}%)</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* إحصائيات الاشتراكات */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">إحصائيات الاشتراكات</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    نوع الاشتراك
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    عدد المشتركين
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإيرادات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    متوسط الإيراد لكل مشترك
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reportData.subscriptionStats.map((stat, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {stat.plan}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {stat.count.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(stat.revenue)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {stat.count > 0 ? formatCurrency(stat.revenue / stat.count) : '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* النشاط اليومي */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">النشاط اليومي (آخر 7 أيام)</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    إعلانات جديدة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    مستخدمين جدد
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإيرادات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {reportData.dailyStats.map((stat, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {new Date(stat.date).toLocaleDateString('ar-SY')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {stat.ads}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {stat.users}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatCurrency(stat.revenue)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* أزرار التصدير */}
        <div className="flex gap-4">
          <button className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
            📊 تصدير التقرير (PDF)
          </button>
          <button className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
            📈 تصدير البيانات (Excel)
          </button>
        </div>
      </div>
    </AdminLayout>
  );
}
