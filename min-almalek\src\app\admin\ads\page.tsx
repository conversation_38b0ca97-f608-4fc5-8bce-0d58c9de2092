'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminLayout from '@/components/admin/AdminLayout';

interface Ad {
  id: number;
  title: string;
  description: string;
  price: number;
  currency: string;
  category: string;
  subcategory: string;
  location: {
    governorate: string;
    city: string;
  };
  seller: {
    name: string;
    phone: string;
    email: string;
    verified: boolean;
  };
  status: 'pending' | 'active' | 'rejected' | 'expired' | 'sold';
  featured: boolean;
  views: number;
  favorites: number;
  createdAt: string;
  updatedAt: string;
  expiresAt: string;
}

export default function AllAdsPage() {
  const [ads, setAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [selectedAds, setSelectedAds] = useState<number[]>([]);
  const router = useRouter();

  useEffect(() => {
    loadAds();
  }, []);

  const loadAds = async () => {
    try {
      // محاكاة تحميل الإعلانات
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockAds: Ad[] = [
        {
          id: 1,
          title: 'شقة للبيع في دمشق - المالكي - 150 متر',
          description: 'شقة مميزة للبيع في منطقة المالكي الراقية، الطابق الخامس مع مصعد، 3 غرف نوم، صالون، مطبخ، حمامين.',
          price: 85000000,
          currency: 'SYP',
          category: 'real-estate',
          subcategory: 'apartments-sale',
          location: {
            governorate: 'damascus',
            city: 'damascus'
          },
          seller: {
            name: 'أحمد محمد',
            phone: '+963944123456',
            email: '<EMAIL>',
            verified: true
          },
          status: 'active',
          featured: true,
          views: 1250,
          favorites: 45,
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-20T14:22:00Z',
          expiresAt: '2024-02-15T10:30:00Z'
        },
        {
          id: 2,
          title: 'سيارة تويوتا كامري 2020 للبيع',
          description: 'سيارة تويوتا كامري موديل 2020، حالة ممتازة، فحص كامل، لون أبيض، مكيف، نظام صوتي.',
          price: 35000000,
          currency: 'SYP',
          category: 'cars',
          subcategory: 'cars-sale',
          location: {
            governorate: 'aleppo',
            city: 'aleppo'
          },
          seller: {
            name: 'محمد علي',
            phone: '+963933456789',
            email: '<EMAIL>',
            verified: false
          },
          status: 'pending',
          featured: false,
          views: 890,
          favorites: 23,
          createdAt: '2024-01-18T16:45:00Z',
          updatedAt: '2024-01-18T16:45:00Z',
          expiresAt: '2024-02-18T16:45:00Z'
        },
        {
          id: 3,
          title: 'وظيفة مطور ويب في شركة تقنية',
          description: 'مطلوب مطور ويب خبرة 3 سنوات، معرفة بـ React و Node.js، راتب مجزي، بيئة عمل ممتازة.',
          price: 2500000,
          currency: 'SYP',
          category: 'jobs',
          subcategory: 'technology',
          location: {
            governorate: 'damascus',
            city: 'damascus'
          },
          seller: {
            name: 'شركة التقنيات المتقدمة',
            phone: '+963911234567',
            email: '<EMAIL>',
            verified: true
          },
          status: 'active',
          featured: false,
          views: 567,
          favorites: 12,
          createdAt: '2024-01-19T09:15:00Z',
          updatedAt: '2024-01-19T09:15:00Z',
          expiresAt: '2024-02-19T09:15:00Z'
        }
      ];

      setAds(mockAds);
    } catch (error) {
      console.error('خطأ في تحميل الإعلانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredAds = ads.filter(ad => {
    const matchesSearch = ad.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ad.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ad.seller.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = filterStatus === 'all' || ad.status === filterStatus;
    const matchesCategory = filterCategory === 'all' || ad.category === filterCategory;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  const handleStatusChange = async (adId: number, newStatus: string) => {
    try {
      setAds(prev => prev.map(ad => 
        ad.id === adId ? { ...ad, status: newStatus as any } : ad
      ));
      alert(`تم تغيير حالة الإعلان إلى: ${getStatusLabel(newStatus)}`);
    } catch (error) {
      alert('حدث خطأ في تغيير حالة الإعلان');
    }
  };

  const handleToggleFeatured = async (adId: number) => {
    try {
      setAds(prev => prev.map(ad => 
        ad.id === adId ? { ...ad, featured: !ad.featured } : ad
      ));
    } catch (error) {
      alert('حدث خطأ في تغيير حالة الإعلان المميز');
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedAds.length === 0) {
      alert('يرجى اختيار إعلان واحد على الأقل');
      return;
    }

    try {
      switch (action) {
        case 'approve':
          setAds(prev => prev.map(ad => 
            selectedAds.includes(ad.id) ? { ...ad, status: 'active' } : ad
          ));
          break;
        case 'reject':
          setAds(prev => prev.map(ad => 
            selectedAds.includes(ad.id) ? { ...ad, status: 'rejected' } : ad
          ));
          break;
        case 'feature':
          setAds(prev => prev.map(ad => 
            selectedAds.includes(ad.id) ? { ...ad, featured: true } : ad
          ));
          break;
        case 'unfeature':
          setAds(prev => prev.map(ad => 
            selectedAds.includes(ad.id) ? { ...ad, featured: false } : ad
          ));
          break;
      }
      setSelectedAds([]);
      alert(`تم تطبيق الإجراء على ${selectedAds.length} إعلان`);
    } catch (error) {
      alert('حدث خطأ في تطبيق الإجراء');
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتظار';
      case 'active': return 'نشط';
      case 'rejected': return 'مرفوض';
      case 'expired': return 'منتهي الصلاحية';
      case 'sold': return 'تم البيع';
      default: return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'active': return 'bg-green-100 text-green-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      case 'expired': return 'bg-gray-100 text-gray-800';
      case 'sold': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('ar-SY', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* العنوان والفلاتر */}
        <div>
          <div className="flex justify-between items-center mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'Cairo, sans-serif' }}>
                إدارة جميع الإعلانات
              </h1>
              <p className="text-gray-600 mt-1">
                {filteredAds.length} من أصل {ads.length} إعلان
              </p>
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={() => router.push('/admin/ads/pending')}
                className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors"
              >
                الإعلانات المنتظرة ({ads.filter(ad => ad.status === 'pending').length})
              </button>
            </div>
          </div>
          
          <div className="flex flex-col lg:flex-row gap-4">
            {/* البحث */}
            <div className="flex-1">
              <input
                type="text"
                placeholder="البحث في الإعلانات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
            
            {/* الفلاتر */}
            <div className="flex gap-4">
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="all">جميع الحالات</option>
                <option value="pending">في الانتظار</option>
                <option value="active">نشط</option>
                <option value="rejected">مرفوض</option>
                <option value="expired">منتهي الصلاحية</option>
                <option value="sold">تم البيع</option>
              </select>
              
              <select
                value={filterCategory}
                onChange={(e) => setFilterCategory(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="all">جميع التصنيفات</option>
                <option value="real-estate">عقارات</option>
                <option value="cars">سيارات</option>
                <option value="jobs">وظائف</option>
                <option value="electronics">إلكترونيات</option>
              </select>
            </div>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          {[
            { label: 'المجموع', count: ads.length, color: 'text-blue-600' },
            { label: 'نشط', count: ads.filter(ad => ad.status === 'active').length, color: 'text-green-600' },
            { label: 'في الانتظار', count: ads.filter(ad => ad.status === 'pending').length, color: 'text-yellow-600' },
            { label: 'مرفوض', count: ads.filter(ad => ad.status === 'rejected').length, color: 'text-red-600' },
            { label: 'مميز', count: ads.filter(ad => ad.featured).length, color: 'text-purple-600' }
          ].map((stat, index) => (
            <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
              <div className="text-center">
                <p className={`text-2xl font-bold ${stat.color}`}>{stat.count}</p>
                <p className="text-sm text-gray-600">{stat.label}</p>
              </div>
            </div>
          ))}
        </div>

        {/* الإجراءات المجمعة */}
        {selectedAds.length > 0 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-blue-800 font-medium">
                تم اختيار {selectedAds.length} إعلان
              </span>
              <div className="flex gap-2">
                <button
                  onClick={() => handleBulkAction('approve')}
                  className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700"
                >
                  قبول
                </button>
                <button
                  onClick={() => handleBulkAction('reject')}
                  className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                >
                  رفض
                </button>
                <button
                  onClick={() => handleBulkAction('feature')}
                  className="bg-purple-600 text-white px-3 py-1 rounded text-sm hover:bg-purple-700"
                >
                  جعل مميز
                </button>
                <button
                  onClick={() => setSelectedAds([])}
                  className="bg-gray-600 text-white px-3 py-1 rounded text-sm hover:bg-gray-700"
                >
                  إلغاء التحديد
                </button>
              </div>
            </div>
          </div>
        )}

        {/* جدول الإعلانات */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right">
                    <input
                      type="checkbox"
                      checked={selectedAds.length === filteredAds.length && filteredAds.length > 0}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedAds(filteredAds.map(ad => ad.id));
                        } else {
                          setSelectedAds([]);
                        }
                      }}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإعلان
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    البائع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    السعر
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإحصائيات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredAds.map((ad) => (
                  <tr key={ad.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedAds.includes(ad.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedAds(prev => [...prev, ad.id]);
                          } else {
                            setSelectedAds(prev => prev.filter(id => id !== ad.id));
                          }
                        }}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center gap-3">
                        {ad.featured && <span className="text-yellow-500">⭐</span>}
                        <div>
                          <div className="text-sm font-medium text-gray-900 line-clamp-1">
                            {ad.title}
                          </div>
                          <div className="text-sm text-gray-500">
                            {ad.category} • {ad.location.governorate}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="flex items-center gap-1">
                          <span className="text-sm font-medium text-gray-900">{ad.seller.name}</span>
                          {ad.seller.verified && <span className="text-green-500">✓</span>}
                        </div>
                        <div className="text-sm text-gray-500">{ad.seller.phone}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatCurrency(ad.price, ad.currency)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(ad.status)}`}>
                        {getStatusLabel(ad.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div>👁️ {ad.views}</div>
                      <div>❤️ {ad.favorites}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(ad.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <select
                          value={ad.status}
                          onChange={(e) => handleStatusChange(ad.id, e.target.value)}
                          className="text-xs border border-gray-300 rounded px-2 py-1"
                        >
                          <option value="pending">في الانتظار</option>
                          <option value="active">نشط</option>
                          <option value="rejected">مرفوض</option>
                          <option value="expired">منتهي</option>
                          <option value="sold">تم البيع</option>
                        </select>
                        <button
                          onClick={() => handleToggleFeatured(ad.id)}
                          className={`text-xs px-2 py-1 rounded ${
                            ad.featured 
                              ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' 
                              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                          }`}
                        >
                          {ad.featured ? '⭐' : '☆'}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
