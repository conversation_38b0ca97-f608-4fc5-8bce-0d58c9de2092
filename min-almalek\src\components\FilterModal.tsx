'use client';

import { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';

interface FilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  filters: any;
  onFilterChange: (key: string, value: string | string[]) => void;
  onClearFilters: () => void;
  category: string;
  // Props للعقارات
  listingTypes?: Array<{value: string, label: string}>;
  propertyTypes?: Array<{value: string, label: string}>;
  roomOptions?: Array<{value: string, label: string}>;
  bathroomOptions?: Array<{value: string, label: string}>;
  floorOptions?: Array<{value: string, label: string}>;
  conditionOptions?: Array<{value: string, label: string}>;
  features?: Array<{value: string, label: string}>;
  // Props للسياحة
  serviceTypes?: Array<{value: string, label: string}>;
  accommodationTypes?: Array<{value: string, label: string}>;
  transportTypes?: Array<{value: string, label: string}>;
  tripTypes?: Array<{value: string, label: string}>;
  ratingOptions?: Array<{value: string, label: string}>;
  durationOptions?: Array<{value: string, label: string}>;
  languageOptions?: Array<{value: string, label: string}>;
  // Props للإلكترونيات
  electronicsCategories?: Array<{value: string, label: string}>;
  subcategoriesByCategory?: any;
  brands?: string[];
  warrantyOptions?: string[];
  // Props عامة
  locations?: string[];
}

function FilterModal({
  isOpen,
  onClose,
  filters,
  onFilterChange,
  onClearFilters,
  category,
  listingTypes = [],
  propertyTypes = [],
  roomOptions = [],
  bathroomOptions = [],
  floorOptions = [],
  conditionOptions = [],
  features = [],
  serviceTypes = [],
  accommodationTypes = [],
  transportTypes = [],
  tripTypes = [],
  ratingOptions = [],
  durationOptions = [],
  languageOptions = [],
  electronicsCategories = [],
  subcategoriesByCategory = {},
  brands = [],
  warrantyOptions = [],
  locations = []
}: FilterModalProps) {
  const [mounted, setMounted] = useState(false);

  // قائمة المحافظات الكاملة
  const allLocations = locations.length > 0 ? locations : [
    'دمشق',
    'ريف دمشق',
    'حلب',
    'حمص',
    'حماة',
    'اللاذقية',
    'طرطوس',
    'إدلب',
    'الحسكة',
    'القامشلي',
    'الرقة',
    'دير الزور',
    'السويداء',
    'درعا',
    'القنيطرة'
  ];

  // دالة لعرض الفلاتر النشطة
  const getActiveFilters = () => {
    const activeFilters: Array<{key: string, value: string, label: string}> = [];

    // التحقق من وجود filters وأنه كائن صالح
    if (!filters || typeof filters !== 'object') {
      return activeFilters;
    }

    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== '' && (!Array.isArray(value) || value.length > 0)) {
        let label = '';
        let displayValue = '';

        if (Array.isArray(value)) {
          displayValue = value.join(', ');
        } else {
          displayValue = value.toString();
        }

        // تحديد التسمية حسب المفتاح
        switch (key) {
          case 'listingType': label = 'نوع الإعلان'; break;
          case 'propertyType': label = 'نوع العقار'; break;
          case 'category': label = 'الفئة'; break;
          case 'subcategory': label = 'الفئة الفرعية'; break;
          case 'brand': label = 'الماركة'; break;
          case 'condition': label = 'الحالة'; break;
          case 'location': label = 'الموقع'; break;
          case 'priceFrom': label = 'السعر من'; break;
          case 'priceTo': label = 'السعر إلى'; break;
          case 'rooms': label = 'الغرف'; break;
          case 'bathrooms': label = 'الحمامات'; break;
          case 'serviceType': label = 'نوع الخدمة'; break;
          case 'accommodationType': label = 'نوع الإقامة'; break;
          case 'transportType': label = 'وسيلة النقل'; break;
          case 'features': label = 'المميزات'; break;
          case 'searchQuery': label = 'البحث'; break;
          case 'model': label = 'الموديل'; break;
          case 'warranty': label = 'الضمان'; break;
          default: label = key;
        }

        activeFilters.push({key, value: displayValue, label});
      }
    });

    return activeFilters;
  };

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!mounted || !isOpen) return null;

  const getCategoryColor = () => {
    switch (category) {
      case 'real-estate': return 'rgba(59, 130, 246, 0.6)'; // blue
      case 'tourism': return 'rgba(14, 165, 233, 0.6)'; // sky
      case 'cars': return 'rgba(239, 68, 68, 0.6)'; // red
      case 'electronics': return 'rgba(168, 85, 247, 0.6)'; // purple
      case 'jobs': return 'rgba(34, 197, 94, 0.6)'; // green
      case 'services': return 'rgba(249, 115, 22, 0.6)'; // orange
      case 'fashion': return 'rgba(236, 72, 153, 0.6)'; // pink
      case 'furniture': return 'rgba(234, 179, 8, 0.6)'; // yellow
      case 'sports': return 'rgba(99, 102, 241, 0.6)'; // indigo
      case 'books': return 'rgba(20, 184, 166, 0.6)'; // teal
      case 'pets': return 'rgba(245, 158, 11, 0.6)'; // amber
      case 'health-beauty': return 'rgba(236, 72, 153, 0.6)'; // pink
      case 'food': return 'rgba(234, 179, 8, 0.6)'; // yellow
      default: return 'rgba(34, 197, 94, 0.6)';
    }
  };

  const modalContent = (
    <div className="fixed inset-0 z-50 lg:hidden">
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div className="fixed inset-x-0 bottom-0 z-50 max-h-[95vh] overflow-hidden">
        <div className="bg-white rounded-t-2xl shadow-xl transform transition-transform">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h2 className="text-lg font-bold text-gray-800 flex items-center gap-2">
              <img
                src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                alt="فلاتر"
                className="w-6 h-6 opacity-80"
                style={{
                  filter: `drop-shadow(0 0 4px ${getCategoryColor()})`
                }}
              />
              البحث والفلاتر
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Active Filters */}
          {getActiveFilters().length > 0 && (
            <div className="p-4 bg-gray-50 border-b border-gray-200">
              <h3 className="text-sm font-medium text-gray-700 mb-2">الفلاتر النشطة:</h3>
              <div className="flex flex-wrap gap-2">
                {getActiveFilters().map((filter, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    <span className="font-semibold">{filter.label}:</span>
                    <span className="mr-1">{filter.value}</span>
                    <button
                      onClick={() => onFilterChange(filter.key, Array.isArray(filters[filter.key]) ? [] : '')}
                      className="mr-1 text-blue-600 hover:text-blue-800 font-bold"
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* Content */}
          <div className="p-4 max-h-[65vh] overflow-y-auto">
            {/* البحث - مشترك لجميع الفئات */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
              <div className="relative">
                <input
                  type="text"
                  placeholder={`ابحث في ${category === 'real-estate' ? 'العقارات' :
                    category === 'cars' ? 'السيارات' :
                    category === 'electronics' ? 'الإلكترونيات' :
                    category === 'services' ? 'الخدمات' :
                    category === 'fashion' ? 'الأزياء' :
                    category === 'furniture' ? 'الأثاث' :
                    category === 'sports' ? 'الرياضة' :
                    category === 'books' ? 'الكتب' :
                    category === 'pets' ? 'الحيوانات الأليفة' :
                    category === 'health-beauty' ? 'الصحة والجمال' :
                    category === 'food' ? 'الطعام والمشروبات' :
                    category === 'tourism' ? 'السياحة والسفر' : 'المنتجات'}...`}
                  value={(filters && filters.searchQuery) || ''}
                  onChange={(e) => onFilterChange('searchQuery', e.target.value)}
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:border-blue-500"
                  style={{
                    focusRingColor: getCategoryColor().replace('0.6', '1')
                  }}
                />
                <img
                  src="/images/jobs/Jobs -Personals/icons/search_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="بحث"
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 opacity-60"
                />
              </div>
            </div>

            {/* Real Estate Filters */}
            {category === 'real-estate' && (
              <div className="space-y-4">
                {/* نوع الإعلان */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع الإعلان</label>
                  <select
                    value={(filters && filters.listingType) || ''}
                    onChange={(e) => onFilterChange('listingType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">جميع الأنواع</option>
                    {listingTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                {/* نوع العقار */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع العقار</label>
                  <select
                    value={filters.propertyType || ''}
                    onChange={(e) => onFilterChange('propertyType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">جميع الأنواع</option>
                    {propertyTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                {/* عدد الغرف */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">عدد الغرف</label>
                  <select
                    value={filters.rooms || ''}
                    onChange={(e) => onFilterChange('rooms', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">جميع الأعداد</option>
                    {roomOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>

                {/* عدد الحمامات */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">عدد الحمامات</label>
                  <select
                    value={filters.bathrooms || ''}
                    onChange={(e) => onFilterChange('bathrooms', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">جميع الأعداد</option>
                    {bathroomOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>

                {/* الطابق */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الطابق</label>
                  <select
                    value={filters.floor || ''}
                    onChange={(e) => onFilterChange('floor', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">جميع الطوابق</option>
                    {floorOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>

                {/* حالة العقار */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">حالة العقار</label>
                  <select
                    value={filters.condition || ''}
                    onChange={(e) => onFilterChange('condition', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">جميع الحالات</option>
                    {conditionOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>

                {/* المساحة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المساحة (متر مربع)</label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="number"
                      placeholder="من"
                      value={filters.areaFrom || ''}
                      onChange={(e) => onFilterChange('areaFrom', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <input
                      type="number"
                      placeholder="إلى"
                      value={filters.areaTo || ''}
                      onChange={(e) => onFilterChange('areaTo', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                {/* نطاق السعر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="number"
                      placeholder="من"
                      value={filters.priceFrom || ''}
                      onChange={(e) => onFilterChange('priceFrom', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <input
                      type="number"
                      placeholder="إلى"
                      value={filters.priceTo || ''}
                      onChange={(e) => onFilterChange('priceTo', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                {/* الموقع */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المحافظة</label>
                  <select
                    value={filters.location || ''}
                    onChange={(e) => onFilterChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">جميع المحافظات</option>
                    {allLocations.map(location => (
                      <option key={location} value={location}>{location}</option>
                    ))}
                  </select>
                </div>

                {/* المميزات */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المميزات</label>
                  <div className="space-y-2 max-h-32 overflow-y-auto">
                    {features.map(feature => (
                      <label key={feature.value} className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={filters.features?.includes(feature.value) || false}
                          onChange={(e) => {
                            const currentFeatures = filters.features || [];
                            if (e.target.checked) {
                              onFilterChange('features', [...currentFeatures, feature.value]);
                            } else {
                              onFilterChange('features', currentFeatures.filter((f: string) => f !== feature.value));
                            }
                          }}
                          className="rounded border-gray-300 text-blue-500 focus:ring-blue-500"
                        />
                        <span className="text-sm text-gray-700">{feature.label}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Tourism Filters */}
            {category === 'tourism' && (
              <div className="space-y-4">
                {/* نوع الخدمة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع الخدمة</label>
                  <select
                    value={filters.serviceType || ''}
                    onChange={(e) => onFilterChange('serviceType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                  >
                    <option value="">جميع الخدمات</option>
                    {serviceTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                {/* نوع الإقامة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع الإقامة</label>
                  <select
                    value={filters.accommodationType || ''}
                    onChange={(e) => onFilterChange('accommodationType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                  >
                    <option value="">جميع الأنواع</option>
                    {accommodationTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                {/* وسائل النقل */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">وسائل النقل</label>
                  <select
                    value={filters.transportType || ''}
                    onChange={(e) => onFilterChange('transportType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                  >
                    <option value="">جميع الوسائل</option>
                    {transportTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                {/* نوع الرحلة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع الرحلة</label>
                  <select
                    value={filters.tripType || ''}
                    onChange={(e) => onFilterChange('tripType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                  >
                    <option value="">جميع الأنواع</option>
                    {tripTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                </div>

                {/* التقييم */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">التقييم</label>
                  <select
                    value={filters.rating || ''}
                    onChange={(e) => onFilterChange('rating', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                  >
                    <option value="">جميع التقييمات</option>
                    {ratingOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>

                {/* المدة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المدة</label>
                  <select
                    value={filters.duration || ''}
                    onChange={(e) => onFilterChange('duration', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                  >
                    <option value="">جميع المدد</option>
                    {durationOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>

                {/* اللغة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">اللغة</label>
                  <select
                    value={filters.language || ''}
                    onChange={(e) => onFilterChange('language', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                  >
                    <option value="">جميع اللغات</option>
                    {languageOptions.map(option => (
                      <option key={option.value} value={option.value}>{option.label}</option>
                    ))}
                  </select>
                </div>

                {/* نطاق السعر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="number"
                      placeholder="من"
                      value={filters.priceFrom || ''}
                      onChange={(e) => onFilterChange('priceFrom', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                    />
                    <input
                      type="number"
                      placeholder="إلى"
                      value={filters.priceTo || ''}
                      onChange={(e) => onFilterChange('priceTo', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                    />
                  </div>
                </div>

                {/* المحافظة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المحافظة</label>
                  <select
                    value={filters.location || ''}
                    onChange={(e) => onFilterChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                  >
                    <option value="">جميع المحافظات</option>
                    {allLocations.map(location => (
                      <option key={location} value={location}>{location}</option>
                    ))}
                  </select>
                </div>
              </div>
            )}

            {/* Electronics Filters */}
            {category === 'electronics' && (
              <div className="space-y-4">
                {/* الفئة الرئيسية */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الرئيسية</label>
                  <select
                    value={filters.category || ''}
                    onChange={(e) => {
                      onFilterChange('category', e.target.value);
                      // مسح الفئة الفرعية عند تغيير الفئة الرئيسية
                      onFilterChange('subcategory', '');
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="">جميع الفئات</option>
                    {electronicsCategories.map(category => (
                      <option key={category.value} value={category.value}>{category.label}</option>
                    ))}
                  </select>
                </div>

                {/* الفئة الفرعية - تظهر فقط عند اختيار فئة رئيسية */}
                {filters.category && subcategoriesByCategory[filters.category] && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الفرعية</label>
                    <select
                      value={filters.subcategory || ''}
                      onChange={(e) => onFilterChange('subcategory', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    >
                      <option value="">جميع الفئات الفرعية</option>
                      {subcategoriesByCategory[filters.category].map((sub: any) => (
                        <option key={sub.value} value={sub.value}>{sub.label}</option>
                      ))}
                    </select>
                  </div>
                )}

                {/* الماركة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الماركة</label>
                  <select
                    value={filters.brand || ''}
                    onChange={(e) => onFilterChange('brand', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="">جميع الماركات</option>
                    {brands.map(brand => (
                      <option key={brand} value={brand}>{brand}</option>
                    ))}
                  </select>
                </div>

                {/* الموديل */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الموديل</label>
                  <input
                    type="text"
                    value={filters.model || ''}
                    onChange={(e) => onFilterChange('model', e.target.value)}
                    placeholder="اكتب الموديل..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>

                {/* الحالة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                  <select
                    value={filters.condition || ''}
                    onChange={(e) => onFilterChange('condition', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option key="all-conditions" value="">جميع الحالات</option>
                    <option key="new" value="new">جديد</option>
                    <option key="like-new" value="like-new">كالجديد</option>
                    <option key="good" value="good">جيد</option>
                    <option key="fair" value="fair">مقبول</option>
                    <option key="poor" value="poor">يحتاج إصلاح</option>
                  </select>
                </div>

                {/* نطاق السعر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="number"
                      placeholder="من"
                      value={filters.priceFrom || ''}
                      onChange={(e) => onFilterChange('priceFrom', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    />
                    <input
                      type="number"
                      placeholder="إلى"
                      value={filters.priceTo || ''}
                      onChange={(e) => onFilterChange('priceTo', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                </div>

                {/* الضمان */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الضمان</label>
                  <select
                    value={filters.warranty || ''}
                    onChange={(e) => onFilterChange('warranty', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="">جميع أنواع الضمان</option>
                    {warrantyOptions.map(warranty => (
                      <option key={warranty} value={warranty}>{warranty}</option>
                    ))}
                  </select>
                </div>

                {/* المحافظة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المحافظة</label>
                  <select
                    value={filters.location || ''}
                    onChange={(e) => onFilterChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="">جميع المحافظات</option>
                    {allLocations.map(location => (
                      <option key={location} value={location}>{location}</option>
                    ))}
                  </select>
                </div>
              </div>
            )}

            {/* Services Filters */}
            {category === 'services' && (
              <div className="space-y-4">
                {/* نوع الخدمة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع الخدمة</label>
                  <select
                    value={filters.serviceType || ''}
                    onChange={(e) => onFilterChange('serviceType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  >
                    <option key="all-services" value="">جميع الخدمات</option>
                    <option key="cleaning" value="cleaning">التنظيف</option>
                    <option key="maintenance" value="maintenance">الصيانة</option>
                    <option key="delivery" value="delivery">التوصيل</option>
                    <option key="tutoring" value="tutoring">التدريس</option>
                    <option key="design" value="design">التصميم</option>
                    <option key="photography" value="photography">التصوير</option>
                    <option key="consulting" value="consulting">الاستشارات</option>
                    <option key="legal" value="legal">الخدمات القانونية</option>
                  </select>
                </div>

                {/* مقدم الخدمة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">مقدم الخدمة</label>
                  <select
                    value={filters.providerType || ''}
                    onChange={(e) => onFilterChange('providerType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  >
                    <option key="all-provider-types" value="">جميع الأنواع</option>
                    <option key="individual" value="individual">فرد</option>
                    <option key="company" value="company">شركة</option>
                    <option key="freelancer" value="freelancer">مستقل</option>
                  </select>
                </div>

                {/* مستوى الخبرة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">مستوى الخبرة</label>
                  <select
                    value={filters.experience || ''}
                    onChange={(e) => onFilterChange('experience', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  >
                    <option key="all-levels" value="">جميع المستويات</option>
                    <option key="beginner" value="beginner">مبتدئ</option>
                    <option key="intermediate" value="intermediate">متوسط</option>
                    <option key="expert" value="expert">خبير</option>
                    <option key="professional" value="professional">محترف</option>
                  </select>
                </div>

                {/* التوفر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">التوفر</label>
                  <select
                    value={filters.availability || ''}
                    onChange={(e) => onFilterChange('availability', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  >
                    <option key="all-availability" value="">جميع الأوقات</option>
                    <option key="immediate" value="immediate">فوري</option>
                    <option key="within-week" value="within-week">خلال أسبوع</option>
                    <option key="flexible" value="flexible">مرن</option>
                  </select>
                </div>

                {/* نطاق السعر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="number"
                      placeholder="من"
                      value={filters.priceFrom || ''}
                      onChange={(e) => onFilterChange('priceFrom', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    />
                    <input
                      type="number"
                      placeholder="إلى"
                      value={filters.priceTo || ''}
                      onChange={(e) => onFilterChange('priceTo', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    />
                  </div>
                </div>

                {/* الموقع */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                  <select
                    value={filters.location || ''}
                    onChange={(e) => onFilterChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  >
                    <option value="">جميع المحافظات</option>
                    {allLocations.map(location => (
                      <option key={location} value={location}>{location}</option>
                    ))}
                  </select>
                </div>
              </div>
            )}

            {/* Fashion Filters */}
            {category === 'fashion' && (
              <div className="space-y-4">
                {/* الفئة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                  <select
                    value={filters.category || ''}
                    onChange={(e) => onFilterChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    <option key="all-fashion-categories" value="">جميع الفئات</option>
                    <option key="men" value="men">رجالي</option>
                    <option key="women" value="women">نسائي</option>
                    <option key="kids" value="kids">أطفال</option>
                    <option key="accessories" value="accessories">إكسسوارات</option>
                    <option key="shoes" value="shoes">أحذية</option>
                    <option key="bags" value="bags">حقائب</option>
                  </select>
                </div>

                {/* الحالة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                  <select
                    value={filters.condition || ''}
                    onChange={(e) => onFilterChange('condition', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    <option value="">جميع الحالات</option>
                    <option value="new">جديد</option>
                    <option value="like-new">كالجديد</option>
                    <option value="good">جيد</option>
                    <option value="fair">مقبول</option>
                  </select>
                </div>

                {/* المقاس */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المقاس</label>
                  <select
                    value={filters.size || ''}
                    onChange={(e) => onFilterChange('size', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    <option key="all-sizes" value="">جميع المقاسات</option>
                    <option key="xs" value="XS">XS</option>
                    <option key="s" value="S">S</option>
                    <option key="m" value="M">M</option>
                    <option key="l" value="L">L</option>
                    <option key="xl" value="XL">XL</option>
                    <option key="xxl" value="XXL">XXL</option>
                  </select>
                </div>

                {/* اللون */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">اللون</label>
                  <input
                    type="text"
                    placeholder="اللون"
                    value={filters.color || ''}
                    onChange={(e) => onFilterChange('color', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  />
                </div>

                {/* الماركة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الماركة</label>
                  <input
                    type="text"
                    placeholder="اسم الماركة"
                    value={filters.brand || ''}
                    onChange={(e) => onFilterChange('brand', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  />
                </div>

                {/* نطاق السعر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="number"
                      placeholder="من"
                      value={filters.priceFrom || ''}
                      onChange={(e) => onFilterChange('priceFrom', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                    />
                    <input
                      type="number"
                      placeholder="إلى"
                      value={filters.priceTo || ''}
                      onChange={(e) => onFilterChange('priceTo', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                    />
                  </div>
                </div>

                {/* الموقع */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                  <select
                    value={filters.location || ''}
                    onChange={(e) => onFilterChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    <option value="">جميع المحافظات</option>
                    {allLocations.map(location => (
                      <option key={location} value={location}>{location}</option>
                    ))}
                  </select>
                </div>
              </div>
            )}

            {/* Furniture Filters */}
            {category === 'furniture' && (
              <div className="space-y-4">
                {/* الفئة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                  <select
                    value={filters.category || ''}
                    onChange={(e) => onFilterChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  >
                    <option value="">جميع الفئات</option>
                    <option value="living-room">غرفة المعيشة</option>
                    <option value="bedroom">غرفة النوم</option>
                    <option value="kitchen">المطبخ</option>
                    <option value="office">المكتب</option>
                    <option value="outdoor">الحديقة</option>
                    <option value="storage">التخزين</option>
                  </select>
                </div>

                {/* المادة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المادة</label>
                  <select
                    value={filters.material || ''}
                    onChange={(e) => onFilterChange('material', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  >
                    <option value="">جميع المواد</option>
                    <option value="wood">خشب</option>
                    <option value="metal">معدن</option>
                    <option value="plastic">بلاستيك</option>
                    <option value="glass">زجاج</option>
                    <option value="fabric">قماش</option>
                    <option value="leather">جلد</option>
                  </select>
                </div>

                {/* اللون */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">اللون</label>
                  <input
                    type="text"
                    placeholder="اللون"
                    value={filters.color || ''}
                    onChange={(e) => onFilterChange('color', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  />
                </div>

                {/* الماركة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الماركة</label>
                  <input
                    type="text"
                    placeholder="اسم الماركة"
                    value={filters.brand || ''}
                    onChange={(e) => onFilterChange('brand', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  />
                </div>

                {/* الحالة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                  <select
                    value={filters.condition || ''}
                    onChange={(e) => onFilterChange('condition', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  >
                    <option value="">جميع الحالات</option>
                    <option value="new">جديد</option>
                    <option value="like-new">كالجديد</option>
                    <option value="good">جيد</option>
                    <option value="fair">مقبول</option>
                  </select>
                </div>

                {/* نطاق السعر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="number"
                      placeholder="من"
                      value={filters.priceFrom || ''}
                      onChange={(e) => onFilterChange('priceFrom', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                    />
                    <input
                      type="number"
                      placeholder="إلى"
                      value={filters.priceTo || ''}
                      onChange={(e) => onFilterChange('priceTo', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                    />
                  </div>
                </div>

                {/* الموقع */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                  <select
                    value={filters.location || ''}
                    onChange={(e) => onFilterChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  >
                    <option value="">جميع المحافظات</option>
                    {allLocations.map(location => (
                      <option key={location} value={location}>{location}</option>
                    ))}
                  </select>
                </div>
              </div>
            )}

            {/* Sports Filters */}
            {category === 'sports' && (
              <div className="space-y-4">
                {/* نوع الرياضة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع الرياضة</label>
                  <select
                    value={filters.sportType || ''}
                    onChange={(e) => onFilterChange('sportType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  >
                    <option value="">جميع الرياضات</option>
                    <option value="football">كرة القدم</option>
                    <option value="basketball">كرة السلة</option>
                    <option value="tennis">التنس</option>
                    <option value="swimming">السباحة</option>
                    <option value="gym">الجيم</option>
                    <option value="running">الجري</option>
                    <option value="cycling">الدراجات</option>
                    <option value="martial-arts">الفنون القتالية</option>
                  </select>
                </div>

                {/* الماركة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الماركة</label>
                  <input
                    type="text"
                    placeholder="اسم الماركة"
                    value={filters.brand || ''}
                    onChange={(e) => onFilterChange('brand', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>

                {/* المقاس */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المقاس</label>
                  <select
                    value={filters.size || ''}
                    onChange={(e) => onFilterChange('size', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  >
                    <option value="">جميع المقاسات</option>
                    <option value="XS">XS</option>
                    <option value="S">S</option>
                    <option value="M">M</option>
                    <option value="L">L</option>
                    <option value="XL">XL</option>
                    <option value="XXL">XXL</option>
                  </select>
                </div>

                {/* الحالة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                  <select
                    value={filters.condition || ''}
                    onChange={(e) => onFilterChange('condition', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  >
                    <option value="">جميع الحالات</option>
                    <option value="new">جديد</option>
                    <option value="like-new">كالجديد</option>
                    <option value="good">جيد</option>
                    <option value="fair">مقبول</option>
                  </select>
                </div>

                {/* نطاق السعر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="number"
                      placeholder="من"
                      value={filters.priceFrom || ''}
                      onChange={(e) => onFilterChange('priceFrom', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    />
                    <input
                      type="number"
                      placeholder="إلى"
                      value={filters.priceTo || ''}
                      onChange={(e) => onFilterChange('priceTo', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    />
                  </div>
                </div>

                {/* الموقع */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                  <select
                    value={filters.location || ''}
                    onChange={(e) => onFilterChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  >
                    <option key="all-locations" value="">جميع المواقع</option>
                    <option key="damascus" value="دمشق">دمشق</option>
                    <option key="aleppo" value="حلب">حلب</option>
                    <option key="homs" value="حمص">حمص</option>
                    <option key="hama" value="حماة">حماة</option>
                    <option key="latakia" value="اللاذقية">اللاذقية</option>
                    <option key="tartous" value="طرطوس">طرطوس</option>
                  </select>
                </div>
              </div>
            )}

            {/* Books Filters */}
            {category === 'books' && (
              <div className="space-y-4">
                {/* الفئة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة</label>
                  <select
                    value={filters.category || ''}
                    onChange={(e) => onFilterChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">جميع الفئات</option>
                    <option value="academic">أكاديمي</option>
                    <option value="literature">أدب</option>
                    <option value="science">علوم</option>
                    <option value="history">تاريخ</option>
                    <option value="religion">ديني</option>
                    <option value="children">أطفال</option>
                    <option value="cooking">طبخ</option>
                    <option value="self-help">تطوير الذات</option>
                  </select>
                </div>

                {/* اللغة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">اللغة</label>
                  <select
                    value={filters.language || ''}
                    onChange={(e) => onFilterChange('language', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">جميع اللغات</option>
                    <option value="arabic">العربية</option>
                    <option value="english">الإنجليزية</option>
                    <option value="french">الفرنسية</option>
                    <option value="german">الألمانية</option>
                    <option value="spanish">الإسبانية</option>
                  </select>
                </div>

                {/* التنسيق */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">التنسيق</label>
                  <select
                    value={filters.format || ''}
                    onChange={(e) => onFilterChange('format', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">جميع التنسيقات</option>
                    <option value="hardcover">غلاف صلب</option>
                    <option value="paperback">غلاف ورقي</option>
                    <option value="ebook">كتاب إلكتروني</option>
                    <option value="audiobook">كتاب صوتي</option>
                  </select>
                </div>

                {/* المؤلف */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">المؤلف</label>
                  <input
                    type="text"
                    placeholder="اسم المؤلف"
                    value={filters.author || ''}
                    onChange={(e) => onFilterChange('author', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* دار النشر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">دار النشر</label>
                  <input
                    type="text"
                    placeholder="اسم دار النشر"
                    value={filters.publisher || ''}
                    onChange={(e) => onFilterChange('publisher', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* نطاق السعر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="number"
                      placeholder="من"
                      value={filters.priceFrom || ''}
                      onChange={(e) => onFilterChange('priceFrom', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                    <input
                      type="number"
                      placeholder="إلى"
                      value={filters.priceTo || ''}
                      onChange={(e) => onFilterChange('priceTo', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                {/* الموقع */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                  <select
                    value={filters.location || ''}
                    onChange={(e) => onFilterChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option key="all-locations-sports" value="">جميع المواقع</option>
                    <option key="damascus-sports" value="دمشق">دمشق</option>
                    <option key="aleppo-sports" value="حلب">حلب</option>
                    <option key="homs-sports" value="حمص">حمص</option>
                    <option key="hama-sports" value="حماة">حماة</option>
                    <option key="latakia-sports" value="اللاذقية">اللاذقية</option>
                    <option key="tartous-sports" value="طرطوس">طرطوس</option>
                  </select>
                </div>
              </div>
            )}

            {/* Pets Filters */}
            {category === 'pets' && (
              <div className="space-y-4">
                {/* الفئة الرئيسية */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الرئيسية</label>
                  <select
                    value={filters.mainCategory || ''}
                    onChange={(e) => onFilterChange('mainCategory', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="">جميع الفئات</option>
                    <option value="animals">الحيوانات</option>
                    <option value="supplies">مستلزمات الحيوانات</option>
                    <option value="services">خدمات الحيوانات</option>
                    <option value="accessories">إكسسوارات وأدوات</option>
                    <option value="additional-services">خدمات إضافية</option>
                  </select>
                </div>

                {/* نوع الحيوان */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع الحيوان</label>
                  <select
                    value={filters.subCategory || ''}
                    onChange={(e) => onFilterChange('subCategory', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="">جميع الأنواع</option>
                    <option value="dogs">كلاب</option>
                    <option value="cats">قطط</option>
                    <option value="birds">طيور</option>
                    <option value="fish">أسماك</option>
                    <option value="rabbits">أرانب</option>
                    <option value="hamsters">هامستر</option>
                    <option value="reptiles">زواحف</option>
                    <option value="farm-animals">حيوانات مزرعة</option>
                  </select>
                </div>

                {/* العمر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">العمر</label>
                  <select
                    value={filters.age || ''}
                    onChange={(e) => onFilterChange('age', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="">جميع الأعمار</option>
                    <option value="أقل من شهر">أقل من شهر</option>
                    <option value="1-3 أشهر">1-3 أشهر</option>
                    <option value="4-6 أشهر">4-6 أشهر</option>
                    <option value="7-12 شهر">7-12 شهر</option>
                    <option value="1-2 سنة">1-2 سنة</option>
                    <option value="3-5 سنوات">3-5 سنوات</option>
                    <option value="أكثر من 5 سنوات">أكثر من 5 سنوات</option>
                  </select>
                </div>

                {/* الجنس */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الجنس</label>
                  <select
                    value={filters.gender || ''}
                    onChange={(e) => onFilterChange('gender', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="">جميع الأجناس</option>
                    <option value="ذكر">ذكر</option>
                    <option value="أنثى">أنثى</option>
                  </select>
                </div>

                {/* الحجم */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الحجم</label>
                  <select
                    value={filters.size || ''}
                    onChange={(e) => onFilterChange('size', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="">جميع الأحجام</option>
                    <option value="صغير">صغير</option>
                    <option value="متوسط">متوسط</option>
                    <option value="كبير">كبير</option>
                    <option value="كبير جداً">كبير جداً</option>
                  </select>
                </div>

                {/* السلالة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">السلالة</label>
                  <input
                    type="text"
                    placeholder="اسم السلالة"
                    value={filters.breed || ''}
                    onChange={(e) => onFilterChange('breed', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>

                {/* التطعيم */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">التطعيم</label>
                  <select
                    value={filters.vaccinated || ''}
                    onChange={(e) => onFilterChange('vaccinated', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="">غير محدد</option>
                    <option value="مطعم">مطعم</option>
                    <option value="غير مطعم">غير مطعم</option>
                    <option value="مطعم جزئياً">مطعم جزئياً</option>
                  </select>
                </div>

                {/* التدريب */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">التدريب</label>
                  <select
                    value={filters.trained || ''}
                    onChange={(e) => onFilterChange('trained', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="">غير محدد</option>
                    <option value="مدرب">مدرب</option>
                    <option value="غير مدرب">غير مدرب</option>
                    <option value="مدرب جزئياً">مدرب جزئياً</option>
                  </select>
                </div>

                {/* نطاق السعر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="number"
                      placeholder="من"
                      value={filters.priceFrom || ''}
                      onChange={(e) => onFilterChange('priceFrom', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    />
                    <input
                      type="number"
                      placeholder="إلى"
                      value={filters.priceTo || ''}
                      onChange={(e) => onFilterChange('priceTo', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                </div>

                {/* الموقع */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                  <select
                    value={filters.location || ''}
                    onChange={(e) => onFilterChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option key="all-locations-pets" value="">جميع المواقع</option>
                    <option key="damascus-pets" value="دمشق">دمشق</option>
                    <option key="aleppo-pets" value="حلب">حلب</option>
                    <option key="homs-pets" value="حمص">حمص</option>
                    <option key="hama-pets" value="حماة">حماة</option>
                    <option key="latakia-pets" value="اللاذقية">اللاذقية</option>
                    <option key="tartous-pets" value="طرطوس">طرطوس</option>
                  </select>
                </div>
              </div>
            )}

            {/* Health Beauty Filters */}
            {category === 'health-beauty' && (
              <div className="space-y-4">
                {/* الفئة الرئيسية */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الرئيسية</label>
                  <select
                    value={filters.mainCategory || ''}
                    onChange={(e) => onFilterChange('mainCategory', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    <option value="">جميع الفئات</option>
                    <option value="personal-care">العناية الشخصية</option>
                    <option value="fitness-aesthetic">اللياقة والتجميل الطبي</option>
                    <option value="medical-health">الطب والصحة</option>
                    <option value="body-care">العناية بالجسم والاسترخاء</option>
                    <option value="products">منتجات الصحة والجمال</option>
                  </select>
                </div>

                {/* نوع الخدمة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع الخدمة</label>
                  <select
                    value={filters.serviceType || ''}
                    onChange={(e) => onFilterChange('serviceType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    <option value="">جميع الخدمات</option>
                    <option value="hair">الشعر</option>
                    <option value="makeup">المكياج</option>
                    <option value="skincare">العناية بالبشرة</option>
                    <option value="nails">الأظافر</option>
                    <option value="massage">التدليك</option>
                    <option value="fitness">اللياقة البدنية</option>
                    <option value="medical">الطب التجميلي</option>
                    <option value="dental">طب الأسنان</option>
                  </select>
                </div>

                {/* الجنس المستهدف */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الجنس المستهدف</label>
                  <select
                    value={filters.gender || ''}
                    onChange={(e) => onFilterChange('gender', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    <option value="">جميع الأجناس</option>
                    <option value="نساء">نساء</option>
                    <option value="رجال">رجال</option>
                    <option value="الجنسين">الجنسين</option>
                  </select>
                </div>

                {/* الماركة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الماركة</label>
                  <input
                    type="text"
                    placeholder="اسم الماركة"
                    value={filters.brand || ''}
                    onChange={(e) => onFilterChange('brand', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  />
                </div>

                {/* نوع البشرة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع البشرة</label>
                  <select
                    value={filters.skinType || ''}
                    onChange={(e) => onFilterChange('skinType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    <option value="">جميع أنواع البشرة</option>
                    <option value="دهنية">دهنية</option>
                    <option value="جافة">جافة</option>
                    <option value="مختلطة">مختلطة</option>
                    <option value="حساسة">حساسة</option>
                    <option value="عادية">عادية</option>
                  </select>
                </div>

                {/* الفئة العمرية */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة العمرية</label>
                  <select
                    value={filters.ageGroup || ''}
                    onChange={(e) => onFilterChange('ageGroup', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    <option value="">جميع الأعمار</option>
                    <option value="أطفال">أطفال (أقل من 12)</option>
                    <option value="مراهقين">مراهقين (12-18)</option>
                    <option value="شباب">شباب (18-30)</option>
                    <option value="بالغين">بالغين (30-50)</option>
                    <option value="كبار السن">كبار السن (50+)</option>
                  </select>
                </div>

                {/* نطاق السعر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                  <div className="grid grid-cols-2 gap-2">
                    <input
                      type="number"
                      placeholder="من"
                      value={filters.priceFrom || ''}
                      onChange={(e) => onFilterChange('priceFrom', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                    />
                    <input
                      type="number"
                      placeholder="إلى"
                      value={filters.priceTo || ''}
                      onChange={(e) => onFilterChange('priceTo', e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                    />
                  </div>
                </div>

                {/* الموقع */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                  <select
                    value={filters.location || ''}
                    onChange={(e) => onFilterChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    <option key="all-locations-health" value="">جميع المواقع</option>
                    <option key="damascus-health" value="دمشق">دمشق</option>
                    <option key="aleppo-health" value="حلب">حلب</option>
                    <option key="homs-health" value="حمص">حمص</option>
                    <option key="hama-health" value="حماة">حماة</option>
                    <option key="latakia-health" value="اللاذقية">اللاذقية</option>
                    <option key="tartous-health" value="طرطوس">طرطوس</option>
                  </select>
                </div>
              </div>
            )}

            {/* Food Filters */}
            {category === 'food' && (
              <div className="space-y-4">
                {/* نوع المطعم */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع المطعم</label>
                  <select
                    value={filters.restaurantType || ''}
                    onChange={(e) => onFilterChange('restaurantType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                  >
                    <option value="">جميع الأنواع</option>
                    <option value="مطعم">مطعم</option>
                    <option value="كافيه">كافيه</option>
                    <option value="مخبز">مخبز</option>
                    <option value="حلويات">حلويات</option>
                    <option value="وجبات سريعة">وجبات سريعة</option>
                    <option value="مطعم شعبي">مطعم شعبي</option>
                    <option value="مطعم راقي">مطعم راقي</option>
                    <option value="بوفيه">بوفيه</option>
                  </select>
                </div>

                {/* نوع المطبخ */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نوع المطبخ</label>
                  <select
                    value={filters.cuisineType || ''}
                    onChange={(e) => onFilterChange('cuisineType', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                  >
                    <option value="">جميع المطابخ</option>
                    <option value="شامي">شامي</option>
                    <option value="لبناني">لبناني</option>
                    <option value="تركي">تركي</option>
                    <option value="إيطالي">إيطالي</option>
                    <option value="صيني">صيني</option>
                    <option value="هندي">هندي</option>
                    <option value="مكسيكي">مكسيكي</option>
                    <option value="أمريكي">أمريكي</option>
                    <option value="فرنسي">فرنسي</option>
                    <option value="يوناني">يوناني</option>
                    <option value="عربي">عربي</option>
                  </select>
                </div>

                {/* نطاق السعر */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                  <select
                    value={filters.priceRange || ''}
                    onChange={(e) => onFilterChange('priceRange', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                  >
                    <option value="">جميع الأسعار</option>
                    <option value="اقتصادي">اقتصادي (أقل من 50,000)</option>
                    <option value="متوسط">متوسط (50,000 - 150,000)</option>
                    <option value="مرتفع">مرتفع (150,000 - 300,000)</option>
                    <option value="فاخر">فاخر (أكثر من 300,000)</option>
                  </select>
                </div>

                {/* التوصيل */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">التوصيل</label>
                  <select
                    value={filters.deliveryAvailable || ''}
                    onChange={(e) => onFilterChange('deliveryAvailable', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                  >
                    <option value="">غير محدد</option>
                    <option value="متوفر">متوفر</option>
                    <option value="غير متوفر">غير متوفر</option>
                  </select>
                </div>

                {/* التقييم */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">التقييم</label>
                  <select
                    value={filters.rating || ''}
                    onChange={(e) => onFilterChange('rating', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                  >
                    <option value="">جميع التقييمات</option>
                    <option value="5">⭐⭐⭐⭐⭐ (5 نجوم)</option>
                    <option value="4">⭐⭐⭐⭐ (4 نجوم فأكثر)</option>
                    <option value="3">⭐⭐⭐ (3 نجوم فأكثر)</option>
                    <option value="2">⭐⭐ (نجمتان فأكثر)</option>
                  </select>
                </div>

                {/* مفتوح الآن */}
                <div>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={filters.openNow || false}
                      onChange={(e) => onFilterChange('openNow', e.target.checked)}
                      className="rounded border-gray-300 text-yellow-500 focus:ring-yellow-500"
                    />
                    <span className="text-sm font-medium text-gray-700">مفتوح الآن</span>
                  </label>
                </div>

                {/* الموقع */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                  <select
                    value={filters.location || ''}
                    onChange={(e) => onFilterChange('location', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                  >
                    <option key="all-locations-food" value="">جميع المواقع</option>
                    <option key="damascus-food" value="دمشق">دمشق</option>
                    <option key="aleppo-food" value="حلب">حلب</option>
                    <option key="homs-food" value="حمص">حمص</option>
                    <option key="hama-food" value="حماة">حماة</option>
                    <option key="latakia-food" value="اللاذقية">اللاذقية</option>
                    <option key="tartous-food" value="طرطوس">طرطوس</option>
                  </select>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 flex gap-3">
            <button
              onClick={onClearFilters}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              مسح الفلاتر
            </button>
            <button
              onClick={onClose}
              className="flex-1 px-4 py-2 text-white rounded-lg transition-colors"
              style={{
                backgroundColor: getCategoryColor().replace('0.6', '1')
              }}
            >
              تطبيق الفلاتر
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
}

export default FilterModal;
