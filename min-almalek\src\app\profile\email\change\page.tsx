'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { useNotificationHelpers } from '@/hooks/useNotificationHelpers';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ClientOnlyWrapper from '@/components/ClientOnlyWrapper';

export default function EmailSettingsPage() {
  const { user } = useAuth();
  const { notifySuccess, notifyError } = useNotificationHelpers();
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState(1); // 1: تغيير البريد، 2: التحقق
  
  const [formData, setFormData] = useState({
    newEmail: '',
    password: '',
    verificationCode: ''
  });

  const handleEmailChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.newEmail || !formData.password) {
      notifyError('يرجى ملء جميع الحقول');
      return;
    }

    setIsLoading(true);
    try {
      // هنا سيتم إضافة منطق تغيير البريد الإلكتروني
      await new Promise(resolve => setTimeout(resolve, 2000)); // محاكاة API call
      setStep(2);
      notifySuccess('تم إرسال رمز التحقق إلى البريد الإلكتروني الجديد');
    } catch (error) {
      notifyError('حدث خطأ أثناء تغيير البريد الإلكتروني');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.verificationCode) {
      notifyError('يرجى إدخال رمز التحقق');
      return;
    }

    setIsLoading(true);
    try {
      // هنا سيتم إضافة منطق التحقق من الرمز
      await new Promise(resolve => setTimeout(resolve, 2000)); // محاكاة API call
      notifySuccess('تم تغيير البريد الإلكتروني بنجاح');
      // إعادة توجيه إلى صفحة الملف الشخصي
      window.location.href = '/profile';
    } catch (error) {
      notifyError('رمز التحقق غير صحيح');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const resendCode = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      notifySuccess('تم إعادة إرسال رمز التحقق');
    } catch (error) {
      notifyError('حدث خطأ أثناء إعادة الإرسال');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ClientOnlyWrapper>
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
        <Header />
        
        <main className="container mx-auto px-4 py-8">
          {/* شريط التنقل */}
          <div className="mb-6">
            <nav className="flex items-center gap-2 text-sm text-gray-600">
              <Link href="/profile" className="hover:text-green-600 transition-colors">
                الملف الشخصي
              </Link>
              <span>›</span>
              <Link href="/profile" className="hover:text-green-600 transition-colors">
                الإعدادات
              </Link>
              <span>›</span>
              <span className="text-gray-800 font-medium">تغيير البريد الإلكتروني</span>
            </nav>
          </div>

          <div className="max-w-2xl mx-auto">
            <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
              {/* العنوان */}
              <div className="flex items-center gap-3 mb-6">
                <span className="text-3xl opacity-80 hover:opacity-100 transition-all duration-300"
                      style={{filter: 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.3))'}}>📧</span>
                <div>
                  <h1 className="text-2xl font-bold text-gray-800">تغيير البريد الإلكتروني</h1>
                  <p className="text-gray-600">
                    {step === 1 ? 'قم بتحديث عنوان البريد الإلكتروني' : 'تحقق من البريد الإلكتروني الجديد'}
                  </p>
                </div>
              </div>

              {/* مؤشر التقدم */}
              <div className="flex items-center justify-center mb-8">
                <div className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    step >= 1 ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-600'
                  }`}>
                    1
                  </div>
                  <div className={`w-16 h-1 ${step >= 2 ? 'bg-green-600' : 'bg-gray-200'}`}></div>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    step >= 2 ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-600'
                  }`}>
                    2
                  </div>
                </div>
              </div>

              {/* الخطوة الأولى: تغيير البريد */}
              {step === 1 && (
                <form onSubmit={handleEmailChange} className="space-y-6">
                  {/* البريد الحالي */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      البريد الإلكتروني الحالي
                    </label>
                    <div className="text-gray-800 font-medium">{user?.email || 'غير محدد'}</div>
                  </div>

                  {/* البريد الجديد */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      البريد الإلكتروني الجديد
                    </label>
                    <input
                      type="email"
                      value={formData.newEmail}
                      onChange={(e) => handleInputChange('newEmail', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="أدخل البريد الإلكتروني الجديد"
                      required
                    />
                  </div>

                  {/* كلمة المرور للتأكيد */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      كلمة المرور الحالية
                    </label>
                    <input
                      type="password"
                      value={formData.password}
                      onChange={(e) => handleInputChange('password', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="أدخل كلمة المرور للتأكيد"
                      required
                    />
                  </div>

                  {/* أزرار الإجراءات */}
                  <div className="flex gap-4 pt-4">
                    <button
                      type="submit"
                      disabled={isLoading}
                      className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    >
                      {isLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                          جاري الإرسال...
                        </>
                      ) : (
                        <>
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z"/>
                          </svg>
                          إرسال رمز التحقق
                        </>
                      )}
                    </button>
                    
                    <Link
                      href="/profile"
                      className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center"
                    >
                      إلغاء
                    </Link>
                  </div>
                </form>
              )}

              {/* الخطوة الثانية: التحقق */}
              {step === 2 && (
                <form onSubmit={handleVerification} className="space-y-6">
                  <div className="text-center">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                      <p className="text-blue-800">
                        تم إرسال رمز التحقق إلى البريد الإلكتروني:
                      </p>
                      <p className="font-medium text-blue-900 mt-1">{formData.newEmail}</p>
                    </div>
                  </div>

                  {/* رمز التحقق */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 text-center">
                      رمز التحقق
                    </label>
                    <input
                      type="text"
                      value={formData.verificationCode}
                      onChange={(e) => handleInputChange('verificationCode', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center text-lg font-mono"
                      placeholder="أدخل رمز التحقق"
                      maxLength={6}
                      required
                    />
                  </div>

                  {/* إعادة الإرسال */}
                  <div className="text-center">
                    <button
                      type="button"
                      onClick={resendCode}
                      disabled={isLoading}
                      className="text-blue-600 hover:text-blue-700 text-sm underline disabled:opacity-50"
                    >
                      لم تستلم الرمز؟ إعادة الإرسال
                    </button>
                  </div>

                  {/* أزرار الإجراءات */}
                  <div className="flex gap-4 pt-4">
                    <button
                      type="submit"
                      disabled={isLoading}
                      className="flex-1 bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                      style={{filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))'}}
                    >
                      {isLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                          جاري التحقق...
                        </>
                      ) : (
                        <>
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"/>
                          </svg>
                          تأكيد التغيير
                        </>
                      )}
                    </button>
                    
                    <button
                      type="button"
                      onClick={() => setStep(1)}
                      className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      رجوع
                    </button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </main>

        <Footer />
      </div>
    </ClientOnlyWrapper>
  );
}
