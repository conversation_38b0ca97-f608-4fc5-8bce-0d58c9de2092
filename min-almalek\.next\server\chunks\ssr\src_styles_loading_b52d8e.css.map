{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/src/styles/loading.css"], "sourcesContent": ["/* انيميشن شاشة التحميل */\n\n@keyframes fade-in {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes fade-in-delay {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes logo-pulse {\n  0%, 100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.05);\n    opacity: 0.8;\n  }\n}\n\n@keyframes progress-shimmer {\n  0% {\n    background-position: -200px 0;\n  }\n  100% {\n    background-position: calc(200px + 100%) 0;\n  }\n}\n\n.animate-fade-in {\n  animation: fade-in 0.8s ease-out forwards;\n}\n\n.animate-fade-in-delay {\n  animation: fade-in-delay 1s ease-out 0.3s forwards;\n  opacity: 0;\n}\n\n.animate-logo-pulse {\n  animation: logo-pulse 2s ease-in-out infinite;\n}\n\n.progress-shimmer {\n  background: linear-gradient(\n    90deg,\n    transparent,\n    rgba(255, 255, 255, 0.4),\n    transparent\n  );\n  background-size: 200px 100%;\n  animation: progress-shimmer 1.5s infinite;\n}\n\n/* تأثيرات إضافية للشعار */\n.logo-glow {\n  filter: drop-shadow(0 0 20px rgba(34, 197, 94, 0.3));\n}\n\n.loading-dots {\n  display: inline-block;\n}\n\n.loading-dots::after {\n  content: '';\n  animation: loading-dots 1.5s infinite;\n}\n\n@keyframes loading-dots {\n  0%, 20% {\n    content: '';\n  }\n  40% {\n    content: '.';\n  }\n  60% {\n    content: '..';\n  }\n  80%, 100% {\n    content: '...';\n  }\n}\n\n/* تأثيرات الوميض والتوهج للشعار */\n@keyframes shimmer {\n  0% {\n    transform: translateX(-100%);\n  }\n  100% {\n    transform: translateX(100%);\n  }\n}\n\n@keyframes glow {\n  0%, 100% {\n    opacity: 0.3;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.6;\n    transform: scale(1.02);\n  }\n}\n\n@keyframes twinkle {\n  0%, 100% {\n    opacity: 0.3;\n    transform: scale(0.8) rotate(0deg);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1.2) rotate(180deg);\n  }\n}\n\n@keyframes logo-glow-pulse {\n  0%, 100% {\n    filter: drop-shadow(0 0 10px rgba(34, 197, 94, 0.3)) drop-shadow(0 0 20px rgba(255, 215, 0, 0.2));\n  }\n  50% {\n    filter: drop-shadow(0 0 20px rgba(34, 197, 94, 0.6)) drop-shadow(0 0 40px rgba(255, 215, 0, 0.4));\n  }\n}\n\n@keyframes pulse-ring {\n  0% {\n    transform: scale(0.8);\n    opacity: 1;\n  }\n  100% {\n    transform: scale(2);\n    opacity: 0;\n  }\n}\n\n/* تطبيق التأثيرات */\n.animate-shimmer {\n  animation: shimmer 2s infinite;\n}\n\n.animate-glow {\n  animation: glow 3s ease-in-out infinite;\n}\n\n.animate-twinkle {\n  animation: twinkle 2s ease-in-out infinite;\n}\n\n.animate-logo-glow-pulse {\n  animation: logo-glow-pulse 2s ease-in-out infinite;\n}\n\n.animate-pulse-ring {\n  animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;\n}\n\n/* تحسين تأثير التوهج */\n.logo-glow {\n  filter: drop-shadow(0 0 15px rgba(34, 197, 94, 0.4))\n          drop-shadow(0 0 30px rgba(255, 215, 0, 0.3))\n          drop-shadow(0 0 45px rgba(34, 197, 94, 0.2));\n  animation: logo-glow-pulse 3s ease-in-out infinite;\n}\n\n/* تأثير إضافي للخلفية */\n.loading-background {\n  background: radial-gradient(circle at center,\n    rgba(34, 197, 94, 0.1) 0%,\n    rgba(255, 215, 0, 0.05) 30%,\n    transparent 70%);\n  animation: glow 4s ease-in-out infinite;\n}\n"], "names": [], "mappings": "AAEA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;AASA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;AAYA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;;;;;;;;;;;;;AAgBA;;;;;;;;;;AASA;;;;;;;;;;;;AAWA;;;;;;;;;;;;AAWA;;;;;;;;;;AASA;;;;;;;;;;;;AAYA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;AAQA"}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}