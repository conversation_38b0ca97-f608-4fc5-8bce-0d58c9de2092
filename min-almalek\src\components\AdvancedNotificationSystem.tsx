'use client';

import { useState, useEffect, createContext, useContext, ReactNode } from 'react';

export interface AdvancedNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info' | 'welcome' | 'payment' | 'message' | 'favorite' | 'ad_posted' | 'search_alert' | 'logout';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  category: 'system' | 'user_action' | 'social' | 'commerce' | 'security';
  actionUrl?: string;
  actionText?: string;
  autoHide?: boolean;
  duration?: number;
  userId?: string;
  relatedData?: any;
  icon?: string;
}

interface AdvancedNotificationContextType {
  notifications: AdvancedNotification[];
  addNotification: (notification: Omit<AdvancedNotification, 'id' | 'timestamp' | 'isRead'>) => string;
  removeNotification: (id: string) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearAll: () => void;
  getUnreadCount: () => number;
  getNotificationsByCategory: (category: string) => AdvancedNotification[];
}

const AdvancedNotificationContext = createContext<AdvancedNotificationContextType | undefined>(undefined);

export const useAdvancedNotifications = () => {
  const context = useContext(AdvancedNotificationContext);
  if (!context) {
    throw new Error('useAdvancedNotifications must be used within an AdvancedNotificationProvider');
  }
  return context;
};

interface AdvancedNotificationProviderProps {
  children: ReactNode;
}

export function AdvancedNotificationProvider({ children }: AdvancedNotificationProviderProps) {
  const [notifications, setNotifications] = useState<AdvancedNotification[]>([]);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // تحميل الإشعارات المحفوظة
  useEffect(() => {
    if (!isClient) return;

    const savedNotifications = localStorage.getItem('advanced_notifications');
    if (savedNotifications) {
      try {
        const parsed = JSON.parse(savedNotifications);
        // التأكد من أن parsed هو مصفوفة
        if (Array.isArray(parsed)) {
          setNotifications(parsed.map((n: any) => ({
            ...n,
            timestamp: new Date(n.timestamp)
          })));
        } else {
          // إذا لم يكن مصفوفة، قم بمسح البيانات المعطوبة
          localStorage.removeItem('advanced_notifications');
          setNotifications([]);
        }
      } catch (error) {
        console.error('Error loading notifications:', error);
        // مسح البيانات المعطوبة
        localStorage.removeItem('advanced_notifications');
        setNotifications([]);
      }
    }
  }, [isClient]);

  // حفظ الإشعارات
  useEffect(() => {
    if (!isClient) return;
    localStorage.setItem('advanced_notifications', JSON.stringify(notifications));
  }, [notifications, isClient]);

  const addNotification = (notificationData: Omit<AdvancedNotification, 'id' | 'timestamp' | 'isRead'>): string => {
    const newNotification: AdvancedNotification = {
      ...notificationData,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      isRead: false
    };

    setNotifications(prev => [newNotification, ...prev.slice(0, 99)]); // الاحتفاظ بآخر 100 إشعار

    // إزالة تلقائية للإشعارات المؤقتة
    if (notificationData.autoHide !== false) {
      const duration = notificationData.duration || getDurationByType(notificationData.type);
      setTimeout(() => {
        removeNotification(newNotification.id);
      }, duration);
    }

    // إشعار صوتي للإشعارات المهمة
    if (notificationData.priority === 'high' || notificationData.priority === 'urgent') {
      playNotificationSound(notificationData.type);
    }

    return newNotification.id;
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(n => n.id === id ? { ...n, isRead: true } : n)
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(n => ({ ...n, isRead: true }))
    );
  };

  const clearAll = () => {
    setNotifications([]);
  };

  const getUnreadCount = () => {
    return notifications.filter(n => !n.isRead).length;
  };

  const getNotificationsByCategory = (category: string) => {
    return notifications.filter(n => n.category === category);
  };

  const getDurationByType = (type: string): number => {
    switch (type) {
      case 'success': return 4000;
      case 'error': return 8000;
      case 'warning': return 6000;
      case 'welcome': return 10000;
      case 'payment': return 6000;
      case 'logout': return 3000;
      default: return 5000;
    }
  };

  const playNotificationSound = (type: string) => {
    if (!isClient) return;
    
    try {
      const audio = new Audio();
      switch (type) {
        case 'success':
        case 'payment':
          audio.src = '/sounds/success.mp3';
          break;
        case 'error':
          audio.src = '/sounds/error.mp3';
          break;
        case 'warning':
          audio.src = '/sounds/warning.mp3';
          break;
        case 'message':
          audio.src = '/sounds/message.mp3';
          break;
        default:
          audio.src = '/sounds/notification.mp3';
      }
      audio.volume = 0.3;
      audio.play().catch(() => {
        // تجاهل الأخطاء الصوتية
      });
    } catch (error) {
      // تجاهل الأخطاء الصوتية
    }
  };

  const value: AdvancedNotificationContextType = {
    notifications,
    addNotification,
    removeNotification,
    markAsRead,
    markAllAsRead,
    clearAll,
    getUnreadCount,
    getNotificationsByCategory
  };

  return (
    <AdvancedNotificationContext.Provider value={value}>
      {children}
    </AdvancedNotificationContext.Provider>
  );
}

// دوال مساعدة لإنشاء إشعارات محددة
export const createWelcomeNotification = (userName: string) => ({
  type: 'welcome' as const,
  title: `مرحباً بك ${userName}! 🎉`,
  message: 'نحن سعداء لانضمامك إلى منصة من المالك. استكشف الميزات الجديدة وابدأ رحلتك معنا!',
  priority: 'high' as const,
  category: 'system' as const,
  actionUrl: '/profile/setup',
  actionText: 'إكمال الملف الشخصي',
  autoHide: false,
  icon: '🎉'
});

export const createPaymentSuccessNotification = (amount: string, service: string) => ({
  type: 'payment' as const,
  title: 'تم الدفع بنجاح! ✅',
  message: `تم دفع ${amount} ل.س لخدمة ${service} بنجاح. ستتلقى إيصالاً عبر البريد الإلكتروني.`,
  priority: 'high' as const,
  category: 'commerce' as const,
  actionUrl: '/payments/history',
  actionText: 'عرض تاريخ المدفوعات',
  icon: '💳'
});

export const createAdPostedNotification = (adTitle: string) => ({
  type: 'ad_posted' as const,
  title: 'تم نشر إعلانك! 🚀',
  message: `تم نشر إعلان "${adTitle}" بنجاح. سيظهر للمستخدمين خلال دقائق قليلة.`,
  priority: 'medium' as const,
  category: 'user_action' as const,
  actionUrl: '/my-ads',
  actionText: 'عرض إعلاناتي',
  icon: '📢'
});

export const createMessageNotification = (senderName: string, preview: string) => ({
  type: 'message' as const,
  title: `رسالة جديدة من ${senderName} 💬`,
  message: preview.length > 50 ? preview.substring(0, 50) + '...' : preview,
  priority: 'medium' as const,
  category: 'social' as const,
  actionUrl: '/messages',
  actionText: 'عرض الرسائل',
  icon: '💬'
});

export const createFavoriteNotification = (itemTitle: string, type: 'product' | 'seller') => ({
  type: 'favorite' as const,
  title: type === 'product' ? 'تمت الإضافة للمفضلة! ❤️' : 'تمت متابعة البائع! 👤',
  message: type === 'product' 
    ? `تم إضافة "${itemTitle}" إلى قائمة المفضلة`
    : `تم إضافة "${itemTitle}" إلى قائمة البائعين المتابعين`,
  priority: 'low' as const,
  category: 'user_action' as const,
  actionUrl: type === 'product' ? '/favorites' : '/following',
  icon: type === 'product' ? '❤️' : '👤'
});

export const createSearchAlertNotification = (searchTerm: string, newItemsCount: number) => ({
  type: 'search_alert' as const,
  title: 'عناصر جديدة تطابق بحثك! 🔍',
  message: `تم العثور على ${newItemsCount} عنصر جديد يطابق بحثك عن "${searchTerm}"`,
  priority: 'medium' as const,
  category: 'system' as const,
  actionUrl: `/search?q=${encodeURIComponent(searchTerm)}`,
  actionText: 'عرض النتائج',
  icon: '🔍'
});

export const createLogoutNotification = () => ({
  type: 'logout' as const,
  title: 'تم تسجيل الخروج بنجاح 👋',
  message: 'شكراً لاستخدام منصة من المالك. نراك قريباً!',
  priority: 'medium' as const,
  category: 'security' as const,
  icon: '👋'
});

export const createErrorNotification = (title: string, message: string) => ({
  type: 'error' as const,
  title,
  message,
  priority: 'high' as const,
  category: 'system' as const,
  icon: '❌'
});

export const createSuccessNotification = (title: string, message: string) => ({
  type: 'success' as const,
  title,
  message,
  priority: 'medium' as const,
  category: 'system' as const,
  icon: '✅'
});

export const createWarningNotification = (title: string, message: string) => ({
  type: 'warning' as const,
  title,
  message,
  priority: 'medium' as const,
  category: 'system' as const,
  icon: '⚠️'
});
