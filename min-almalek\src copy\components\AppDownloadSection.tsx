'use client';

import React from 'react';

const AppDownloadSection = () => {
  return (
    <section className="py-16 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-8">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              حمل تطبيق من المالك
            </h2>
            <p className="text-gray-300 text-lg mb-6">
              احصل على تجربة أفضل وأسرع مع تطبيقنا المجاني
            </p>
            <div className="flex justify-center items-center gap-4 mb-8">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-400">+50K</div>
                <div className="text-gray-400 text-sm">تحميل</div>
              </div>
              <div className="w-px h-8 bg-gray-600"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-400">4.8★</div>
                <div className="text-gray-400 text-sm">تقييم</div>
              </div>
              <div className="w-px h-8 bg-gray-600"></div>
              <div className="text-center">
                <div className="text-2xl font-bold text-primary-400">24/7</div>
                <div className="text-gray-400 text-sm">دعم</div>
              </div>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 justify-center items-center mb-8">
            {/* App Store */}
            <a
              href="#"
              className="group bg-black hover:bg-gray-800 text-white px-4 py-3 rounded-xl transition-all duration-300 flex items-center gap-3 min-w-[160px] transform hover:scale-105"
            >
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center group-hover:bg-gray-100 transition-colors">
                <svg viewBox="0 0 24 24" className="w-5 h-5 text-black" fill="currentColor">
                  <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                </svg>
              </div>
              <div className="text-right">
                <div className="text-xs opacity-80">تحميل من</div>
                <div className="font-semibold">App Store</div>
              </div>
            </a>

            {/* Google Play */}
            <a
              href="#"
              className="group bg-black hover:bg-gray-800 text-white px-4 py-3 rounded-xl transition-all duration-300 flex items-center gap-3 min-w-[160px] transform hover:scale-105"
            >
              <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center group-hover:bg-gray-100 transition-colors">
                <svg viewBox="0 0 24 24" className="w-5 h-5 text-black" fill="currentColor">
                  <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                </svg>
              </div>
              <div className="text-right">
                <div className="text-xs opacity-80">متوفر على</div>
                <div className="font-semibold">Google Play</div>
              </div>
            </a>
          </div>

          <div className="text-gray-400 text-sm">
            متوفر لأجهزة iOS و Android • مجاني تماماً
          </div>

          {/* Features */}
          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-white font-semibold mb-2">سرعة فائقة</h3>
              <p className="text-gray-400 text-sm">تصفح وابحث بسرعة البرق</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM9 7H4l5-5v5z" />
                </svg>
              </div>
              <h3 className="text-white font-semibold mb-2">سهولة الاستخدام</h3>
              <p className="text-gray-400 text-sm">واجهة بسيطة ومريحة</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-white font-semibold mb-2">أمان عالي</h3>
              <p className="text-gray-400 text-sm">حماية كاملة لبياناتك</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AppDownloadSection;
