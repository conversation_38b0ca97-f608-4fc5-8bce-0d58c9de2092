'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import { useToast } from '@/components/Toast';

interface PendingAd {
  id: number;
  title: string;
  description: string;
  price: number;
  currency: string;
  category: string;
  location: {
    governorate: string;
    city: string;
  };
  seller: {
    name: string;
    phone: string;
    email: string;
    verified: boolean;
  };
  images: string[];
  createdAt: string;
  status: 'pending';
}

export default function PendingAdsPage() {
  const [pendingAds, setPendingAds] = useState<PendingAd[]>([]);
  const { showSuccess, showError } = useToast();
  const [loading, setLoading] = useState(true);
  const [selectedAd, setSelectedAd] = useState<PendingAd | null>(null);

  useEffect(() => {
    loadPendingAds();
  }, []);

  const loadPendingAds = async () => {
    try {
      // محاكاة تحميل الإعلانات المنتظرة
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockPendingAds: PendingAd[] = [
        {
          id: 1,
          title: 'شقة للبيع في دمشق - المالكي',
          description: 'شقة مميزة للبيع في منطقة المالكي الراقية، الطابق الخامس مع مصعد، 3 غرف نوم، صالون، مطبخ، حمامين.',
          price: 85000000,
          currency: 'SYP',
          category: 'real-estate',
          location: {
            governorate: 'damascus',
            city: 'damascus'
          },
          seller: {
            name: 'أحمد محمد',
            phone: '+963944123456',
            email: '<EMAIL>',
            verified: true
          },
          images: ['/images/apt1-1.jpg'],
          createdAt: '2024-01-20T10:30:00Z',
          status: 'pending'
        },
        {
          id: 2,
          title: 'سيارة تويوتا كامري 2020',
          description: 'سيارة تويوتا كامري موديل 2020، حالة ممتازة، فحص كامل، لون أبيض.',
          price: 35000000,
          currency: 'SYP',
          category: 'cars',
          location: {
            governorate: 'aleppo',
            city: 'aleppo'
          },
          seller: {
            name: 'محمد علي',
            phone: '+963933456789',
            email: '<EMAIL>',
            verified: false
          },
          images: ['/images/car1.jpg'],
          createdAt: '2024-01-20T14:15:00Z',
          status: 'pending'
        }
      ];

      setPendingAds(mockPendingAds);
    } catch (error) {
      console.error('خطأ في تحميل الإعلانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (adId: number) => {
    try {
      // محاكاة قبول الإعلان
      setPendingAds(prev => prev.filter(ad => ad.id !== adId));
      showSuccess('تم قبول الإعلان بنجاح!', 'تم نشر الإعلان وإشعار صاحبه');
    } catch (error) {
      showError('حدث خطأ في قبول الإعلان', 'يرجى المحاولة مرة أخرى');
    }
  };

  const handleReject = async (adId: number) => {
    const reason = prompt('سبب الرفض (اختياري):');
    try {
      // محاكاة رفض الإعلان
      setPendingAds(prev => prev.filter(ad => ad.id !== adId));
      showSuccess('تم رفض الإعلان', 'تم إشعار صاحب الإعلان بالرفض');
    } catch (error) {
      showError('حدث خطأ في رفض الإعلان', 'يرجى المحاولة مرة أخرى');
    }
  };

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('ar-SY', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const arabicMonths = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    const day = date.getDate();
    const month = arabicMonths[date.getMonth()];
    const year = date.getFullYear();
    const time = date.toLocaleTimeString('ar-EG', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });

    return `${day} ${month} ${year} في ${time}`;
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'Cairo, sans-serif' }}>
              الإعلانات المنتظرة للمراجعة
            </h1>
            <p className="text-gray-600 mt-1">
              {pendingAds.length} إعلان في انتظار المراجعة
            </p>
          </div>
        </div>

        {/* قائمة الإعلانات */}
        {pendingAds.length === 0 ? (
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 text-center">
            <div className="text-6xl mb-4">✅</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد إعلانات منتظرة</h3>
            <p className="text-gray-600">جميع الإعلانات تمت مراجعتها</p>
          </div>
        ) : (
          <div className="space-y-4">
            {pendingAds.map((ad) => (
              <div key={ad.id} className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* معلومات الإعلان */}
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">{ad.title}</h3>
                        <p className="text-gray-600 text-sm mb-3 line-clamp-2">{ad.description}</p>
                        <div className="flex items-center gap-4 text-sm text-gray-500">
                          <span>📍 {ad.location.governorate}</span>
                          <span>💰 {formatPrice(ad.price, ad.currency)}</span>
                          <span>🕒 {formatDate(ad.createdAt)}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="bg-orange-100 text-orange-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                          في الانتظار
                        </span>
                      </div>
                    </div>

                    {/* معلومات البائع */}
                    <div className="bg-gray-50 rounded-lg p-4 mb-4">
                      <h4 className="font-medium text-gray-900 mb-2">معلومات البائع:</h4>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                        <div>
                          <span className="text-gray-600">الاسم: </span>
                          <span className="font-medium">{ad.seller.name}</span>
                          {ad.seller.verified && <span className="text-green-600 mr-1">✓</span>}
                        </div>
                        <div>
                          <span className="text-gray-600">الهاتف: </span>
                          <span className="font-medium">{ad.seller.phone}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">البريد: </span>
                          <span className="font-medium">{ad.seller.email}</span>
                        </div>
                      </div>
                    </div>

                    {/* الإجراءات */}
                    <div className="flex gap-3">
                      <button
                        onClick={() => handleApprove(ad.id)}
                        className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
                      >
                        <span>✅</span>
                        قبول الإعلان
                      </button>
                      <button
                        onClick={() => handleReject(ad.id)}
                        className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
                      >
                        <span>❌</span>
                        رفض الإعلان
                      </button>
                      <button
                        onClick={() => setSelectedAd(ad)}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                      >
                        <span>👁️</span>
                        عرض التفاصيل
                      </button>
                    </div>
                  </div>

                  {/* صورة الإعلان */}
                  {ad.images.length > 0 && (
                    <div className="w-full lg:w-48 h-32 bg-gray-200 rounded-lg overflow-hidden">
                      <img
                        src={ad.images[0]}
                        alt={ad.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = '/images/placeholder.jpg';
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* نافذة التفاصيل */}
        {selectedAd && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-xl font-bold text-gray-900">تفاصيل الإعلان</h3>
                  <button
                    onClick={() => setSelectedAd(null)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    ✕
                  </button>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">العنوان:</h4>
                    <p className="text-gray-700">{selectedAd.title}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">الوصف:</h4>
                    <p className="text-gray-700">{selectedAd.description}</p>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">السعر:</h4>
                      <p className="text-gray-700">{formatPrice(selectedAd.price, selectedAd.currency)}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">الموقع:</h4>
                      <p className="text-gray-700">{selectedAd.location.governorate} - {selectedAd.location.city}</p>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-3 mt-6">
                  <button
                    onClick={() => {
                      handleApprove(selectedAd.id);
                      setSelectedAd(null);
                    }}
                    className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    قبول
                  </button>
                  <button
                    onClick={() => {
                      handleReject(selectedAd.id);
                      setSelectedAd(null);
                    }}
                    className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                  >
                    رفض
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
