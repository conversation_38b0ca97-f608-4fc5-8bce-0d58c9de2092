'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import { DataService } from '@/lib/data';
import type { Ad } from '@/lib/data';

export default function FeaturedAdsPage() {
  const [ads, setAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'جميع التصنيفات' },
    { id: 'real-estate', name: 'العقارات' },
    { id: 'cars', name: 'السيارات' },
    { id: 'electronics', name: 'الإلكترونيات' },
    { id: 'jobs', name: 'الوظائف' },
    { id: 'services', name: 'الخدمات' },
    { id: 'fashion', name: 'الأزياء والموضة' }
  ];

  useEffect(() => {
    loadFeaturedAds();
  }, [sortBy, currentPage, selectedCategory]);

  const loadFeaturedAds = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const result = DataService.getAds({
      category: selectedCategory === 'all' ? undefined : selectedCategory,
      page: currentPage,
      limit: 12,
      sortBy: sortBy as any,
      featured: true // فقط الإعلانات المميزة
    });
    
    setAds(result.ads.filter(ad => ad.featured)); // التأكد من أنها مميزة
    setTotalPages(result.totalPages);
    setLoading(false);
  };

  const stats = {
    totalFeaturedAds: 1247,
    premiumAds: 892,
    goldAds: 355,
    avgViews: 2840
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* العنوان والإحصائيات */}
        <div className="mb-8">
          <div className="text-center mb-6">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">
              ⭐ الإعلانات المميزة
            </h1>
            <p className="text-lg text-gray-600">
              اكتشف أفضل الإعلانات المميزة والمدفوعة من المشتركين
            </p>
          </div>

          {/* إحصائيات سريعة */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div className="bg-white rounded-lg p-4 text-center shadow-sm border border-yellow-200">
              <div className="text-2xl font-bold text-yellow-600">{stats.totalFeaturedAds.toLocaleString()}</div>
              <div className="text-sm text-gray-600">إعلان مميز</div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center shadow-sm border border-blue-200">
              <div className="text-2xl font-bold text-blue-600">{stats.premiumAds.toLocaleString()}</div>
              <div className="text-sm text-gray-600">إعلان مميز</div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center shadow-sm border border-amber-200">
              <div className="text-2xl font-bold text-amber-600">{stats.goldAds.toLocaleString()}</div>
              <div className="text-sm text-gray-600">إعلان ذهبي</div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center shadow-sm border border-green-200">
              <div className="text-2xl font-bold text-green-600">{stats.avgViews.toLocaleString()}</div>
              <div className="text-sm text-gray-600">متوسط المشاهدات</div>
            </div>
          </div>
        </div>

        {/* فلاتر التصنيف */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">تصفية حسب التصنيف</h3>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* شريط التحكم */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="text-gray-600">
              عرض {ads.length} من أصل {stats.totalFeaturedAds} إعلان مميز
            </div>
            
            <div className="flex items-center gap-4">
              {/* طريقة العرض */}
              <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`px-3 py-2 text-sm ${
                    viewMode === 'grid' 
                      ? 'bg-primary-600 text-white' 
                      : 'bg-white text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  🔲 شبكة
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`px-3 py-2 text-sm ${
                    viewMode === 'list' 
                      ? 'bg-primary-600 text-white' 
                      : 'bg-white text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  📋 قائمة
                </button>
              </div>

              {/* الترتيب */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="newest">الأحدث</option>
                <option value="oldest">الأقدم</option>
                <option value="price_low">السعر: من الأقل للأعلى</option>
                <option value="price_high">السعر: من الأعلى للأقل</option>
                <option value="most_viewed">الأكثر مشاهدة</option>
              </select>
            </div>
          </div>
        </div>

        {/* الإعلانات */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                <div className="h-48 bg-gray-200"></div>
                <div className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : ads.length > 0 ? (
          <>
            <div className={
              viewMode === 'grid' 
                ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                : 'space-y-4'
            }>
              {ads.map((ad) => (
                <AdCard 
                  key={ad.id} 
                  ad={ad} 
                  viewMode={viewMode}
                />
              ))}
            </div>

            {/* الصفحات */}
            {totalPages > 1 && (
              <div className="flex justify-center mt-8">
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-2 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    السابق
                  </button>
                  
                  {[...Array(totalPages)].map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentPage(index + 1)}
                      className={`px-3 py-2 border rounded-lg text-sm ${
                        currentPage === index + 1
                          ? 'bg-primary-600 text-white border-primary-600'
                          : 'border-gray-200 hover:bg-gray-50'
                      }`}
                    >
                      {index + 1}
                    </button>
                  ))}
                  
                  <button
                    onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-2 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    التالي
                  </button>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">⭐</div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد إعلانات مميزة</h3>
            <p className="text-gray-600 mb-6">لم نجد أي إعلانات مميزة تطابق معايير البحث</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
            >
              إعادة تحميل
            </button>
          </div>
        )}

        {/* معلومات الاشتراك */}
        <div className="mt-16 bg-gradient-to-r from-yellow-500 to-amber-600 rounded-xl text-white p-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">⭐ هل تريد إعلان مميز؟</h2>
            <p className="text-yellow-100 mb-6">
              اشترك في إحدى باقاتنا المميزة لتحصل على أولوية في النتائج ومشاهدات أكثر
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/subscription"
                className="bg-white text-yellow-600 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-50 transition-colors"
              >
                عرض الباقات
              </a>
              <a
                href="/add-ad"
                className="bg-yellow-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-yellow-700 transition-colors border border-yellow-400"
              >
                أضف إعلانك الآن
              </a>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
