/* انيميشن شاشة التحميل */

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-delay {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes logo-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes progress-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

.animate-fade-in-delay {
  animation: fade-in-delay 1s ease-out 0.3s forwards;
  opacity: 0;
}

.animate-logo-pulse {
  animation: logo-pulse 2s ease-in-out infinite;
}

.progress-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200px 100%;
  animation: progress-shimmer 1.5s infinite;
}

/* تأثيرات إضافية للشعار */
.logo-glow {
  filter: drop-shadow(0 0 20px rgba(34, 197, 94, 0.3));
}

.loading-dots {
  display: inline-block;
}

.loading-dots::after {
  content: '';
  animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
  0%, 20% {
    content: '';
  }
  40% {
    content: '.';
  }
  60% {
    content: '..';
  }
  80%, 100% {
    content: '...';
  }
}

/* تأثيرات الوميض والتوهج للشعار */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes glow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.02);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(180deg);
  }
}

@keyframes logo-glow-pulse {
  0%, 100% {
    filter: drop-shadow(0 0 10px rgba(34, 197, 94, 0.3)) drop-shadow(0 0 20px rgba(255, 215, 0, 0.2));
  }
  50% {
    filter: drop-shadow(0 0 20px rgba(34, 197, 94, 0.6)) drop-shadow(0 0 40px rgba(255, 215, 0, 0.4));
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* تطبيق التأثيرات */
.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

.animate-twinkle {
  animation: twinkle 2s ease-in-out infinite;
}

.animate-logo-glow-pulse {
  animation: logo-glow-pulse 2s ease-in-out infinite;
}

.animate-pulse-ring {
  animation: pulse-ring 2s cubic-bezier(0.455, 0.03, 0.515, 0.955) infinite;
}

/* تحسين تأثير التوهج */
.logo-glow {
  filter: drop-shadow(0 0 15px rgba(34, 197, 94, 0.4))
          drop-shadow(0 0 30px rgba(255, 215, 0, 0.3))
          drop-shadow(0 0 45px rgba(34, 197, 94, 0.2));
  animation: logo-glow-pulse 3s ease-in-out infinite;
}

/* تأثير إضافي للخلفية */
.loading-background {
  background: radial-gradient(circle at center,
    rgba(34, 197, 94, 0.1) 0%,
    rgba(255, 215, 0, 0.05) 30%,
    transparent 70%);
  animation: glow 4s ease-in-out infinite;
}
