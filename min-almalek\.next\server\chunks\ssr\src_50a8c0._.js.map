{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/hooks/useNotificationHelpers.ts"], "sourcesContent": ["import { useNotifications } from '@/components/NotificationSystem';\n\nexport const useNotificationHelpers = () => {\n  const { addNotification } = useNotifications();\n\n  // إشعارات الإعلانات\n  const notifyAdPublished = (adTitle: string, adId: string) => {\n    addNotification({\n      type: 'success',\n      title: 'تم نشر إعلانك بنجاح!',\n      message: `تم نشر إعلان \"${adTitle}\" وهو الآن مرئي للمستخدمين.`,\n      category: 'ad',\n      icon: '🎉',\n      actionUrl: `/ad/${adId}`,\n      actionText: 'عرض الإعلان'\n    });\n  };\n\n  const notifyAdUnderReview = (adTitle: string, adId: string) => {\n    addNotification({\n      type: 'warning',\n      title: 'إعلانك قيد المراجعة',\n      message: `إعلان \"${adTitle}\" قيد المراجعة من قبل فريقنا. سيتم نشره خلال 24 ساعة.`,\n      category: 'ad',\n      icon: '⏳',\n      actionUrl: `/ad/${adId}`,\n      actionText: 'عرض الإعلان'\n    });\n  };\n\n  const notifyAdRejected = (adTitle: string, reason: string, adId: string) => {\n    addNotification({\n      type: 'error',\n      title: 'تم رفض إعلانك',\n      message: `إعلان \"${adTitle}\" تم رفضه. السبب: ${reason}`,\n      category: 'ad',\n      icon: '❌',\n      actionUrl: `/ad/edit/${adId}`,\n      actionText: 'تعديل الإعلان'\n    });\n  };\n\n  // إشعارات الدفع\n  const notifyPaymentSuccess = (amount: number, currency: string, planName: string) => {\n    addNotification({\n      type: 'success',\n      title: 'تم الدفع بنجاح',\n      message: `تم استلام دفعة بقيمة ${amount.toLocaleString()} ${currency} لباقة ${planName}.`,\n      category: 'payment',\n      icon: '💳',\n      actionUrl: '/subscription',\n      actionText: 'عرض الاشتراكات'\n    });\n  };\n\n  const notifyPaymentFailed = (reason: string) => {\n    addNotification({\n      type: 'error',\n      title: 'فشل في الدفع',\n      message: `لم نتمكن من إتمام عملية الدفع. ${reason}`,\n      category: 'payment',\n      icon: '❌',\n      actionUrl: '/subscription',\n      actionText: 'إعادة المحاولة'\n    });\n  };\n\n  const notifySubscriptionExpiring = (planName: string, daysLeft: number) => {\n    addNotification({\n      type: 'warning',\n      title: 'اشتراكك ينتهي قريباً',\n      message: `باقة ${planName} ستنتهي خلال ${daysLeft} أيام. جدد اشتراكك لتجنب انقطاع الخدمة.`,\n      category: 'payment',\n      icon: '⏰',\n      actionUrl: '/subscription',\n      actionText: 'تجديد الاشتراك'\n    });\n  };\n\n  // إشعارات النظام\n  const notifySystemMaintenance = (startTime: string, duration: string) => {\n    addNotification({\n      type: 'info',\n      title: 'صيانة مجدولة للنظام',\n      message: `سيتم إجراء صيانة للنظام في ${startTime} لمدة ${duration}. قد تواجه انقطاع مؤقت في الخدمة.`,\n      category: 'system',\n      icon: '🔧'\n    });\n  };\n\n  const notifySystemUpdate = (features: string[]) => {\n    addNotification({\n      type: 'info',\n      title: 'تحديث جديد للنظام',\n      message: `تم إضافة ميزات جديدة: ${features.join(', ')}. استكشف التحديثات الجديدة!`,\n      category: 'system',\n      icon: '🆕',\n      actionUrl: '/about',\n      actionText: 'تعرف على الجديد'\n    });\n  };\n\n  // إشعارات المستخدم\n  const notifyProfileUpdated = () => {\n    addNotification({\n      type: 'success',\n      title: 'تم تحديث ملفك الشخصي',\n      message: 'تم حفظ التغييرات على ملفك الشخصي بنجاح.',\n      category: 'user',\n      icon: '👤'\n    });\n  };\n\n  const notifyPasswordChanged = () => {\n    addNotification({\n      type: 'success',\n      title: 'تم تغيير كلمة المرور',\n      message: 'تم تغيير كلمة المرور بنجاح. إذا لم تقم بهذا التغيير، يرجى التواصل معنا فوراً.',\n      category: 'user',\n      icon: '🔒'\n    });\n  };\n\n  const notifyLoginFromNewDevice = (deviceInfo: string, location: string) => {\n    addNotification({\n      type: 'warning',\n      title: 'تسجيل دخول من جهاز جديد',\n      message: `تم تسجيل الدخول من ${deviceInfo} في ${location}. إذا لم تكن أنت، يرجى تغيير كلمة المرور فوراً.`,\n      category: 'user',\n      icon: '🔐'\n    });\n  };\n\n  // إشعارات عامة\n  const notifyWelcome = (userName: string) => {\n    addNotification({\n      type: 'success',\n      title: `🌟 أهلاً وسهلاً ${userName}!`,\n      message: 'مرحباً بك في منصة من المالك - منصتك المفضلة للإعلانات المبوبة. نتمنى لك تجربة مميزة واستكشاف جميع الميزات الاحترافية المتاحة.',\n      category: 'general',\n      icon: '✨',\n      actionUrl: '/dashboard',\n      actionText: 'استكشف لوحة التحكم'\n    });\n  };\n\n  const notifyNewFeature = (featureName: string, description: string) => {\n    addNotification({\n      type: 'info',\n      title: `🚀 ميزة جديدة: ${featureName}`,\n      message: `${description} - نحن نعمل باستمرار على تطوير منصة من المالك لتقديم أفضل تجربة لك.`,\n      category: 'general',\n      icon: '🎉',\n      actionUrl: '/features',\n      actionText: 'اكتشف المزيد'\n    });\n  };\n\n  // إشعارات الأخطاء\n  const notifyError = (title: string, message: string) => {\n    addNotification({\n      type: 'error',\n      title: `⚠️ ${title}`,\n      message: `${message} - إذا استمرت المشكلة، يرجى التواصل مع فريق الدعم.`,\n      category: 'system',\n      icon: '🚨'\n    });\n  };\n\n  const notifySuccess = (title: string, message: string) => {\n    addNotification({\n      type: 'success',\n      title: `🎉 ${title}`,\n      message: `${message} - شكراً لاستخدامك منصة من المالك!`,\n      category: 'general',\n      icon: '✨'\n    });\n  };\n\n  return {\n    // إشعارات الإعلانات\n    notifyAdPublished,\n    notifyAdUnderReview,\n    notifyAdRejected,\n    \n    // إشعارات الدفع\n    notifyPaymentSuccess,\n    notifyPaymentFailed,\n    notifySubscriptionExpiring,\n    \n    // إشعارات النظام\n    notifySystemMaintenance,\n    notifySystemUpdate,\n    \n    // إشعارات المستخدم\n    notifyProfileUpdated,\n    notifyPasswordChanged,\n    notifyLoginFromNewDevice,\n    \n    // إشعارات عامة\n    notifyWelcome,\n    notifyNewFeature,\n    \n    // إشعارات عامة للأخطاء والنجاح\n    notifyError,\n    notifySuccess\n  };\n};\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,yBAAyB;IACpC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,mBAAgB,AAAD;IAE3C,oBAAoB;IACpB,MAAM,oBAAoB,CAAC,SAAiB;QAC1C,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,cAAc,EAAE,QAAQ,2BAA2B,CAAC;YAC9D,UAAU;YACV,MAAM;YACN,WAAW,CAAC,IAAI,EAAE,MAAM;YACxB,YAAY;QACd;IACF;IAEA,MAAM,sBAAsB,CAAC,SAAiB;QAC5C,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,OAAO,EAAE,QAAQ,qDAAqD,CAAC;YACjF,UAAU;YACV,MAAM;YACN,WAAW,CAAC,IAAI,EAAE,MAAM;YACxB,YAAY;QACd;IACF;IAEA,MAAM,mBAAmB,CAAC,SAAiB,QAAgB;QACzD,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,OAAO,EAAE,QAAQ,kBAAkB,EAAE,QAAQ;YACvD,UAAU;YACV,MAAM;YACN,WAAW,CAAC,SAAS,EAAE,MAAM;YAC7B,YAAY;QACd;IACF;IAEA,gBAAgB;IAChB,MAAM,uBAAuB,CAAC,QAAgB,UAAkB;QAC9D,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,qBAAqB,EAAE,OAAO,cAAc,GAAG,CAAC,EAAE,SAAS,OAAO,EAAE,SAAS,CAAC,CAAC;YACzF,UAAU;YACV,MAAM;YACN,WAAW;YACX,YAAY;QACd;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,+BAA+B,EAAE,QAAQ;YACnD,UAAU;YACV,MAAM;YACN,WAAW;YACX,YAAY;QACd;IACF;IAEA,MAAM,6BAA6B,CAAC,UAAkB;QACpD,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,KAAK,EAAE,SAAS,aAAa,EAAE,SAAS,uCAAuC,CAAC;YAC1F,UAAU;YACV,MAAM;YACN,WAAW;YACX,YAAY;QACd;IACF;IAEA,iBAAiB;IACjB,MAAM,0BAA0B,CAAC,WAAmB;QAClD,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,2BAA2B,EAAE,UAAU,MAAM,EAAE,SAAS,iCAAiC,CAAC;YACpG,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,sBAAsB,EAAE,SAAS,IAAI,CAAC,MAAM,2BAA2B,CAAC;YAClF,UAAU;YACV,MAAM;YACN,WAAW;YACX,YAAY;QACd;IACF;IAEA,mBAAmB;IACnB,MAAM,uBAAuB;QAC3B,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,wBAAwB;QAC5B,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,2BAA2B,CAAC,YAAoB;QACpD,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,CAAC,mBAAmB,EAAE,WAAW,IAAI,EAAE,SAAS,+CAA+C,CAAC;YACzG,UAAU;YACV,MAAM;QACR;IACF;IAEA,eAAe;IACf,MAAM,gBAAgB,CAAC;QACrB,gBAAgB;YACd,MAAM;YACN,OAAO,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;YACrC,SAAS;YACT,UAAU;YACV,MAAM;YACN,WAAW;YACX,YAAY;QACd;IACF;IAEA,MAAM,mBAAmB,CAAC,aAAqB;QAC7C,gBAAgB;YACd,MAAM;YACN,OAAO,CAAC,eAAe,EAAE,aAAa;YACtC,SAAS,GAAG,YAAY,mEAAmE,CAAC;YAC5F,UAAU;YACV,MAAM;YACN,WAAW;YACX,YAAY;QACd;IACF;IAEA,kBAAkB;IAClB,MAAM,cAAc,CAAC,OAAe;QAClC,gBAAgB;YACd,MAAM;YACN,OAAO,CAAC,GAAG,EAAE,OAAO;YACpB,SAAS,GAAG,QAAQ,kDAAkD,CAAC;YACvE,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,CAAC,OAAe;QACpC,gBAAgB;YACd,MAAM;YACN,OAAO,CAAC,GAAG,EAAE,OAAO;YACpB,SAAS,GAAG,QAAQ,kCAAkC,CAAC;YACvD,UAAU;YACV,MAAM;QACR;IACF;IAEA,OAAO;QACL,oBAAoB;QACpB;QACA;QACA;QAEA,gBAAgB;QAChB;QACA;QACA;QAEA,iBAAiB;QACjB;QACA;QAEA,mBAAmB;QACnB;QACA;QACA;QAEA,eAAe;QACf;QACA;QAEA,+BAA+B;QAC/B;QACA;IACF;AACF"}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/AuthModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useNotificationHelpers } from '@/hooks/useNotificationHelpers';\nimport { useToast } from '@/components/ToastManager';\nimport Logo from './Logo';\n\ninterface AuthModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  defaultTab?: 'login' | 'register';\n}\n\nconst AuthModal = ({ isOpen, onClose, defaultTab = 'login' }: AuthModalProps) => {\n  const { login, register, isLoading, error } = useAuth();\n  const { notifyWelcome } = useNotificationHelpers();\n  const toast = useToast();\n\n  const [activeTab, setActiveTab] = useState<'login' | 'register'>(defaultTab);\n  const [userType, setUserType] = useState<'individual' | 'business' | 'real-estate-office'>('individual');\n  const [loginMethod, setLoginMethod] = useState<'email' | 'phone'>('email');\n  const [rememberMe, setRememberMe] = useState(false);\n  const [agreeToTerms, setAgreeToTerms] = useState(false);\n  const [companyType, setCompanyType] = useState('');\n  const [isGoogleLoading, setIsGoogleLoading] = useState(false);\n  const [isFacebookLoading, setIsFacebookLoading] = useState(false);\n\n  // بيانات النموذج\n  const [formData, setFormData] = useState({\n    email: '',\n    password: '',\n    confirmPassword: '',\n    phone: '',\n    firstName: '',\n    lastName: '',\n    companyName: '',\n  });\n\n  // أنواع الشركات المتاحة\n  const companyTypes = [\n    { value: 'real-estate', label: 'شركة عقارية' },\n    { value: 'commercial', label: 'شركة تجارية' },\n    { value: 'organization', label: 'منظمة' },\n    { value: 'institution', label: 'مؤسسة' },\n    { value: 'government', label: 'مؤسسة حكومية' },\n    { value: 'non-profit', label: 'مؤسسة غير ربحية' },\n    { value: 'marketing', label: 'شركة تسويق' },\n    { value: 'public-company', label: 'شركة مساهمة عامة' },\n    { value: 'limited-company', label: 'شركة محدودة المسؤولية' },\n    { value: 'partnership', label: 'شركة تضامن' },\n    { value: 'holding', label: 'شركة قابضة' },\n    { value: 'consulting', label: 'شركة استشارية' },\n    { value: 'manufacturing', label: 'شركة تصنيع' },\n    { value: 'services', label: 'شركة خدمات' },\n    { value: 'technology', label: 'شركة تقنية' },\n    { value: 'other', label: 'أخرى' }\n  ];\n\n  // تحديث التبويب عند تغيير defaultTab\n  useEffect(() => {\n    setActiveTab(defaultTab);\n  }, [defaultTab]);\n\n  // إعادة تعيين نوع الشركة عند تغيير نوع المستخدم\n  useEffect(() => {\n    if (userType === 'individual') {\n      setCompanyType('');\n    }\n  }, [userType]);\n\n  // معالج تسجيل الدخول\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // التحقق من بيانات Mahmut Madenli\n    if (formData.email === '<EMAIL>' && formData.password === 'Ma123456') {\n      // تسجيل دخول ناجح\n      const userData = {\n        id: 'user-mahmut-001',\n        name: 'Mahmut Madenli',\n        email: '<EMAIL>',\n        userType: 'individual',\n        individualInfo: {\n          firstName: 'Mahmut',\n          lastName: 'Madenli'\n        },\n        membershipId: 'MIN-2024-001247',\n        createdAt: '2024-01-15',\n        subscriptionPlan: 'premium',\n        verificationBadge: 'silver'\n      };\n\n      // حفظ بيانات المستخدم في localStorage\n      localStorage.setItem('currentUser', JSON.stringify(userData));\n      localStorage.setItem('isLoggedIn', 'true');\n\n      // إظهار رسالة ترحيب\n      toast.showWelcome(userData.name);\n\n      // إظهار رسالة نجاح تسجيل الدخول\n      setTimeout(() => {\n        toast.showSuccess('تم تسجيل الدخول بنجاح!', 'مرحباً بك مرة أخرى في منصة من المالك');\n      }, 1000);\n\n      onClose();\n\n      // إعادة تحميل الصفحة لتحديث الحالة\n      setTimeout(() => {\n        window.location.reload();\n      }, 2000);\n    } else {\n      alert('بيانات تسجيل الدخول غير صحيحة');\n    }\n  };\n\n  // معالج التسجيل\n  const handleRegister = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      await register({\n        email: formData.email,\n        password: formData.password,\n        confirmPassword: formData.confirmPassword,\n        phone: formData.phone,\n        name: `${formData.firstName} ${formData.lastName}`,\n        userType,\n        acceptTerms: agreeToTerms,\n        acceptPrivacy: agreeToTerms,\n        individualInfo: userType === 'individual' ? {\n          firstName: formData.firstName,\n          lastName: formData.lastName,\n        } : undefined,\n        businessInfo: userType === 'business' ? {\n          companyName: formData.companyName,\n          businessType: companyType,\n          contactPerson: {\n            name: `${formData.firstName} ${formData.lastName}`,\n            position: 'مدير',\n            phone: formData.phone,\n            email: formData.email,\n          },\n          address: {\n            governorate: '',\n            city: '',\n            area: '',\n            street: '',\n          },\n        } : undefined,\n        realEstateOfficeInfo: userType === 'real-estate-office' ? {\n          officeName: formData.companyName,\n          licenseNumber: '',\n          licenseIssueDate: new Date(),\n          licenseExpiryDate: new Date(),\n          ownerName: `${formData.firstName} ${formData.lastName}`,\n          specializations: [],\n          serviceAreas: [],\n          yearsOfExperience: 0,\n          teamSize: 1,\n          address: {\n            governorate: '',\n            city: '',\n            area: '',\n            street: '',\n            building: '',\n          },\n          workingHours: {\n            sunday: { open: '09:00', close: '17:00', isOpen: true },\n            monday: { open: '09:00', close: '17:00', isOpen: true },\n            tuesday: { open: '09:00', close: '17:00', isOpen: true },\n            wednesday: { open: '09:00', close: '17:00', isOpen: true },\n            thursday: { open: '09:00', close: '17:00', isOpen: true },\n            friday: { open: '09:00', close: '12:00', isOpen: true },\n            saturday: { open: '09:00', close: '17:00', isOpen: true },\n          },\n        } : undefined,\n      });\n\n      // إظهار رسالة ترحيب للمستخدم الجديد\n      toast.showWelcome(`${formData.firstName} ${formData.lastName}`);\n\n      // إظهار رسالة نجاح التسجيل\n      setTimeout(() => {\n        toast.showSuccess('تم إنشاء حسابك بنجاح!', 'مرحباً بك في منصة من المالك. يمكنك الآن الاستفادة من جميع الخدمات');\n      }, 1500);\n\n      onClose();\n    } catch (error) {\n      console.error('Register error:', error);\n    }\n  };\n\n  // معالج تسجيل الدخول بـ Google\n  const handleGoogleAuth = async () => {\n    setIsGoogleLoading(true);\n    try {\n      // TODO: Implement Google OAuth integration\n      console.log('Google Auth initiated');\n\n      // محاكاة عملية التحقق\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // هنا سيتم إضافة منطق Google OAuth الفعلي\n      alert('سيتم تفعيل تسجيل الدخول بـ Google قريباً');\n\n    } catch (error) {\n      console.error('Google Auth Error:', error);\n      alert('حدث خطأ في تسجيل الدخول بـ Google');\n    } finally {\n      setIsGoogleLoading(false);\n    }\n  };\n\n  // معالج تسجيل الدخول بـ Facebook\n  const handleFacebookAuth = async () => {\n    setIsFacebookLoading(true);\n    try {\n      // TODO: Implement Facebook OAuth integration\n      console.log('Facebook Auth initiated');\n\n      // محاكاة عملية التحقق\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // هنا سيتم إضافة منطق Facebook OAuth الفعلي\n      alert('سيتم تفعيل تسجيل الدخول بـ Facebook قريباً');\n\n    } catch (error) {\n      console.error('Facebook Auth Error:', error);\n      alert('حدث خطأ في تسجيل الدخول بـ Facebook');\n    } finally {\n      setIsFacebookLoading(false);\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto\">\n        {/* Header */}\n        <div className=\"p-6 border-b\">\n          <div className=\"flex items-center justify-between mb-4\">\n            <Logo variant=\"transparent\" size=\"md\" showText={false} />\n            <button\n              onClick={onClose}\n              className=\"text-gray-400 hover:text-gray-600 text-2xl\"\n            >\n              ×\n            </button>\n          </div>\n          <h2 className=\"text-2xl font-bold text-gray-800 text-center\">\n            {activeTab === 'login' ? 'تسجيل الدخول' : 'إنشاء حساب جديد'}\n          </h2>\n        </div>\n\n        {/* Tabs */}\n        <div className=\"flex border-b\">\n          <button\n            onClick={() => setActiveTab('login')}\n            className={`flex-1 py-3 px-4 text-center font-medium ${\n              activeTab === 'login'\n                ? 'text-primary-600 border-b-2 border-primary-600'\n                : 'text-gray-500 hover:text-gray-700'\n            }`}\n          >\n            تسجيل الدخول\n          </button>\n          <button\n            onClick={() => setActiveTab('register')}\n            className={`flex-1 py-3 px-4 text-center font-medium ${\n              activeTab === 'register'\n                ? 'text-primary-600 border-b-2 border-primary-600'\n                : 'text-gray-500 hover:text-gray-700'\n            }`}\n          >\n            إنشاء حساب\n          </button>\n        </div>\n\n        <div className=\"p-6\">\n          {error && (\n            <div className=\"mb-4 p-3 bg-red-50 border border-red-200 rounded-lg\">\n              <p className=\"text-sm text-red-800\">{error}</p>\n            </div>\n          )}\n\n          {activeTab === 'login' ? (\n            <form onSubmit={handleLogin} className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  البريد الإلكتروني\n                </label>\n                <input\n                  type=\"email\"\n                  value={formData.email}\n                  onChange={(e) => setFormData({...formData, email: e.target.value})}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                  placeholder=\"<EMAIL>\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  كلمة المرور\n                </label>\n                <input\n                  type=\"password\"\n                  value={formData.password}\n                  onChange={(e) => setFormData({...formData, password: e.target.value})}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                  placeholder=\"••••••••\"\n                  required\n                />\n              </div>\n\n              <div className=\"flex items-center justify-between\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    checked={rememberMe}\n                    onChange={(e) => setRememberMe(e.target.checked)}\n                    className=\"rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                  />\n                  <span className=\"mr-2 text-sm text-gray-600\">تذكرني</span>\n                </label>\n                <a href=\"#\" className=\"text-sm text-primary-600 hover:text-primary-700\">\n                  نسيت كلمة المرور؟\n                </a>\n              </div>\n\n              <button\n                type=\"submit\"\n                disabled={isLoading}\n                className=\"w-full bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}\n              </button>\n            </form>\n          ) : (\n            <div className=\"space-y-4\">\n              {/* User Type Selection */}\n              <div className=\"mb-6\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n                  نوع الحساب\n                </label>\n                <div className=\"grid grid-cols-2 gap-3\">\n                  <button\n                    onClick={() => setUserType('individual')}\n                    className={`p-4 border-2 rounded-lg text-center ${\n                      userType === 'individual'\n                        ? 'border-primary-600 bg-primary-50 text-primary-700'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                  >\n                    <div className=\"text-2xl mb-2\">👤</div>\n                    <div className=\"font-medium\">فرد</div>\n                    <div className=\"text-xs text-gray-500\">حساب شخصي</div>\n                  </button>\n                  <button\n                    onClick={() => setUserType('business')}\n                    className={`p-4 border-2 rounded-lg text-center ${\n                      userType === 'business'\n                        ? 'border-primary-600 bg-primary-50 text-primary-700'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                  >\n                    <div className=\"text-2xl mb-2\">🏢</div>\n                    <div className=\"font-medium\">شركة</div>\n                    <div className=\"text-xs text-gray-500\">حساب تجاري</div>\n                  </button>\n                </div>\n              </div>\n\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    الاسم الأول\n                  </label>\n                  <input\n                    type=\"text\"\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                    placeholder=\"أحمد\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    الاسم الأخير\n                  </label>\n                  <input\n                    type=\"text\"\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                    placeholder=\"محمد\"\n                  />\n                </div>\n              </div>\n\n              {userType === 'business' && (\n                <>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      اسم الشركة\n                    </label>\n                    <input\n                      type=\"text\"\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                      placeholder=\"اسم الشركة\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      نوع الشركة <span className=\"text-red-500\">*</span>\n                    </label>\n                    <select\n                      value={companyType}\n                      onChange={(e) => setCompanyType(e.target.value)}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 bg-white\"\n                      required\n                    >\n                      <option value=\"\">اختر نوع الشركة</option>\n                      {companyTypes.map((type) => (\n                        <option key={type.value} value={type.value}>\n                          {type.label}\n                        </option>\n                      ))}\n                    </select>\n                    {companyType === '' && (\n                      <p className=\"mt-1 text-sm text-gray-500\">\n                        يرجى اختيار نوع الشركة لإكمال التسجيل\n                      </p>\n                    )}\n                  </div>\n                </>\n              )}\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  البريد الإلكتروني\n                </label>\n                <input\n                  type=\"email\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                  placeholder=\"<EMAIL>\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  رقم الموبايل السوري\n                </label>\n                <div className=\"flex\">\n                  <span className=\"inline-flex items-center px-3 border border-l-0 border-gray-300 bg-gray-50 text-gray-500 rounded-r-lg\">\n                    +963\n                  </span>\n                  <input\n                    type=\"tel\"\n                    className=\"flex-1 px-4 py-3 border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                    placeholder=\"9X XXX XXXX\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  كلمة المرور\n                </label>\n                <input\n                  type=\"password\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                  placeholder=\"••••••••\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  تأكيد كلمة المرور\n                </label>\n                <input\n                  type=\"password\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500\"\n                  placeholder=\"••••••••\"\n                />\n              </div>\n\n              <div className=\"flex items-start\">\n                <input\n                  type=\"checkbox\"\n                  checked={agreeToTerms}\n                  onChange={(e) => setAgreeToTerms(e.target.checked)}\n                  className=\"mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500\"\n                />\n                <span className=\"mr-2 text-sm text-gray-600\">\n                  أوافق على{' '}\n                  <a href=\"/terms\" target=\"_blank\" className=\"text-primary-600 hover:text-primary-700 underline\">شروط الاستخدام</a>\n                  {' '}و{' '}\n                  <a href=\"/privacy\" target=\"_blank\" className=\"text-primary-600 hover:text-primary-700 underline\">سياسة الخصوصية</a>\n                </span>\n              </div>\n\n              {/* رسالة تنبيه للشركات */}\n              {userType === 'business' && companyType === '' && agreeToTerms && (\n                <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-3\">\n                  <div className=\"flex items-center gap-2\">\n                    <span className=\"text-yellow-600\">⚠️</span>\n                    <p className=\"text-sm text-yellow-800\">\n                      يرجى اختيار نوع الشركة لإكمال عملية التسجيل\n                    </p>\n                  </div>\n                </div>\n              )}\n\n              <button\n                disabled={!agreeToTerms || (userType === 'business' && companyType === '')}\n                className={`w-full py-3 rounded-lg transition-colors font-medium ${\n                  agreeToTerms && (userType === 'individual' || companyType !== '')\n                    ? 'bg-primary-600 text-white hover:bg-primary-700'\n                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'\n                }`}\n              >\n                إنشاء حساب\n              </button>\n            </div>\n          )}\n\n          {/* Social Login */}\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-white text-gray-500\">أو</span>\n              </div>\n            </div>\n\n            <div className=\"mt-6 grid grid-cols-1 gap-3\">\n              {/* Google Sign In */}\n              <button\n                onClick={handleGoogleAuth}\n                disabled={isGoogleLoading || isFacebookLoading}\n                className={`w-full inline-flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg bg-white text-sm font-medium transition-all ${\n                  isGoogleLoading || isFacebookLoading\n                    ? 'text-gray-400 cursor-not-allowed'\n                    : 'text-gray-700 hover:bg-gray-50 hover:border-gray-400'\n                }`}\n              >\n                {isGoogleLoading ? (\n                  <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-gray-400 mr-3\"></div>\n                ) : (\n                  <svg className=\"w-5 h-5 mr-3\" viewBox=\"0 0 24 24\">\n                    <path fill=\"#4285F4\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n                    <path fill=\"#34A853\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n                    <path fill=\"#FBBC05\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n                    <path fill=\"#EA4335\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n                  </svg>\n                )}\n                {isGoogleLoading\n                  ? 'جاري التحقق...'\n                  : activeTab === 'login'\n                    ? 'تسجيل الدخول بـ Google'\n                    : 'إنشاء حساب بـ Google'\n                }\n              </button>\n\n              {/* Facebook Sign In */}\n              <button\n                onClick={handleFacebookAuth}\n                disabled={isGoogleLoading || isFacebookLoading}\n                className={`w-full inline-flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg bg-white text-sm font-medium transition-all ${\n                  isGoogleLoading || isFacebookLoading\n                    ? 'text-gray-400 cursor-not-allowed'\n                    : 'text-gray-700 hover:bg-gray-50 hover:border-gray-400'\n                }`}\n              >\n                {isFacebookLoading ? (\n                  <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-gray-400 mr-3\"></div>\n                ) : (\n                  <svg className=\"w-5 h-5 mr-3\" viewBox=\"0 0 24 24\">\n                    <path fill=\"#1877F2\" d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                  </svg>\n                )}\n                {isFacebookLoading\n                  ? 'جاري التحقق...'\n                  : activeTab === 'login'\n                    ? 'تسجيل الدخول بـ Facebook'\n                    : 'إنشاء حساب بـ Facebook'\n                }\n              </button>\n            </div>\n\n            {/* رسالة تنبيه للتسجيل الاجتماعي */}\n            <div className=\"mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg\">\n              <div className=\"flex items-start gap-2\">\n                <span className=\"text-blue-600 mt-0.5\">ℹ️</span>\n                <div className=\"text-sm text-blue-800\">\n                  <p className=\"font-medium mb-1\">تسجيل الدخول الاجتماعي</p>\n                  <p>\n                    عند التسجيل بـ Google أو Facebook، ستتمكن من الوصول السريع لحسابك\n                    وستحتفظ بجميع إعلاناتك وتفضيلاتك.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AuthModal;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAcA,MAAM,YAAY,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,OAAO,EAAkB;IAC1E,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACpD,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,yBAAsB,AAAD;IAC/C,MAAM,QAAQ,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IAErB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACjE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoD;IAC3F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,iBAAiB;IACjB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,OAAO;QACP,WAAW;QACX,UAAU;QACV,aAAa;IACf;IAEA,wBAAwB;IACxB,MAAM,eAAe;QACnB;YAAE,OAAO;YAAe,OAAO;QAAc;QAC7C;YAAE,OAAO;YAAc,OAAO;QAAc;QAC5C;YAAE,OAAO;YAAgB,OAAO;QAAQ;QACxC;YAAE,OAAO;YAAe,OAAO;QAAQ;QACvC;YAAE,OAAO;YAAc,OAAO;QAAe;QAC7C;YAAE,OAAO;YAAc,OAAO;QAAkB;QAChD;YAAE,OAAO;YAAa,OAAO;QAAa;QAC1C;YAAE,OAAO;YAAkB,OAAO;QAAmB;QACrD;YAAE,OAAO;YAAmB,OAAO;QAAwB;QAC3D;YAAE,OAAO;YAAe,OAAO;QAAa;QAC5C;YAAE,OAAO;YAAW,OAAO;QAAa;QACxC;YAAE,OAAO;YAAc,OAAO;QAAgB;QAC9C;YAAE,OAAO;YAAiB,OAAO;QAAa;QAC9C;YAAE,OAAO;YAAY,OAAO;QAAa;QACzC;YAAE,OAAO;YAAc,OAAO;QAAa;QAC3C;YAAE,OAAO;YAAS,OAAO;QAAO;KACjC;IAED,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG;QAAC;KAAW;IAEf,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,cAAc;YAC7B,eAAe;QACjB;IACF,GAAG;QAAC;KAAS;IAEb,qBAAqB;IACrB,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAEhB,kCAAkC;QAClC,IAAI,SAAS,KAAK,KAAK,6BAA6B,SAAS,QAAQ,KAAK,YAAY;YACpF,kBAAkB;YAClB,MAAM,WAAW;gBACf,IAAI;gBACJ,MAAM;gBACN,OAAO;gBACP,UAAU;gBACV,gBAAgB;oBACd,WAAW;oBACX,UAAU;gBACZ;gBACA,cAAc;gBACd,WAAW;gBACX,kBAAkB;gBAClB,mBAAmB;YACrB;YAEA,sCAAsC;YACtC,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;YACnD,aAAa,OAAO,CAAC,cAAc;YAEnC,oBAAoB;YACpB,MAAM,WAAW,CAAC,SAAS,IAAI;YAE/B,gCAAgC;YAChC,WAAW;gBACT,MAAM,WAAW,CAAC,0BAA0B;YAC9C,GAAG;YAEH;YAEA,mCAAmC;YACnC,WAAW;gBACT,OAAO,QAAQ,CAAC,MAAM;YACxB,GAAG;QACL,OAAO;YACL,MAAM;QACR;IACF;IAEA,gBAAgB;IAChB,MAAM,iBAAiB,OAAO;QAC5B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,SAAS;gBACb,OAAO,SAAS,KAAK;gBACrB,UAAU,SAAS,QAAQ;gBAC3B,iBAAiB,SAAS,eAAe;gBACzC,OAAO,SAAS,KAAK;gBACrB,MAAM,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;gBAClD;gBACA,aAAa;gBACb,eAAe;gBACf,gBAAgB,aAAa,eAAe;oBAC1C,WAAW,SAAS,SAAS;oBAC7B,UAAU,SAAS,QAAQ;gBAC7B,IAAI;gBACJ,cAAc,aAAa,aAAa;oBACtC,aAAa,SAAS,WAAW;oBACjC,cAAc;oBACd,eAAe;wBACb,MAAM,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;wBAClD,UAAU;wBACV,OAAO,SAAS,KAAK;wBACrB,OAAO,SAAS,KAAK;oBACvB;oBACA,SAAS;wBACP,aAAa;wBACb,MAAM;wBACN,MAAM;wBACN,QAAQ;oBACV;gBACF,IAAI;gBACJ,sBAAsB,aAAa,uBAAuB;oBACxD,YAAY,SAAS,WAAW;oBAChC,eAAe;oBACf,kBAAkB,IAAI;oBACtB,mBAAmB,IAAI;oBACvB,WAAW,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;oBACvD,iBAAiB,EAAE;oBACnB,cAAc,EAAE;oBAChB,mBAAmB;oBACnB,UAAU;oBACV,SAAS;wBACP,aAAa;wBACb,MAAM;wBACN,MAAM;wBACN,QAAQ;wBACR,UAAU;oBACZ;oBACA,cAAc;wBACZ,QAAQ;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACtD,QAAQ;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACtD,SAAS;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACvD,WAAW;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACzD,UAAU;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACxD,QAAQ;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACtD,UAAU;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;oBAC1D;gBACF,IAAI;YACN;YAEA,oCAAoC;YACpC,MAAM,WAAW,CAAC,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE;YAE9D,2BAA2B;YAC3B,WAAW;gBACT,MAAM,WAAW,CAAC,yBAAyB;YAC7C,GAAG;YAEH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB;QACvB,mBAAmB;QACnB,IAAI;YACF,2CAA2C;YAC3C,QAAQ,GAAG,CAAC;YAEZ,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,0CAA0C;YAC1C,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,MAAM;QACR,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,iCAAiC;IACjC,MAAM,qBAAqB;QACzB,qBAAqB;QACrB,IAAI;YACF,6CAA6C;YAC7C,QAAQ,GAAG,CAAC;YAEZ,sBAAsB;YACtB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,4CAA4C;YAC5C,MAAM;QAER,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,UAAI;oCAAC,SAAQ;oCAAc,MAAK;oCAAK,UAAU;;;;;;8CAChD,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAIH,8OAAC;4BAAG,WAAU;sCACX,cAAc,UAAU,iBAAiB;;;;;;;;;;;;8BAK9C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,UACV,mDACA,qCACJ;sCACH;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,mDACA,qCACJ;sCACH;;;;;;;;;;;;8BAKH,8OAAC;oBAAI,WAAU;;wBACZ,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;wBAIxC,cAAc,wBACb,8OAAC;4BAAK,UAAU;4BAAa,WAAU;;8CACrC,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,YAAY;oDAAC,GAAG,QAAQ;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAA;4CAChE,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,OAAO,SAAS,QAAQ;4CACxB,UAAU,CAAC,IAAM,YAAY;oDAAC,GAAG,QAAQ;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAA;4CACnE,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;;8DACf,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,OAAO;oDAC/C,WAAU;;;;;;8DAEZ,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;;;;;;;sDAE/C,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDAAkD;;;;;;;;;;;;8CAK1E,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,YAAY,yBAAyB;;;;;;;;;;;iDAI1C,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,YAAY;oDAC3B,WAAW,CAAC,oCAAoC,EAC9C,aAAa,eACT,sDACA,yCACJ;;sEAEF,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,8OAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,8OAAC;oDACC,SAAS,IAAM,YAAY;oDAC3B,WAAW,CAAC,oCAAoC,EAC9C,aAAa,aACT,sDACA,yCACJ;;sEAEF,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,8OAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;8CAK7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAGhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;gCAKjB,aAAa,4BACZ;;sDACE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;;wDAA+C;sEACnD,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAE5C,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,WAAU;oDACV,QAAQ;;sEAER,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;gEAAwB,OAAO,KAAK,KAAK;0EACvC,KAAK,KAAK;+DADA,KAAK,KAAK;;;;;;;;;;;gDAK1B,gBAAgB,oBACf,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;;;8CAQlD,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAwG;;;;;;8DAGxH,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;4CACjD,WAAU;;;;;;sDAEZ,8OAAC;4CAAK,WAAU;;gDAA6B;gDACjC;8DACV,8OAAC;oDAAE,MAAK;oDAAS,QAAO;oDAAS,WAAU;8DAAoD;;;;;;gDAC9F;gDAAI;gDAAE;8DACP,8OAAC;oDAAE,MAAK;oDAAW,QAAO;oDAAS,WAAU;8DAAoD;;;;;;;;;;;;;;;;;;gCAKpG,aAAa,cAAc,gBAAgB,MAAM,8BAChD,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAkB;;;;;;0DAClC,8OAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;;;;;;8CAO7C,8OAAC;oCACC,UAAU,CAAC,gBAAiB,aAAa,cAAc,gBAAgB;oCACvE,WAAW,CAAC,qDAAqD,EAC/D,gBAAgB,CAAC,aAAa,gBAAgB,gBAAgB,EAAE,IAC5D,mDACA,gDACJ;8CACH;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA8B;;;;;;;;;;;;;;;;;8CAIlD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,SAAS;4CACT,UAAU,mBAAmB;4CAC7B,WAAW,CAAC,uIAAuI,EACjJ,mBAAmB,oBACf,qCACA,wDACJ;;gDAED,gCACC,8OAAC;oDAAI,WAAU;;;;;yEAEf,8OAAC;oDAAI,WAAU;oDAAe,SAAQ;;sEACpC,8OAAC;4DAAK,MAAK;4DAAU,GAAE;;;;;;sEACvB,8OAAC;4DAAK,MAAK;4DAAU,GAAE;;;;;;sEACvB,8OAAC;4DAAK,MAAK;4DAAU,GAAE;;;;;;sEACvB,8OAAC;4DAAK,MAAK;4DAAU,GAAE;;;;;;;;;;;;gDAG1B,kBACG,mBACA,cAAc,UACZ,2BACA;;;;;;;sDAKR,8OAAC;4CACC,SAAS;4CACT,UAAU,mBAAmB;4CAC7B,WAAW,CAAC,uIAAuI,EACjJ,mBAAmB,oBACf,qCACA,wDACJ;;gDAED,kCACC,8OAAC;oDAAI,WAAU;;;;;yEAEf,8OAAC;oDAAI,WAAU;oDAAe,SAAQ;8DACpC,cAAA,8OAAC;wDAAK,MAAK;wDAAU,GAAE;;;;;;;;;;;gDAG1B,oBACG,mBACA,cAAc,UACZ,6BACA;;;;;;;;;;;;;8CAMV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAmB;;;;;;kEAChC,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB;uCAEe"}}, {"offset": {"line": 1396, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1402, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/MyCvLogo.tsx"], "sourcesContent": ["import React from 'react';\nimport Image from 'next/image';\n\ninterface MyCvLogoProps {\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';\n  variant?: 'icon' | 'text' | 'full' | 'square';\n  className?: string;\n  showText?: boolean;\n}\n\nconst MyCvLogo: React.FC<MyCvLogoProps> = ({\n  size = 'md',\n  variant = 'full',\n  className = '',\n  showText = true\n}) => {\n  const sizeClasses = {\n    xs: 'w-4 h-4',\n    sm: 'w-6 h-6',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12',\n    xl: 'w-16 h-16'\n  };\n\n  const textSizes = {\n    xs: 'text-xs',\n    sm: 'text-sm',\n    md: 'text-base',\n    lg: 'text-lg',\n    xl: 'text-xl'\n  };\n\n  const LogoImage = ({ isSquare = false }: { isSquare?: boolean }) => (\n    <div className={`${sizeClasses[size]} relative ${isSquare ? 'rounded-lg overflow-hidden' : ''} ${className}`}>\n      <Image\n        src=\"/images/MyCV (1).jpg\"\n        alt=\"MyCv - منصة متكاملة للسير الذاتية والتوظيف\"\n        fill\n        className=\"object-contain\"\n        priority\n        sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n      />\n    </div>\n  );\n\n  const TextComponent = () => (\n    <span className={`${textSizes[size]} font-bold text-gray-800 ${className}`}>\n      MyCv\n    </span>\n  );\n\n  const FullComponent = () => (\n    <div className={`flex items-center gap-2 ${className}`}>\n      <LogoImage />\n      {showText && <TextComponent />}\n    </div>\n  );\n\n  switch (variant) {\n    case 'icon':\n      return <LogoImage />;\n    case 'square':\n      return <LogoImage isSquare={true} />;\n    case 'text':\n      return <TextComponent />;\n    case 'full':\n    default:\n      return <FullComponent />;\n  }\n};\n\nexport default MyCvLogo;\n"], "names": [], "mappings": ";;;;AACA;;;AASA,MAAM,WAAoC,CAAC,EACzC,OAAO,IAAI,EACX,UAAU,MAAM,EAChB,YAAY,EAAE,EACd,WAAW,IAAI,EAChB;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,YAAY;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,YAAY,CAAC,EAAE,WAAW,KAAK,EAA0B,iBAC7D,8OAAC;YAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,UAAU,EAAE,WAAW,+BAA+B,GAAG,CAAC,EAAE,WAAW;sBAC1G,cAAA,8OAAC,6HAAA,CAAA,UAAK;gBACJ,KAAI;gBACJ,KAAI;gBACJ,IAAI;gBACJ,WAAU;gBACV,QAAQ;gBACR,OAAM;;;;;;;;;;;IAKZ,MAAM,gBAAgB,kBACpB,8OAAC;YAAK,WAAW,GAAG,SAAS,CAAC,KAAK,CAAC,yBAAyB,EAAE,WAAW;sBAAE;;;;;;IAK9E,MAAM,gBAAgB,kBACpB,8OAAC;YAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;8BACpD,8OAAC;;;;;gBACA,0BAAY,8OAAC;;;;;;;;;;;IAIlB,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC;;;;;QACV,KAAK;YACH,qBAAO,8OAAC;gBAAU,UAAU;;;;;;QAC9B,KAAK;YACH,qBAAO,8OAAC;;;;;QACV,KAAK;QACL;YACE,qBAAO,8OAAC;;;;;IACZ;AACF;uCAEe"}}, {"offset": {"line": 1501, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1507, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/SafeNavigationButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { useRouter } from 'next/navigation';\n\ninterface SafeNavigationButtonProps {\n  href: string;\n  children: React.ReactNode;\n  className?: string;\n  onClick?: (e: React.MouseEvent) => void;\n  disabled?: boolean;\n  title?: string;\n  [key: string]: any;\n}\n\nconst SafeNavigationButton = ({\n  href,\n  children,\n  className,\n  onClick,\n  disabled = false,\n  title,\n  ...props\n}: SafeNavigationButtonProps) => {\n  const router = useRouter();\n  const [isClient, setIsClient] = useState(false);\n  const [isNavigating, setIsNavigating] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  const handleClick = useCallback((e: React.MouseEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (disabled || isNavigating) return;\n\n    if (onClick) {\n      onClick(e);\n    }\n\n    if (isClient && href) {\n      setIsNavigating(true);\n\n      try {\n        // التوجيه المباشر بدون setTimeout\n        router.push(href);\n      } catch (error) {\n        console.error('Navigation error:', error);\n      } finally {\n        // إعادة تعيين حالة التنقل بعد فترة قصيرة\n        setTimeout(() => {\n          setIsNavigating(false);\n        }, 500);\n      }\n    }\n  }, [disabled, isNavigating, onClick, isClient, href, router]);\n\n  // عرض زر عادي قبل الـ hydration\n  if (!isClient) {\n    return (\n      <button\n        className={`${className} relative z-20`}\n        disabled={disabled}\n        title={title}\n        {...props}\n      >\n        {children}\n      </button>\n    );\n  }\n\n  return (\n    <button\n      onClick={handleClick}\n      className={`${className} relative z-20 ${isNavigating ? 'opacity-75 cursor-wait' : ''}`}\n      disabled={disabled || isNavigating}\n      title={title}\n      {...props}\n    >\n      {isNavigating ? (\n        <div className=\"flex items-center gap-2\">\n          <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n          {children}\n        </div>\n      ) : (\n        children\n      )}\n    </button>\n  );\n};\n\nexport default SafeNavigationButton;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAeA,MAAM,uBAAuB,CAAC,EAC5B,IAAI,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,EACP,WAAW,KAAK,EAChB,KAAK,EACL,GAAG,OACuB;IAC1B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,IAAI,YAAY,cAAc;QAE9B,IAAI,SAAS;YACX,QAAQ;QACV;QAEA,IAAI,YAAY,MAAM;YACpB,gBAAgB;YAEhB,IAAI;gBACF,kCAAkC;gBAClC,OAAO,IAAI,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qBAAqB;YACrC,SAAU;gBACR,yCAAyC;gBACzC,WAAW;oBACT,gBAAgB;gBAClB,GAAG;YACL;QACF;IACF,GAAG;QAAC;QAAU;QAAc;QAAS;QAAU;QAAM;KAAO;IAE5D,gCAAgC;IAChC,IAAI,CAAC,UAAU;QACb,qBACE,8OAAC;YACC,WAAW,GAAG,UAAU,cAAc,CAAC;YACvC,UAAU;YACV,OAAO;YACN,GAAG,KAAK;sBAER;;;;;;IAGP;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAW,GAAG,UAAU,eAAe,EAAE,eAAe,2BAA2B,IAAI;QACvF,UAAU,YAAY;QACtB,OAAO;QACN,GAAG,KAAK;kBAER,6BACC,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;gBACd;;;;;;mBAGH;;;;;;AAIR;uCAEe"}}, {"offset": {"line": 1597, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1603, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/NotificationBell.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { useAdvancedNotifications } from './AdvancedNotificationSystem';\n\nexport default function NotificationBell() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [filter, setFilter] = useState<string>('all');\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const { \n    notifications, \n    getUnreadCount, \n    markAsRead, \n    markAllAsRead, \n    removeNotification,\n    getNotificationsByCategory \n  } = useAdvancedNotifications();\n\n  const unreadCount = getUnreadCount();\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const filteredNotifications = filter === 'all' \n    ? notifications \n    : getNotificationsByCategory(filter);\n\n  const getTimeAgo = (timestamp: Date) => {\n    const now = new Date();\n    const diff = now.getTime() - timestamp.getTime();\n    const minutes = Math.floor(diff / 60000);\n    const hours = Math.floor(diff / 3600000);\n    const days = Math.floor(diff / 86400000);\n\n    if (minutes < 1) return 'الآن';\n    if (minutes < 60) return `منذ ${minutes} دقيقة`;\n    if (hours < 24) return `منذ ${hours} ساعة`;\n    return `منذ ${days} يوم`;\n  };\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'success': return '✅';\n      case 'error': return '❌';\n      case 'warning': return '⚠️';\n      case 'welcome': return '🎉';\n      case 'payment': return '💳';\n      case 'message': return '💬';\n      case 'favorite': return '❤️';\n      case 'ad_posted': return '📢';\n      case 'search_alert': return '🔍';\n      case 'logout': return '👋';\n      default: return '🔔';\n    }\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'urgent': return 'border-l-red-500 bg-red-50/50';\n      case 'high': return 'border-l-orange-500 bg-orange-50/50';\n      case 'medium': return 'border-l-blue-500 bg-blue-50/50';\n      case 'low': return 'border-l-gray-500 bg-gray-50/50';\n      default: return 'border-l-gray-500 bg-gray-50/50';\n    }\n  };\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      <style jsx>{`\n        @keyframes bellGlow {\n          0% {\n            filter: grayscale(1) opacity(0.7) brightness(1) drop-shadow(0 0 4px rgba(251, 191, 36, 0.4));\n          }\n          100% {\n            filter: grayscale(0) opacity(1) brightness(1.5) drop-shadow(0 0 8px rgba(251, 191, 36, 0.8)) hue-rotate(45deg) saturate(1.5);\n          }\n        }\n      `}</style>\n      {/* زر الجرس */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-3 text-gray-600 hover:text-gray-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-2 rounded-full hover:bg-gray-100\"\n        aria-label=\"الإشعارات\"\n        style={{\n          filter: isOpen\n            ? 'drop-shadow(0 0 8px rgba(251, 191, 36, 0.8)) brightness(1.3)'\n            : 'none',\n          animation: unreadCount > 0 ? 'bellGlow 2s infinite alternate' : 'none'\n        }}\n      >\n        <span\n          className=\"text-xl transition-all duration-300\"\n          style={{\n            filter: isOpen\n              ? 'brightness(1.5) drop-shadow(0 0 6px rgba(251, 191, 36, 0.8)) hue-rotate(45deg) saturate(1.5)'\n              : 'grayscale(1) opacity(0.7)',\n            textShadow: isOpen\n              ? '0 0 6px rgba(251, 191, 36, 0.6)'\n              : 'none'\n          }}\n        >\n          🔔\n        </span>\n        \n        {/* عداد الإشعارات غير المقروءة */}\n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </button>\n\n      {/* قائمة الإشعارات */}\n      {isOpen && (\n        <div className=\"absolute left-0 mt-2 w-80 md:w-96 bg-white/95 backdrop-blur-md rounded-xl shadow-2xl border border-gray-200/50 z-50 max-h-[500px] overflow-hidden\">\n          {/* الرأس */}\n          <div className=\"p-4 border-b border-gray-200/50\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h3 className=\"text-lg font-semibold text-gray-800\">الإشعارات</h3>\n              <div className=\"flex items-center gap-2\">\n                {unreadCount > 0 && (\n                  <button\n                    onClick={markAllAsRead}\n                    className=\"text-sm text-blue-600 hover:text-blue-800 font-medium\"\n                  >\n                    تحديد الكل كمقروء\n                  </button>\n                )}\n                <button\n                  onClick={() => setIsOpen(false)}\n                  className=\"text-gray-400 hover:text-gray-600\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n\n            {/* فلاتر */}\n            <div className=\"flex gap-2 overflow-x-auto\">\n              {[\n                { key: 'all', label: 'الكل', count: notifications.length },\n                { key: 'system', label: 'النظام', count: getNotificationsByCategory('system').length },\n                { key: 'social', label: 'اجتماعي', count: getNotificationsByCategory('social').length },\n                { key: 'commerce', label: 'تجاري', count: getNotificationsByCategory('commerce').length },\n                { key: 'user_action', label: 'إجراءات', count: getNotificationsByCategory('user_action').length }\n              ].map(({ key, label, count }) => (\n                <button\n                  key={key}\n                  onClick={() => setFilter(key)}\n                  className={`\n                    px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap transition-colors\n                    ${filter === key \n                      ? 'bg-blue-100 text-blue-800 border border-blue-200' \n                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'\n                    }\n                  `}\n                >\n                  {label} {count > 0 && `(${count})`}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* قائمة الإشعارات */}\n          <div className=\"max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100\">\n            {filteredNotifications.length > 0 ? (\n              filteredNotifications.slice(0, 15).map((notification) => (\n                <div\n                  key={notification.id}\n                  className={`\n                    p-3 border-l-4 hover:bg-gray-50/70 transition-all duration-200 cursor-pointer group\n                    ${getPriorityColor(notification.priority)}\n                    ${!notification.isRead ? 'bg-blue-50/40 shadow-sm' : 'bg-white/20'}\n                    border-b border-gray-100/50 last:border-b-0\n                  `}\n                  onClick={() => {\n                    if (!notification.isRead) {\n                      markAsRead(notification.id);\n                    }\n                    if (notification.actionUrl) {\n                      setIsOpen(false);\n                      window.location.href = notification.actionUrl;\n                    }\n                  }}\n                >\n                  <div className=\"flex items-start gap-3\">\n                    {/* الأيقونة */}\n                    <div className=\"flex-shrink-0 mt-0.5\">\n                      <div className=\"w-8 h-8 rounded-full bg-gray-100/50 flex items-center justify-center\">\n                        <span className=\"text-lg\">\n                          {notification.icon || getNotificationIcon(notification.type)}\n                        </span>\n                      </div>\n                    </div>\n\n                    {/* المحتوى */}\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-start justify-between mb-1\">\n                        <h4 className={`\n                          text-sm leading-tight text-gray-800\n                          ${!notification.isRead ? 'font-semibold' : 'font-medium'}\n                        `}>\n                          {notification.title}\n                        </h4>\n                        <div className=\"flex items-center gap-1 ml-2 flex-shrink-0\">\n                          {!notification.isRead && (\n                            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                          )}\n                          <button\n                            onClick={(e) => {\n                              e.stopPropagation();\n                              removeNotification(notification.id);\n                            }}\n                            className=\"text-gray-400 hover:text-red-500 opacity-0 group-hover:opacity-100 transition-all duration-200 p-1 rounded hover:bg-red-50\"\n                            title=\"حذف الإشعار\"\n                          >\n                            <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                            </svg>\n                          </button>\n                        </div>\n                      </div>\n\n                      <p className=\"text-xs text-gray-600 mb-2 leading-relaxed line-clamp-2\">\n                        {notification.message}\n                      </p>\n\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"text-xs text-gray-500 font-medium\">\n                          {getTimeAgo(notification.timestamp)}\n                        </span>\n\n                        {notification.actionText && (\n                          <span className=\"text-xs text-green-600 font-medium bg-green-50 px-2 py-1 rounded-full\">\n                            {notification.actionText} ←\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"p-8 text-center\">\n                <div className=\"text-4xl mb-4\">🔔</div>\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">لا توجد إشعارات</h3>\n                <p className=\"text-gray-600\">\n                  {filter === 'all' \n                    ? 'ستظهر الإشعارات الجديدة هنا' \n                    : `لا توجد إشعارات في فئة ${filter === 'system' ? 'النظام' : filter === 'social' ? 'الاجتماعي' : filter === 'commerce' ? 'التجاري' : 'الإجراءات'}`\n                  }\n                </p>\n              </div>\n            )}\n          </div>\n\n          {/* الذيل */}\n          {filteredNotifications.length > 0 && (\n            <div className=\"p-3 border-t border-gray-200/50 bg-gray-50/50\">\n              <button\n                onClick={() => {\n                  window.location.href = '/notifications';\n                  setIsOpen(false);\n                }}\n                className=\"w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium\"\n              >\n                عرض جميع الإشعارات\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;;AAKe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,EACJ,aAAa,EACb,cAAc,EACd,UAAU,EACV,aAAa,EACb,kBAAkB,EAClB,0BAA0B,EAC3B,GAAG,CAAA,GAAA,gJAAA,CAAA,2BAAwB,AAAD;IAE3B,MAAM,cAAc;IAEpB,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,wBAAwB,WAAW,QACrC,gBACA,2BAA2B;IAE/B,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,OAAO,KAAK,UAAU,OAAO;QAC9C,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO;QAChC,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO;QAE/B,IAAI,UAAU,GAAG,OAAO;QACxB,IAAI,UAAU,IAAI,OAAO,CAAC,IAAI,EAAE,QAAQ,MAAM,CAAC;QAC/C,IAAI,QAAQ,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,KAAK,CAAC;QAC1C,OAAO,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC;IAC1B;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAgB,OAAO;YAC5B,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAyB,KAAK;kDAAhB;;;;;;0BAYb,8OAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAE1B,cAAW;gBACX,OAAO;oBACL,QAAQ,SACJ,iEACA;oBACJ,WAAW,cAAc,IAAI,mCAAmC;gBAClE;0DAPU;;kCASV,8OAAC;wBAEC,OAAO;4BACL,QAAQ,SACJ,iGACA;4BACJ,YAAY,SACR,oCACA;wBACN;kEARU;kCASX;;;;;;oBAKA,cAAc,mBACb,8OAAC;kEAAe;kCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;YAMjC,wBACC,8OAAC;0DAAc;;kCAEb,8OAAC;kEAAc;;0CACb,8OAAC;0EAAc;;kDACb,8OAAC;kFAAa;kDAAsC;;;;;;kDACpD,8OAAC;kFAAc;;4CACZ,cAAc,mBACb,8OAAC;gDACC,SAAS;0FACC;0DACX;;;;;;0DAIH,8OAAC;gDACC,SAAS,IAAM,UAAU;0FACf;0DAEV,cAAA,8OAAC;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8FAApD;8DACb,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO7E,8OAAC;0EAAc;0CACZ;oCACC;wCAAE,KAAK;wCAAO,OAAO;wCAAQ,OAAO,cAAc,MAAM;oCAAC;oCACzD;wCAAE,KAAK;wCAAU,OAAO;wCAAU,OAAO,2BAA2B,UAAU,MAAM;oCAAC;oCACrF;wCAAE,KAAK;wCAAU,OAAO;wCAAW,OAAO,2BAA2B,UAAU,MAAM;oCAAC;oCACtF;wCAAE,KAAK;wCAAY,OAAO;wCAAS,OAAO,2BAA2B,YAAY,MAAM;oCAAC;oCACxF;wCAAE,KAAK;wCAAe,OAAO;wCAAW,OAAO,2BAA2B,eAAe,MAAM;oCAAC;iCACjG,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,iBAC1B,8OAAC;wCAEC,SAAS,IAAM,UAAU;kFACd,CAAC;;oBAEV,EAAE,WAAW,MACT,qDACA,8CACH;kBACH,CAAC;;4CAEA;4CAAM;4CAAE,QAAQ,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;;uCAV7B;;;;;;;;;;;;;;;;kCAiBb,8OAAC;kEAAc;kCACZ,sBAAsB,MAAM,GAAG,IAC9B,sBAAsB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,6BACtC,8OAAC;gCAQC,SAAS;oCACP,IAAI,CAAC,aAAa,MAAM,EAAE;wCACxB,WAAW,aAAa,EAAE;oCAC5B;oCACA,IAAI,aAAa,SAAS,EAAE;wCAC1B,UAAU;wCACV,OAAO,QAAQ,CAAC,IAAI,GAAG,aAAa,SAAS;oCAC/C;gCACF;0EAdW,CAAC;;oBAEV,EAAE,iBAAiB,aAAa,QAAQ,EAAE;oBAC1C,EAAE,CAAC,aAAa,MAAM,GAAG,4BAA4B,cAAc;;kBAErE,CAAC;0CAWD,cAAA,8OAAC;8EAAc;;sDAEb,8OAAC;sFAAc;sDACb,cAAA,8OAAC;0FAAc;0DACb,cAAA,8OAAC;8FAAe;8DACb,aAAa,IAAI,IAAI,oBAAoB,aAAa,IAAI;;;;;;;;;;;;;;;;sDAMjE,8OAAC;sFAAc;;8DACb,8OAAC;8FAAc;;sEACb,8OAAC;sGAAc,CAAC;;0BAEd,EAAE,CAAC,aAAa,MAAM,GAAG,kBAAkB,cAAc;wBAC3D,CAAC;sEACE,aAAa,KAAK;;;;;;sEAErB,8OAAC;sGAAc;;gEACZ,CAAC,aAAa,MAAM,kBACnB,8OAAC;8GAAc;;;;;;8EAEjB,8OAAC;oEACC,SAAS,CAAC;wEACR,EAAE,eAAe;wEACjB,mBAAmB,aAAa,EAAE;oEACpC;oEAEA,OAAM;8GADI;8EAGV,cAAA,8OAAC;wEAAwB,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kHAApD;kFACb,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAM7E,8OAAC;8FAAY;8DACV,aAAa,OAAO;;;;;;8DAGvB,8OAAC;8FAAc;;sEACb,8OAAC;sGAAe;sEACb,WAAW,aAAa,SAAS;;;;;;wDAGnC,aAAa,UAAU,kBACtB,8OAAC;sGAAe;;gEACb,aAAa,UAAU;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;+BAlE9B,aAAa,EAAE;;;;sDA2ExB,8OAAC;sEAAc;;8CACb,8OAAC;8EAAc;8CAAgB;;;;;;8CAC/B,8OAAC;8EAAa;8CAA2C;;;;;;8CACzD,8OAAC;8EAAY;8CACV,WAAW,QACR,gCACA,CAAC,uBAAuB,EAAE,WAAW,WAAW,WAAW,WAAW,WAAW,cAAc,WAAW,aAAa,YAAY,aAAa;;;;;;;;;;;;;;;;;oBAQ3J,sBAAsB,MAAM,GAAG,mBAC9B,8OAAC;kEAAc;kCACb,cAAA,8OAAC;4BACC,SAAS;gCACP,OAAO,QAAQ,CAAC,IAAI,GAAG;gCACvB,UAAU;4BACZ;sEACU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AASf"}}, {"offset": {"line": 2088, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2094, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/ClientOnlyNotifications.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport NotificationBell from './NotificationBell';\n\nconst ClientOnlyNotifications = () => {\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  if (!isClient) {\n    // عرض placeholder أثناء التحميل\n    return (\n      <div className=\"relative p-3 text-gray-600 rounded-full\">\n        <svg\n          className=\"w-6 h-6\"\n          fill=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path d=\"M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 19V20H3V19L5 17V11C5 7.9 7 5.2 10 4.3V4C10 2.9 10.9 2 12 2S14 2.9 14 4V4.3C17 5.2 19 7.9 19 11V17L21 19ZM12 22C10.9 22 10 21.1 10 20H14C14 21.1 13.1 22 12 22Z\"/>\n        </svg>\n      </div>\n    );\n  }\n\n  return <NotificationBell />;\n};\n\nexport default ClientOnlyNotifications;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,0BAA0B;IAC9B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,IAAI,CAAC,UAAU;QACb,gCAAgC;QAChC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBACC,WAAU;gBACV,MAAK;gBACL,SAAQ;0BAER,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;IAIhB;IAEA,qBAAO,8OAAC,sIAAA,CAAA,UAAgB;;;;;AAC1B;uCAEe"}}, {"offset": {"line": 2142, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2148, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/lib/verification.ts"], "sourcesContent": ["// نظام علامات التوثيق والشارات للمستخدمين والإعلانات\n\nexport interface VerificationBadge {\n  id: string;\n  name: string;\n  nameEn: string;\n  description: string;\n  icon: string;\n  color: string;\n  bgColor: string;\n  borderColor: string;\n  textColor: string;\n  priority: number; // أولوية العرض (أعلى رقم = أولوية أكبر)\n  requirements: string[];\n  benefits: string[];\n}\n\n// علامات التوثيق للأفراد\nexport const INDIVIDUAL_BADGES: VerificationBadge[] = [\n  {\n    id: 'verified-basic',\n    name: 'موثق أساسي',\n    nameEn: 'Basic Verified',\n    description: 'مستخدم موثق مع الباقة الأساسية',\n    icon: '✓',\n    color: '#3B82F6', // أزرق\n    bgColor: 'bg-blue-100',\n    borderColor: 'border-blue-300',\n    textColor: 'text-blue-700',\n    priority: 1,\n    requirements: [\n      'اشتراك في الباقة الأساسية',\n      'تأكيد رقم الهاتف',\n      'تأكيد البريد الإلكتروني'\n    ],\n    benefits: [\n      'شارة التوثيق الأساسي',\n      'ثقة أكبر من المشترين',\n      'أولوية في البحث'\n    ]\n  },\n  {\n    id: 'verified-premium',\n    name: 'موثق مميز',\n    nameEn: 'Premium Verified',\n    description: 'مستخدم موثق مع الباقة المميزة',\n    icon: '✓',\n    color: '#9CA3AF', // فضي\n    bgColor: 'bg-gray-100',\n    borderColor: 'border-gray-400',\n    textColor: 'text-gray-700',\n    priority: 2,\n    requirements: [\n      'اشتراك في الباقة المميزة',\n      'تأكيد الهوية',\n      'تقييم إيجابي 4+ نجوم',\n      'عدم وجود شكاوى'\n    ],\n    benefits: [\n      'شارة التوثيق المميز',\n      'ظهور مميز في النتائج',\n      'ثقة عالية من المشترين',\n      'دعم فني أولوية'\n    ]\n  },\n  {\n    id: 'verified-gold',\n    name: 'موثق ذهبي',\n    nameEn: 'Gold Verified',\n    description: 'مستخدم موثق مع باقة الأعمال - الأكثر إعلاناً',\n    icon: '✓',\n    color: '#F59E0B', // ذهبي\n    bgColor: 'bg-yellow-100',\n    borderColor: 'border-yellow-400',\n    textColor: 'text-yellow-700',\n    priority: 3,\n    requirements: [\n      'اشتراك في باقة الأعمال',\n      'أكثر من 50 إعلان منشور',\n      'تقييم ممتاز 4.5+ نجوم',\n      'عضوية أكثر من 6 أشهر',\n      'تأكيد الهوية والعنوان'\n    ],\n    benefits: [\n      'شارة التوثيق الذهبي',\n      'أولوية قصوى في النتائج',\n      'ثقة استثنائية',\n      'دعم VIP',\n      'إحصائيات متقدمة'\n    ]\n  }\n];\n\n// علامات التوثيق للشركات\nexport const BUSINESS_BADGES: VerificationBadge[] = [\n  {\n    id: 'business-verified',\n    name: 'شركة موثقة',\n    nameEn: 'Verified Business',\n    description: 'شركة موثقة رسمياً - باقة البداية',\n    icon: '✓',\n    color: '#3B82F6', // أزرق\n    bgColor: 'bg-blue-100',\n    borderColor: 'border-blue-300',\n    textColor: 'text-blue-700',\n    priority: 4,\n    requirements: [\n      'اشتراك في باقة الشركات',\n      'تأكيد السجل التجاري',\n      'تأكيد عنوان الشركة',\n      'تأكيد رقم الضريبة'\n    ],\n    benefits: [\n      'شارة الشركة الموثقة',\n      'ثقة عالية من العملاء',\n      'ظهور في قسم الشركات',\n      'دعم فني للشركات'\n    ]\n  },\n  {\n    id: 'real-estate-office',\n    name: 'مكتب عقاري موثق',\n    nameEn: 'Verified Real Estate Office',\n    description: 'مكتب عقاري موثق ومرخص - باقة مخصصة',\n    icon: '✓',\n    color: '#9CA3AF', // فضي\n    bgColor: 'bg-gray-100',\n    borderColor: 'border-gray-400',\n    textColor: 'text-gray-700',\n    priority: 5,\n    requirements: [\n      'اشتراك في باقة المكاتب العقارية',\n      'ترخيص مكتب عقاري ساري',\n      'تأكيد عنوان المكتب',\n      'تأكيد الهوية المهنية',\n      'السجل التجاري ساري المفعول'\n    ],\n    benefits: [\n      'شارة المكتب العقاري الموثق',\n      'أولوية في البحث العقاري',\n      'ثقة عالية من العملاء',\n      'أدوات تقييم العقارات',\n      'تقارير السوق العقاري',\n      'نظام إدارة العملاء',\n      'دعم فني متخصص'\n    ]\n  },\n  {\n    id: 'business-premium',\n    name: 'شركة مميزة',\n    nameEn: 'Premium Business',\n    description: 'شركة مميزة مع الخطة المهنية',\n    icon: '✓',\n    color: '#F59E0B', // ذهبي\n    bgColor: 'bg-yellow-100',\n    borderColor: 'border-yellow-400',\n    textColor: 'text-yellow-700',\n    priority: 6,\n    requirements: [\n      'اشتراك في الخطة المهنية',\n      'أكثر من 100 إعلان',\n      'تقييم ممتاز من العملاء',\n      'عضوية أكثر من سنة'\n    ],\n    benefits: [\n      'شارة الشركة المميزة',\n      'أولوية عالية في النتائج',\n      'تقارير متقدمة',\n      'دعم مخصص'\n    ]\n  }\n];\n\n// علامات خاصة إضافية\nexport const SPECIAL_BADGES: VerificationBadge[] = [\n  {\n    id: 'top-seller',\n    name: 'أفضل بائع',\n    nameEn: 'Top Seller',\n    description: 'من أفضل البائعين على المنصة',\n    icon: '✓',\n    color: '#F59E0B', // ذهبي\n    bgColor: 'bg-yellow-50',\n    borderColor: 'border-yellow-200',\n    textColor: 'text-yellow-800',\n    priority: 7,\n    requirements: [\n      'أكثر من 100 عملية بيع ناجحة',\n      'تقييم 4.8+ نجوم',\n      'معدل استجابة 95%+',\n      'عدم وجود شكاوى جدية'\n    ],\n    benefits: [\n      'شارة أفضل بائع',\n      'ظهور في قائمة الأفضل',\n      'ثقة استثنائية',\n      'مكافآت خاصة'\n    ]\n  },\n  {\n    id: 'new-member',\n    name: 'عضو جديد',\n    nameEn: 'New Member',\n    description: 'عضو جديد على المنصة',\n    icon: '✓',\n    color: '#06B6D4', // سماوي\n    bgColor: 'bg-cyan-50',\n    borderColor: 'border-cyan-200',\n    textColor: 'text-cyan-700',\n    priority: 0,\n    requirements: [\n      'عضوية أقل من شهر',\n      'تأكيد البريد الإلكتروني'\n    ],\n    benefits: [\n      'ترحيب خاص',\n      'دعم للمبتدئين',\n      'نصائح مفيدة'\n    ]\n  }\n];\n\n// جميع الشارات\nexport const ALL_BADGES = [\n  ...INDIVIDUAL_BADGES,\n  ...BUSINESS_BADGES,\n  ...SPECIAL_BADGES\n];\n\n// دوال مساعدة\nexport const BadgeUtils = {\n  // الحصول على شارة بالمعرف\n  getBadgeById: (id: string): VerificationBadge | undefined => {\n    return ALL_BADGES.find(badge => badge.id === id);\n  },\n\n  // الحصول على أعلى شارة للمستخدم\n  getHighestBadge: (userBadges: string[]): VerificationBadge | undefined => {\n    const badges = userBadges\n      .map(id => BadgeUtils.getBadgeById(id))\n      .filter(Boolean) as VerificationBadge[];\n\n    return badges.sort((a, b) => b.priority - a.priority)[0];\n  },\n\n  // فلترة الشارات حسب النوع\n  getIndividualBadges: () => INDIVIDUAL_BADGES,\n  getBusinessBadges: () => BUSINESS_BADGES,\n  getSpecialBadges: () => SPECIAL_BADGES,\n\n  // تحديد نوع المستخدم من الشارة\n  getUserType: (badgeId: string): 'individual' | 'business' | 'special' => {\n    if (INDIVIDUAL_BADGES.find(b => b.id === badgeId)) return 'individual';\n    if (BUSINESS_BADGES.find(b => b.id === badgeId)) return 'business';\n    return 'special';\n  },\n\n  // تنسيق عرض الشارة\n  formatBadgeDisplay: (badge: VerificationBadge, size: 'sm' | 'md' | 'lg' = 'md') => {\n    const sizes = {\n      sm: 'text-xs px-2 py-1',\n      md: 'text-sm px-3 py-1',\n      lg: 'text-base px-4 py-2'\n    };\n\n    return {\n      className: `inline-flex items-center gap-1 rounded-full font-medium ${badge.bgColor} ${badge.borderColor} ${badge.textColor} border ${sizes[size]}`,\n      content: `${badge.icon} ${badge.name}`\n    };\n  }\n};\n\n// تحديد شارة المستخدم بناءً على اشتراكه\nexport const determineUserBadge = (\n  subscriptionType: string,\n  adsCount: number = 0,\n  rating: number = 0,\n  membershipMonths: number = 0,\n  isBusinessVerified: boolean = false\n): string[] => {\n  const badges: string[] = [];\n\n  // شارات الأفراد\n  if (subscriptionType === 'basic') {\n    badges.push('verified-basic');\n  } else if (subscriptionType === 'premium') {\n    badges.push('verified-premium');\n  } else if (subscriptionType === 'business' || subscriptionType === 'individual-business') {\n    badges.push('verified-gold');\n  }\n\n  // شارات الشركات\n  if (subscriptionType.startsWith('business-')) {\n    if (subscriptionType === 'business-starter') {\n      // خطة البداية - شارة زرقاء\n      if (isBusinessVerified) {\n        badges.push('business-verified');\n      }\n    } else if (subscriptionType === 'real-estate-office') {\n      // باقة المكاتب العقارية - شارة فضية\n      badges.push('real-estate-office');\n    } else if (subscriptionType === 'business-professional') {\n      // الخطة المهنية - شارة ذهبية\n      badges.push('business-premium');\n    }\n  }\n\n  // شارة المكاتب العقارية (يمكن أن تكون منفصلة)\n  if (subscriptionType === 'real-estate-office') {\n    badges.push('real-estate-office');\n  }\n\n  // شارات خاصة\n  if (membershipMonths < 1) {\n    badges.push('new-member');\n  }\n\n  if (adsCount >= 100 && rating >= 4.8) {\n    badges.push('top-seller');\n  }\n\n  return badges;\n};\n\nexport default {\n  INDIVIDUAL_BADGES,\n  BUSINESS_BADGES,\n  SPECIAL_BADGES,\n  ALL_BADGES,\n  BadgeUtils,\n  determineUserBadge\n};\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;;;;;AAkB9C,MAAM,oBAAyC;IACpD;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;IACH;CACD;AAGM,MAAM,kBAAuC;IAClD;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;CACD;AAGM,MAAM,iBAAsC;IACjD;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,aAAa;QACb,WAAW;QACX,UAAU;QACV,cAAc;YACZ;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;SACD;IACH;CACD;AAGM,MAAM,aAAa;OACrB;OACA;OACA;CACJ;AAGM,MAAM,aAAa;IACxB,0BAA0B;IAC1B,cAAc,CAAC;QACb,OAAO,WAAW,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC/C;IAEA,gCAAgC;IAChC,iBAAiB,CAAC;QAChB,MAAM,SAAS,WACZ,GAAG,CAAC,CAAA,KAAM,WAAW,YAAY,CAAC,KAClC,MAAM,CAAC;QAEV,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE;IAC1D;IAEA,0BAA0B;IAC1B,qBAAqB,IAAM;IAC3B,mBAAmB,IAAM;IACzB,kBAAkB,IAAM;IAExB,+BAA+B;IAC/B,aAAa,CAAC;QACZ,IAAI,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU,OAAO;QAC1D,IAAI,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU,OAAO;QACxD,OAAO;IACT;IAEA,mBAAmB;IACnB,oBAAoB,CAAC,OAA0B,OAA2B,IAAI;QAC5E,MAAM,QAAQ;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QAEA,OAAO;YACL,WAAW,CAAC,wDAAwD,EAAE,MAAM,OAAO,CAAC,CAAC,EAAE,MAAM,WAAW,CAAC,CAAC,EAAE,MAAM,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,KAAK,EAAE;YACnJ,SAAS,GAAG,MAAM,IAAI,CAAC,CAAC,EAAE,MAAM,IAAI,EAAE;QACxC;IACF;AACF;AAGO,MAAM,qBAAqB,CAChC,kBACA,WAAmB,CAAC,EACpB,SAAiB,CAAC,EAClB,mBAA2B,CAAC,EAC5B,qBAA8B,KAAK;IAEnC,MAAM,SAAmB,EAAE;IAE3B,gBAAgB;IAChB,IAAI,qBAAqB,SAAS;QAChC,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,qBAAqB,WAAW;QACzC,OAAO,IAAI,CAAC;IACd,OAAO,IAAI,qBAAqB,cAAc,qBAAqB,uBAAuB;QACxF,OAAO,IAAI,CAAC;IACd;IAEA,gBAAgB;IAChB,IAAI,iBAAiB,UAAU,CAAC,cAAc;QAC5C,IAAI,qBAAqB,oBAAoB;YAC3C,2BAA2B;YAC3B,IAAI,oBAAoB;gBACtB,OAAO,IAAI,CAAC;YACd;QACF,OAAO,IAAI,qBAAqB,sBAAsB;YACpD,oCAAoC;YACpC,OAAO,IAAI,CAAC;QACd,OAAO,IAAI,qBAAqB,yBAAyB;YACvD,6BAA6B;YAC7B,OAAO,IAAI,CAAC;QACd;IACF;IAEA,8CAA8C;IAC9C,IAAI,qBAAqB,sBAAsB;QAC7C,OAAO,IAAI,CAAC;IACd;IAEA,aAAa;IACb,IAAI,mBAAmB,GAAG;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,YAAY,OAAO,UAAU,KAAK;QACpC,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;AACT;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;AACF"}}, {"offset": {"line": 2441, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2447, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/VerificationBadge.tsx"], "sourcesContent": ["import React from 'react';\nimport { VerificationBadge as BadgeType, BadgeUtils } from '@/lib/verification';\n\ninterface VerificationBadgeProps {\n  badgeId?: string;\n  badge?: BadgeType;\n  size?: 'xs' | 'sm' | 'md' | 'lg';\n  showTooltip?: boolean;\n  className?: string;\n}\n\ninterface BadgeDisplayProps {\n  badges: string[];\n  size?: 'xs' | 'sm' | 'md' | 'lg';\n  maxDisplay?: number;\n  showTooltip?: boolean;\n  className?: string;\n}\n\n// مكون عرض شارة واحدة\nconst VerificationBadge: React.FC<VerificationBadgeProps> = ({\n  badgeId,\n  badge,\n  size = 'md',\n  showTooltip = true,\n  className = ''\n}) => {\n  const badgeData = badge || (badgeId ? BadgeUtils.getBadgeById(badgeId) : undefined);\n\n  if (!badgeData) return null;\n\n  const sizeClasses = {\n    xs: 'text-xs px-1.5 py-0.5 gap-1',\n    sm: 'text-xs px-2 py-1 gap-1',\n    md: 'text-sm px-3 py-1 gap-1.5',\n    lg: 'text-base px-4 py-2 gap-2'\n  };\n\n  const iconSizes = {\n    xs: 'w-3 h-3',\n    sm: 'w-4 h-4',\n    md: 'w-5 h-5',\n    lg: 'w-6 h-6'\n  };\n\n  return (\n    <div className=\"relative inline-block\">\n      <span\n        className={`\n          inline-flex items-center rounded-full font-medium border\n          ${badgeData.bgColor} ${badgeData.borderColor} ${badgeData.textColor}\n          ${sizeClasses[size]} ${className}\n          transition-all duration-200 hover:scale-105 cursor-default\n        `}\n        title={showTooltip ? badgeData.description : undefined}\n      >\n        <div\n          className={`\n            ${iconSizes[size]} rounded-full flex items-center justify-center\n            font-bold text-white shadow-sm\n          `}\n          style={{ backgroundColor: badgeData.color }}\n        >\n          ✓\n        </div>\n        <span className=\"font-semibold\">{badgeData.name}</span>\n      </span>\n\n      {showTooltip && (\n        <div className=\"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-10\">\n          {badgeData.description}\n          <div className=\"absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900\"></div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// مكون عرض عدة شارات\nexport const BadgeDisplay: React.FC<BadgeDisplayProps> = ({\n  badges,\n  size = 'md',\n  maxDisplay = 2,\n  showTooltip = true,\n  className = ''\n}) => {\n  if (!badges || badges.length === 0) return null;\n\n  // ترتيب الشارات حسب الأولوية\n  const sortedBadges = badges\n    .map(id => BadgeUtils.getBadgeById(id))\n    .filter(Boolean)\n    .sort((a, b) => (b?.priority || 0) - (a?.priority || 0)) as BadgeType[];\n\n  const displayBadges = sortedBadges.slice(0, maxDisplay);\n  const remainingCount = sortedBadges.length - maxDisplay;\n\n  return (\n    <div className={`flex items-center gap-2 ${className}`}>\n      {displayBadges.map((badge) => (\n        <VerificationBadge\n          key={badge.id}\n          badge={badge}\n          size={size}\n          showTooltip={showTooltip}\n        />\n      ))}\n\n      {remainingCount > 0 && (\n        <span className=\"text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full\">\n          +{remainingCount}\n        </span>\n      )}\n    </div>\n  );\n};\n\n// مكون شارة مبسطة للإعلانات\nexport const AdBadge: React.FC<{\n  userBadges: string[];\n  size?: 'xs' | 'sm' | 'md';\n  className?: string;\n}> = ({ userBadges, size = 'sm', className = '' }) => {\n  const highestBadge = BadgeUtils.getHighestBadge(userBadges);\n\n  if (!highestBadge) return null;\n\n  return (\n    <VerificationBadge\n      badge={highestBadge}\n      size={size}\n      showTooltip={true}\n      className={className}\n    />\n  );\n};\n\n// مكون شارة للملف الشخصي\nexport const ProfileBadge: React.FC<{\n  userBadges: string[];\n  showAll?: boolean;\n  className?: string;\n}> = ({ userBadges, showAll = false, className = '' }) => {\n  if (!userBadges || userBadges.length === 0) return null;\n\n  if (showAll) {\n    return (\n      <BadgeDisplay\n        badges={userBadges}\n        size=\"md\"\n        maxDisplay={userBadges.length}\n        className={className}\n      />\n    );\n  }\n\n  const highestBadge = BadgeUtils.getHighestBadge(userBadges);\n\n  if (!highestBadge) return null;\n\n  return (\n    <div className={`flex items-center gap-3 ${className}`}>\n      <VerificationBadge\n        badge={highestBadge}\n        size=\"lg\"\n        showTooltip={true}\n      />\n      <div className=\"text-sm text-gray-600\">\n        <div className=\"font-medium\">{highestBadge.description}</div>\n        <div className=\"text-xs text-gray-500\">\n          {highestBadge.benefits.slice(0, 2).join(' • ')}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// مكون معلومات الشارة المفصلة\nexport const BadgeInfo: React.FC<{\n  badgeId: string;\n  className?: string;\n}> = ({ badgeId, className = '' }) => {\n  const badge = BadgeUtils.getBadgeById(badgeId);\n\n  if (!badge) return null;\n\n  return (\n    <div className={`bg-white rounded-lg border p-4 ${className}`}>\n      <div className=\"flex items-center gap-3 mb-3\">\n        <div className={`w-12 h-12 rounded-full ${badge.bgColor} ${badge.borderColor} border-2 flex items-center justify-center relative`}>\n          <div\n            className=\"w-8 h-8 rounded-full flex items-center justify-center font-bold text-white shadow-sm text-lg\"\n            style={{ backgroundColor: badge.color }}\n          >\n            ✓\n          </div>\n        </div>\n        <div>\n          <h3 className=\"font-semibold text-gray-800\">{badge.name}</h3>\n          <p className=\"text-sm text-gray-600\">{badge.description}</p>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div>\n          <h4 className=\"font-medium text-gray-700 mb-2\">متطلبات الحصول:</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            {badge.requirements.map((req, index) => (\n              <li key={index} className=\"flex items-start gap-2\">\n                <span className=\"text-green-500 mt-0.5\">✓</span>\n                <span>{req}</span>\n              </li>\n            ))}\n          </ul>\n        </div>\n\n        <div>\n          <h4 className=\"font-medium text-gray-700 mb-2\">المميزات:</h4>\n          <ul className=\"text-sm text-gray-600 space-y-1\">\n            {badge.benefits.map((benefit, index) => (\n              <li key={index} className=\"flex items-start gap-2\">\n                <span className=\"text-blue-500 mt-0.5\">★</span>\n                <span>{benefit}</span>\n              </li>\n            ))}\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default VerificationBadge;\n"], "names": [], "mappings": ";;;;;;;;AACA;;;AAkBA,sBAAsB;AACtB,MAAM,oBAAsD,CAAC,EAC3D,OAAO,EACP,KAAK,EACL,OAAO,IAAI,EACX,cAAc,IAAI,EAClB,YAAY,EAAE,EACf;IACC,MAAM,YAAY,SAAS,CAAC,UAAU,0HAAA,CAAA,aAAU,CAAC,YAAY,CAAC,WAAW,SAAS;IAElF,IAAI,CAAC,WAAW,OAAO;IAEvB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,YAAY;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,WAAW,CAAC;;UAEV,EAAE,UAAU,OAAO,CAAC,CAAC,EAAE,UAAU,WAAW,CAAC,CAAC,EAAE,UAAU,SAAS,CAAC;UACpE,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU;;QAEnC,CAAC;gBACD,OAAO,cAAc,UAAU,WAAW,GAAG;;kCAE7C,8OAAC;wBACC,WAAW,CAAC;YACV,EAAE,SAAS,CAAC,KAAK,CAAC;;UAEpB,CAAC;wBACD,OAAO;4BAAE,iBAAiB,UAAU,KAAK;wBAAC;kCAC3C;;;;;;kCAGD,8OAAC;wBAAK,WAAU;kCAAiB,UAAU,IAAI;;;;;;;;;;;;YAGhD,6BACC,8OAAC;gBAAI,WAAU;;oBACZ,UAAU,WAAW;kCACtB,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAKzB;AAGO,MAAM,eAA4C,CAAC,EACxD,MAAM,EACN,OAAO,IAAI,EACX,aAAa,CAAC,EACd,cAAc,IAAI,EAClB,YAAY,EAAE,EACf;IACC,IAAI,CAAC,UAAU,OAAO,MAAM,KAAK,GAAG,OAAO;IAE3C,6BAA6B;IAC7B,MAAM,eAAe,OAClB,GAAG,CAAC,CAAA,KAAM,0HAAA,CAAA,aAAU,CAAC,YAAY,CAAC,KAClC,MAAM,CAAC,SACP,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC;IAExD,MAAM,gBAAgB,aAAa,KAAK,CAAC,GAAG;IAC5C,MAAM,iBAAiB,aAAa,MAAM,GAAG;IAE7C,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;YACnD,cAAc,GAAG,CAAC,CAAC,sBAClB,8OAAC;oBAEC,OAAO;oBACP,MAAM;oBACN,aAAa;mBAHR,MAAM,EAAE;;;;;YAOhB,iBAAiB,mBAChB,8OAAC;gBAAK,WAAU;;oBAA2D;oBACvE;;;;;;;;;;;;;AAKZ;AAGO,MAAM,UAIR,CAAC,EAAE,UAAU,EAAE,OAAO,IAAI,EAAE,YAAY,EAAE,EAAE;IAC/C,MAAM,eAAe,0HAAA,CAAA,aAAU,CAAC,eAAe,CAAC;IAEhD,IAAI,CAAC,cAAc,OAAO;IAE1B,qBACE,8OAAC;QACC,OAAO;QACP,MAAM;QACN,aAAa;QACb,WAAW;;;;;;AAGjB;AAGO,MAAM,eAIR,CAAC,EAAE,UAAU,EAAE,UAAU,KAAK,EAAE,YAAY,EAAE,EAAE;IACnD,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG,OAAO;IAEnD,IAAI,SAAS;QACX,qBACE,8OAAC;YACC,QAAQ;YACR,MAAK;YACL,YAAY,WAAW,MAAM;YAC7B,WAAW;;;;;;IAGjB;IAEA,MAAM,eAAe,0HAAA,CAAA,aAAU,CAAC,eAAe,CAAC;IAEhD,IAAI,CAAC,cAAc,OAAO;IAE1B,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BACpD,8OAAC;gBACC,OAAO;gBACP,MAAK;gBACL,aAAa;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAe,aAAa,WAAW;;;;;;kCACtD,8OAAC;wBAAI,WAAU;kCACZ,aAAa,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;;;;;;;;;;;;;;;;;;AAKlD;AAGO,MAAM,YAGR,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,EAAE;IAC/B,MAAM,QAAQ,0HAAA,CAAA,aAAU,CAAC,YAAY,CAAC;IAEtC,IAAI,CAAC,OAAO,OAAO;IAEnB,qBACE,8OAAC;QAAI,WAAW,CAAC,+BAA+B,EAAE,WAAW;;0BAC3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAW,CAAC,uBAAuB,EAAE,MAAM,OAAO,CAAC,CAAC,EAAE,MAAM,WAAW,CAAC,mDAAmD,CAAC;kCAC/H,cAAA,8OAAC;4BACC,WAAU;4BACV,OAAO;gCAAE,iBAAiB,MAAM,KAAK;4BAAC;sCACvC;;;;;;;;;;;kCAIH,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAA+B,MAAM,IAAI;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAyB,MAAM,WAAW;;;;;;;;;;;;;;;;;;0BAI3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAG,WAAU;0CACX,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC5B,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,8OAAC;0DAAM;;;;;;;uCAFA;;;;;;;;;;;;;;;;kCAQf,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAiC;;;;;;0CAC/C,8OAAC;gCAAG,WAAU;0CACX,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC;wCAAe,WAAU;;0DACxB,8OAAC;gDAAK,WAAU;0DAAuB;;;;;;0DACvC,8OAAC;0DAAM;;;;;;;uCAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvB;uCAEe"}}, {"offset": {"line": 2816, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2822, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/AccountDropdown.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport VerificationBadge from './VerificationBadge';\nimport { determineUserBadge } from '@/lib/verification';\n\ninterface AccountDropdownProps {\n  className?: string;\n}\n\nconst AccountDropdown = ({ className = '' }: AccountDropdownProps) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const { user, isAuthenticated, logout } = useAuth();\n  const router = useRouter();\n\n  // إغلاق القائمة عند النقر خارجها\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleToggle = () => {\n    setIsOpen(!isOpen);\n  };\n\n  const handleLogout = () => {\n    logout();\n    setIsOpen(false);\n\n    // إعادة توجيه فورية للصفحة الرئيسية\n    setTimeout(() => {\n      router.push('/');\n    }, 100);\n  };\n\n  if (!isAuthenticated || !user) {\n    // عرض أيقونة تسجيل الدخول للمستخدمين غير المسجلين\n    return (\n      <button\n        onClick={() => router.push('/')}\n        className=\"relative p-3 text-gray-600 hover:text-primary-600 transition-all duration-300 group rounded-full focus:outline-none focus:ring-0\"\n        title=\"تسجيل الدخول\"\n      >\n        <svg\n          className=\"w-6 h-6 transition-all duration-300 group-hover:text-primary-600 group-hover:scale-110\"\n          fill=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path d=\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"/>\n        </svg>\n      </button>\n    );\n  }\n\n  const userBadge = determineUserBadge(user.userType, user.subscription?.planId);\n\n  const getMenuIcon = (iconType: string) => {\n    const iconClass = \"w-5 h-5 transition-all duration-300\";\n    const iconStyle = {\n      filter: 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))',\n      transition: 'all 0.3s ease'\n    };\n\n    const handleIconHover = (e: React.MouseEvent, isEnter: boolean) => {\n      if (isEnter) {\n        e.currentTarget.style.filter = 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))';\n        e.currentTarget.style.color = '#22c55e';\n      } else {\n        e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))';\n        e.currentTarget.style.color = '';\n      }\n    };\n\n    switch (iconType) {\n      case 'dashboard':\n        return (\n          <svg\n            className={iconClass}\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            style={iconStyle}\n            onMouseEnter={(e) => handleIconHover(e, true)}\n            onMouseLeave={(e) => handleIconHover(e, false)}\n          >\n            <path d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z\" />\n          </svg>\n        );\n      case 'ads':\n        return (\n          <svg\n            className={iconClass}\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            style={iconStyle}\n            onMouseEnter={(e) => handleIconHover(e, true)}\n            onMouseLeave={(e) => handleIconHover(e, false)}\n          >\n            <path fillRule=\"evenodd\" d=\"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'subscription':\n        return (\n          <svg\n            className={iconClass}\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            style={iconStyle}\n            onMouseEnter={(e) => handleIconHover(e, true)}\n            onMouseLeave={(e) => handleIconHover(e, false)}\n          >\n            <path d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n        );\n      case 'profile':\n        return (\n          <svg\n            className={iconClass}\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            style={iconStyle}\n            onMouseEnter={(e) => handleIconHover(e, true)}\n            onMouseLeave={(e) => handleIconHover(e, false)}\n          >\n            <path fillRule=\"evenodd\" d=\"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'messages':\n        return (\n          <svg\n            className={iconClass}\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            style={iconStyle}\n            onMouseEnter={(e) => handleIconHover(e, true)}\n            onMouseLeave={(e) => handleIconHover(e, false)}\n          >\n            <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"/>\n            <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"/>\n          </svg>\n        );\n      case 'settings':\n        return (\n          <svg\n            className={iconClass}\n            fill=\"currentColor\"\n            viewBox=\"0 0 20 20\"\n            style={iconStyle}\n            onMouseEnter={(e) => handleIconHover(e, true)}\n            onMouseLeave={(e) => handleIconHover(e, false)}\n          >\n            <path fillRule=\"evenodd\" d=\"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      default:\n        return <span className=\"text-lg\">📋</span>;\n    }\n  };\n\n  const menuItems = [\n    {\n      icon: 'dashboard',\n      label: 'لوحة التحكم',\n      href: '/dashboard',\n      description: 'نظرة عامة على حسابك'\n    },\n    {\n      icon: 'messages',\n      label: 'الرسائل',\n      href: '/messages',\n      description: 'إدارة رسائلك والردود'\n    },\n    {\n      icon: 'subscription',\n      label: 'الاشتراكات',\n      href: '/my-subscription',\n      description: 'إدارة باقاتك والدفع'\n    },\n    {\n      icon: 'profile',\n      label: 'الملف الشخصي',\n      href: '/profile',\n      description: 'تحديث معلوماتك الشخصية'\n    },\n    {\n      icon: 'settings',\n      label: 'الإعدادات',\n      href: '/settings',\n      description: 'إعدادات الحساب والخصوصية'\n    },\n  ];\n\n  return (\n    <div className={`relative ${className}`} ref={dropdownRef}>\n      {/* زر الحساب */}\n      <button\n        onClick={handleToggle}\n        className={`relative p-3 text-gray-600 hover:text-primary-600 transition-all duration-300 group rounded-full focus:outline-none focus:ring-0 ${\n          isOpen ? 'shadow-lg shadow-green-500/30 bg-green-50' : ''\n        }`}\n        title=\"حسابي\"\n      >\n        <svg\n          className=\"w-6 h-6 transition-all duration-300 group-hover:text-primary-600 group-hover:scale-110\"\n          fill=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path d=\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"/>\n        </svg>\n\n        {/* نقطة الحالة */}\n        <span className=\"absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></span>\n      </button>\n\n      {/* قائمة الحساب */}\n      {isOpen && (\n        <div className=\"absolute left-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden\">\n          {/* رأس القائمة - معلومات المستخدم */}\n          <div className=\"p-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center\">\n                <span className=\"text-xl\">\n                  {user.userType === 'individual' && '👤'}\n                  {user.userType === 'business' && '🏢'}\n                  {user.userType === 'real-estate-office' && '🏘️'}\n                </span>\n              </div>\n              <div className=\"flex-1\">\n                <div className=\"flex items-center gap-2\">\n                  <h3 className=\"font-semibold text-white\">{user.name}</h3>\n                  <VerificationBadge type={userBadge.type} size=\"xs\" />\n                </div>\n                <p className=\"text-sm opacity-90\">{user.email}</p>\n                <p className=\"text-xs opacity-75\">\n                  {user.userType === 'individual' && 'مستخدم فردي'}\n                  {user.userType === 'business' && 'حساب شركة'}\n                  {user.userType === 'real-estate-office' && 'مكتب عقاري'}\n                  {' • '}\n                  عضو منذ {new Date(user.createdAt).toLocaleDateString('en-GB', {\n                    year: 'numeric',\n                    month: '2-digit',\n                    day: '2-digit'\n                  }).split('/').reverse().join('/')}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* إحصائيات سريعة */}\n          <div className=\"p-4 bg-gray-50 border-b border-gray-200\">\n            <div className=\"grid grid-cols-3 gap-4 text-center\">\n              <div>\n                <div className=\"text-lg font-bold text-primary-600\">{user.stats.activeAds}</div>\n                <div className=\"text-xs text-gray-600\">إعلانات نشطة</div>\n              </div>\n              <div>\n                <div className=\"flex items-center justify-center gap-1\">\n                  <svg\n                    className=\"w-4 h-4 text-green-600 transition-all duration-300\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                    style={{\n                      filter: 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 6px rgba(34, 197, 94, 0.8))';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))';\n                    }}\n                  >\n                    <path d=\"M10 12a2 2 0 100-4 2 2 0 000 4z\"/>\n                    <path fillRule=\"evenodd\" d=\"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z\" clipRule=\"evenodd\"/>\n                  </svg>\n                  <div className=\"text-lg font-bold text-green-600\">{user.stats.totalViews}</div>\n                </div>\n                <div className=\"text-xs text-gray-600\">مشاهدات</div>\n              </div>\n              <div>\n                <div className=\"flex items-center justify-center gap-1\">\n                  <svg\n                    className=\"w-4 h-4 text-orange-600 transition-all duration-300\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                    style={{\n                      filter: 'drop-shadow(0 0 0px rgba(249, 115, 22, 0.6))',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 6px rgba(249, 115, 22, 0.8))';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(249, 115, 22, 0.6))';\n                    }}\n                  >\n                    <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\"/>\n                    <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\"/>\n                  </svg>\n                  <div className=\"text-lg font-bold text-orange-600\">{user.stats.totalContacts}</div>\n                </div>\n                <div className=\"text-xs text-gray-600\">استفسارات</div>\n              </div>\n            </div>\n          </div>\n\n          {/* عناصر القائمة */}\n          <div className=\"py-2\">\n            {menuItems.map((item, index) => (\n              <Link\n                key={index}\n                href={item.href}\n                className=\"flex items-center gap-3 px-4 py-3 hover:bg-gray-50 transition-colors\"\n                onClick={() => setIsOpen(false)}\n              >\n                <span className=\"text-primary-600\">{getMenuIcon(item.icon)}</span>\n                <div className=\"flex-1\">\n                  <div className=\"font-medium text-gray-800\">{item.label}</div>\n                  <div className=\"text-xs text-gray-500\">{item.description}</div>\n                </div>\n                <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                </svg>\n              </Link>\n            ))}\n          </div>\n\n          {/* معلومات الاشتراك */}\n          {user.subscription && (\n            <div className=\"p-4 bg-primary-50 border-t border-gray-200\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"text-sm font-medium text-primary-800\">{user.subscription.planName}</span>\n                  <VerificationBadge type={userBadge.type} size=\"xs\" />\n                </div>\n                <span className={`text-xs px-2 py-1 rounded-full ${\n                  user.subscription.isActive\n                    ? 'bg-green-100 text-green-800'\n                    : 'bg-red-100 text-red-800'\n                }`}>\n                  {user.subscription.isActive ? 'نشط' : 'غير نشط'}\n                </span>\n              </div>\n              <div className=\"text-xs text-primary-600\">\n                ينتهي في {new Date(user.subscription.endDate).toLocaleDateString('en-GB', {\n                  year: 'numeric',\n                  month: '2-digit',\n                  day: '2-digit'\n                }).split('/').reverse().join('/')}\n              </div>\n            </div>\n          )}\n\n          {/* أزرار التحكم */}\n          <div className=\"p-4 border-t border-gray-200 bg-gray-50\">\n            <div className=\"flex gap-2\">\n              <button\n                onClick={() => {\n                  router.push('/add-ad');\n                  setIsOpen(false);\n                }}\n                className=\"flex-1 px-3 py-2 text-sm bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n              >\n                إضافة إعلان\n              </button>\n              <button\n                onClick={handleLogout}\n                className=\"flex-1 px-3 py-2 text-sm bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors\"\n              >\n                تسجيل الخروج\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AccountDropdown;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAaA,MAAM,kBAAkB,CAAC,EAAE,YAAY,EAAE,EAAwB;IAC/D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,UAAU,CAAC;IACb;IAEA,MAAM,eAAe;QACnB;QACA,UAAU;QAEV,oCAAoC;QACpC,WAAW;YACT,OAAO,IAAI,CAAC;QACd,GAAG;IACL;IAEA,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAC7B,kDAAkD;QAClD,qBACE,8OAAC;YACC,SAAS,IAAM,OAAO,IAAI,CAAC;YAC3B,WAAU;YACV,OAAM;sBAEN,cAAA,8OAAC;gBACC,WAAU;gBACV,MAAK;gBACL,SAAQ;0BAER,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;IAIhB;IAEA,MAAM,YAAY,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,QAAQ,EAAE,KAAK,YAAY,EAAE;IAEvE,MAAM,cAAc,CAAC;QACnB,MAAM,YAAY;QAClB,MAAM,YAAY;YAChB,QAAQ;YACR,YAAY;QACd;QAEA,MAAM,kBAAkB,CAAC,GAAqB;YAC5C,IAAI,SAAS;gBACX,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;YAChC,OAAO;gBACL,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;YAChC;QACF;QAEA,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAW;oBACX,MAAK;oBACL,SAAQ;oBACR,OAAO;oBACP,cAAc,CAAC,IAAM,gBAAgB,GAAG;oBACxC,cAAc,CAAC,IAAM,gBAAgB,GAAG;8BAExC,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAW;oBACX,MAAK;oBACL,SAAQ;oBACR,OAAO;oBACP,cAAc,CAAC,IAAM,gBAAgB,GAAG;oBACxC,cAAc,CAAC,IAAM,gBAAgB,GAAG;8BAExC,cAAA,8OAAC;wBAAK,UAAS;wBAAU,GAAE;wBAAoM,UAAS;;;;;;;;;;;YAG9O,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAW;oBACX,MAAK;oBACL,SAAQ;oBACR,OAAO;oBACP,cAAc,CAAC,IAAM,gBAAgB,GAAG;oBACxC,cAAc,CAAC,IAAM,gBAAgB,GAAG;8BAExC,cAAA,8OAAC;wBAAK,GAAE;;;;;;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAW;oBACX,MAAK;oBACL,SAAQ;oBACR,OAAO;oBACP,cAAc,CAAC,IAAM,gBAAgB,GAAG;oBACxC,cAAc,CAAC,IAAM,gBAAgB,GAAG;8BAExC,cAAA,8OAAC;wBAAK,UAAS;wBAAU,GAAE;wBAAsD,UAAS;;;;;;;;;;;YAGhG,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAW;oBACX,MAAK;oBACL,SAAQ;oBACR,OAAO;oBACP,cAAc,CAAC,IAAM,gBAAgB,GAAG;oBACxC,cAAc,CAAC,IAAM,gBAAgB,GAAG;;sCAExC,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAK,GAAE;;;;;;;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC;oBACC,WAAW;oBACX,MAAK;oBACL,SAAQ;oBACR,OAAO;oBACP,cAAc,CAAC,IAAM,gBAAgB,GAAG;oBACxC,cAAc,CAAC,IAAM,gBAAgB,GAAG;8BAExC,cAAA,8OAAC;wBAAK,UAAS;wBAAU,GAAE;wBAA+f,UAAS;;;;;;;;;;;YAGziB;gBACE,qBAAO,8OAAC;oBAAK,WAAU;8BAAU;;;;;;QACrC;IACF;IAEA,MAAM,YAAY;QAChB;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,MAAM;YACN,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;QAAE,KAAK;;0BAE5C,8OAAC;gBACC,SAAS;gBACT,WAAW,CAAC,iIAAiI,EAC3I,SAAS,8CAA8C,IACvD;gBACF,OAAM;;kCAEN,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,SAAQ;kCAER,cAAA,8OAAC;4BAAK,GAAE;;;;;;;;;;;kCAIV,8OAAC;wBAAK,WAAU;;;;;;;;;;;;YAIjB,wBACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;;4CACb,KAAK,QAAQ,KAAK,gBAAgB;4CAClC,KAAK,QAAQ,KAAK,cAAc;4CAChC,KAAK,QAAQ,KAAK,wBAAwB;;;;;;;;;;;;8CAG/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA4B,KAAK,IAAI;;;;;;8DACnD,8OAAC,uIAAA,CAAA,UAAiB;oDAAC,MAAM,UAAU,IAAI;oDAAE,MAAK;;;;;;;;;;;;sDAEhD,8OAAC;4CAAE,WAAU;sDAAsB,KAAK,KAAK;;;;;;sDAC7C,8OAAC;4CAAE,WAAU;;gDACV,KAAK,QAAQ,KAAK,gBAAgB;gDAClC,KAAK,QAAQ,KAAK,cAAc;gDAChC,KAAK,QAAQ,KAAK,wBAAwB;gDAC1C;gDAAM;gDACE,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,CAAC,SAAS;oDAC5D,MAAM;oDACN,OAAO;oDACP,KAAK;gDACP,GAAG,KAAK,CAAC,KAAK,OAAO,GAAG,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAOrC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDAAsC,KAAK,KAAK,CAAC,SAAS;;;;;;sDACzE,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,OAAO;wDACL,QAAQ;wDACR,YAAY;oDACd;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDACjC;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDACjC;;sEAEA,8OAAC;4DAAK,GAAE;;;;;;sEACR,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAA0I,UAAS;;;;;;;;;;;;8DAEhL,8OAAC;oDAAI,WAAU;8DAAoC,KAAK,KAAK,CAAC,UAAU;;;;;;;;;;;;sDAE1E,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,OAAO;wDACL,QAAQ;wDACR,YAAY;oDACd;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDACjC;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDACjC;;sEAEA,8OAAC;4DAAK,GAAE;;;;;;sEACR,8OAAC;4DAAK,GAAE;;;;;;;;;;;;8DAEV,8OAAC;oDAAI,WAAU;8DAAqC,KAAK,KAAK,CAAC,aAAa;;;;;;;;;;;;sDAE9E,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,8OAAC;wCAAK,WAAU;kDAAoB,YAAY,KAAK,IAAI;;;;;;kDACzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6B,KAAK,KAAK;;;;;;0DACtD,8OAAC;gDAAI,WAAU;0DAAyB,KAAK,WAAW;;;;;;;;;;;;kDAE1D,8OAAC;wCAAI,WAAU;wCAAwB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC/E,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;+BAXlE;;;;;;;;;;oBAkBV,KAAK,YAAY,kBAChB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAwC,KAAK,YAAY,CAAC,QAAQ;;;;;;0DAClF,8OAAC,uIAAA,CAAA,UAAiB;gDAAC,MAAM,UAAU,IAAI;gDAAE,MAAK;;;;;;;;;;;;kDAEhD,8OAAC;wCAAK,WAAW,CAAC,+BAA+B,EAC/C,KAAK,YAAY,CAAC,QAAQ,GACtB,gCACA,2BACJ;kDACC,KAAK,YAAY,CAAC,QAAQ,GAAG,QAAQ;;;;;;;;;;;;0CAG1C,8OAAC;gCAAI,WAAU;;oCAA2B;oCAC9B,IAAI,KAAK,KAAK,YAAY,CAAC,OAAO,EAAE,kBAAkB,CAAC,SAAS;wCACxE,MAAM;wCACN,OAAO;wCACP,KAAK;oCACP,GAAG,KAAK,CAAC,KAAK,OAAO,GAAG,IAAI,CAAC;;;;;;;;;;;;;kCAMnC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;wCACP,OAAO,IAAI,CAAC;wCACZ,UAAU;oCACZ;oCACA,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe"}}, {"offset": {"line": 3592, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3598, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/ClientOnlyAccount.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport AccountDropdown from './AccountDropdown';\n\nconst ClientOnlyAccount = () => {\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  if (!isClient) {\n    // عرض placeholder أثناء التحميل\n    return (\n      <div className=\"relative p-3 text-gray-600 rounded-full\">\n        <svg\n          className=\"w-6 h-6\"\n          fill=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path d=\"M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z\"/>\n        </svg>\n      </div>\n    );\n  }\n\n  return <AccountDropdown />;\n};\n\nexport default ClientOnlyAccount;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,oBAAoB;IACxB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,IAAI,CAAC,UAAU;QACb,gCAAgC;QAChC,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBACC,WAAU;gBACV,MAAK;gBACL,SAAQ;0BAER,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;;;;;;IAIhB;IAEA,qBAAO,8OAAC,qIAAA,CAAA,UAAe;;;;;AACzB;uCAEe"}}, {"offset": {"line": 3646, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3652, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/lib/notifications.ts"], "sourcesContent": ["// نظام الإشعارات المتقدم\nexport interface Notification {\n  id: number;\n  userId: number;\n  type: 'message' | 'ad_expired' | 'ad_approved' | 'ad_rejected' | 'payment' | 'system' | 'offer' | 'favorite_ad' | 'new_follower';\n  title: string;\n  message: string;\n  data?: any;\n  read: boolean;\n  priority: 'low' | 'medium' | 'high' | 'urgent';\n  category: 'account' | 'ads' | 'messages' | 'payments' | 'system';\n  actionUrl?: string;\n  actionText?: string;\n  expiresAt?: string;\n  createdAt: string;\n  readAt?: string;\n}\n\nexport interface NotificationSettings {\n  userId: number;\n  emailNotifications: boolean;\n  pushNotifications: boolean;\n  smsNotifications: boolean;\n  categories: {\n    messages: boolean;\n    ads: boolean;\n    payments: boolean;\n    system: boolean;\n    marketing: boolean;\n  };\n  frequency: 'instant' | 'hourly' | 'daily' | 'weekly';\n  quietHours: {\n    enabled: boolean;\n    start: string; // HH:MM\n    end: string; // HH:MM\n  };\n}\n\n// بيانات تجريبية للإشعارات\nconst sampleNotifications: Notification[] = [\n  {\n    id: 1,\n    userId: 1,\n    type: 'message',\n    title: 'رسالة جديدة',\n    message: 'لديك رسالة جديدة من محمد أحمد بخصوص إعلان \"شقة للبيع في دمشق\"',\n    data: { conversationId: 1, senderId: 2 },\n    read: false,\n    priority: 'high',\n    category: 'messages',\n    actionUrl: '/dashboard?tab=messages',\n    actionText: 'عرض الرسالة',\n    createdAt: '2024-01-20T14:30:00Z'\n  },\n  {\n    id: 2,\n    userId: 1,\n    type: 'ad_approved',\n    title: 'تم قبول إعلانك',\n    message: 'تم قبول ونشر إعلان \"شقة للبيع في دمشق - المالكي\" وهو الآن متاح للمشاهدة',\n    data: { adId: 1 },\n    read: false,\n    priority: 'medium',\n    category: 'ads',\n    actionUrl: '/ad/1',\n    actionText: 'عرض الإعلان',\n    createdAt: '2024-01-20T10:15:00Z'\n  },\n  {\n    id: 3,\n    userId: 1,\n    type: 'offer',\n    title: 'عرض جديد',\n    message: 'تلقيت عرضاً بقيمة 80,000,000 ل.س على إعلان \"شقة للبيع في دمشق\"',\n    data: { adId: 1, offerId: 1, amount: 80000000, currency: 'SYP' },\n    read: true,\n    priority: 'high',\n    category: 'messages',\n    actionUrl: '/dashboard?tab=offers',\n    actionText: 'عرض التفاصيل',\n    createdAt: '2024-01-19T16:45:00Z',\n    readAt: '2024-01-19T17:00:00Z'\n  },\n  {\n    id: 4,\n    userId: 1,\n    type: 'ad_expired',\n    title: 'انتهاء صلاحية إعلان',\n    message: 'انتهت صلاحية إعلان \"لابتوب Dell Gaming\". يمكنك تجديده الآن',\n    data: { adId: 5 },\n    read: false,\n    priority: 'medium',\n    category: 'ads',\n    actionUrl: '/dashboard?tab=ads',\n    actionText: 'تجديد الإعلان',\n    expiresAt: '2024-01-25T23:59:59Z',\n    createdAt: '2024-01-18T09:00:00Z'\n  },\n  {\n    id: 5,\n    userId: 1,\n    type: 'payment',\n    title: 'تم الدفع بنجاح',\n    message: 'تم تأكيد دفع اشتراك الخطة المميزة. مميزاتك الجديدة متاحة الآن',\n    data: { planId: 'premium', amount: 50000, currency: 'SYP' },\n    read: true,\n    priority: 'medium',\n    category: 'payments',\n    actionUrl: '/dashboard?tab=subscription',\n    actionText: 'عرض الاشتراك',\n    createdAt: '2024-01-17T11:30:00Z',\n    readAt: '2024-01-17T12:00:00Z'\n  }\n];\n\nconst sampleNotificationSettings: NotificationSettings[] = [\n  {\n    userId: 1,\n    emailNotifications: true,\n    pushNotifications: true,\n    smsNotifications: false,\n    categories: {\n      messages: true,\n      ads: true,\n      payments: true,\n      system: true,\n      marketing: false\n    },\n    frequency: 'instant',\n    quietHours: {\n      enabled: true,\n      start: '22:00',\n      end: '08:00'\n    }\n  }\n];\n\nexport class NotificationService {\n  // إنشاء إشعار جديد\n  static createNotification(notification: Omit<Notification, 'id' | 'createdAt'>): Notification {\n    const newNotification: Notification = {\n      id: Date.now(),\n      createdAt: new Date().toISOString(),\n      ...notification\n    };\n\n    sampleNotifications.unshift(newNotification);\n    return newNotification;\n  }\n\n  // الحصول على إشعارات المستخدم\n  static getUserNotifications(\n    userId: number,\n    filters?: {\n      category?: string;\n      read?: boolean;\n      priority?: string;\n      limit?: number;\n      offset?: number;\n    }\n  ): { notifications: Notification[]; total: number } {\n    let userNotifications = sampleNotifications.filter(n => n.userId === userId);\n\n    // تطبيق الفلاتر\n    if (filters) {\n      if (filters.category) {\n        userNotifications = userNotifications.filter(n => n.category === filters.category);\n      }\n      if (filters.read !== undefined) {\n        userNotifications = userNotifications.filter(n => n.read === filters.read);\n      }\n      if (filters.priority) {\n        userNotifications = userNotifications.filter(n => n.priority === filters.priority);\n      }\n    }\n\n    // ترتيب حسب التاريخ\n    userNotifications.sort((a, b) =>\n      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n    );\n\n    const total = userNotifications.length;\n\n    // تطبيق الصفحات\n    if (filters?.limit) {\n      const offset = filters.offset || 0;\n      userNotifications = userNotifications.slice(offset, offset + filters.limit);\n    }\n\n    return { notifications: userNotifications, total };\n  }\n\n  // تحديد إشعار كمقروء\n  static markAsRead(notificationId: number, userId: number): boolean {\n    const notification = sampleNotifications.find(n =>\n      n.id === notificationId && n.userId === userId\n    );\n\n    if (notification && !notification.read) {\n      notification.read = true;\n      notification.readAt = new Date().toISOString();\n      return true;\n    }\n\n    return false;\n  }\n\n  // تحديد جميع الإشعارات كمقروءة\n  static markAllAsRead(userId: number): number {\n    let count = 0;\n    sampleNotifications.forEach(notification => {\n      if (notification.userId === userId && !notification.read) {\n        notification.read = true;\n        notification.readAt = new Date().toISOString();\n        count++;\n      }\n    });\n    return count;\n  }\n\n  // حذف إشعار\n  static deleteNotification(notificationId: number, userId: number): boolean {\n    const index = sampleNotifications.findIndex(n =>\n      n.id === notificationId && n.userId === userId\n    );\n\n    if (index !== -1) {\n      sampleNotifications.splice(index, 1);\n      return true;\n    }\n\n    return false;\n  }\n\n  // حذف جميع الإشعارات المقروءة\n  static deleteReadNotifications(userId: number): number {\n    const initialLength = sampleNotifications.length;\n\n    for (let i = sampleNotifications.length - 1; i >= 0; i--) {\n      const notification = sampleNotifications[i];\n      if (notification.userId === userId && notification.read) {\n        sampleNotifications.splice(i, 1);\n      }\n    }\n\n    return initialLength - sampleNotifications.length;\n  }\n\n  // الحصول على عدد الإشعارات غير المقروءة\n  static getUnreadCount(userId: number): number {\n    return sampleNotifications.filter(n =>\n      n.userId === userId && !n.read\n    ).length;\n  }\n\n  // الحصول على إحصائيات الإشعارات\n  static getNotificationStats(userId: number) {\n    const userNotifications = sampleNotifications.filter(n => n.userId === userId);\n\n    const stats = {\n      total: userNotifications.length,\n      unread: userNotifications.filter(n => !n.read).length,\n      byCategory: {} as Record<string, number>,\n      byPriority: {} as Record<string, number>,\n      recent: userNotifications.filter(n =>\n        new Date(n.createdAt).getTime() > Date.now() - (24 * 60 * 60 * 1000)\n      ).length\n    };\n\n    // إحصائيات حسب التصنيف\n    userNotifications.forEach(n => {\n      stats.byCategory[n.category] = (stats.byCategory[n.category] || 0) + 1;\n      stats.byPriority[n.priority] = (stats.byPriority[n.priority] || 0) + 1;\n    });\n\n    return stats;\n  }\n\n  // الحصول على إعدادات الإشعارات\n  static getUserNotificationSettings(userId: number): NotificationSettings | null {\n    return sampleNotificationSettings.find(s => s.userId === userId) || null;\n  }\n\n  // تحديث إعدادات الإشعارات\n  static updateNotificationSettings(\n    userId: number,\n    settings: Partial<NotificationSettings>\n  ): boolean {\n    const index = sampleNotificationSettings.findIndex(s => s.userId === userId);\n\n    if (index !== -1) {\n      sampleNotificationSettings[index] = {\n        ...sampleNotificationSettings[index],\n        ...settings,\n        userId // التأكد من عدم تغيير معرف المستخدم\n      };\n      return true;\n    } else {\n      // إنشاء إعدادات جديدة\n      const newSettings: NotificationSettings = {\n        userId,\n        emailNotifications: true,\n        pushNotifications: true,\n        smsNotifications: false,\n        categories: {\n          messages: true,\n          ads: true,\n          payments: true,\n          system: true,\n          marketing: false\n        },\n        frequency: 'instant',\n        quietHours: {\n          enabled: false,\n          start: '22:00',\n          end: '08:00'\n        },\n        ...settings\n      };\n\n      sampleNotificationSettings.push(newSettings);\n      return true;\n    }\n  }\n\n  // إنشاء إشعارات تلقائية للأحداث المختلفة\n  static createAdApprovedNotification(userId: number, adId: number, adTitle: string) {\n    return this.createNotification({\n      userId,\n      type: 'ad_approved',\n      title: 'تم قبول إعلانك',\n      message: `تم قبول ونشر إعلان \"${adTitle}\" وهو الآن متاح للمشاهدة`,\n      data: { adId },\n      read: false,\n      priority: 'medium',\n      category: 'ads',\n      actionUrl: `/ad/${adId}`,\n      actionText: 'عرض الإعلان'\n    });\n  }\n\n  static createNewMessageNotification(\n    userId: number,\n    senderName: string,\n    adTitle: string,\n    conversationId: number\n  ) {\n    return this.createNotification({\n      userId,\n      type: 'message',\n      title: 'رسالة جديدة',\n      message: `لديك رسالة جديدة من ${senderName} بخصوص إعلان \"${adTitle}\"`,\n      data: { conversationId },\n      read: false,\n      priority: 'high',\n      category: 'messages',\n      actionUrl: '/dashboard?tab=messages',\n      actionText: 'عرض الرسالة'\n    });\n  }\n\n  static createOfferNotification(\n    userId: number,\n    amount: number,\n    currency: string,\n    adTitle: string,\n    offerId: number\n  ) {\n    return this.createNotification({\n      userId,\n      type: 'offer',\n      title: 'عرض جديد',\n      message: `تلقيت عرضاً بقيمة ${amount.toLocaleString()} ${currency === 'SYP' ? 'ل.س' : '$'} على إعلان \"${adTitle}\"`,\n      data: { offerId, amount, currency },\n      read: false,\n      priority: 'high',\n      category: 'messages',\n      actionUrl: '/dashboard?tab=offers',\n      actionText: 'عرض التفاصيل'\n    });\n  }\n\n  // تنظيف الإشعارات القديمة\n  static cleanupOldNotifications(userId: number, daysOld: number = 30): number {\n    const cutoffDate = new Date(Date.now() - (daysOld * 24 * 60 * 60 * 1000));\n    const initialLength = sampleNotifications.length;\n\n    for (let i = sampleNotifications.length - 1; i >= 0; i--) {\n      const notification = sampleNotifications[i];\n      if (notification.userId === userId &&\n          new Date(notification.createdAt) < cutoffDate) {\n        sampleNotifications.splice(i, 1);\n      }\n    }\n\n    return initialLength - sampleNotifications.length;\n  }\n\n  // التحقق من الساعات الهادئة\n  static isQuietHours(userId: number): boolean {\n    const settings = this.getUserNotificationSettings(userId);\n\n    if (!settings || !settings.quietHours.enabled) {\n      return false;\n    }\n\n    const now = new Date();\n    const currentTime = now.getHours() * 60 + now.getMinutes();\n\n    const [startHour, startMin] = settings.quietHours.start.split(':').map(Number);\n    const [endHour, endMin] = settings.quietHours.end.split(':').map(Number);\n\n    const startTime = startHour * 60 + startMin;\n    const endTime = endHour * 60 + endMin;\n\n    if (startTime <= endTime) {\n      return currentTime >= startTime && currentTime <= endTime;\n    } else {\n      // الساعات الهادئة تمتد عبر منتصف الليل\n      return currentTime >= startTime || currentTime <= endTime;\n    }\n  }\n\n  // طلب إذن الإشعارات من المتصفح\n  static async requestNotificationPermission(): Promise<NotificationPermission> {\n    if (typeof window === 'undefined' || !('Notification' in window)) {\n      return 'denied';\n    }\n\n    if (Notification.permission === 'default') {\n      return await Notification.requestPermission();\n    }\n\n    return Notification.permission;\n  }\n\n  // إرسال إشعار push في المتصفح\n  static sendBrowserNotification(title: string, message: string, icon?: string) {\n    if (typeof window !== 'undefined' && 'Notification' in window && Notification.permission === 'granted') {\n      return new Notification(title, {\n        body: message,\n        icon: icon || '/favicon.ico',\n        tag: Date.now().toString()\n      });\n    }\n    return null;\n  }\n\n  // إشعارات خاصة بالرسائل الجديدة\n  static notifyNewMessage(userId: number, senderName: string, adTitle: string, conversationId: number) {\n    const notification = this.sendRealTimeNotification({\n      userId,\n      type: 'message',\n      title: 'رسالة جديدة 📨',\n      message: `رسالة جديدة من ${senderName} حول \"${adTitle}\"`,\n      data: { conversationId, senderName, adTitle },\n      priority: 'high',\n      category: 'messages',\n      actionUrl: `/messages/${conversationId}`,\n      actionText: 'عرض الرسالة'\n    });\n\n    // إرسال إشعار المتصفح\n    this.sendBrowserNotification(\n      'رسالة جديدة 📨',\n      `رسالة جديدة من ${senderName}`,\n      '💬'\n    );\n\n    return notification;\n  }\n\n  // إشعارات خاصة بالمدفوعات\n  static notifyPaymentSuccess(userId: number, amount: number, subscriptionType: string) {\n    const notification = this.sendRealTimeNotification({\n      userId,\n      type: 'payment',\n      title: 'تم تأكيد الدفع 💳',\n      message: `تم تأكيد دفع ${amount} ل.س لاشتراك ${subscriptionType} بنجاح`,\n      data: { amount, subscriptionType },\n      priority: 'high',\n      category: 'payments',\n      actionUrl: '/profile/subscription',\n      actionText: 'عرض الاشتراك'\n    });\n\n    // إرسال إشعار المتصفح\n    this.sendBrowserNotification(\n      'تم تأكيد الدفع 💳',\n      `تم تأكيد دفع ${amount} ل.س بنجاح`,\n      '💳'\n    );\n\n    return notification;\n  }\n\n  // إشعارات خاصة بحالة الإعلانات\n  static notifyAdStatusChange(userId: number, adTitle: string, status: 'approved' | 'rejected' | 'expired', adId: number) {\n    const statusMessages = {\n      approved: 'تم الموافقة على إعلانك ✅',\n      rejected: 'تم رفض إعلانك ❌',\n      expired: 'انتهت صلاحية إعلانك ⏰'\n    };\n\n    const notification = this.sendRealTimeNotification({\n      userId,\n      type: `ad_${status}` as any,\n      title: statusMessages[status],\n      message: `${statusMessages[status]} \"${adTitle}\"`,\n      data: { adId, status, adTitle },\n      priority: status === 'rejected' ? 'urgent' : 'medium',\n      category: 'ads',\n      actionUrl: `/ads/${adId}`,\n      actionText: 'عرض الإعلان'\n    });\n\n    // إرسال إشعار المتصفح\n    this.sendBrowserNotification(\n      statusMessages[status],\n      `\"${adTitle}\"`,\n      status === 'approved' ? '✅' : status === 'rejected' ? '❌' : '⏰'\n    );\n\n    return notification;\n  }\n\n  // إشعارات خاصة بالعروض الجديدة\n  static notifyNewOffer(userId: number, offerAmount: number, adTitle: string, buyerName: string, adId: number) {\n    const notification = this.sendRealTimeNotification({\n      userId,\n      type: 'offer',\n      title: 'عرض جديد 💰',\n      message: `عرض جديد بقيمة ${offerAmount} ل.س من ${buyerName} على \"${adTitle}\"`,\n      data: { offerAmount, adTitle, buyerName, adId },\n      priority: 'high',\n      category: 'messages',\n      actionUrl: `/ads/${adId}/offers`,\n      actionText: 'عرض العروض'\n    });\n\n    // إرسال إشعار المتصفح\n    this.sendBrowserNotification(\n      'عرض جديد 💰',\n      `عرض بقيمة ${offerAmount} ل.س من ${buyerName}`,\n      '💰'\n    );\n\n    return notification;\n  }\n\n  // جلب إحصائيات الإشعارات\n  static getNotificationStats(userId: number) {\n    const userNotifications = sampleNotifications.filter(n => n.userId === userId);\n\n    return {\n      total: userNotifications.length,\n      unread: userNotifications.filter(n => !n.read).length,\n      byCategory: {\n        messages: userNotifications.filter(n => n.category === 'messages').length,\n        ads: userNotifications.filter(n => n.category === 'ads').length,\n        payments: userNotifications.filter(n => n.category === 'payments').length,\n        system: userNotifications.filter(n => n.category === 'system').length,\n        account: userNotifications.filter(n => n.category === 'account').length\n      },\n      byPriority: {\n        urgent: userNotifications.filter(n => n.priority === 'urgent').length,\n        high: userNotifications.filter(n => n.priority === 'high').length,\n        medium: userNotifications.filter(n => n.priority === 'medium').length,\n        low: userNotifications.filter(n => n.priority === 'low').length\n      }\n    };\n  }\n}\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;AAsCzB,2BAA2B;AAC3B,MAAM,sBAAsC;IAC1C;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;YAAE,gBAAgB;YAAG,UAAU;QAAE;QACvC,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW;QACX,YAAY;QACZ,WAAW;IACb;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;YAAE,MAAM;QAAE;QAChB,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW;QACX,YAAY;QACZ,WAAW;IACb;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;YAAE,MAAM;YAAG,SAAS;YAAG,QAAQ;YAAU,UAAU;QAAM;QAC/D,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW;QACX,YAAY;QACZ,WAAW;QACX,QAAQ;IACV;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;YAAE,MAAM;QAAE;QAChB,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW;QACX,YAAY;QACZ,WAAW;QACX,WAAW;IACb;IACA;QACE,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,OAAO;QACP,SAAS;QACT,MAAM;YAAE,QAAQ;YAAW,QAAQ;YAAO,UAAU;QAAM;QAC1D,MAAM;QACN,UAAU;QACV,UAAU;QACV,WAAW;QACX,YAAY;QACZ,WAAW;QACX,QAAQ;IACV;CACD;AAED,MAAM,6BAAqD;IACzD;QACE,QAAQ;QACR,oBAAoB;QACpB,mBAAmB;QACnB,kBAAkB;QAClB,YAAY;YACV,UAAU;YACV,KAAK;YACL,UAAU;YACV,QAAQ;YACR,WAAW;QACb;QACA,WAAW;QACX,YAAY;YACV,SAAS;YACT,OAAO;YACP,KAAK;QACP;IACF;CACD;AAEM,MAAM;IACX,mBAAmB;IACnB,OAAO,mBAAmB,YAAoD,EAAgB;QAC5F,MAAM,kBAAgC;YACpC,IAAI,KAAK,GAAG;YACZ,WAAW,IAAI,OAAO,WAAW;YACjC,GAAG,YAAY;QACjB;QAEA,oBAAoB,OAAO,CAAC;QAC5B,OAAO;IACT;IAEA,8BAA8B;IAC9B,OAAO,qBACL,MAAc,EACd,OAMC,EACiD;QAClD,IAAI,oBAAoB,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAErE,gBAAgB;QAChB,IAAI,SAAS;YACX,IAAI,QAAQ,QAAQ,EAAE;gBACpB,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,QAAQ;YACnF;YACA,IAAI,QAAQ,IAAI,KAAK,WAAW;gBAC9B,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,IAAI;YAC3E;YACA,IAAI,QAAQ,QAAQ,EAAE;gBACpB,oBAAoB,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,QAAQ;YACnF;QACF;QAEA,oBAAoB;QACpB,kBAAkB,IAAI,CAAC,CAAC,GAAG,IACzB,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;QAGjE,MAAM,QAAQ,kBAAkB,MAAM;QAEtC,gBAAgB;QAChB,IAAI,SAAS,OAAO;YAClB,MAAM,SAAS,QAAQ,MAAM,IAAI;YACjC,oBAAoB,kBAAkB,KAAK,CAAC,QAAQ,SAAS,QAAQ,KAAK;QAC5E;QAEA,OAAO;YAAE,eAAe;YAAmB;QAAM;IACnD;IAEA,qBAAqB;IACrB,OAAO,WAAW,cAAsB,EAAE,MAAc,EAAW;QACjE,MAAM,eAAe,oBAAoB,IAAI,CAAC,CAAA,IAC5C,EAAE,EAAE,KAAK,kBAAkB,EAAE,MAAM,KAAK;QAG1C,IAAI,gBAAgB,CAAC,aAAa,IAAI,EAAE;YACtC,aAAa,IAAI,GAAG;YACpB,aAAa,MAAM,GAAG,IAAI,OAAO,WAAW;YAC5C,OAAO;QACT;QAEA,OAAO;IACT;IAEA,+BAA+B;IAC/B,OAAO,cAAc,MAAc,EAAU;QAC3C,IAAI,QAAQ;QACZ,oBAAoB,OAAO,CAAC,CAAA;YAC1B,IAAI,aAAa,MAAM,KAAK,UAAU,CAAC,aAAa,IAAI,EAAE;gBACxD,aAAa,IAAI,GAAG;gBACpB,aAAa,MAAM,GAAG,IAAI,OAAO,WAAW;gBAC5C;YACF;QACF;QACA,OAAO;IACT;IAEA,YAAY;IACZ,OAAO,mBAAmB,cAAsB,EAAE,MAAc,EAAW;QACzE,MAAM,QAAQ,oBAAoB,SAAS,CAAC,CAAA,IAC1C,EAAE,EAAE,KAAK,kBAAkB,EAAE,MAAM,KAAK;QAG1C,IAAI,UAAU,CAAC,GAAG;YAChB,oBAAoB,MAAM,CAAC,OAAO;YAClC,OAAO;QACT;QAEA,OAAO;IACT;IAEA,8BAA8B;IAC9B,OAAO,wBAAwB,MAAc,EAAU;QACrD,MAAM,gBAAgB,oBAAoB,MAAM;QAEhD,IAAK,IAAI,IAAI,oBAAoB,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YACxD,MAAM,eAAe,mBAAmB,CAAC,EAAE;YAC3C,IAAI,aAAa,MAAM,KAAK,UAAU,aAAa,IAAI,EAAE;gBACvD,oBAAoB,MAAM,CAAC,GAAG;YAChC;QACF;QAEA,OAAO,gBAAgB,oBAAoB,MAAM;IACnD;IAEA,wCAAwC;IACxC,OAAO,eAAe,MAAc,EAAU;QAC5C,OAAO,oBAAoB,MAAM,CAAC,CAAA,IAChC,EAAE,MAAM,KAAK,UAAU,CAAC,EAAE,IAAI,EAC9B,MAAM;IACV;IAEA,gCAAgC;IAChC,OAAO,qBAAqB,MAAc,EAAE;QAC1C,MAAM,oBAAoB,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAEvE,MAAM,QAAQ;YACZ,OAAO,kBAAkB,MAAM;YAC/B,QAAQ,kBAAkB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;YACrD,YAAY,CAAC;YACb,YAAY,CAAC;YACb,QAAQ,kBAAkB,MAAM,CAAC,CAAA,IAC/B,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,KAAK,GAAG,KAAM,KAAK,KAAK,KAAK,MAC/D,MAAM;QACV;QAEA,uBAAuB;QACvB,kBAAkB,OAAO,CAAC,CAAA;YACxB,MAAM,UAAU,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,MAAM,UAAU,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;YACrE,MAAM,UAAU,CAAC,EAAE,QAAQ,CAAC,GAAG,CAAC,MAAM,UAAU,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,IAAI;QACvE;QAEA,OAAO;IACT;IAEA,+BAA+B;IAC/B,OAAO,4BAA4B,MAAc,EAA+B;QAC9E,OAAO,2BAA2B,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW;IACtE;IAEA,0BAA0B;IAC1B,OAAO,2BACL,MAAc,EACd,QAAuC,EAC9B;QACT,MAAM,QAAQ,2BAA2B,SAAS,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAErE,IAAI,UAAU,CAAC,GAAG;YAChB,0BAA0B,CAAC,MAAM,GAAG;gBAClC,GAAG,0BAA0B,CAAC,MAAM;gBACpC,GAAG,QAAQ;gBACX;YACF;YACA,OAAO;QACT,OAAO;YACL,sBAAsB;YACtB,MAAM,cAAoC;gBACxC;gBACA,oBAAoB;gBACpB,mBAAmB;gBACnB,kBAAkB;gBAClB,YAAY;oBACV,UAAU;oBACV,KAAK;oBACL,UAAU;oBACV,QAAQ;oBACR,WAAW;gBACb;gBACA,WAAW;gBACX,YAAY;oBACV,SAAS;oBACT,OAAO;oBACP,KAAK;gBACP;gBACA,GAAG,QAAQ;YACb;YAEA,2BAA2B,IAAI,CAAC;YAChC,OAAO;QACT;IACF;IAEA,yCAAyC;IACzC,OAAO,6BAA6B,MAAc,EAAE,IAAY,EAAE,OAAe,EAAE;QACjF,OAAO,IAAI,CAAC,kBAAkB,CAAC;YAC7B;YACA,MAAM;YACN,OAAO;YACP,SAAS,CAAC,oBAAoB,EAAE,QAAQ,wBAAwB,CAAC;YACjE,MAAM;gBAAE;YAAK;YACb,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW,CAAC,IAAI,EAAE,MAAM;YACxB,YAAY;QACd;IACF;IAEA,OAAO,6BACL,MAAc,EACd,UAAkB,EAClB,OAAe,EACf,cAAsB,EACtB;QACA,OAAO,IAAI,CAAC,kBAAkB,CAAC;YAC7B;YACA,MAAM;YACN,OAAO;YACP,SAAS,CAAC,oBAAoB,EAAE,WAAW,cAAc,EAAE,QAAQ,CAAC,CAAC;YACrE,MAAM;gBAAE;YAAe;YACvB,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;YACX,YAAY;QACd;IACF;IAEA,OAAO,wBACL,MAAc,EACd,MAAc,EACd,QAAgB,EAChB,OAAe,EACf,OAAe,EACf;QACA,OAAO,IAAI,CAAC,kBAAkB,CAAC;YAC7B;YACA,MAAM;YACN,OAAO;YACP,SAAS,CAAC,kBAAkB,EAAE,OAAO,cAAc,GAAG,CAAC,EAAE,aAAa,QAAQ,QAAQ,IAAI,YAAY,EAAE,QAAQ,CAAC,CAAC;YAClH,MAAM;gBAAE;gBAAS;gBAAQ;YAAS;YAClC,MAAM;YACN,UAAU;YACV,UAAU;YACV,WAAW;YACX,YAAY;QACd;IACF;IAEA,0BAA0B;IAC1B,OAAO,wBAAwB,MAAc,EAAE,UAAkB,EAAE,EAAU;QAC3E,MAAM,aAAa,IAAI,KAAK,KAAK,GAAG,KAAM,UAAU,KAAK,KAAK,KAAK;QACnE,MAAM,gBAAgB,oBAAoB,MAAM;QAEhD,IAAK,IAAI,IAAI,oBAAoB,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YACxD,MAAM,eAAe,mBAAmB,CAAC,EAAE;YAC3C,IAAI,aAAa,MAAM,KAAK,UACxB,IAAI,KAAK,aAAa,SAAS,IAAI,YAAY;gBACjD,oBAAoB,MAAM,CAAC,GAAG;YAChC;QACF;QAEA,OAAO,gBAAgB,oBAAoB,MAAM;IACnD;IAEA,4BAA4B;IAC5B,OAAO,aAAa,MAAc,EAAW;QAC3C,MAAM,WAAW,IAAI,CAAC,2BAA2B,CAAC;QAElD,IAAI,CAAC,YAAY,CAAC,SAAS,UAAU,CAAC,OAAO,EAAE;YAC7C,OAAO;QACT;QAEA,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,IAAI,QAAQ,KAAK,KAAK,IAAI,UAAU;QAExD,MAAM,CAAC,WAAW,SAAS,GAAG,SAAS,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;QACvE,MAAM,CAAC,SAAS,OAAO,GAAG,SAAS,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;QAEjE,MAAM,YAAY,YAAY,KAAK;QACnC,MAAM,UAAU,UAAU,KAAK;QAE/B,IAAI,aAAa,SAAS;YACxB,OAAO,eAAe,aAAa,eAAe;QACpD,OAAO;YACL,uCAAuC;YACvC,OAAO,eAAe,aAAa,eAAe;QACpD;IACF;IAEA,+BAA+B;IAC/B,aAAa,gCAAiE;QAC5E,IAAI,gBAAkB,eAAe,CAAC,CAAC,kBAAkB,MAAM,GAAG;YAChE,OAAO;QACT;;IAOF;IAEA,8BAA8B;IAC9B,OAAO,wBAAwB,KAAa,EAAE,OAAe,EAAE,IAAa,EAAE;QAC5E,IAAI,gBAAkB,eAAe,kBAAkB,UAAU,aAAa,UAAU,KAAK,WAAW;;QAMxG;QACA,OAAO;IACT;IAEA,gCAAgC;IAChC,OAAO,iBAAiB,MAAc,EAAE,UAAkB,EAAE,OAAe,EAAE,cAAsB,EAAE;QACnG,MAAM,eAAe,IAAI,CAAC,wBAAwB,CAAC;YACjD;YACA,MAAM;YACN,OAAO;YACP,SAAS,CAAC,eAAe,EAAE,WAAW,MAAM,EAAE,QAAQ,CAAC,CAAC;YACxD,MAAM;gBAAE;gBAAgB;gBAAY;YAAQ;YAC5C,UAAU;YACV,UAAU;YACV,WAAW,CAAC,UAAU,EAAE,gBAAgB;YACxC,YAAY;QACd;QAEA,sBAAsB;QACtB,IAAI,CAAC,uBAAuB,CAC1B,kBACA,CAAC,eAAe,EAAE,YAAY,EAC9B;QAGF,OAAO;IACT;IAEA,0BAA0B;IAC1B,OAAO,qBAAqB,MAAc,EAAE,MAAc,EAAE,gBAAwB,EAAE;QACpF,MAAM,eAAe,IAAI,CAAC,wBAAwB,CAAC;YACjD;YACA,MAAM;YACN,OAAO;YACP,SAAS,CAAC,aAAa,EAAE,OAAO,aAAa,EAAE,iBAAiB,MAAM,CAAC;YACvE,MAAM;gBAAE;gBAAQ;YAAiB;YACjC,UAAU;YACV,UAAU;YACV,WAAW;YACX,YAAY;QACd;QAEA,sBAAsB;QACtB,IAAI,CAAC,uBAAuB,CAC1B,qBACA,CAAC,aAAa,EAAE,OAAO,UAAU,CAAC,EAClC;QAGF,OAAO;IACT;IAEA,+BAA+B;IAC/B,OAAO,qBAAqB,MAAc,EAAE,OAAe,EAAE,MAA2C,EAAE,IAAY,EAAE;QACtH,MAAM,iBAAiB;YACrB,UAAU;YACV,UAAU;YACV,SAAS;QACX;QAEA,MAAM,eAAe,IAAI,CAAC,wBAAwB,CAAC;YACjD;YACA,MAAM,CAAC,GAAG,EAAE,QAAQ;YACpB,OAAO,cAAc,CAAC,OAAO;YAC7B,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;YACjD,MAAM;gBAAE;gBAAM;gBAAQ;YAAQ;YAC9B,UAAU,WAAW,aAAa,WAAW;YAC7C,UAAU;YACV,WAAW,CAAC,KAAK,EAAE,MAAM;YACzB,YAAY;QACd;QAEA,sBAAsB;QACtB,IAAI,CAAC,uBAAuB,CAC1B,cAAc,CAAC,OAAO,EACtB,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EACd,WAAW,aAAa,MAAM,WAAW,aAAa,MAAM;QAG9D,OAAO;IACT;IAEA,+BAA+B;IAC/B,OAAO,eAAe,MAAc,EAAE,WAAmB,EAAE,OAAe,EAAE,SAAiB,EAAE,IAAY,EAAE;QAC3G,MAAM,eAAe,IAAI,CAAC,wBAAwB,CAAC;YACjD;YACA,MAAM;YACN,OAAO;YACP,SAAS,CAAC,eAAe,EAAE,YAAY,QAAQ,EAAE,UAAU,MAAM,EAAE,QAAQ,CAAC,CAAC;YAC7E,MAAM;gBAAE;gBAAa;gBAAS;gBAAW;YAAK;YAC9C,UAAU;YACV,UAAU;YACV,WAAW,CAAC,KAAK,EAAE,KAAK,OAAO,CAAC;YAChC,YAAY;QACd;QAEA,sBAAsB;QACtB,IAAI,CAAC,uBAAuB,CAC1B,eACA,CAAC,UAAU,EAAE,YAAY,QAAQ,EAAE,WAAW,EAC9C;QAGF,OAAO;IACT;IAEA,yBAAyB;IACzB,OAAO,qBAAqB,MAAc,EAAE;QAC1C,MAAM,oBAAoB,oBAAoB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAEvE,OAAO;YACL,OAAO,kBAAkB,MAAM;YAC/B,QAAQ,kBAAkB,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;YACrD,YAAY;gBACV,UAAU,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,YAAY,MAAM;gBACzE,KAAK,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,OAAO,MAAM;gBAC/D,UAAU,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,YAAY,MAAM;gBACzE,QAAQ,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,UAAU,MAAM;gBACrE,SAAS,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,WAAW,MAAM;YACzE;YACA,YAAY;gBACV,QAAQ,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,UAAU,MAAM;gBACrE,MAAM,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,MAAM;gBACjE,QAAQ,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,UAAU,MAAM;gBACrE,KAAK,kBAAkB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,OAAO,MAAM;YACjE;QACF;IACF;AACF"}}, {"offset": {"line": 4118, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4124, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/hooks/useNotifications.ts"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { NotificationService, Notification } from '@/lib/notifications';\n\nexport function useNotifications() {\n  const { user, isAuthenticated } = useAuth();\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [unreadCount, setUnreadCount] = useState(0);\n  const [loading, setLoading] = useState(false);\n  const [hasPermission, setHasPermission] = useState(false);\n\n  // طلب إذن الإشعارات\n  const requestPermission = useCallback(async () => {\n    if (!isAuthenticated) return false;\n    \n    const permission = await NotificationService.requestNotificationPermission();\n    setHasPermission(permission === 'granted');\n    return permission === 'granted';\n  }, [isAuthenticated]);\n\n  // تحميل الإشعارات\n  const loadNotifications = useCallback(async (options: {\n    includeRead?: boolean;\n    category?: string;\n    limit?: number;\n  } = {}) => {\n    if (!user) return;\n\n    setLoading(true);\n    try {\n      const result = NotificationService.getUserNotifications(user.id, {\n        includeRead: options.includeRead ?? true,\n        category: options.category,\n        limit: options.limit ?? 20\n      });\n      \n      setNotifications(result.notifications);\n      \n      // تحديث عدد الإشعارات غير المقروءة\n      const unread = NotificationService.getUnreadCount(user.id);\n      setUnreadCount(unread);\n    } catch (error) {\n      console.error('خطأ في تحميل الإشعارات:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, [user]);\n\n  // تحديد إشعار كمقروء\n  const markAsRead = useCallback((notificationId: string) => {\n    const success = NotificationService.markAsRead(notificationId);\n    if (success && user) {\n      setNotifications(prev => \n        prev.map(n => \n          n.id.toString() === notificationId \n            ? { ...n, read: true, readAt: new Date().toISOString() } \n            : n\n        )\n      );\n      \n      // تحديث العدد\n      const unread = NotificationService.getUnreadCount(user.id);\n      setUnreadCount(unread);\n    }\n    return success;\n  }, [user]);\n\n  // تحديد جميع الإشعارات كمقروءة\n  const markAllAsRead = useCallback(() => {\n    if (!user) return 0;\n    \n    const count = NotificationService.markAllAsRead(user.id);\n    if (count > 0) {\n      setNotifications(prev => \n        prev.map(n => ({ ...n, read: true, readAt: new Date().toISOString() }))\n      );\n      setUnreadCount(0);\n    }\n    return count;\n  }, [user]);\n\n  // حذف إشعار\n  const deleteNotification = useCallback((notificationId: string) => {\n    const success = NotificationService.deleteNotification(notificationId);\n    if (success) {\n      setNotifications(prev => prev.filter(n => n.id.toString() !== notificationId));\n      \n      // تحديث العدد إذا كان الإشعار غير مقروء\n      if (user) {\n        const unread = NotificationService.getUnreadCount(user.id);\n        setUnreadCount(unread);\n      }\n    }\n    return success;\n  }, [user]);\n\n  // إرسال إشعار جديد\n  const sendNotification = useCallback((notification: Omit<Notification, 'id' | 'createdAt' | 'read'>) => {\n    if (!user) return null;\n    \n    const newNotification = NotificationService.sendRealTimeNotification({\n      ...notification,\n      userId: user.id\n    });\n    \n    // إضافة الإشعار للقائمة المحلية\n    setNotifications(prev => [newNotification, ...prev]);\n    setUnreadCount(prev => prev + 1);\n    \n    return newNotification;\n  }, [user]);\n\n  // إشعارات سريعة للأحداث الشائعة\n  const notifyNewMessage = useCallback((senderName: string, adTitle: string, conversationId: number) => {\n    if (!user) return null;\n    return NotificationService.notifyNewMessage(user.id, senderName, adTitle, conversationId);\n  }, [user]);\n\n  const notifyPaymentSuccess = useCallback((amount: number, subscriptionType: string) => {\n    if (!user) return null;\n    return NotificationService.notifyPaymentSuccess(user.id, amount, subscriptionType);\n  }, [user]);\n\n  const notifyAdStatusChange = useCallback((adTitle: string, status: 'approved' | 'rejected' | 'expired', adId: number) => {\n    if (!user) return null;\n    return NotificationService.notifyAdStatusChange(user.id, adTitle, status, adId);\n  }, [user]);\n\n  const notifyNewOffer = useCallback((offerAmount: number, adTitle: string, buyerName: string, adId: number) => {\n    if (!user) return null;\n    return NotificationService.notifyNewOffer(user.id, offerAmount, adTitle, buyerName, adId);\n  }, [user]);\n\n  // جلب إحصائيات الإشعارات\n  const getStats = useCallback(() => {\n    if (!user) return null;\n    return NotificationService.getNotificationStats(user.id);\n  }, [user]);\n\n  // تحميل الإشعارات عند تسجيل الدخول\n  useEffect(() => {\n    if (isAuthenticated && user) {\n      loadNotifications();\n      \n      // التحقق من إذن الإشعارات\n      if (typeof window !== 'undefined' && 'Notification' in window) {\n        setHasPermission(Notification.permission === 'granted');\n      }\n    } else {\n      setNotifications([]);\n      setUnreadCount(0);\n    }\n  }, [isAuthenticated, user, loadNotifications]);\n\n  // تحديث دوري للإشعارات (كل 30 ثانية)\n  useEffect(() => {\n    if (!isAuthenticated || !user) return;\n\n    const interval = setInterval(() => {\n      loadNotifications({ includeRead: false, limit: 10 });\n    }, 30000);\n\n    return () => clearInterval(interval);\n  }, [isAuthenticated, user, loadNotifications]);\n\n  return {\n    notifications,\n    unreadCount,\n    loading,\n    hasPermission,\n    \n    // Actions\n    loadNotifications,\n    markAsRead,\n    markAllAsRead,\n    deleteNotification,\n    sendNotification,\n    requestPermission,\n    \n    // Quick notifications\n    notifyNewMessage,\n    notifyPaymentSuccess,\n    notifyAdStatusChange,\n    notifyNewOffer,\n    \n    // Stats\n    getStats\n  };\n}\n\n// Hook مخصص لإشعارات الرسائل\nexport function useMessageNotifications() {\n  const { notifyNewMessage, unreadCount } = useNotifications();\n  \n  const notifyMessage = useCallback((senderName: string, adTitle: string, conversationId: number) => {\n    return notifyNewMessage(senderName, adTitle, conversationId);\n  }, [notifyNewMessage]);\n\n  return {\n    notifyMessage,\n    unreadCount\n  };\n}\n\n// Hook مخصص لإشعارات المدفوعات\nexport function usePaymentNotifications() {\n  const { notifyPaymentSuccess } = useNotifications();\n  \n  const notifyPayment = useCallback((amount: number, subscriptionType: string) => {\n    return notifyPaymentSuccess(amount, subscriptionType);\n  }, [notifyPaymentSuccess]);\n\n  return {\n    notifyPayment\n  };\n}\n\n// Hook مخصص لإشعارات الإعلانات\nexport function useAdNotifications() {\n  const { notifyAdStatusChange, notifyNewOffer } = useNotifications();\n  \n  const notifyAdStatus = useCallback((adTitle: string, status: 'approved' | 'rejected' | 'expired', adId: number) => {\n    return notifyAdStatusChange(adTitle, status, adId);\n  }, [notifyAdStatusChange]);\n\n  const notifyOffer = useCallback((offerAmount: number, adTitle: string, buyerName: string, adId: number) => {\n    return notifyNewOffer(offerAmount, adTitle, buyerName, adId);\n  }, [notifyNewOffer]);\n\n  return {\n    notifyAdStatus,\n    notifyOffer\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAJA;;;;AAMO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACxC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,oBAAoB;IACpB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,IAAI,CAAC,iBAAiB,OAAO;QAE7B,MAAM,aAAa,MAAM,2HAAA,CAAA,sBAAmB,CAAC,6BAA6B;QAC1E,iBAAiB,eAAe;QAChC,OAAO,eAAe;IACxB,GAAG;QAAC;KAAgB;IAEpB,kBAAkB;IAClB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,UAIzC,CAAC,CAAC;QACJ,IAAI,CAAC,MAAM;QAEX,WAAW;QACX,IAAI;YACF,MAAM,SAAS,2HAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC,KAAK,EAAE,EAAE;gBAC/D,aAAa,QAAQ,WAAW,IAAI;gBACpC,UAAU,QAAQ,QAAQ;gBAC1B,OAAO,QAAQ,KAAK,IAAI;YAC1B;YAEA,iBAAiB,OAAO,aAAa;YAErC,mCAAmC;YACnC,MAAM,SAAS,2HAAA,CAAA,sBAAmB,CAAC,cAAc,CAAC,KAAK,EAAE;YACzD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAK;IAET,qBAAqB;IACrB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,MAAM,UAAU,2HAAA,CAAA,sBAAmB,CAAC,UAAU,CAAC;QAC/C,IAAI,WAAW,MAAM;YACnB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IACP,EAAE,EAAE,CAAC,QAAQ,OAAO,iBAChB;wBAAE,GAAG,CAAC;wBAAE,MAAM;wBAAM,QAAQ,IAAI,OAAO,WAAW;oBAAG,IACrD;YAIR,cAAc;YACd,MAAM,SAAS,2HAAA,CAAA,sBAAmB,CAAC,cAAc,CAAC,KAAK,EAAE;YACzD,eAAe;QACjB;QACA,OAAO;IACT,GAAG;QAAC;KAAK;IAET,+BAA+B;IAC/B,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,QAAQ,2HAAA,CAAA,sBAAmB,CAAC,aAAa,CAAC,KAAK,EAAE;QACvD,IAAI,QAAQ,GAAG;YACb,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC;wBAAE,GAAG,CAAC;wBAAE,MAAM;wBAAM,QAAQ,IAAI,OAAO,WAAW;oBAAG,CAAC;YAEvE,eAAe;QACjB;QACA,OAAO;IACT,GAAG;QAAC;KAAK;IAET,YAAY;IACZ,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,MAAM,UAAU,2HAAA,CAAA,sBAAmB,CAAC,kBAAkB,CAAC;QACvD,IAAI,SAAS;YACX,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,CAAC,QAAQ,OAAO;YAE9D,wCAAwC;YACxC,IAAI,MAAM;gBACR,MAAM,SAAS,2HAAA,CAAA,sBAAmB,CAAC,cAAc,CAAC,KAAK,EAAE;gBACzD,eAAe;YACjB;QACF;QACA,OAAO;IACT,GAAG;QAAC;KAAK;IAET,mBAAmB;IACnB,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,kBAAkB,2HAAA,CAAA,sBAAmB,CAAC,wBAAwB,CAAC;YACnE,GAAG,YAAY;YACf,QAAQ,KAAK,EAAE;QACjB;QAEA,gCAAgC;QAChC,iBAAiB,CAAA,OAAQ;gBAAC;mBAAoB;aAAK;QACnD,eAAe,CAAA,OAAQ,OAAO;QAE9B,OAAO;IACT,GAAG;QAAC;KAAK;IAET,gCAAgC;IAChC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,YAAoB,SAAiB;QACzE,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,2HAAA,CAAA,sBAAmB,CAAC,gBAAgB,CAAC,KAAK,EAAE,EAAE,YAAY,SAAS;IAC5E,GAAG;QAAC;KAAK;IAET,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAgB;QACxD,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,2HAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC,KAAK,EAAE,EAAE,QAAQ;IACnE,GAAG;QAAC;KAAK;IAET,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,SAAiB,QAA6C;QACtG,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,2HAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC,KAAK,EAAE,EAAE,SAAS,QAAQ;IAC5E,GAAG;QAAC;KAAK;IAET,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,aAAqB,SAAiB,WAAmB;QAC3F,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,2HAAA,CAAA,sBAAmB,CAAC,cAAc,CAAC,KAAK,EAAE,EAAE,aAAa,SAAS,WAAW;IACtF,GAAG;QAAC;KAAK;IAET,yBAAyB;IACzB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC3B,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,2HAAA,CAAA,sBAAmB,CAAC,oBAAoB,CAAC,KAAK,EAAE;IACzD,GAAG;QAAC;KAAK;IAET,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB,MAAM;YAC3B;YAEA,0BAA0B;YAC1B,IAAI,gBAAkB,eAAe,kBAAkB,QAAQ;;YAE/D;QACF,OAAO;YACL,iBAAiB,EAAE;YACnB,eAAe;QACjB;IACF,GAAG;QAAC;QAAiB;QAAM;KAAkB;IAE7C,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,mBAAmB,CAAC,MAAM;QAE/B,MAAM,WAAW,YAAY;YAC3B,kBAAkB;gBAAE,aAAa;gBAAO,OAAO;YAAG;QACpD,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;QAAiB;QAAM;KAAkB;IAE7C,OAAO;QACL;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QACA;QAEA,sBAAsB;QACtB;QACA;QACA;QACA;QAEA,QAAQ;QACR;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,gBAAgB,EAAE,WAAW,EAAE,GAAG;IAE1C,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,YAAoB,SAAiB;QACtE,OAAO,iBAAiB,YAAY,SAAS;IAC/C,GAAG;QAAC;KAAiB;IAErB,OAAO;QACL;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,oBAAoB,EAAE,GAAG;IAEjC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAgB;QACjD,OAAO,qBAAqB,QAAQ;IACtC,GAAG;QAAC;KAAqB;IAEzB,OAAO;QACL;IACF;AACF;AAGO,SAAS;IACd,MAAM,EAAE,oBAAoB,EAAE,cAAc,EAAE,GAAG;IAEjD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,SAAiB,QAA6C;QAChG,OAAO,qBAAqB,SAAS,QAAQ;IAC/C,GAAG;QAAC;KAAqB;IAEzB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,aAAqB,SAAiB,WAAmB;QACxF,OAAO,eAAe,aAAa,SAAS,WAAW;IACzD,GAAG;QAAC;KAAe;IAEnB,OAAO;QACL;QACA;IACF;AACF"}}, {"offset": {"line": 4364, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4370, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/NotificationCenter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { NotificationService, Notification } from '@/lib/notifications';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { toast } from 'react-hot-toast';\n\ninterface Notification {\n  id: number;\n  type: 'message' | 'ad_expired' | 'ad_approved' | 'payment' | 'system';\n  title: string;\n  message: string;\n  time: string;\n  read: boolean;\n  actionUrl?: string;\n}\n\nconst NotificationCenter = () => {\n  const [notifications, setNotifications] = useState<Notification[]>([\n    {\n      id: 1,\n      type: 'message',\n      title: 'رسالة جديدة',\n      message: 'لديك رسالة جديدة من محمد أحمد بخصوص إعلان \"شقة للبيع في دمشق\"',\n      time: 'منذ 5 دقائق',\n      read: false,\n      actionUrl: '/dashboard?tab=messages'\n    },\n    {\n      id: 2,\n      type: 'ad_approved',\n      title: 'تم قبول إعلانك',\n      message: 'تم قبول ونشر إعلان \"سيارة BMW للبيع\" وهو الآن متاح للمشاهدة',\n      time: 'منذ ساعة',\n      read: false,\n      actionUrl: '/ad/123'\n    },\n    {\n      id: 3,\n      type: 'ad_expired',\n      title: 'انتهاء صلاحية إعلان',\n      message: 'انتهت صلاحية إعلان \"لابتوب Dell Gaming\". يمكنك تجديده الآن',\n      time: 'منذ 3 ساعات',\n      read: true,\n      actionUrl: '/dashboard?tab=ads'\n    },\n    {\n      id: 4,\n      type: 'payment',\n      title: 'تم الدفع بنجاح',\n      message: 'تم تأكيد دفع اشتراك الخطة المميزة. مميزاتك الجديدة متاحة الآن',\n      time: 'منذ يوم',\n      read: true,\n      actionUrl: '/dashboard'\n    },\n    {\n      id: 5,\n      type: 'system',\n      title: 'تحديث الموقع',\n      message: 'تم إضافة ميزات جديدة للبحث المتقدم. اكتشف المزيد الآن',\n      time: 'منذ يومين',\n      read: true,\n      actionUrl: '/ads'\n    }\n  ]);\n\n  const [filter, setFilter] = useState<'all' | 'unread'>('all');\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'message': return '💬';\n      case 'ad_expired': return '⏰';\n      case 'ad_approved': return '✅';\n      case 'payment': return '💳';\n      case 'system': return '🔔';\n      default: return '📢';\n    }\n  };\n\n  const getNotificationColor = (type: string) => {\n    switch (type) {\n      case 'message': return 'bg-blue-100 border-blue-200';\n      case 'ad_expired': return 'bg-orange-100 border-orange-200';\n      case 'ad_approved': return 'bg-green-100 border-green-200';\n      case 'payment': return 'bg-purple-100 border-purple-200';\n      case 'system': return 'bg-gray-100 border-gray-200';\n      default: return 'bg-gray-100 border-gray-200';\n    }\n  };\n\n  const markAsRead = (id: number) => {\n    setNotifications(prev =>\n      prev.map(notif =>\n        notif.id === id ? { ...notif, read: true } : notif\n      )\n    );\n  };\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(notif => ({ ...notif, read: true }))\n    );\n  };\n\n  const deleteNotification = (id: number) => {\n    setNotifications(prev => prev.filter(notif => notif.id !== id));\n  };\n\n  const filteredNotifications = filter === 'unread' \n    ? notifications.filter(n => !n.read)\n    : notifications;\n\n  const unreadCount = notifications.filter(n => !n.read).length;\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg\">\n      {/* Header */}\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-xl font-bold text-gray-800 flex items-center gap-2\">\n            🔔 الإشعارات\n            {unreadCount > 0 && (\n              <span className=\"bg-red-500 text-white text-xs px-2 py-1 rounded-full\">\n                {unreadCount}\n              </span>\n            )}\n          </h2>\n          {unreadCount > 0 && (\n            <button\n              onClick={markAllAsRead}\n              className=\"text-sm text-primary-600 hover:text-primary-700 font-medium\"\n            >\n              تحديد الكل كمقروء\n            </button>\n          )}\n        </div>\n\n        {/* Filters */}\n        <div className=\"flex gap-2\">\n          <button\n            onClick={() => setFilter('all')}\n            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\n              filter === 'all'\n                ? 'bg-primary-600 text-white'\n                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n            }`}\n          >\n            الكل ({notifications.length})\n          </button>\n          <button\n            onClick={() => setFilter('unread')}\n            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\n              filter === 'unread'\n                ? 'bg-primary-600 text-white'\n                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n            }`}\n          >\n            غير مقروءة ({unreadCount})\n          </button>\n        </div>\n      </div>\n\n      {/* Notifications List */}\n      <div className=\"max-h-96 overflow-y-auto\">\n        {filteredNotifications.length > 0 ? (\n          <div className=\"divide-y divide-gray-100\">\n            {filteredNotifications.map((notification) => (\n              <div\n                key={notification.id}\n                className={`p-4 hover:bg-gray-50 transition-colors ${\n                  !notification.read ? 'bg-blue-50' : ''\n                }`}\n              >\n                <div className=\"flex items-start gap-4\">\n                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-lg ${getNotificationColor(notification.type)}`}>\n                    {getNotificationIcon(notification.type)}\n                  </div>\n                  \n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <h3 className={`font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>\n                          {notification.title}\n                        </h3>\n                        <p className=\"text-sm text-gray-600 mt-1 line-clamp-2\">\n                          {notification.message}\n                        </p>\n                        <div className=\"flex items-center gap-4 mt-2\">\n                          <span className=\"text-xs text-gray-500\">{notification.time}</span>\n                          {!notification.read && (\n                            <span className=\"w-2 h-2 bg-blue-500 rounded-full\"></span>\n                          )}\n                        </div>\n                      </div>\n                      \n                      <div className=\"flex items-center gap-2 ml-4\">\n                        {!notification.read && (\n                          <button\n                            onClick={() => markAsRead(notification.id)}\n                            className=\"text-xs text-blue-600 hover:text-blue-700\"\n                            title=\"تحديد كمقروء\"\n                          >\n                            ✓\n                          </button>\n                        )}\n                        <button\n                          onClick={() => deleteNotification(notification.id)}\n                          className=\"text-xs text-red-600 hover:text-red-700\"\n                          title=\"حذف\"\n                        >\n                          ×\n                        </button>\n                      </div>\n                    </div>\n                    \n                    {notification.actionUrl && (\n                      <div className=\"mt-3\">\n                        <a\n                          href={notification.actionUrl}\n                          className=\"text-sm text-primary-600 hover:text-primary-700 font-medium\"\n                        >\n                          عرض التفاصيل ←\n                        </a>\n                      </div>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"p-8 text-center\">\n            <div className=\"text-4xl mb-4\">🔔</div>\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n              {filter === 'unread' ? 'لا توجد إشعارات غير مقروءة' : 'لا توجد إشعارات'}\n            </h3>\n            <p className=\"text-gray-600\">\n              {filter === 'unread' \n                ? 'جميع إشعاراتك مقروءة' \n                : 'ستظهر إشعاراتك هنا عند وصولها'\n              }\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Footer */}\n      {filteredNotifications.length > 0 && (\n        <div className=\"p-4 border-t border-gray-200 bg-gray-50\">\n          <div className=\"flex items-center justify-between text-sm\">\n            <span className=\"text-gray-600\">\n              {filteredNotifications.length} إشعار\n            </span>\n            <button className=\"text-primary-600 hover:text-primary-700 font-medium\">\n              إعدادات الإشعارات\n            </button>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default NotificationCenter;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAiBA,MAAM,qBAAqB;IACzB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACjE;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW;QACb;QACA;YACE,IAAI;YAC<PERSON>,MAAM;YACN,OAAO;YACP,SAAS;YAC<PERSON>,MAAM;YACN,MAAM;YACN,WAAW;QACb;KACD;IAED,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAEvD,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QACP,MAAM,EAAE,KAAK,KAAK;oBAAE,GAAG,KAAK;oBAAE,MAAM;gBAAK,IAAI;IAGnD;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,QAAS,CAAC;oBAAE,GAAG,KAAK;oBAAE,MAAM;gBAAK,CAAC;IAE/C;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC7D;IAEA,MAAM,wBAAwB,WAAW,WACrC,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,IACjC;IAEJ,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;IAE7D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA0D;oCAErE,cAAc,mBACb,8OAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;4BAIN,cAAc,mBACb,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,QACP,8BACA,+CACJ;;oCACH;oCACQ,cAAc,MAAM;oCAAC;;;;;;;0CAE9B,8OAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAW,CAAC,2DAA2D,EACrE,WAAW,WACP,8BACA,+CACJ;;oCACH;oCACc;oCAAY;;;;;;;;;;;;;;;;;;;0BAM/B,8OAAC;gBAAI,WAAU;0BACZ,sBAAsB,MAAM,GAAG,kBAC9B,8OAAC;oBAAI,WAAU;8BACZ,sBAAsB,GAAG,CAAC,CAAC,6BAC1B,8OAAC;4BAEC,WAAW,CAAC,uCAAuC,EACjD,CAAC,aAAa,IAAI,GAAG,eAAe,IACpC;sCAEF,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,gEAAgE,EAAE,qBAAqB,aAAa,IAAI,GAAG;kDACzH,oBAAoB,aAAa,IAAI;;;;;;kDAGxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAW,CAAC,YAAY,EAAE,CAAC,aAAa,IAAI,GAAG,kBAAkB,iBAAiB;0EACnF,aAAa,KAAK;;;;;;0EAErB,8OAAC;gEAAE,WAAU;0EACV,aAAa,OAAO;;;;;;0EAEvB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAyB,aAAa,IAAI;;;;;;oEACzD,CAAC,aAAa,IAAI,kBACjB,8OAAC;wEAAK,WAAU;;;;;;;;;;;;;;;;;;kEAKtB,8OAAC;wDAAI,WAAU;;4DACZ,CAAC,aAAa,IAAI,kBACjB,8OAAC;gEACC,SAAS,IAAM,WAAW,aAAa,EAAE;gEACzC,WAAU;gEACV,OAAM;0EACP;;;;;;0EAIH,8OAAC;gEACC,SAAS,IAAM,mBAAmB,aAAa,EAAE;gEACjD,WAAU;gEACV,OAAM;0EACP;;;;;;;;;;;;;;;;;;4CAMJ,aAAa,SAAS,kBACrB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,MAAM,aAAa,SAAS;oDAC5B,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;2BApDJ,aAAa,EAAE;;;;;;;;;yCA+D1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCACX,WAAW,WAAW,+BAA+B;;;;;;sCAExD,8OAAC;4BAAE,WAAU;sCACV,WAAW,WACR,yBACA;;;;;;;;;;;;;;;;;YAQX,sBAAsB,MAAM,GAAG,mBAC9B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;;gCACb,sBAAsB,MAAM;gCAAC;;;;;;;sCAEhC,8OAAC;4BAAO,WAAU;sCAAsD;;;;;;;;;;;;;;;;;;;;;;;AAQpF;uCAEe"}}, {"offset": {"line": 4784, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4790, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport AuthModal from './AuthModal';\nimport MyCvLogo from './MyCvLogo';\nimport Logo from './Logo';\nimport SafeNavigationButton from './SafeNavigationButton';\nimport ClientOnlyNotifications from './ClientOnlyNotifications';\nimport ClientOnlyAccount from './ClientOnlyAccount';\nimport { useToast } from '@/components/ToastManager';\nimport { useNotifications } from '@/hooks/useNotifications';\nimport NotificationCenter from './NotificationCenter';\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);\n  const [isLoggedIn, setIsLoggedIn] = useState(false);\n  const [currentUser, setCurrentUser] = useState<any>(null);\n  const [showNotifications, setShowNotifications] = useState(false);\n  const toast = useToast();\n  const { unreadCount, requestPermission } = useNotifications();\n\n  // التحقق من حالة تسجيل الدخول عند تحميل المكون\n  useEffect(() => {\n    const checkLoginStatus = () => {\n      const loginStatus = localStorage.getItem('isLoggedIn');\n      const userData = localStorage.getItem('currentUser');\n\n      if (loginStatus === 'true' && userData) {\n        setIsLoggedIn(true);\n        setCurrentUser(JSON.parse(userData));\n      }\n    };\n\n    checkLoginStatus();\n  }, []);\n\n  // دالة تسجيل الخروج المحسنة\n  const handleLogout = () => {\n    // إظهار رسالة تسجيل الخروج\n    toast.showLogout();\n\n    // إزالة جميع بيانات المستخدم\n    localStorage.removeItem('isLoggedIn');\n    localStorage.removeItem('currentUser');\n    localStorage.removeItem('userSession');\n    localStorage.removeItem('userSettings');\n    localStorage.removeItem('userPreferences');\n\n    // تحديث الحالة المحلية\n    setIsLoggedIn(false);\n    setCurrentUser(null);\n\n    // التحقق من الصفحة الحالية وإعادة التوجيه\n    const currentPath = window.location.pathname;\n    const protectedPaths = ['/settings', '/profile', '/dashboard', '/add-ad', '/my-ads', '/favorites', '/messages'];\n    const isOnProtectedPage = protectedPaths.some(path => currentPath.startsWith(path));\n\n    // إعادة توجيه بعد تأخير قصير\n    setTimeout(() => {\n      if (isOnProtectedPage) {\n        // إعادة توجيه للصفحة الرئيسية إذا كان في صفحة محمية\n        window.location.href = '/';\n      } else {\n        // إعادة تحميل الصفحة الحالية إذا لم تكن محمية\n        window.location.reload();\n      }\n    }, 1500);\n  };\n  const [authModalTab, setAuthModalTab] = useState<'login' | 'register'>('login');\n\n  return (\n    <header className=\"bg-white shadow-lg border-b border-primary-100\">\n\n\n      {/* Main Header */}\n      <div className=\"container mx-auto px-4 py-2\">\n        <div className=\"flex items-center justify-between\">\n          {/* Logo with Syria Badge - Mobile Optimized */}\n          <div className=\"flex items-center gap-2\">\n            <Logo variant=\"transparent\" size={isMenuOpen ? \"lg\" : \"xl\"} showText={true} href=\"/\" />\n            <div className=\"bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold\">\n              سوريا\n            </div>\n          </div>\n\n          {/* Desktop Auth Buttons */}\n          <div className=\"hidden md:flex items-center gap-0.5\">\n            {!isLoggedIn ? (\n              // للمستخدمين غير المسجلين\n              <>\n                {/* الوظائف */}\n                <Link\n                  href=\"/jobs\"\n                  className=\"flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors font-medium\"\n                  title=\"الوظائف والسير الذاتية - مدعوم من قبل تطبيق MyCv\"\n                >\n                  <MyCvLogo size=\"xs\" variant=\"square\" />\n                  <span>الوظائف</span>\n                </Link>\n\n                {/* أضف إعلانك - ينقل لتسجيل الدخول */}\n                <button\n                  onClick={() => {\n                    setAuthModalTab('login');\n                    setIsAuthModalOpen(true);\n                  }}\n                  className=\"px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-semibold\"\n                >\n                  أضف إعلانك\n                </button>\n\n                {/* تسجيل الدخول */}\n                <button\n                  onClick={() => {\n                    setAuthModalTab('login');\n                    setIsAuthModalOpen(true);\n                  }}\n                  className=\"px-4 py-2 text-primary-600 border border-primary-600 rounded-lg hover:bg-primary-50 transition-colors\"\n                >\n                  تسجيل الدخول\n                </button>\n\n                {/* إنشاء حساب */}\n                <button\n                  onClick={() => {\n                    setAuthModalTab('register');\n                    setIsAuthModalOpen(true);\n                  }}\n                  className=\"px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors\"\n                >\n                  إنشاء حساب\n                </button>\n              </>\n            ) : (\n              // للمستخدمين المسجلين\n              <>\n                {/* الوظائف */}\n                <Link\n                  href=\"/jobs\"\n                  className=\"flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors font-medium\"\n                  title=\"الوظائف والسير الذاتية - مدعوم من قبل تطبيق MyCv\"\n                >\n                  <MyCvLogo size=\"xs\" variant=\"square\" />\n                  <span>الوظائف</span>\n                </Link>\n\n                {/* أضف إعلانك */}\n                <SafeNavigationButton\n                  href=\"/add-ad\"\n                  className=\"px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-semibold\"\n                >\n                  أضف إعلانك\n                </SafeNavigationButton>\n\n                {/* المفضلة */}\n                <Link\n                  href=\"/favorites\"\n                  className=\"relative p-3 text-gray-600 hover:text-green-600 rounded-full hover:bg-gray-100 transition-all duration-300 group\"\n                  title=\"المفضلة\"\n                >\n                  <svg\n                    className=\"w-6 h-6 transition-all duration-300 group-hover:scale-110\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                    style={{\n                      filter: 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))';\n                      e.currentTarget.style.color = '#22c55e';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))';\n                      e.currentTarget.style.color = '';\n                    }}\n                  >\n                    <path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"/>\n                  </svg>\n                </Link>\n\n                {/* الرسائل */}\n                <Link\n                  href=\"/messages\"\n                  className=\"relative p-3 text-gray-600 hover:text-blue-600 rounded-full hover:bg-gray-100 transition-all duration-300 group\"\n                  title=\"الرسائل\"\n                >\n                  <svg\n                    className=\"w-6 h-6 transition-all duration-300 group-hover:scale-110\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                    style={{\n                      filter: 'drop-shadow(0 0 0px rgba(59, 130, 246, 0.6))',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.8))';\n                      e.currentTarget.style.color = '#3b82f6';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(59, 130, 246, 0.6))';\n                      e.currentTarget.style.color = '';\n                    }}\n                  >\n                    <path d=\"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z\"/>\n                  </svg>\n\n                  {/* عداد الرسائل غير المقروءة */}\n                  <span className=\"absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold\">\n                    2\n                  </span>\n                </Link>\n\n                {/* الإشعارات */}\n                <button\n                  onClick={() => setShowNotifications(true)}\n                  className=\"relative p-2 text-gray-600 hover:text-yellow-600 rounded-full hover:bg-gray-100 transition-all duration-300\"\n                  title=\"الإشعارات\"\n                >\n                  <svg\n                    className=\"w-5 h-5\"\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                    style={{\n                      filter: 'drop-shadow(0 0 6px rgba(245, 158, 11, 0.6))',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 12px rgba(245, 158, 11, 0.8))';\n                      e.currentTarget.style.color = '#f59e0b';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.filter = 'drop-shadow(0 0 6px rgba(245, 158, 11, 0.6))';\n                      e.currentTarget.style.color = '';\n                    }}\n                  >\n                    <path d=\"M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z\"/>\n                  </svg>\n\n                  {/* عداد الإشعارات غير المقروءة */}\n                  {unreadCount > 0 && (\n                    <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold\">\n                      {unreadCount > 99 ? '99+' : unreadCount}\n                    </span>\n                  )}\n                </button>\n\n                {/* حسابي */}\n                <ClientOnlyAccount />\n\n                {/* تسجيل الخروج */}\n                <button\n                  onClick={handleLogout}\n                  className=\"text-red-600 hover:text-red-700 px-4 py-2 rounded-lg hover:bg-red-50 transition-colors\"\n                  title=\"تسجيل الخروج\"\n                >\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d=\"M16 17v-3H9v-4h7V7l5 5-5 5M14 2a2 2 0 012 2v2h-2V4H4v16h10v-2h2v2a2 2 0 01-2 2H4a2 2 0 01-2-2V4a2 2 0 012-2h10z\"/>\n                  </svg>\n                </button>\n              </>\n            )}\n          </div>\n\n          {/* Mobile Right Side */}\n          <div className=\"md:hidden flex items-center gap-2\">\n            {/* المفضلة للموبايل */}\n            <Link\n              href=\"/favorites\"\n              className=\"relative p-2 text-gray-600 hover:text-green-600 rounded-full hover:bg-gray-100 transition-all duration-300\"\n              title=\"المفضلة\"\n            >\n              <svg\n                className=\"w-5 h-5\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path d=\"M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z\"/>\n              </svg>\n            </Link>\n\n            {/* الرسائل للموبايل */}\n            <Link\n              href=\"/messages\"\n              className=\"relative p-2 text-gray-600 hover:text-blue-600 rounded-full hover:bg-gray-100 transition-all duration-300\"\n              title=\"الرسائل\"\n            >\n              <svg\n                className=\"w-5 h-5\"\n                fill=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path d=\"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z\"/>\n              </svg>\n\n              {/* عداد الرسائل غير المقروءة للموبايل */}\n              <span className=\"absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-bold text-[10px]\">\n                2\n              </span>\n            </Link>\n\n            {/* الإشعارات للموبايل */}\n            <ClientOnlyNotifications />\n\n            {/* حسابي للموبايل */}\n            <ClientOnlyAccount />\n\n            {/* زر إضافة إعلان للموبايل */}\n            <SafeNavigationButton\n              href=\"/add-ad\"\n              className=\"px-3 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors font-medium text-sm\"\n            >\n              + إعلان\n            </SafeNavigationButton>\n\n            {/* Mobile Menu Button */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\"\n            >\n              <span className=\"text-xl\">{isMenuOpen ? '✕' : '☰'}</span>\n            </button>\n          </div>\n        </div>\n\n        {/* Desktop Navigation */}\n        <nav className=\"hidden md:block mt-4 border-t pt-4\">\n          <ul className=\"flex flex-wrap gap-6 text-gray-700\">\n            <li><SafeNavigationButton href=\"/\" className=\"hover:text-primary-600 font-medium bg-transparent border-0 p-0\">الرئيسية</SafeNavigationButton></li>\n            <li><SafeNavigationButton href=\"/ads\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">جميع الإعلانات</SafeNavigationButton></li>\n            <li><SafeNavigationButton href=\"/jobs\" className=\"hover:text-primary-600 font-medium flex items-center gap-1 bg-transparent border-0 p-0\">\n              <MyCvLogo size=\"xs\" variant=\"square\" />\n              <span>الوظائف</span>\n            </SafeNavigationButton></li>\n            {isLoggedIn && (\n              <>\n                <li><SafeNavigationButton href=\"/notifications\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">الإشعارات</SafeNavigationButton></li>\n                <li><SafeNavigationButton href=\"/map\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">الخريطة</SafeNavigationButton></li>\n                <li><SafeNavigationButton href=\"/compare\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">المقارنة</SafeNavigationButton></li>\n              </>\n            )}\n            <li><SafeNavigationButton href=\"/category/real-estate\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">العقارات</SafeNavigationButton></li>\n            <li><SafeNavigationButton href=\"/category/cars\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">السيارات</SafeNavigationButton></li>\n            <li><SafeNavigationButton href=\"/category/electronics\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">الإلكترونيات</SafeNavigationButton></li>\n            <li><SafeNavigationButton href=\"/category/services\" className=\"hover:text-primary-600 bg-transparent border-0 p-0\">الخدمات</SafeNavigationButton></li>\n            <li><SafeNavigationButton href=\"/subscription\" className=\"hover:text-primary-600 font-medium bg-transparent border-0 p-0\">💎 الاشتراكات</SafeNavigationButton></li>\n          </ul>\n        </nav>\n      </div>\n\n      {/* Mobile Menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden bg-white border-t shadow-lg\">\n          <div className=\"container mx-auto px-4 py-4 max-h-screen overflow-y-auto\">\n            {/* أزرار المصادقة للموبايل - تظهر فقط للمستخدمين غير المسجلين */}\n            {!isLoggedIn && (\n              <div className=\"flex flex-col gap-3 mb-6\">\n                <button\n                  onClick={() => {\n                    setAuthModalTab('login');\n                    setIsAuthModalOpen(true);\n                    setIsMenuOpen(false);\n                  }}\n                  className=\"w-full px-4 py-3 text-primary-600 border border-primary-600 rounded-lg hover:bg-primary-50 transition-colors font-medium\"\n                >\n                  تسجيل الدخول\n                </button>\n                <button\n                  onClick={() => {\n                    setAuthModalTab('register');\n                    setIsAuthModalOpen(true);\n                    setIsMenuOpen(false);\n                  }}\n                  className=\"w-full px-4 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium\"\n                >\n                  إنشاء حساب جديد\n                </button>\n              </div>\n            )}\n\n            {/* روابط التنقل الرئيسية */}\n            <div className=\"mb-6\">\n              <h3 className=\"font-semibold text-gray-800 mb-4 text-lg\">التنقل السريع</h3>\n              <div className=\"grid grid-cols-2 gap-3\">\n                <SafeNavigationButton\n                  href=\"/\"\n                  className=\"p-4 text-center bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors border border-gray-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">🏠</div>\n                  <div className=\"text-sm font-medium\">الرئيسية</div>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/ads\"\n                  className=\"p-4 text-center bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors border border-blue-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">📋</div>\n                  <div className=\"text-sm font-medium text-blue-700\">جميع الإعلانات</div>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/jobs\"\n                  className=\"p-4 text-center bg-green-50 rounded-xl hover:bg-green-100 transition-colors border border-green-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"flex justify-center mb-2\">\n                    <MyCvLogo size=\"sm\" variant=\"square\" />\n                  </div>\n                  <div className=\"text-sm font-medium text-green-700\">الوظائف</div>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/map\"\n                  className=\"p-4 text-center bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors border border-gray-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">🗺️</div>\n                  <div className=\"text-sm font-medium\">الخريطة</div>\n                </SafeNavigationButton>\n              </div>\n            </div>\n\n            {/* التصنيفات الرئيسية */}\n            <div className=\"mb-6\">\n              <h3 className=\"font-semibold text-gray-800 mb-4 text-lg\">التصنيفات الرئيسية</h3>\n              <div className=\"grid grid-cols-2 gap-3\">\n                <SafeNavigationButton\n                  href=\"/category/real-estate\"\n                  className=\"p-4 text-center bg-blue-50 rounded-xl hover:bg-blue-100 transition-colors border border-blue-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">🏘️</div>\n                  <div className=\"text-sm font-medium text-blue-700\">العقارات</div>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/category/cars\"\n                  className=\"p-4 text-center bg-red-50 rounded-xl hover:bg-red-100 transition-colors border border-red-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">🚗</div>\n                  <div className=\"text-sm font-medium text-red-700\">السيارات</div>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/category/electronics\"\n                  className=\"p-4 text-center bg-purple-50 rounded-xl hover:bg-purple-100 transition-colors border border-purple-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">📱</div>\n                  <div className=\"text-sm font-medium text-purple-700\">الإلكترونيات</div>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/category/services\"\n                  className=\"p-4 text-center bg-orange-50 rounded-xl hover:bg-orange-100 transition-colors border border-orange-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">🛠️</div>\n                  <div className=\"text-sm font-medium text-orange-700\">الخدمات</div>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/subscription\"\n                  className=\"p-4 text-center bg-yellow-50 rounded-xl hover:bg-yellow-100 transition-colors border border-yellow-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <div className=\"text-2xl mb-2\">💎</div>\n                  <div className=\"text-sm font-medium text-yellow-700\">الاشتراكات</div>\n                </SafeNavigationButton>\n              </div>\n            </div>\n\n            {/* روابط إضافية */}\n            <div className=\"border-t border-gray-200 pt-4\">\n              <div className=\"flex flex-col gap-2\">\n                <SafeNavigationButton\n                  href=\"/notifications\"\n                  className=\"flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <span className=\"text-xl\">🔔</span>\n                  <span className=\"font-medium\">الإشعارات</span>\n                </SafeNavigationButton>\n\n                <SafeNavigationButton\n                  href=\"/compare\"\n                  className=\"flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  <span className=\"text-xl\">⚖️</span>\n                  <span className=\"font-medium\">المقارنة</span>\n                </SafeNavigationButton>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Auth Modal */}\n      <AuthModal\n        isOpen={isAuthModalOpen}\n        onClose={() => setIsAuthModalOpen(false)}\n        defaultTab={authModalTab}\n      />\n\n      {/* Notification Center */}\n      <NotificationCenter\n        isOpen={showNotifications}\n        onClose={() => setShowNotifications(false)}\n      />\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAcA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,QAAQ,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IACrB,MAAM,EAAE,WAAW,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD;IAE1D,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,MAAM,WAAW,aAAa,OAAO,CAAC;YAEtC,IAAI,gBAAgB,UAAU,UAAU;gBACtC,cAAc;gBACd,eAAe,KAAK,KAAK,CAAC;YAC5B;QACF;QAEA;IACF,GAAG,EAAE;IAEL,4BAA4B;IAC5B,MAAM,eAAe;QACnB,2BAA2B;QAC3B,MAAM,UAAU;QAEhB,6BAA6B;QAC7B,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,uBAAuB;QACvB,cAAc;QACd,eAAe;QAEf,0CAA0C;QAC1C,MAAM,cAAc,OAAO,QAAQ,CAAC,QAAQ;QAC5C,MAAM,iBAAiB;YAAC;YAAa;YAAY;YAAc;YAAW;YAAW;YAAc;SAAY;QAC/G,MAAM,oBAAoB,eAAe,IAAI,CAAC,CAAA,OAAQ,YAAY,UAAU,CAAC;QAE7E,6BAA6B;QAC7B,WAAW;YACT,IAAI,mBAAmB;gBACrB,oDAAoD;gBACpD,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO;gBACL,8CAA8C;gBAC9C,OAAO,QAAQ,CAAC,MAAM;YACxB;QACF,GAAG;IACL;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAEvE,qBACE,8OAAC;QAAO,WAAU;;0BAIhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,UAAI;wCAAC,SAAQ;wCAAc,MAAM,aAAa,OAAO;wCAAM,UAAU;wCAAM,MAAK;;;;;;kDACjF,8OAAC;wCAAI,WAAU;kDAA+D;;;;;;;;;;;;0CAMhF,8OAAC;gCAAI,WAAU;0CACZ,CAAC,aACA,0BAA0B;8CAC1B;;sDAEE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,OAAM;;8DAEN,8OAAC,8HAAA,CAAA,UAAQ;oDAAC,MAAK;oDAAK,SAAQ;;;;;;8DAC5B,8OAAC;8DAAK;;;;;;;;;;;;sDAIR,8OAAC;4CACC,SAAS;gDACP,gBAAgB;gDAChB,mBAAmB;4CACrB;4CACA,WAAU;sDACX;;;;;;sDAKD,8OAAC;4CACC,SAAS;gDACP,gBAAgB;gDAChB,mBAAmB;4CACrB;4CACA,WAAU;sDACX;;;;;;sDAKD,8OAAC;4CACC,SAAS;gDACP,gBAAgB;gDAChB,mBAAmB;4CACrB;4CACA,WAAU;sDACX;;;;;;;mDAKH,sBAAsB;8CACtB;;sDAEE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,OAAM;;8DAEN,8OAAC,8HAAA,CAAA,UAAQ;oDAAC,MAAK;oDAAK,SAAQ;;;;;;8DAC5B,8OAAC;8DAAK;;;;;;;;;;;;sDAIR,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;sDACX;;;;;;sDAKD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC;gDACC,WAAU;gDACV,MAAK;gDACL,SAAQ;gDACR,OAAO;oDACL,QAAQ;oDACR,YAAY;gDACd;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gDAChC;gDACA,cAAc,CAAC;oDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;oDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;gDAChC;0DAEA,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAKZ,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,OAAM;;8DAEN,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,OAAO;wDACL,QAAQ;wDACR,YAAY;oDACd;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;wDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;oDAChC;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;wDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;oDAChC;8DAEA,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;8DAIV,8OAAC;oDAAK,WAAU;8DAA0H;;;;;;;;;;;;sDAM5I,8OAAC;4CACC,SAAS,IAAM,qBAAqB;4CACpC,WAAU;4CACV,OAAM;;8DAEN,8OAAC;oDACC,WAAU;oDACV,MAAK;oDACL,SAAQ;oDACR,OAAO;wDACL,QAAQ;wDACR,YAAY;oDACd;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;wDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;oDAChC;oDACA,cAAc,CAAC;wDACb,EAAE,aAAa,CAAC,KAAK,CAAC,MAAM,GAAG;wDAC/B,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,GAAG;oDAChC;8DAEA,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;gDAIT,cAAc,mBACb,8OAAC;oDAAK,WAAU;8DACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;sDAMlC,8OAAC,uIAAA,CAAA,UAAiB;;;;;sDAGlB,8OAAC;4CACC,SAAS;4CACT,WAAU;4CACV,OAAM;sDAEN,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;0CAQlB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,SAAQ;sDAER,cAAA,8OAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAKZ,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,OAAM;;0DAEN,8OAAC;gDACC,WAAU;gDACV,MAAK;gDACL,SAAQ;0DAER,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;0DAIV,8OAAC;gDAAK,WAAU;0DAAsI;;;;;;;;;;;;kDAMxJ,8OAAC,6IAAA,CAAA,UAAuB;;;;;kDAGxB,8OAAC,uIAAA,CAAA,UAAiB;;;;;kDAGlB,8OAAC,0IAAA,CAAA,UAAoB;wCACnB,MAAK;wCACL,WAAU;kDACX;;;;;;kDAKD,8OAAC;wCACC,SAAS,IAAM,cAAc,CAAC;wCAC9B,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDAAW,aAAa,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAMpD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAI,WAAU;kDAAiE;;;;;;;;;;;8CAC9G,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAO,WAAU;kDAAqD;;;;;;;;;;;8CACrG,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAQ,WAAU;;0DAC/C,8OAAC,8HAAA,CAAA,UAAQ;gDAAC,MAAK;gDAAK,SAAQ;;;;;;0DAC5B,8OAAC;0DAAK;;;;;;;;;;;;;;;;;gCAEP,4BACC;;sDACE,8OAAC;sDAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;gDAAC,MAAK;gDAAiB,WAAU;0DAAqD;;;;;;;;;;;sDAC/G,8OAAC;sDAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;gDAAC,MAAK;gDAAO,WAAU;0DAAqD;;;;;;;;;;;sDACrG,8OAAC;sDAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;gDAAC,MAAK;gDAAW,WAAU;0DAAqD;;;;;;;;;;;;;8CAG7G,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAwB,WAAU;kDAAqD;;;;;;;;;;;8CACtH,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAiB,WAAU;kDAAqD;;;;;;;;;;;8CAC/G,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAwB,WAAU;kDAAqD;;;;;;;;;;;8CACtH,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAqB,WAAU;kDAAqD;;;;;;;;;;;8CACnH,8OAAC;8CAAG,cAAA,8OAAC,0IAAA,CAAA,UAAoB;wCAAC,MAAK;wCAAgB,WAAU;kDAAiE;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAM/H,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBAEZ,CAAC,4BACA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS;wCACP,gBAAgB;wCAChB,mBAAmB;wCACnB,cAAc;oCAChB;oCACA,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS;wCACP,gBAAgB;wCAChB,mBAAmB;wCACnB,cAAc;oCAChB;oCACA,WAAU;8CACX;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAsB;;;;;;;;;;;;sDAGvC,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;;;;;;;sDAGrD,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8HAAA,CAAA,UAAQ;wDAAC,MAAK;wDAAK,SAAQ;;;;;;;;;;;8DAE9B,8OAAC;oDAAI,WAAU;8DAAqC;;;;;;;;;;;;sDAGtD,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;;;;;;;sCAM3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;;;;;;;sDAGrD,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAmC;;;;;;;;;;;;sDAGpD,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAsC;;;;;;;;;;;;sDAGvD,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAsC;;;;;;;;;;;;sDAGvD,8OAAC,0IAAA,CAAA,UAAoB;4CACnB,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,cAAc;;8DAE7B,8OAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,8OAAC;oDAAI,WAAU;8DAAsC;;;;;;;;;;;;;;;;;;;;;;;;sCAM3D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0IAAA,CAAA,UAAoB;wCACnB,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;;0DAE7B,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAGhC,8OAAC,0IAAA,CAAA,UAAoB;wCACnB,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;;0DAE7B,8OAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1C,8OAAC,+HAAA,CAAA,UAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,YAAY;;;;;;0BAId,8OAAC,wIAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,qBAAqB;;;;;;;;;;;;AAI5C;uCAEe"}}, {"offset": {"line": 5950, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5956, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport Logo from './Logo';\nimport MyCvLogo from './MyCvLogo';\nimport ContactButtons, { ContactInfo } from '@/components/ContactButtons';\nimport { COMPANY_CONTACT } from '@/lib/contact';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Main Footer */}\n      <div className=\"container mx-auto px-4 py-8 md:py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8\">\n          {/* Company Info */}\n          <div>\n            <div className=\"mb-6\">\n              <div className=\"flex items-center gap-3 mb-2\">\n                <Logo variant=\"transparent\" size=\"lg\" showText={true} textColor=\"yellow\" href=\"/\" isFooter={true} />\n                <div className=\"bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold\">\n                  سوريا\n                </div>\n              </div>\n            </div>\n            <p className=\"text-gray-400 mb-4\">\n              موقع من المالك هو أكبر موقع للإعلانات المبوبة في سوريا، يوفر منصة آمنة وسهلة للبيع والشراء.\n            </p>\n\n\n\n            {/* مواقع التواصل الاجتماعي */}\n            <div className=\"mt-6\">\n              <h5 className=\"font-medium mb-3 text-sm text-gray-300\">تابعنا على</h5>\n              <div className=\"flex gap-2\">\n                <a href=\"#\" className=\"w-8 h-8 bg-gray-800 hover:bg-blue-600 rounded-md flex items-center justify-center transition-all duration-300 group\" title=\"فيسبوك\">\n                  <svg viewBox=\"0 0 24 24\" className=\"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\" fill=\"currentColor\">\n                    <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"w-8 h-8 bg-gray-800 hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 rounded-md flex items-center justify-center transition-all duration-300 group\" title=\"انستغرام\">\n                  <svg viewBox=\"0 0 24 24\" className=\"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\" fill=\"currentColor\">\n                    <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"w-8 h-8 bg-gray-800 hover:bg-red-600 rounded-md flex items-center justify-center transition-all duration-300 group\" title=\"يوتيوب\">\n                  <svg viewBox=\"0 0 24 24\" className=\"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\" fill=\"currentColor\">\n                    <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"w-8 h-8 bg-gray-800 hover:bg-gray-900 rounded-md flex items-center justify-center transition-all duration-300 group\" title=\"X (تويتر)\">\n                  <svg viewBox=\"0 0 24 24\" className=\"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\" fill=\"currentColor\">\n                    <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"w-8 h-8 bg-gray-800 hover:bg-black rounded-md flex items-center justify-center transition-all duration-300 group\" title=\"تيك توك\">\n                  <svg viewBox=\"0 0 24 24\" className=\"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\" fill=\"currentColor\">\n                    <path d=\"M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z\"/>\n                  </svg>\n                </a>\n                <a href=\"#\" className=\"w-8 h-8 bg-gray-800 hover:bg-blue-700 rounded-md flex items-center justify-center transition-all duration-300 group\" title=\"لينكد إن\">\n                  <svg viewBox=\"0 0 24 24\" className=\"w-4 h-4 text-gray-400 group-hover:text-white transition-colors\" fill=\"currentColor\">\n                    <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                  </svg>\n                </a>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">روابط سريعة</h4>\n            <ul className=\"space-y-3\">\n              <li><Link href=\"/about\" className=\"text-gray-400 hover:text-white transition-colors\">من نحن</Link></li>\n              <li><Link href=\"/pricing\" className=\"text-gray-400 hover:text-white transition-colors\">أسعار الإعلانات</Link></li>\n              <li><Link href=\"/safety\" className=\"text-gray-400 hover:text-white transition-colors\">نصائح الأمان</Link></li>\n              <li><Link href=\"/faq\" className=\"text-gray-400 hover:text-white transition-colors\">الأسئلة الشائعة</Link></li>\n              <li><Link href=\"/dashboard\" className=\"text-gray-400 hover:text-white transition-colors\">لوحة التحكم</Link></li>\n              <li><Link href=\"/contact\" className=\"text-gray-400 hover:text-white transition-colors\">اتصل بنا</Link></li>\n            </ul>\n          </div>\n\n          {/* Categories */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">التصنيفات الرئيسية</h4>\n            <ul className=\"space-y-3\">\n              <li><Link href=\"/category/real-estate\" className=\"text-gray-400 hover:text-white transition-colors\">العقارات</Link></li>\n              <li><Link href=\"/category/cars\" className=\"text-gray-400 hover:text-white transition-colors\">السيارات</Link></li>\n              <li><Link href=\"/category/electronics\" className=\"text-gray-400 hover:text-white transition-colors\">الإلكترونيات</Link></li>\n              <li><Link href=\"/jobs\" className=\"text-gray-400 hover:text-white transition-colors\">الوظائف</Link></li>\n              <li><Link href=\"/category/services\" className=\"text-gray-400 hover:text-white transition-colors\">الخدمات</Link></li>\n              <li><Link href=\"/category/fashion\" className=\"text-gray-400 hover:text-white transition-colors\">الأزياء</Link></li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h4 className=\"text-lg font-semibold mb-6\">معلومات التواصل</h4>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-start gap-3\">\n                <img\n                  src=\"/images/الفوتر/دمشق، سوريا.png\"\n                  alt=\"الموقع\"\n                  className=\"w-5 h-5 mt-1 opacity-80\"\n                  style={{ filter: 'brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(200deg)' }}\n                />\n                <div>\n                  <p className=\"text-white font-semibold\">دمشق، سوريا</p>\n                  <p className=\"text-gray-400 text-sm\">العنوان الرئيسي</p>\n                </div>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <img\n                  src=\"/images/الفوتر/+963 988 652 401.png\"\n                  alt=\"الهاتف\"\n                  className=\"w-5 h-5 opacity-80\"\n                  style={{ filter: 'brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(200deg)' }}\n                />\n                <div>\n                  <p className=\"text-white font-semibold\">+963 988 652 401</p>\n                  <p className=\"text-gray-400 text-sm\">للاتصال والواتساب</p>\n                </div>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <img\n                  src=\"/images/الفوتر/<EMAIL>\"\n                  alt=\"البريد الإلكتروني\"\n                  className=\"w-5 h-5 opacity-80\"\n                  style={{ filter: 'brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(200deg)' }}\n                />\n                <p className=\"text-gray-400\"><EMAIL></p>\n              </div>\n              <div className=\"flex items-center gap-3\">\n                <img\n                  src=\"/images/الفوتر/من السبت إلى الخميس.png\"\n                  alt=\"أوقات العمل\"\n                  className=\"w-5 h-5 opacity-80\"\n                  style={{ filter: 'brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(200deg)' }}\n                />\n                <div>\n                  <p className=\"text-gray-400\">من السبت إلى الخميس</p>\n                  <p className=\"text-gray-400 text-sm\">وقت الرد: خلال 24 ساعة</p>\n                </div>\n              </div>\n            </div>\n\n\n          </div>\n        </div>\n      </div>\n\n      {/* Apps Download Section */}\n      <div className=\"border-t border-gray-800 bg-gray-800/50\">\n        <div className=\"container mx-auto px-4 py-4 md:py-6\">\n          {/* Apps and Payment Methods in One Row */}\n          <div className=\"flex flex-wrap items-center justify-center gap-4 md:gap-6\">\n            {/* تطبيق MyCv */}\n            <div className=\"flex items-center gap-3 bg-gray-900 rounded-lg p-3 min-w-[240px]\">\n              <MyCvLogo size=\"sm\" variant=\"square\" />\n              <div className=\"flex-1 min-w-0\">\n                <h6 className=\"font-medium text-white text-sm\">تطبيق MyCv</h6>\n                <p className=\"text-gray-400 text-xs mb-2\">منصة السير الذاتية والتوظيف</p>\n                <div className=\"flex gap-1\">\n                  <a href=\"#\" className=\"bg-black text-white px-2 py-1 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs\">\n                    <svg viewBox=\"0 0 24 24\" className=\"w-3 h-3 text-white\" fill=\"currentColor\">\n                      <path d=\"M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z\"/>\n                    </svg>\n                    <span className=\"hidden sm:inline\">AppStore</span>\n                  </a>\n                  <a href=\"#\" className=\"bg-black text-white px-2 py-1 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs\">\n                    <svg viewBox=\"0 0 24 24\" className=\"w-3 h-3 text-white\" fill=\"currentColor\">\n                      <path d=\"M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z\"/>\n                    </svg>\n                    <span className=\"hidden sm:inline\">Android</span>\n                  </a>\n                </div>\n              </div>\n            </div>\n\n            {/* تطبيق من المالك */}\n            <div className=\"flex items-center gap-3 bg-gray-900 rounded-lg p-3 min-w-[240px]\">\n              <Logo variant=\"transparent\" size=\"sm\" showText={false} />\n              <div className=\"flex-1 min-w-0\">\n                <h6 className=\"font-medium text-white text-sm\">تطبيق من المالك</h6>\n                <p className=\"text-gray-400 text-xs mb-2\">منصة الإعلانات المبوبة</p>\n                <div className=\"flex gap-1\">\n                  <a href=\"#\" className=\"bg-black text-white px-2 py-1 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs\">\n                    <svg viewBox=\"0 0 24 24\" className=\"w-3 h-3 text-white\" fill=\"currentColor\">\n                      <path d=\"M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z\"/>\n                    </svg>\n                    <span className=\"hidden sm:inline\">AppStore</span>\n                  </a>\n                  <a href=\"#\" className=\"bg-black text-white px-2 py-1 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs\">\n                    <svg viewBox=\"0 0 24 24\" className=\"w-3 h-3 text-white\" fill=\"currentColor\">\n                      <path d=\"M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z\"/>\n                    </svg>\n                    <span className=\"hidden sm:inline\">Android</span>\n                  </a>\n                </div>\n              </div>\n            </div>\n\n\n\n            {/* QR Code Section */}\n            <div className=\"flex items-center gap-4 bg-gray-900 rounded-lg p-4 min-w-[200px]\">\n              <div className=\"relative flex-shrink-0\">\n                {/* QR Code Background */}\n                <div className=\"w-20 h-20 bg-white rounded-md p-1 shadow-lg\">\n                  {/* Real QR Code for Min Almalek website */}\n                  <img\n                    src=\"https://api.qrserver.com/v1/create-qr-code/?size=80x80&data=https://min-almalek.com&bgcolor=ffffff&color=000000&margin=1\"\n                    alt=\"QR Code for Min Almalek\"\n                    className=\"w-full h-full rounded\"\n                  />\n\n                  {/* Logo overlay in center */}\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\n                    <div className=\"w-4 h-4 bg-white rounded-sm flex items-center justify-center shadow-sm\">\n                      <Logo variant=\"dark\" size=\"xs\" showText={false} />\n                    </div>\n                  </div>\n                </div>\n              </div>\n              <div className=\"flex-1\">\n                <h6 className=\"font-medium text-white text-sm\">موقع من المالك</h6>\n                <p className=\"text-gray-400 text-xs mb-1\">امسح للوصول السريع</p>\n                <p className=\"text-gray-500 text-xs\">min-almalek.com</p>\n              </div>\n            </div>\n\n            {/* Partner Logos and Payment Methods Section */}\n            <div className=\"flex items-center gap-4 bg-gray-900 rounded-lg p-4 min-w-[200px]\">\n              <div className=\"flex-1\">\n                {/* Partner Logos */}\n                <div className=\"flex items-center justify-center gap-4 mb-3\">\n                  {/* MADENLİ Group Logo */}\n                  <div className=\"flex items-center justify-center\">\n                    <img\n                      src=\"/images/madenli group LOGO.jpg\"\n                      alt=\"MADENLİ Group\"\n                      className=\"h-8 w-auto object-contain rounded-sm\"\n                    />\n                  </div>\n\n                  {/* MyCv Logo */}\n                  <div className=\"flex items-center justify-center\">\n                    <img\n                      src=\"/images/MyCV Logo.jpg\"\n                      alt=\"MyCv\"\n                      className=\"h-8 w-auto object-contain rounded-sm\"\n                    />\n                  </div>\n                </div>\n\n                {/* Payment Methods */}\n                <div className=\"flex flex-wrap items-center justify-center gap-2\">\n                  {/* Visa */}\n                  <img\n                    src=\"https://upload.wikimedia.org/wikipedia/commons/4/41/Visa_Logo.png\"\n                    alt=\"Visa\"\n                    className=\"h-4 w-auto object-contain\"\n                  />\n\n                  {/* MasterCard */}\n                  <img\n                    src=\"https://upload.wikimedia.org/wikipedia/commons/0/04/Mastercard-logo.png\"\n                    alt=\"MasterCard\"\n                    className=\"h-4 w-auto object-contain\"\n                  />\n\n                  {/* PayPal */}\n                  <img\n                    src=\"https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg\"\n                    alt=\"PayPal\"\n                    className=\"h-4 w-auto object-contain\"\n                  />\n\n                  {/* Cash App */}\n                  <img\n                    src=\"/images/cash-app-logo.svg\"\n                    alt=\"Cash App\"\n                    className=\"h-4 w-auto object-contain\"\n                  />\n\n                  {/* Apple Pay */}\n                  <div className=\"h-4 flex items-center gap-1\">\n                    <img\n                      src=\"https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg\"\n                      alt=\"Apple\"\n                      className=\"h-4 w-3 object-contain invert\"\n                    />\n                    <span className=\"text-xs font-semibold text-white\">Pay</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Bar */}\n      <div className=\"border-t border-gray-800\">\n        <div className=\"container mx-auto px-4 py-6\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between gap-4\">\n            <div className=\"text-gray-400 text-sm\">\n              © 2017-2025 من المالك. جميع الحقوق محفوظة.\n            </div>\n            <div className=\"flex flex-wrap gap-6 text-sm\">\n              <Link href=\"/privacy\" className=\"text-gray-400 hover:text-white transition-colors\">\n                سياسة الخصوصية\n              </Link>\n              <Link href=\"/terms\" className=\"text-gray-400 hover:text-white transition-colors\">\n                شروط الاستخدام\n              </Link>\n              <Link href=\"/cookies\" className=\"text-gray-400 hover:text-white transition-colors\">\n                سياسة الكوكيز\n              </Link>\n              <Link href=\"/sitemap\" className=\"text-gray-400 hover:text-white transition-colors\">\n                خريطة الموقع\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n\n\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAIA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;;8CACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0HAAA,CAAA,UAAI;gDAAC,SAAQ;gDAAc,MAAK;gDAAK,UAAU;gDAAM,WAAU;gDAAS,MAAK;gDAAI,UAAU;;;;;;0DAC5F,8OAAC;gDAAI,WAAU;0DAA+D;;;;;;;;;;;;;;;;;8CAKlF,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAOlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,MAAK;oDAAI,WAAU;oDAAsH,OAAM;8DAChJ,cAAA,8OAAC;wDAAI,SAAQ;wDAAY,WAAU;wDAAiE,MAAK;kEACvG,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,MAAK;oDAAI,WAAU;oDAAmK,OAAM;8DAC7L,cAAA,8OAAC;wDAAI,SAAQ;wDAAY,WAAU;wDAAiE,MAAK;kEACvG,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,MAAK;oDAAI,WAAU;oDAAqH,OAAM;8DAC/I,cAAA,8OAAC;wDAAI,SAAQ;wDAAY,WAAU;wDAAiE,MAAK;kEACvG,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,MAAK;oDAAI,WAAU;oDAAsH,OAAM;8DAChJ,cAAA,8OAAC;wDAAI,SAAQ;wDAAY,WAAU;wDAAiE,MAAK;kEACvG,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,MAAK;oDAAI,WAAU;oDAAmH,OAAM;8DAC7I,cAAA,8OAAC;wDAAI,SAAQ;wDAAY,WAAU;wDAAiE,MAAK;kEACvG,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDAAE,MAAK;oDAAI,WAAU;oDAAsH,OAAM;8DAChJ,cAAA,8OAAC;wDAAI,SAAQ;wDAAY,WAAU;wDAAiE,MAAK;kEACvG,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmD;;;;;;;;;;;sDACrF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;sDACvF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmD;;;;;;;;;;;sDACtF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAO,WAAU;0DAAmD;;;;;;;;;;;sDACnF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;0DAAmD;;;;;;;;;;;sDACzF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAK3F,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAwB,WAAU;0DAAmD;;;;;;;;;;;sDACpG,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAiB,WAAU;0DAAmD;;;;;;;;;;;sDAC7F,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAwB,WAAU;0DAAmD;;;;;;;;;;;sDACpG,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmD;;;;;;;;;;;sDACpF,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAqB,WAAU;0DAAmD;;;;;;;;;;;sDACjG,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAoB,WAAU;0DAAmD;;;;;;;;;;;;;;;;;;;;;;;sCAKpG,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;oDACV,OAAO;wDAAE,QAAQ;oDAAkE;;;;;;8DAErF,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA2B;;;;;;sEACxC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;oDACV,OAAO;wDAAE,QAAQ;oDAAkE;;;;;;8DAErF,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAA2B;;;;;;sEACxC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;oDACV,OAAO;wDAAE,QAAQ;oDAAkE;;;;;;8DAErF,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAE/B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;oDACV,OAAO;wDAAE,QAAQ;oDAAkE;;;;;;8DAErF,8OAAC;;sEACC,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWjD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8HAAA,CAAA,UAAQ;wCAAC,MAAK;wCAAK,SAAQ;;;;;;kDAC5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,MAAK;wDAAI,WAAU;;0EACpB,8OAAC;gEAAI,SAAQ;gEAAY,WAAU;gEAAqB,MAAK;0EAC3D,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;0EAEV,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;kEAErC,8OAAC;wDAAE,MAAK;wDAAI,WAAU;;0EACpB,8OAAC;gEAAI,SAAQ;gEAAY,WAAU;gEAAqB,MAAK;0EAC3D,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;0EAEV,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0HAAA,CAAA,UAAI;wCAAC,SAAQ;wCAAc,MAAK;wCAAK,UAAU;;;;;;kDAChD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,MAAK;wDAAI,WAAU;;0EACpB,8OAAC;gEAAI,SAAQ;gEAAY,WAAU;gEAAqB,MAAK;0EAC3D,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;0EAEV,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;kEAErC,8OAAC;wDAAE,MAAK;wDAAI,WAAU;;0EACpB,8OAAC;gEAAI,SAAQ;gEAAY,WAAU;gEAAqB,MAAK;0EAC3D,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;0EAEV,8OAAC;gEAAK,WAAU;0EAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAEb,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0HAAA,CAAA,UAAI;4DAAC,SAAQ;4DAAO,MAAK;4DAAK,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKjD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAC1C,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAKzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;8DAKd,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;;;;;;;;;;;;sDAMhB,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;;;;;;8DAIZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,KAAI;4DACJ,KAAI;4DACJ,WAAU;;;;;;sEAEZ,8OAAC;4DAAK,WAAU;sEAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;0CAGvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAmD;;;;;;kDAGnF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAmD;;;;;;kDAGjF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAmD;;;;;;kDAGnF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAmD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWjG;uCAEe"}}, {"offset": {"line": 7198, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7204, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/lib/jobs.ts"], "sourcesContent": ["// نظام الوظائف والسير الذاتية المتقدم\n\nexport interface Experience {\n  id: string;\n  jobTitle: string;\n  company: string;\n  location: string;\n  startDate: string;\n  endDate?: string;\n  current: boolean;\n  description: string;\n  achievements: string[];\n}\n\nexport interface Education {\n  id: string;\n  degree: string;\n  institution: string;\n  field: string;\n  location: string;\n  startDate: string;\n  endDate?: string;\n  current: boolean;\n  gpa?: string;\n  description?: string;\n}\n\nexport interface Skill {\n  id: string;\n  name: string;\n  level: 'مبتدئ' | 'متوسط' | 'متقدم' | 'خبير';\n  category: 'تقني' | 'لغوي' | 'إداري' | 'إبداعي' | 'أخرى';\n  verified: boolean;\n}\n\nexport interface Language {\n  id: string;\n  name: string;\n  level: 'مبتدئ' | 'متوسط' | 'متقدم' | 'أصلي';\n  certification?: string;\n}\n\nexport interface Course {\n  id: string;\n  name: string;\n  provider: string;\n  completionDate: string;\n  certificateUrl?: string;\n  skills: string[];\n}\n\nexport interface Project {\n  id: string;\n  name: string;\n  description: string;\n  technologies: string[];\n  url?: string;\n  startDate: string;\n  endDate?: string;\n  status: 'مكتمل' | 'قيد التطوير' | 'متوقف';\n}\n\nexport interface ContactInfo {\n  phone: string;\n  email: string;\n  linkedin?: string;\n  github?: string;\n  portfolio?: string;\n  address: string;\n  city: string;\n}\n\nexport interface Resume {\n  id: string;\n  userId: string;\n  personalInfo: {\n    firstName: string;\n    lastName: string;\n    title: string;\n    summary: string;\n    photo?: string;\n    dateOfBirth?: string;\n    nationality: string;\n    maritalStatus?: string;\n  };\n  contactInfo: ContactInfo;\n  experiences: Experience[];\n  education: Education[];\n  skills: Skill[];\n  languages: Language[];\n  courses: Course[];\n  projects?: Project[];\n  references?: {\n    id: string;\n    name: string;\n    position: string;\n    company: string;\n    phone: string;\n    email: string;\n    relationship?: string;\n  }[];\n  createdAt: string;\n  updatedAt: string;\n  isPublic: boolean;\n  views: number;\n}\n\nexport interface JobPosting {\n  id: string;\n  companyId: string;\n  companyName: string;\n  companyLogo?: string;\n  title: string;\n  department: string;\n  location: string;\n  workType: 'دوام كامل' | 'دوام جزئي' | 'عمل حر' | 'تدريب' | 'عقد مؤقت';\n  workModel: 'حضوري' | 'عن بعد';\n  salaryRange: {\n    min: number;\n    max: number;\n    currency: string;\n    period: 'شهري' | 'سنوي' | 'يومي' | 'ساعي';\n  };\n  description: string;\n  requirements: string[];\n  responsibilities: string[];\n  benefits: string[];\n  skills: string[];\n  experienceLevel: 'مبتدئ' | 'متوسط' | 'متقدم' | 'خبير';\n  experienceYears: {\n    min: number;\n    max: number;\n  };\n  educationLevel: 'ثانوية' | 'دبلوم' | 'بكالوريوس' | 'ماجستير' | 'دكتوراه';\n  applicationDeadline: string;\n  postedDate: string;\n  status: 'نشط' | 'مغلق' | 'مؤجل';\n  applicationsCount: number;\n  featured: boolean;\n  urgent: boolean;\n  category: string;\n  subcategory?: string;\n}\n\nexport interface JobApplication {\n  id: string;\n  jobId: string;\n  applicantId: string;\n  resumeId: string;\n  coverLetter: string;\n  status: 'مرسل' | 'قيد المراجعة' | 'مقبول للمقابلة' | 'مرفوض' | 'مقبول';\n  appliedDate: string;\n  lastUpdated: string;\n  notes?: string;\n}\n\n// فئات الوظائف\nexport const JOB_CATEGORIES = [\n  {\n    id: 'technology',\n    name: 'التكنولوجيا والبرمجة',\n    icon: '💻',\n    subcategories: [\n      'تطوير الويب',\n      'تطوير التطبيقات',\n      'أمن المعلومات',\n      'الذكاء الاصطناعي',\n      'تحليل البيانات',\n      'DevOps',\n      'UI/UX Design',\n      'إدارة المشاريع التقنية'\n    ]\n  },\n  {\n    id: 'medical',\n    name: 'الطب والصحة',\n    icon: '🏥',\n    subcategories: [\n      'أطباء',\n      'ممرضين',\n      'صيادلة',\n      'أطباء أسنان',\n      'فنيين طبيين',\n      'إدارة صحية',\n      'علاج طبيعي',\n      'تغذية'\n    ]\n  },\n  {\n    id: 'engineering',\n    name: 'الهندسة',\n    icon: '⚙️',\n    subcategories: [\n      'هندسة مدنية',\n      'هندسة كهربائية',\n      'هندسة ميكانيكية',\n      'هندسة معمارية',\n      'هندسة صناعية',\n      'هندسة بترول',\n      'هندسة بيئية',\n      'هندسة طيران'\n    ]\n  },\n  {\n    id: 'business',\n    name: 'الأعمال والإدارة',\n    icon: '💼',\n    subcategories: [\n      'إدارة عامة',\n      'الموارد البشرية',\n      'المبيعات',\n      'التسويق',\n      'المحاسبة',\n      'المالية',\n      'خدمة العملاء',\n      'إدارة المشاريع'\n    ]\n  },\n  {\n    id: 'education',\n    name: 'التعليم والتدريب',\n    icon: '📚',\n    subcategories: [\n      'معلمين',\n      'أساتذة جامعيين',\n      'مدربين',\n      'مستشارين تعليميين',\n      'إدارة تعليمية',\n      'تطوير مناهج',\n      'تعليم خاص',\n      'تعليم إلكتروني'\n    ]\n  },\n  {\n    id: 'creative',\n    name: 'الإبداع والفنون',\n    icon: '🎨',\n    subcategories: [\n      'تصميم جرافيك',\n      'تصوير',\n      'كتابة وتحرير',\n      'إنتاج فيديو',\n      'تصميم داخلي',\n      'موسيقى',\n      'إعلان وتسويق',\n      'فنون بصرية'\n    ]\n  }\n];\n\n// مستويات الخبرة\nexport const EXPERIENCE_LEVELS = [\n  { id: 'entry', name: 'مبتدئ', years: '0-2 سنوات' },\n  { id: 'mid', name: 'متوسط', years: '2-5 سنوات' },\n  { id: 'senior', name: 'متقدم', years: '5-10 سنوات' },\n  { id: 'expert', name: 'خبير', years: '10+ سنوات' }\n];\n\n// أنواع العمل\nexport const WORK_TYPES = [\n  { id: 'full-time', name: 'دوام كامل', icon: '🕘' },\n  { id: 'part-time', name: 'دوام جزئي', icon: '🕐' },\n  { id: 'freelance', name: 'عمل حر', icon: '💼' },\n  { id: 'internship', name: 'تدريب', icon: '🎓' },\n  { id: 'contract', name: 'عقد مؤقت', icon: '📄' }\n];\n\n// نماذج العمل\nexport const WORK_MODELS = [\n  { id: 'onsite', name: 'حضوري', icon: '🏢' },\n  { id: 'remote', name: 'عن بعد', icon: '🏠' }\n];\n\n// مستويات التعليم\nexport const EDUCATION_LEVELS = [\n  { id: 'high-school', name: 'ثانوية عامة' },\n  { id: 'diploma', name: 'دبلوم' },\n  { id: 'bachelor', name: 'بكالوريوس' },\n  { id: 'master', name: 'ماجستير' },\n  { id: 'phd', name: 'دكتوراه' }\n];\n\n// دوال مساعدة\nexport const JobUtils = {\n  // تنسيق نطاق الراتب\n  formatSalaryRange: (range: JobPosting['salaryRange']) => {\n    if (range.min === range.max) {\n      return `${range.min.toLocaleString()} ${range.currency} ${range.period}`;\n    }\n    return `${range.min.toLocaleString()} - ${range.max.toLocaleString()} ${range.currency} ${range.period}`;\n  },\n\n  // حساب عدد أيام النشر\n  getDaysAgo: (date: string) => {\n    const posted = new Date(date);\n    const now = new Date();\n    const diffTime = Math.abs(now.getTime() - posted.getTime());\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n\n    if (diffDays === 1) return 'منذ يوم واحد';\n    if (diffDays < 7) return `منذ ${diffDays} أيام`;\n    if (diffDays < 30) return `منذ ${Math.floor(diffDays / 7)} أسابيع`;\n    return `منذ ${Math.floor(diffDays / 30)} أشهر`;\n  },\n\n  // دالة لإرجاع نمط CSS للأيقونات والمراحل (سادة وشفافة، وتوهج فقط عند الضغط، حجم احترافي)\n  getStepIconStyle: (active: boolean = false) => ({\n    background: 'rgba(255,255,255,0.85)',\n    border: 'none',\n    color: '#22c55e',\n    boxShadow: active\n      ? '0 0 16px 4px rgba(34,197,94,0.25), 0 0 0 2px #22c55e'\n      : '0 2px 8px 0 rgba(34,197,94,0.08)',\n    borderRadius: '50%',\n    padding: '0',\n    width: '44px',\n    height: '44px',\n    fontSize: '1.6rem',\n    transition: 'all 0.2s',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    cursor: 'pointer',\n    outline: active ? 'none' : undefined,\n  }),\n\n  // فلترة الوظائف\n  filterJobs: (jobs: JobPosting[], filters: {\n    category?: string;\n    workType?: string;\n    workModel?: string;\n    experienceLevel?: string;\n    location?: string;\n    salaryMin?: number;\n    salaryMax?: number;\n  }) => {\n    return jobs.filter(job => {\n      if (filters.category && job.category !== filters.category) return false;\n      if (filters.workType && job.workType !== filters.workType) return false;\n      if (filters.workModel && job.workModel !== filters.workModel) return false;\n      if (filters.experienceLevel && job.experienceLevel !== filters.experienceLevel) return false;\n      if (filters.location && !job.location.includes(filters.location)) return false;\n      if (filters.salaryMin && job.salaryRange.min < filters.salaryMin) return false;\n      if (filters.salaryMax && job.salaryRange.max > filters.salaryMax) return false;\n      return true;\n    });\n  },\n\n  // البحث في الوظائف\n  searchJobs: (jobs: JobPosting[], query: string) => {\n    const searchTerm = query.toLowerCase();\n    return jobs.filter(job =>\n      job.title.toLowerCase().includes(searchTerm) ||\n      job.companyName.toLowerCase().includes(searchTerm) ||\n      job.description.toLowerCase().includes(searchTerm) ||\n      job.skills.some(skill => skill.toLowerCase().includes(searchTerm))\n    );\n  }\n};\n\nexport default {\n  JOB_CATEGORIES,\n  EXPERIENCE_LEVELS,\n  WORK_TYPES,\n  WORK_MODELS,\n  EDUCATION_LEVELS,\n  JobUtils\n};\n"], "names": [], "mappings": "AAAA,sCAAsC;;;;;;;;;;AA6J/B,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,eAAe;YACb;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;CACD;AAGM,MAAM,oBAAoB;IAC/B;QAAE,IAAI;QAAS,MAAM;QAAS,OAAO;IAAY;IACjD;QAAE,IAAI;QAAO,MAAM;QAAS,OAAO;IAAY;IAC/C;QAAE,IAAI;QAAU,MAAM;QAAS,OAAO;IAAa;IACnD;QAAE,IAAI;QAAU,MAAM;QAAQ,OAAO;IAAY;CAClD;AAGM,MAAM,aAAa;IACxB;QAAE,IAAI;QAAa,MAAM;QAAa,MAAM;IAAK;IACjD;QAAE,IAAI;QAAa,MAAM;QAAa,MAAM;IAAK;IACjD;QAAE,IAAI;QAAa,MAAM;QAAU,MAAM;IAAK;IAC9C;QAAE,IAAI;QAAc,MAAM;QAAS,MAAM;IAAK;IAC9C;QAAE,IAAI;QAAY,MAAM;QAAY,MAAM;IAAK;CAChD;AAGM,MAAM,cAAc;IACzB;QAAE,IAAI;QAAU,MAAM;QAAS,MAAM;IAAK;IAC1C;QAAE,IAAI;QAAU,MAAM;QAAU,MAAM;IAAK;CAC5C;AAGM,MAAM,mBAAmB;IAC9B;QAAE,IAAI;QAAe,MAAM;IAAc;IACzC;QAAE,IAAI;QAAW,MAAM;IAAQ;IAC/B;QAAE,IAAI;QAAY,MAAM;IAAY;IACpC;QAAE,IAAI;QAAU,MAAM;IAAU;IAChC;QAAE,IAAI;QAAO,MAAM;IAAU;CAC9B;AAGM,MAAM,WAAW;IACtB,oBAAoB;IACpB,mBAAmB,CAAC;QAClB,IAAI,MAAM,GAAG,KAAK,MAAM,GAAG,EAAE;YAC3B,OAAO,GAAG,MAAM,GAAG,CAAC,cAAc,GAAG,CAAC,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;QAC1E;QACA,OAAO,GAAG,MAAM,GAAG,CAAC,cAAc,GAAG,GAAG,EAAE,MAAM,GAAG,CAAC,cAAc,GAAG,CAAC,EAAE,MAAM,QAAQ,CAAC,CAAC,EAAE,MAAM,MAAM,EAAE;IAC1G;IAEA,sBAAsB;IACtB,YAAY,CAAC;QACX,MAAM,SAAS,IAAI,KAAK;QACxB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,OAAO,KAAK,OAAO,OAAO;QACxD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE1D,IAAI,aAAa,GAAG,OAAO;QAC3B,IAAI,WAAW,GAAG,OAAO,CAAC,IAAI,EAAE,SAAS,KAAK,CAAC;QAC/C,IAAI,WAAW,IAAI,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,WAAW,GAAG,OAAO,CAAC;QAClE,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC;IAChD;IAEA,yFAAyF;IACzF,kBAAkB,CAAC,SAAkB,KAAK,GAAK,CAAC;YAC9C,YAAY;YACZ,QAAQ;YACR,OAAO;YACP,WAAW,SACP,yDACA;YACJ,cAAc;YACd,SAAS;YACT,OAAO;YACP,QAAQ;YACR,UAAU;YACV,YAAY;YACZ,SAAS;YACT,YAAY;YACZ,gBAAgB;YAChB,QAAQ;YACR,SAAS,SAAS,SAAS;QAC7B,CAAC;IAED,gBAAgB;IAChB,YAAY,CAAC,MAAoB;QAS/B,OAAO,KAAK,MAAM,CAAC,CAAA;YACjB,IAAI,QAAQ,QAAQ,IAAI,IAAI,QAAQ,KAAK,QAAQ,QAAQ,EAAE,OAAO;YAClE,IAAI,QAAQ,QAAQ,IAAI,IAAI,QAAQ,KAAK,QAAQ,QAAQ,EAAE,OAAO;YAClE,IAAI,QAAQ,SAAS,IAAI,IAAI,SAAS,KAAK,QAAQ,SAAS,EAAE,OAAO;YACrE,IAAI,QAAQ,eAAe,IAAI,IAAI,eAAe,KAAK,QAAQ,eAAe,EAAE,OAAO;YACvF,IAAI,QAAQ,QAAQ,IAAI,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,QAAQ,GAAG,OAAO;YACzE,IAAI,QAAQ,SAAS,IAAI,IAAI,WAAW,CAAC,GAAG,GAAG,QAAQ,SAAS,EAAE,OAAO;YACzE,IAAI,QAAQ,SAAS,IAAI,IAAI,WAAW,CAAC,GAAG,GAAG,QAAQ,SAAS,EAAE,OAAO;YACzE,OAAO;QACT;IACF;IAEA,mBAAmB;IACnB,YAAY,CAAC,MAAoB;QAC/B,MAAM,aAAa,MAAM,WAAW;QACpC,OAAO,KAAK,MAAM,CAAC,CAAA,MACjB,IAAI,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eACjC,IAAI,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,eACvC,IAAI,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,eACvC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,WAAW,GAAG,QAAQ,CAAC;IAE1D;AACF;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;AACF"}}, {"offset": {"line": 7453, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7459, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/app/jobs/individuals/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport Header from '@/components/Header';\nimport Footer from '@/components/Footer';\nimport JobFilters, { JobFilters as JobFiltersType } from '@/components/JobFilters';\nimport JobCard, { FeaturedJobCard } from '@/components/JobCard';\nimport MyCvLogo from '@/components/MyCvLogo';\nimport { JobPosting, JobUtils, JOB_CATEGORIES } from '@/lib/jobs';\n\n// بيانات تجريبية للوظائف\nconst sampleJobs: JobPosting[] = [\n  {\n    id: '1',\n    companyId: 'comp1',\n    companyName: 'شركة التقنيات المتقدمة',\n    title: 'مطور React.js متقدم',\n    department: 'التطوير',\n    location: 'دمشق',\n    workType: 'دوام كامل',\n    workModel: 'مختلط',\n    salaryRange: {\n      min: 800000,\n      max: 1200000,\n      currency: 'ل.س',\n      period: 'شهري'\n    },\n    description: 'نبحث عن مطور React.js متقدم للانضمام إلى فريقنا المتميز.',\n    requirements: [\n      'خبرة 3+ سنوات في React.js',\n      'إتقان TypeScript',\n      'خبرة في Redux أو Context API',\n      'معرفة بـ Next.js',\n      'خبرة في Git'\n    ],\n    responsibilities: [\n      'تطوير واجهات المستخدم التفاعلية',\n      'كتابة كود نظيف وقابل للصيانة',\n      'التعاون مع فريق التصميم'\n    ],\n    benefits: [\n      'تأمين صحي',\n      'مكافآت سنوية',\n      'تدريب وتطوير',\n      'عمل مرن'\n    ],\n    skills: ['React.js', 'TypeScript', 'Redux', 'Next.js', 'Git'],\n    experienceLevel: 'متقدم',\n    experienceYears: { min: 3, max: 7 },\n    educationLevel: 'بكالوريوس',\n    applicationDeadline: '2024-03-15',\n    postedDate: '2024-02-15',\n    status: 'نشط',\n    applicationsCount: 45,\n    featured: true,\n    urgent: false,\n    category: 'technology'\n  }\n];\n\nexport default function IndividualJobsPage() {\n  const [jobs, setJobs] = useState<JobPosting[]>(sampleJobs);\n  const [filteredJobs, setFilteredJobs] = useState<JobPosting[]>(sampleJobs);\n  const [filters, setFilters] = useState<JobFiltersType>({\n    search: '',\n    location: '',\n    category: '',\n    workType: '',\n    experienceLevel: '',\n    salaryRange: { min: 0, max: 5000000 },\n    featured: false,\n    urgent: false\n  });\n\n  // تطبيق الفلاتر\n  useEffect(() => {\n    const filtered = JobUtils.filterJobs(jobs, filters);\n    setFilteredJobs(filtered);\n  }, [jobs, filters]);\n\n  const handleFiltersChange = (newFilters: JobFiltersType) => {\n    setFilters(newFilters);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Header />\n      \n      <main className=\"py-8 relative\">\n        {/* MyCv Logo with Glow */}\n        <div className=\"fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-0 pointer-events-none\">\n          <img\n            src=\"/images/MyCV Logo.jpg\"\n            alt=\"MyCv\"\n            className=\"w-32 h-32 opacity-5 transition-all duration-1000 hover:opacity-10\"\n            style={{\n              filter: 'drop-shadow(0 0 20px rgba(34, 197, 94, 0.3))'\n            }}\n          />\n        </div>\n\n        <div className=\"container mx-auto px-4 relative z-10\">\n          {/* العنوان الرئيسي */}\n          <div className=\"text-center mb-8\">\n            <div className=\"flex items-center justify-center gap-3 mb-4\">\n              <img\n                src=\"/images/mycv logo/الوظائف/transparent-Photoroom (3).png\"\n                alt=\"الوظائف\"\n                className=\"w-12 h-12\"\n                style={{\n                  filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.6))'\n                }}\n              />\n              <h1 className=\"text-4xl font-bold text-gray-800\">الوظائف</h1>\n            </div>\n            <p className=\"text-xl text-gray-600\">أنشئ سيرتك الذاتية الاحترافية - ابحث عن وظيفة أحلامك</p>\n          </div>\n\n          {/* أزرار الإجراءات السريعة */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12\">\n            {/* إنشاء السيرة الذاتية */}\n            <div className=\"bg-white/20 backdrop-blur-sm rounded-xl shadow-lg p-6 hover:shadow-2xl transition-all duration-300 border border-white/30 group hover:bg-white/30\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-blue-500/50 group-hover:bg-white/40\">\n                  <img\n                    src=\"/images/mycv logo/أنشئ سيرتك الذاتية/applicant (1).v2.png\"\n                    alt=\"إنشاء\"\n                    className=\"w-8 h-8 opacity-80 group-hover:opacity-100 transition-opacity filter group-hover:drop-shadow-lg\"\n                    style={{\n                      filter: 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.6))'\n                    }}\n                  />\n                </div>\n                <h3 className=\"text-lg font-bold text-gray-800 mb-2\">أنشئ سيرتك الذاتية</h3>\n                <p className=\"text-gray-600 mb-4 text-sm\">إنشاء سيرة ذاتية احترافية</p>\n                <Link\n                  href=\"/resume/create\"\n                  className=\"inline-block w-full py-2 bg-green-400 text-white rounded-lg hover:bg-green-500 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium text-sm border border-green-300\"\n                >\n                  ابدأ الآن\n                </Link>\n              </div>\n            </div>\n\n            {/* الوظائف المميزة */}\n            <div className=\"bg-white/20 backdrop-blur-sm rounded-xl shadow-lg p-6 hover:shadow-2xl transition-all duration-300 border border-white/30 group hover:bg-white/30\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-yellow-500/50 group-hover:bg-white/40\">\n                  <img\n                    src=\"/images/jobs/Jobs -Personals/icons/hotel_class_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png\"\n                    alt=\"مميز\"\n                    className=\"w-8 h-8 opacity-80 group-hover:opacity-100 transition-opacity filter group-hover:drop-shadow-lg\"\n                    style={{\n                      filter: 'drop-shadow(0 0 8px rgba(234, 179, 8, 0.6))'\n                    }}\n                  />\n                </div>\n                <h3 className=\"text-lg font-bold text-gray-800 mb-2\">الوظائف المميزة</h3>\n                <p className=\"text-gray-600 mb-4 text-sm\">أفضل الفرص المتاحة</p>\n                <Link\n                  href=\"/jobs/individuals?featured=true\"\n                  className=\"inline-block w-full py-2 bg-green-400 text-white rounded-lg hover:bg-green-500 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium text-sm border border-green-300\"\n                >\n                  تصفح الآن\n                </Link>\n              </div>\n            </div>\n\n            {/* جميع الوظائف */}\n            <div className=\"bg-white/20 backdrop-blur-sm rounded-xl shadow-lg p-6 hover:shadow-2xl transition-all duration-300 border border-white/30 group hover:bg-white/30\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-green-500/50 group-hover:bg-white/40\">\n                  <img\n                    src=\"/images/jobs/Jobs -Personals/icons/search_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png\"\n                    alt=\"بحث\"\n                    className=\"w-8 h-8 opacity-80 group-hover:opacity-100 transition-opacity filter group-hover:drop-shadow-lg\"\n                    style={{\n                      filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.6))'\n                    }}\n                  />\n                </div>\n                <h3 className=\"text-lg font-bold text-gray-800 mb-2\">جميع الوظائف</h3>\n                <p className=\"text-gray-600 mb-4 text-sm\">تصفح كافة الوظائف</p>\n                <Link\n                  href=\"/jobs/all\"\n                  className=\"inline-block w-full py-2 bg-green-400 text-white rounded-lg hover:bg-green-500 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium text-sm border border-green-300\"\n                >\n                  ابحث الآن\n                </Link>\n              </div>\n            </div>\n\n            {/* الوظائف العاجلة */}\n            <div className=\"bg-white/20 backdrop-blur-sm rounded-xl shadow-lg p-6 hover:shadow-2xl transition-all duration-300 border border-white/30 group hover:bg-white/30\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-red-500/50 group-hover:bg-white/40\">\n                  <div className=\"text-2xl\">⚡</div>\n                </div>\n                <h3 className=\"text-lg font-bold text-gray-800 mb-2\">وظائف عاجلة</h3>\n                <p className=\"text-gray-600 mb-4 text-sm\">فرص تحتاج تقديم سريع</p>\n                <Link\n                  href=\"/jobs/individuals?urgent=true\"\n                  className=\"inline-block w-full py-2 bg-green-400 text-white rounded-lg hover:bg-green-500 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium text-sm border border-green-300\"\n                >\n                  عرض الآن\n                </Link>\n              </div>\n            </div>\n          </div>\n\n\n\n          {/* إدارة السيرة الذاتية */}\n          <div className=\"bg-white/20 backdrop-blur-sm rounded-2xl shadow-xl p-8 mb-12 border border-white/30\">\n            <div className=\"text-center mb-8\">\n              <div className=\"w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg\">\n                <img\n                  src=\"/images/jobs/Jobs -Personals/icons/settings_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png\"\n                  alt=\"إدارة\"\n                  className=\"w-10 h-10 opacity-80\"\n                  style={{\n                    filter: 'drop-shadow(0 0 8px rgba(147, 51, 234, 0.6))'\n                  }}\n                />\n              </div>\n              <h2 className=\"text-2xl font-bold text-gray-800 mb-2\">إدارة السيرة الذاتية</h2>\n              <p className=\"text-gray-600\">تحكم كامل في سيرتك الذاتية الاحترافية</p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              <div className=\"bg-white/30 backdrop-blur-sm rounded-xl shadow-md p-8 text-center hover:shadow-lg transition-all duration-300 border border-white/40 group hover:bg-white/40 active:shadow-lg active:shadow-green-400/50 active:scale-105\">\n                <div className=\"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 active:scale-125 active:shadow-lg active:shadow-green-400/50 active:bg-green-100/30 transition-all duration-300\">\n                  <img\n                    src=\"/images/jobs/Jobs -Personals/icons/edit_square_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png\"\n                    alt=\"تعديل\"\n                    className=\"w-6 h-6 opacity-80 group-hover:opacity-100 transition-opacity\"\n                    style={{\n                      filter: 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.6))'\n                    }}\n                  />\n                </div>\n                <h3 className=\"text-lg font-bold text-gray-800 mb-2\">تعديل السيرة الذاتية</h3>\n                <p className=\"text-gray-600 mb-4 text-sm\">قم بتحديث وتعديل سيرتك الذاتية</p>\n                <Link\n                  href=\"/resume/my-resume\"\n                  className=\"inline-block w-full py-2 bg-green-400 text-white rounded-lg hover:bg-green-500 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium text-sm border border-green-300\"\n                >\n                  تعديل الآن\n                </Link>\n              </div>\n\n              <div className=\"bg-white/30 backdrop-blur-sm rounded-xl shadow-md p-8 text-center hover:shadow-lg transition-all duration-300 border border-white/40 group hover:bg-white/40 active:shadow-lg active:shadow-green-400/50 active:scale-105\">\n                <div className=\"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 active:scale-125 active:shadow-lg active:shadow-green-400/50 active:bg-green-100/30 transition-all duration-300\">\n                  <img\n                    src=\"/images/jobs/Jobs -Personals/icons/print_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png\"\n                    alt=\"طباعة\"\n                    className=\"w-6 h-6 opacity-80 group-hover:opacity-100 transition-opacity\"\n                    style={{\n                      filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.6))'\n                    }}\n                  />\n                </div>\n                <h3 className=\"text-lg font-bold text-gray-800 mb-2\">طباعة السيرة الذاتية</h3>\n                <p className=\"text-gray-600 mb-4 text-sm\">اطبع سيرتك الذاتية بتنسيق احترافي</p>\n                <button\n                  onClick={() => {\n                    const savedResume = localStorage.getItem('userResume');\n                    if (savedResume) {\n                      const resume = JSON.parse(savedResume);\n                      const printWindow = window.open('', '_blank');\n                      if (printWindow) {\n                        printWindow.document.write(`\n                          <!DOCTYPE html>\n                          <html dir=\"rtl\">\n                            <head>\n                              <title>السيرة الذاتية - ${resume.personalInfo?.firstName} ${resume.personalInfo?.lastName}</title>\n                              <meta charset=\"UTF-8\">\n                              <style>\n                                body {\n                                  font-family: 'Arial', sans-serif;\n                                  margin: 20px;\n                                  line-height: 1.6;\n                                  color: #333;\n                                  direction: rtl;\n                                }\n                                .header {\n                                  text-align: center;\n                                  border-bottom: 2px solid #333;\n                                  padding-bottom: 20px;\n                                  margin-bottom: 30px;\n                                }\n                                .name {\n                                  font-size: 28px;\n                                  font-weight: bold;\n                                  margin-bottom: 10px;\n                                }\n                                .mycv-logo {\n                                  position: fixed;\n                                  bottom: 20px;\n                                  left: 20px;\n                                  opacity: 0.6;\n                                  z-index: 1000;\n                                }\n                                @media print {\n                                  .mycv-logo { display: block !important; }\n                                  body { margin: 15px; }\n                                }\n                              </style>\n                            </head>\n                            <body>\n                              <div class=\"header\">\n                                <div class=\"name\">${resume.personalInfo?.firstName} ${resume.personalInfo?.lastName}</div>\n                                <div class=\"title\">${resume.personalInfo?.title || ''}</div>\n                                <div class=\"contact\">\n                                  ${resume.contactInfo?.phone || ''} | ${resume.contactInfo?.email || ''} | ${resume.contactInfo?.city || ''}\n                                </div>\n                              </div>\n\n                              ${resume.personalInfo?.summary ? `\n                                <div class=\"section\">\n                                  <div class=\"section-title\">نبذة شخصية</div>\n                                  <p>${resume.personalInfo.summary}</p>\n                                </div>\n                              ` : ''}\n\n                              <div class=\"mycv-logo\">\n                                <img src=\"/images/MyCV Logo.jpg\" alt=\"MyCv\" style=\"width: 50px; height: auto;\" />\n                              </div>\n                            </body>\n                          </html>\n                        `);\n                        printWindow.document.close();\n                        printWindow.print();\n                      }\n                    } else {\n                      alert('لا توجد سيرة ذاتية محفوظة للطباعة');\n                    }\n                  }}\n                  className=\"inline-block w-full py-2 bg-green-400 text-white rounded-lg hover:bg-green-500 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium text-sm border border-green-300\"\n                >\n                  طباعة الآن\n                </button>\n              </div>\n\n              <div className=\"bg-white/30 backdrop-blur-sm rounded-xl shadow-md p-8 text-center hover:shadow-lg transition-all duration-300 border border-white/40 group hover:bg-white/40 active:shadow-lg active:shadow-green-400/50 active:scale-105\">\n                <div className=\"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 active:scale-125 active:shadow-lg active:shadow-green-400/50 active:bg-green-100/30 transition-all duration-300\">\n                  <img\n                    src=\"/images/jobs/Jobs -Personals/icons/demography_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png\"\n                    alt=\"قوالب\"\n                    className=\"w-6 h-6 opacity-80 group-hover:opacity-100 transition-opacity\"\n                    style={{\n                      filter: 'drop-shadow(0 0 8px rgba(147, 51, 234, 0.6))'\n                    }}\n                  />\n                </div>\n                <h3 className=\"text-lg font-bold text-gray-800 mb-2\">قوالب السيرة الذاتية</h3>\n                <p className=\"text-gray-600 mb-4 text-sm\">اختر من مجموعة متنوعة من القوالب</p>\n                <Link\n                  href=\"/resume/templates\"\n                  className=\"inline-block w-full py-2 bg-green-400 text-white rounded-lg hover:bg-green-500 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium text-sm border border-green-300\"\n                >\n                  اختر قالب\n                </Link>\n              </div>\n            </div>\n          </div>\n\n          {/* شعار MyCv */}\n          <div className=\"mt-16 text-center\">\n            <div className=\"bg-white/20 backdrop-blur-sm rounded-xl p-6 border border-white/30 inline-block\">\n              <div className=\"inline-flex items-center gap-3 text-gray-700\">\n                <span className=\"font-medium\">مدعوم من قبل</span>\n                <div className=\"bg-white/30 backdrop-blur-sm rounded-lg p-2\">\n                  <MyCvLogo size=\"sm\" />\n                </div>\n                <span className=\"font-medium\">- منصة متكاملة للسير الذاتية والتوظيف</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </main>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAGA;AACA;AATA;;;;;;;;AAWA,yBAAyB;AACzB,MAAM,aAA2B;IAC/B;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,OAAO;QACP,YAAY;QACZ,UAAU;QACV,UAAU;QACV,WAAW;QACX,aAAa;YACX,KAAK;YACL,KAAK;YACL,UAAU;YACV,QAAQ;QACV;QACA,aAAa;QACb,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;QACD,kBAAkB;YAChB;YACA;YACA;SACD;QACD,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,QAAQ;YAAC;YAAY;YAAc;YAAS;YAAW;SAAM;QAC7D,iBAAiB;QACjB,iBAAiB;YAAE,KAAK;YAAG,KAAK;QAAE;QAClC,gBAAgB;QAChB,qBAAqB;QACrB,YAAY;QACZ,QAAQ;QACR,mBAAmB;QACnB,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACrD,QAAQ;QACR,UAAU;QACV,UAAU;QACV,UAAU;QACV,iBAAiB;QACjB,aAAa;YAAE,KAAK;YAAG,KAAK;QAAQ;QACpC,UAAU;QACV,QAAQ;IACV;IAEA,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,kHAAA,CAAA,WAAQ,CAAC,UAAU,CAAC,MAAM;QAC3C,gBAAgB;IAClB,GAAG;QAAC;QAAM;KAAQ;IAElB,MAAM,sBAAsB,CAAC;QAC3B,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAM;;;;;0BAEP,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,KAAI;4BACJ,KAAI;4BACJ,WAAU;4BACV,OAAO;gCACL,QAAQ;4BACV;;;;;;;;;;;kCAIJ,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;gDACV,OAAO;oDACL,QAAQ;gDACV;;;;;;0DAEF,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;;;;;;;kDAEnD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAIvC,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;wDACV,OAAO;4DACL,QAAQ;wDACV;;;;;;;;;;;8DAGJ,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;kDAOL,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;wDACV,OAAO;4DACL,QAAQ;wDACV;;;;;;;;;;;8DAGJ,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;kDAOL,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;wDACV,OAAO;4DACL,QAAQ;wDACV;;;;;;;;;;;8DAGJ,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;kDAOL,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;8DAE5B,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAC1C,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;0CAUP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;oDACV,OAAO;wDACL,QAAQ;oDACV;;;;;;;;;;;0DAGJ,8OAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAG/B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,KAAI;4DACJ,KAAI;4DACJ,WAAU;4DACV,OAAO;gEACL,QAAQ;4DACV;;;;;;;;;;;kEAGJ,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAC1C,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;0DAKH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,KAAI;4DACJ,KAAI;4DACJ,WAAU;4DACV,OAAO;gEACL,QAAQ;4DACV;;;;;;;;;;;kEAGJ,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAC1C,8OAAC;wDACC,SAAS;4DACP,MAAM,cAAc,aAAa,OAAO,CAAC;4DACzC,IAAI,aAAa;gEACf,MAAM,SAAS,KAAK,KAAK,CAAC;gEAC1B,MAAM,cAAc,OAAO,IAAI,CAAC,IAAI;gEACpC,IAAI,aAAa;oEACf,YAAY,QAAQ,CAAC,KAAK,CAAC,CAAC;;;;sDAIE,EAAE,OAAO,YAAY,EAAE,UAAU,CAAC,EAAE,OAAO,YAAY,EAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAoCtE,EAAE,OAAO,YAAY,EAAE,UAAU,CAAC,EAAE,OAAO,YAAY,EAAE,SAAS;mDACjE,EAAE,OAAO,YAAY,EAAE,SAAS,GAAG;;kCAEpD,EAAE,OAAO,WAAW,EAAE,SAAS,GAAG,GAAG,EAAE,OAAO,WAAW,EAAE,SAAS,GAAG,GAAG,EAAE,OAAO,WAAW,EAAE,QAAQ,GAAG;;;;8BAI/G,EAAE,OAAO,YAAY,EAAE,UAAU,CAAC;;;qCAG3B,EAAE,OAAO,YAAY,CAAC,OAAO,CAAC;;8BAErC,CAAC,GAAG,GAAG;;;;;;;wBAOb,CAAC;oEACD,YAAY,QAAQ,CAAC,KAAK;oEAC1B,YAAY,KAAK;gEACnB;4DACF,OAAO;gEACL,MAAM;4DACR;wDACF;wDACA,WAAU;kEACX;;;;;;;;;;;;0DAKH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,KAAI;4DACJ,KAAI;4DACJ,WAAU;4DACV,OAAO;gEACL,QAAQ;4DACV;;;;;;;;;;;kEAGJ,8OAAC;wDAAG,WAAU;kEAAuC;;;;;;kEACrD,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;kEAC1C,8OAAC,4JAAA,CAAA,UAAI;wDACH,MAAK;wDACL,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;;;0CAQP,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAc;;;;;;0DAC9B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8HAAA,CAAA,UAAQ;oDAAC,MAAK;;;;;;;;;;;0DAEjB,8OAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOxC,8OAAC,4HAAA,CAAA,UAAM;;;;;;;;;;;AAGb"}}, {"offset": {"line": 8256, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}