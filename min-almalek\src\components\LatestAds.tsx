'use client';

import Link from 'next/link';
import SafeNavigationButton from './SafeNavigationButton';
import { AdBadge } from '@/components/VerificationBadge';
import { determineUserBadge } from '@/lib/verification';

const latestAds = [
  {
    id: 101,
    title: 'محل تجاري للإيجار في دمشق',
    price: '300,000',
    currency: 'ل.س',
    location: 'دمشق - الشعلان',
    category: 'عقارات',
    postedDate: 'منذ 15 دقيقة',
    isNew: true,
    seller: {
      name: 'شركة العقارات الذهبية',
      subscription: 'business-enterprise',
      adsCount: 180,
      rating: 4.9,
      membershipMonths: 20,
      isBusinessVerified: true
    }
  },
  {
    id: 102,
    title: 'Toyota Camry 2019 نظيفة جداً',
    price: '28,000',
    currency: '$',
    location: 'حلب - الحمدانية',
    category: 'سيارات',
    postedDate: 'منذ 30 دقيقة',
    isNew: true,
    seller: {
      name: 'أحمد الخليل',
      subscription: 'premium',
      adsCount: 12,
      rating: 4.4,
      membershipMonths: 6,
      isBusinessVerified: false
    }
  },
  {
    id: 103,
    title: 'مدرس رياضيات خصوصي',
    price: '15,000',
    currency: 'ل.س',
    location: 'دمشق - المزة',
    category: 'خدمات',
    postedDate: 'منذ 45 دقيقة',
    isNew: true,
    seller: {
      name: 'محمد الأستاذ',
      subscription: 'basic',
      adsCount: 5,
      rating: 4.3,
      membershipMonths: 2,
      isBusinessVerified: false
    }
  },
  {
    id: 104,
    title: 'Samsung Galaxy S24 Ultra',
    price: '950',
    currency: '$',
    location: 'حمص - الوعر',
    category: 'إلكترونيات',
    postedDate: 'منذ ساعة',
    isNew: true,
    seller: {
      name: 'متجر التقنية الحديثة',
      subscription: 'business-professional',
      adsCount: 95,
      rating: 4.7,
      membershipMonths: 14,
      isBusinessVerified: true
    }
  },
  {
    id: 105,
    title: 'غرفة نوم تركية جديدة',
    price: '450,000',
    currency: 'ل.س',
    location: 'اللاذقية - الزراعة',
    category: 'أثاث',
    postedDate: 'منذ ساعة ونصف',
    isNew: false,
    seller: {
      name: 'معرض الأثاث التركي',
      subscription: 'business-starter',
      adsCount: 35,
      rating: 4.5,
      membershipMonths: 8,
      isBusinessVerified: true
    }
  },
  {
    id: 106,
    title: 'مطلوب سكرتيرة طبية',
    price: '400,000',
    currency: 'ل.س',
    location: 'دمشق - أبو رمانة',
    category: 'وظائف',
    postedDate: 'منذ ساعتين',
    isNew: false,
    seller: {
      name: 'عيادة الدكتور أحمد',
      subscription: 'premium',
      adsCount: 8,
      rating: 4.6,
      membershipMonths: 4,
      isBusinessVerified: false
    }
  },
  {
    id: 107,
    title: 'دراجة هوائية جبلية',
    price: '180,000',
    currency: 'ل.س',
    location: 'حماة - الحميدية',
    category: 'رياضة',
    postedDate: 'منذ ساعتين',
    isNew: false,
    seller: {
      name: 'سارة محمد',
      subscription: 'basic',
      adsCount: 3,
      rating: 4.1,
      membershipMonths: 1,
      isBusinessVerified: false
    }
  },
  {
    id: 108,
    title: 'كتب جامعية - كلية الطب',
    price: '120,000',
    currency: 'ل.س',
    location: 'دمشق - المزة',
    category: 'كتب',
    postedDate: 'منذ 3 ساعات',
    isNew: false,
    seller: {
      name: 'طالب طب',
      subscription: 'basic',
      adsCount: 2,
      rating: 4.0,
      membershipMonths: 0.5,
      isBusinessVerified: false
    }
  }
];

const LatestAds = () => {

  return (
    <section className="py-12 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-3xl font-bold text-gray-800 mb-2">أحدث الإعلانات</h2>
            <p className="text-gray-600">آخر الإعلانات المضافة إلى الموقع</p>
          </div>
          <Link
            href="/latest"
            className="text-primary-600 hover:text-primary-700 font-medium flex items-center gap-2"
          >
            عرض الكل
            <span>←</span>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {latestAds.map((ad) => (
            <Link
              key={ad.id}
              href={`/ad/${ad.id}`}
              className="bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 p-4 border border-gray-100 hover:border-primary-200 group"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-semibold text-gray-800 group-hover:text-primary-600 transition-colors line-clamp-1">
                      {ad.title}
                    </h3>
                    {ad.isNew && (
                      <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                        جديد
                      </span>
                    )}
                  </div>

                  <div className="flex items-center justify-between mb-2">
                    <div className="text-lg font-bold text-primary-600">
                      {ad.price.toLocaleString()} {ad.currency}
                    </div>
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                      {ad.category}
                    </span>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500 mb-2">
                    <div className="flex items-center gap-1">
                      <span>📍</span>
                      <span>{ad.location}</span>
                    </div>
                    <span>{ad.postedDate}</span>
                  </div>

                  {/* معلومات البائع مع شارة التوثيق */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-gray-500">بواسطة:</span>
                      <span className="text-xs font-medium text-gray-700">{ad.seller.name}</span>
                    </div>
                    <AdBadge
                      userBadges={determineUserBadge(
                        ad.seller.subscription,
                        ad.seller.adsCount,
                        ad.seller.rating,
                        ad.seller.membershipMonths,
                        ad.seller.isBusinessVerified
                      )}
                      size="xs"
                    />
                  </div>
                </div>

                <div className="mr-4 flex-shrink-0">
                  <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                    <span className="text-gray-400 text-2xl">🖼️</span>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Live Updates Banner */}
        <div className="mt-8 bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
              <div>
                <h3 className="font-semibold text-lg">تحديثات مباشرة</h3>
                <p className="text-green-100">يتم إضافة إعلانات جديدة كل دقيقة</p>
              </div>
            </div>
            <Link
              href="/live-updates"
              className="bg-white text-green-600 px-4 py-2 rounded-lg font-medium hover:bg-green-50 transition-colors"
            >
              مشاهدة مباشرة
            </Link>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
          <SafeNavigationButton
            href="/add-ad"
            className="bg-orange-500 text-white p-6 rounded-lg hover:bg-orange-600 transition-colors text-center group"
          >
            <div className="text-3xl mb-2">
              <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 24 24">
                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" fillOpacity="0.8"/>
                <path d="M15,13H13V11H11V13H9V15H11V17H13V15H15V13Z" fillOpacity="1"/>
              </svg>
            </div>
            <h3 className="font-semibold text-lg mb-1">أضف إعلانك</h3>
            <p className="text-orange-100 text-sm">انشر إعلانك بسهولة</p>
          </SafeNavigationButton>

          <Link
            href="/search"
            className="bg-blue-500 text-white p-6 rounded-lg hover:bg-blue-600 transition-colors text-center group"
          >
            <div className="text-3xl mb-2">
              <svg className="w-8 h-8 mx-auto" fill="currentColor" viewBox="0 0 24 24">
                <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" fillOpacity="0.8"/>
              </svg>
            </div>
            <h3 className="font-semibold text-lg mb-1">بحث متقدم</h3>
            <p className="text-blue-100 text-sm">ابحث بدقة أكبر</p>
          </Link>

          <Link
            href="/alerts"
            className="bg-purple-500 text-white p-6 rounded-lg hover:bg-purple-600 transition-colors text-center group"
          >
            <div className="text-3xl mb-2">🔔</div>
            <h3 className="font-semibold text-lg mb-1">تنبيهات</h3>
            <p className="text-purple-100 text-sm">احصل على تنبيهات</p>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default LatestAds;
