'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ImageUpload from '@/components/ImageUpload';
import SubscriptionLimits from '@/components/SubscriptionLimits';
import CarSpecificFields from '@/components/CarSpecificFields';
import CarSummary from '@/components/CarSummary';
import RealEstateSpecificFields from '@/components/RealEstateSpecificFields';
import RealEstateSummary from '@/components/RealEstateSummary';
import PaymentProcessor from '@/components/PaymentProcessor';
import ContactButtons, { ContactInfo } from '@/components/ContactButtons';
import AlertModal from '@/components/AlertModal';
import CashAppLogo from '@/components/CashAppLogo';
import { INDIVIDUAL_PLANS, BUSINESS_PLANS, formatPrice } from '@/lib/pricing';
import { COMPANY_CONTACT } from '@/lib/contact';
import VerificationBadge from '@/components/VerificationBadge';
import { determineUserBadge } from '@/lib/verification';
import { useNotificationHelpers } from '@/hooks/useNotificationHelpers';

interface AdFormData {
  title: string;
  description: string;
  price: number;
  currency: 'SYP' | 'USD' | 'EUR';
  category: string;
  subcategory: string;
  condition: 'new' | 'used' | 'refurbished' | 'renovated';
  location: {
    governorate: string;
    city: string;
    area: string;
  };
  contactPhone: string;
  contactEmail: string;
  images: File[];
  adType: 'free' | 'featured' | 'premium' | '';
  acceptTerms: boolean;
  selectedPlan?: string;
  realEstateOffice?: string;
  carData?: {
    listingType?: 'sale' | 'rent' | 'exchange';
    rentPeriod?: 'daily' | 'weekly' | 'monthly';
    carType?: 'private' | 'commercial';
    transmission?: 'automatic' | 'manual' | 'hybrid';
    year?: number;
    brand?: string;
    model?: string;
    fuelType?: 'gasoline' | 'diesel' | 'electric' | 'hybrid';
    mileage?: number;
    engineCapacity?: string;
    features?: string[];
    insurance?: 'full' | 'basic' | 'none';
    insuranceDetails?: string;
    exchangeDetails?: string;
  };
  realEstateData?: {
    listingType?: 'sale' | 'rent' | 'exchange';
    rentPeriod?: 'daily' | 'weekly' | 'monthly' | 'yearly';
    propertyType?: string;
    area?: number;
    streetArea?: string;
    rooms?: string | number;
    bathrooms?: number;
    floor?: string;
    condition?: string;
    features?: string[];
    exchangeDetails?: string;
  };
}

export default function AddAdPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { notifyAdPublished, notifyAdUnderReview, notifyPaymentSuccess } = useNotificationHelpers();
  const [currentStep, setCurrentStep] = useState(1);
  const [userType, setUserType] = useState<'individual' | 'business' | 'real-estate-office'>('individual');
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const [planType, setPlanType] = useState<'individual' | 'business'>('individual');
  const [isClient, setIsClient] = useState(false);
  const [forceUpdate, setForceUpdate] = useState(0);
  const [selectedRealEstateOffice, setSelectedRealEstateOffice] = useState<string>('');


  const [showPayment, setShowPayment] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [paymentFormData, setPaymentFormData] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    email: '',
    phone: ''
  });
  const [isDragOver, setIsDragOver] = useState(false);
  const [draggedImageIndex, setDraggedImageIndex] = useState<number | null>(null);
  const [alertModal, setAlertModal] = useState<{
    isOpen: boolean;
    title: string;
    message: string;
    type: 'warning' | 'error' | 'success' | 'info';
    icon?: string;
  }>({
    isOpen: false,
    title: '',
    message: '',
    type: 'warning'
  });

  // حالات التمرير الذكي (للتمرير المخصص لهذه الصفحة فقط)
  const [lastScrollY, setLastScrollY] = useState(0);
  const [formData, setFormData] = useState<AdFormData>({
    title: '',
    description: '',
    price: 0,
    currency: 'SYP',
    category: '',
    subcategory: '',
    condition: 'new',
    location: {
      governorate: '',
      city: '',
      area: ''
    },
    contactPhone: '',
    contactEmail: '',
    images: [],
    adType: '',
    acceptTerms: false,
    carData: {
      features: []
    },
    realEstateData: {
      features: []
    }
  });

  const steps = [
    { number: 1, title: 'نوع الحساب', icon: '👤' },
    { number: 2, title: 'التصنيف', icon: '📂' },
    { number: 3, title: 'التفاصيل', icon: '📝' },
    { number: 4, title: 'نوع الإعلان', icon: '💎' },
    { number: 5, title: 'الصور', icon: '📸' },
    { number: 6, title: 'النشر', icon: '🚀' }
  ];

  // إصلاح مشكلة Hydration
  useEffect(() => {
    setIsClient(true);
  }, []);

  // مراقبة تغيير planType
  useEffect(() => {
    console.log('planType تغير إلى:', planType);
  }, [planType]);

  // تحديد الفئة من URL
  useEffect(() => {
    const categoryFromUrl = searchParams.get('category');
    if (categoryFromUrl) {
      setFormData(prev => ({
        ...prev,
        category: categoryFromUrl
      }));
      setCurrentStep(3); // الانتقال مباشرة لخطوة التفاصيل
      // تأخير التمرير للسماح للمكون بالتحديث
      setTimeout(() => {
        scrollToOptimalPosition(3);
      }, 200);
    }
  }, [searchParams]);

  // إعادة تعديل التمرير عند تغيير حجم الشاشة
  useEffect(() => {
    if (typeof window === 'undefined' || !isClient) return;

    const handleResize = () => {
      // تأخير قصير لتجنب التمرير المتكرر أثناء تغيير الحجم
      setTimeout(() => {
        scrollToOptimalPosition();
      }, 300);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [currentStep, isClient]);



  const categories = [
    { id: 'real-estate', name: 'العقارات', icon: '🏠' },
    { id: 'cars', name: 'السيارات', icon: '🚗' },
    { id: 'electronics', name: 'الإلكترونيات', icon: '📱' },
    { id: 'jobs', name: 'الوظائف', icon: '💼' },
    { id: 'services', name: 'الخدمات', icon: '🔧' },
    { id: 'fashion', name: 'الأزياء', icon: '👗' }
  ];

  const conditions = [
    { id: 'new', name: 'جديد' },
    { id: 'renovated', name: 'معفش' },
    { id: 'used', name: 'مستعمل' },
    { id: 'refurbished', name: 'مجدد' }
  ];

  const currencies = [
    { id: 'SYP', name: 'ليرة سورية', symbol: 'ل.س' },
    { id: 'USD', name: 'دولار أمريكي', symbol: '$' },
    { id: 'EUR', name: 'يورو', symbol: '€' }
  ];

  const governorates = [
    { id: 'damascus', name: 'دمشق' },
    { id: 'damascus-countryside', name: 'ريف دمشق' },
    { id: 'aleppo', name: 'حلب' },
    { id: 'homs', name: 'حمص' },
    { id: 'hama', name: 'حماة' },
    { id: 'lattakia', name: 'اللاذقية' },
    { id: 'tartous', name: 'طرطوس' },
    { id: 'idlib', name: 'إدلب' },
    { id: 'daraa', name: 'درعا' },
    { id: 'sweida', name: 'السويداء' },
    { id: 'quneitra', name: 'القنيطرة' },
    { id: 'deir-ez-zor', name: 'دير الزور' },
    { id: 'raqqa', name: 'الرقة' },
    { id: 'hasaka', name: 'الحسكة' }
  ];

  const realEstateOffices = [
    {
      id: 'damascus-premium',
      name: 'مكتب دمشق العقاري المميز',
      location: 'دمشق - المالكي',
      rating: 4.8,
      properties: 150,
      verified: true,
      description: 'مكتب عقاري متخصص في العقارات الفاخرة في دمشق',
      phone: '+963 11 123 4567',
      established: 2015
    },
    {
      id: 'aleppo-elite',
      name: 'مكتب حلب النخبة العقاري',
      location: 'حلب - الفرقان',
      rating: 4.6,
      properties: 120,
      verified: true,
      description: 'خبرة واسعة في العقارات السكنية والتجارية',
      phone: '+963 21 987 6543',
      established: 2012
    },
    {
      id: 'homs-central',
      name: 'مكتب حمص المركزي للعقارات',
      location: 'حمص - الوعر',
      rating: 4.5,
      properties: 95,
      verified: true,
      description: 'متخصص في العقارات السكنية والاستثمارية',
      phone: '+963 31 555 7890',
      established: 2018
    },
    {
      id: 'lattakia-coast',
      name: 'مكتب الساحل العقاري',
      location: 'اللاذقية - الصليبة',
      rating: 4.7,
      properties: 80,
      verified: true,
      description: 'متخصص في العقارات الساحلية والمنتجعات',
      phone: '+963 41 333 2222',
      established: 2016
    },
    {
      id: 'damascus-countryside',
      name: 'مكتب ريف دمشق العقاري',
      location: 'ريف دمشق - جرمانا',
      rating: 4.4,
      properties: 110,
      verified: true,
      description: 'خدمات عقارية شاملة في ريف دمشق',
      phone: '+963 11 777 8888',
      established: 2017
    },
    {
      id: 'hama-green',
      name: 'مكتب حماة الأخضر',
      location: 'حماة - الحاضر',
      rating: 4.3,
      properties: 70,
      verified: true,
      description: 'مكتب عقاري موثوق مع خدمة عملاء ممتازة',
      phone: '+963 33 444 5555',
      established: 2019
    }
  ];

  const scrollToOptimalPosition = (step?: number) => {
    // التحقق من وجود window (client-side only)
    if (typeof window === 'undefined' || !isClient) return;

    // تأخير قصير للسماح للمحتوى بالتحديث أولاً
    setTimeout(() => {
      const currentStepNumber = step || currentStep;

      // البحث عن عنصر محتوى الخطوة
      const stepContentElement = document.querySelector('.step-content');
      const headerElement = document.querySelector('header');
      const progressElement = document.querySelector('.progress-bar');

      if (stepContentElement && headerElement) {
        // حساب الموضع بناءً على العناصر الفعلية
        const headerHeight = headerElement.offsetHeight;
        const stepContentTop = stepContentElement.offsetTop;

        // تحديد نوع الجهاز لتخصيص التمرير بدقة
        const screenWidth = window.innerWidth;
        const isMobile = screenWidth < 768;
        const isTablet = screenWidth >= 768 && screenWidth < 1024;
        const isDesktop = screenWidth >= 1024;
        const isLargeDesktop = screenWidth >= 1440;

        // حساب المسافة الإضافية بناءً على الجهاز ونوع الخطوة
        let extraOffset = 20;
        let deviceMultiplier = 1;

        // تعديل المضاعف حسب نوع الجهاز
        if (isMobile) {
          deviceMultiplier = 1.2; // مسافة أكبر للموبايل
        } else if (isTablet) {
          deviceMultiplier = 1.1; // مسافة متوسطة للتابلت
        } else if (isLargeDesktop) {
          deviceMultiplier = 0.8; // مسافة أقل للشاشات الكبيرة
        }

        // تعديل المسافة حسب نوع الخطوة مع مراعاة الجهاز
        switch (currentStepNumber) {
          case 1: // نوع الحساب - خطوة بسيطة
            extraOffset = Math.round((isMobile ? 40 : isTablet ? 50 : 60) * deviceMultiplier);
            break;
          case 2: // التصنيف - شبكة التصنيفات
            extraOffset = Math.round((isMobile ? 35 : isTablet ? 45 : 55) * deviceMultiplier);
            break;
          case 3: // التفاصيل - نماذج طويلة
            extraOffset = Math.round((isMobile ? 50 : isTablet ? 60 : 70) * deviceMultiplier);
            break;
          case 4: // نوع الإعلان - باقات
            extraOffset = Math.round((isMobile ? 60 : isTablet ? 70 : 80) * deviceMultiplier);
            break;
          case 5: // الصور - منطقة رفع
            extraOffset = Math.round((isMobile ? 45 : isTablet ? 55 : 65) * deviceMultiplier);
            break;
          case 6: // النشر - ملخص نهائي
            extraOffset = Math.round((isMobile ? 70 : isTablet ? 80 : 90) * deviceMultiplier);
            break;
          default:
            extraOffset = Math.round((isMobile ? 40 : isTablet ? 50 : 60) * deviceMultiplier);
        }

        // حساب الموضع النهائي مع ضمان عدم التمرير فوق الحد المطلوب
        const scrollPosition = Math.max(0, stepContentTop - headerHeight - extraOffset);

        // التمرير إلى الموضع المحسوب مع انسيابية محسنة
        window.scrollTo({
          top: scrollPosition,
          behavior: 'smooth'
        });
      } else {
        // fallback محسن للطريقة القديمة إذا لم نجد العناصر
        const isMobile = window.innerWidth < 768;
        const isTablet = window.innerWidth >= 768 && window.innerWidth < 1024;

        const headerHeight = isMobile ? 70 : isTablet ? 80 : 90;
        const progressBarHeight = isMobile ? 140 : isTablet ? 160 : 180;
        const baseOffset = headerHeight + progressBarHeight;
        const scrollPosition = Math.max(0, baseOffset - (isMobile ? 30 : isTablet ? 40 : 50));

        window.scrollTo({
          top: scrollPosition,
          behavior: 'smooth'
        });
      }
    }, 250); // زيادة التأخير قليلاً لضمان تحديث المحتوى بشكل كامل
  };



  const handleNext = () => {
    if (currentStep < 6) {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      scrollToOptimalPosition(nextStep);
    }
  };

  const handlePrev = () => {
    if (currentStep > 1) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      scrollToOptimalPosition(prevStep);
    }
  };

  const handlePlanSelect = (planId: string) => {
    setSelectedPlan(planId);
    setFormData(prev => ({
      ...prev,
      selectedPlan: planId,
      adType: planId === 'individual-free' ? 'free' : 'featured'
    }));
  };

  const handleRealEstateOfficeSelect = (officeId: string) => {
    setSelectedRealEstateOffice(officeId);
    setFormData(prev => ({
      ...prev,
      realEstateOffice: officeId
    }));
  };

  const paymentMethods = [
    {
      id: 'visa',
      name: 'Visa',
      nameAr: 'فيزا كارد',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/4/41/Visa_Logo.png',
      description: 'ادفع بأمان باستخدام بطاقة الفيزا',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      id: 'mastercard',
      name: 'MasterCard',
      nameAr: 'ماستر كارد',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/0/04/Mastercard-logo.png',
      description: 'ادفع بأمان باستخدام بطاقة الماستر كارد',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    },
    {
      id: 'paypal',
      name: 'PayPal',
      nameAr: 'باي بال',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg',
      description: 'ادفع بسهولة عبر حسابك في PayPal',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      id: 'cashapp',
      name: 'Cash App',
      nameAr: 'تطبيق كاش',
      logo: null,
      description: 'ادفع بسرعة عبر تطبيق Cash App',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      id: 'applepay',
      name: 'Apple Pay',
      nameAr: 'آبل باي',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg',
      description: 'ادفع بأمان عبر Apple Pay',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200'
    }
  ];

  const handlePayment = () => {
    if (selectedPlan && selectedPlan !== 'individual-free') {
      setShowPayment(true);
      // تمرير خاص لصفحة الدفع
      setTimeout(() => {
        window.scrollTo({
          top: 50,
          behavior: 'smooth'
        });
      }, 100);
    } else {
      // إرسال الإعلان المجاني مباشرة
      handleSubmit();
    }
  };

  const handlePaymentMethodSelect = (methodId: string) => {
    setSelectedPaymentMethod(methodId);
  };

  const handlePaymentFormChange = (field: string, value: string) => {
    setPaymentFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const handlePaymentSubmit = () => {
    if (!selectedPaymentMethod) {
      showAlert(
        'يرجى اختيار وسيلة الدفع',
        'يجب اختيار وسيلة دفع لإتمام العملية',
        'warning',
        '💳'
      );
      return;
    }

    // التحقق من البيانات المطلوبة حسب وسيلة الدفع
    if ((selectedPaymentMethod === 'visa' || selectedPaymentMethod === 'mastercard') &&
        (!paymentFormData.cardNumber || !paymentFormData.expiryDate || !paymentFormData.cvv || !paymentFormData.cardholderName)) {
      showAlert(
        'بيانات البطاقة ناقصة',
        'يرجى إدخال جميع بيانات البطاقة المطلوبة',
        'warning',
        '💳'
      );
      return;
    }

    if (selectedPaymentMethod === 'paypal' && !paymentFormData.email) {
      showAlert(
        'بريد إلكتروني مطلوب',
        'يرجى إدخال البريد الإلكتروني المرتبط بحساب PayPal',
        'warning',
        '📧'
      );
      return;
    }

    // محاكاة معالجة الدفع
    showAlert(
      'جاري معالجة الدفع...',
      'يرجى الانتظار، جاري معالجة عملية الدفع',
      'info',
      '⏳'
    );

    setTimeout(() => {
      // إضافة إشعار نجاح الدفع
      const planPrice = selectedPlan === 'individual-basic' ? 25000 :
                       selectedPlan === 'individual-premium' ? 50000 :
                       selectedPlan === 'business-gold' ? 100000 :
                       selectedPlan === 'business-starter' ? 500000 :
                       selectedPlan === 'real-estate-office' ? 700000 :
                       selectedPlan === 'business-professional' ? 1000000 : 0;

      const planName = selectedPlan === 'individual-basic' ? 'الباقة الأساسية' :
                      selectedPlan === 'individual-premium' ? 'الباقة المميزة' :
                      selectedPlan === 'business-gold' ? 'باقة الأعمال الذهبية' :
                      selectedPlan === 'business-starter' ? 'باقة البداية' :
                      selectedPlan === 'real-estate-office' ? 'باقة المكتب العقاري' :
                      selectedPlan === 'business-professional' ? 'الباقة المتقدمة' : 'الباقة';

      notifyPaymentSuccess(planPrice, 'ل.س', planName);

      // إضافة إشعار نشر الإعلان
      notifyAdPublished(formData.title, 'temp-id-' + Date.now());

      showAlert(
        'تم إرسال الإعلان بنجاح!',
        'تم الدفع بنجاح! تم نشر إعلانك فوراً وهو الآن مرئي للمستخدمين. ستصلك رسالة تأكيد عبر البريد الإلكتروني.',
        'success',
        '🎉'
      );
      setTimeout(() => {
        router.push('/');
      }, 2000);
    }, 3000);
  };

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof AdFormData] as any),
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleImagesChange = (images: File[]) => {
    setFormData(prev => ({
      ...prev,
      images
    }));
  };

  const handleCarDataChange = (carData: any) => {
    setFormData(prev => ({
      ...prev,
      carData
    }));
  };

  const handleRealEstateDataChange = (realEstateData: any) => {
    setFormData(prev => ({
      ...prev,
      realEstateData
    }));
  };

  const showAlert = (title: string, message: string, type: 'warning' | 'error' | 'success' | 'info' = 'warning', icon?: string) => {
    setAlertModal({
      isOpen: true,
      title,
      message,
      type,
      icon
    });
  };

  const handleSubmit = () => {
    console.log('بيانات الإعلان:', formData);

    // إضافة إشعار حسب نوع الإعلان
    if (selectedPlan === 'individual-free') {
      // الإعلانات المجانية تحتاج مراجعة
      notifyAdUnderReview(formData.title, 'temp-id-' + Date.now());
    } else {
      // الإعلانات المدفوعة تنشر مباشرة
      notifyAdPublished(formData.title, 'temp-id-' + Date.now());
    }

    showAlert(
      'تم إرسال الإعلان بنجاح!',
      selectedPlan === 'individual-free'
        ? 'سيتم مراجعة إعلانك من قبل فريقنا ونشره خلال 24 ساعة. ستصلك رسالة تأكيد عبر البريد الإلكتروني.'
        : 'تم نشر إعلانك فوراً وهو الآن مرئي للمستخدمين. ستصلك رسالة تأكيد عبر البريد الإلكتروني.',
      'success',
      '🎉'
    );

    setTimeout(() => {
      router.push('/');
    }, 2000);
  };

  const handleImageFiles = (files: FileList | File[]) => {
    const fileArray = Array.from(files);
    if (fileArray.length === 0) return;

    const maxImages =
      selectedPlan === 'individual-free' ? 3 :
      selectedPlan === 'individual-basic' ? 3 :
      selectedPlan === 'individual-premium' ? 5 :
      selectedPlan === 'business-gold' ? 20 :
      selectedPlan === 'business-starter' ? 5 :
      selectedPlan === 'real-estate-office' ? 10 :
      selectedPlan === 'business-professional' ? 15 : 3;

    // فحص حجم الملفات ونوعها
    const invalidFiles: string[] = [];
    const validFiles = fileArray.filter(file => {
      // فحص نوع الملف
      if (!file.type.startsWith('image/')) {
        invalidFiles.push(`${file.name} (ليس صورة صالحة)`);
        return false;
      }

      // فحص حجم الملف
      if (file.size > 5 * 1024 * 1024) { // 5MB
        invalidFiles.push(`${file.name} (كبير جداً - الحد الأقصى 5MB)`);
        return false;
      }
      return true;
    });

    // إظهار رسالة للملفات غير الصالحة
    if (invalidFiles.length > 0) {
      showAlert(
        'ملفات غير صالحة',
        `لا يمكن رفع الملفات التالية:\n${invalidFiles.join('\n')}\n\nيرجى اختيار صور صالحة (JPG, PNG, GIF, WebP) بحجم أقل من 5MB.`,
        'error',
        '🚫'
      );
    }

    if (validFiles.length === 0) return;

    // دمج الصور الجديدة مع الموجودة
    const currentImages = formData.images || [];
    const availableSlots = maxImages - currentImages.length;

    if (availableSlots <= 0) {
      const planName =
        selectedPlan === 'individual-free' ? 'الباقة المجانية' :
        selectedPlan === 'individual-basic' ? 'الباقة الأساسية' :
        selectedPlan === 'individual-premium' ? 'الباقة المميزة' :
        selectedPlan === 'business-gold' ? 'باقة الأعمال' :
        selectedPlan === 'business-starter' ? 'باقة البداية' :
        selectedPlan === 'real-estate-office' ? 'مكتب عقاري' :
        selectedPlan === 'business-professional' ? 'الباقة المتقدمة' : 'الباقة الحالية';

      showAlert(
        'وصلت للحد الأقصى من الصور',
        `لقد وصلت للحد الأقصى من الصور (${maxImages}) في ${planName}.\n\nلإضافة المزيد من الصور، يمكنك:\n• حذف بعض الصور الموجودة\n• ترقية باقتك للحصول على المزيد من الصور`,
        'warning',
        '📸'
      );
      return;
    }

    const filesToAdd = validFiles.slice(0, availableSlots);
    const newImages = [...currentImages, ...filesToAdd];

    setFormData(prev => ({ ...prev, images: newImages }));

    if (validFiles.length > availableSlots) {
      showAlert(
        'تم إضافة جزء من الصور',
        `تم إضافة ${filesToAdd.length} صور فقط من أصل ${validFiles.length}.\n\nالحد الأقصى ${maxImages} صور في باقتك الحالية. لإضافة المزيد، قم بترقية باقتك.`,
        'info',
        '📊'
      );
    }
  };

  const handleImageDragStart = (e: React.DragEvent, index: number) => {
    setDraggedImageIndex(index);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', index.toString());
  };

  const handleImageDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleImageDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();

    if (draggedImageIndex === null || draggedImageIndex === dropIndex) {
      setDraggedImageIndex(null);
      return;
    }

    const newImages = [...formData.images];
    const draggedImage = newImages[draggedImageIndex];

    // إزالة الصورة من موقعها الأصلي
    newImages.splice(draggedImageIndex, 1);

    // إدراج الصورة في الموقع الجديد
    newImages.splice(dropIndex, 0, draggedImage);

    setFormData(prev => ({ ...prev, images: newImages }));
    setDraggedImageIndex(null);
  };

  const moveImage = (fromIndex: number, toIndex: number) => {
    if (fromIndex === toIndex || toIndex < 0 || toIndex >= formData.images.length) return;

    const newImages = [...formData.images];
    const movedImage = newImages.splice(fromIndex, 1)[0];
    newImages.splice(toIndex, 0, movedImage);

    setFormData(prev => ({ ...prev, images: newImages }));
  };

  const renderPaymentForm = () => {
    if (!selectedPaymentMethod) return null;

    const selectedMethod = paymentMethods.find(method => method.id === selectedPaymentMethod);

    return (
      <div className="mt-8 bg-gray-50 rounded-xl p-6">
        <div className="flex items-center gap-3 mb-6">
          <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center shadow-md">
            {selectedPaymentMethod === 'cashapp' ? (
              <CashAppLogo size="sm" />
            ) : selectedPaymentMethod === 'applepay' ? (
              <div className="flex items-center gap-1">
                <img src={selectedMethod?.logo} alt="Apple" className="w-3 h-4 object-contain" />
                <span className="text-xs font-semibold text-gray-800">Pay</span>
              </div>
            ) : (
              <img src={selectedMethod?.logo} alt={selectedMethod?.name} className="w-6 h-4 object-contain" />
            )}
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-800">{selectedMethod?.nameAr}</h3>
            <p className="text-sm text-gray-600">{selectedMethod?.description}</p>
          </div>
        </div>

        {/* نموذج بطاقة ائتمان (Visa/MasterCard) */}
        {(selectedPaymentMethod === 'visa' || selectedPaymentMethod === 'mastercard') && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">رقم البطاقة</label>
                <input
                  type="text"
                  placeholder="1234 5678 9012 3456"
                  value={paymentFormData.cardNumber}
                  onChange={(e) => handlePaymentFormChange('cardNumber', formatCardNumber(e.target.value))}
                  maxLength={19}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">اسم حامل البطاقة</label>
                <input
                  type="text"
                  placeholder="الاسم كما هو مكتوب على البطاقة"
                  value={paymentFormData.cardholderName}
                  onChange={(e) => handlePaymentFormChange('cardholderName', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الانتهاء</label>
                <input
                  type="text"
                  placeholder="MM/YY"
                  value={paymentFormData.expiryDate}
                  onChange={(e) => handlePaymentFormChange('expiryDate', formatExpiryDate(e.target.value))}
                  maxLength={5}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">CVV</label>
                <input
                  type="text"
                  placeholder="123"
                  value={paymentFormData.cvv}
                  onChange={(e) => handlePaymentFormChange('cvv', e.target.value.replace(/\D/g, '').substring(0, 4))}
                  maxLength={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>
          </div>
        )}

        {/* نموذج PayPal */}
        {selectedPaymentMethod === 'paypal' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني لحساب PayPal</label>
              <input
                type="email"
                placeholder="<EMAIL>"
                value={paymentFormData.email}
                onChange={(e) => handlePaymentFormChange('email', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-sm text-blue-700">
                🔒 ستتم إعادة توجيهك إلى موقع PayPal الآمن لإتمام عملية الدفع
              </p>
            </div>
          </div>
        )}

        {/* نموذج Cash App */}
        {selectedPaymentMethod === 'cashapp' && (
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف أو $Cashtag</label>
              <input
                type="text"
                placeholder="$username أو رقم الهاتف"
                value={paymentFormData.phone}
                onChange={(e) => handlePaymentFormChange('phone', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <p className="text-sm text-green-700">
                📱 ستتم إعادة توجيهك إلى تطبيق Cash App لإتمام عملية الدفع
              </p>
            </div>
          </div>
        )}

        {/* نموذج Apple Pay */}
        {selectedPaymentMethod === 'applepay' && (
          <div className="space-y-4">
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
              <div className="text-4xl mb-4">📱</div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">الدفع عبر Apple Pay</h3>
              <p className="text-sm text-gray-600 mb-4">
                استخدم Touch ID أو Face ID لإتمام عملية الدفع بأمان
              </p>
              <div className="bg-black text-white rounded-lg py-3 px-6 inline-flex items-center gap-2">
                <img src={selectedMethod?.logo} alt="Apple" className="w-4 h-5 object-contain" />
                <span className="font-semibold">Pay</span>
              </div>
            </div>
          </div>
        )}

        {/* أزرار التحكم */}
        <div className="flex gap-4 justify-end mt-6 pt-4 border-t border-gray-200">
          <button
            onClick={() => {
              setShowPaymentForm(false);
              setSelectedPaymentMethod('');
              setPaymentFormData({
                cardNumber: '',
                expiryDate: '',
                cvv: '',
                cardholderName: '',
                email: '',
                phone: ''
              });
              scrollToTop();
            }}
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            العودة
          </button>
          <button
            onClick={handlePaymentSubmit}
            className="px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-semibold"
          >
            💳 إتمام الدفع ({selectedPlan === 'individual-basic' ? '25,000' :
                           selectedPlan === 'individual-premium' ? '50,000' :
                           selectedPlan === 'business-gold' ? '100,000' :
                           selectedPlan === 'business-starter' ? '500,000' :
                           selectedPlan === 'real-estate-office' ? '700,000' :
                           selectedPlan === 'business-professional' ? '1,000,000' : '0'} ل.س)
          </button>
        </div>
      </div>
    );
  };

  const renderAdSummary = () => {
    return (
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-4 mb-6">
        <div className="flex items-center gap-2 mb-4">
          <span className="text-2xl">📋</span>
          <h3 className="text-lg font-bold text-blue-800">ملخص الإعلان</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* المعلومات الأساسية */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-800 text-sm border-b border-gray-200 pb-1">المعلومات الأساسية</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">العنوان:</span>
                <span className="font-medium text-gray-800 truncate max-w-32">{formData.title || 'غير محدد'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">الفئة:</span>
                <span className="font-medium text-gray-800">
                  {formData.category === 'cars' ? 'سيارات' :
                   formData.category === 'real-estate' ? 'عقارات' :
                   formData.category === 'electronics' ? 'إلكترونيات' :
                   formData.category === 'furniture' ? 'أثاث' :
                   formData.category === 'jobs' ? 'وظائف' : 'غير محدد'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">السعر:</span>
                <span className="font-medium text-gray-800">
                  {formData.price ? `${formData.price.toLocaleString()} ${
                    formData.currency === 'SYP' ? 'ل.س' :
                    formData.currency === 'USD' ? '$' : '€'
                  }` : 'غير محدد'}
                </span>
              </div>
            </div>
          </div>

          {/* معلومات إضافية */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-800 text-sm border-b border-gray-200 pb-1">معلومات إضافية</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">الموقع:</span>
                <span className="font-medium text-gray-800 truncate max-w-32">
                  {formData.location.governorate && formData.location.city
                    ? `${formData.location.governorate}`
                    : 'غير محدد'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">الصور:</span>
                <span className="font-medium text-gray-800">{formData.images.length} صورة</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">نوع الإعلان:</span>
                <span className="font-medium text-gray-800">
                  {selectedPlan === 'individual-free' ? 'مجاني' :
                   selectedPlan === 'individual-basic' ? 'أساسي' :
                   selectedPlan === 'individual-premium' ? 'مميز' :
                   selectedPlan === 'business-gold' ? 'ذهبي' :
                   selectedPlan === 'business-starter' ? 'بداية' :
                   selectedPlan === 'real-estate-office' ? 'عقاري' :
                   selectedPlan === 'business-professional' ? 'متقدم' : 'غير محدد'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">التكلفة:</span>
                <span className="font-medium text-primary-600">
                  {selectedPlan === 'individual-free' ? 'مجاناً' :
                   selectedPlan === 'individual-basic' ? '25,000 ل.س' :
                   selectedPlan === 'individual-premium' ? '50,000 ل.س' :
                   selectedPlan === 'business-gold' ? '100,000 ل.س' :
                   selectedPlan === 'business-starter' ? '500,000 ل.س' :
                   selectedPlan === 'real-estate-office' ? '700,000 ل.س' :
                   selectedPlan === 'business-professional' ? '1,000,000 ل.س' : '0 ل.س'}
                </span>
              </div>
            </div>
          </div>

          {/* معاينة الصور */}
          <div className="space-y-2">
            <h4 className="font-medium text-gray-800 text-sm border-b border-gray-200 pb-1">معاينة الصور</h4>
            {formData.images.length > 0 ? (
              <div className="grid grid-cols-4 gap-1">
                {formData.images.slice(0, 4).map((image, index) => {
                  const imageUrl = URL.createObjectURL(image);
                  return (
                    <div key={index} className="relative">
                      <div className="aspect-square w-full overflow-hidden rounded border border-gray-200 bg-gray-100">
                        <img
                          src={imageUrl}
                          alt={`صورة ${index + 1}`}
                          className="w-full h-full object-cover"
                          onLoad={() => {
                            setTimeout(() => URL.revokeObjectURL(imageUrl), 100);
                          }}
                        />
                      </div>
                      {index === 0 && (
                        <div className="absolute top-0 left-0 bg-green-500 text-white text-xs px-1 rounded-br">
                          رئيسية
                        </div>
                      )}
                      {index === 3 && formData.images.length > 4 && (
                        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center rounded">
                          <span className="text-white text-xs font-bold">+{formData.images.length - 4}</span>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-sm text-gray-500 text-center py-4">
                لا توجد صور
              </div>
            )}
          </div>
        </div>

        {/* الوصف المختصر */}
        {formData.description && (
          <div className="mt-4 pt-3 border-t border-gray-200">
            <div className="bg-white rounded-lg p-3 border border-gray-200">
              <p className="text-gray-700 text-sm leading-relaxed">
                {formData.description.length > 120
                  ? `${formData.description.substring(0, 120)}...`
                  : formData.description}
              </p>
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderCategoryStep = () => {
    // تحديد التصنيفات المتاحة حسب نوع المستخدم
    const availableCategories = userType === 'real-estate-office'
      ? categories.filter(category => category.id === 'real-estate')
      : categories;

    return (
      <div className="space-y-6">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-2">اختر التصنيف</h2>
          <p className="text-gray-600">
            {userType === 'real-estate-office'
              ? 'كمكتب عقاري، يمكنك نشر إعلانات عقارية فقط'
              : 'حدد تصنيف إعلانك'
            }
          </p>
        </div>

        {/* تنبيه للمكاتب العقارية */}
        {userType === 'real-estate-office' && (
          <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-xl p-6 mb-6">
            <div className="flex items-center gap-3 mb-4">
              <span className="text-2xl">🏘️</span>
              <h3 className="text-lg font-semibold text-orange-800">مكتب عقاري متخصص</h3>
            </div>
            <p className="text-orange-700 leading-relaxed">
              كمكتب عقاري، يمكنك نشر إعلانات عقارية فقط مع مميزات خاصة وشارة موثقة
            </p>
          </div>
        )}

        <div className={`grid gap-4 ${
          userType === 'real-estate-office'
            ? 'grid-cols-1 max-w-md mx-auto'
            : 'grid-cols-2 md:grid-cols-3'
        }`}>
          {availableCategories.map(category => (
            <div
              key={category.id}
              onClick={() => setFormData(prev => ({ ...prev, category: category.id }))}
              className={`p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 text-center ${
                formData.category === category.id
                  ? 'border-primary-500 bg-primary-50 shadow-lg'
                  : 'border-gray-200 hover:border-primary-300 hover:shadow-md'
              }`}
            >
              <div className="text-3xl mb-3">{category.icon}</div>
              <h3 className="font-semibold text-gray-800">{category.name}</h3>
              {userType === 'real-estate-office' && category.id === 'real-estate' && (
                <div className="mt-2">
                  <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded-full text-xs font-medium">
                    ✓ تخصصك
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="flex justify-between mt-8">
          <button
            onClick={handlePrev}
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
          >
            السابق
          </button>
          <button
            onClick={handleNext}
            disabled={!formData.category}
            className="px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold"
          >
            التالي
          </button>
        </div>
      </div>
    );
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">اختر نوع حسابك</h2>
              <p className="text-gray-600">حدد نوع الحساب المناسب لك</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* حساب فردي */}
              <div
                onClick={() => setUserType('individual')}
                className={`p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                  userType === 'individual'
                    ? 'border-blue-500 bg-blue-50 shadow-lg'
                    : 'border-gray-200 hover:border-blue-300 hover:shadow-md'
                }`}
              >
                <div className="text-center">
                  <div className="text-4xl mb-4">👤</div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">حساب فردي</h3>
                  <p className="text-sm text-gray-600 mb-4">للأفراد والاستخدام الشخصي</p>
                  <div className="space-y-2 text-xs text-gray-500">
                    <div>✓ إعلانات مجانية ومدفوعة</div>
                    <div>✓ سهولة في الاستخدام</div>
                    <div>✓ دعم فني أساسي</div>
                  </div>
                </div>
              </div>

              {/* حساب شركة */}
              <div
                onClick={() => setUserType('business')}
                className={`p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                  userType === 'business'
                    ? 'border-purple-500 bg-purple-50 shadow-lg'
                    : 'border-gray-200 hover:border-purple-300 hover:shadow-md'
                }`}
              >
                <div className="text-center">
                  <div className="text-4xl mb-4">🏢</div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">حساب شركة</h3>
                  <p className="text-sm text-gray-600 mb-4">للشركات والأعمال التجارية</p>
                  <div className="space-y-2 text-xs text-gray-500">
                    <div>✓ إعلانات مميزة فقط</div>
                    <div>✓ شارة شركة موثقة</div>
                    <div>✓ دعم فني متخصص</div>
                  </div>
                </div>
              </div>

              {/* مكتب عقاري */}
              <div
                onClick={() => setUserType('real-estate-office')}
                className={`p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                  userType === 'real-estate-office'
                    ? 'border-orange-500 bg-orange-50 shadow-lg'
                    : 'border-gray-200 hover:border-orange-300 hover:shadow-md'
                }`}
              >
                <div className="text-center">
                  <div className="text-4xl mb-4">🏘️</div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">مكتب عقاري</h3>
                  <p className="text-sm text-gray-600 mb-4">للمكاتب العقارية المتخصصة</p>
                  <div className="space-y-2 text-xs text-gray-500">
                    <div>✓ إعلانات عقارية فقط</div>
                    <div>✓ شارة مكتب عقاري موثق</div>
                    <div>✓ أدوات عقارية متقدمة</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-center mt-8">
              <button
                onClick={handleNext}
                disabled={!userType}
                className="px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold"
              >
                التالي
              </button>
            </div>
          </div>
        );
      case 2:
        return renderCategoryStep();
      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">تفاصيل الإعلان</h2>
              <p className="text-gray-600">أدخل تفاصيل إعلانك</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* عنوان الإعلان */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  عنوان الإعلان *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="اكتب عنوان جذاب لإعلانك"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>



              {/* السعر والعملة - للفئات الأخرى (غير السيارات والعقارات) */}
              {formData.category !== 'cars' && formData.category !== 'real-estate' && (
                <>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">السعر</label>
                    <input
                      type="number"
                      value={formData.price || ''}
                      onChange={(e) => handleInputChange('price', parseInt(e.target.value) || 0)}
                      placeholder="0"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">العملة</label>
                    <select
                      value={formData.currency}
                      onChange={(e) => handleInputChange('currency', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="SYP">ليرة سورية (ل.س)</option>
                      <option value="USD">دولار أمريكي ($)</option>
                      <option value="EUR">يورو (€)</option>
                    </select>
                  </div>
                </>
              )}

              {/* الوصف */}
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وصف الإعلان *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="اكتب وصف مفصل عن المنتج أو الخدمة"
                  rows={4}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>

              {/* الموقع */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المحافظة</label>
                <select
                  value={formData.location.governorate}
                  onChange={(e) => handleInputChange('location.governorate', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">اختر المحافظة</option>
                  {governorates.map(gov => (
                    <option key={gov.id} value={gov.id}>{gov.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المدينة أو الحي</label>
                <input
                  type="text"
                  value={formData.location.city}
                  onChange={(e) => handleInputChange('location.city', e.target.value)}
                  placeholder="اسم المدينة أو الحي"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              {/* معلومات التواصل */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
                <input
                  type="tel"
                  value={formData.contactPhone}
                  onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                  placeholder="+963 xxx xxx xxx"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                <input
                  type="email"
                  value={formData.contactEmail}
                  onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>

            {/* حقول متخصصة للسيارات */}
            {formData.category === 'cars' && (
              <div className="mt-8 p-6 bg-blue-50 rounded-xl border border-blue-200">
                <h3 className="text-lg font-semibold text-blue-800 mb-4 flex items-center gap-2">
                  🚗 تفاصيل السيارة
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* نوع الإعلان */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">نوع الإعلان</label>
                    <select
                      value={formData.carData?.listingType || ''}
                      onChange={(e) => handleInputChange('carData.listingType', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر نوع الإعلان</option>
                      <option value="sale">للبيع</option>
                      <option value="rent">للإيجار</option>
                      <option value="exchange">للمقايضة</option>
                    </select>
                  </div>

                  {/* فترة الإيجار */}
                  {formData.carData?.listingType === 'rent' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">فترة الإيجار</label>
                      <select
                        value={formData.carData?.rentPeriod || ''}
                        onChange={(e) => handleInputChange('carData.rentPeriod', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      >
                        <option value="">اختر فترة الإيجار</option>
                        <option value="daily">يومي</option>
                        <option value="weekly">أسبوعي</option>
                        <option value="monthly">شهري</option>
                      </select>
                    </div>
                  )}

                  {/* تفاصيل المقايضة */}
                  {formData.carData?.listingType === 'exchange' && (
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">تفاصيل المقايضة</label>
                      <textarea
                        value={formData.carData?.exchangeDetails || ''}
                        onChange={(e) => handleInputChange('carData.exchangeDetails', e.target.value)}
                        placeholder="اكتب ما تريد مقايضته بالسيارة (مثال: سيارة أخرى، عقار، إلخ)"
                        rows={3}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                      <p className="text-sm text-blue-600 mt-1 flex items-center gap-1">
                        <span>💡</span>
                        <span>حدد بوضوح ما تريد مقايضته وأي شروط إضافية</span>
                      </p>
                    </div>
                  )}

                  {/* نوع السيارة */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">نوع السيارة</label>
                    <select
                      value={formData.carData?.carType || ''}
                      onChange={(e) => handleInputChange('carData.carType', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر نوع السيارة</option>
                      <option value="private">خصوصي</option>
                      <option value="commercial">عمومي</option>
                    </select>
                  </div>

                  {/* ناقل الحركة */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">ناقل الحركة</label>
                    <select
                      value={formData.carData?.transmission || ''}
                      onChange={(e) => handleInputChange('carData.transmission', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر ناقل الحركة</option>
                      <option value="automatic">أوتوماتيك</option>
                      <option value="manual">يدوي</option>
                      <option value="hybrid">هجين</option>
                    </select>
                  </div>

                  {/* سنة الصنع */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">سنة الصنع</label>
                    <select
                      value={formData.carData?.year || ''}
                      onChange={(e) => handleInputChange('carData.year', parseInt(e.target.value))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر سنة الصنع</option>
                      {Array.from({ length: 54 }, (_, i) => 2024 - i).map(year => (
                        <option key={year} value={year}>{year}</option>
                      ))}
                    </select>
                  </div>

                  {/* الماركة */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الماركة</label>
                    <select
                      value={formData.carData?.brand || ''}
                      onChange={(e) => handleInputChange('carData.brand', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر الماركة</option>

                      {/* الماركات اليابانية */}
                      <optgroup label="الماركات اليابانية">
                        <option value="toyota">تويوتا (Toyota)</option>
                        <option value="honda">هوندا (Honda)</option>
                        <option value="nissan">نيسان (Nissan)</option>
                        <option value="mazda">مازدا (Mazda)</option>
                        <option value="mitsubishi">ميتسوبيشي (Mitsubishi)</option>
                        <option value="subaru">سوبارو (Subaru)</option>
                        <option value="suzuki">سوزوكي (Suzuki)</option>
                        <option value="lexus">لكزس (Lexus)</option>
                        <option value="infiniti">إنفينيتي (Infiniti)</option>
                        <option value="acura">أكورا (Acura)</option>
                        <option value="isuzu">إيسوزو (Isuzu)</option>
                        <option value="daihatsu">دايهاتسو (Daihatsu)</option>
                      </optgroup>

                      {/* الماركات الكورية */}
                      <optgroup label="الماركات الكورية">
                        <option value="hyundai">هيونداي (Hyundai)</option>
                        <option value="kia">كيا (Kia)</option>
                        <option value="genesis">جينيسيس (Genesis)</option>
                        <option value="daewoo">دايو (Daewoo)</option>
                        <option value="ssangyong">سانغ يونغ (SsangYong)</option>
                      </optgroup>

                      {/* الماركات الألمانية */}
                      <optgroup label="الماركات الألمانية">
                        <option value="bmw">بي إم دبليو (BMW)</option>
                        <option value="mercedes">مرسيدس بنز (Mercedes-Benz)</option>
                        <option value="audi">أودي (Audi)</option>
                        <option value="volkswagen">فولكس فاجن (Volkswagen)</option>
                        <option value="porsche">بورشه (Porsche)</option>
                        <option value="opel">أوبل (Opel)</option>
                        <option value="mini">ميني (MINI)</option>
                        <option value="smart">سمارت (Smart)</option>
                      </optgroup>

                      {/* الماركات الأمريكية */}
                      <optgroup label="الماركات الأمريكية">
                        <option value="ford">فورد (Ford)</option>
                        <option value="chevrolet">شيفروليه (Chevrolet)</option>
                        <option value="cadillac">كاديلاك (Cadillac)</option>
                        <option value="gmc">جي إم سي (GMC)</option>
                        <option value="buick">بويك (Buick)</option>
                        <option value="lincoln">لينكولن (Lincoln)</option>
                        <option value="chrysler">كرايسلر (Chrysler)</option>
                        <option value="dodge">دودج (Dodge)</option>
                        <option value="jeep">جيب (Jeep)</option>
                        <option value="ram">رام (RAM)</option>
                        <option value="tesla">تيسلا (Tesla)</option>
                      </optgroup>

                      {/* الماركات الفرنسية */}
                      <optgroup label="الماركات الفرنسية">
                        <option value="peugeot">بيجو (Peugeot)</option>
                        <option value="renault">رينو (Renault)</option>
                        <option value="citroen">ستروين (Citroën)</option>
                        <option value="dacia">داتشيا (Dacia)</option>
                      </optgroup>

                      {/* الماركات الإيطالية */}
                      <optgroup label="الماركات الإيطالية">
                        <option value="fiat">فيات (Fiat)</option>
                        <option value="alfa-romeo">ألفا روميو (Alfa Romeo)</option>
                        <option value="ferrari">فيراري (Ferrari)</option>
                        <option value="lamborghini">لامبورغيني (Lamborghini)</option>
                        <option value="maserati">مازيراتي (Maserati)</option>
                        <option value="lancia">لانتشيا (Lancia)</option>
                      </optgroup>

                      {/* الماركات البريطانية */}
                      <optgroup label="الماركات البريطانية">
                        <option value="land-rover">لاند روفر (Land Rover)</option>
                        <option value="jaguar">جاكوار (Jaguar)</option>
                        <option value="bentley">بنتلي (Bentley)</option>
                        <option value="rolls-royce">رولز رويس (Rolls-Royce)</option>
                        <option value="aston-martin">أستون مارتن (Aston Martin)</option>
                        <option value="lotus">لوتس (Lotus)</option>
                        <option value="mg">إم جي (MG)</option>
                      </optgroup>

                      {/* الماركات السويدية */}
                      <optgroup label="الماركات السويدية">
                        <option value="volvo">فولفو (Volvo)</option>
                        <option value="saab">ساب (Saab)</option>
                      </optgroup>

                      {/* الماركات التشيكية */}
                      <optgroup label="الماركات التشيكية">
                        <option value="skoda">سكودا (Škoda)</option>
                      </optgroup>

                      {/* الماركات الصينية */}
                      <optgroup label="الماركات الصينية">
                        <option value="geely">جيلي (Geely)</option>
                        <option value="chery">شيري (Chery)</option>
                        <option value="byd">بي واي دي (BYD)</option>
                        <option value="great-wall">جريت وول (Great Wall)</option>
                        <option value="haval">هافال (Haval)</option>
                        <option value="mg-motor">إم جي موتور (MG Motor)</option>
                        <option value="dongfeng">دونغ فنغ (Dongfeng)</option>
                        <option value="changan">تشانغان (Changan)</option>
                        <option value="faw">فاو (FAW)</option>
                        <option value="jac">جاك (JAC)</option>
                        <option value="lifan">ليفان (Lifan)</option>
                        <option value="brilliance">بريليانس (Brilliance)</option>
                        <option value="foton">فوتون (Foton)</option>
                        <option value="zotye">زوتي (Zotye)</option>
                        <option value="nio">نيو (NIO)</option>
                        <option value="xpeng">إكس بنغ (XPeng)</option>
                        <option value="li-auto">لي أوتو (Li Auto)</option>
                      </optgroup>

                      {/* الماركات الهندية */}
                      <optgroup label="الماركات الهندية">
                        <option value="tata">تاتا (Tata)</option>
                        <option value="mahindra">ماهيندرا (Mahindra)</option>
                        <option value="maruti-suzuki">ماروتي سوزوكي (Maruti Suzuki)</option>
                        <option value="bajaj">باجاج (Bajaj)</option>
                      </optgroup>

                      {/* الماركات الروسية */}
                      <optgroup label="الماركات الروسية">
                        <option value="lada">لادا (Lada)</option>
                        <option value="uaz">يو إيه زد (UAZ)</option>
                        <option value="gaz">جاز (GAZ)</option>
                      </optgroup>

                      {/* الماركات الإيرانية */}
                      <optgroup label="الماركات الإيرانية">
                        <option value="iran-khodro">إيران خودرو (Iran Khodro)</option>
                        <option value="saipa">سايبا (Saipa)</option>
                        <option value="pars-khodro">پارس خودرو (Pars Khodro)</option>
                      </optgroup>

                      {/* ماركات أخرى */}
                      <optgroup label="ماركات أخرى">
                        <option value="other">أخرى</option>
                      </optgroup>
                    </select>
                  </div>

                  {/* نوع الوقود */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">نوع الوقود</label>
                    <select
                      value={formData.carData?.fuelType || ''}
                      onChange={(e) => handleInputChange('carData.fuelType', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر نوع الوقود</option>
                      <option value="gasoline">بنزين</option>
                      <option value="diesel">ديزل</option>
                      <option value="electric">كهربائي</option>
                      <option value="hybrid">هجين</option>
                    </select>
                  </div>

                  {/* المسافة المقطوعة */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">المسافة المقطوعة (كم)</label>
                    <input
                      type="number"
                      value={formData.carData?.mileage || ''}
                      onChange={(e) => handleInputChange('carData.mileage', parseInt(e.target.value) || 0)}
                      placeholder="0"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  {/* قوة المحرك */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">قوة المحرك (cc)</label>
                    <select
                      value={formData.carData?.engineCapacity || ''}
                      onChange={(e) => handleInputChange('carData.engineCapacity', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر قوة المحرك</option>
                      <option value="800">800 cc</option>
                      <option value="1000">1000 cc</option>
                      <option value="1200">1200 cc</option>
                      <option value="1300">1300 cc</option>
                      <option value="1400">1400 cc</option>
                      <option value="1500">1500 cc</option>
                      <option value="1600">1600 cc</option>
                      <option value="1800">1800 cc</option>
                      <option value="2000">2000 cc</option>
                      <option value="2200">2200 cc</option>
                      <option value="2400">2400 cc</option>
                      <option value="2500">2500 cc</option>
                      <option value="2700">2700 cc</option>
                      <option value="3000">3000 cc</option>
                      <option value="3200">3200 cc</option>
                      <option value="3500">3500 cc</option>
                      <option value="3600">3600 cc</option>
                      <option value="4000">4000 cc</option>
                      <option value="4200">4200 cc</option>
                      <option value="4600">4600 cc</option>
                      <option value="5000">5000 cc</option>
                      <option value="5700">5700 cc</option>
                      <option value="6000">6000 cc</option>
                      <option value="6200">6200 cc</option>
                      <option value="other">أخرى</option>
                    </select>
                  </div>

                  {/* التأمين */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">حالة التأمين</label>
                    <select
                      value={formData.carData?.insurance || ''}
                      onChange={(e) => handleInputChange('carData.insurance', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر حالة التأمين</option>
                      <option value="full">تأمين شامل</option>
                      <option value="basic">تأمين أساسي</option>
                      <option value="none">بدون تأمين</option>
                    </select>
                  </div>

                  {/* تفاصيل التأمين */}
                  {formData.carData?.insurance && formData.carData?.insurance !== 'none' && (
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">تفاصيل التأمين</label>
                      <textarea
                        value={formData.carData?.insuranceDetails || ''}
                        onChange={(e) => handleInputChange('carData.insuranceDetails', e.target.value)}
                        placeholder="اكتب تفاصيل التأمين (شركة التأمين، تاريخ الانتهاء، التغطية، إلخ)"
                        rows={3}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                      <p className="text-sm text-blue-600 mt-1 flex items-center gap-1">
                        <span>💡</span>
                        <span>أضف معلومات مفيدة مثل شركة التأمين وتاريخ انتهاء التأمين</span>
                      </p>
                    </div>
                  )}
                </div>

                {/* المميزات الإضافية */}
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">المميزات الإضافية</label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {[
                      { id: 'fourWD', label: 'دفع رباعي' },
                      { id: 'sunroof', label: 'فتحة سقف' },
                      { id: 'leather', label: 'مقاعد جلد' },
                      { id: 'navigation', label: 'نظام ملاحة' },
                      { id: 'bluetooth', label: 'بلوتوث' },
                      { id: 'camera', label: 'كاميرا خلفية' },
                      { id: 'sensors', label: 'حساسات' },
                      { id: 'cruise', label: 'مثبت سرعة' },
                      { id: 'keyless', label: 'تشغيل بدون مفتاح' }
                    ].map(feature => (
                      <label key={feature.id} className="flex items-center space-x-2 rtl:space-x-reverse">
                        <input
                          type="checkbox"
                          checked={formData.carData?.features?.includes(feature.id) || false}
                          onChange={(e) => {
                            const currentFeatures = formData.carData?.features || [];
                            const newFeatures = e.target.checked
                              ? [...currentFeatures, feature.id]
                              : currentFeatures.filter(f => f !== feature.id);
                            handleInputChange('carData.features', newFeatures);
                          }}
                          className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                        />
                        <span className="text-sm text-gray-700">{feature.label}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* السعر والعملة للسيارات */}
                <div className="mt-6 pt-4 border-t border-blue-200">
                  <h4 className="text-md font-semibold text-blue-800 mb-4 flex items-center gap-2">
                    💰 السعر والعملة
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* السعر */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {formData.carData?.listingType === 'rent'
                          ? `السعر (${
                              formData.carData?.rentPeriod === 'daily' ? 'إيجار يومي' :
                              formData.carData?.rentPeriod === 'weekly' ? 'إيجار أسبوعي' :
                              formData.carData?.rentPeriod === 'monthly' ? 'إيجار شهري' : 'للفترة المحددة'
                            })`
                          : formData.carData?.listingType === 'exchange'
                          ? 'القيمة التقديرية (اختياري)'
                          : 'السعر'
                        }
                      </label>
                      <input
                        type="number"
                        value={formData.price || ''}
                        onChange={(e) => handleInputChange('price', parseInt(e.target.value) || 0)}
                        placeholder={
                          formData.carData?.listingType === 'rent'
                            ? 'سعر الإيجار'
                            : formData.carData?.listingType === 'exchange'
                            ? 'القيمة التقديرية'
                            : '0'
                        }
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                      {formData.carData?.listingType === 'rent' && (
                        <p className="text-sm text-blue-600 mt-1 flex items-center gap-1">
                          <span>💡</span>
                          <span>
                            {formData.carData?.rentPeriod === 'daily' && 'أدخل سعر الإيجار لليوم الواحد'}
                            {formData.carData?.rentPeriod === 'weekly' && 'أدخل سعر الإيجار للأسبوع الواحد'}
                            {formData.carData?.rentPeriod === 'monthly' && 'أدخل سعر الإيجار للشهر الواحد'}
                            {!formData.carData?.rentPeriod && 'حدد فترة الإيجار أولاً'}
                          </span>
                        </p>
                      )}
                    </div>

                    {/* العملة */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">العملة</label>
                      <select
                        value={formData.currency}
                        onChange={(e) => handleInputChange('currency', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      >
                        <option value="SYP">ليرة سورية (ل.س)</option>
                        <option value="USD">دولار أمريكي ($)</option>
                        <option value="EUR">يورو (€)</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* حقول متخصصة للعقارات */}
            {formData.category === 'real-estate' && (
              <div className="mt-8 p-6 bg-green-50 rounded-xl border border-green-200">
                <h3 className="text-lg font-semibold text-green-800 mb-4 flex items-center gap-2">
                  🏠 تفاصيل العقار
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* نوع الإعلان */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">نوع الإعلان</label>
                    <select
                      value={formData.realEstateData?.listingType || ''}
                      onChange={(e) => handleInputChange('realEstateData.listingType', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر نوع الإعلان</option>
                      <option value="sale">للبيع</option>
                      <option value="rent">للإيجار</option>
                      <option value="exchange">للمقايضة</option>
                    </select>
                  </div>

                  {/* فترة الإيجار */}
                  {formData.realEstateData?.listingType === 'rent' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">فترة الإيجار</label>
                      <select
                        value={formData.realEstateData?.rentPeriod || ''}
                        onChange={(e) => handleInputChange('realEstateData.rentPeriod', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      >
                        <option value="">اختر فترة الإيجار</option>
                        <option value="daily">يومي</option>
                        <option value="weekly">أسبوعي</option>
                        <option value="monthly">شهري</option>
                        <option value="yearly">سنوي</option>
                      </select>
                    </div>
                  )}

                  {/* تفاصيل المقايضة */}
                  {formData.realEstateData?.listingType === 'exchange' && (
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">تفاصيل المقايضة</label>
                      <textarea
                        value={formData.realEstateData?.exchangeDetails || ''}
                        onChange={(e) => handleInputChange('realEstateData.exchangeDetails', e.target.value)}
                        placeholder="اكتب ما تريد مقايضته بالعقار (مثال: عقار آخر، سيارة، إلخ)"
                        rows={3}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                      <p className="text-sm text-green-600 mt-1 flex items-center gap-1">
                        <span>💡</span>
                        <span>حدد بوضوح ما تريد مقايضته وأي شروط إضافية</span>
                      </p>
                    </div>
                  )}

                  {/* نوع العقار */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">نوع العقار</label>
                    <select
                      value={formData.realEstateData?.propertyType || ''}
                      onChange={(e) => handleInputChange('realEstateData.propertyType', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر نوع العقار</option>
                      {/* العقارات السكنية */}
                      <option value="apartment">شقة</option>
                      <option value="house">منزل</option>
                      {/* العقارات التجارية */}
                      <option value="office">مكتب</option>
                      <option value="shop">محل تجاري</option>
                      <option value="commercial-complex">مجمع تجاري</option>
                      {/* العقارات الصناعية */}
                      <option value="warehouse">مستودع</option>
                      <option value="factory">مصنع</option>
                      <option value="laboratory">معمل</option>
                      <option value="industrial-facility">منشأة صناعية</option>
                      {/* عقارات أخرى */}
                      <option value="building">بناء كامل</option>
                      {/* العقارات الزراعية والأراضي */}
                      <option value="farm">مزرعة</option>
                      <option value="land">أرض</option>
                      {/* العقارات الفاخرة */}
                      <option value="villa">فيلا</option>
                      <option value="chalet">شاليه</option>
                    </select>
                  </div>

                  {/* المساحة */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">المساحة (م²)</label>
                    <input
                      type="number"
                      value={formData.realEstateData?.area || ''}
                      onChange={(e) => handleInputChange('realEstateData.area', parseInt(e.target.value) || 0)}
                      placeholder="0"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  {/* المنطقة أو اسم الشارع */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">المنطقة أو الشارع</label>
                    <input
                      type="text"
                      value={formData.realEstateData?.streetArea || ''}
                      onChange={(e) => handleInputChange('realEstateData.streetArea', e.target.value)}
                      placeholder="اسم المنطقة أو الشارع"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  {/* عدد الغرف */}
                  {['apartment', 'house', 'villa', 'office', 'building'].includes(formData.realEstateData?.propertyType || '') && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        عدد الغرف <span className="text-xs text-gray-500">* (+1) صالون</span>
                      </label>
                      <select
                        value={formData.realEstateData?.rooms || ''}
                        onChange={(e) => handleInputChange('realEstateData.rooms', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      >
                        <option value="">اختر عدد الغرف</option>
                        <option value="1">1 غرفة</option>
                        <option value="1+1">1+1 غرفة (صالون)</option>
                        <option value="2">2 غرفة</option>
                        <option value="2+1">2+1 غرفة (صالون)</option>
                        <option value="3">3 غرف</option>
                        <option value="3+1">3+1 غرف (صالون)</option>
                        <option value="4">4 غرف</option>
                        <option value="4+1">4+1 غرف (صالون)</option>
                        <option value="5">5 غرف</option>
                        <option value="5+1">5+1 غرف (صالون)</option>
                        <option value="6">6 غرف</option>
                        <option value="6+1">6+1 غرف (صالون)</option>
                        <option value="7">7 غرف</option>
                        <option value="7+1">7+1 غرف (صالون)</option>
                        <option value="8">8 غرف</option>
                        <option value="8+1">8+1 غرف (صالون)</option>
                        <option value="9+">أكثر من 8 غرف</option>
                      </select>
                    </div>
                  )}

                  {/* عدد الحمامات */}
                  {['apartment', 'house', 'villa', 'office', 'building'].includes(formData.realEstateData?.propertyType || '') && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">عدد الحمامات</label>
                      <select
                        value={formData.realEstateData?.bathrooms || ''}
                        onChange={(e) => handleInputChange('realEstateData.bathrooms', parseInt(e.target.value))}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      >
                        <option value="">اختر عدد الحمامات</option>
                        <option value="1">1 حمام</option>
                        <option value="2">2 حمام</option>
                        <option value="3">3 حمامات</option>
                        <option value="4">4 حمامات</option>
                        <option value="5">5 حمامات أو أكثر</option>
                      </select>
                    </div>
                  )}

                  {/* الطابق */}
                  {['apartment', 'office', 'building'].includes(formData.realEstateData?.propertyType || '') && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">الطابق</label>
                      <select
                        value={formData.realEstateData?.floor || ''}
                        onChange={(e) => handleInputChange('realEstateData.floor', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      >
                        <option value="">اختر الطابق</option>
                        <option value="basement">قبو</option>
                        <option value="ground">الأرضي</option>
                        <option value="first">الأول</option>
                        <option value="second">الثاني</option>
                        <option value="third">الثالث</option>
                        <option value="fourth">الرابع</option>
                        <option value="fifth">الخامس</option>
                        <option value="sixth">السادس</option>
                        <option value="seventh">السابع</option>
                        <option value="eighth">الثامن</option>
                        <option value="roof">السطح</option>
                        <option value="high">أكثر من 8 طوابق</option>
                      </select>
                    </div>
                  )}

                  {/* حالة العقار */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">حالة العقار</label>
                    <select
                      value={formData.realEstateData?.condition || ''}
                      onChange={(e) => handleInputChange('realEstateData.condition', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر حالة العقار</option>
                      <option value="new">جديد</option>
                      <option value="renovated">معفش</option>
                      <option value="deluxe">كسوة ديلوكس</option>
                      <option value="regular">كسوة عادية</option>
                      <option value="old-finishing">كسوة قديمة</option>
                      <option value="unfinished">غير مكسي</option>
                      <option value="furnished">مفروش</option>
                      <option value="unfurnished">غير مفروش</option>
                      <option value="damaged">مدمر</option>
                    </select>
                  </div>
                </div>

                {/* المميزات الإضافية */}
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">المميزات الإضافية</label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {(() => {
                      const propertyType = formData.realEstateData?.propertyType;
                      let features = [];

                      // المميزات المشتركة لجميع العقارات
                      const commonFeatures = [
                        { id: 'greenDeed', label: 'سند أخضر' },
                        { id: 'agriculturalDeed', label: 'سند زراعي' },
                        { id: 'withinPlan', label: 'ضمن المخطط التنظيمي' },
                        { id: 'primeLocation', label: 'موقع مميز' },
                        { id: 'generator', label: 'مولد كهرباء' },
                        { id: 'solar', label: 'طاقة شمسية' },
                        { id: 'internet', label: 'إنترنت' },
                        { id: 'satellite', label: 'ستلايت' }
                      ];

                      // مميزات خاصة بكل نوع عقار
                      if (propertyType === 'apartment' || propertyType === 'house' || propertyType === 'villa') {
                        features = [
                          ...commonFeatures,
                          { id: 'elevator', label: 'مصعد' },
                          { id: 'parking', label: 'موقف سيارة' },
                          { id: 'garden', label: 'حديقة' },
                          { id: 'balcony', label: 'شرفة' },
                          { id: 'furnished', label: 'مفروش' },
                          { id: 'airCondition', label: 'تكييف' },
                          { id: 'heating', label: 'تدفئة' },
                          { id: 'security', label: 'حراسة' },
                          { id: 'pool', label: 'مسبح' },
                          { id: 'gym', label: 'نادي رياضي' },
                          { id: 'storage', label: 'مستودع' },
                          { id: 'maidRoom', label: 'غرفة خادمة' },
                          { id: 'laundry', label: 'غرفة غسيل' },
                          { id: 'kitchen', label: 'مطبخ مجهز' },
                          { id: 'seaview', label: 'إطلالة بحر' },
                          { id: 'mountainview', label: 'إطلالة جبل' },
                          { id: 'cityview', label: 'إطلالة مدينة' }
                        ];
                      } else if (propertyType === 'office') {
                        features = [
                          ...commonFeatures,
                          { id: 'elevator', label: 'مصعد' },
                          { id: 'parking', label: 'موقف سيارة' },
                          { id: 'airCondition', label: 'تكييف' },
                          { id: 'heating', label: 'تدفئة' },
                          { id: 'security', label: 'حراسة' },
                          { id: 'furnished', label: 'مفروش' },
                          { id: 'reception', label: 'استقبال' },
                          { id: 'meetingRoom', label: 'غرفة اجتماعات' },
                          { id: 'businessCenter', label: 'مركز أعمال' },
                          { id: 'commercialComplex', label: 'ضمن مجمع تجاري' },
                          { id: 'withinMall', label: 'ضمن مول' },
                          { id: 'vitalArea', label: 'منطقة حيوية' },
                          { id: 'mainStreet', label: 'شارع رئيسي' },
                          { id: 'cityview', label: 'إطلالة مدينة' }
                        ];
                      } else if (propertyType === 'shop') {
                        features = [
                          ...commonFeatures,
                          { id: 'mainStreet', label: 'ضمن شارع رئيسي' },
                          { id: 'mall', label: 'ضمن مول' },
                          { id: 'commercialComplex', label: 'ضمن مجمع تجاري' },
                          { id: 'commercialMarket', label: 'ضمن سوق تجاري' },
                          { id: 'vitalArea', label: 'منطقة حيوية' },
                          { id: 'industrialArea', label: 'ضمن منطقة صناعية' },
                          { id: 'cornerLocation', label: 'موقع زاوية' },
                          { id: 'storefront', label: 'واجهة عرض' },
                          { id: 'storage', label: 'مستودع' },
                          { id: 'airCondition', label: 'تكييف' },
                          { id: 'security', label: 'حراسة' },
                          { id: 'parking', label: 'موقف سيارة' },
                          { id: 'loadingDock', label: 'منطقة تحميل' }
                        ];
                      } else if (propertyType === 'warehouse') {
                        features = [
                          ...commonFeatures,
                          { id: 'loadingDock', label: 'منطقة تحميل' },
                          { id: 'truckAccess', label: 'دخول شاحنات' },
                          { id: 'highCeiling', label: 'سقف عالي' },
                          { id: 'securitySystem', label: 'نظام أمان' },
                          { id: 'fireSystem', label: 'نظام إطفاء' },
                          { id: 'office', label: 'مكتب إداري' },
                          { id: 'parking', label: 'موقف سيارة' },
                          { id: 'industrialArea', label: 'منطقة صناعية' }
                        ];
                      } else if (propertyType === 'land') {
                        features = [
                          ...commonFeatures,
                          { id: 'buildingPermit', label: 'رخصة بناء' },
                          { id: 'waterAccess', label: 'وصول مياه' },
                          { id: 'electricityAccess', label: 'وصول كهرباء' },
                          { id: 'roadAccess', label: 'وصول طريق' },
                          { id: 'cornerLot', label: 'قطعة زاوية' },
                          { id: 'flatLand', label: 'أرض مستوية' },
                          { id: 'seaview', label: 'إطلالة بحر' },
                          { id: 'mountainview', label: 'إطلالة جبل' }
                        ];
                      } else if (propertyType === 'chalet') {
                        features = [
                          ...commonFeatures,
                          { id: 'seaview', label: 'إطلالة بحر' },
                          { id: 'mountainview', label: 'إطلالة جبل' },
                          { id: 'garden', label: 'حديقة' },
                          { id: 'pool', label: 'مسبح' },
                          { id: 'barbecue', label: 'منطقة شواء' },
                          { id: 'terrace', label: 'تراس' },
                          { id: 'furnished', label: 'مفروش' },
                          { id: 'airCondition', label: 'تكييف' },
                          { id: 'parking', label: 'موقف سيارة' },
                          { id: 'beachAccess', label: 'وصول للشاطئ' }
                        ];
                      } else if (propertyType === 'farm') {
                        // مميزات المزرعة - محدثة

                        // المميزات المشتركة للمزارع (بدون السندات والمخطط التنظيمي للإيجار)
                        const farmCommonFeatures = formData.realEstateData?.listingType === 'rent'
                          ? [
                              // للإيجار: إزالة السندات والمخطط التنظيمي
                              { id: 'primeLocation', label: 'موقع مميز' },
                              { id: 'generator', label: 'مولد كهرباء' },
                              { id: 'solar', label: 'طاقة شمسية' },
                              { id: 'internet', label: 'إنترنت' },
                              { id: 'satellite', label: 'ستلايت' }
                            ]
                          : commonFeatures; // للبيع والمقايضة: جميع المميزات المشتركة

                        const farmBaseFeatures = [
                          ...farmCommonFeatures,
                          { id: 'waterWell', label: 'بئر مياه' },
                          { id: 'irrigation', label: 'نظام ري' },
                          { id: 'pool', label: 'مسبح' },
                          { id: 'filteredPool', label: 'مسبح مفلتر' },
                          { id: 'waterfall', label: 'شلال ماء' },
                          { id: 'terrace', label: 'تراس' },
                          { id: 'barbecue', label: 'باربكيو' },
                          { id: 'swimmingChangingRooms', label: 'مشالح سباحة' }
                        ];

                        // المميزات التي تظهر فقط للبيع والمقايضة (ليس للإيجار)
                        const saleOnlyFeatures = [
                          { id: 'farmHouse', label: 'بيت مزرعة' },
                          { id: 'animalShelter', label: 'مأوى حيوانات' },
                          { id: 'storage', label: 'مستودع' },
                          { id: 'fruitTrees', label: 'أشجار مثمرة' },
                          { id: 'vegetables', label: 'زراعة خضار' },
                          { id: 'greenhouse', label: 'بيت بلاستيكي' },
                          { id: 'tractorAccess', label: 'دخول جرار' }
                        ];

                        // إضافة المميزات حسب نوع الإعلان
                        if (formData.realEstateData?.listingType === 'rent') {
                          features = farmBaseFeatures; // للإيجار: المميزات الأساسية فقط (بدون السندات)
                        } else {
                          features = [...farmBaseFeatures, ...saleOnlyFeatures]; // للبيع والمقايضة: جميع المميزات
                        }
                      } else {
                        // إذا لم يتم اختيار نوع العقار، عرض المميزات العامة
                        features = commonFeatures;
                      }

                      return features.map(feature => (
                        <label key={feature.id} className="flex items-center space-x-2 rtl:space-x-reverse">
                          <input
                            type="checkbox"
                            checked={formData.realEstateData?.features?.includes(feature.id) || false}
                            onChange={(e) => {
                              const currentFeatures = formData.realEstateData?.features || [];
                              const newFeatures = e.target.checked
                                ? [...currentFeatures, feature.id]
                                : currentFeatures.filter(f => f !== feature.id);
                              handleInputChange('realEstateData.features', newFeatures);
                            }}
                            className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                          />
                          <span className="text-sm text-gray-700">{feature.label}</span>
                        </label>
                      ));
                    })()}
                  </div>
                </div>

                {/* السعر والعملة للعقارات */}
                <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {formData.realEstateData?.listingType === 'rent'
                        ? `السعر (${
                            formData.realEstateData?.rentPeriod === 'daily' ? 'إيجار يومي' :
                            formData.realEstateData?.rentPeriod === 'weekly' ? 'إيجار أسبوعي' :
                            formData.realEstateData?.rentPeriod === 'monthly' ? 'إيجار شهري' :
                            formData.realEstateData?.rentPeriod === 'yearly' ? 'إيجار سنوي' : 'للفترة المحددة'
                          })`
                        : formData.realEstateData?.listingType === 'exchange'
                        ? 'القيمة التقديرية (اختياري)'
                        : 'السعر'
                      }
                    </label>
                    <input
                      type="number"
                      value={formData.price || ''}
                      onChange={(e) => handleInputChange('price', parseInt(e.target.value) || 0)}
                      placeholder={
                        formData.realEstateData?.listingType === 'rent'
                          ? 'سعر الإيجار'
                          : formData.realEstateData?.listingType === 'exchange'
                          ? 'القيمة التقديرية'
                          : '0'
                      }
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                    {formData.realEstateData?.listingType === 'rent' && (
                      <p className="text-sm text-green-600 mt-1 flex items-center gap-1">
                        <span>💡</span>
                        <span>
                          {formData.realEstateData?.rentPeriod === 'daily' && 'أدخل سعر الإيجار لليوم الواحد'}
                          {formData.realEstateData?.rentPeriod === 'weekly' && 'أدخل سعر الإيجار للأسبوع الواحد'}
                          {formData.realEstateData?.rentPeriod === 'monthly' && 'أدخل سعر الإيجار للشهر الواحد'}
                          {formData.realEstateData?.rentPeriod === 'yearly' && 'أدخل سعر الإيجار للسنة الواحدة'}
                          {!formData.realEstateData?.rentPeriod && 'حدد فترة الإيجار أولاً'}
                        </span>
                      </p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">العملة</label>
                    <select
                      value={formData.currency}
                      onChange={(e) => handleInputChange('currency', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="SYP">ليرة سورية (ل.س)</option>
                      <option value="USD">دولار أمريكي ($)</option>
                      <option value="EUR">يورو (€)</option>
                    </select>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-between mt-8">
              <button
                onClick={handlePrev}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                السابق
              </button>
              <button
                onClick={handleNext}
                disabled={!formData.title || !formData.description || !formData.contactPhone}
                className="px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold"
              >
                التالي
              </button>
            </div>
          </div>
        );
      case 4:
        return (
          <div key={`plan-selection-${planType}-${forceUpdate}`} className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">اختر نوع الإعلان</h2>
              <p className="text-gray-600">حدد الباقة المناسبة لإعلانك</p>
            </div>



            {/* أزرار التبديل بين الأفراد والشركات - تظهر فقط إذا لم يكن مكتب عقاري */}
            {userType !== 'real-estate-office' && (
              <div className="flex justify-center mb-8">
                <div className="bg-white rounded-xl shadow-lg p-2 inline-flex">
                  <button
                    onClick={() => {
                      setPlanType('individual');
                      setSelectedPlan('');
                      setFormData(prev => ({ ...prev, selectedPlan: '', adType: 'free' }));
                      setForceUpdate(prev => prev + 1);
                    }}
                    className={`px-8 py-3 rounded-lg font-semibold transition-all duration-300 ${
                      planType === 'individual'
                        ? 'bg-primary-600 text-white shadow-md'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    🧑‍💼 باقات الأفراد
                  </button>
                  <button
                    onClick={() => {
                      setPlanType('business');
                      setSelectedPlan('');
                      setFormData(prev => ({ ...prev, selectedPlan: '', adType: 'featured' }));
                      setForceUpdate(prev => prev + 1);
                    }}
                    className={`px-8 py-3 rounded-lg font-semibold transition-all duration-300 ${
                      planType === 'business'
                        ? 'bg-primary-600 text-white shadow-md'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    🏢 باقات الشركات
                  </button>
                </div>
              </div>
            )}

            {/* تنبيه مهم للشركات */}
            {planType === 'business' && (
              <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-6 mb-6">
                <div className="flex items-center gap-3 mb-4">
                  <span className="text-2xl">⚠️</span>
                  <h3 className="text-lg font-semibold text-orange-800">تنبيه مهم</h3>
                </div>
                <p className="text-orange-700 leading-relaxed">
                  للشركات، يجب اختيار اشتراك شهري للحصول على إعلانات مميزة دائماً
                </p>
              </div>
            )}





            {(() => {
              // إذا كان مكتب عقاري، اعرض باقة مكتب عقاري فقط
              if (userType === 'real-estate-office') {
                return (
                  <div key="real-estate-office-package" className="max-w-md mx-auto">
                    <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-xl p-6 mb-6">
                      <div className="flex items-center gap-3 mb-4">
                        <span className="text-2xl">🏘️</span>
                        <h3 className="text-lg font-semibold text-orange-800">باقة مكتب عقاري حصرية</h3>
                      </div>
                      <p className="text-orange-700 leading-relaxed">
                        باقة مخصصة للمكاتب العقارية مع مميزات متقدمة وشارة موثقة
                      </p>
                    </div>

                    {/* باقة مكتب عقاري */}
                    <div
                      onClick={() => handlePlanSelect('real-estate-office')}
                      className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                        selectedPlan === 'real-estate-office'
                          ? 'border-orange-500 bg-orange-50 shadow-lg transform scale-105'
                          : 'border-gray-200 hover:border-orange-300 hover:shadow-md'
                      }`}
                    >
                      <div className="text-center">
                        <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-2xl">🏘️</span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">مكتب عقاري</h3>
                        <div className="text-2xl font-bold text-orange-600 mb-2">700,000 ل.س</div>
                        <p className="text-sm text-gray-600 mb-4">/شهر</p>
                        <div className="space-y-2 text-xs text-gray-500 text-right">
                          <div>✓ 30 إعلان عقاري شهرياً</div>
                          <div>✓ 10 صور لكل إعلان</div>
                          <div>✓ شارة فضية موثقة</div>
                          <div>✓ أولوية في البحث العقاري</div>
                          <div>✓ أدوات عقارية متقدمة</div>
                          <div>✓ دعم فني متخصص</div>
                        </div>
                      </div>
                      {selectedPlan === 'real-estate-office' && (
                        <div className="absolute -top-3 -right-3 bg-orange-500 text-white rounded-full px-3 py-1 text-xs font-semibold">
                          محدد
                        </div>
                      )}
                    </div>
                  </div>
                );
              }

              if (planType === 'individual') {
                return (
                  <div key="individual-packages" className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* الباقة المجانية */}
                    <div
                      onClick={() => handlePlanSelect('individual-free')}
                      className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                        selectedPlan === 'individual-free'
                          ? 'border-green-500 bg-green-50 shadow-lg transform scale-105'
                          : 'border-gray-200 hover:border-green-300 hover:shadow-md'
                      }`}
                    >
                      <div className="text-center">
                        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-2xl">🆓</span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">الباقة المجانية</h3>
                        <div className="text-2xl font-bold text-green-600 mb-2">مجاناً</div>
                        <p className="text-sm text-gray-600 mb-4">للأفراد</p>
                        <div className="space-y-2 text-xs text-gray-500 text-right">
                          <div>✓ 5 إعلانات شهرياً</div>
                          <div>✓ 3 صور لكل إعلان</div>
                          <div>✓ دعم فني أساسي</div>
                          <div>✓ عرض في النتائج العادية</div>
                        </div>
                      </div>
                      {selectedPlan === 'individual-free' && (
                        <div className="absolute -top-3 -right-3 bg-green-500 text-white rounded-full px-3 py-1 text-xs font-semibold">
                          محدد
                        </div>
                      )}
                    </div>

                    {/* الباقة الأساسية */}
                    <div
                      onClick={() => handlePlanSelect('individual-basic')}
                      className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                        selectedPlan === 'individual-basic'
                          ? 'border-blue-500 bg-blue-50 shadow-lg transform scale-105'
                          : 'border-gray-200 hover:border-blue-300 hover:shadow-md'
                      }`}
                    >
                      <div className="text-center">
                        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-2xl">📋</span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">الباقة الأساسية</h3>
                        <div className="text-2xl font-bold text-blue-600 mb-2">25,000 ل.س</div>
                        <p className="text-sm text-gray-600 mb-4">/شهر</p>
                        <div className="space-y-2 text-xs text-gray-500 text-right">
                          <div>✓ 5 إعلانات شهرياً</div>
                          <div>✓ 3 صور لكل إعلان</div>
                          <div>✓ دعم فني أساسي</div>
                          <div>✓ عرض في النتائج العادية</div>
                        </div>
                      </div>
                      {selectedPlan === 'individual-basic' && (
                        <div className="absolute -top-3 -right-3 bg-blue-500 text-white rounded-full px-3 py-1 text-xs font-semibold">
                          محدد
                        </div>
                      )}
                    </div>

                    {/* الباقة المميزة */}
                    <div
                      onClick={() => handlePlanSelect('individual-premium')}
                      className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                        selectedPlan === 'individual-premium'
                          ? 'border-purple-500 bg-purple-50 shadow-lg transform scale-105'
                          : 'border-gray-200 hover:border-purple-300 hover:shadow-md'
                      }`}
                    >
                      <div className="text-center">
                        <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-2xl">⭐</span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">الباقة المميزة</h3>
                        <div className="text-2xl font-bold text-purple-600 mb-2">50,000 ل.س</div>
                        <p className="text-sm text-gray-600 mb-4">/شهر</p>
                        <div className="space-y-2 text-xs text-gray-500 text-right">
                          <div>✓ 15 إعلان مميز شهرياً</div>
                          <div>✓ 5 صور لكل إعلان</div>
                          <div>✓ أولوية في النتائج</div>
                          <div>✓ دعم فني متقدم</div>
                        </div>
                      </div>
                      {selectedPlan === 'individual-premium' && (
                        <div className="absolute -top-3 -right-3 bg-purple-500 text-white rounded-full px-3 py-1 text-xs font-semibold">
                          الأكثر شعبية
                        </div>
                      )}
                    </div>
                  </div>
                );
              } else {
                return (
                  <div key="business-packages" className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    {/* باقة البداية للشركات */}
                    <div
                      onClick={() => handlePlanSelect('business-starter')}
                      className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                        selectedPlan === 'business-starter'
                          ? 'border-blue-500 bg-blue-50 shadow-lg transform scale-105'
                          : 'border-gray-200 hover:border-blue-300 hover:shadow-md'
                      }`}
                    >
                      <div className="text-center">
                        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-2xl">🏢</span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">باقة البداية</h3>
                        <div className="text-2xl font-bold text-blue-600 mb-2">500,000 ل.س</div>
                        <p className="text-sm text-gray-600 mb-4">/شهر</p>
                        <div className="space-y-2 text-xs text-gray-500 text-right">
                          <div>✓ 15 إعلان شهرياً</div>
                          <div>✓ 5 صور لكل إعلان</div>
                          <div>✓ شارة زرقاء موثقة</div>
                          <div>✓ دعم فني متقدم</div>
                        </div>
                      </div>
                      {selectedPlan === 'business-starter' && (
                        <div className="absolute -top-3 -right-3 bg-blue-500 text-white rounded-full px-3 py-1 text-xs font-semibold">
                          محدد
                        </div>
                      )}
                    </div>

                    {/* باقة الأعمال */}
                    <div
                      onClick={() => handlePlanSelect('business-gold')}
                      className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                        selectedPlan === 'business-gold'
                          ? 'border-yellow-500 bg-yellow-50 shadow-lg transform scale-105'
                          : 'border-gray-200 hover:border-yellow-300 hover:shadow-md'
                      }`}
                    >
                      <div className="text-center">
                        <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-2xl">👑</span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">باقة الأعمال</h3>
                        <div className="text-2xl font-bold text-yellow-600 mb-2">100,000 ل.س</div>
                        <p className="text-sm text-gray-600 mb-4">/شهر</p>
                        <div className="space-y-2 text-xs text-gray-500 text-right">
                          <div>✓ إعلانات غير محدودة</div>
                          <div>✓ 20 صورة لكل إعلان</div>
                          <div>✓ أولوية قصوى</div>
                          <div>✓ شارة ذهبية موثقة</div>
                        </div>
                      </div>
                      {selectedPlan === 'business-gold' && (
                        <div className="absolute -top-3 -right-3 bg-yellow-500 text-white rounded-full px-3 py-1 text-xs font-semibold">
                          الأكثر شعبية
                        </div>
                      )}
                    </div>

                    {/* مكتب عقاري */}
                    <div
                      onClick={() => handlePlanSelect('real-estate-office')}
                      className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                        selectedPlan === 'real-estate-office'
                          ? 'border-green-500 bg-green-50 shadow-lg transform scale-105'
                          : 'border-gray-200 hover:border-green-300 hover:shadow-md'
                      }`}
                    >
                      <div className="text-center">
                        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-2xl">🏠</span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">مكتب عقاري</h3>
                        <div className="text-2xl font-bold text-green-600 mb-2">700,000 ل.س</div>
                        <p className="text-sm text-gray-600 mb-4">/شهر</p>
                        <div className="space-y-2 text-xs text-gray-500 text-right">
                          <div>✓ 30 إعلان عقاري شهرياً</div>
                          <div>✓ 10 صور لكل إعلان</div>
                          <div>✓ شارة فضية موثقة</div>
                          <div>✓ أولوية في البحث العقاري</div>
                        </div>
                      </div>
                      {selectedPlan === 'real-estate-office' && (
                        <div className="absolute -top-3 -right-3 bg-green-500 text-white rounded-full px-3 py-1 text-xs font-semibold">
                          محدد
                        </div>
                      )}
                    </div>

                    {/* باقة الشركات المتقدمة */}
                    <div
                      onClick={() => handlePlanSelect('business-professional')}
                      className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                        selectedPlan === 'business-professional'
                          ? 'border-purple-500 bg-purple-50 shadow-lg transform scale-105'
                          : 'border-gray-200 hover:border-purple-300 hover:shadow-md'
                      }`}
                    >
                      <div className="text-center">
                        <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <span className="text-2xl">💎</span>
                        </div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-2">الباقة المتقدمة</h3>
                        <div className="text-2xl font-bold text-purple-600 mb-2">1,000,000 ل.س</div>
                        <p className="text-sm text-gray-600 mb-4">/شهر</p>
                        <div className="space-y-2 text-xs text-gray-500 text-right">
                          <div>✓ 50 إعلان شهرياً</div>
                          <div>✓ 15 صورة لكل إعلان</div>
                          <div>✓ شارة ذهبية موثقة</div>
                          <div>✓ أولوية قصوى في جميع النتائج</div>
                        </div>
                      </div>
                      {selectedPlan === 'business-professional' && (
                        <div className="absolute -top-3 -right-3 bg-purple-500 text-white rounded-full px-3 py-1 text-xs font-semibold">
                          الأكثر شعبية
                        </div>
                      )}
                    </div>
                  </div>
                );
              }
            })()}



            <div className="flex justify-between mt-8">
              <button
                onClick={handlePrev}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                السابق
              </button>
              <button
                onClick={handleNext}
                disabled={!selectedPlan}
                className="px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold"
              >
                التالي
              </button>
            </div>
          </div>
        );
      case 5:
        return (
          <div className="space-y-6">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">إضافة الصور</h2>
              <p className="text-gray-600">
                أضف صور لإعلانك
                {selectedPlan === 'individual-free' && ' (حتى 3 صور)'}
                {selectedPlan === 'individual-basic' && ' (حتى 3 صور)'}
                {selectedPlan === 'individual-premium' && ' (حتى 5 صور)'}
                {selectedPlan === 'business-gold' && ' (حتى 20 صورة)'}
                {selectedPlan === 'business-starter' && ' (حتى 5 صور)'}
                {selectedPlan === 'real-estate-office' && ' (حتى 10 صور)'}
                {selectedPlan === 'business-professional' && ' (حتى 15 صورة)'}
              </p>
            </div>

            <div
              className={`border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
                isDragOver
                  ? 'border-primary-500 bg-primary-50 scale-105'
                  : 'border-gray-300 hover:border-primary-400'
              }`}
              onDragOver={(e) => {
                e.preventDefault();
                setIsDragOver(true);
              }}
              onDragLeave={(e) => {
                e.preventDefault();
                setIsDragOver(false);
              }}
              onDrop={(e) => {
                e.preventDefault();
                setIsDragOver(false);
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                  handleImageFiles(files);
                }
              }}
            >
              <div className={`text-4xl mb-4 transition-transform ${isDragOver ? 'scale-110' : ''}`}>
                {isDragOver ? '📥' : '📸'}
              </div>
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                {isDragOver ? 'اتركها هنا!' : 'اسحب الصور هنا أو انقر للاختيار'}
              </h3>
              <p className="text-gray-600 mb-2">
                يمكنك إضافة حتى {
                  selectedPlan === 'individual-free' ? '3 صور' :
                  selectedPlan === 'individual-basic' ? '3 صور' :
                  selectedPlan === 'individual-premium' ? '5 صور' :
                  selectedPlan === 'business-gold' ? '20 صورة' :
                  selectedPlan === 'business-starter' ? '5 صور' :
                  selectedPlan === 'real-estate-office' ? '10 صور' :
                  selectedPlan === 'business-professional' ? '15 صورة' : '3 صور'
                }
              </p>
              <p className="text-sm text-gray-500 mb-4">
                الصور المدعومة: JPG, PNG, GIF, WebP (حد أقصى 5MB لكل صورة)
              </p>

              <input
                type="file"
                multiple
                accept="image/jpeg,image/png,image/gif,image/webp"
                onChange={(e) => {
                  if (e.target.files && e.target.files.length > 0) {
                    handleImageFiles(e.target.files);
                    // إعادة تعيين قيمة input لتمكين اختيار نفس الملفات مرة أخرى إذا لزم الأمر
                    e.target.value = '';
                  }
                }}
                className="hidden"
                id="image-upload"
              />

              <div className="flex flex-col sm:flex-row gap-3 items-center justify-center">
                <label
                  htmlFor="image-upload"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 cursor-pointer transition-colors"
                >
                  <span>📁</span>
                  اختر الصور
                </label>

                {formData.images.length > 0 && (
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, images: [] }))}
                    className="inline-flex items-center gap-2 px-4 py-2 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors"
                  >
                    <span>🗑️</span>
                    حذف الكل
                  </button>
                )}
              </div>

              {/* عرض الصور المرفوعة داخل منطقة الرفع */}
              {formData.images.length > 0 && (
                <div className="mt-6 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm font-medium text-gray-700">
                      الصور المرفوعة ({formData.images.length})
                    </span>
                    <span className="text-xs text-green-600">
                      ✅ تم الرفع بنجاح
                    </span>
                  </div>

                  <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-3">
                    {formData.images.map((image, index) => {
                      const imageUrl = URL.createObjectURL(image);
                      const isDragging = draggedImageIndex === index;
                      return (
                        <div
                          key={`image-${index}-${image.name}-${image.lastModified}`}
                          className={`relative group cursor-move ${isDragging ? 'opacity-50 scale-95' : ''}`}
                          draggable
                          onDragStart={(e) => handleImageDragStart(e, index)}
                          onDragOver={handleImageDragOver}
                          onDrop={(e) => handleImageDrop(e, index)}
                        >
                          <div className={`aspect-square w-full overflow-hidden rounded-lg border-2 transition-all duration-200 bg-gray-100 ${
                            isDragging
                              ? 'border-primary-500 shadow-lg'
                              : 'border-gray-200 hover:border-primary-300 group-hover:shadow-md'
                          }`}>
                            <img
                              src={imageUrl}
                              alt={`صورة ${index + 1}`}
                              className="w-full h-full object-cover"
                              onLoad={() => {
                                setTimeout(() => URL.revokeObjectURL(imageUrl), 100);
                              }}
                              onError={() => {
                                URL.revokeObjectURL(imageUrl);
                              }}
                            />
                          </div>

                          {/* أزرار التحكم */}
                          <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg">
                            {/* زر الحذف */}
                            <button
                              onClick={() => {
                                const newImages = formData.images.filter((_, i) => i !== index);
                                setFormData(prev => ({ ...prev, images: newImages }));
                                URL.revokeObjectURL(imageUrl);
                              }}
                              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 transition-colors shadow-lg opacity-0 group-hover:opacity-100"
                              title="حذف الصورة"
                            >
                              ×
                            </button>

                            {/* أزرار إعادة الترتيب */}
                            {formData.images.length > 1 && (
                              <div className="absolute top-1 left-1 flex flex-col gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                                {index > 0 && (
                                  <button
                                    onClick={() => moveImage(index, index - 1)}
                                    className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-blue-600 transition-colors shadow-md"
                                    title="تحريك للأمام"
                                  >
                                    ↑
                                  </button>
                                )}
                                {index < formData.images.length - 1 && (
                                  <button
                                    onClick={() => moveImage(index, index + 1)}
                                    className="bg-blue-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs hover:bg-blue-600 transition-colors shadow-md"
                                    title="تحريك للخلف"
                                  >
                                    ↓
                                  </button>
                                )}
                              </div>
                            )}
                          </div>

                          {/* رقم الصورة */}
                          <div className="absolute bottom-1 left-1 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
                            {index + 1}
                          </div>

                          {/* علامة الصورة الرئيسية */}
                          {index === 0 && (
                            <div className="absolute top-1 right-1 bg-green-500 text-white text-xs px-2 py-1 rounded">
                              رئيسية
                            </div>
                          )}

                          {/* أيقونة السحب */}
                          <div className="absolute bottom-1 right-1 bg-gray-800 bg-opacity-70 text-white text-xs px-1 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                            ⋮⋮
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  {/* معلومات إضافية */}
                  <div className="mt-4 space-y-2">
                    <div className="text-xs text-gray-500 text-center">
                      💡 الصورة الأولى ستكون الصورة الرئيسية للإعلان
                    </div>
                    {formData.images.length > 1 && (
                      <div className="text-xs text-blue-600 text-center bg-blue-50 p-2 rounded">
                        🔄 يمكنك إعادة ترتيب الصور بالسحب والإفلات أو استخدام الأزرار ↑↓
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-between mt-8">
              <button
                onClick={handlePrev}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                السابق
              </button>
              <button
                onClick={handleNext}
                className="px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-semibold"
              >
                التالي
              </button>
            </div>
          </div>
        );
      case 6:
        return (
          <div className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-2">نشر الإعلان</h2>
              <p className="text-gray-600">مراجعة نهائية ونشر إعلانك</p>
            </div>

            {/* ملخص الإعلان النهائي */}
            {renderAdSummary()}

            {/* ملخص الباقة المختارة */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-6">
              <div className="flex items-center gap-3 mb-4">
                <span className="text-2xl">📋</span>
                <h3 className="text-lg font-semibold text-blue-800">ملخص الباقة المختارة</h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-blue-700">
                    <strong>نوع الباقة:</strong> {
                      selectedPlan === 'individual-free' ? 'الباقة المجانية' :
                      selectedPlan === 'individual-basic' ? 'الباقة الأساسية' :
                      selectedPlan === 'individual-premium' ? 'الباقة المميزة' :
                      selectedPlan === 'business-gold' ? 'باقة الأعمال' :
                      selectedPlan === 'business-starter' ? 'باقة البداية' :
                      selectedPlan === 'real-estate-office' ? 'مكتب عقاري' :
                      selectedPlan === 'business-professional' ? 'الباقة المتقدمة' : 'غير محدد'
                    }
                  </p>
                  <p className="text-blue-700">
                    <strong>السعر:</strong> {
                      selectedPlan === 'individual-free' ? 'مجاناً' :
                      selectedPlan === 'individual-basic' ? '25,000 ل.س/شهر' :
                      selectedPlan === 'individual-premium' ? '50,000 ل.س/شهر' :
                      selectedPlan === 'business-gold' ? '100,000 ل.س/شهر' :
                      selectedPlan === 'business-starter' ? '500,000 ل.س/شهر' :
                      selectedPlan === 'real-estate-office' ? '700,000 ل.س/شهر' :
                      selectedPlan === 'business-professional' ? '1,000,000 ل.س/شهر' : 'غير محدد'
                    }
                  </p>
                </div>
                <div>
                  <p className="text-blue-700">
                    <strong>عدد الصور المسموح:</strong> {
                      selectedPlan === 'individual-free' ? '3 صور' :
                      selectedPlan === 'individual-basic' ? '3 صور' :
                      selectedPlan === 'individual-premium' ? '5 صور' :
                      selectedPlan === 'business-gold' ? '20 صورة' :
                      selectedPlan === 'business-starter' ? '5 صور' :
                      selectedPlan === 'real-estate-office' ? '10 صور' :
                      selectedPlan === 'business-professional' ? '15 صورة' : 'غير محدد'
                    }
                  </p>
                  <p className="text-blue-700">
                    <strong>الصور المرفوعة:</strong> {formData.images.length} صورة
                  </p>
                </div>
              </div>
            </div>

            {/* شروط وأحكام */}
            <div className="bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-200 rounded-xl p-6">
              <div className="flex items-start gap-3">
                <input
                  type="checkbox"
                  id="terms"
                  checked={formData.acceptTerms}
                  onChange={(e) => setFormData(prev => ({ ...prev, acceptTerms: e.target.checked }))}
                  className="mt-1 w-5 h-5 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
                />
                <div className="flex-1">
                  <label htmlFor="terms" className="text-sm text-gray-700 leading-relaxed block">
                    أوافق على{' '}
                    <a
                      href="/terms"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary-600 font-semibold hover:text-primary-700 underline decoration-2 underline-offset-2 transition-colors"
                    >
                      الشروط والأحكام
                    </a>{' '}
                    وأتحمل مسؤولية دقة المعلومات المقدمة.
                  </label>
                  <div className="mt-2 text-xs text-gray-600 bg-white rounded-lg p-3 border border-gray-200">
                    <p className="font-medium text-gray-700 mb-2">🔒 تعهد قانوني مهم:</p>
                    <ul className="space-y-1">
                      <li>• أتعهد بعدم نشر صور مخالفة لقوانين الجمهورية العربية السورية</li>
                      <li>• أتعهد بعدم نشر محتوى مخالف للآداب العامة والقيم الاجتماعية</li>
                      <li>• أتحمل المسؤولية الكاملة عن جميع المحتويات التي أنشرها</li>
                      <li>• أخلي مسؤولية الموقع من أي مخالفات أو أضرار تنتج عن إعلاناتي</li>
                    </ul>
                    <div className="mt-3 pt-2 border-t border-gray-200">
                      <p className="text-xs text-gray-500">
                        💡 انقر على "الشروط والأحكام" أعلاه لقراءة النص الكامل في صفحة منفصلة
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* أزرار التحكم */}
            <div className="flex justify-between items-center">
              <button
                onClick={handlePrev}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                السابق
              </button>

              <div className="flex gap-4">
                {selectedPlan === 'individual-free' ? (
                  <button
                    onClick={handleSubmit}
                    disabled={!formData.acceptTerms}
                    className="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold"
                  >
                    🚀 نشر الإعلان مجاناً
                  </button>
                ) : (
                  <button
                    onClick={handlePayment}
                    disabled={!selectedPlan || !formData.acceptTerms}
                    className="px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold"
                  >
                    💳 الدفع ونشر الإعلان
                  </button>
                )}
              </div>
            </div>

            {/* معالج الدفع */}
            {showPayment && selectedPlan && selectedPlan !== 'individual-free' && (
              <div className="mt-8">
                <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
                  <div className="text-center mb-8">
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">💳 اختر وسيلة الدفع</h2>
                    <p className="text-gray-600">اختر الطريقة المناسبة لك لإتمام عملية الدفع</p>
                  </div>

                  {/* عرض تفاصيل الباقة */}
                  <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-6 mb-8">
                    <div className="text-center">
                      <h3 className="text-lg font-semibold text-blue-800 mb-2">
                        {selectedPlan === 'individual-basic' ? 'الباقة الأساسية' :
                         selectedPlan === 'individual-premium' ? 'الباقة المميزة' :
                         selectedPlan === 'business-gold' ? 'باقة الأعمال' :
                         selectedPlan === 'business-starter' ? 'باقة البداية' :
                         selectedPlan === 'real-estate-office' ? 'مكتب عقاري' :
                         selectedPlan === 'business-professional' ? 'الباقة المتقدمة' : 'باقة مميزة'}
                      </h3>
                      <div className="text-3xl font-bold text-primary-600">
                        {selectedPlan === 'individual-basic' ? '25,000' :
                         selectedPlan === 'individual-premium' ? '50,000' :
                         selectedPlan === 'business-gold' ? '100,000' :
                         selectedPlan === 'business-starter' ? '500,000' :
                         selectedPlan === 'real-estate-office' ? '700,000' :
                         selectedPlan === 'business-professional' ? '1,000,000' : '0'} ل.س
                      </div>
                      <p className="text-gray-600 text-sm mt-2">دفعة واحدة لنشر الإعلان</p>
                    </div>
                  </div>

                  {/* وسائل الدفع */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8">
                    {paymentMethods.map((method) => (
                      <div
                        key={method.id}
                        className={`border-2 rounded-xl p-6 transition-all duration-300 cursor-pointer ${
                          selectedPaymentMethod === method.id
                            ? 'border-primary-500 bg-primary-50 shadow-lg transform scale-105'
                            : `${method.borderColor} ${method.bgColor} hover:shadow-md hover:transform hover:scale-102`
                        }`}
                        onClick={() => handlePaymentMethodSelect(method.id)}
                      >
                        <div className="text-center">
                          <div className="w-16 h-12 mx-auto mb-3 flex items-center justify-center">
                            {method.id === 'cashapp' ? (
                              <CashAppLogo size="md" />
                            ) : method.id === 'applepay' ? (
                              <div className="flex items-center gap-1">
                                <img
                                  src={method.logo}
                                  alt="Apple"
                                  className="w-6 h-7 object-contain"
                                />
                                <span className="text-sm font-semibold text-gray-800">Pay</span>
                              </div>
                            ) : (
                              <img
                                src={method.logo}
                                alt={method.name}
                                className="w-12 h-8 object-contain"
                                onError={(e) => {
                                  e.currentTarget.style.display = 'none';
                                  e.currentTarget.nextElementSibling!.style.display = 'block';
                                }}
                              />
                            )}
                            <div className="hidden text-xl font-bold text-gray-600">
                              {method.name}
                            </div>
                          </div>
                          <h3 className="font-semibold text-gray-800 mb-1">{method.nameAr}</h3>
                          <p className="text-sm text-gray-500 mb-3">{method.name}</p>
                          <p className="text-xs text-gray-400">{method.description}</p>

                          <div className="mt-4">
                            <div className={`w-6 h-6 rounded-full border-2 mx-auto flex items-center justify-center ${
                              selectedPaymentMethod === method.id
                                ? 'bg-primary-500 border-primary-500'
                                : 'border-gray-300'
                            }`}>
                              {selectedPaymentMethod === method.id && (
                                <span className="text-white text-sm font-bold">✓</span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* معلومات الأمان */}
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <span className="text-green-600">🔒</span>
                      <span className="text-sm text-green-700 font-medium">جميع المعاملات محمية بتشفير SSL</span>
                    </div>
                    <p className="text-xs text-green-600 text-center">
                      معلوماتك الشخصية والمالية محمية بأعلى معايير الأمان
                    </p>
                  </div>

                  {/* أزرار التحكم - تظهر فقط إذا لم يتم اختيار وسيلة دفع */}
                  {!showPaymentForm && (
                    <div className="flex gap-4 justify-center">
                      <button
                        onClick={() => {
                          setShowPayment(false);
                          scrollToOptimalPosition(6); // العودة لخطوة النشر
                        }}
                        className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        إلغاء
                      </button>
                      <button
                        onClick={() => {
                          if (!selectedPaymentMethod) {
                            showAlert(
                              'يرجى اختيار وسيلة الدفع',
                              'يجب اختيار وسيلة دفع لإتمام العملية',
                              'warning',
                              '💳'
                            );
                            return;
                          }
                          setShowPaymentForm(true);
                          // تمرير خاص لنموذج الدفع
                          setTimeout(() => {
                            window.scrollTo({
                              top: 80,
                              behavior: 'smooth'
                            });
                          }, 100);
                        }}
                        disabled={!selectedPaymentMethod}
                        className="px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-semibold"
                      >
                        💳 المتابعة إلى الدفع
                      </button>
                    </div>
                  )}

                  {/* نموذج الدفع */}
                  {showPaymentForm && renderPaymentForm()}
                </div>
              </div>
            )}
          </div>
        );
      default:
        return null;
    }
  };

  // عدم عرض المحتوى حتى يتم تحميل الـ client لتجنب hydration mismatch
  if (!isClient) {
    return (
      <>
        <Header />
        <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-8">
          <div className="max-w-6xl mx-auto px-4">
            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                <p className="text-gray-600">جاري التحميل...</p>
              </div>
            </div>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <Header />

      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-8">
        <div className="max-w-6xl mx-auto px-4">
          {/* العنوان الرئيسي */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">أضف إعلانك</h1>
            <p className="text-lg text-gray-600 mb-8">انشر إعلانك مجاناً واصل إلى آلاف المشترين</p>

            {/* شريط التقدم */}
            <div className="flex items-center justify-center mb-8 progress-bar">
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                {steps.map((step, index) => (
                  <div key={step.number} className="flex items-center">
                    <div
                      onClick={() => {
                        // السماح بالانتقال للخطوات المكتملة أو الخطوة التالية مباشرة
                        if (step.number <= currentStep + 1) {
                          setCurrentStep(step.number);
                          scrollToOptimalPosition(step.number);
                        }
                      }}
                      className={`flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-300 ${
                        currentStep >= step.number
                          ? 'bg-primary-600 border-primary-600 text-white cursor-pointer hover:bg-primary-700'
                          : step.number === currentStep + 1
                          ? 'bg-white border-gray-300 text-gray-400 cursor-pointer hover:border-primary-300 hover:text-primary-500'
                          : 'bg-white border-gray-300 text-gray-400 cursor-not-allowed'
                      }`}>
                      <span className="text-lg font-semibold">{step.number}</span>
                    </div>
                    <div className="mr-3 text-center">
                      <div className={`text-sm font-medium ${
                        currentStep >= step.number ? 'text-primary-600' : 'text-gray-400'
                      }`}>
                        {step.title}
                      </div>
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`w-16 h-0.5 mx-4 ${
                        currentStep > step.number ? 'bg-primary-600' : 'bg-gray-300'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* محتوى الخطوة */}
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 mb-8 step-content">
            {renderStepContent()}
          </div>

          {/* معلومات التواصل */}
          <ContactButtons contactInfo={COMPANY_CONTACT} />
        </div>
      </div>
      <Footer />



      {/* مودال التحذيرات */}
      <AlertModal
        isOpen={alertModal.isOpen}
        onClose={() => setAlertModal(prev => ({ ...prev, isOpen: false }))}
        title={alertModal.title}
        message={alertModal.message}
        type={alertModal.type}
        icon={alertModal.icon}
      />
    </>
  );
}
