// نظام الوظائف والسير الذاتية المتقدم

export interface Experience {
  id: string;
  jobTitle: string;
  company: string;
  location: string;
  startDate: string;
  endDate?: string;
  current: boolean;
  description: string;
  achievements: string[];
}

export interface Education {
  id: string;
  degree: string;
  institution: string;
  field: string;
  location: string;
  startDate: string;
  endDate?: string;
  current: boolean;
  gpa?: string;
  description?: string;
}

export interface Skill {
  id: string;
  name: string;
  level: 'مبتدئ' | 'متوسط' | 'متقدم' | 'خبير';
  category: 'تقني' | 'لغوي' | 'إداري' | 'إبداعي' | 'أخرى';
  verified: boolean;
}

export interface Language {
  id: string;
  name: string;
  level: 'مبتدئ' | 'متوسط' | 'متقدم' | 'أصلي';
  certification?: string;
}

export interface Course {
  id: string;
  name: string;
  provider: string;
  completionDate: string;
  certificateUrl?: string;
  skills: string[];
}

export interface Project {
  id: string;
  name: string;
  description: string;
  technologies: string[];
  url?: string;
  startDate: string;
  endDate?: string;
  status: 'مكتمل' | 'قيد التطوير' | 'متوقف';
}

export interface ContactInfo {
  phone: string;
  email: string;
  linkedin?: string;
  github?: string;
  portfolio?: string;
  address: string;
  city: string;
}

export interface Resume {
  id: string;
  userId: string;
  personalInfo: {
    firstName: string;
    lastName: string;
    title: string;
    summary: string;
    photo?: string;
    dateOfBirth?: string;
    nationality: string;
    maritalStatus?: string;
  };
  contactInfo: ContactInfo;
  experiences: Experience[];
  education: Education[];
  skills: Skill[];
  languages: Language[];
  courses: Course[];
  projects?: Project[];
  references?: {
    id: string;
    name: string;
    position: string;
    company: string;
    phone: string;
    email: string;
    relationship?: string;
  }[];
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
  views: number;
}

export interface JobPosting {
  id: string;
  companyId: string;
  companyName: string;
  companyLogo?: string;
  title: string;
  department: string;
  location: string;
  workType: 'دوام كامل' | 'دوام جزئي' | 'عمل حر' | 'تدريب' | 'عقد مؤقت';
  workModel: 'حضوري' | 'عن بعد';
  salaryRange: {
    min: number;
    max: number;
    currency: string;
    period: 'شهري' | 'سنوي' | 'يومي' | 'ساعي';
  };
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  skills: string[];
  experienceLevel: 'مبتدئ' | 'متوسط' | 'متقدم' | 'خبير';
  experienceYears: {
    min: number;
    max: number;
  };
  educationLevel: 'ثانوية' | 'دبلوم' | 'بكالوريوس' | 'ماجستير' | 'دكتوراه';
  applicationDeadline: string;
  postedDate: string;
  status: 'نشط' | 'مغلق' | 'مؤجل';
  applicationsCount: number;
  featured: boolean;
  urgent: boolean;
  category: string;
  subcategory?: string;
}

export interface JobApplication {
  id: string;
  jobId: string;
  applicantId: string;
  resumeId: string;
  coverLetter: string;
  status: 'مرسل' | 'قيد المراجعة' | 'مقبول للمقابلة' | 'مرفوض' | 'مقبول';
  appliedDate: string;
  lastUpdated: string;
  notes?: string;
}

// فئات الوظائف
export const JOB_CATEGORIES = [
  {
    id: 'technology',
    name: 'التكنولوجيا والبرمجة',
    icon: '💻',
    subcategories: [
      'تطوير الويب',
      'تطوير التطبيقات',
      'أمن المعلومات',
      'الذكاء الاصطناعي',
      'تحليل البيانات',
      'DevOps',
      'UI/UX Design',
      'إدارة المشاريع التقنية'
    ]
  },
  {
    id: 'medical',
    name: 'الطب والصحة',
    icon: '🏥',
    subcategories: [
      'أطباء',
      'ممرضين',
      'صيادلة',
      'أطباء أسنان',
      'فنيين طبيين',
      'إدارة صحية',
      'علاج طبيعي',
      'تغذية'
    ]
  },
  {
    id: 'engineering',
    name: 'الهندسة',
    icon: '⚙️',
    subcategories: [
      'هندسة مدنية',
      'هندسة كهربائية',
      'هندسة ميكانيكية',
      'هندسة معمارية',
      'هندسة صناعية',
      'هندسة بترول',
      'هندسة بيئية',
      'هندسة طيران'
    ]
  },
  {
    id: 'business',
    name: 'الأعمال والإدارة',
    icon: '💼',
    subcategories: [
      'إدارة عامة',
      'الموارد البشرية',
      'المبيعات',
      'التسويق',
      'المحاسبة',
      'المالية',
      'خدمة العملاء',
      'إدارة المشاريع'
    ]
  },
  {
    id: 'education',
    name: 'التعليم والتدريب',
    icon: '📚',
    subcategories: [
      'معلمين',
      'أساتذة جامعيين',
      'مدربين',
      'مستشارين تعليميين',
      'إدارة تعليمية',
      'تطوير مناهج',
      'تعليم خاص',
      'تعليم إلكتروني'
    ]
  },
  {
    id: 'creative',
    name: 'الإبداع والفنون',
    icon: '🎨',
    subcategories: [
      'تصميم جرافيك',
      'تصوير',
      'كتابة وتحرير',
      'إنتاج فيديو',
      'تصميم داخلي',
      'موسيقى',
      'إعلان وتسويق',
      'فنون بصرية'
    ]
  }
];

// مستويات الخبرة
export const EXPERIENCE_LEVELS = [
  { id: 'entry', name: 'مبتدئ', years: '0-2 سنوات' },
  { id: 'mid', name: 'متوسط', years: '2-5 سنوات' },
  { id: 'senior', name: 'متقدم', years: '5-10 سنوات' },
  { id: 'expert', name: 'خبير', years: '10+ سنوات' }
];

// أنواع العمل
export const WORK_TYPES = [
  { id: 'full-time', name: 'دوام كامل', icon: '🕘' },
  { id: 'part-time', name: 'دوام جزئي', icon: '🕐' },
  { id: 'freelance', name: 'عمل حر', icon: '💼' },
  { id: 'internship', name: 'تدريب', icon: '🎓' },
  { id: 'contract', name: 'عقد مؤقت', icon: '📄' }
];

// نماذج العمل
export const WORK_MODELS = [
  { id: 'onsite', name: 'حضوري', icon: '🏢' },
  { id: 'remote', name: 'عن بعد', icon: '🏠' }
];

// مستويات التعليم
export const EDUCATION_LEVELS = [
  { id: 'high-school', name: 'ثانوية عامة' },
  { id: 'diploma', name: 'دبلوم' },
  { id: 'bachelor', name: 'بكالوريوس' },
  { id: 'master', name: 'ماجستير' },
  { id: 'phd', name: 'دكتوراه' }
];

// دوال مساعدة
export const JobUtils = {
  // تنسيق نطاق الراتب
  formatSalaryRange: (range: JobPosting['salaryRange']) => {
    if (range.min === range.max) {
      return `${range.min.toLocaleString()} ${range.currency} ${range.period}`;
    }
    return `${range.min.toLocaleString()} - ${range.max.toLocaleString()} ${range.currency} ${range.period}`;
  },

  // حساب عدد أيام النشر
  getDaysAgo: (date: string) => {
    const posted = new Date(date);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - posted.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'منذ يوم واحد';
    if (diffDays < 7) return `منذ ${diffDays} أيام`;
    if (diffDays < 30) return `منذ ${Math.floor(diffDays / 7)} أسابيع`;
    return `منذ ${Math.floor(diffDays / 30)} أشهر`;
  },

  // دالة لإرجاع نمط CSS للأيقونات والمراحل (سادة وشفافة، وتوهج فقط عند الضغط، حجم احترافي)
  getStepIconStyle: (active: boolean = false) => ({
    background: 'rgba(255,255,255,0.85)',
    border: 'none',
    color: '#22c55e',
    boxShadow: active
      ? '0 0 16px 4px rgba(34,197,94,0.25), 0 0 0 2px #22c55e'
      : '0 2px 8px 0 rgba(34,197,94,0.08)',
    borderRadius: '50%',
    padding: '0',
    width: '44px',
    height: '44px',
    fontSize: '1.6rem',
    transition: 'all 0.2s',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
    outline: active ? 'none' : undefined,
  }),

  // فلترة الوظائف
  filterJobs: (jobs: JobPosting[], filters: {
    category?: string;
    workType?: string;
    workModel?: string;
    experienceLevel?: string;
    location?: string;
    salaryMin?: number;
    salaryMax?: number;
  }) => {
    return jobs.filter(job => {
      if (filters.category && job.category !== filters.category) return false;
      if (filters.workType && job.workType !== filters.workType) return false;
      if (filters.workModel && job.workModel !== filters.workModel) return false;
      if (filters.experienceLevel && job.experienceLevel !== filters.experienceLevel) return false;
      if (filters.location && !job.location.includes(filters.location)) return false;
      if (filters.salaryMin && job.salaryRange.min < filters.salaryMin) return false;
      if (filters.salaryMax && job.salaryRange.max > filters.salaryMax) return false;
      return true;
    });
  },

  // البحث في الوظائف
  searchJobs: (jobs: JobPosting[], query: string) => {
    const searchTerm = query.toLowerCase();
    return jobs.filter(job =>
      job.title.toLowerCase().includes(searchTerm) ||
      job.companyName.toLowerCase().includes(searchTerm) ||
      job.description.toLowerCase().includes(searchTerm) ||
      job.skills.some(skill => skill.toLowerCase().includes(searchTerm))
    );
  }
};

export default {
  JOB_CATEGORIES,
  EXPERIENCE_LEVELS,
  WORK_TYPES,
  WORK_MODELS,
  EDUCATION_LEVELS,
  JobUtils
};
