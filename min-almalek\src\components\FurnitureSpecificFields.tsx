'use client';

import { useState } from 'react';

interface FurnitureData {
  mainCategory?: string;
  subCategory?: string;
  condition?: string;
  material?: string;
  color?: string;
  brand?: string;
  dimensions?: string;
  type?: string;
}

interface FurnitureSpecificFieldsProps {
  data: FurnitureData;
  onChange: (data: FurnitureData) => void;
}

const furnitureCategories = {
  'living-room': {
    name: 'أثاث غرف المعيشة',
    subCategories: [
      'كنبات',
      'كراسي',
      'طاولات قهوة',
      'طاولات جانبية',
      'مكتبات وأرفف',
      'وحدات تلفزيون',
      'بوفيهات'
    ]
  },
  'bedroom': {
    name: 'أثاث غرف النوم',
    subCategories: [
      'أسرّة',
      'خزائن ملابس',
      'كومودينات',
      'تسريحات',
      'طاولات جانبية',
      'مرايا'
    ]
  },
  'dining-kitchen': {
    name: 'أثاث غرف الطعام والمطابخ',
    subCategories: [
      'طاولات طعام',
      'كراسي طعام',
      'خزائن مطبخ',
      'بوفيهات طعام'
    ]
  },
  'office': {
    name: 'أثاث المكاتب',
    subCategories: [
      'مكاتب عمل',
      'كراسي مكتب',
      'خزائن ملفات',
      'رفوف كتب'
    ]
  },
  'outdoor-garden': {
    name: 'أثاث خارجي وحدائق',
    subCategories: [
      'طاولات وكراسي خارجية',
      'جلسات حدائق',
      'مظلات وشماسي',
      'شوايات',
      'أرجوحات'
    ]
  },
  'appliances': {
    name: 'أجهزة كهربائية منزلية',
    subCategories: [
      'ثلاجات',
      'أفران',
      'ميكروويف',
      'غسالات',
      'مجففات',
      'مكانس كهربائية',
      'مكيفات'
    ]
  },
  'kitchen-tools': {
    name: 'أدوات المطبخ',
    subCategories: [
      'أواني الطهي (قدور، مقالي)',
      'أواني التقديم (صحون، كاسات، فناجين)',
      'أدوات الخبز',
      'أدوات تقطيع وتحضير'
    ]
  },
  'home-tools': {
    name: 'أدوات منزلية عامة',
    subCategories: [
      'سلل غسيل',
      'سلال قمامة',
      'مراوح',
      'سخانات مياه'
    ]
  },
  'decor': {
    name: 'ديكور وإكسسوارات منزلية',
    subCategories: [
      'لوحات فنية',
      'تماثيل وزينة',
      'فازات وأحواض نباتات',
      'ساعات حائط'
    ]
  },
  'lighting': {
    name: 'إضاءة',
    subCategories: [
      'ثريات',
      'أباجورات',
      'إضاءة جدارية',
      'إضاءة خارجية'
    ]
  },
  'carpets-curtains': {
    name: 'سجاد وستائر',
    subCategories: [
      'سجاد أرضيات',
      'ستائر نوافذ',
      'بسط'
    ]
  },
  'storage': {
    name: 'تخزين وتنظيم',
    subCategories: [
      'صناديق تخزين',
      'منظمات أدراج',
      'رفوف حائط'
    ]
  }
};

export default function FurnitureSpecificFields({ data, onChange }: FurnitureSpecificFieldsProps) {
  const handleChange = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    onChange(newData);
  };

  const conditionOptions = ['جديد', 'مستعمل - ممتاز', 'مستعمل - جيد', 'مستعمل - مقبول'];
  const materialOptions = ['خشب', 'معدن', 'بلاستيك', 'زجاج', 'قماش', 'جلد', 'رخام', 'سيراميك', 'أخرى'];
  const colorOptions = ['أسود', 'أبيض', 'بني', 'بيج', 'رمادي', 'أزرق', 'أحمر', 'أخضر', 'أصفر', 'وردي', 'بنفسجي', 'ذهبي'];
  const brandOptions = ['IKEA', 'Ashley', 'La-Z-Boy', 'West Elm', 'Pottery Barn', 'Crate & Barrel', 'Wayfair', 'محلي', 'أخرى'];

  return (
    <div className="space-y-6">
      <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-amber-800 mb-4 flex items-center gap-2">
          🏠 تفاصيل الأثاث والمنزل
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* الفئة الرئيسية */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الفئة الرئيسية *
            </label>
            <select
              value={data.mainCategory || ''}
              onChange={(e) => handleChange('mainCategory', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              required
            >
              <option value="">اختر الفئة الرئيسية</option>
              {Object.entries(furnitureCategories).map(([key, category]) => (
                <option key={key} value={key}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* الفئة الفرعية */}
          {data.mainCategory && furnitureCategories[data.mainCategory as keyof typeof furnitureCategories] && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الفئة الفرعية *
              </label>
              <select
                value={data.subCategory || ''}
                onChange={(e) => handleChange('subCategory', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
                required
              >
                <option value="">اختر الفئة الفرعية</option>
                {furnitureCategories[data.mainCategory as keyof typeof furnitureCategories].subCategories.map((sub, index) => (
                  <option key={index} value={sub}>
                    {sub}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* الحالة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الحالة *
            </label>
            <select
              value={data.condition || ''}
              onChange={(e) => handleChange('condition', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              required
            >
              <option value="">اختر الحالة</option>
              {conditionOptions.map((condition) => (
                <option key={condition} value={condition}>
                  {condition}
                </option>
              ))}
            </select>
          </div>

          {/* المادة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              المادة
            </label>
            <select
              value={data.material || ''}
              onChange={(e) => handleChange('material', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
            >
              <option value="">اختر المادة</option>
              {materialOptions.map((material) => (
                <option key={material} value={material}>
                  {material}
                </option>
              ))}
            </select>
          </div>

          {/* اللون */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اللون
            </label>
            <select
              value={data.color || ''}
              onChange={(e) => handleChange('color', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
            >
              <option value="">اختر اللون</option>
              {colorOptions.map((color) => (
                <option key={color} value={color}>
                  {color}
                </option>
              ))}
            </select>
          </div>

          {/* الماركة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الماركة
            </label>
            <select
              value={data.brand || ''}
              onChange={(e) => handleChange('brand', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
            >
              <option value="">اختر الماركة</option>
              {brandOptions.map((brand) => (
                <option key={brand} value={brand}>
                  {brand}
                </option>
              ))}
            </select>
          </div>

          {/* الأبعاد */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الأبعاد/المقاس
            </label>
            <input
              type="text"
              value={data.dimensions || ''}
              onChange={(e) => handleChange('dimensions', e.target.value)}
              placeholder="مثال: 200 × 100 × 80 سم"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
