import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto text-center">
          <div className="text-8xl mb-8">🔍</div>
          
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            404
          </h1>
          
          <h2 className="text-2xl font-semibold text-gray-700 mb-6">
            الصفحة غير موجودة
          </h2>
          
          <p className="text-gray-600 mb-8 leading-relaxed">
            عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر.
            يمكنك العودة إلى الصفحة الرئيسية أو البحث عن ما تريد.
          </p>
          
          <div className="space-y-4">
            <Link
              href="/"
              className="block w-full bg-primary-600 text-white py-3 px-6 rounded-lg hover:bg-primary-700 transition-colors font-semibold"
            >
              العودة للصفحة الرئيسية
            </Link>
            
            <Link
              href="/ads"
              className="block w-full border border-primary-600 text-primary-600 py-3 px-6 rounded-lg hover:bg-primary-50 transition-colors font-semibold"
            >
              تصفح جميع الإعلانات
            </Link>
          </div>
          
          <div className="mt-12 grid grid-cols-2 gap-4 text-sm">
            <Link
              href="/search"
              className="flex items-center justify-center gap-2 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow"
            >
              <span>🔍</span>
              <span>البحث المتقدم</span>
            </Link>
            
            <Link
              href="/contact"
              className="flex items-center justify-center gap-2 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow"
            >
              <span>📞</span>
              <span>اتصل بنا</span>
            </Link>
          </div>
        </div>
        
        {/* اقتراحات شائعة */}
        <div className="mt-16 max-w-4xl mx-auto">
          <h3 className="text-xl font-semibold text-gray-800 text-center mb-8">
            أو جرب هذه الروابط الشائعة
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Link
              href="/category/real-estate"
              className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow text-center"
            >
              <div className="text-3xl mb-3">🏠</div>
              <h4 className="font-semibold text-gray-800 mb-2">العقارات</h4>
              <p className="text-sm text-gray-600">شقق، فيلات، أراضي</p>
            </Link>
            
            <Link
              href="/category/cars"
              className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow text-center"
            >
              <div className="text-3xl mb-3">🚗</div>
              <h4 className="font-semibold text-gray-800 mb-2">السيارات</h4>
              <p className="text-sm text-gray-600">جديدة ومستعملة</p>
            </Link>
            
            <Link
              href="/category/electronics"
              className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow text-center"
            >
              <div className="text-3xl mb-3">📱</div>
              <h4 className="font-semibold text-gray-800 mb-2">الإلكترونيات</h4>
              <p className="text-sm text-gray-600">هواتف، حاسوب، أجهزة</p>
            </Link>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
