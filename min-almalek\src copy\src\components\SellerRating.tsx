'use client';

import { useState } from 'react';
import StarRating from './StarRating';

interface Review {
  id: number;
  reviewerName: string;
  rating: number;
  comment: string;
  date: string;
  adTitle: string;
  verified: boolean;
}

interface SellerRatingProps {
  sellerId: number;
  sellerName: string;
  averageRating: number;
  totalReviews: number;
  canReview?: boolean;
}

const SellerRating = ({ 
  sellerId, 
  sellerName, 
  averageRating, 
  totalReviews, 
  canReview = false 
}: SellerRatingProps) => {
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [newReview, setNewReview] = useState({
    rating: 5,
    comment: ''
  });

  const reviews: Review[] = [
    {
      id: 1,
      reviewerName: 'محمد أحمد',
      rating: 5,
      comment: 'بائع ممتاز، تعامل راقي وصادق في الوصف. أنصح بالتعامل معه.',
      date: '2024-01-15',
      adTitle: 'شقة للبيع في دمشق',
      verified: true
    },
    {
      id: 2,
      reviewerName: 'سارة علي',
      rating: 4,
      comment: 'تعامل جيد، لكن كان هناك تأخير بسيط في الموعد المحدد.',
      date: '2024-01-10',
      adTitle: 'سيارة BMW للبيع',
      verified: true
    },
    {
      id: 3,
      reviewerName: 'أحمد محمود',
      rating: 5,
      comment: 'بائع موثوق، المنتج كما هو موصوف تماماً. شكراً لك.',
      date: '2024-01-05',
      adTitle: 'لابتوب Dell Gaming',
      verified: false
    }
  ];

  const ratingDistribution = [
    { stars: 5, count: 12, percentage: 60 },
    { stars: 4, count: 5, percentage: 25 },
    { stars: 3, count: 2, percentage: 10 },
    { stars: 2, count: 1, percentage: 5 },
    { stars: 1, count: 0, percentage: 0 }
  ];



  const handleSubmitReview = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('تقييم جديد:', newReview);
    setShowReviewForm(false);
    setNewReview({ rating: 5, comment: '' });
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <h3 className="text-xl font-bold text-gray-800 mb-6">تقييمات البائع</h3>

      {/* ملخص التقييم */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div className="text-center">
          <div className="text-4xl font-bold text-primary-600 mb-2">
            {averageRating.toFixed(1)}
          </div>
          <StarRating rating={averageRating} size="lg" showValue={false} />
          <div className="text-gray-600 mt-2">
            من أصل {totalReviews} تقييم
          </div>
        </div>

        <div className="space-y-2">
          {ratingDistribution.map((item) => (
            <div key={item.stars} className="flex items-center gap-3">
              <div className="flex items-center gap-1 w-8">
                <span className="text-sm text-gray-600">{item.stars}</span>
                <StarRating rating={1} size="xs" showValue={false} />
              </div>
              <div className="flex-1 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-yellow-400 h-2 rounded-full transition-all duration-500"
                  style={{ width: `${item.percentage}%` }}
                />
              </div>
              <span className="text-sm text-gray-600 w-8">
                {item.count}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* زر إضافة تقييم */}
      {canReview && (
        <div className="mb-6">
          <button
            onClick={() => setShowReviewForm(!showReviewForm)}
            className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
          >
            {showReviewForm ? 'إلغاء' : 'إضافة تقييم'}
          </button>
        </div>
      )}

      {/* نموذج إضافة تقييم */}
      {showReviewForm && (
        <div className="bg-gray-50 rounded-lg p-6 mb-6">
          <h4 className="text-lg font-semibold text-gray-800 mb-4">
            أضف تقييمك لـ {sellerName}
          </h4>
          <form onSubmit={handleSubmitReview} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                التقييم
              </label>
              <StarRating
                rating={newReview.rating}
                size="lg"
                interactive={true}
                onChange={(rating) => setNewReview(prev => ({ ...prev, rating }))}
                showValue={false}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                التعليق
              </label>
              <textarea
                value={newReview.comment}
                onChange={(e) => setNewReview(prev => ({ ...prev, comment: e.target.value }))}
                rows={4}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="شاركنا تجربتك مع هذا البائع..."
                required
              />
            </div>

            <div className="flex gap-3">
              <button
                type="submit"
                className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
              >
                إرسال التقييم
              </button>
              <button
                type="button"
                onClick={() => setShowReviewForm(false)}
                className="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </form>
        </div>
      )}

      {/* قائمة التقييمات */}
      <div className="space-y-6">
        <h4 className="text-lg font-semibold text-gray-800">
          آراء العملاء ({reviews.length})
        </h4>

        {reviews.map((review) => (
          <div key={review.id} className="border-b border-gray-100 pb-6 last:border-b-0">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-primary-600 font-semibold">
                    {review.reviewerName.charAt(0)}
                  </span>
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-800">
                      {review.reviewerName}
                    </span>
                    {review.verified && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                        ✓ موثق
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-gray-600">
                    بخصوص: {review.adTitle}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <StarRating rating={review.rating} size="sm" showValue={false} />
                <div className="text-xs text-gray-500 mt-1">
                  {review.date}
                </div>
              </div>
            </div>

            <p className="text-gray-700 leading-relaxed">
              {review.comment}
            </p>
          </div>
        ))}

        {reviews.length === 0 && (
          <div className="text-center py-8">
            <div className="text-4xl mb-4">💬</div>
            <h4 className="text-lg font-semibold text-gray-800 mb-2">
              لا توجد تقييمات بعد
            </h4>
            <p className="text-gray-600">
              كن أول من يقيم هذا البائع
            </p>
          </div>
        )}

        {reviews.length > 3 && (
          <div className="text-center">
            <button className="text-primary-600 hover:text-primary-700 font-medium">
              عرض جميع التقييمات ({totalReviews})
            </button>
          </div>
        )}
      </div>

      {/* إحصائيات إضافية */}
      <div className="mt-8 pt-6 border-t border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
          <div className="bg-green-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-green-600 mb-1">
              {Math.round((ratingDistribution[0].count + ratingDistribution[1].count) / totalReviews * 100)}%
            </div>
            <div className="text-sm text-gray-600">تقييمات إيجابية</div>
          </div>
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-600 mb-1">
              {Math.round(averageRating * 20)}%
            </div>
            <div className="text-sm text-gray-600">معدل الرضا</div>
          </div>
          <div className="bg-purple-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-purple-600 mb-1">
              {totalReviews}
            </div>
            <div className="text-sm text-gray-600">إجمالي التقييمات</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SellerRating;
