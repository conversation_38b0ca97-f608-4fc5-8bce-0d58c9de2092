'use client';

import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function TestPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8 text-center">
            <div className="text-6xl mb-6">✅</div>
            <h1 className="text-3xl font-bold text-gray-800 mb-4">
              صفحة الاختبار
            </h1>
            <p className="text-lg text-gray-600 mb-8">
              هذه صفحة اختبار للتأكد من أن التوجيه يعمل بشكل صحيح
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="text-3xl mb-3">🏠</div>
                <h3 className="font-semibold text-blue-800 mb-2">الصفحة الرئيسية</h3>
                <p className="text-blue-600 text-sm">العودة إلى الصفحة الرئيسية</p>
              </div>
              
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="text-3xl mb-3">📋</div>
                <h3 className="font-semibold text-green-800 mb-2">الإعلانات</h3>
                <p className="text-green-600 text-sm">تصفح جميع الإعلانات</p>
              </div>
              
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                <div className="text-3xl mb-3">💼</div>
                <h3 className="font-semibold text-purple-800 mb-2">الوظائف</h3>
                <p className="text-purple-600 text-sm">البحث عن وظائف</p>
              </div>
            </div>
            
            <div className="mt-8 p-4 bg-gray-100 rounded-lg">
              <p className="text-gray-700">
                إذا كنت ترى هذه الصفحة، فهذا يعني أن التوجيه يعمل بشكل صحيح! 🎉
              </p>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
