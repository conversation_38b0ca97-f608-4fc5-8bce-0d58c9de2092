'use client';

import { useState } from 'react';

interface Notification {
  id: number;
  type: 'message' | 'ad_expired' | 'ad_approved' | 'payment' | 'system';
  title: string;
  message: string;
  time: string;
  read: boolean;
  actionUrl?: string;
}

const NotificationCenter = () => {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 1,
      type: 'message',
      title: 'رسالة جديدة',
      message: 'لديك رسالة جديدة من محمد أحمد بخصوص إعلان "شقة للبيع في دمشق"',
      time: 'منذ 5 دقائق',
      read: false,
      actionUrl: '/dashboard?tab=messages'
    },
    {
      id: 2,
      type: 'ad_approved',
      title: 'تم قبول إعلانك',
      message: 'تم قبول ونشر إعلان "سيارة BMW للبيع" وهو الآن متاح للمشاهدة',
      time: 'منذ ساعة',
      read: false,
      actionUrl: '/ad/123'
    },
    {
      id: 3,
      type: 'ad_expired',
      title: 'انتهاء صلاحية إعلان',
      message: 'انتهت صلاحية إعلان "لابتوب Dell Gaming". يمكنك تجديده الآن',
      time: 'منذ 3 ساعات',
      read: true,
      actionUrl: '/dashboard?tab=ads'
    },
    {
      id: 4,
      type: 'payment',
      title: 'تم الدفع بنجاح',
      message: 'تم تأكيد دفع اشتراك الخطة المميزة. مميزاتك الجديدة متاحة الآن',
      time: 'منذ يوم',
      read: true,
      actionUrl: '/dashboard'
    },
    {
      id: 5,
      type: 'system',
      title: 'تحديث الموقع',
      message: 'تم إضافة ميزات جديدة للبحث المتقدم. اكتشف المزيد الآن',
      time: 'منذ يومين',
      read: true,
      actionUrl: '/ads'
    }
  ]);

  const [filter, setFilter] = useState<'all' | 'unread'>('all');

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'message': return '💬';
      case 'ad_expired': return '⏰';
      case 'ad_approved': return '✅';
      case 'payment': return '💳';
      case 'system': return '🔔';
      default: return '📢';
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'message': return 'bg-blue-100 border-blue-200';
      case 'ad_expired': return 'bg-orange-100 border-orange-200';
      case 'ad_approved': return 'bg-green-100 border-green-200';
      case 'payment': return 'bg-purple-100 border-purple-200';
      case 'system': return 'bg-gray-100 border-gray-200';
      default: return 'bg-gray-100 border-gray-200';
    }
  };

  const markAsRead = (id: number) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notif => ({ ...notif, read: true }))
    );
  };

  const deleteNotification = (id: number) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const filteredNotifications = filter === 'unread' 
    ? notifications.filter(n => !n.read)
    : notifications;

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <div className="bg-white rounded-xl shadow-lg">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-800 flex items-center gap-2">
            🔔 الإشعارات
            {unreadCount > 0 && (
              <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                {unreadCount}
              </span>
            )}
          </h2>
          {unreadCount > 0 && (
            <button
              onClick={markAllAsRead}
              className="text-sm text-primary-600 hover:text-primary-700 font-medium"
            >
              تحديد الكل كمقروء
            </button>
          )}
        </div>

        {/* Filters */}
        <div className="flex gap-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === 'all'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            الكل ({notifications.length})
          </button>
          <button
            onClick={() => setFilter('unread')}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
              filter === 'unread'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            غير مقروءة ({unreadCount})
          </button>
        </div>
      </div>

      {/* Notifications List */}
      <div className="max-h-96 overflow-y-auto">
        {filteredNotifications.length > 0 ? (
          <div className="divide-y divide-gray-100">
            {filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`p-4 hover:bg-gray-50 transition-colors ${
                  !notification.read ? 'bg-blue-50' : ''
                }`}
              >
                <div className="flex items-start gap-4">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center text-lg ${getNotificationColor(notification.type)}`}>
                    {getNotificationIcon(notification.type)}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className={`font-medium ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>
                          {notification.title}
                        </h3>
                        <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                          {notification.message}
                        </p>
                        <div className="flex items-center gap-4 mt-2">
                          <span className="text-xs text-gray-500">{notification.time}</span>
                          {!notification.read && (
                            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2 ml-4">
                        {!notification.read && (
                          <button
                            onClick={() => markAsRead(notification.id)}
                            className="text-xs text-blue-600 hover:text-blue-700"
                            title="تحديد كمقروء"
                          >
                            ✓
                          </button>
                        )}
                        <button
                          onClick={() => deleteNotification(notification.id)}
                          className="text-xs text-red-600 hover:text-red-700"
                          title="حذف"
                        >
                          ×
                        </button>
                      </div>
                    </div>
                    
                    {notification.actionUrl && (
                      <div className="mt-3">
                        <a
                          href={notification.actionUrl}
                          className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                        >
                          عرض التفاصيل ←
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-8 text-center">
            <div className="text-4xl mb-4">🔔</div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              {filter === 'unread' ? 'لا توجد إشعارات غير مقروءة' : 'لا توجد إشعارات'}
            </h3>
            <p className="text-gray-600">
              {filter === 'unread' 
                ? 'جميع إشعاراتك مقروءة' 
                : 'ستظهر إشعاراتك هنا عند وصولها'
              }
            </p>
          </div>
        )}
      </div>

      {/* Footer */}
      {filteredNotifications.length > 0 && (
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">
              {filteredNotifications.length} إشعار
            </span>
            <button className="text-primary-600 hover:text-primary-700 font-medium">
              إعدادات الإشعارات
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationCenter;
