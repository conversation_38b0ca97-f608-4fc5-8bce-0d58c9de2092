'use client';

import { useState } from 'react';
import AdCard from './AdCard';
import AdvancedSearch from './AdvancedSearch';
import { sampleAds } from '@/lib/data';

const AdsList = () => {
  const [layout, setLayout] = useState<'grid' | 'list'>('grid');
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [adsPerPage] = useState(12);

  // حساب الإعلانات للصفحة الحالية
  const indexOfLastAd = currentPage * adsPerPage;
  const indexOfFirstAd = indexOfLastAd - adsPerPage;
  const currentAds = sampleAds.slice(indexOfFirstAd, indexOfLastAd);
  const totalPages = Math.ceil(sampleAds.length / adsPerPage);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* عنوان الصفحة */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">جميع الإعلانات</h1>
          <p className="text-gray-600">
            عرض {indexOfFirstAd + 1}-{Math.min(indexOfLastAd, sampleAds.length)} من أصل {sampleAds.length} إعلان
          </p>
        </div>

        {/* أزرار التحكم */}
        <div className="flex items-center gap-4">
          <button
            onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              showAdvancedSearch
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            🔍 بحث متقدم
          </button>

          <div className="flex border border-gray-300 rounded-lg overflow-hidden">
            <button
              onClick={() => setLayout('grid')}
              className={`px-4 py-2 ${
                layout === 'grid'
                  ? 'bg-primary-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              ⊞ شبكة
            </button>
            <button
              onClick={() => setLayout('list')}
              className={`px-4 py-2 ${
                layout === 'list'
                  ? 'bg-primary-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              ☰ قائمة
            </button>
          </div>
        </div>
      </div>

      {/* البحث المتقدم */}
      {showAdvancedSearch && <AdvancedSearch />}

      {/* فلاتر سريعة */}
      <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
        <div className="flex flex-wrap gap-2">
          <span className="text-sm font-medium text-gray-700 mr-4">فلاتر سريعة:</span>
          {['مميز', 'عاجل', 'جديد', 'اليوم', 'هذا الأسبوع'].map((filter) => (
            <button
              key={filter}
              className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-primary-100 hover:text-primary-700 transition-colors"
            >
              {filter}
            </button>
          ))}
        </div>
      </div>

      {/* قائمة الإعلانات */}
      <div className={
        layout === 'grid'
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
          : 'space-y-4'
      }>
        {currentAds.map((ad) => (
          <AdCard key={ad.id} ad={ad} viewMode={layout} />
        ))}
      </div>

      {/* رسالة عدم وجود نتائج */}
      {currentAds.length === 0 && (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔍</div>
          <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد إعلانات</h3>
          <p className="text-gray-600">جرب تغيير معايير البحث أو الفلاتر</p>
        </div>
      )}

      {/* التنقل بين الصفحات */}
      {totalPages > 1 && (
        <div className="flex justify-center items-center gap-2 mt-8">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            السابق
          </button>

          {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNumber) => (
            <button
              key={pageNumber}
              onClick={() => handlePageChange(pageNumber)}
              className={`px-4 py-2 rounded-lg ${
                currentPage === pageNumber
                  ? 'bg-primary-600 text-white'
                  : 'border border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              {pageNumber}
            </button>
          ))}

          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            التالي
          </button>
        </div>
      )}

      {/* إحصائيات */}
      <div className="mt-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl p-6 text-white">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold mb-1">{sampleAds.length}</div>
            <div className="text-primary-100">إجمالي الإعلانات</div>
          </div>
          <div>
            <div className="text-2xl font-bold mb-1">
              {sampleAds.filter(ad => ad.featured).length}
            </div>
            <div className="text-primary-100">إعلانات مميزة</div>
          </div>
          <div>
            <div className="text-2xl font-bold mb-1">
              {sampleAds.filter(ad => ad.status === 'active').length}
            </div>
            <div className="text-primary-100">إعلانات نشطة</div>
          </div>
          <div>
            <div className="text-2xl font-bold mb-1">
              {sampleAds.reduce((sum, ad) => sum + ad.views, 0)}
            </div>
            <div className="text-primary-100">إجمالي المشاهدات</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdsList;
