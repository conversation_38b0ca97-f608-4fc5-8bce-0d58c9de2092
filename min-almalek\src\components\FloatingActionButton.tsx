'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter, usePathname } from 'next/navigation';

interface AssistiveTouchProps {
  onFilterClick?: () => void;
}

export default function AssistiveTouch({ onFilterClick }: AssistiveTouchProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [position, setPosition] = useState({ x: 20, y: 100 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const buttonRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleStart = (clientX: number, clientY: number) => {
    if (isOpen) return;
    setIsDragging(true);
    setDragStart({
      x: clientX - position.x,
      y: clientY - position.y
    });
  };

  const handleMouseDown = (e: React.MouseEvent) => handleStart(e.clientX, e.clientY);
  const handleTouchStart = (e: React.TouchEvent) => {
    const touch = e.touches[0];
    handleStart(touch.clientX, touch.clientY);
  };

  const handleMove = (clientX: number, clientY: number) => {
    if (!isDragging) return;
    const newX = clientX - dragStart.x;
    const newY = clientY - dragStart.y;

    const radius = 110; // نصف قطر الدائرة
    const buttonSize = 64; // حجم الزر الرئيسي
    const maxX = window.innerWidth - radius - buttonSize / 2;
    const minX = radius - buttonSize / 2;
    const maxY = window.innerHeight - radius - buttonSize / 2;
    const minY = radius - buttonSize / 2;

    setPosition({
      x: Math.max(minX, Math.min(newX, maxX)),
      y: Math.max(minY, Math.min(newY, maxY))
    });
  };

  const handleMouseMove = (e: MouseEvent) => handleMove(e.clientX, e.clientY);
  const handleTouchMove = (e: TouchEvent) => {
    e.preventDefault();
    const touch = e.touches[0];
    handleMove(touch.clientX, touch.clientY);
  };

  const handleEnd = () => setIsDragging(false);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleEnd);
      document.addEventListener('touchmove', handleTouchMove, { passive: false });
      document.addEventListener('touchend', handleEnd);
    }
    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleEnd);
      document.removeEventListener('touchmove', handleTouchMove);
      document.removeEventListener('touchend', handleEnd);
    };
  }, [isDragging, dragStart]);

  const handleGoBack = () => { window.history.back(); setIsOpen(false); };
  const handleGoHome = () => { router.push('/'); setIsOpen(false); };
  const handleMyAds = () => { router.push('/dashboard'); setIsOpen(false); };
  const handleJobs = () => { router.push('/jobs/all'); setIsOpen(false); };

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (!isDragging) setIsOpen(!isOpen);
  };

  const menuItems = [
    { icon: '/images/AssistiveTouch/الصفحة الرئيسية.png', label: 'الرئيسية', action: handleGoHome },
    { icon: '/images/AssistiveTouch/رجوع.png', label: 'رجوع', action: handleGoBack },
    { icon: '/images/AssistiveTouch/إعلاناتي.png', label: 'إعلاناتي', action: handleMyAds },
    { icon: '/images/AssistiveTouch/أيقونة الوظائف.png', label: 'الوظائف', action: handleJobs }
  ];

  const containerSize = 320; // w-80 = 320px
  const radius = 110;
  const buttonSize = 64;

  return (
    <div
      ref={buttonRef}
      className="fixed z-50 transition-all duration-300 block"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        cursor: isDragging ? 'grabbing' : 'grab',
        display: 'block',
        visibility: 'visible'
      }}
    >
      {isOpen && (
        <div className="relative w-80 h-80">
          {/* الخلفية الدائرية */}
          <div
            className="absolute inset-0 rounded-full backdrop-blur-lg"
            style={{
              background:
                'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 50%, rgba(0,0,0,0.1) 100%)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 8px 32px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.1)'
            }}
          />

          {/* شعار الموقع في الوسط */}
          <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16">
            <img
              src="/images/AssistiveTouch/شعار موقع من المالك.png"
              alt="شعار الموقع"
              className="w-full h-full object-contain"
            />
          </div>

          {/* الأزرار الأربعة */}
          {menuItems.map((item, index) => {
            let x = containerSize / 2 - buttonSize / 2;
            let y = containerSize / 2 - buttonSize / 2;

            switch (item.label) {
              case 'الرئيسية': x += radius; break; // يمين
              case 'رجوع': x -= radius; break;    // يسار
              case 'الوظائف': y += radius; break; // تحت
              case 'إعلاناتي': y -= radius; break; // فوق
            }

            return (
              <div
                key={index}
                className="absolute flex flex-col items-center opacity-0"
                style={{
                  left: `${x}px`,
                  top: `${y}px`,
                  animation: `fadeInScale 0.4s ease-out ${index * 150}ms forwards`
                }}
              >
                <button
                  onClick={item.action}
                  className="w-16 h-16 rounded-full flex items-center justify-center hover:scale-110 transition-all duration-200 p-2"
                  style={{
                    background: 'linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.1) 100%)',
                    border: '1px solid rgba(255,255,255,0.3)',
                    boxShadow: '0 4px 16px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.2)',
                    backdropFilter: 'blur(10px)'
                  }}
                >
                  <img src={item.icon} alt={item.label} className="w-full h-full object-contain" />
                </button>
                <span className="text-white text-xs font-medium text-center mt-1 drop-shadow-lg">{item.label}</span>
              </div>
            );
          })}
        </div>
      )}

      {/* الزر الرئيسي */}
      <div
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        onClick={handleClick}
        className={`w-16 h-16 bg-black bg-opacity-70 backdrop-blur-md rounded-2xl flex items-center justify-center transition-all duration-300 hover:scale-105 active:scale-95 ${
          isOpen ? 'scale-110 bg-opacity-80' : 'scale-100'
        }`}
        style={{
          border: '2px solid rgba(255, 255, 255, 0.3)',
          boxShadow: isOpen
            ? '0 12px 40px rgba(0, 0, 0, 0.5), 0 0 20px rgba(255, 255, 255, 0.1)'
            : '0 8px 32px rgba(0, 0, 0, 0.3)',
          userSelect: 'none',
          touchAction: 'none'
        }}
      >
        <img
          src="/images/AssistiveTouch/AssistiveTouch.png"
          alt="Assistive Touch"
          className="w-10 h-10"
          style={{ filter: isOpen ? 'brightness(1.2)' : 'brightness(1)' }}
        />
      </div>

      <style jsx>{`
        @keyframes fadeInScale {
          0% { opacity: 0; transform: scale(0.5); }
          100% { opacity: 1; transform: scale(1); }
        }
      `}</style>
    </div>
  );
}