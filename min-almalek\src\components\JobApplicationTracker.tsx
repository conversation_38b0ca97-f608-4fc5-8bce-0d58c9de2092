'use client';

import { useState, useEffect } from 'react';

interface Application {
  id: string;
  jobTitle: string;
  company: string;
  appliedDate: string;
  status: 'pending' | 'reviewed' | 'interview' | 'accepted' | 'rejected';
  lastUpdate: string;
  notes?: string;
}

interface JobApplicationTrackerProps {
  userId: string;
}

export default function JobApplicationTracker({ userId }: JobApplicationTrackerProps) {
  const [applications, setApplications] = useState<Application[]>([]);
  const [filter, setFilter] = useState<string>('all');

  // بيانات وهمية للتطبيقات
  useEffect(() => {
    const sampleApplications: Application[] = [
      {
        id: '1',
        jobTitle: 'مطور ويب متقدم',
        company: 'شركة التقنيات المتطورة',
        appliedDate: '2024-01-15',
        status: 'interview',
        lastUpdate: '2024-01-20',
        notes: 'تم تحديد موعد المقابلة ليوم الأحد'
      },
      {
        id: '2',
        jobTitle: 'مصمم جرافيك',
        company: 'وكالة الإبداع للتسويق',
        appliedDate: '2024-01-10',
        status: 'reviewed',
        lastUpdate: '2024-01-18',
        notes: 'تم مراجعة السيرة الذاتية'
      },
      {
        id: '3',
        jobTitle: 'محاسب مالي',
        company: 'شركة الاستثمارات المالية',
        appliedDate: '2024-01-05',
        status: 'accepted',
        lastUpdate: '2024-01-22',
        notes: 'تم قبول الطلب! تهانينا'
      }
    ];
    setApplications(sampleApplications);
  }, [userId]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'reviewed': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'interview': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'accepted': return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'قيد المراجعة';
      case 'reviewed': return 'تم المراجعة';
      case 'interview': return 'مقابلة شخصية';
      case 'accepted': return 'تم القبول';
      case 'rejected': return 'تم الرفض';
      default: return 'غير محدد';
    }
  };

  const filteredApplications = applications.filter(app => 
    filter === 'all' || app.status === filter
  );

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
          📋 تتبع التقديمات
        </h2>
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">فلترة حسب:</span>
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">جميع التقديمات</option>
            <option value="pending">قيد المراجعة</option>
            <option value="reviewed">تم المراجعة</option>
            <option value="interview">مقابلة شخصية</option>
            <option value="accepted">تم القبول</option>
            <option value="rejected">تم الرفض</option>
          </select>
        </div>
      </div>

      {/* إحصائيات سريعة */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-yellow-600">
            {applications.filter(app => app.status === 'pending').length}
          </div>
          <div className="text-xs text-yellow-700">قيد المراجعة</div>
        </div>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-blue-600">
            {applications.filter(app => app.status === 'reviewed').length}
          </div>
          <div className="text-xs text-blue-700">تم المراجعة</div>
        </div>
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-purple-600">
            {applications.filter(app => app.status === 'interview').length}
          </div>
          <div className="text-xs text-purple-700">مقابلات</div>
        </div>
        <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-green-600">
            {applications.filter(app => app.status === 'accepted').length}
          </div>
          <div className="text-xs text-green-700">تم القبول</div>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 text-center">
          <div className="text-2xl font-bold text-red-600">
            {applications.filter(app => app.status === 'rejected').length}
          </div>
          <div className="text-xs text-red-700">تم الرفض</div>
        </div>
      </div>

      {/* قائمة التقديمات */}
      <div className="space-y-4">
        {filteredApplications.length > 0 ? (
          filteredApplications.map((application) => (
            <div key={application.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-800 mb-1">
                    {application.jobTitle}
                  </h3>
                  <p className="text-gray-600 mb-2">{application.company}</p>
                  <div className="flex items-center gap-4 text-sm text-gray-500 mb-2">
                    <span>📅 تاريخ التقديم: {application.appliedDate}</span>
                    <span>🔄 آخر تحديث: {application.lastUpdate}</span>
                  </div>
                  {application.notes && (
                    <p className="text-sm text-gray-700 bg-gray-50 p-2 rounded">
                      💬 {application.notes}
                    </p>
                  )}
                </div>
                <div className="flex flex-col items-end gap-2">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(application.status)}`}>
                    {getStatusText(application.status)}
                  </span>
                  <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    عرض التفاصيل
                  </button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8">
            <div className="text-6xl mb-4">📋</div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد تقديمات</h3>
            <p className="text-gray-600">لم تقم بالتقديم على أي وظيفة بعد</p>
          </div>
        )}
      </div>
    </div>
  );
}
