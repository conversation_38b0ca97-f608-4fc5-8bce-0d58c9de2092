'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import PaymentProcessor from '@/components/PaymentProcessor';
import CashAppLogo from '@/components/CashAppLogo';
import ContactButtons, { ContactInfo } from '@/components/ContactButtons';
import { SUBSCRIPTION_PLANS, BUSINESS_PLANS, formatPrice } from '@/lib/pricing';
import { COMPANY_CONTACT } from '@/lib/contact';
import VerificationBadge from '@/components/VerificationBadge';
import { determineUserBadge } from '@/lib/verification';

export default function SubscriptionPage() {
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const [selectedPayment, setSelectedPayment] = useState<string>('');
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [planType, setPlanType] = useState<'individual' | 'business'>('individual');
  const [applicationId, setApplicationId] = useState<string | null>(null);
  const [applicationStatus, setApplicationStatus] = useState<'pending' | 'approved' | 'rejected' | null>(null);

  // التحقق من معاملات URL للطلبات القادمة من تسجيل المكاتب العقارية
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const plan = urlParams.get('plan');
    const application = urlParams.get('application');

    if (plan === 'real-estate-office' && application) {
      setSelectedPlan('real-estate-office');
      setPlanType('business');
      setApplicationId(application);
      setApplicationStatus('pending'); // في الواقع سيتم جلب الحالة من الخادم
    }
  }, []);

  // تحويل خطط الشركات لنفس تنسيق خطط الأفراد
  const businessPlansFormatted = BUSINESS_PLANS.map(plan => ({
    id: plan.id,
    name: plan.name,
    price: typeof plan.price === 'string' ? plan.price : plan.price,
    currency: plan.currency,
    duration: plan.period,
    features: plan.features,
    popular: plan.popular || false
  }));

  const plans = planType === 'individual' ? SUBSCRIPTION_PLANS : businessPlansFormatted;

  const paymentMethods = [
    {
      id: 'visa',
      name: 'Visa',
      nameAr: 'فيزا كارد',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/4/41/Visa_Logo.png',
      description: 'ادفع بأمان باستخدام بطاقة الفيزا',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      id: 'mastercard',
      name: 'MasterCard',
      nameAr: 'ماستر كارد',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/0/04/Mastercard-logo.png',
      description: 'ادفع بأمان باستخدام بطاقة الماستر كارد',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    },
    {
      id: 'paypal',
      name: 'PayPal',
      nameAr: 'باي بال',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg',
      description: 'ادفع بسهولة عبر حسابك في PayPal',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      id: 'cashapp',
      name: 'Cash App',
      nameAr: 'تطبيق كاش',
      logo: null, // سنستخدم SVG مخصص
      description: 'ادفع بسرعة عبر تطبيق Cash App',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      id: 'applepay',
      name: 'Apple Pay',
      nameAr: 'آبل باي',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg',
      description: 'ادفع بأمان عبر Apple Pay',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200'
    }
  ];

  const handleSubscribe = () => {
    if (!selectedPlan || !selectedPayment) {
      alert('يرجى اختيار الباقة ووسيلة الدفع');
      return;
    }
    setShowPaymentForm(true);
  };

  const selectedPlanData = plans.find(plan => plan.id === selectedPlan);
  const selectedPaymentData = paymentMethods.find(method => method.id === selectedPayment);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="py-12">
        <div className="container mx-auto px-4">
          {/* العنوان */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">
              💎 باقات الاشتراك المميزة
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
              اختر الباقة المناسبة لك واستمتع بمميزات حصرية لتعزيز إعلاناتك
            </p>

            {/* مميزات الأمان */}
            <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl mb-2">🔒</div>
                  <h3 className="font-semibold text-gray-800 mb-1">دفع آمن 100%</h3>
                  <p className="text-sm text-gray-600">تشفير SSL وحماية كاملة</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl mb-2">💳</div>
                  <h3 className="font-semibold text-gray-800 mb-1">جميع وسائل الدفع</h3>
                  <p className="text-sm text-gray-600">Visa, Mastercard, PayPal, Apple Pay</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl mb-2">📞</div>
                  <h3 className="font-semibold text-gray-800 mb-1">دعم فني 24/7</h3>
                  <p className="text-sm text-gray-600">مساعدة فورية في أي وقت</p>
                </div>
              </div>
            </div>
          </div>

          {!showPaymentForm ? (
            <>
              {/* تنبيه خاص للمكاتب العقارية */}
              {applicationId && applicationStatus === 'pending' && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-8 max-w-4xl mx-auto">
                  <div className="flex items-center gap-3 mb-4">
                    <span className="text-3xl">🏘️</span>
                    <div>
                      <h3 className="text-lg font-semibold text-yellow-800">طلب تسجيل مكتب عقاري</h3>
                      <p className="text-yellow-600">رقم الطلب: {applicationId}</p>
                    </div>
                  </div>
                  <div className="bg-white rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-3">
                      <span className="text-lg">⏳</span>
                      <span className="font-medium text-gray-800">حالة الطلب: قيد المراجعة</span>
                    </div>
                    <p className="text-gray-600 text-sm mb-4">
                      طلبك قيد المراجعة من قبل فريقنا المختص. يمكنك إتمام عملية الدفع الآن وسيتم تفعيل حسابك فور الموافقة على الطلب.
                    </p>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                      <div className="flex items-center gap-2 text-blue-600">
                        <span>📋</span>
                        <span>مراجعة الوثائق</span>
                      </div>
                      <div className="flex items-center gap-2 text-orange-600">
                        <span>💳</span>
                        <span>إتمام الدفع</span>
                      </div>
                      <div className="flex items-center gap-2 text-green-600">
                        <span>🏘️</span>
                        <span>تفعيل الحساب</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* أزرار التبديل بين الأفراد والشركات */}
              <div className="flex justify-center mb-12">
                <div className="bg-white rounded-xl shadow-lg p-2 inline-flex">
                  <button
                    onClick={() => {
                      setPlanType('individual');
                      setSelectedPlan('');
                      setSelectedPayment('');
                    }}
                    className={`px-8 py-3 rounded-lg font-semibold transition-all duration-300 ${
                      planType === 'individual'
                        ? 'bg-primary-600 text-white shadow-md'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    🧑‍💼 باقات الأفراد
                  </button>
                  <button
                    onClick={() => {
                      setPlanType('business');
                      setSelectedPlan('');
                      setSelectedPayment('');
                    }}
                    className={`px-8 py-3 rounded-lg font-semibold transition-all duration-300 ${
                      planType === 'business'
                        ? 'bg-primary-600 text-white shadow-md'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    🏢 باقات الشركات
                  </button>
                </div>
              </div>

              {/* عنوان القسم */}
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-gray-800 mb-2">
                  {planType === 'individual' ? '🧑‍💼 باقات الأفراد' : '🏢 باقات الشركات'}
                </h2>
                <p className="text-lg text-gray-600 mb-4">
                  {planType === 'individual'
                    ? 'اختر الباقة المناسبة لاحتياجاتك الشخصية'
                    : 'حلول متقدمة ومتخصصة للشركات والمؤسسات'
                  }
                </p>

                {/* تنبيه للشركات */}
                {planType === 'business' && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-2xl mx-auto mb-6">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <span className="text-2xl">⚠️</span>
                      <h3 className="font-semibold text-yellow-800">تنبيه مهم</h3>
                    </div>
                    <p className="text-sm text-yellow-700">
                      للشركات، يجب اختيار اشتراك شهري للحصول على إعلانات مميزة دائماً
                    </p>
                  </div>
                )}

                {/* معلومات إضافية لباقات الشركات */}
                {planType === 'business' && (
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-2xl mx-auto mb-6">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      <span className="text-2xl">🏢</span>
                      <h3 className="font-semibold text-blue-800">مميزات خاصة للشركات</h3>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-700">
                      <div className="text-center">
                        <span className="block font-medium">📊 تقارير متقدمة</span>
                        <span>إحصائيات مفصلة وتحليلات</span>
                      </div>
                      <div className="text-center">
                        <span className="block font-medium">👥 إدارة الفريق</span>
                        <span>حسابات متعددة للموظفين</span>
                      </div>
                      <div className="text-center">
                        <span className="block font-medium">🎯 أولوية عالية</span>
                        <span>ظهور مميز في النتائج</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* قسم المساعدة والدعم */}
                <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6 max-w-2xl mx-auto">
                  <div className="text-center mb-4">
                    <h3 className="font-semibold text-gray-800 mb-2 flex items-center justify-center gap-2">
                      <span className="text-2xl">🤝</span>
                      <span>هل تحتاج مساعدة في اختيار الباقة المناسبة؟</span>
                    </h3>
                    <p className="text-gray-600 text-sm">
                      فريقنا جاهز لمساعدتك في اختيار الباقة الأنسب لاحتياجاتك
                    </p>
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <ContactButtons
                      variant="inline"
                      size="sm"
                      defaultMessage={
                        planType === 'business'
                          ? COMPANY_CONTACT.whatsappMessages.business
                          : COMPANY_CONTACT.whatsappMessages.subscription
                      }
                    />
                  </div>

                  <div className="mt-4 text-center">
                    <ContactInfo variant="minimal" />
                  </div>
                </div>
              </div>

              {/* الباقات */}
              <div className={`grid grid-cols-1 ${plans.length === 3 ? 'md:grid-cols-3' : 'md:grid-cols-2 lg:grid-cols-3'} gap-8 mb-12`}>
                {plans.map((plan) => (
                  <div
                    key={plan.id}
                    className={`relative bg-white rounded-2xl shadow-lg p-8 transition-all duration-300 cursor-pointer ${
                      selectedPlan === plan.id
                        ? 'ring-4 ring-primary-500 transform scale-105'
                        : 'hover:shadow-xl hover:transform hover:scale-102'
                    } ${plan.popular ? 'border-2 border-primary-500' : ''}`}
                    onClick={() => setSelectedPlan(plan.id)}
                  >
                    {plan.popular && (
                      <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <span className="bg-primary-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                          الأكثر شعبية ⭐
                        </span>
                      </div>
                    )}

                    <div className="text-center mb-6">
                      <h3 className="text-2xl font-bold text-gray-800 mb-2">{plan.name}</h3>
                      <div className="text-4xl font-bold text-primary-600 mb-1">
                        {typeof plan.price === 'string' ? plan.price : formatPrice(plan.price, plan.currency)}
                      </div>
                      <div className="text-gray-500 mb-3">{plan.duration}</div>

                      {/* عرض الشارة المتوقعة */}
                      <div className="flex justify-center">
                        <VerificationBadge
                          badgeId={determineUserBadge(
                            plan.id,
                            0,
                            0,
                            0,
                            planType === 'business'
                          )[0]}
                          size="md"
                          showTooltip={true}
                        />
                      </div>
                    </div>

                    <ul className="space-y-3 mb-8">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-gray-700">
                          <span className="text-green-500 mr-3">✓</span>
                          {feature}
                        </li>
                      ))}
                    </ul>

                    <div className="text-center">
                      <div className={`w-6 h-6 rounded-full border-2 mx-auto ${
                        selectedPlan === plan.id
                          ? 'bg-primary-500 border-primary-500'
                          : 'border-gray-300'
                      }`}>
                        {selectedPlan === plan.id && (
                          <span className="text-white text-sm">✓</span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* وسائل الدفع - تظهر دائماً */}
              <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
                  💳 وسائل الدفع المتاحة
                </h2>

                {!selectedPlan && (
                  <div className="text-center mb-6">
                    <p className="text-gray-500">اختر الباقة أولاً لتفعيل وسائل الدفع</p>
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
                  {paymentMethods.map((method) => (
                    <div
                      key={method.id}
                      className={`border-2 rounded-xl p-6 transition-all duration-300 ${
                        !selectedPlan
                          ? 'border-gray-200 opacity-50 cursor-not-allowed'
                          : selectedPayment === method.id
                            ? 'border-primary-500 bg-primary-50 cursor-pointer shadow-lg transform scale-105'
                            : `${method.borderColor} ${method.bgColor} hover:shadow-md cursor-pointer hover:transform hover:scale-102`
                      }`}
                      onClick={() => selectedPlan && setSelectedPayment(method.id)}
                    >
                      <div className="text-center">
                        <div className="w-20 h-16 mx-auto mb-4 flex items-center justify-center">
                          {method.id === 'cashapp' ? (
                            <CashAppLogo size="lg" />
                          ) : method.id === 'applepay' ? (
                            // شعار Apple Pay مخصص
                            <div className="flex items-center gap-1">
                              <img
                                src={method.logo}
                                alt="Apple"
                                className="w-8 h-10 object-contain"
                              />
                              <span className="text-lg font-semibold text-gray-800">Pay</span>
                            </div>
                          ) : (
                            <img
                              src={method.logo}
                              alt={method.name}
                              className="w-16 h-12 object-contain"
                              onError={(e) => {
                                // في حالة فشل تحميل الصورة، نعرض نص بديل
                                e.currentTarget.style.display = 'none';
                                e.currentTarget.nextElementSibling!.style.display = 'block';
                              }}
                            />
                          )}
                          <div className="hidden text-2xl font-bold text-gray-600">
                            {method.name}
                          </div>
                        </div>
                        <h3 className="font-semibold text-gray-800 mb-1">{method.nameAr}</h3>
                        <p className="text-sm text-gray-500 mb-3">{method.name}</p>
                        <p className="text-xs text-gray-400">{method.description}</p>

                        <div className="mt-4">
                          <div className={`w-6 h-6 rounded-full border-2 mx-auto flex items-center justify-center ${
                            selectedPayment === method.id && selectedPlan
                              ? 'bg-primary-500 border-primary-500'
                              : 'border-gray-300'
                          }`}>
                            {selectedPayment === method.id && selectedPlan && (
                              <span className="text-white text-sm font-bold">✓</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* شعارات وسائل الدفع */}
                <div className="mt-8 pt-6 border-t">
                  <p className="text-center text-sm text-gray-500 mb-6">نقبل جميع وسائل الدفع الآمنة</p>
                  <div className="flex justify-center items-center gap-4 flex-wrap">
                    {/* Visa */}
                    <div className="bg-white p-3 rounded-lg shadow-md flex flex-col items-center w-28">
                      <img
                        src="https://upload.wikimedia.org/wikipedia/commons/4/41/Visa_Logo.png"
                        alt="Visa"
                        className="w-16 h-10 object-contain mb-1"
                      />
                      <span className="text-gray-800 font-medium text-xs">Visa</span>
                    </div>

                    {/* MasterCard */}
                    <div className="bg-white p-3 rounded-lg shadow-md flex flex-col items-center w-28">
                      <img
                        src="https://upload.wikimedia.org/wikipedia/commons/0/04/Mastercard-logo.png"
                        alt="MasterCard"
                        className="w-16 h-10 object-contain mb-1"
                      />
                      <span className="text-gray-800 font-medium text-xs">MasterCard</span>
                    </div>

                    {/* PayPal */}
                    <div className="bg-white p-3 rounded-lg shadow-md flex flex-col items-center w-28">
                      <img
                        src="https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg"
                        alt="PayPal"
                        className="w-16 h-10 object-contain mb-1"
                      />
                      <span className="text-gray-800 font-medium text-xs">PayPal</span>
                    </div>

                    {/* Cash App */}
                    <div className="bg-white p-3 rounded-lg shadow-md flex flex-col items-center w-28">
                      <div className="w-16 h-10 mb-1 flex items-center justify-center">
                        <CashAppLogo size="lg" />
                      </div>
                      <span className="text-gray-800 font-medium text-xs">Cash App</span>
                    </div>

                    {/* Apple Pay */}
                    <div className="bg-white p-3 rounded-lg shadow-md flex flex-col items-center w-28">
                      <div className="flex items-center gap-1 mb-1">
                        <img
                          src="https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg"
                          alt="Apple"
                          className="w-6 h-8 object-contain"
                        />
                        <span className="text-sm font-semibold text-gray-800">Pay</span>
                      </div>
                      <span className="text-gray-800 font-medium text-xs">Apple Pay</span>
                    </div>
                  </div>

                  {/* معلومات الأمان */}
                  <div className="mt-6 text-center">
                    <div className="inline-flex items-center gap-2 bg-green-50 px-4 py-2 rounded-full">
                      <span className="text-green-600">🔒</span>
                      <span className="text-sm text-green-700 font-medium">جميع المعاملات محمية بتشفير SSL</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* ملخص وزر الاشتراك */}
              {selectedPlan && selectedPayment && (
                <div className="bg-white rounded-2xl shadow-lg p-8">
                  <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">
                    📋 ملخص الاشتراك
                  </h2>

                  <div className="max-w-md mx-auto">
                    <div className="space-y-4 mb-6">
                      <div className="flex justify-between">
                        <span className="text-gray-600">نوع الباقة:</span>
                        <span className="font-semibold">
                          {planType === 'individual' ? '🧑‍💼 باقة فردية' : '🏢 باقة شركة'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">الباقة المختارة:</span>
                        <span className="font-semibold">{selectedPlanData?.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">السعر:</span>
                        <span className="font-semibold text-primary-600">
                          {typeof selectedPlanData?.price === 'string'
                            ? selectedPlanData.price
                            : formatPrice(selectedPlanData?.price || 0, selectedPlanData?.currency)
                          }
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">وسيلة الدفع:</span>
                        <span className="font-semibold">{selectedPaymentData?.nameAr}</span>
                      </div>
                      <div className="border-t pt-4">
                        <div className="flex justify-between text-lg font-bold">
                          <span>المجموع:</span>
                          <span className="text-primary-600">
                            {typeof selectedPlanData?.price === 'string'
                              ? selectedPlanData.price
                              : formatPrice(selectedPlanData?.price || 0, selectedPlanData?.currency)
                            }
                          </span>
                        </div>
                      </div>
                    </div>

                    <button
                      onClick={handleSubscribe}
                      className="w-full bg-primary-600 text-white py-4 rounded-xl font-semibold text-lg hover:bg-primary-700 transition-colors"
                    >
                      🚀 متابعة الدفع
                    </button>
                  </div>
                </div>
              )}
            </>
          ) : paymentSuccess ? (
            /* صفحة نجاح الدفع */
            <div className="max-w-2xl mx-auto text-center">
              <div className="bg-white rounded-2xl shadow-lg p-8">
                <div className="text-6xl mb-6">🎉</div>
                <h2 className="text-3xl font-bold text-green-600 mb-4">
                  تم الاشتراك بنجاح!
                </h2>
                <p className="text-lg text-gray-600 mb-6">
                  مبروك! تم تفعيل اشتراكك في {selectedPlanData?.name}
                </p>

                <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                  <h3 className="font-semibold text-green-800 mb-3">تفاصيل الاشتراك:</h3>
                  <div className="space-y-2 text-sm text-green-700">
                    <div>نوع الباقة: {planType === 'individual' ? '🧑‍💼 باقة فردية' : '🏢 باقة شركة'}</div>
                    <div>الباقة: {selectedPlanData?.name}</div>
                    <div>المبلغ المدفوع: {typeof selectedPlanData?.price === 'string'
                      ? selectedPlanData.price
                      : formatPrice(selectedPlanData?.price || 0, selectedPlanData?.currency)
                    }</div>
                    <div>وسيلة الدفع: {selectedPaymentData?.nameAr}</div>
                    <div>تاريخ التفعيل: {new Date().toLocaleDateString('ar-SA')}</div>
                    <div>تاريخ التجديد: {new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString('ar-SA')}</div>
                  </div>
                </div>

                <div className="space-y-4">
                  <button
                    onClick={() => window.location.href = '/add-ad'}
                    className="w-full bg-primary-600 text-white py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors"
                  >
                    🚀 ابدأ بإضافة إعلاناتك
                  </button>
                  <button
                    onClick={() => window.location.href = '/'}
                    className="w-full bg-gray-200 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
                  >
                    العودة للرئيسية
                  </button>
                </div>
              </div>
            </div>
          ) : (
            /* معالج الدفع */
            <PaymentProcessor
              plan={{
                id: selectedPlan,
                name: selectedPlanData?.name || '',
                price: selectedPlanData?.price || 0,
                currency: selectedPlanData?.currency || 'ل.س'
              }}
              paymentMethod={{
                id: selectedPayment,
                name: selectedPaymentData?.name || '',
                nameAr: selectedPaymentData?.nameAr || ''
              }}
              onSuccess={() => setPaymentSuccess(true)}
              onCancel={() => setShowPaymentForm(false)}
            />
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
}
