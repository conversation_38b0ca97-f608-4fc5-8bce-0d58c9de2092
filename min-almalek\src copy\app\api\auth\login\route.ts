import { NextRequest, NextResponse } from 'next/server';
import { AuthService, LoginCredentials } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const credentials: LoginCredentials = await request.json();

    // التحقق من وجود البيانات المطلوبة
    if (!credentials.password) {
      return NextResponse.json(
        { success: false, error: 'كلمة المرور مطلوبة' },
        { status: 400 }
      );
    }

    if (!credentials.email && !credentials.phone) {
      return NextResponse.json(
        { success: false, error: 'البريد الإلكتروني أو رقم الهاتف مطلوب' },
        { status: 400 }
      );
    }

    // محاولة تسجيل الدخول
    const result = await AuthService.login(credentials);

    if (result.success && result.data) {
      // إنشاء استجابة مع كوكيز الجلسة
      const response = NextResponse.json({
        success: true,
        data: {
          user: result.data.user,
          expiresAt: result.data.expiresAt
        },
        message: 'تم تسجيل الدخول بنجاح'
      });

      // إضافة كوكي الجلسة
      response.cookies.set('auth-token', result.data.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 // أسبوع بالثواني
      });

      return response;
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 401 }
      );
    }

  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
