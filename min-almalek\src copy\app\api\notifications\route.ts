import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';
import { NotificationService } from '@/lib/notifications';

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    // التحقق من صحة الجلسة
    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const read = searchParams.get('read');
    const priority = searchParams.get('priority');
    const limit = searchParams.get('limit');
    const offset = searchParams.get('offset');
    const action = searchParams.get('action');

    // إجراءات خاصة
    if (action === 'count') {
      const unreadCount = NotificationService.getUnreadCount(sessionResult.data.id);
      return NextResponse.json({
        success: true,
        data: { unreadCount }
      });
    }

    if (action === 'stats') {
      const stats = NotificationService.getNotificationStats(sessionResult.data.id);
      return NextResponse.json({
        success: true,
        data: stats
      });
    }

    // الحصول على الإشعارات مع الفلاتر
    const filters = {
      category: category || undefined,
      read: read ? read === 'true' : undefined,
      priority: priority || undefined,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined
    };

    const result = NotificationService.getUserNotifications(sessionResult.data.id, filters);

    return NextResponse.json({
      success: true,
      data: result.notifications,
      meta: {
        total: result.total,
        limit: filters.limit,
        offset: filters.offset
      }
    });

  } catch (error) {
    console.error('خطأ في جلب الإشعارات:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    // التحقق من صحة الجلسة
    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { action, notificationId } = await request.json();

    switch (action) {
      case 'mark_read':
        if (!notificationId) {
          return NextResponse.json(
            { success: false, error: 'معرف الإشعار مطلوب' },
            { status: 400 }
          );
        }

        const marked = NotificationService.markAsRead(notificationId, sessionResult.data.id);
        
        if (marked) {
          return NextResponse.json({
            success: true,
            message: 'تم تحديد الإشعار كمقروء'
          });
        } else {
          return NextResponse.json(
            { success: false, error: 'الإشعار غير موجود' },
            { status: 404 }
          );
        }

      case 'mark_all_read':
        const count = NotificationService.markAllAsRead(sessionResult.data.id);
        
        return NextResponse.json({
          success: true,
          message: `تم تحديد ${count} إشعار كمقروء`,
          data: { count }
        });

      default:
        return NextResponse.json(
          { success: false, error: 'إجراء غير صحيح' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('خطأ في تحديث الإشعارات:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    // التحقق من صحة الجلسة
    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { action, notificationId } = await request.json();

    switch (action) {
      case 'delete_one':
        if (!notificationId) {
          return NextResponse.json(
            { success: false, error: 'معرف الإشعار مطلوب' },
            { status: 400 }
          );
        }

        const deleted = NotificationService.deleteNotification(notificationId, sessionResult.data.id);
        
        if (deleted) {
          return NextResponse.json({
            success: true,
            message: 'تم حذف الإشعار'
          });
        } else {
          return NextResponse.json(
            { success: false, error: 'الإشعار غير موجود' },
            { status: 404 }
          );
        }

      case 'delete_read':
        const deletedCount = NotificationService.deleteReadNotifications(sessionResult.data.id);
        
        return NextResponse.json({
          success: true,
          message: `تم حذف ${deletedCount} إشعار مقروء`,
          data: { deletedCount }
        });

      case 'cleanup_old':
        const cleanedCount = NotificationService.cleanupOldNotifications(sessionResult.data.id, 30);
        
        return NextResponse.json({
          success: true,
          message: `تم حذف ${cleanedCount} إشعار قديم`,
          data: { cleanedCount }
        });

      default:
        return NextResponse.json(
          { success: false, error: 'إجراء غير صحيح' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('خطأ في حذف الإشعارات:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
