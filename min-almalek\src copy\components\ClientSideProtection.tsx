'use client';

import { useEffect } from 'react';

export default function ClientSideProtection() {
  useEffect(() => {
    // منع إضافات المتصفح من التدخل
    const preventExtensions = () => {
      // منع Grammarly
      (window as any).grammarly = false;
      (window as any).grammarlyExtension = false;
      
      // إزالة خصائص الإضافات
      const elements = document.querySelectorAll('*');
      elements.forEach(el => {
        // إزالة خصائص Grammarly
        if (el.hasAttribute('data-new-gr-c-s-check-loaded')) {
          el.removeAttribute('data-new-gr-c-s-check-loaded');
        }
        if (el.hasAttribute('data-gr-ext-installed')) {
          el.removeAttribute('data-gr-ext-installed');
        }
        
        // منع تحرير النصوص
        if ((el as HTMLElement).contentEditable === 'true' && 
            !el.matches('input, textarea, [data-editable="true"]')) {
          (el as HTMLElement).contentEditable = 'false';
        }
        
        // إزالة خاصية user-modify
        const htmlEl = el as HTMLElement;
        if (htmlEl.style) {
          htmlEl.style.webkitUserModify = 'read-only';
          (htmlEl.style as any).userModify = 'read-only';
        }
      });
    };

    // تشغيل فوري
    preventExtensions();
    
    // مراقبة مستمرة للتغييرات
    const observer = new MutationObserver(preventExtensions);
    
    observer.observe(document.body, {
      attributes: true,
      subtree: true,
      childList: true,
      attributeFilter: ['data-new-gr-c-s-check-loaded', 'data-gr-ext-installed', 'contenteditable']
    });

    // تنظيف عند إلغاء التحميل
    return () => {
      observer.disconnect();
    };
  }, []);

  return null; // هذا المكون لا يعرض أي شيء
}
