'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import FilterModal from '@/components/FilterModal';
import CategoryIcon from '@/components/CategoryIcon';
import { Ad, DataService } from '@/lib/data';

export default function ServicesPage() {
  const [ads, setAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);

  // فلاتر البحث
  const [filters, setFilters] = useState({
    category: '',
    subcategory: '',
    serviceType: '',
    subService: '',
    provider: '',
    experience: '',
    priceFrom: '',
    priceTo: '',
    availability: '',
    location: '',
    features: [] as string[]
  });

  const subcategories = [
    { name: 'خدمات التنظيف', filter: 'cleaning' },
    { name: 'خدمات الصيانة والإصلاح', filter: 'maintenance' },
    { name: 'خدمات البناء والتشطيب', filter: 'construction' },
    { name: 'الدروس الخصوصية والتعليم', filter: 'education' },
    { name: 'التصوير والمونتاج', filter: 'photography' },
    { name: 'الخدمات التجميلية', filter: 'beauty' },
    { name: 'خدمات السيارات', filter: 'automotive' },
    { name: 'النقل والخدمات اللوجستية', filter: 'logistics' },
    { name: 'رعاية الأطفال والمسنين', filter: 'childcare' },
    { name: 'خدمات الحيوانات الأليفة', filter: 'pets' },
    { name: 'الطبخ المنزلي', filter: 'cooking' },
    { name: 'خدمات التصميم والتقنية', filter: 'design' },
    { name: 'خدمات إدارية وقانونية', filter: 'legal' },
    { name: 'خدمات ترفيهية وفنية', filter: 'entertainment' }
  ];

  const servicesCategories = {
    'cleaning': {
      name: 'خدمات التنظيف',
      subServices: [
        'تنظيف منازل وشقق',
        'تنظيف مكاتب وشركات',
        'تنظيف سجاد وموكيت',
        'تنظيف خزانات مياه',
        'تنظيف واجهات زجاج',
        'تعقيم منازل ومؤسسات'
      ]
    },
    'maintenance': {
      name: 'خدمات الصيانة والإصلاح',
      subServices: [
        'صيانة كهرباء عامة',
        'صيانة تمديدات المياه',
        'صيانة الصرف الصحي',
        'صيانة الأقفال',
        'تصليح أجهزة كهربائية',
        'صيانة كمبيوترات ولابتوبات',
        'صيانة موبايلات',
        'تركيب ستلايت',
        'تركيب انترنت'
      ]
    },
    'construction': {
      name: 'خدمات البناء والتشطيب',
      subServices: [
        'أعمال الدهان والديكور',
        'بلاط وسيراميك',
        'تركيب جبصين',
        'تمديدات صحية',
        'تمديدات كهربائية',
        'تركيب مطابخ وأعمال نجارة'
      ]
    },
    'education': {
      name: 'الدروس الخصوصية والتعليم',
      subServices: [
        'دروس خصوصية (رياضيات، فيزياء، كيمياء)',
        'تعليم لغات',
        'تعليم برمجة',
        'تعليم برامج تصميم',
        'دورات تقوية للشهادات'
      ]
    },
    'photography': {
      name: 'التصوير والمونتاج',
      subServices: [
        'تصوير مناسبات',
        'تصوير منتجات تجارية',
        'تصوير فيديو إعلاني',
        'خدمات مونتاج احترافي'
      ]
    },
    'beauty': {
      name: 'الخدمات التجميلية',
      subServices: [
        'كوافير نسائي / رجالي',
        'تركيب أظافر ورموش',
        'ميك أب وتسريحات',
        'خدمات عناية بالبشرة والشعر'
      ]
    }
  };

  const popularServices = [
    { name: 'تنظيف منازل وشقق', providers: 45, filter: 'cleaning' },
    { name: 'صيانة كهرباء عامة', providers: 38, filter: 'maintenance' },
    { name: 'دروس خصوصية', providers: 67, filter: 'education' },
    { name: 'تصوير مناسبات', providers: 29, filter: 'photography' },
    { name: 'تصميم جرافيك', providers: 52, filter: 'design' },
    { name: 'نقل أثاث', providers: 34, filter: 'logistics' }
  ];

  useEffect(() => {
    loadAds();
  }, [sortBy, currentPage]);

  const loadAds = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    const result = DataService.getAds({
      category: 'services',
      page: currentPage,
      limit: 12,
      sortBy: sortBy as any
    });
    setAds(result.ads);
    setTotalPages(result.totalPages);
    setLoading(false);
  };

  const handleSearch = (newFilters?: any) => {
    if (newFilters) {
      setFilters(newFilters);
    }
    setCurrentPage(1);
    loadAds();
  };

  const handleFilterChange = (key: string, value: string | string[]) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      category: '',
      subcategory: '',
      serviceType: '',
      provider: '',
      experience: '',
      priceFrom: '',
      priceTo: '',
      availability: '',
      location: '',
      features: []
    });
  };

  const handleServiceFilter = (filterValue: string) => {
    setFilters(prev => ({
      ...prev,
      subcategory: filterValue
    }));
    setCurrentPage(1);
    loadAds();
  };

  const handleQuickFilter = (filterType: string) => {
    let newFilters = { ...filters };

    switch(filterType) {
      case 'مميز':
        newFilters.features = [...newFilters.features, 'featured'];
        break;
      case 'عاجل':
        newFilters.features = [...newFilters.features, 'urgent'];
        break;
      case 'جديد':
        newFilters.datePosted = 'today';
        break;
      case 'اليوم':
        newFilters.datePosted = 'today';
        break;
      case 'هذا الأسبوع':
        newFilters.datePosted = 'week';
        break;
      case 'متاح الآن':
        newFilters.availability = 'available';
        break;
      case 'خبرة عالية':
        newFilters.experience = 'expert';
        break;
    }

    setFilters(newFilters);
    setCurrentPage(1);
    loadAds();
  };

  const stats = {
    totalAds: 567,
    avgPrice: '75,000',
    newToday: 15,
    featuredAds: 34,
    activeProviders: 245,
    avgRating: 4.6
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 flex items-center justify-center">
              <CategoryIcon
                category="services"
                className="w-10 h-10"
                color="#f97316"
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">الخدمات</h1>
              <p className="text-gray-600">اعثر على مقدمي الخدمات المحترفين في منطقتك</p>
            </div>
          </div>


        </div>

        {/* زر فتح الفلاتر على الموبايل */}
        <div className="lg:hidden mb-6">
          <button
            onClick={() => setIsFilterModalOpen(true)}
            className="w-full bg-white rounded-xl shadow-lg p-4 flex items-center justify-center gap-3 border border-gray-200 hover:bg-gray-50 transition-colors"
          >
            <img
              src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
              alt="فلاتر"
              className="w-6 h-6 opacity-80"
              style={{
                filter: 'drop-shadow(0 0 4px rgba(249, 115, 22, 0.6))'
              }}
            />
            <span className="text-lg font-semibold text-gray-800">البحث والفلاتر</span>
          </button>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* فلاتر الخدمات */}
          <div className="lg:w-1/4 hidden lg:block">
            <div className="bg-white/30 backdrop-blur-sm rounded-xl shadow-lg p-6 sticky top-8 border border-white/40">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                <img
                  src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="فلاتر"
                  className="w-6 h-6 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(249, 115, 22, 0.6))'
                  }}
                />
                البحث والفلاتر
              </h2>

              {/* البحث السريع */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">البحث السريع</label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="ابحث عن خدمة..."
                    value={filters.serviceType}
                    onChange={(e) => handleFilterChange('serviceType', e.target.value)}
                    className="w-full px-3 py-2 pr-10 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-orange-500 focus:bg-white/70"
                  />
                  <img
                    src="/images/jobs/Jobs -Personals/icons/search_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                    alt="بحث"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 opacity-60"
                    style={{
                      filter: 'drop-shadow(0 0 4px rgba(249, 115, 22, 0.6))'
                    }}
                  />
                </div>
                <button
                  onClick={() => handleSearch(filters)}
                  className="w-full mt-3 bg-orange-500 text-white py-2 px-4 rounded-lg hover:bg-orange-600 active:shadow-lg active:shadow-orange-400/50 active:scale-105 transition-all duration-200 font-medium flex items-center justify-center gap-2"
                >
                  <img
                    src="/images/jobs/Jobs -Personals/icons/search_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
                    alt="بحث"
                    className="w-4 h-4 opacity-80"
                    style={{
                      filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.6))'
                    }}
                  />
                  بحث
                </button>
              </div>

              {/* نوع الخدمة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نوع الخدمة *
                </label>
                <select
                  value={filters.subcategory || ''}
                  onChange={(e) => handleFilterChange('subcategory', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-orange-500 focus:bg-white/70"
                >
                  <option value="">اختر نوع الخدمة</option>
                  {Object.entries(servicesCategories).map(([key, category]) => (
                    <option key={key} value={key}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* الخدمات الشائعة */}
              <div className="mb-6">
                <h4 className="text-md font-semibold text-gray-800 mb-3 flex items-center gap-2">
                  <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                  الخدمات الشائعة
                </h4>
                <div className="space-y-2">
                  {popularServices.map((service, index) => (
                    <button
                      key={index}
                      onClick={() => handleServiceFilter(service.filter)}
                      className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                        filters.subcategory === service.filter
                          ? 'bg-orange-500 text-white shadow-md'
                          : 'bg-white/50 text-gray-700 hover:bg-orange-100 hover:text-orange-700'
                      }`}
                    >
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{service.name}</span>
                        <span className="text-xs opacity-75">{service.providers}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* فلاتر الخدمات المحددة */}
              {filters.subcategory && servicesCategories[filters.subcategory as keyof typeof servicesCategories] && (
                <div className="mb-6">
                  <h4 className="text-md font-semibold text-gray-800 mb-3 flex items-center gap-2">
                    <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                    الخدمات المحددة
                  </h4>
                  <div className="space-y-1">
                    <button
                      onClick={() => handleFilterChange('subService', '')}
                      className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                        filters.subService === ''
                          ? 'bg-orange-500 text-white shadow-md'
                          : 'bg-white/50 text-gray-700 hover:bg-orange-100 hover:text-orange-700'
                      }`}
                    >
                      جميع خدمات {servicesCategories[filters.subcategory as keyof typeof servicesCategories].name}
                    </button>
                    {servicesCategories[filters.subcategory as keyof typeof servicesCategories].subServices.map((service, index) => (
                      <button
                        key={index}
                        onClick={() => handleFilterChange('subService', service)}
                        className={`w-full text-left px-3 py-2 rounded-lg text-sm transition-all duration-200 ${
                          filters.subService === service
                            ? 'bg-orange-500 text-white shadow-md'
                            : 'bg-white/50 text-gray-700 hover:bg-orange-100 hover:text-orange-700'
                        }`}
                      >
                        {service}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* فلتر المحافظة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المحافظة
                </label>
                <select
                  value={filters.location || ''}
                  onChange={(e) => handleFilterChange('location', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-orange-500 focus:bg-white/70"
                >
                  <option value="">جميع المحافظات</option>
                  <option value="damascus">دمشق</option>
                  <option value="aleppo">حلب</option>
                  <option value="homs">حمص</option>
                  <option value="hama">حماة</option>
                  <option value="lattakia">اللاذقية</option>
                  <option value="tartous">طرطوس</option>
                  <option value="daraa">درعا</option>
                  <option value="sweida">السويداء</option>
                  <option value="quneitra">القنيطرة</option>
                  <option value="idlib">إدلب</option>
                  <option value="raqqa">الرقة</option>
                  <option value="deir-ez-zor">دير الزور</option>
                  <option value="hasaka">الحسكة</option>
                  <option value="rif-damascus">ريف دمشق</option>
                </select>
              </div>
            </div>
          </div>

          {/* النتائج */}
          <div className="lg:w-3/4">
            {/* فلاتر سريعة */}
            <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="text-sm font-medium text-gray-700 mr-4">
                  فلاتر سريعة:
                </span>
                {['مميز', 'عاجل', 'متاح الآن', 'خبرة عالية'].map((filter) => (
                  <button
                    key={filter}
                    onClick={() => handleQuickFilter(filter)}
                    className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-orange-100 hover:text-orange-700 transition-colors"
                  >
                    {filter}
                  </button>
                ))}
                <button
                  onClick={clearFilters}
                  className="px-3 py-1 bg-red-100 text-red-700 rounded-full text-sm hover:bg-red-200 transition-colors"
                >
                  مسح الفلاتر
                </button>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="text-gray-600">
                  عرض {ads.length} من أصل {stats.totalAds} خدمة
                  {filters.subcategory && (
                    <span className="ml-2 px-2 py-1 bg-orange-100 text-orange-700 rounded-full text-xs">
                      فلتر: {subcategories.find(s => s.filter === filters.subcategory)?.name || filters.subcategory}
                    </span>
                  )}
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'grid' 
                          ? 'bg-primary-600 text-white' 
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      🔲 شبكة
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'list' 
                          ? 'bg-primary-600 text-white' 
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      📋 قائمة
                    </button>
                  </div>

                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="oldest">الأقدم</option>
                    <option value="most_viewed">الأكثر مشاهدة</option>
                    <option value="rating">الأعلى تقييماً</option>
                  </select>
                </div>
              </div>
            </div>

            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                    <div className="h-48 bg-gray-200"></div>
                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : ads.length > 0 ? (
              <>
                <div className={
                  viewMode === 'grid' 
                    ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                    : 'space-y-4'
                }>
                  {ads.map((ad) => (
                    <AdCard 
                      key={ad.id} 
                      ad={ad} 
                      viewMode={viewMode}
                    />
                  ))}
                </div>

                {totalPages > 1 && (
                  <div className="flex justify-center mt-8">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                        className="px-3 py-2 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        السابق
                      </button>
                      
                      {[...Array(totalPages)].map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentPage(index + 1)}
                          className={`px-3 py-2 border rounded-lg text-sm ${
                            currentPage === index + 1
                              ? 'bg-primary-600 text-white border-primary-600'
                              : 'border-gray-200 hover:bg-gray-50'
                          }`}
                        >
                          {index + 1}
                        </button>
                      ))}
                      
                      <button
                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                        disabled={currentPage === totalPages}
                        className="px-3 py-2 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        التالي
                      </button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🛠️</div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد خدمات</h3>
                <p className="text-gray-600 mb-6">لم نجد أي خدمات تطابق معايير البحث</p>
                <button
                  onClick={() => window.location.reload()}
                  className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  إعادة تحميل
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Modal الفلاتر للموبايل */}
        <FilterModal
          isOpen={isFilterModalOpen}
          onClose={() => setIsFilterModalOpen(false)}
          filters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={clearFilters}
          category="services"
        />
      </main>

      <Footer />
    </div>
  );
}
