'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { NotificationService, Notification } from '@/lib/notifications';

export function useNotifications() {
  const { user, isAuthenticated } = useAuth();
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [hasPermission, setHasPermission] = useState(false);

  // طلب إذن الإشعارات
  const requestPermission = useCallback(async () => {
    if (!isAuthenticated) return false;
    
    const permission = await NotificationService.requestNotificationPermission();
    setHasPermission(permission === 'granted');
    return permission === 'granted';
  }, [isAuthenticated]);

  // تحميل الإشعارات
  const loadNotifications = useCallback(async (options: {
    includeRead?: boolean;
    category?: string;
    limit?: number;
  } = {}) => {
    if (!user) return;

    setLoading(true);
    try {
      const result = NotificationService.getUserNotifications(user.id, {
        includeRead: options.includeRead ?? true,
        category: options.category,
        limit: options.limit ?? 20
      });
      
      setNotifications(result.notifications);
      
      // تحديث عدد الإشعارات غير المقروءة
      const unread = NotificationService.getUnreadCount(user.id);
      setUnreadCount(unread);
    } catch (error) {
      console.error('خطأ في تحميل الإشعارات:', error);
    } finally {
      setLoading(false);
    }
  }, [user]);

  // تحديد إشعار كمقروء
  const markAsRead = useCallback((notificationId: string) => {
    const success = NotificationService.markAsRead(notificationId);
    if (success && user) {
      setNotifications(prev => 
        prev.map(n => 
          n.id.toString() === notificationId 
            ? { ...n, read: true, readAt: new Date().toISOString() } 
            : n
        )
      );
      
      // تحديث العدد
      const unread = NotificationService.getUnreadCount(user.id);
      setUnreadCount(unread);
    }
    return success;
  }, [user]);

  // تحديد جميع الإشعارات كمقروءة
  const markAllAsRead = useCallback(() => {
    if (!user) return 0;
    
    const count = NotificationService.markAllAsRead(user.id);
    if (count > 0) {
      setNotifications(prev => 
        prev.map(n => ({ ...n, read: true, readAt: new Date().toISOString() }))
      );
      setUnreadCount(0);
    }
    return count;
  }, [user]);

  // حذف إشعار
  const deleteNotification = useCallback((notificationId: string) => {
    const success = NotificationService.deleteNotification(notificationId);
    if (success) {
      setNotifications(prev => prev.filter(n => n.id.toString() !== notificationId));
      
      // تحديث العدد إذا كان الإشعار غير مقروء
      if (user) {
        const unread = NotificationService.getUnreadCount(user.id);
        setUnreadCount(unread);
      }
    }
    return success;
  }, [user]);

  // إرسال إشعار جديد
  const sendNotification = useCallback((notification: Omit<Notification, 'id' | 'createdAt' | 'read'>) => {
    if (!user) return null;
    
    const newNotification = NotificationService.sendRealTimeNotification({
      ...notification,
      userId: user.id
    });
    
    // إضافة الإشعار للقائمة المحلية
    setNotifications(prev => [newNotification, ...prev]);
    setUnreadCount(prev => prev + 1);
    
    return newNotification;
  }, [user]);

  // إشعارات سريعة للأحداث الشائعة
  const notifyNewMessage = useCallback((senderName: string, adTitle: string, conversationId: number) => {
    if (!user) return null;
    return NotificationService.notifyNewMessage(user.id, senderName, adTitle, conversationId);
  }, [user]);

  const notifyPaymentSuccess = useCallback((amount: number, subscriptionType: string) => {
    if (!user) return null;
    return NotificationService.notifyPaymentSuccess(user.id, amount, subscriptionType);
  }, [user]);

  const notifyAdStatusChange = useCallback((adTitle: string, status: 'approved' | 'rejected' | 'expired', adId: number) => {
    if (!user) return null;
    return NotificationService.notifyAdStatusChange(user.id, adTitle, status, adId);
  }, [user]);

  const notifyNewOffer = useCallback((offerAmount: number, adTitle: string, buyerName: string, adId: number) => {
    if (!user) return null;
    return NotificationService.notifyNewOffer(user.id, offerAmount, adTitle, buyerName, adId);
  }, [user]);

  // جلب إحصائيات الإشعارات
  const getStats = useCallback(() => {
    if (!user) return null;
    return NotificationService.getNotificationStats(user.id);
  }, [user]);

  // تحميل الإشعارات عند تسجيل الدخول
  useEffect(() => {
    if (isAuthenticated && user) {
      loadNotifications();
      
      // التحقق من إذن الإشعارات
      if (typeof window !== 'undefined' && 'Notification' in window) {
        setHasPermission(Notification.permission === 'granted');
      }
    } else {
      setNotifications([]);
      setUnreadCount(0);
    }
  }, [isAuthenticated, user, loadNotifications]);

  // تحديث دوري للإشعارات (كل 30 ثانية)
  useEffect(() => {
    if (!isAuthenticated || !user) return;

    const interval = setInterval(() => {
      loadNotifications({ includeRead: false, limit: 10 });
    }, 30000);

    return () => clearInterval(interval);
  }, [isAuthenticated, user, loadNotifications]);

  return {
    notifications,
    unreadCount,
    loading,
    hasPermission,
    
    // Actions
    loadNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    sendNotification,
    requestPermission,
    
    // Quick notifications
    notifyNewMessage,
    notifyPaymentSuccess,
    notifyAdStatusChange,
    notifyNewOffer,
    
    // Stats
    getStats
  };
}

// Hook مخصص لإشعارات الرسائل
export function useMessageNotifications() {
  const { notifyNewMessage, unreadCount } = useNotifications();
  
  const notifyMessage = useCallback((senderName: string, adTitle: string, conversationId: number) => {
    return notifyNewMessage(senderName, adTitle, conversationId);
  }, [notifyNewMessage]);

  return {
    notifyMessage,
    unreadCount
  };
}

// Hook مخصص لإشعارات المدفوعات
export function usePaymentNotifications() {
  const { notifyPaymentSuccess } = useNotifications();
  
  const notifyPayment = useCallback((amount: number, subscriptionType: string) => {
    return notifyPaymentSuccess(amount, subscriptionType);
  }, [notifyPaymentSuccess]);

  return {
    notifyPayment
  };
}

// Hook مخصص لإشعارات الإعلانات
export function useAdNotifications() {
  const { notifyAdStatusChange, notifyNewOffer } = useNotifications();
  
  const notifyAdStatus = useCallback((adTitle: string, status: 'approved' | 'rejected' | 'expired', adId: number) => {
    return notifyAdStatusChange(adTitle, status, adId);
  }, [notifyAdStatusChange]);

  const notifyOffer = useCallback((offerAmount: number, adTitle: string, buyerName: string, adId: number) => {
    return notifyNewOffer(offerAmount, adTitle, buyerName, adId);
  }, [notifyNewOffer]);

  return {
    notifyAdStatus,
    notifyOffer
  };
}
