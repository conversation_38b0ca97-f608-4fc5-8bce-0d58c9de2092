// نظام إدارة الملفات والصور
export interface UploadedFile {
  id: string;
  originalName: string;
  filename: string;
  path: string;
  url: string;
  size: number;
  mimeType: string;
  userId: number;
  adId?: number;
  type: 'image' | 'document' | 'video';
  metadata?: {
    width?: number;
    height?: number;
    duration?: number;
  };
  createdAt: string;
}

export interface UploadConfig {
  maxFileSize: number; // بالبايت
  allowedTypes: string[];
  maxFiles: number;
  quality?: number; // للصور (0-100)
  resize?: {
    width: number;
    height: number;
    fit: 'cover' | 'contain' | 'fill';
  };
}

// إعدادات الرفع حسب النوع
export const uploadConfigs = {
  adImages: {
    maxFileSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
    maxFiles: 10,
    quality: 85,
    resize: {
      width: 1200,
      height: 800,
      fit: 'cover' as const
    }
  },
  profileImage: {
    maxFileSize: 2 * 1024 * 1024, // 2MB
    allowedTypes: ['image/jpeg', 'image/png'],
    maxFiles: 1,
    quality: 90,
    resize: {
      width: 400,
      height: 400,
      fit: 'cover' as const
    }
  },
  documents: {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    maxFiles: 5
  },
  videos: {
    maxFileSize: 50 * 1024 * 1024, // 50MB
    allowedTypes: ['video/mp4', 'video/webm', 'video/ogg'],
    maxFiles: 3
  }
};

// بيانات تجريبية للملفات المرفوعة
const uploadedFiles: UploadedFile[] = [
  {
    id: 'file_1',
    originalName: 'apartment_1.jpg',
    filename: 'apartment_1_1642678900.jpg',
    path: '/uploads/ads/apartment_1_1642678900.jpg',
    url: '/api/files/apartment_1_1642678900.jpg',
    size: 2048576,
    mimeType: 'image/jpeg',
    userId: 1,
    adId: 1,
    type: 'image',
    metadata: {
      width: 1200,
      height: 800
    },
    createdAt: '2024-01-15T10:30:00Z'
  },
  {
    id: 'file_2',
    originalName: 'apartment_2.jpg',
    filename: 'apartment_2_1642678901.jpg',
    path: '/uploads/ads/apartment_2_1642678901.jpg',
    url: '/api/files/apartment_2_1642678901.jpg',
    size: 1876543,
    mimeType: 'image/jpeg',
    userId: 1,
    adId: 1,
    type: 'image',
    metadata: {
      width: 1200,
      height: 800
    },
    createdAt: '2024-01-15T10:31:00Z'
  }
];

export class UploadService {
  // التحقق من صحة الملف
  static validateFile(file: File, config: UploadConfig): { valid: boolean; error?: string } {
    // التحقق من الحجم
    if (file.size > config.maxFileSize) {
      const maxSizeMB = Math.round(config.maxFileSize / (1024 * 1024));
      return { 
        valid: false, 
        error: `حجم الملف يجب أن يكون أقل من ${maxSizeMB} ميجابايت` 
      };
    }

    // التحقق من النوع
    if (!config.allowedTypes.includes(file.type)) {
      return { 
        valid: false, 
        error: `نوع الملف غير مدعوم. الأنواع المدعومة: ${config.allowedTypes.join(', ')}` 
      };
    }

    return { valid: true };
  }

  // التحقق من عدد الملفات
  static validateFileCount(
    currentFiles: UploadedFile[], 
    newFilesCount: number, 
    config: UploadConfig
  ): { valid: boolean; error?: string } {
    const totalFiles = currentFiles.length + newFilesCount;
    
    if (totalFiles > config.maxFiles) {
      return { 
        valid: false, 
        error: `يمكنك رفع ${config.maxFiles} ملف كحد أقصى` 
      };
    }

    return { valid: true };
  }

  // محاكاة رفع ملف
  static async uploadFile(
    file: File, 
    userId: number, 
    type: keyof typeof uploadConfigs,
    adId?: number
  ): Promise<{ success: boolean; data?: UploadedFile; error?: string }> {
    try {
      const config = uploadConfigs[type];
      
      // التحقق من صحة الملف
      const validation = this.validateFile(file, config);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      // محاكاة معالجة الملف
      await new Promise(resolve => setTimeout(resolve, 1000));

      // إنشاء معلومات الملف
      const fileId = `file_${Date.now()}`;
      const filename = `${file.name.split('.')[0]}_${Date.now()}.${file.name.split('.').pop()}`;
      const path = `/uploads/${type}/${filename}`;
      
      const uploadedFile: UploadedFile = {
        id: fileId,
        originalName: file.name,
        filename,
        path,
        url: `/api/files/${filename}`,
        size: file.size,
        mimeType: file.type,
        userId,
        adId,
        type: this.getFileType(file.type),
        metadata: await this.extractMetadata(file),
        createdAt: new Date().toISOString()
      };

      // حفظ في القائمة (في التطبيق الحقيقي ستحفظ في قاعدة البيانات)
      uploadedFiles.push(uploadedFile);

      return { success: true, data: uploadedFile };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في رفع الملف' };
    }
  }

  // رفع ملفات متعددة
  static async uploadMultipleFiles(
    files: File[], 
    userId: number, 
    type: keyof typeof uploadConfigs,
    adId?: number
  ): Promise<{ success: boolean; data?: UploadedFile[]; errors?: string[] }> {
    const config = uploadConfigs[type];
    const currentFiles = this.getUserFiles(userId, adId);
    
    // التحقق من عدد الملفات
    const countValidation = this.validateFileCount(currentFiles, files.length, config);
    if (!countValidation.valid) {
      return { success: false, errors: [countValidation.error!] };
    }

    const results: UploadedFile[] = [];
    const errors: string[] = [];

    for (const file of files) {
      const result = await this.uploadFile(file, userId, type, adId);
      
      if (result.success && result.data) {
        results.push(result.data);
      } else {
        errors.push(`${file.name}: ${result.error}`);
      }
    }

    return {
      success: results.length > 0,
      data: results.length > 0 ? results : undefined,
      errors: errors.length > 0 ? errors : undefined
    };
  }

  // حذف ملف
  static deleteFile(fileId: string, userId: number): { success: boolean; error?: string } {
    const index = uploadedFiles.findIndex(f => 
      f.id === fileId && f.userId === userId
    );

    if (index === -1) {
      return { success: false, error: 'الملف غير موجود' };
    }

    // في التطبيق الحقيقي، ستحذف الملف من النظام أيضاً
    uploadedFiles.splice(index, 1);
    
    return { success: true };
  }

  // الحصول على ملفات المستخدم
  static getUserFiles(userId: number, adId?: number): UploadedFile[] {
    return uploadedFiles.filter(f => 
      f.userId === userId && (!adId || f.adId === adId)
    );
  }

  // الحصول على ملف بالمعرف
  static getFileById(fileId: string): UploadedFile | null {
    return uploadedFiles.find(f => f.id === fileId) || null;
  }

  // تحديث معلومات الملف
  static updateFile(
    fileId: string, 
    userId: number, 
    updates: Partial<UploadedFile>
  ): { success: boolean; data?: UploadedFile; error?: string } {
    const file = uploadedFiles.find(f => 
      f.id === fileId && f.userId === userId
    );

    if (!file) {
      return { success: false, error: 'الملف غير موجود' };
    }

    // تحديث البيانات
    Object.assign(file, updates);
    
    return { success: true, data: file };
  }

  // تحديد نوع الملف
  private static getFileType(mimeType: string): 'image' | 'document' | 'video' {
    if (mimeType.startsWith('image/')) return 'image';
    if (mimeType.startsWith('video/')) return 'video';
    return 'document';
  }

  // استخراج البيانات الوصفية
  private static async extractMetadata(file: File): Promise<any> {
    if (file.type.startsWith('image/')) {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          resolve({
            width: img.width,
            height: img.height
          });
        };
        img.onerror = () => resolve({});
        img.src = URL.createObjectURL(file);
      });
    }

    if (file.type.startsWith('video/')) {
      return new Promise((resolve) => {
        const video = document.createElement('video');
        video.onloadedmetadata = () => {
          resolve({
            width: video.videoWidth,
            height: video.videoHeight,
            duration: video.duration
          });
        };
        video.onerror = () => resolve({});
        video.src = URL.createObjectURL(file);
      });
    }

    return {};
  }

  // ضغط الصورة
  static async compressImage(
    file: File, 
    quality: number = 85,
    maxWidth: number = 1200,
    maxHeight: number = 800
  ): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      const img = new Image();

      img.onload = () => {
        // حساب الأبعاد الجديدة
        let { width, height } = img;
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width *= ratio;
          height *= ratio;
        }

        canvas.width = width;
        canvas.height = height;

        // رسم الصورة
        ctx.drawImage(img, 0, 0, width, height);

        // تحويل إلى blob
        canvas.toBlob((blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          } else {
            resolve(file);
          }
        }, file.type, quality / 100);
      };

      img.src = URL.createObjectURL(file);
    });
  }

  // الحصول على إحصائيات الملفات
  static getFileStats(userId: number) {
    const userFiles = this.getUserFiles(userId);
    
    const stats = {
      totalFiles: userFiles.length,
      totalSize: userFiles.reduce((sum, f) => sum + f.size, 0),
      byType: {} as Record<string, number>,
      byMimeType: {} as Record<string, number>
    };

    userFiles.forEach(file => {
      stats.byType[file.type] = (stats.byType[file.type] || 0) + 1;
      stats.byMimeType[file.mimeType] = (stats.byMimeType[file.mimeType] || 0) + 1;
    });

    return stats;
  }

  // تنظيف الملفات القديمة
  static cleanupOldFiles(daysOld: number = 30): number {
    const cutoffDate = new Date(Date.now() - (daysOld * 24 * 60 * 60 * 1000));
    const initialLength = uploadedFiles.length;

    for (let i = uploadedFiles.length - 1; i >= 0; i--) {
      const file = uploadedFiles[i];
      if (new Date(file.createdAt) < cutoffDate && !file.adId) {
        uploadedFiles.splice(i, 1);
      }
    }

    return initialLength - uploadedFiles.length;
  }
}
