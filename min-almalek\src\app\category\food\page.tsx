'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import FilterModal from '@/components/FilterModal';
import CategoryIcon from '@/components/CategoryIcon';
import { Ad, DataService } from '@/lib/data';

export default function FoodPage() {
  const [ads, setAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);

  // فلاتر البحث
  const [filters, setFilters] = useState({
    searchQuery: '',
    restaurantType: '',
    cuisineType: '',
    location: '',
    priceRange: '',
    deliveryAvailable: '',
    rating: '',
    openNow: false
  });

  // أنواع المطاعم
  const restaurantTypes = [
    'مطعم',
    'كافيه',
    'مخبز',
    'حلويات',
    'وجبات سريعة',
    'خدمة توصيل',
    'معدات مطاعم',
    'بقالية / مواد غذائية'
  ];

  // أنواع المطابخ
  const cuisineTypes = [
    'سوري',
    'تركي',
    'لبناني',
    'إيطالي',
    'صيني',
    'هندي',
    'مكسيكي',
    'وجبات سريعة',
    'مشاوي',
    'مأكولات بحرية'
  ];

  // المحافظات السورية
  const locations = [
    'دمشق',
    'ريف دمشق',
    'حلب',
    'حمص',
    'حماة',
    'اللاذقية',
    'طرطوس',
    'إدلب',
    'درعا',
    'السويداء',
    'القنيطرة',
    'دير الزور',
    'الرقة',
    'الحسكة'
  ];

  // نطاقات الأسعار
  const priceRanges = [
    { label: 'اقتصادي (أقل من 1000 ل.س)', value: '0-1000' },
    { label: 'متوسط (1000-3000 ل.س)', value: '1000-3000' },
    { label: 'مرتفع (3000-5000 ل.س)', value: '3000-5000' },
    { label: 'راقي (أكثر من 5000 ل.س)', value: '5000+' }
  ];

  // إحصائيات
  const stats = {
    totalRestaurants: 1247,
    avgRating: 4.2,
    newToday: 23,
    withDelivery: 856
  };

  useEffect(() => {
    const loadAds = async () => {
      try {
        setLoading(true);
        const data = await DataService.getAds('food', currentPage);
        setAds(data.ads);
        setTotalPages(data.totalPages);
      } catch (error) {
        console.error('Error loading food ads:', error);
      } finally {
        setLoading(false);
      }
    };

    loadAds();
  }, [currentPage, sortBy, filters]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* العنوان والوصف */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-4 mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg">
              <CategoryIcon
                category="food"
                className="w-10 h-10"
                color="#ffffff"
              />
            </div>
            <div className="text-right">
              <h1 className="text-4xl font-bold text-gray-800">الطعام والمشروبات</h1>
              <p className="text-gray-600">اكتشف أفضل المطاعم والمقاهي في سوريا</p>
            </div>
          </div>
        </div>

        {/* زر فتح الفلاتر على الموبايل */}
        <div className="lg:hidden mb-6">
          <button
            onClick={() => setIsFilterModalOpen(true)}
            className="w-full bg-white rounded-xl shadow-lg p-4 flex items-center justify-center gap-3 border border-gray-200 hover:bg-gray-50 transition-colors"
          >
            <img
              src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
              alt="فلاتر"
              className="w-6 h-6 opacity-80"
              style={{
                filter: 'drop-shadow(0 0 4px rgba(239, 68, 68, 0.6))'
              }}
            />
            <span className="text-lg font-semibold text-gray-800">البحث والفلاتر</span>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* فلاتر الطعام والمشروبات */}
          <div className="lg:col-span-1 hidden lg:block">
            <div className="bg-white rounded-xl shadow-lg p-6 sticky top-8 border border-gray-200">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                <img
                  src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="فلاتر"
                  className="w-6 h-6 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(239, 68, 68, 0.6))'
                  }}
                />
                البحث والفلاتر
              </h2>

              {/* البحث */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                <input
                  type="text"
                  value={filters.searchQuery}
                  onChange={(e) => setFilters({...filters, searchQuery: e.target.value})}
                  placeholder="ابحث عن مطعم أو طبق..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                />
              </div>

              {/* نوع المطعم */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع المطعم</label>
                <select
                  value={filters.restaurantType}
                  onChange={(e) => setFilters({...filters, restaurantType: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                >
                  <option value="">جميع الأنواع</option>
                  {restaurantTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              {/* نوع المطبخ */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع المطبخ</label>
                <select
                  value={filters.cuisineType}
                  onChange={(e) => setFilters({...filters, cuisineType: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                >
                  <option value="">جميع المطابخ</option>
                  {cuisineTypes.map(cuisine => (
                    <option key={cuisine} value={cuisine}>{cuisine}</option>
                  ))}
                </select>
              </div>

              {/* المحافظة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">المحافظة</label>
                <select
                  value={filters.location}
                  onChange={(e) => setFilters({...filters, location: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                >
                  <option value="">جميع المحافظات</option>
                  {locations.map(location => (
                    <option key={location} value={location}>{location}</option>
                  ))}
                </select>
              </div>

              {/* نطاق الأسعار */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نطاق الأسعار</label>
                <select
                  value={filters.priceRange}
                  onChange={(e) => setFilters({...filters, priceRange: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                >
                  <option value="">جميع الأسعار</option>
                  {priceRanges.map(range => (
                    <option key={range.value} value={range.value}>{range.label}</option>
                  ))}
                </select>
              </div>

              {/* خدمة التوصيل */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">خدمة التوصيل</label>
                <select
                  value={filters.deliveryAvailable}
                  onChange={(e) => setFilters({...filters, deliveryAvailable: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                >
                  <option value="">الكل</option>
                  <option value="yes">متوفر</option>
                  <option value="no">غير متوفر</option>
                </select>
              </div>

              {/* التقييم */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">التقييم</label>
                <select
                  value={filters.rating}
                  onChange={(e) => setFilters({...filters, rating: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                >
                  <option value="">جميع التقييمات</option>
                  <option value="4+">4 نجوم فأكثر</option>
                  <option value="3+">3 نجوم فأكثر</option>
                  <option value="2+">2 نجوم فأكثر</option>
                </select>
              </div>

              {/* مفتوح الآن */}
              <div className="mb-6">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={filters.openNow}
                    onChange={(e) => setFilters({...filters, openNow: e.target.checked})}
                    className="rounded border-gray-300 text-red-600 focus:ring-red-500"
                  />
                  <span className="text-sm font-medium text-gray-700">مفتوح الآن</span>
                </label>
              </div>
            </div>
          </div>

          {/* النتائج */}
          <div className="lg:col-span-3">
            {/* شريط النتائج */}
            <div className="bg-white rounded-xl shadow-lg p-4 mb-6 border border-gray-200">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="text-gray-600">
                  عرض <span className="font-semibold text-red-600">{ads.length}</span> من أصل {stats.totalRestaurants} مطعم
                </div>

                <div className="flex items-center gap-4">
                  {/* طريقة العرض */}
                  <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'grid'
                          ? 'bg-red-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      🔲 شبكة
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'list'
                          ? 'bg-red-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      📋 قائمة
                    </button>
                  </div>

                  {/* الترتيب */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="rating">الأعلى تقييماً</option>
                    <option value="popular">الأكثر شعبية</option>
                    <option value="price-low">السعر: من الأقل للأعلى</option>
                    <option value="price-high">السعر: من الأعلى للأقل</option>
                  </select>
                </div>
              </div>
            </div>

            {/* المطاعم */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                    <div className="h-48 bg-gray-200"></div>
                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : ads.length > 0 ? (
              <div className={
                viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                  : 'space-y-4'
              }>
                {ads.map((ad) => (
                  <AdCard
                    key={ad.id}
                    ad={ad}
                    viewMode={viewMode}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <CategoryIcon
                    category="food"
                    className="w-14 h-14"
                    color="#ef4444"
                  />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد مطاعم</h3>
                <p className="text-gray-600">لم يتم العثور على مطاعم تطابق معايير البحث</p>
              </div>
            )}
          </div>
        </div>

        {/* نصائح للطعام الصحي */}
        <div className="mt-12 bg-gradient-to-r from-red-100 to-orange-200 rounded-lg text-red-800 p-6">
          <h2 className="text-lg font-bold mb-4 text-center">نصائح للطعام الصحي</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl mb-2">🥗</div>
              <h3 className="font-semibold mb-1 text-sm">اختر الطازج</h3>
              <p className="text-red-600 text-xs">ابحث عن المكونات الطازجة والطبيعية</p>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">🍽️</div>
              <h3 className="font-semibold mb-1 text-sm">تنوع الوجبات</h3>
              <p className="text-red-600 text-xs">نوع في اختيار الأطباق للحصول على تغذية متوازنة</p>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">💧</div>
              <h3 className="font-semibold mb-1 text-sm">اشرب الماء</h3>
              <p className="text-red-600 text-xs">تناول كمية كافية من الماء مع الوجبات</p>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">⭐</div>
              <h3 className="font-semibold mb-1 text-sm">اقرأ التقييمات</h3>
              <p className="text-red-600 text-xs">تحقق من تقييمات العملاء قبل الطلب</p>
            </div>
          </div>
        </div>
      </main>

      <Footer />

      {/* Modal للفلاتر على الموبايل */}
      <FilterModal
        isOpen={isFilterModalOpen}
        onClose={() => setIsFilterModalOpen(false)}
        title="البحث والفلاتر"
      >
        <div className="space-y-6">
          {/* البحث */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
            <input
              type="text"
              value={filters.searchQuery}
              onChange={(e) => setFilters({...filters, searchQuery: e.target.value})}
              placeholder="ابحث عن مطعم أو طبق..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
            />
          </div>

          {/* نوع المطعم */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">نوع المطعم</label>
            <select
              value={filters.restaurantType}
              onChange={(e) => setFilters({...filters, restaurantType: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
            >
              <option value="">جميع الأنواع</option>
              {restaurantTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          {/* نوع المطبخ */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">نوع المطبخ</label>
            <select
              value={filters.cuisineType}
              onChange={(e) => setFilters({...filters, cuisineType: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
            >
              <option value="">جميع المطابخ</option>
              {cuisineTypes.map(cuisine => (
                <option key={cuisine} value={cuisine}>{cuisine}</option>
              ))}
            </select>
          </div>

          {/* المحافظة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">المحافظة</label>
            <select
              value={filters.location}
              onChange={(e) => setFilters({...filters, location: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
            >
              <option value="">جميع المحافظات</option>
              {locations.map(location => (
                <option key={location} value={location}>{location}</option>
              ))}
            </select>
          </div>

          {/* نطاق الأسعار */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">نطاق الأسعار</label>
            <select
              value={filters.priceRange}
              onChange={(e) => setFilters({...filters, priceRange: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
            >
              <option value="">جميع الأسعار</option>
              {priceRanges.map(range => (
                <option key={range.value} value={range.value}>{range.label}</option>
              ))}
            </select>
          </div>

          {/* خدمة التوصيل */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">خدمة التوصيل</label>
            <select
              value={filters.deliveryAvailable}
              onChange={(e) => setFilters({...filters, deliveryAvailable: e.target.value})}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
            >
              <option value="">الكل</option>
              <option value="yes">متوفر</option>
              <option value="no">غير متوفر</option>
            </select>
          </div>
        </div>
      </FilterModal>
    </div>
  );
}
