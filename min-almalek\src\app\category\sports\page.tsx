'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import FilterModal from '@/components/FilterModal';
import CategoryIcon from '@/components/CategoryIcon';
import { Ad, DataService } from '@/lib/data';

export default function SportsPage() {
  const [ads, setAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);

  // فلاتر البحث
  const [filters, setFilters] = useState({
    mainCategory: '',
    subCategory: '',
    condition: '',
    brand: '',
    size: '',
    priceFrom: '',
    priceTo: '',
    location: '',
    features: [] as string[]
  });

  const sportsCategories = {
    'equipment': {
      name: 'معدات وأجهزة رياضية',
      subCategories: [
        'أجهزة جيم منزلية (مشايات، دراجات ثابتة، أجهزة أوزان)',
        'أوزان ودنابل',
        'أدوات تمارين مقاومة',
        'حصائر تمارين (يوغا، بيلاتس)',
        'كرات تمارين',
        'معدات تمارين البطن'
      ]
    },
    'clothing-shoes': {
      name: 'ملابس وأحذية رياضية',
      subCategories: [
        'تيشيرتات رياضية',
        'بناطيل/شورتات رياضية',
        'أحذية جري',
        'أحذية كرة قدم',
        'ملابس سباحة',
        'ملابس يوجا وبيلاتس'
      ]
    },
    'outdoor-indoor': {
      name: 'رياضات خارجية وداخلية',
      subCategories: [
        'كرة القدم',
        'كرة السلة',
        'كرة الطائرة',
        'التنس وتنس الطاولة',
        'الجولف',
        'البيسبول'
      ]
    },
    'camping': {
      name: 'معدات التخييم والرحلات',
      subCategories: [
        'خيام',
        'حقائب ظهر',
        'أكياس نوم',
        'أدوات طبخ للرحلات',
        'كراسي وطاولات تخييم',
        'مصابيح ومستلزمات إضاءة للرحلات'
      ]
    },
    'bicycles': {
      name: 'الدراجات',
      subCategories: [
        'دراجات هوائية للكبار',
        'دراجات هوائية للأطفال',
        'دراجات جبلية',
        'دراجات سباق',
        'إكسسوارات الدراجات (خوذات، أضواء، أقفال)'
      ]
    },
    'games-hobbies': {
      name: 'ألعاب وهوايات',
      subCategories: [
        'ألعاب إلكترونية',
        'ألعاب لوحية (Board Games)',
        'ألعاب تركيب (ليغو، بازل)',
        'سيارات وطائرات تحكم عن بعد',
        'مجسمات وهوايات تجميع'
      ]
    },
    'fishing': {
      name: 'مستلزمات الصيد',
      subCategories: [
        'صنارات صيد',
        'شباك صيد',
        'أدوات تغليف الطُعم',
        'صناديق تخزين معدات الصيد'
      ]
    },
    'water-sports': {
      name: 'مستلزمات السباحة والرياضات المائية',
      subCategories: [
        'معدات غوص',
        'نظارات سباحة',
        'زعانف',
        'ألواح تزلج مائي',
        'سترات نجاة'
      ]
    }
  };

  const conditionOptions = ['جديد', 'مستعمل - ممتاز', 'مستعمل - جيد', 'مستعمل - مقبول'];
  const brandOptions = ['Nike', 'Adidas', 'Puma', 'Under Armour', 'Reebok', 'New Balance', 'Wilson', 'Spalding', 'Coleman', 'The North Face', 'أخرى'];
  const sizeOptions = ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45', '46', '47'];



  useEffect(() => {
    loadAds();
  }, [sortBy, currentPage]);

  const loadAds = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    const result = DataService.getAds({
      category: 'sports',
      page: currentPage,
      limit: 12,
      sortBy: sortBy as any
    });
    setAds(result.ads);
    setTotalPages(result.totalPages);
    setLoading(false);
  };

  const handleSearch = (filters: any) => {
    console.log('تطبيق فلاتر البحث:', filters);
    loadAds();
  };

  const handleFilterChange = (key: string, value: string | string[]) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      mainCategory: '',
      subCategory: '',
      condition: '',
      brand: '',
      size: '',
      priceFrom: '',
      priceTo: '',
      location: '',
      features: []
    });
  };

  const stats = {
    totalAds: 445,
    avgPrice: '275,000',
    newToday: 12,
    featuredAds: 34
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 flex items-center justify-center">
              <CategoryIcon
                category="sports"
                className="w-10 h-10"
                color="#6366f1"
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">الرياضة والترفيه</h1>
              <p className="text-gray-600">كل ما تحتاجه لحياة رياضية صحية ونشطة</p>
            </div>
          </div>


        </div>

        {/* زر فتح الفلاتر على الموبايل */}
        <div className="lg:hidden mb-6">
          <button
            onClick={() => setIsFilterModalOpen(true)}
            className="w-full bg-white rounded-xl shadow-lg p-4 flex items-center justify-center gap-3 border border-gray-200 hover:bg-gray-50 transition-colors"
          >
            <img
              src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
              alt="فلاتر"
              className="w-6 h-6 opacity-80"
              style={{
                filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.6))'
              }}
            />
            <span className="text-lg font-semibold text-gray-800">البحث والفلاتر</span>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <div className="lg:col-span-1 hidden lg:block">
            <div className="bg-white/30 backdrop-blur-sm rounded-xl shadow-lg p-6 sticky top-8 border border-white/40">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                <img
                  src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="فلاتر"
                  className="w-6 h-6 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(16, 185, 129, 0.6))'
                  }}
                />
                البحث والفلاتر
              </h2>

              {/* البحث السريع */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">البحث السريع</label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="ابحث عن معدات رياضية..."
                    className="w-full px-3 py-2 pr-10 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-green-500 focus:bg-white/70"
                  />
                  <img
                    src="/images/jobs/Jobs -Personals/icons/search_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                    alt="بحث"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 opacity-60"
                    style={{
                      filter: 'drop-shadow(0 0 4px rgba(16, 185, 129, 0.6))'
                    }}
                  />
                </div>
              </div>

              {/* الفئة الرئيسية */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الفئة الرئيسية
                </label>
                <select
                  value={filters.mainCategory || ''}
                  onChange={(e) => handleFilterChange('mainCategory', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-green-500 focus:bg-white/70"
                >
                  <option value="">جميع الفئات</option>
                  {Object.entries(sportsCategories).map(([key, category]) => (
                    <option key={key} value={key}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* الفئة الفرعية */}
              {filters.mainCategory && sportsCategories[filters.mainCategory as keyof typeof sportsCategories] && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الفئة الفرعية
                  </label>
                  <select
                    value={filters.subCategory || ''}
                    onChange={(e) => handleFilterChange('subCategory', e.target.value)}
                    className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-green-500 focus:bg-white/70"
                  >
                    <option value="">جميع الفئات الفرعية</option>
                    {sportsCategories[filters.mainCategory as keyof typeof sportsCategories].subCategories.map((sub, index) => (
                      <option key={index} value={sub}>
                        {sub}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* الحالة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الحالة
                </label>
                <select
                  value={filters.condition || ''}
                  onChange={(e) => handleFilterChange('condition', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-green-500 focus:bg-white/70"
                >
                  <option value="">جميع الحالات</option>
                  {conditionOptions.map((condition) => (
                    <option key={condition} value={condition}>
                      {condition}
                    </option>
                  ))}
                </select>
              </div>

              {/* الماركة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الماركة
                </label>
                <select
                  value={filters.brand || ''}
                  onChange={(e) => handleFilterChange('brand', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-green-500 focus:bg-white/70"
                >
                  <option value="">جميع الماركات</option>
                  {brandOptions.map((brand) => (
                    <option key={brand} value={brand}>
                      {brand}
                    </option>
                  ))}
                </select>
              </div>

              {/* المقاس */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المقاس
                </label>
                <select
                  value={filters.size || ''}
                  onChange={(e) => handleFilterChange('size', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-green-500 focus:bg-white/70"
                >
                  <option value="">جميع المقاسات</option>
                  {sizeOptions.map((size) => (
                    <option key={size} value={size}>
                      {size}
                    </option>
                  ))}
                </select>
              </div>

              {/* نطاق السعر */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نطاق السعر (ل.س)
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="من"
                    value={filters.priceFrom}
                    onChange={(e) => handleFilterChange('priceFrom', e.target.value)}
                    className="px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-green-500 focus:bg-white/70"
                  />
                  <input
                    type="number"
                    placeholder="إلى"
                    value={filters.priceTo}
                    onChange={(e) => handleFilterChange('priceTo', e.target.value)}
                    className="px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-green-500 focus:bg-white/70"
                  />
                </div>
              </div>

              {/* المحافظة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المحافظة
                </label>
                <select
                  value={filters.location || ''}
                  onChange={(e) => handleFilterChange('location', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-green-500 focus:bg-white/70"
                >
                  <option value="">جميع المحافظات</option>
                  <option value="damascus">دمشق</option>
                  <option value="aleppo">حلب</option>
                  <option value="homs">حمص</option>
                  <option value="hama">حماة</option>
                  <option value="lattakia">اللاذقية</option>
                  <option value="tartous">طرطوس</option>
                  <option value="daraa">درعا</option>
                  <option value="sweida">السويداء</option>
                  <option value="quneitra">القنيطرة</option>
                  <option value="idlib">إدلب</option>
                  <option value="raqqa">الرقة</option>
                  <option value="deir-ez-zor">دير الزور</option>
                  <option value="hasaka">الحسكة</option>
                  <option value="rif-damascus">ريف دمشق</option>
                </select>
              </div>

              {/* زر مسح الفلاتر */}
              <button
                onClick={clearFilters}
                className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                مسح جميع الفلاتر
              </button>
            </div>
          </div>

          <div className="lg:col-span-3">
            <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="text-gray-600">
                  عرض {ads.length} من أصل {stats.totalAds} إعلان
                </div>
                
                <div className="flex items-center gap-4">
                  <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'grid' 
                          ? 'bg-primary-600 text-white' 
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      🔲 شبكة
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'list' 
                          ? 'bg-primary-600 text-white' 
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      📋 قائمة
                    </button>
                  </div>

                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="oldest">الأقدم</option>
                    <option value="price_low">السعر: من الأقل للأعلى</option>
                    <option value="price_high">السعر: من الأعلى للأقل</option>
                    <option value="most_viewed">الأكثر مشاهدة</option>
                  </select>
                </div>
              </div>
            </div>

            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                    <div className="h-48 bg-gray-200"></div>
                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : ads.length > 0 ? (
              <>
                <div className={
                  viewMode === 'grid' 
                    ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                    : 'space-y-4'
                }>
                  {ads.map((ad) => (
                    <AdCard 
                      key={ad.id} 
                      ad={ad} 
                      viewMode={viewMode}
                    />
                  ))}
                </div>

                {totalPages > 1 && (
                  <div className="flex justify-center mt-8">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                        className="px-3 py-2 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        السابق
                      </button>
                      
                      {[...Array(totalPages)].map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentPage(index + 1)}
                          className={`px-3 py-2 border rounded-lg text-sm ${
                            currentPage === index + 1
                              ? 'bg-primary-600 text-white border-primary-600'
                              : 'border-gray-200 hover:bg-gray-50'
                          }`}
                        >
                          {index + 1}
                        </button>
                      ))}
                      
                      <button
                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                        disabled={currentPage === totalPages}
                        className="px-3 py-2 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        التالي
                      </button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <div className="w-24 h-24 flex items-center justify-center mx-auto mb-4">
                  <CategoryIcon
                    category="sports"
                    className="w-20 h-20"
                    color="#6366f1"
                  />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد منتجات رياضية</h3>
                <p className="text-gray-600 mb-6">لم نجد أي منتجات رياضية تطابق معايير البحث</p>
                <button
                  onClick={() => window.location.reload()}
                  className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  إعادة تحميل
                </button>
              </div>
            )}
          </div>
        </div>

        {/* نصائح للحياة الرياضية */}
        <div className="mt-12 bg-gradient-to-r from-green-100 to-green-200 rounded-lg text-green-800 p-6">
          <h2 className="text-lg font-bold mb-4 text-center">نصائح للحياة الرياضية الصحية</h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl mb-2">🏃</div>
              <h3 className="font-semibold mb-1 text-sm">ابدأ تدريجياً</h3>
              <p className="text-green-600 text-xs">لا تبدأ بتمارين شاقة من اليوم الأول</p>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">💧</div>
              <h3 className="font-semibold mb-1 text-sm">اشرب الماء</h3>
              <p className="text-green-600 text-xs">حافظ على ترطيب جسمك أثناء التمرين</p>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">🥗</div>
              <h3 className="font-semibold mb-1 text-sm">تغذية صحية</h3>
              <p className="text-green-600 text-xs">اتبع نظام غذائي متوازن</p>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">😴</div>
              <h3 className="font-semibold mb-1 text-sm">راحة كافية</h3>
              <p className="text-green-600 text-xs">احصل على نوم كافي للتعافي</p>
            </div>
          </div>
        </div>
        {/* Modal الفلاتر للموبايل */}
        <FilterModal
          isOpen={isFilterModalOpen}
          onClose={() => setIsFilterModalOpen(false)}
          filters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={clearFilters}
          category="sports"
        />
      </main>

      <Footer />
    </div>
  );
}
