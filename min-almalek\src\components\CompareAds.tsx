'use client';

import { useState } from 'react';

interface Ad {
  id: number;
  title: string;
  price: string;
  currency: string;
  location: string;
  category: string;
  image: string;
  specs: { [key: string]: string };
  seller: {
    name: string;
    verified: boolean;
  };
  postedDate: string;
}

const CompareAds = () => {
  const [selectedAds, setSelectedAds] = useState<Ad[]>([]);
  const [showComparison, setShowComparison] = useState(false);

  // بيانات تجريبية للإعلانات
  const sampleAds: Ad[] = [
    {
      id: 1,
      title: 'شقة للبيع في دمشق - المالكي',
      price: '85,000,000',
      currency: 'ل.س',
      location: 'دمشق - المالكي',
      category: 'عقارات',
      image: '/api/placeholder/300/200',
      specs: {
        'المساحة': '150 م²',
        'عدد الغرف': '3',
        'عدد الحمامات': '2',
        'الطابق': '5',
        'عمر البناء': '10 سنوات',
        'التدفئة': 'مركزية',
        'المصعد': 'متوفر',
        'موقف السيارة': 'متوفر'
      },
      seller: { name: 'أحمد محمد', verified: true },
      postedDate: 'منذ ساعتين'
    },
    {
      id: 2,
      title: 'شقة للبيع في دمشق - أبو رمانة',
      price: '95,000,000',
      currency: 'ل.س',
      location: 'دمشق - أبو رمانة',
      category: 'عقارات',
      image: '/api/placeholder/300/200',
      specs: {
        'المساحة': '180 م²',
        'عدد الغرف': '4',
        'عدد الحمامات': '3',
        'الطابق': '3',
        'عمر البناء': '5 سنوات',
        'التدفئة': 'مركزية',
        'المصعد': 'متوفر',
        'موقف السيارة': 'متوفر'
      },
      seller: { name: 'سارة أحمد', verified: true },
      postedDate: 'منذ 4 ساعات'
    },
    {
      id: 3,
      title: 'شقة للبيع في دمشق - المزة',
      price: '75,000,000',
      currency: 'ل.س',
      location: 'دمشق - المزة',
      category: 'عقارات',
      image: '/api/placeholder/300/200',
      specs: {
        'المساحة': '130 م²',
        'عدد الغرف': '3',
        'عدد الحمامات': '2',
        'الطابق': '2',
        'عمر البناء': '15 سنوات',
        'التدفئة': 'فردية',
        'المصعد': 'غير متوفر',
        'موقف السيارة': 'غير متوفر'
      },
      seller: { name: 'محمد علي', verified: false },
      postedDate: 'منذ يوم'
    }
  ];

  const addToComparison = (ad: Ad) => {
    if (selectedAds.length < 3 && !selectedAds.find(a => a.id === ad.id)) {
      setSelectedAds([...selectedAds, ad]);
    }
  };

  const removeFromComparison = (adId: number) => {
    setSelectedAds(selectedAds.filter(ad => ad.id !== adId));
  };

  const clearComparison = () => {
    setSelectedAds([]);
    setShowComparison(false);
  };

  // الحصول على جميع المواصفات الفريدة
  const allSpecs = Array.from(
    new Set(selectedAds.flatMap(ad => Object.keys(ad.specs)))
  );

  return (
    <div className="container mx-auto px-4 py-8">
      {!showComparison ? (
        <div>
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-4">مقارنة الإعلانات</h1>
            <p className="text-lg text-gray-600">اختر حتى 3 إعلانات لمقارنتها</p>
          </div>

          {/* شريط المقارنة */}
          {selectedAds.length > 0 && (
            <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-50">
              <div className="container mx-auto px-4 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <span className="font-medium text-gray-800">
                      المقارنة ({selectedAds.length}/3)
                    </span>
                    <div className="flex gap-2">
                      {selectedAds.map((ad) => (
                        <div key={ad.id} className="relative">
                          <div className="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden">
                            <div className="w-full h-full bg-gray-300 flex items-center justify-center">
                              <span className="text-gray-500 text-xs">🖼️</span>
                            </div>
                          </div>
                          <button
                            onClick={() => removeFromComparison(ad.id)}
                            className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="flex gap-3">
                    <button
                      onClick={clearComparison}
                      className="px-4 py-2 text-gray-600 hover:text-gray-800"
                    >
                      مسح الكل
                    </button>
                    <button
                      onClick={() => setShowComparison(true)}
                      disabled={selectedAds.length < 2}
                      className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      مقارنة ({selectedAds.length})
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* قائمة الإعلانات */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-20">
            {sampleAds.map((ad) => {
              const isSelected = selectedAds.find(a => a.id === ad.id);
              const canAdd = selectedAds.length < 3;

              return (
                <div
                  key={ad.id}
                  className={`bg-white rounded-xl shadow-md overflow-hidden border-2 transition-all ${
                    isSelected ? 'border-primary-500 bg-primary-50' : 'border-gray-100 hover:border-gray-200'
                  }`}
                >
                  <div className="relative h-48 bg-gray-200 flex items-center justify-center">
                    <span className="text-gray-400 text-4xl">🖼️</span>
                    {isSelected && (
                      <div className="absolute top-3 right-3 w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center">
                        ✓
                      </div>
                    )}
                  </div>

                  <div className="p-5">
                    <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2">
                      {ad.title}
                    </h3>
                    
                    <div className="text-xl font-bold text-primary-600 mb-2">
                      {ad.price} {ad.currency}
                    </div>
                    
                    <div className="text-sm text-gray-600 mb-4">
                      📍 {ad.location}
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-gray-600">{ad.seller.name}</span>
                        {ad.seller.verified && (
                          <span className="text-green-500 text-sm">✓</span>
                        )}
                      </div>
                      
                      {isSelected ? (
                        <button
                          onClick={() => removeFromComparison(ad.id)}
                          className="px-4 py-2 bg-red-100 text-red-600 rounded-lg hover:bg-red-200 text-sm"
                        >
                          إزالة
                        </button>
                      ) : (
                        <button
                          onClick={() => addToComparison(ad)}
                          disabled={!canAdd}
                          className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                        >
                          {canAdd ? 'إضافة للمقارنة' : 'الحد الأقصى 3'}
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ) : (
        <div>
          {/* صفحة المقارنة */}
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-3xl font-bold text-gray-800">مقارنة الإعلانات</h1>
            <div className="flex gap-3">
              <button
                onClick={() => setShowComparison(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                العودة للقائمة
              </button>
              <button
                onClick={clearComparison}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                مسح المقارنة
              </button>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-right text-sm font-medium text-gray-500 w-48">
                      المواصفات
                    </th>
                    {selectedAds.map((ad) => (
                      <th key={ad.id} className="px-6 py-4 text-center min-w-64">
                        <div className="space-y-3">
                          <div className="w-full h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                            <span className="text-gray-400 text-2xl">🖼️</span>
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-800 text-sm line-clamp-2">
                              {ad.title}
                            </h3>
                            <div className="text-lg font-bold text-primary-600 mt-1">
                              {ad.price} {ad.currency}
                            </div>
                          </div>
                        </div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  <tr>
                    <td className="px-6 py-4 font-medium text-gray-800">الموقع</td>
                    {selectedAds.map((ad) => (
                      <td key={ad.id} className="px-6 py-4 text-center text-gray-700">
                        {ad.location}
                      </td>
                    ))}
                  </tr>
                  
                  <tr className="bg-gray-50">
                    <td className="px-6 py-4 font-medium text-gray-800">البائع</td>
                    {selectedAds.map((ad) => (
                      <td key={ad.id} className="px-6 py-4 text-center">
                        <div className="flex items-center justify-center gap-2">
                          <span className="text-gray-700">{ad.seller.name}</span>
                          {ad.seller.verified && (
                            <span className="text-green-500">✓</span>
                          )}
                        </div>
                      </td>
                    ))}
                  </tr>

                  <tr>
                    <td className="px-6 py-4 font-medium text-gray-800">تاريخ النشر</td>
                    {selectedAds.map((ad) => (
                      <td key={ad.id} className="px-6 py-4 text-center text-gray-700">
                        {ad.postedDate}
                      </td>
                    ))}
                  </tr>

                  {allSpecs.map((spec, index) => (
                    <tr key={spec} className={index % 2 === 0 ? 'bg-gray-50' : ''}>
                      <td className="px-6 py-4 font-medium text-gray-800">{spec}</td>
                      {selectedAds.map((ad) => (
                        <td key={ad.id} className="px-6 py-4 text-center text-gray-700">
                          {ad.specs[spec] || '-'}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* أزرار الإجراءات */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
            {selectedAds.map((ad) => (
              <div key={ad.id} className="bg-white rounded-lg shadow-md p-6 text-center">
                <h3 className="font-semibold text-gray-800 mb-4 line-clamp-2">
                  {ad.title}
                </h3>
                <div className="space-y-3">
                  <a
                    href={`/ad/${ad.id}`}
                    className="block w-full bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700 transition-colors"
                  >
                    عرض التفاصيل
                  </a>
                  <button className="w-full border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                    إضافة للمفضلة
                  </button>
                  <button
                    onClick={() => removeFromComparison(ad.id)}
                    className="w-full text-red-600 py-2 rounded-lg hover:bg-red-50 transition-colors"
                  >
                    إزالة من المقارنة
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default CompareAds;
