'use client';

import { useState } from 'react';

interface StarRatingProps {
  rating: number;
  maxRating?: number;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  interactive?: boolean;
  onChange?: (rating: number) => void;
  showValue?: boolean;
  className?: string;
}

const StarRating = ({
  rating,
  maxRating = 5,
  size = 'md',
  interactive = false,
  onChange,
  showValue = false,
  className = ''
}: StarRatingProps) => {
  const [hoverRating, setHoverRating] = useState(0);

  const sizeClasses = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-8 h-8'
  };

  const textSizes = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  const handleClick = (value: number) => {
    if (interactive && onChange) {
      onChange(value);
    }
  };

  const handleMouseEnter = (value: number) => {
    if (interactive) {
      setHoverRating(value);
    }
  };

  const handleMouseLeave = () => {
    if (interactive) {
      setHoverRating(0);
    }
  };

  const getStarFill = (starIndex: number) => {
    const currentRating = hoverRating || rating;
    const fillPercentage = Math.min(Math.max(currentRating - starIndex, 0), 1) * 100;
    return fillPercentage;
  };

  const StarIcon = ({ index, fillPercentage }: { index: number; fillPercentage: number }) => {
    const isActive = fillPercentage > 0;
    const isHovered = interactive && hoverRating > 0;
    
    return (
      <div
        className={`relative ${sizeClasses[size]} ${interactive ? 'cursor-pointer hover:scale-110' : ''} transition-all duration-300 ease-in-out`}
        onClick={() => handleClick(index + 1)}
        onMouseEnter={() => handleMouseEnter(index + 1)}
        onMouseLeave={handleMouseLeave}
      >
        {/* النجمة الخلفية */}
        <svg
          className={`absolute inset-0 ${sizeClasses[size]} text-gray-300 transition-all duration-300`}
          fill="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
        </svg>

        {/* النجمة المملوءة مع التدرج */}
        <div
          className="absolute inset-0 overflow-hidden"
          style={{ width: `${fillPercentage}%` }}
        >
          <svg
            className={`${sizeClasses[size]} transition-all duration-300 ${
              isActive
                ? isHovered
                  ? 'text-yellow-500'
                  : 'text-yellow-400'
                : 'text-gray-300'
            }`}
            fill="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
            style={{
              filter: isActive
                ? isHovered
                  ? 'drop-shadow(0 0 6px rgba(251, 191, 36, 0.8)) brightness(1.15)'
                  : 'drop-shadow(0 0 3px rgba(251, 191, 36, 0.5))'
                : 'none'
            }}
          >
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
          </svg>
        </div>

        {/* تأثير التوهج عند التفاعل */}
        {interactive && isHovered && (
          <div className="absolute inset-0 animate-pulse">
            <svg
              className={`${sizeClasses[size]} text-yellow-300 opacity-40`}
              fill="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
              style={{ filter: 'drop-shadow(0 0 8px rgba(251, 191, 36, 0.9))' }}
            >
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
            </svg>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`flex items-center gap-1 ${className}`}>
      <div className="flex items-center gap-0.5">
        {Array.from({ length: maxRating }, (_, index) => (
          <StarIcon
            key={index}
            index={index}
            fillPercentage={getStarFill(index)}
          />
        ))}
      </div>
      
      {showValue && (
        <span className={`ml-2 font-medium text-gray-600 ${textSizes[size]}`}>
          {rating.toFixed(1)}
        </span>
      )}
    </div>
  );
};

export default StarRating;
