// نظام إدارة التقييمات والمراجعات
export interface Review {
  id: number;
  reviewerId: number;
  revieweeId: number;
  adId?: number;
  rating: number; // 1-5
  title: string;
  comment: string;
  pros?: string[];
  cons?: string[];
  verified: boolean;
  helpful: number;
  notHelpful: number;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
  updatedAt: string;
}

export interface ReviewSummary {
  userId: number;
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  recentReviews: Review[];
}

export interface ReviewResponse {
  id: number;
  reviewId: number;
  userId: number;
  response: string;
  createdAt: string;
}

// بيانات تجريبية للتقييمات
const sampleReviews: Review[] = [
  {
    id: 1,
    reviewerId: 2,
    revieweeId: 1,
    adId: 1,
    rating: 5,
    title: 'بائع ممتاز وصادق',
    comment: 'تعامل راقي جداً، الشقة كما هو موصوف تماماً، والبائع متعاون ومتفهم. أنصح بالتعامل معه.',
    pros: ['صادق في الوصف', 'متعاون', 'سرعة في الرد'],
    cons: [],
    verified: true,
    helpful: 8,
    notHelpful: 0,
    status: 'approved',
    createdAt: '2024-01-16T14:30:00Z',
    updatedAt: '2024-01-16T14:30:00Z'
  },
  {
    id: 2,
    reviewerId: 3,
    revieweeId: 2,
    adId: 2,
    rating: 4,
    title: 'معرض موثوق',
    comment: 'السيارة في حالة جيدة كما وُصفت، لكن السعر كان قابل للتفاوض أكثر. بشكل عام تجربة جيدة.',
    pros: ['سيارة نظيفة', 'أوراق سليمة', 'معرض منظم'],
    cons: ['السعر مرتفع قليلاً'],
    verified: true,
    helpful: 5,
    notHelpful: 1,
    status: 'approved',
    createdAt: '2024-01-18T10:15:00Z',
    updatedAt: '2024-01-18T10:15:00Z'
  },
  {
    id: 3,
    reviewerId: 1,
    revieweeId: 3,
    adId: 3,
    rating: 5,
    title: 'متجر ممتاز للإلكترونيات',
    comment: 'الجهاز أصلي وجديد بالكرتونة، الضمان ساري، والخدمة ممتازة. أنصح بشدة.',
    pros: ['منتجات أصلية', 'ضمان ساري', 'خدمة عملاء ممتازة'],
    cons: [],
    verified: true,
    helpful: 12,
    notHelpful: 0,
    status: 'approved',
    createdAt: '2024-01-19T16:45:00Z',
    updatedAt: '2024-01-19T16:45:00Z'
  },
  {
    id: 4,
    reviewerId: 4,
    revieweeId: 1,
    rating: 3,
    title: 'تجربة متوسطة',
    comment: 'البائع لطيف لكن كان هناك تأخير في الموعد المحدد للمعاينة.',
    pros: ['بائع لطيف'],
    cons: ['تأخير في المواعيد'],
    verified: false,
    helpful: 2,
    notHelpful: 3,
    status: 'approved',
    createdAt: '2024-01-20T09:30:00Z',
    updatedAt: '2024-01-20T09:30:00Z'
  }
];

const sampleResponses: ReviewResponse[] = [
  {
    id: 1,
    reviewId: 4,
    userId: 1,
    response: 'أعتذر عن التأخير، كان هناك ظرف طارئ. أقدر تفهمك وأتطلع للتعامل معك مرة أخرى.',
    createdAt: '2024-01-20T11:00:00Z'
  }
];

export class ReviewService {
  // إضافة تقييم جديد
  static async addReview(
    reviewerId: number,
    revieweeId: number,
    rating: number,
    title: string,
    comment: string,
    adId?: number,
    pros?: string[],
    cons?: string[]
  ): Promise<{ success: boolean; data?: Review; error?: string }> {
    try {
      // التحقق من عدم وجود تقييم مسبق لنفس الإعلان
      if (adId) {
        const existingReview = sampleReviews.find(r => 
          r.reviewerId === reviewerId && 
          r.revieweeId === revieweeId && 
          r.adId === adId
        );

        if (existingReview) {
          return { success: false, error: 'لقد قمت بتقييم هذا البائع مسبقاً لهذا الإعلان' };
        }
      }

      // التحقق من صحة التقييم
      if (rating < 1 || rating > 5) {
        return { success: false, error: 'التقييم يجب أن يكون بين 1 و 5' };
      }

      if (!title.trim() || !comment.trim()) {
        return { success: false, error: 'العنوان والتعليق مطلوبان' };
      }

      const newReview: Review = {
        id: Date.now(),
        reviewerId,
        revieweeId,
        adId,
        rating,
        title: title.trim(),
        comment: comment.trim(),
        pros: pros?.filter(p => p.trim()) || [],
        cons: cons?.filter(c => c.trim()) || [],
        verified: false, // سيتم التحقق لاحقاً
        helpful: 0,
        notHelpful: 0,
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      sampleReviews.push(newReview);
      return { success: true, data: newReview };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في إضافة التقييم' };
    }
  }

  // الحصول على تقييمات المستخدم
  static getUserReviews(userId: number, type: 'received' | 'given' = 'received'): Review[] {
    const reviews = type === 'received' 
      ? sampleReviews.filter(r => r.revieweeId === userId)
      : sampleReviews.filter(r => r.reviewerId === userId);

    return reviews
      .filter(r => r.status === 'approved')
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  // الحصول على ملخص التقييمات
  static getReviewSummary(userId: number): ReviewSummary {
    const reviews = this.getUserReviews(userId, 'received');
    
    const ratingDistribution = {
      1: reviews.filter(r => r.rating === 1).length,
      2: reviews.filter(r => r.rating === 2).length,
      3: reviews.filter(r => r.rating === 3).length,
      4: reviews.filter(r => r.rating === 4).length,
      5: reviews.filter(r => r.rating === 5).length
    };

    const averageRating = reviews.length > 0 
      ? reviews.reduce((sum, r) => sum + r.rating, 0) / reviews.length 
      : 0;

    return {
      userId,
      totalReviews: reviews.length,
      averageRating: Math.round(averageRating * 10) / 10,
      ratingDistribution,
      recentReviews: reviews.slice(0, 5)
    };
  }

  // تحديث التقييم
  static async updateReview(
    reviewId: number,
    reviewerId: number,
    updates: Partial<Pick<Review, 'rating' | 'title' | 'comment' | 'pros' | 'cons'>>
  ): Promise<{ success: boolean; data?: Review; error?: string }> {
    try {
      const review = sampleReviews.find(r => r.id === reviewId && r.reviewerId === reviewerId);
      
      if (!review) {
        return { success: false, error: 'التقييم غير موجود' };
      }

      if (review.status !== 'pending') {
        return { success: false, error: 'لا يمكن تعديل تقييم تم الموافقة عليه' };
      }

      // تحديث البيانات
      Object.assign(review, updates, {
        updatedAt: new Date().toISOString()
      });

      return { success: true, data: review };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في تحديث التقييم' };
    }
  }

  // حذف التقييم
  static async deleteReview(reviewId: number, userId: number): Promise<{ success: boolean; error?: string }> {
    try {
      const index = sampleReviews.findIndex(r => 
        r.id === reviewId && (r.reviewerId === userId || r.revieweeId === userId)
      );
      
      if (index === -1) {
        return { success: false, error: 'التقييم غير موجود' };
      }

      sampleReviews.splice(index, 1);
      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في حذف التقييم' };
    }
  }

  // تقييم مفيد/غير مفيد
  static async rateReviewHelpfulness(
    reviewId: number,
    userId: number,
    helpful: boolean
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const review = sampleReviews.find(r => r.id === reviewId);
      
      if (!review) {
        return { success: false, error: 'التقييم غير موجود' };
      }

      if (review.reviewerId === userId || review.revieweeId === userId) {
        return { success: false, error: 'لا يمكنك تقييم تقييمك الخاص' };
      }

      // في التطبيق الحقيقي، ستتحقق من عدم تقييم المستخدم مسبقاً
      if (helpful) {
        review.helpful += 1;
      } else {
        review.notHelpful += 1;
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في تقييم المفيدة' };
    }
  }

  // إضافة رد على التقييم
  static async addResponse(
    reviewId: number,
    userId: number,
    response: string
  ): Promise<{ success: boolean; data?: ReviewResponse; error?: string }> {
    try {
      const review = sampleReviews.find(r => r.id === reviewId);
      
      if (!review) {
        return { success: false, error: 'التقييم غير موجود' };
      }

      if (review.revieweeId !== userId) {
        return { success: false, error: 'يمكن فقط للشخص المُقيَّم الرد على التقييم' };
      }

      // التحقق من عدم وجود رد مسبق
      const existingResponse = sampleResponses.find(r => r.reviewId === reviewId);
      if (existingResponse) {
        return { success: false, error: 'لقد قمت بالرد على هذا التقييم مسبقاً' };
      }

      const newResponse: ReviewResponse = {
        id: Date.now(),
        reviewId,
        userId,
        response: response.trim(),
        createdAt: new Date().toISOString()
      };

      sampleResponses.push(newResponse);
      return { success: true, data: newResponse };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في إضافة الرد' };
    }
  }

  // الحصول على رد التقييم
  static getReviewResponse(reviewId: number): ReviewResponse | null {
    return sampleResponses.find(r => r.reviewId === reviewId) || null;
  }

  // الموافقة على التقييم (للإدارة)
  static async approveReview(reviewId: number): Promise<{ success: boolean; error?: string }> {
    try {
      const review = sampleReviews.find(r => r.id === reviewId);
      
      if (!review) {
        return { success: false, error: 'التقييم غير موجود' };
      }

      review.status = 'approved';
      review.verified = true;
      review.updatedAt = new Date().toISOString();

      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في الموافقة على التقييم' };
    }
  }

  // رفض التقييم (للإدارة)
  static async rejectReview(reviewId: number, reason?: string): Promise<{ success: boolean; error?: string }> {
    try {
      const review = sampleReviews.find(r => r.id === reviewId);
      
      if (!review) {
        return { success: false, error: 'التقييم غير موجود' };
      }

      review.status = 'rejected';
      review.updatedAt = new Date().toISOString();
      
      if (reason) {
        review.comment += `\n[مرفوض: ${reason}]`;
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في رفض التقييم' };
    }
  }

  // الحصول على إحصائيات التقييمات
  static getReviewStats() {
    const totalReviews = sampleReviews.length;
    const approvedReviews = sampleReviews.filter(r => r.status === 'approved').length;
    const pendingReviews = sampleReviews.filter(r => r.status === 'pending').length;
    const averageRating = sampleReviews.length > 0 
      ? sampleReviews.reduce((sum, r) => sum + r.rating, 0) / sampleReviews.length 
      : 0;

    const ratingDistribution = {
      1: sampleReviews.filter(r => r.rating === 1).length,
      2: sampleReviews.filter(r => r.rating === 2).length,
      3: sampleReviews.filter(r => r.rating === 3).length,
      4: sampleReviews.filter(r => r.rating === 4).length,
      5: sampleReviews.filter(r => r.rating === 5).length
    };

    return {
      totalReviews,
      approvedReviews,
      pendingReviews,
      averageRating: Math.round(averageRating * 10) / 10,
      ratingDistribution,
      verifiedReviews: sampleReviews.filter(r => r.verified).length
    };
  }

  // البحث في التقييمات
  static searchReviews(query: string, filters?: {
    rating?: number;
    verified?: boolean;
    status?: Review['status'];
  }): Review[] {
    let results = sampleReviews.filter(review => {
      const searchText = `${review.title} ${review.comment}`.toLowerCase();
      return searchText.includes(query.toLowerCase());
    });

    if (filters) {
      if (filters.rating) {
        results = results.filter(r => r.rating === filters.rating);
      }
      if (filters.verified !== undefined) {
        results = results.filter(r => r.verified === filters.verified);
      }
      if (filters.status) {
        results = results.filter(r => r.status === filters.status);
      }
    }

    return results.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }
}
