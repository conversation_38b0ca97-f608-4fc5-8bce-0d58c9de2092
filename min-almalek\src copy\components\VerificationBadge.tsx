import React from 'react';
import { VerificationBadge as BadgeType, BadgeUtils } from '@/lib/verification';

interface VerificationBadgeProps {
  badgeId?: string;
  badge?: BadgeType;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  showTooltip?: boolean;
  className?: string;
}

interface BadgeDisplayProps {
  badges: string[];
  size?: 'xs' | 'sm' | 'md' | 'lg';
  maxDisplay?: number;
  showTooltip?: boolean;
  className?: string;
}

// مكون عرض شارة واحدة
const VerificationBadge: React.FC<VerificationBadgeProps> = ({
  badgeId,
  badge,
  size = 'md',
  showTooltip = true,
  className = ''
}) => {
  const badgeData = badge || (badgeId ? BadgeUtils.getBadgeById(badgeId) : undefined);

  if (!badgeData) return null;

  const sizeClasses = {
    xs: 'text-xs px-1.5 py-0.5 gap-1',
    sm: 'text-xs px-2 py-1 gap-1',
    md: 'text-sm px-3 py-1 gap-1.5',
    lg: 'text-base px-4 py-2 gap-2'
  };

  const iconSizes = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  return (
    <div className="relative inline-block">
      <span
        className={`
          inline-flex items-center rounded-full font-medium border
          ${badgeData.bgColor} ${badgeData.borderColor} ${badgeData.textColor}
          ${sizeClasses[size]} ${className}
          transition-all duration-200 hover:scale-105 cursor-default
        `}
        title={showTooltip ? badgeData.description : undefined}
      >
        <div
          className={`
            ${iconSizes[size]} rounded-full flex items-center justify-center
            font-bold text-white shadow-sm
          `}
          style={{ backgroundColor: badgeData.color }}
        >
          ✓
        </div>
        <span className="font-semibold">{badgeData.name}</span>
      </span>

      {showTooltip && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-10">
          {badgeData.description}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
        </div>
      )}
    </div>
  );
};

// مكون عرض عدة شارات
export const BadgeDisplay: React.FC<BadgeDisplayProps> = ({
  badges,
  size = 'md',
  maxDisplay = 2,
  showTooltip = true,
  className = ''
}) => {
  if (!badges || badges.length === 0) return null;

  // ترتيب الشارات حسب الأولوية
  const sortedBadges = badges
    .map(id => BadgeUtils.getBadgeById(id))
    .filter(Boolean)
    .sort((a, b) => (b?.priority || 0) - (a?.priority || 0)) as BadgeType[];

  const displayBadges = sortedBadges.slice(0, maxDisplay);
  const remainingCount = sortedBadges.length - maxDisplay;

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {displayBadges.map((badge) => (
        <VerificationBadge
          key={badge.id}
          badge={badge}
          size={size}
          showTooltip={showTooltip}
        />
      ))}

      {remainingCount > 0 && (
        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
          +{remainingCount}
        </span>
      )}
    </div>
  );
};

// مكون شارة مبسطة للإعلانات
export const AdBadge: React.FC<{
  userBadges: string[];
  size?: 'xs' | 'sm' | 'md';
  className?: string;
}> = ({ userBadges, size = 'sm', className = '' }) => {
  const highestBadge = BadgeUtils.getHighestBadge(userBadges);

  if (!highestBadge) return null;

  return (
    <VerificationBadge
      badge={highestBadge}
      size={size}
      showTooltip={true}
      className={className}
    />
  );
};

// مكون شارة للملف الشخصي
export const ProfileBadge: React.FC<{
  userBadges: string[];
  showAll?: boolean;
  className?: string;
}> = ({ userBadges, showAll = false, className = '' }) => {
  if (!userBadges || userBadges.length === 0) return null;

  if (showAll) {
    return (
      <BadgeDisplay
        badges={userBadges}
        size="md"
        maxDisplay={userBadges.length}
        className={className}
      />
    );
  }

  const highestBadge = BadgeUtils.getHighestBadge(userBadges);

  if (!highestBadge) return null;

  return (
    <div className={`flex items-center gap-3 ${className}`}>
      <VerificationBadge
        badge={highestBadge}
        size="lg"
        showTooltip={true}
      />
      <div className="text-sm text-gray-600">
        <div className="font-medium">{highestBadge.description}</div>
        <div className="text-xs text-gray-500">
          {highestBadge.benefits.slice(0, 2).join(' • ')}
        </div>
      </div>
    </div>
  );
};

// مكون معلومات الشارة المفصلة
export const BadgeInfo: React.FC<{
  badgeId: string;
  className?: string;
}> = ({ badgeId, className = '' }) => {
  const badge = BadgeUtils.getBadgeById(badgeId);

  if (!badge) return null;

  return (
    <div className={`bg-white rounded-lg border p-4 ${className}`}>
      <div className="flex items-center gap-3 mb-3">
        <div className={`w-12 h-12 rounded-full ${badge.bgColor} ${badge.borderColor} border-2 flex items-center justify-center relative`}>
          <div
            className="w-8 h-8 rounded-full flex items-center justify-center font-bold text-white shadow-sm text-lg"
            style={{ backgroundColor: badge.color }}
          >
            ✓
          </div>
        </div>
        <div>
          <h3 className="font-semibold text-gray-800">{badge.name}</h3>
          <p className="text-sm text-gray-600">{badge.description}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 className="font-medium text-gray-700 mb-2">متطلبات الحصول:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            {badge.requirements.map((req, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-green-500 mt-0.5">✓</span>
                <span>{req}</span>
              </li>
            ))}
          </ul>
        </div>

        <div>
          <h4 className="font-medium text-gray-700 mb-2">المميزات:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            {badge.benefits.map((benefit, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-blue-500 mt-0.5">★</span>
                <span>{benefit}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default VerificationBadge;
