/* [project]/src/styles/loading.css [app-client] (css) */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-delay {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes logo-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }

  50% {
    transform: scale(1.05);
    opacity: .8;
  }
}

@keyframes progress-shimmer {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.animate-fade-in {
  animation: .8s ease-out forwards fade-in;
}

.animate-fade-in-delay {
  animation: 1s ease-out .3s forwards fade-in-delay;
  opacity: 0;
}

.animate-logo-pulse {
  animation: 2s ease-in-out infinite logo-pulse;
}

.progress-shimmer {
  background: linear-gradient(90deg, #0000, #fff6, #0000);
  background-size: 200px 100%;
  animation: 1.5s infinite progress-shimmer;
}

.logo-glow {
  filter: drop-shadow(0 0 20px #22c55e4d);
}

.loading-dots {
  display: inline-block;
}

.loading-dots:after {
  content: "";
  animation: 1.5s infinite loading-dots;
}

@keyframes loading-dots {
  0%, 20% {
    content: "";
  }

  40% {
    content: ".";
  }

  60% {
    content: "..";
  }

  80%, 100% {
    content: "...";
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

@keyframes glow {
  0%, 100% {
    opacity: .3;
    transform: scale(1);
  }

  50% {
    opacity: .6;
    transform: scale(1.02);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: .3;
    transform: scale(.8)rotate(0);
  }

  50% {
    opacity: 1;
    transform: scale(1.2)rotate(180deg);
  }
}

@keyframes logo-glow-pulse {
  0%, 100% {
    filter: drop-shadow(0 0 10px #22c55e4d) drop-shadow(0 0 20px #ffd70033);
  }

  50% {
    filter: drop-shadow(0 0 20px #22c55e99) drop-shadow(0 0 40px #ffd70066);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(.8);
    opacity: 1;
  }

  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-shimmer {
  animation: 2s infinite shimmer;
}

.animate-glow {
  animation: 3s ease-in-out infinite glow;
}

.animate-twinkle {
  animation: 2s ease-in-out infinite twinkle;
}

.animate-logo-glow-pulse {
  animation: 2s ease-in-out infinite logo-glow-pulse;
}

.animate-pulse-ring {
  animation: 2s cubic-bezier(.455, .03, .515, .955) infinite pulse-ring;
}

.logo-glow {
  filter: drop-shadow(0 0 15px #22c55e66) drop-shadow(0 0 30px #ffd7004d) drop-shadow(0 0 45px #22c55e33);
  animation: 3s ease-in-out infinite logo-glow-pulse;
}

.loading-background {
  background: radial-gradient(circle, #22c55e1a 0%, #ffd7000d 30%, #0000 70%);
  animation: 4s ease-in-out infinite glow;
}

/*# sourceMappingURL=src_styles_loading_b52d8e.css.map*/