'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';

interface SafeNavigationButtonProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  onClick?: (e: React.MouseEvent) => void;
  disabled?: boolean;
  title?: string;
  [key: string]: any;
}

const SafeNavigationButton = ({
  href,
  children,
  className,
  onClick,
  disabled = false,
  title,
  ...props
}: SafeNavigationButtonProps) => {
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleClick = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (disabled || isNavigating) return;

    if (onClick) {
      onClick(e);
    }

    if (isClient && href) {
      setIsNavigating(true);

      try {
        // التوجيه المباشر بدون setTimeout
        router.push(href);
      } catch (error) {
        console.error('Navigation error:', error);
      } finally {
        // إعادة تعيين حالة التنقل بعد فترة قصيرة
        setTimeout(() => {
          setIsNavigating(false);
        }, 500);
      }
    }
  }, [disabled, isNavigating, onClick, isClient, href, router]);

  // عرض زر عادي قبل الـ hydration
  if (!isClient) {
    return (
      <button
        className={`${className} relative z-20`}
        disabled={disabled}
        title={title}
        {...props}
      >
        {children}
      </button>
    );
  }

  return (
    <button
      onClick={handleClick}
      className={`${className} relative z-20 ${isNavigating ? 'opacity-75 cursor-wait' : ''}`}
      disabled={disabled || isNavigating}
      title={title}
      {...props}
    >
      {isNavigating ? (
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          {children}
        </div>
      ) : (
        children
      )}
    </button>
  );
};

export default SafeNavigationButton;
