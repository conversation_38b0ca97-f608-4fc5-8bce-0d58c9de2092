'use client';

import { useState, useEffect } from 'react';
import { Ad, SearchFilters } from '@/lib/data';

interface UseAdsResult {
  ads: Ad[];
  loading: boolean;
  error: string | null;
  stats: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } | null;
  refetch: () => void;
}

export const useAds = (filters: SearchFilters = {}, page: number = 1, limit: number = 12): UseAdsResult => {
  const [ads, setAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<UseAdsResult['stats']>(null);

  const fetchAds = async () => {
    setLoading(true);
    setError(null);

    try {
      // بناء URL مع المعاملات
      const params = new URLSearchParams();
      
      if (filters.keyword) params.append('keyword', filters.keyword);
      if (filters.category) params.append('category', filters.category);
      if (filters.subcategory) params.append('subcategory', filters.subcategory);
      if (filters.governorate) params.append('governorate', filters.governorate);
      if (filters.city) params.append('city', filters.city);
      if (filters.priceMin !== undefined) params.append('priceMin', filters.priceMin.toString());
      if (filters.priceMax !== undefined) params.append('priceMax', filters.priceMax.toString());
      if (filters.currency) params.append('currency', filters.currency);
      if (filters.condition) params.append('condition', filters.condition);
      if (filters.sellerType) params.append('sellerType', filters.sellerType);
      if (filters.verified) params.append('verified', 'true');
      if (filters.featured) params.append('featured', 'true');
      if (filters.hasImages) params.append('hasImages', 'true');
      if (filters.datePosted) params.append('datePosted', filters.datePosted);
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      
      params.append('page', page.toString());
      params.append('limit', limit.toString());

      const response = await fetch(`/api/ads?${params.toString()}`);
      const result = await response.json();

      if (result.success) {
        setAds(result.data);
        setStats(result.stats);
      } else {
        setError(result.error || 'حدث خطأ في جلب البيانات');
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال بالخادم');
      console.error('خطأ في جلب الإعلانات:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAds();
  }, [JSON.stringify(filters), page, limit]);

  return {
    ads,
    loading,
    error,
    stats,
    refetch: fetchAds
  };
};

interface UseAdResult {
  ad: Ad | null;
  similarAds: Ad[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

export const useAd = (id: number): UseAdResult => {
  const [ad, setAd] = useState<Ad | null>(null);
  const [similarAds, setSimilarAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAd = async () => {
    if (!id) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/ads/${id}`);
      const result = await response.json();

      if (result.success) {
        setAd(result.data.ad);
        setSimilarAds(result.data.similarAds || []);
      } else {
        setError(result.error || 'حدث خطأ في جلب الإعلان');
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال بالخادم');
      console.error('خطأ في جلب الإعلان:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAd();
  }, [id]);

  return {
    ad,
    similarAds,
    loading,
    error,
    refetch: fetchAd
  };
};

// Hook لإنشاء إعلان جديد
export const useCreateAd = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createAd = async (adData: Partial<Ad>) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/ads', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(adData),
      });

      const result = await response.json();

      if (result.success) {
        return result.data;
      } else {
        setError(result.error || 'حدث خطأ في إنشاء الإعلان');
        return null;
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال بالخادم');
      console.error('خطأ في إنشاء الإعلان:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    createAd,
    loading,
    error
  };
};

// Hook لتحديث إعلان
export const useUpdateAd = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateAd = async (id: number, adData: Partial<Ad>) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/ads/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(adData),
      });

      const result = await response.json();

      if (result.success) {
        return result.data;
      } else {
        setError(result.error || 'حدث خطأ في تحديث الإعلان');
        return null;
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال بالخادم');
      console.error('خطأ في تحديث الإعلان:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return {
    updateAd,
    loading,
    error
  };
};

// Hook لحذف إعلان
export const useDeleteAd = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const deleteAd = async (id: number) => {
    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/ads/${id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (result.success) {
        return true;
      } else {
        setError(result.error || 'حدث خطأ في حذف الإعلان');
        return false;
      }
    } catch (err) {
      setError('حدث خطأ في الاتصال بالخادم');
      console.error('خطأ في حذف الإعلان:', err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    deleteAd,
    loading,
    error
  };
};
