// نظام علامات التوثيق والشارات للمستخدمين والإعلانات

export interface VerificationBadge {
  id: string;
  name: string;
  nameEn: string;
  description: string;
  icon: string;
  color: string;
  bgColor: string;
  borderColor: string;
  textColor: string;
  priority: number; // أولوية العرض (أعلى رقم = أولوية أكبر)
  requirements: string[];
  benefits: string[];
}

// علامات التوثيق للأفراد
export const INDIVIDUAL_BADGES: VerificationBadge[] = [
  {
    id: 'verified-basic',
    name: 'موثق أساسي',
    nameEn: 'Basic Verified',
    description: 'مستخدم موثق مع الباقة الأساسية',
    icon: '✓',
    color: '#3B82F6', // أزرق
    bgColor: 'bg-blue-100',
    borderColor: 'border-blue-300',
    textColor: 'text-blue-700',
    priority: 1,
    requirements: [
      'اشتراك في الباقة الأساسية',
      'تأكيد رقم الهاتف',
      'تأكيد البريد الإلكتروني'
    ],
    benefits: [
      'شارة التوثيق الأساسي',
      'ثقة أكبر من المشترين',
      'أولوية في البحث'
    ]
  },
  {
    id: 'verified-premium',
    name: 'موثق مميز',
    nameEn: 'Premium Verified',
    description: 'مستخدم موثق مع الباقة المميزة',
    icon: '✓',
    color: '#9CA3AF', // فضي
    bgColor: 'bg-gray-100',
    borderColor: 'border-gray-400',
    textColor: 'text-gray-700',
    priority: 2,
    requirements: [
      'اشتراك في الباقة المميزة',
      'تأكيد الهوية',
      'تقييم إيجابي 4+ نجوم',
      'عدم وجود شكاوى'
    ],
    benefits: [
      'شارة التوثيق المميز',
      'ظهور مميز في النتائج',
      'ثقة عالية من المشترين',
      'دعم فني أولوية'
    ]
  },
  {
    id: 'verified-gold',
    name: 'موثق ذهبي',
    nameEn: 'Gold Verified',
    description: 'مستخدم موثق مع باقة الأعمال - الأكثر إعلاناً',
    icon: '✓',
    color: '#F59E0B', // ذهبي
    bgColor: 'bg-yellow-100',
    borderColor: 'border-yellow-400',
    textColor: 'text-yellow-700',
    priority: 3,
    requirements: [
      'اشتراك في باقة الأعمال',
      'أكثر من 50 إعلان منشور',
      'تقييم ممتاز 4.5+ نجوم',
      'عضوية أكثر من 6 أشهر',
      'تأكيد الهوية والعنوان'
    ],
    benefits: [
      'شارة التوثيق الذهبي',
      'أولوية قصوى في النتائج',
      'ثقة استثنائية',
      'دعم VIP',
      'إحصائيات متقدمة'
    ]
  }
];

// علامات التوثيق للشركات
export const BUSINESS_BADGES: VerificationBadge[] = [
  {
    id: 'business-verified',
    name: 'شركة موثقة',
    nameEn: 'Verified Business',
    description: 'شركة موثقة رسمياً - باقة البداية',
    icon: '✓',
    color: '#3B82F6', // أزرق
    bgColor: 'bg-blue-100',
    borderColor: 'border-blue-300',
    textColor: 'text-blue-700',
    priority: 4,
    requirements: [
      'اشتراك في باقة الشركات',
      'تأكيد السجل التجاري',
      'تأكيد عنوان الشركة',
      'تأكيد رقم الضريبة'
    ],
    benefits: [
      'شارة الشركة الموثقة',
      'ثقة عالية من العملاء',
      'ظهور في قسم الشركات',
      'دعم فني للشركات'
    ]
  },
  {
    id: 'real-estate-office',
    name: 'مكتب عقاري موثق',
    nameEn: 'Verified Real Estate Office',
    description: 'مكتب عقاري موثق ومرخص - باقة مخصصة',
    icon: '✓',
    color: '#9CA3AF', // فضي
    bgColor: 'bg-gray-100',
    borderColor: 'border-gray-400',
    textColor: 'text-gray-700',
    priority: 5,
    requirements: [
      'اشتراك في باقة المكاتب العقارية',
      'ترخيص مكتب عقاري ساري',
      'تأكيد عنوان المكتب',
      'تأكيد الهوية المهنية',
      'السجل التجاري ساري المفعول'
    ],
    benefits: [
      'شارة المكتب العقاري الموثق',
      'أولوية في البحث العقاري',
      'ثقة عالية من العملاء',
      'أدوات تقييم العقارات',
      'تقارير السوق العقاري',
      'نظام إدارة العملاء',
      'دعم فني متخصص'
    ]
  },
  {
    id: 'business-premium',
    name: 'شركة مميزة',
    nameEn: 'Premium Business',
    description: 'شركة مميزة مع الخطة المهنية',
    icon: '✓',
    color: '#F59E0B', // ذهبي
    bgColor: 'bg-yellow-100',
    borderColor: 'border-yellow-400',
    textColor: 'text-yellow-700',
    priority: 6,
    requirements: [
      'اشتراك في الخطة المهنية',
      'أكثر من 100 إعلان',
      'تقييم ممتاز من العملاء',
      'عضوية أكثر من سنة'
    ],
    benefits: [
      'شارة الشركة المميزة',
      'أولوية عالية في النتائج',
      'تقارير متقدمة',
      'دعم مخصص'
    ]
  }
];

// علامات خاصة إضافية
export const SPECIAL_BADGES: VerificationBadge[] = [
  {
    id: 'top-seller',
    name: 'أفضل بائع',
    nameEn: 'Top Seller',
    description: 'من أفضل البائعين على المنصة',
    icon: '✓',
    color: '#F59E0B', // ذهبي
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    textColor: 'text-yellow-800',
    priority: 7,
    requirements: [
      'أكثر من 100 عملية بيع ناجحة',
      'تقييم 4.8+ نجوم',
      'معدل استجابة 95%+',
      'عدم وجود شكاوى جدية'
    ],
    benefits: [
      'شارة أفضل بائع',
      'ظهور في قائمة الأفضل',
      'ثقة استثنائية',
      'مكافآت خاصة'
    ]
  },
  {
    id: 'new-member',
    name: 'عضو جديد',
    nameEn: 'New Member',
    description: 'عضو جديد على المنصة',
    icon: '✓',
    color: '#06B6D4', // سماوي
    bgColor: 'bg-cyan-50',
    borderColor: 'border-cyan-200',
    textColor: 'text-cyan-700',
    priority: 0,
    requirements: [
      'عضوية أقل من شهر',
      'تأكيد البريد الإلكتروني'
    ],
    benefits: [
      'ترحيب خاص',
      'دعم للمبتدئين',
      'نصائح مفيدة'
    ]
  }
];

// جميع الشارات
export const ALL_BADGES = [
  ...INDIVIDUAL_BADGES,
  ...BUSINESS_BADGES,
  ...SPECIAL_BADGES
];

// دوال مساعدة
export const BadgeUtils = {
  // الحصول على شارة بالمعرف
  getBadgeById: (id: string): VerificationBadge | undefined => {
    return ALL_BADGES.find(badge => badge.id === id);
  },

  // الحصول على أعلى شارة للمستخدم
  getHighestBadge: (userBadges: string[]): VerificationBadge | undefined => {
    const badges = userBadges
      .map(id => BadgeUtils.getBadgeById(id))
      .filter(Boolean) as VerificationBadge[];

    return badges.sort((a, b) => b.priority - a.priority)[0];
  },

  // فلترة الشارات حسب النوع
  getIndividualBadges: () => INDIVIDUAL_BADGES,
  getBusinessBadges: () => BUSINESS_BADGES,
  getSpecialBadges: () => SPECIAL_BADGES,

  // تحديد نوع المستخدم من الشارة
  getUserType: (badgeId: string): 'individual' | 'business' | 'special' => {
    if (INDIVIDUAL_BADGES.find(b => b.id === badgeId)) return 'individual';
    if (BUSINESS_BADGES.find(b => b.id === badgeId)) return 'business';
    return 'special';
  },

  // تنسيق عرض الشارة
  formatBadgeDisplay: (badge: VerificationBadge, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizes = {
      sm: 'text-xs px-2 py-1',
      md: 'text-sm px-3 py-1',
      lg: 'text-base px-4 py-2'
    };

    return {
      className: `inline-flex items-center gap-1 rounded-full font-medium ${badge.bgColor} ${badge.borderColor} ${badge.textColor} border ${sizes[size]}`,
      content: `${badge.icon} ${badge.name}`
    };
  }
};

// تحديد شارة المستخدم بناءً على اشتراكه
export const determineUserBadge = (
  subscriptionType: string,
  adsCount: number = 0,
  rating: number = 0,
  membershipMonths: number = 0,
  isBusinessVerified: boolean = false
): string[] => {
  const badges: string[] = [];

  // شارات الأفراد
  if (subscriptionType === 'basic') {
    badges.push('verified-basic');
  } else if (subscriptionType === 'premium') {
    badges.push('verified-premium');
  } else if (subscriptionType === 'business' || subscriptionType === 'individual-business') {
    badges.push('verified-gold');
  }

  // شارات الشركات
  if (subscriptionType.startsWith('business-')) {
    if (subscriptionType === 'business-starter') {
      // خطة البداية - شارة زرقاء
      if (isBusinessVerified) {
        badges.push('business-verified');
      }
    } else if (subscriptionType === 'real-estate-office') {
      // باقة المكاتب العقارية - شارة فضية
      badges.push('real-estate-office');
    } else if (subscriptionType === 'business-professional') {
      // الخطة المهنية - شارة ذهبية
      badges.push('business-premium');
    }
  }

  // شارة المكاتب العقارية (يمكن أن تكون منفصلة)
  if (subscriptionType === 'real-estate-office') {
    badges.push('real-estate-office');
  }

  // شارات خاصة
  if (membershipMonths < 1) {
    badges.push('new-member');
  }

  if (adsCount >= 100 && rating >= 4.8) {
    badges.push('top-seller');
  }

  return badges;
};

export default {
  INDIVIDUAL_BADGES,
  BUSINESS_BADGES,
  SPECIAL_BADGES,
  ALL_BADGES,
  BadgeUtils,
  determineUserBadge
};
