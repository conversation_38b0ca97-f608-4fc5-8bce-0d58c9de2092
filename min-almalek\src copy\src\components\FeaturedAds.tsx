import Link from 'next/link';
import Image from 'next/image';
import { AdBadge } from '@/components/VerificationBadge';
import { determineUserBadge } from '@/lib/verification';

const featuredAds = [
  {
    id: 1,
    title: 'شقة للبيع في دمشق - المالكي',
    price: '85,000,000',
    currency: 'ل.س',
    location: 'دمشق - المالكي',
    category: 'عقارات',
    image: '/api/placeholder/300/200',
    featured: true,
    urgent: false,
    specs: ['3 غرف', '2 حمام', '150 م²'],
    postedDate: 'منذ ساعتين',
    views: 245,
    isFavorite: false,
    seller: {
      name: 'أحمد محمد',
      subscription: 'premium',
      adsCount: 15,
      rating: 4.5,
      membershipMonths: 8,
      isBusinessVerified: false
    }
  },
  {
    id: 2,
    title: 'BMW X5 2020 فل أوبشن',
    price: '45,000',
    currency: '$',
    location: 'حلب - الفرقان',
    category: 'سيارات',
    image: '/api/placeholder/300/200',
    featured: true,
    urgent: true,
    specs: ['2020', 'أوتوماتيك', '50,000 كم'],
    postedDate: 'منذ 3 ساعات',
    views: 189,
    isFavorite: false,
    seller: {
      name: 'معرض الفخامة للسيارات',
      subscription: 'business-professional',
      adsCount: 120,
      rating: 4.8,
      membershipMonths: 18,
      isBusinessVerified: true
    }
  },
  {
    id: 3,
    title: 'iPhone 15 Pro Max جديد بالكرتونة',
    price: '1,200',
    currency: '$',
    location: 'دمشق - أبو رمانة',
    category: 'إلكترونيات',
    image: '/api/placeholder/300/200',
    featured: true,
    urgent: false,
    specs: ['256 GB', 'تيتانيوم', 'ضمان سنة'],
    postedDate: 'منذ 5 ساعات',
    views: 156,
    isFavorite: false,
    seller: {
      name: 'فاطمة أحمد',
      subscription: 'basic',
      adsCount: 8,
      rating: 4.2,
      membershipMonths: 3,
      isBusinessVerified: false
    }
  },
  {
    id: 4,
    title: 'مطلوب مطور ويب - دوام كامل',
    price: '800,000',
    currency: 'ل.س',
    location: 'دمشق - المزة',
    category: 'وظائف',
    image: '/api/placeholder/300/200',
    featured: true,
    urgent: false,
    specs: ['React', 'Node.js', 'خبرة 3 سنوات'],
    postedDate: 'منذ يوم',
    views: 98,
    isFavorite: false,
    seller: {
      name: 'شركة التقنيات المتقدمة',
      subscription: 'business-starter',
      adsCount: 45,
      rating: 4.6,
      membershipMonths: 12,
      isBusinessVerified: true
    }
  },
  {
    id: 5,
    title: 'فيلا للإيجار في حمص',
    price: '500,000',
    currency: 'ل.س',
    location: 'حمص - الوعر',
    category: 'عقارات',
    image: '/api/placeholder/300/200',
    featured: true,
    urgent: false,
    specs: ['5 غرف', 'حديقة', '300 م²'],
    postedDate: 'منذ يومين',
    views: 134,
    isFavorite: false,
    seller: {
      name: 'خالد السوري',
      subscription: 'business',
      adsCount: 65,
      rating: 4.7,
      membershipMonths: 15,
      isBusinessVerified: false
    }
  },
  {
    id: 6,
    title: 'لابتوب Dell Gaming جديد',
    price: '850',
    currency: '$',
    location: 'اللاذقية - الزراعة',
    category: 'إلكترونيات',
    image: '/api/placeholder/300/200',
    featured: true,
    urgent: true,
    specs: ['RTX 3060', '16GB RAM', 'SSD 512GB'],
    postedDate: 'منذ 3 أيام',
    views: 87,
    isFavorite: false,
    seller: {
      name: 'محمد علي',
      subscription: 'premium',
      adsCount: 25,
      rating: 4.6,
      membershipMonths: 10,
      isBusinessVerified: false
    }
  },
  {
    id: 15,
    title: 'شقة فاخرة للبيع في أبو رمانة - 180 متر',
    price: '120,000,000',
    currency: 'ل.س',
    location: 'دمشق - أبو رمانة',
    category: 'عقارات',
    image: '/api/placeholder/300/200',
    featured: true,
    urgent: false,
    specs: ['4 غرف', '3 حمامات', '180 م²', 'إطلالة بانورامية'],
    postedDate: 'منذ ساعة',
    views: 567,
    isFavorite: false,
    seller: {
      name: 'مكتب دمشق العقاري المتخصص',
      subscription: 'real-estate-office',
      adsCount: 89,
      rating: 4.9,
      membershipMonths: 24,
      isBusinessVerified: true
    }
  }
];

const FeaturedAds = () => {
  return (
    <section className="py-12 bg-gradient-to-br from-primary-50 to-blue-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-3xl font-bold text-gray-800 mb-2">الإعلانات المميزة</h2>
            <p className="text-gray-600">أفضل العروض والإعلانات المختارة بعناية</p>
          </div>
          <Link
            href="/featured"
            className="text-primary-600 hover:text-primary-700 font-medium flex items-center gap-2"
          >
            عرض الكل
            <span>←</span>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredAds.map((ad) => (
            <div
              key={ad.id}
              className="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group border border-gray-100"
            >
              {/* Image Container */}
              <div className="relative h-48 overflow-hidden">
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-400 text-4xl">🖼️</span>
                </div>

                {/* Badges */}
                <div className="absolute top-3 right-3 flex flex-col gap-2">
                  {ad.featured && (
                    <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                      مميز
                    </span>
                  )}
                  {ad.urgent && (
                    <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                      عاجل
                    </span>
                  )}
                </div>

                {/* Favorite Button */}
                <button className="absolute top-3 left-3 w-8 h-8 bg-white/80 rounded-full flex items-center justify-center hover:bg-white transition-colors">
                  <span className="text-gray-600 hover:text-red-500">♡</span>
                </button>

                {/* Category Badge */}
                <div className="absolute bottom-3 right-3">
                  <span className="bg-black/70 text-white text-xs px-2 py-1 rounded-full">
                    {ad.category}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="p-5">
                <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors">
                  {ad.title}
                </h3>

                <div className="flex items-center justify-between mb-3">
                  <div className="text-2xl font-bold text-primary-600">
                    {ad.price.toLocaleString()} {ad.currency}
                  </div>
                </div>

                <div className="flex items-center text-gray-600 text-sm mb-3">
                  <span className="mr-1">📍</span>
                  {ad.location}
                </div>

                {/* معلومات البائع مع شارة التوثيق */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">بواسطة:</span>
                    <span className="text-sm font-medium text-gray-800">{ad.seller.name}</span>
                  </div>
                  <AdBadge
                    userBadges={determineUserBadge(
                      ad.seller.subscription,
                      ad.seller.adsCount,
                      ad.seller.rating,
                      ad.seller.membershipMonths,
                      ad.seller.isBusinessVerified
                    )}
                    size="xs"
                  />
                </div>

                {/* Specs */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {ad.specs.map((spec, index) => (
                    <span
                      key={index}
                      className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                    >
                      {spec}
                    </span>
                  ))}
                </div>

                {/* Footer */}
                <div className="flex items-center justify-between text-sm text-gray-500 pt-3 border-t border-gray-100">
                  <span>{ad.postedDate}</span>
                  <div className="flex items-center gap-1">
                    <span>👁️</span>
                    <span>{ad.views}</span>
                  </div>
                </div>
              </div>

              {/* Hover Overlay - تقليل z-index لتجنب التداخل مع الأزرار */}
              <Link
                href={`/ad/${ad.id}`}
                className="absolute inset-0 z-0"
              >
                <span className="sr-only">عرض الإعلان</span>
              </Link>
            </div>
          ))}
        </div>

        <div className="text-center mt-10">
          <Link
            href="/featured"
            className="inline-flex items-center px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium text-lg"
          >
            عرض جميع الإعلانات المميزة
            <span className="mr-2">→</span>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedAds;
