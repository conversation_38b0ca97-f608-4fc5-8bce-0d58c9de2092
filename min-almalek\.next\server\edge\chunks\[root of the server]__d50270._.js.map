{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\nimport type { NextRequest } from 'next/server';\n\n// الصفحات المحمية التي تتطلب تسجيل دخول\nconst protectedPaths = [\n  '/settings',\n  '/profile',\n  '/dashboard',\n  '/add-ad',\n  '/my-ads',\n  '/favorites',\n  '/messages',\n  '/notifications',\n  '/subscription',\n  '/billing'\n];\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl;\n  console.log('🔍 Middleware: فحص المسار:', pathname);\n\n  // التحقق من الصفحات المحمية للمستخدمين العاديين\n  const isProtectedPath = protectedPaths.some(path => pathname.startsWith(path));\n\n  if (isProtectedPath) {\n    console.log('🛡️ Middleware: صفحة محمية تم اكتشافها');\n\n    // التحقق من وجود token في الكوكيز\n    const authToken = request.cookies.get('auth-token')?.value;\n\n    console.log('🍪 Middleware: البحث عن token:', authToken ? 'موجود' : 'غير موجود');\n\n    // إذا لم يكن هناك token، إعادة توجيه للصفحة الرئيسية\n    if (!authToken) {\n      console.log('❌ Middleware: غير مصرح، إعادة توجيه للصفحة الرئيسية');\n      const url = request.nextUrl.clone();\n      url.pathname = '/';\n      url.searchParams.set('auth_required', 'true');\n      url.searchParams.set('message', 'يجب تسجيل الدخول للوصول لهذه الصفحة');\n      return NextResponse.redirect(url);\n    }\n  }\n\n  // التحقق من الصفحات الإدارية\n  if (pathname.startsWith('/admin')) {\n    console.log('🛡️ Middleware: صفحة إدارية تم اكتشافها');\n\n    // السماح بصفحة تسجيل الدخول الإداري\n    if (request.nextUrl.pathname === '/admin/login') {\n      console.log('✅ Middleware: السماح بصفحة تسجيل الدخول');\n      return NextResponse.next();\n    }\n\n    // التحقق من وجود جلسة إدارية في الكوكيز\n    const adminSession = request.cookies.get('admin-session');\n    console.log('🍪 Middleware: البحث عن جلسة في الكوكيز:', adminSession ? 'موجودة' : 'غير موجودة');\n\n    // مؤقت<|im_start|>: السماح بالدخول بدون تحقق للاختبار\n    console.log('⚠️ Middleware: السماح المؤقت بالدخول للاختبار');\n    return NextResponse.next();\n\n    /*\n    if (!adminSession) {\n      console.log('❌ Middleware: لا توجد جلسة، إعادة توجيه لتسجيل الدخول');\n      return NextResponse.redirect(new URL('/admin/login', request.url));\n    }\n\n    // في التطبيق الحقيقي، ستتحقق من صحة التوكن هنا\n    try {\n      const sessionData = JSON.parse(adminSession.value);\n      if (!sessionData.admin || !sessionData.token) {\n        console.log('❌ Middleware: جلسة غير صالحة، إعادة توجيه');\n        return NextResponse.redirect(new URL('/admin/login', request.url));\n      }\n      console.log('✅ Middleware: جلسة صالحة، السماح بالمرور');\n    } catch (error) {\n      console.log('❌ Middleware: خطأ في تحليل الجلسة، إعادة توجيه');\n      return NextResponse.redirect(new URL('/admin/login', request.url));\n    }\n    */\n  }\n\n  console.log('✅ Middleware: السماح بالمرور');\n  return NextResponse.next();\n}\n\nexport const config = {\n  matcher: [\n    '/admin/:path*',\n    '/settings/:path*',\n    '/profile/:path*',\n    '/dashboard/:path*',\n    '/add-ad/:path*',\n    '/my-ads/:path*',\n    '/favorites/:path*',\n    '/messages/:path*',\n    '/notifications/:path*',\n    '/subscription/:path*',\n    '/billing/:path*'\n  ]\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGA,wCAAwC;AACxC,MAAM,iBAAiB;IACrB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IACpC,QAAQ,GAAG,CAAC,8BAA8B;IAE1C,gDAAgD;IAChD,MAAM,kBAAkB,eAAe,IAAI,CAAC,CAAA,OAAQ,SAAS,UAAU,CAAC;IAExE,IAAI,iBAAiB;QACnB,QAAQ,GAAG,CAAC;QAEZ,kCAAkC;QAClC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAErD,QAAQ,GAAG,CAAC,kCAAkC,YAAY,UAAU;QAEpE,qDAAqD;QACrD,IAAI,CAAC,WAAW;YACd,QAAQ,GAAG,CAAC;YACZ,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;YACjC,IAAI,QAAQ,GAAG;YACf,IAAI,YAAY,CAAC,GAAG,CAAC,iBAAiB;YACtC,IAAI,YAAY,CAAC,GAAG,CAAC,WAAW;YAChC,OAAO,qLAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;IACF;IAEA,6BAA6B;IAC7B,IAAI,SAAS,UAAU,CAAC,WAAW;QACjC,QAAQ,GAAG,CAAC;QAEZ,oCAAoC;QACpC,IAAI,QAAQ,OAAO,CAAC,QAAQ,KAAK,gBAAgB;YAC/C,QAAQ,GAAG,CAAC;YACZ,OAAO,qLAAA,CAAA,eAAY,CAAC,IAAI;QAC1B;QAEA,wCAAwC;QACxC,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC;QACzC,QAAQ,GAAG,CAAC,4CAA4C,eAAe,WAAW;QAElF,sDAAsD;QACtD,QAAQ,GAAG,CAAC;QACZ,OAAO,qLAAA,CAAA,eAAY,CAAC,IAAI;IAExB;;;;;;;;;;;;;;;;;;IAkBA,GACF;IAEA,QAAQ,GAAG,CAAC;IACZ,OAAO,qLAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH"}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}