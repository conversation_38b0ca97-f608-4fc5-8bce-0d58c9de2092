// نظام خوارزميات ترتيب الإعلانات
export interface AdPriority {
  adId: number;
  score: number;
  factors: {
    subscriptionTier: number;
    adAge: number;
    userRating: number;
    adQuality: number;
    engagement: number;
    location: number;
    price: number;
  };
}

export interface SubscriptionTier {
  id: string;
  name: string;
  priority: number; // كلما زاد الرقم، زادت الأولوية
  features: string[];
  boostMultiplier: number; // مضاعف التعزيز
  maxAdsPerDay: number;
  highlightDuration: number; // بالأيام
}

// أنواع الاشتراكات مرتبة حسب الأولوية
export const subscriptionTiers: SubscriptionTier[] = [
  {
    id: 'diamond',
    name: 'الماسي',
    priority: 100,
    features: ['إعلانات مميزة', 'ترتيب أولوي', 'دعم فوري', 'إحصائيات متقدمة'],
    boostMultiplier: 5.0,
    maxAdsPerDay: 50,
    highlightDuration: 30
  },
  {
    id: 'platinum',
    name: 'البلاتيني',
    priority: 80,
    features: ['إعلانات مميزة', 'ترتيب متقدم', 'دعم سريع'],
    boostMultiplier: 4.0,
    maxAdsPerDay: 30,
    highlightDuration: 21
  },
  {
    id: 'gold',
    name: 'الذهبي',
    priority: 60,
    features: ['إعلانات مميزة', 'ترتيب محسن'],
    boostMultiplier: 3.0,
    maxAdsPerDay: 20,
    highlightDuration: 14
  },
  {
    id: 'silver',
    name: 'الفضي',
    priority: 40,
    features: ['ترتيب محسن'],
    boostMultiplier: 2.0,
    maxAdsPerDay: 15,
    highlightDuration: 7
  },
  {
    id: 'bronze',
    name: 'البرونزي',
    priority: 20,
    features: ['ترتيب عادي'],
    boostMultiplier: 1.5,
    maxAdsPerDay: 10,
    highlightDuration: 3
  },
  {
    id: 'free',
    name: 'مجاني',
    priority: 10,
    features: ['إعلانات أساسية'],
    boostMultiplier: 1.0,
    maxAdsPerDay: 5,
    highlightDuration: 0
  }
];

export class AdAlgorithm {
  // حساب نقاط الإعلان بناءً على عوامل متعددة
  static calculateAdScore(ad: any): AdPriority {
    const subscriptionScore = this.getSubscriptionScore(ad.seller?.subscription || 'free');
    const ageScore = this.getAdAgeScore(ad.createdAt);
    const ratingScore = this.getUserRatingScore(ad.seller?.rating || 0);
    const qualityScore = this.getAdQualityScore(ad);
    const engagementScore = this.getEngagementScore(ad);
    const locationScore = this.getLocationScore(ad);
    const priceScore = this.getPriceScore(ad);

    const factors = {
      subscriptionTier: subscriptionScore,
      adAge: ageScore,
      userRating: ratingScore,
      adQuality: qualityScore,
      engagement: engagementScore,
      location: locationScore,
      price: priceScore
    };

    // حساب النقاط الإجمالية مع الأوزان
    const totalScore = 
      subscriptionScore * 0.4 +  // 40% للاشتراك
      qualityScore * 0.2 +       // 20% لجودة الإعلان
      ratingScore * 0.15 +       // 15% لتقييم المستخدم
      engagementScore * 0.1 +    // 10% للتفاعل
      ageScore * 0.08 +          // 8% لعمر الإعلان
      locationScore * 0.04 +     // 4% للموقع
      priceScore * 0.03;         // 3% للسعر

    return {
      adId: ad.id,
      score: Math.round(totalScore * 100) / 100,
      factors
    };
  }

  // نقاط الاشتراك
  static getSubscriptionScore(subscriptionType: string): number {
    const tier = subscriptionTiers.find(t => t.id === subscriptionType);
    return tier ? tier.priority : 10;
  }

  // نقاط عمر الإعلان (الإعلانات الأحدث تحصل على نقاط أعلى)
  static getAdAgeScore(createdAt: string): number {
    const now = new Date();
    const adDate = new Date(createdAt);
    const daysDiff = Math.floor((now.getTime() - adDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysDiff <= 1) return 100;
    if (daysDiff <= 3) return 80;
    if (daysDiff <= 7) return 60;
    if (daysDiff <= 14) return 40;
    if (daysDiff <= 30) return 20;
    return 10;
  }

  // نقاط تقييم المستخدم
  static getUserRatingScore(rating: number): number {
    return Math.min(rating * 20, 100); // تحويل التقييم من 5 إلى 100
  }

  // نقاط جودة الإعلان
  static getAdQualityScore(ad: any): number {
    let score = 50; // نقاط أساسية

    // وجود صور
    if (ad.images && ad.images.length > 0) {
      score += 20;
      if (ad.images.length >= 3) score += 10;
      if (ad.images.length >= 5) score += 10;
    }

    // طول الوصف
    if (ad.description && ad.description.length > 100) {
      score += 10;
      if (ad.description.length > 300) score += 10;
    }

    // وجود معلومات الاتصال
    if (ad.seller?.phone) score += 10;

    // التحقق من الهوية
    if (ad.seller?.isVerified) score += 20;

    return Math.min(score, 100);
  }

  // نقاط التفاعل
  static getEngagementScore(ad: any): number {
    const views = ad.views || 0;
    const favorites = ad.favoritesCount || 0;
    const messages = ad.messagesCount || 0;

    let score = 0;
    
    // نقاط المشاهدات
    if (views > 100) score += 20;
    else if (views > 50) score += 15;
    else if (views > 20) score += 10;
    else if (views > 5) score += 5;

    // نقاط المفضلة
    if (favorites > 10) score += 30;
    else if (favorites > 5) score += 20;
    else if (favorites > 2) score += 10;
    else if (favorites > 0) score += 5;

    // نقاط الرسائل
    if (messages > 5) score += 30;
    else if (messages > 2) score += 20;
    else if (messages > 0) score += 10;

    return Math.min(score, 100);
  }

  // نقاط الموقع (المواقع المطلوبة أكثر تحصل على نقاط أعلى)
  static getLocationScore(ad: any): number {
    const popularLocations = ['دمشق', 'حلب', 'حمص', 'اللاذقية'];
    const location = ad.location?.governorate || '';
    
    if (popularLocations.includes(location)) {
      return 80 + (popularLocations.indexOf(location) * 5);
    }
    
    return 50; // نقاط عادية للمواقع الأخرى
  }

  // نقاط السعر (الأسعار المعقولة تحصل على نقاط أعلى)
  static getPriceScore(ad: any): number {
    const price = ad.price || 0;
    const category = ad.category || '';
    
    // نطاقات سعرية مختلفة حسب الفئة
    const priceRanges: { [key: string]: { min: number; max: number } } = {
      'شقق': { min: 50000000, max: 200000000 },
      'فلل': { min: 100000000, max: 500000000 },
      'محلات': { min: 20000000, max: 100000000 },
      'أراضي': { min: 10000000, max: 100000000 }
    };

    const range = priceRanges[category];
    if (!range) return 50;

    if (price >= range.min && price <= range.max) {
      return 100;
    } else if (price < range.min) {
      return Math.max(20, 100 - ((range.min - price) / range.min) * 80);
    } else {
      return Math.max(20, 100 - ((price - range.max) / range.max) * 80);
    }
  }

  // ترتيب الإعلانات حسب الخوارزمية
  static sortAdsByAlgorithm(ads: any[]): any[] {
    const adsWithScores = ads.map(ad => ({
      ...ad,
      priority: this.calculateAdScore(ad)
    }));

    return adsWithScores.sort((a, b) => b.priority.score - a.priority.score);
  }

  // فلترة الإعلانات المميزة
  static getFeaturedAds(ads: any[], limit: number = 10): any[] {
    const premiumTiers = ['diamond', 'platinum', 'gold'];
    
    const featuredAds = ads.filter(ad => 
      premiumTiers.includes(ad.seller?.subscription || 'free')
    );

    return this.sortAdsByAlgorithm(featuredAds).slice(0, limit);
  }

  // الحصول على إعلانات مقترحة بناءً على تفضيلات المستخدم
  static getRecommendedAds(ads: any[], userPreferences: any, limit: number = 5): any[] {
    let filteredAds = ads;

    // فلترة حسب الموقع المفضل
    if (userPreferences.preferredLocation) {
      filteredAds = filteredAds.filter(ad => 
        ad.location?.governorate === userPreferences.preferredLocation
      );
    }

    // فلترة حسب الفئة المفضلة
    if (userPreferences.preferredCategory) {
      filteredAds = filteredAds.filter(ad => 
        ad.category === userPreferences.preferredCategory
      );
    }

    // فلترة حسب النطاق السعري
    if (userPreferences.priceRange) {
      filteredAds = filteredAds.filter(ad => 
        ad.price >= userPreferences.priceRange.min && 
        ad.price <= userPreferences.priceRange.max
      );
    }

    return this.sortAdsByAlgorithm(filteredAds).slice(0, limit);
  }

  // تطبيق تعزيز الإعلانات المدفوعة
  static applyPaidBoost(ads: any[]): any[] {
    return ads.map(ad => {
      const tier = subscriptionTiers.find(t => t.id === (ad.seller?.subscription || 'free'));
      const boostMultiplier = tier ? tier.boostMultiplier : 1.0;
      
      if (ad.priority) {
        ad.priority.score *= boostMultiplier;
      }
      
      return ad;
    });
  }

  // الحصول على إحصائيات الخوارزمية
  static getAlgorithmStats(ads: any[]) {
    const adsWithScores = ads.map(ad => this.calculateAdScore(ad));
    
    const subscriptionDistribution = subscriptionTiers.map(tier => ({
      tier: tier.name,
      count: ads.filter(ad => (ad.seller?.subscription || 'free') === tier.id).length
    }));

    const averageScore = adsWithScores.reduce((sum, ad) => sum + ad.score, 0) / adsWithScores.length;
    
    return {
      totalAds: ads.length,
      averageScore: Math.round(averageScore * 100) / 100,
      subscriptionDistribution,
      topScores: adsWithScores.sort((a, b) => b.score - a.score).slice(0, 10)
    };
  }
}
