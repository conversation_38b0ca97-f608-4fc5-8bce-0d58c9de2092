'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';

interface Payment {
  id: string;
  userId: number;
  userName: string;
  userEmail: string;
  amount: number;
  currency: string;
  plan: string;
  method: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  transactionId: string;
  createdAt: string;
  completedAt?: string;
  description: string;
}

interface Invoice {
  id: string;
  userId: number;
  userName: string;
  amount: number;
  currency: string;
  plan: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  dueDate: string;
  createdAt: string;
  paidAt?: string;
  items: { description: string; quantity: number; price: number }[];
}

export default function PaymentsPage() {
  const [payments, setPayments] = useState<Payment[]>([]);
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'payments' | 'invoices'>('payments');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      // محاكاة تحميل البيانات
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockPayments: Payment[] = [
        {
          id: 'pay_001',
          userId: 1,
          userName: 'أحمد محمد',
          userEmail: '<EMAIL>',
          amount: 150000,
          currency: 'SYP',
          plan: 'premium',
          method: 'visa',
          status: 'completed',
          transactionId: 'txn_abc123',
          createdAt: '2024-01-20T10:30:00Z',
          completedAt: '2024-01-20T10:32:00Z',
          description: 'اشتراك خطة مميزة - شهر واحد'
        },
        {
          id: 'pay_002',
          userId: 2,
          userName: 'شركة العقارات الذهبية',
          userEmail: '<EMAIL>',
          amount: 300000,
          currency: 'SYP',
          plan: 'business',
          method: 'paypal',
          status: 'pending',
          transactionId: 'txn_def456',
          createdAt: '2024-01-20T14:15:00Z',
          description: 'اشتراك خطة تجارية - شهر واحد'
        },
        {
          id: 'pay_003',
          userId: 3,
          userName: 'مكتب الأمل العقاري',
          userEmail: '<EMAIL>',
          amount: 700000,
          currency: 'SYP',
          plan: 'real-estate-office',
          method: 'bank_transfer',
          status: 'failed',
          transactionId: 'txn_ghi789',
          createdAt: '2024-01-19T16:45:00Z',
          description: 'اشتراك مكتب عقاري - شهر واحد'
        }
      ];

      const mockInvoices: Invoice[] = [
        {
          id: 'inv_001',
          userId: 1,
          userName: 'أحمد محمد',
          amount: 150000,
          currency: 'SYP',
          plan: 'premium',
          status: 'paid',
          dueDate: '2024-02-20T23:59:59Z',
          createdAt: '2024-01-20T10:30:00Z',
          paidAt: '2024-01-20T10:32:00Z',
          items: [
            { description: 'اشتراك خطة مميزة', quantity: 1, price: 150000 }
          ]
        },
        {
          id: 'inv_002',
          userId: 2,
          userName: 'شركة العقارات الذهبية',
          amount: 300000,
          currency: 'SYP',
          plan: 'business',
          status: 'sent',
          dueDate: '2024-02-25T23:59:59Z',
          createdAt: '2024-01-20T14:15:00Z',
          items: [
            { description: 'اشتراك خطة تجارية', quantity: 1, price: 300000 }
          ]
        }
      ];

      setPayments(mockPayments);
      setInvoices(mockInvoices);
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('ar-SY', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتظار';
      case 'completed': return 'مكتمل';
      case 'failed': return 'فشل';
      case 'refunded': return 'مسترد';
      case 'draft': return 'مسودة';
      case 'sent': return 'مرسل';
      case 'paid': return 'مدفوع';
      case 'overdue': return 'متأخر';
      case 'cancelled': return 'ملغي';
      default: return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'completed': case 'paid': return 'bg-green-100 text-green-800';
      case 'failed': case 'overdue': return 'bg-red-100 text-red-800';
      case 'refunded': case 'cancelled': return 'bg-gray-100 text-gray-800';
      case 'sent': return 'bg-blue-100 text-blue-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getMethodLabel = (method: string) => {
    switch (method) {
      case 'visa': return 'فيزا';
      case 'mastercard': return 'ماستركارد';
      case 'paypal': return 'باي بال';
      case 'bank_transfer': return 'تحويل بنكي';
      case 'cash_app': return 'كاش آب';
      case 'apple_pay': return 'آبل باي';
      default: return method;
    }
  };

  const filteredPayments = payments.filter(payment => 
    filterStatus === 'all' || payment.status === filterStatus
  );

  const filteredInvoices = invoices.filter(invoice => 
    filterStatus === 'all' || invoice.status === filterStatus
  );

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'Cairo, sans-serif' }}>
            الفواتير والمدفوعات
          </h1>
          <p className="text-gray-600 mt-1">
            إدارة جميع المعاملات المالية والفواتير
          </p>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المدفوعات</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(payments.reduce((sum, p) => sum + p.amount, 0), 'SYP')}
                </p>
              </div>
              <span className="text-2xl">💰</span>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">مدفوعات مكتملة</p>
                <p className="text-2xl font-bold text-blue-600">
                  {payments.filter(p => p.status === 'completed').length}
                </p>
              </div>
              <span className="text-2xl">✅</span>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">في الانتظار</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {payments.filter(p => p.status === 'pending').length}
                </p>
              </div>
              <span className="text-2xl">⏳</span>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">فواتير مدفوعة</p>
                <p className="text-2xl font-bold text-purple-600">
                  {invoices.filter(i => i.status === 'paid').length}
                </p>
              </div>
              <span className="text-2xl">📄</span>
            </div>
          </div>
        </div>

        {/* التبويبات */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('payments')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'payments'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                المدفوعات ({payments.length})
              </button>
              <button
                onClick={() => setActiveTab('invoices')}
                className={`py-4 px-6 text-sm font-medium border-b-2 ${
                  activeTab === 'invoices'
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                الفواتير ({invoices.length})
              </button>
            </nav>
          </div>

          {/* الفلاتر */}
          <div className="p-4 border-b border-gray-200">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="all">جميع الحالات</option>
              {activeTab === 'payments' ? (
                <>
                  <option value="pending">في الانتظار</option>
                  <option value="completed">مكتمل</option>
                  <option value="failed">فشل</option>
                  <option value="refunded">مسترد</option>
                </>
              ) : (
                <>
                  <option value="draft">مسودة</option>
                  <option value="sent">مرسل</option>
                  <option value="paid">مدفوع</option>
                  <option value="overdue">متأخر</option>
                  <option value="cancelled">ملغي</option>
                </>
              )}
            </select>
          </div>

          {/* المحتوى */}
          <div className="p-6">
            {activeTab === 'payments' ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المستخدم
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المبلغ
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الخطة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        طريقة الدفع
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        التاريخ
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredPayments.map((payment) => (
                      <tr key={payment.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{payment.userName}</div>
                            <div className="text-sm text-gray-500">{payment.userEmail}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(payment.amount, payment.currency)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {payment.plan}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {getMethodLabel(payment.method)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(payment.status)}`}>
                            {getStatusLabel(payment.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(payment.createdAt)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        رقم الفاتورة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المستخدم
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المبلغ
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الحالة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        تاريخ الاستحقاق
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredInvoices.map((invoice) => (
                      <tr key={invoice.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {invoice.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{invoice.userName}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(invoice.amount, invoice.currency)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(invoice.status)}`}>
                            {getStatusLabel(invoice.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(invoice.dueDate)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button className="text-blue-600 hover:text-blue-900 ml-3">
                            عرض
                          </button>
                          <button className="text-green-600 hover:text-green-900">
                            تحميل PDF
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
