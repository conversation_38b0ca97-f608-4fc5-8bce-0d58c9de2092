'use client';

import { useState } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { JobPosting } from '@/lib/jobs';
import { AdBadge } from '@/components/VerificationBadge';
import { determineUserBadge } from '@/lib/verification';

// بيانات تجريبية للشركة
const companyData = {
  id: 'comp1',
  name: 'شركة التقنيات المتقدمة',
  logo: '/company-logo.png',
  industry: 'تكنولوجيا المعلومات',
  size: 'متوسطة (51-200)',
  location: 'دمشق، سوريا',
  website: 'https://advanced-tech.sy',
  description: 'شركة رائدة في مجال تطوير البرمجيات والحلول التقنية المتقدمة',
  founded: '2015',
  employees: 120,
  verified: true,
  subscription: 'premium',
  badges: determineUserBadge('business-professional', 120, 4.8, 8, true)
};

// بيانات تجريبية للوظائف
const companyJobs: JobPosting[] = [
  {
    id: '1',
    companyId: 'comp1',
    companyName: 'شركة التقنيات المتقدمة',
    title: 'مطور React.js متقدم',
    department: 'التطوير',
    location: 'دمشق',
    workType: 'دوام كامل',
    workModel: 'مختلط',
    salaryRange: { min: 800000, max: 1200000, currency: 'ل.س', period: 'شهري' },
    description: 'نبحث عن مطور React.js متقدم...',
    requirements: ['خبرة 3+ سنوات في React.js'],
    responsibilities: ['تطوير واجهات المستخدم'],
    benefits: ['تأمين صحي', 'مكافآت'],
    skills: ['React.js', 'TypeScript'],
    experienceLevel: 'متقدم',
    experienceYears: { min: 3, max: 7 },
    educationLevel: 'بكالوريوس',
    applicationDeadline: '2024-03-15',
    postedDate: '2024-02-15',
    status: 'نشط',
    applicationsCount: 45,
    featured: true,
    urgent: false,
    category: 'technology'
  },
  {
    id: '2',
    companyId: 'comp1',
    companyName: 'شركة التقنيات المتقدمة',
    title: 'مصمم UX/UI',
    department: 'التصميم',
    location: 'دمشق',
    workType: 'دوام كامل',
    workModel: 'حضوري',
    salaryRange: { min: 600000, max: 900000, currency: 'ل.س', period: 'شهري' },
    description: 'مطلوب مصمم UX/UI مبدع...',
    requirements: ['خبرة 2+ سنوات في التصميم'],
    responsibilities: ['تصميم واجهات المستخدم'],
    benefits: ['تأمين صحي', 'تدريب'],
    skills: ['Figma', 'Adobe XD'],
    experienceLevel: 'متوسط',
    experienceYears: { min: 2, max: 5 },
    educationLevel: 'بكالوريوس',
    applicationDeadline: '2024-03-20',
    postedDate: '2024-02-10',
    status: 'نشط',
    applicationsCount: 28,
    featured: false,
    urgent: true,
    category: 'design'
  }
];

export default function CompanyDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const [jobs] = useState<JobPosting[]>(companyJobs);

  const tabs = [
    { id: 'overview', name: 'نظرة عامة', icon: '📊' },
    { id: 'jobs', name: 'الوظائف', icon: '💼' },
    { id: 'applications', name: 'التقديمات', icon: '📝' },
    { id: 'candidates', name: 'المرشحين', icon: '👥' },
    { id: 'analytics', name: 'التحليلات', icon: '📈' },
    { id: 'settings', name: 'الإعدادات', icon: '⚙️' }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">الوظائف النشطة</p>
              <p className="text-2xl font-bold text-gray-900">{jobs.filter(j => j.status === 'نشط').length}</p>
            </div>
            <div className="text-3xl">💼</div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي التقديمات</p>
              <p className="text-2xl font-bold text-gray-900">
                {jobs.reduce((sum, job) => sum + job.applicationsCount, 0)}
              </p>
            </div>
            <div className="text-3xl">📝</div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">مشاهدات الملف</p>
              <p className="text-2xl font-bold text-gray-900">1,234</p>
            </div>
            <div className="text-3xl">👁️</div>
          </div>
        </div>

        <div className="bg-white rounded-lg p-6 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">معدل الاستجابة</p>
              <p className="text-2xl font-bold text-gray-900">85%</p>
            </div>
            <div className="text-3xl">📊</div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">النشاط الأخير</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">تقديم جديد على وظيفة مطور React.js</p>
                <p className="text-xs text-gray-500">منذ 5 دقائق</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">تم نشر وظيفة مصمم UX/UI</p>
                <p className="text-xs text-gray-500">منذ ساعتين</p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">تحديث ملف الشركة</p>
                <p className="text-xs text-gray-500">منذ يوم واحد</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">إجراءات سريعة</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              href="/jobs/post"
              className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="text-2xl">➕</div>
              <div>
                <h4 className="font-medium text-gray-900">نشر وظيفة جديدة</h4>
                <p className="text-sm text-gray-600">أضف فرصة عمل جديدة</p>
              </div>
            </Link>

            <Link
              href="/company/candidates"
              className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="text-2xl">🔍</div>
              <div>
                <h4 className="font-medium text-gray-900">البحث عن مواهب</h4>
                <p className="text-sm text-gray-600">استكشف قاعدة المرشحين</p>
              </div>
            </Link>

            <Link
              href="/company/profile"
              className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="text-2xl">✏️</div>
              <div>
                <h4 className="font-medium text-gray-900">تحديث الملف</h4>
                <p className="text-sm text-gray-600">حدث معلومات الشركة</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );

  const renderJobs = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">إدارة الوظائف</h3>
        <Link
          href="/jobs/post"
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
        >
          + نشر وظيفة جديدة
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الوظيفة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  التقديمات
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ النشر
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {jobs.map((job) => (
                <tr key={job.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 flex items-center gap-2">
                        {job.title}
                        {job.featured && <span className="text-yellow-500">⭐</span>}
                        {job.urgent && <span className="text-red-500">🔥</span>}
                      </div>
                      <div className="text-sm text-gray-500">{job.department} • {job.location}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      job.status === 'نشط'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {job.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {job.applicationsCount}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {job.postedDate}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button className="text-primary-600 hover:text-primary-900">عرض</button>
                      <button className="text-gray-600 hover:text-gray-900">تعديل</button>
                      <button className="text-red-600 hover:text-red-900">حذف</button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Company Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex items-center gap-6">
            <div className="w-20 h-20 bg-primary-100 rounded-lg flex items-center justify-center">
              <span className="text-3xl">🏢</span>
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-2xl font-bold text-gray-900">{companyData.name}</h1>
                <AdBadge userBadges={companyData.badges} size="sm" />
              </div>
              <p className="text-gray-600 mb-2">{companyData.description}</p>
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <span>🏭 {companyData.industry}</span>
                <span>👥 {companyData.size}</span>
                <span>📍 {companyData.location}</span>
                <span>📅 تأسست {companyData.founded}</span>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500 mb-1">الاشتراك</div>
              <div className="text-lg font-semibold text-primary-600 capitalize">
                {companyData.subscription}
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-8">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.icon} {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div>
          {activeTab === 'overview' && renderOverview()}
          {activeTab === 'jobs' && renderJobs()}
          {activeTab === 'applications' && (
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <div className="text-6xl mb-4">📝</div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">إدارة التقديمات</h3>
              <p className="text-gray-600">قريباً - ستتمكن من إدارة جميع التقديمات هنا</p>
            </div>
          )}
          {activeTab === 'candidates' && (
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <div className="text-6xl mb-4">👥</div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">قاعدة المرشحين</h3>
              <p className="text-gray-600">قريباً - ستتمكن من البحث في قاعدة المرشحين</p>
            </div>
          )}
          {activeTab === 'analytics' && (
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <div className="text-6xl mb-4">📈</div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">التحليلات والتقارير</h3>
              <p className="text-gray-600">قريباً - تحليلات مفصلة لأداء إعلاناتك</p>
            </div>
          )}
          {activeTab === 'settings' && (
            <div className="bg-white rounded-lg shadow-sm p-8 text-center">
              <div className="text-6xl mb-4">⚙️</div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">إعدادات الحساب</h3>
              <p className="text-gray-600">قريباً - إدارة إعدادات حساب الشركة</p>
            </div>
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
}
