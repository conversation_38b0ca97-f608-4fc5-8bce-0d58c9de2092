'use client';

import { useState } from 'react';

interface RealEstateFilters {
  keyword?: string;
  propertyType?: string;
  transactionType?: string;
  governorate?: string;
  city?: string;
  area?: string;
  priceMin?: number;
  priceMax?: number;
  currency?: 'SYP' | 'USD' | 'EUR';
  areaMin?: number;
  areaMax?: number;
  rooms?: string;
  bathrooms?: string;
  floor?: string;
  condition?: string;
  features?: string[];
  sortBy?: string;
}

interface Props {
  onSearch: (filters: RealEstateFilters) => void;
}

export default function RealEstateAdvancedSearch({ onSearch }: Props) {
  const [filters, setFilters] = useState<RealEstateFilters>({
    currency: 'SYP',
    sortBy: 'newest'
  });

  const propertyTypes = [
    { id: 'apartment', name: 'شقة' },
    { id: 'villa', name: 'فيلا' },
    { id: 'house', name: 'منزل' },
    { id: 'office', name: 'مكتب' },
    { id: 'shop', name: 'محل تجاري' },
    { id: 'warehouse', name: 'مستودع' },
    { id: 'land', name: 'أرض' },
    { id: 'farm', name: 'مزرعة' },
    { id: 'chalet', name: 'شاليه' }
  ];

  const transactionTypes = [
    { id: 'sale', name: 'للبيع' },
    { id: 'rent', name: 'للإيجار الشهري' },
    { id: 'rent_daily', name: 'للإيجار اليومي' },
    { id: 'rent_weekly', name: 'للإيجار الأسبوعي' },
    { id: 'rent_seasonal', name: 'للإيجار الموسمي' },
    { id: 'exchange', name: 'للمقايضة' }
  ];

  const governorates = [
    { id: 'damascus', name: 'دمشق' },
    { id: 'aleppo', name: 'حلب' },
    { id: 'homs', name: 'حمص' },
    { id: 'hama', name: 'حماة' },
    { id: 'lattakia', name: 'اللاذقية' },
    { id: 'tartous', name: 'طرطوس' },
    { id: 'daraa', name: 'درعا' },
    { id: 'sweida', name: 'السويداء' },
    { id: 'quneitra', name: 'القنيطرة' },
    { id: 'idlib', name: 'إدلب' },
    { id: 'raqqa', name: 'الرقة' },
    { id: 'deir_ezzor', name: 'دير الزور' },
    { id: 'hasakah', name: 'الحسكة' },
    { id: 'rif_damascus', name: 'ريف دمشق' }
  ];

  const roomOptions = [
    { id: '1', name: 'غرفة واحدة' },
    { id: '2', name: 'غرفتان' },
    { id: '3', name: '3 غرف' },
    { id: '4', name: '4 غرف' },
    { id: '5', name: '5 غرف' },
    { id: '6+', name: '6 غرف أو أكثر' }
  ];

  const bathroomOptions = [
    { id: '1', name: 'حمام واحد' },
    { id: '2', name: 'حمامان' },
    { id: '3', name: '3 حمامات' },
    { id: '4+', name: '4 حمامات أو أكثر' }
  ];

  const floorOptions = [
    { id: 'ground', name: 'الطابق الأرضي' },
    { id: '1', name: 'الطابق الأول' },
    { id: '2', name: 'الطابق الثاني' },
    { id: '3', name: 'الطابق الثالث' },
    { id: '4', name: 'الطابق الرابع' },
    { id: '5', name: 'الطابق الخامس' },
    { id: '6+', name: 'الطابق السادس أو أعلى' },
    { id: 'roof', name: 'السطح' }
  ];

  const conditionOptions = [
    { id: 'new', name: 'جديد' },
    { id: 'renovated', name: 'معفش' },
    { id: 'used', name: 'مستعمل' },
    { id: 'refurbished', name: 'مجدد' },
    { id: 'excellent', name: 'ممتاز' },
    { id: 'very_good', name: 'جيد جداً' },
    { id: 'good', name: 'جيد' },
    { id: 'needs_renovation', name: 'يحتاج تجديد' }
  ];



  const currencies = [
    { id: 'SYP', name: 'ليرة سورية', symbol: 'ل.س' },
    { id: 'USD', name: 'دولار أمريكي', symbol: '$' },
    { id: 'EUR', name: 'يورو', symbol: '€' }
  ];

  const sortOptions = [
    { id: 'newest', name: 'الأحدث أولاً' },
    { id: 'oldest', name: 'الأقدم أولاً' },
    { id: 'price_low', name: 'السعر: من الأقل للأعلى' },
    { id: 'price_high', name: 'السعر: من الأعلى للأقل' },
    { id: 'area_large', name: 'المساحة: من الأكبر للأصغر' },
    { id: 'area_small', name: 'المساحة: من الأصغر للأكبر' },
    { id: 'most_viewed', name: 'الأكثر مشاهدة' }
  ];

  // فئات الأسعار بالليرة السورية للعقارات
  const priceRangesSYP = [
    { id: 'under-10m', name: 'أقل من 10 مليون', min: 0, max: 10000000 },
    { id: '10m-25m', name: '10 - 25 مليون', min: 10000000, max: 25000000 },
    { id: '25m-50m', name: '25 - 50 مليون', min: 25000000, max: 50000000 },
    { id: '50m-100m', name: '50 - 100 مليون', min: 50000000, max: 100000000 },
    { id: '100m-200m', name: '100 - 200 مليون', min: 100000000, max: 200000000 },
    { id: '200m-500m', name: '200 - 500 مليون', min: 200000000, max: 500000000 },
    { id: 'over-500m', name: 'أكثر من 500 مليون', min: 500000000, max: null }
  ];

  // فئات الأسعار بالدولار للعقارات
  const priceRangesUSD = [
    { id: 'under-25k', name: 'أقل من 25,000 دولار', min: 0, max: 25000 },
    { id: '25k-50k', name: '25,000 - 50,000 دولار', min: 25000, max: 50000 },
    { id: '50k-100k', name: '50,000 - 100,000 دولار', min: 50000, max: 100000 },
    { id: '100k-200k', name: '100,000 - 200,000 دولار', min: 100000, max: 200000 },
    { id: '200k-500k', name: '200,000 - 500,000 دولار', min: 200000, max: 500000 },
    { id: 'over-500k', name: 'أكثر من 500,000 دولار', min: 500000, max: null }
  ];

  // فئات الأسعار باليورو للعقارات
  const priceRangesEUR = [
    { id: 'under-20k', name: 'أقل من 20,000 يورو', min: 0, max: 20000 },
    { id: '20k-40k', name: '20,000 - 40,000 يورو', min: 20000, max: 40000 },
    { id: '40k-80k', name: '40,000 - 80,000 يورو', min: 40000, max: 80000 },
    { id: '80k-150k', name: '80,000 - 150,000 يورو', min: 80000, max: 150000 },
    { id: '150k-300k', name: '150,000 - 300,000 يورو', min: 150000, max: 300000 },
    { id: 'over-300k', name: 'أكثر من 300,000 يورو', min: 300000, max: null }
  ];

  const getCurrentPriceRanges = () => {
    switch (filters.currency) {
      case 'USD':
        return priceRangesUSD;
      case 'EUR':
        return priceRangesEUR;
      default:
        return priceRangesSYP;
    }
  };

  const handleFilterChange = (key: keyof RealEstateFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };



  const handlePriceRangeSelect = (range: any) => {
    setFilters(prev => ({
      ...prev,
      priceMin: range.min,
      priceMax: range.max
    }));
  };

  const clearFilters = () => {
    setFilters({
      currency: 'SYP',
      sortBy: 'newest'
    });
  };

  const handleSearch = () => {
    onSearch(filters);
  };

  return (
    <div className="space-y-6">
      {/* عنوان البحث */}
      <div className="text-center mb-6">
        <div className="flex items-center justify-center gap-3 mb-2">
          <span className="text-4xl">🏠</span>
          <h2 className="text-2xl font-bold text-gray-800">البحث المتقدم للعقارات</h2>
        </div>
        <p className="text-gray-600">ابحث عن العقار المثالي بمواصفات دقيقة</p>
      </div>

      {/* البحث بالكلمات المفتاحية */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          البحث بالكلمات المفتاحية
        </label>
        <input
          type="text"
          value={filters.keyword || ''}
          onChange={(e) => handleFilterChange('keyword', e.target.value)}
          placeholder="ابحث عن عقار... (مثال: شقة في دمشق، فيلا مع حديقة)"
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 min-h-[48px]"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* نوع العقار */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            نوع العقار
          </label>
          <select
            value={filters.propertyType || ''}
            onChange={(e) => handleFilterChange('propertyType', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 min-h-[48px]"
          >
            <option value="">جميع الأنواع</option>
            {propertyTypes.map(type => (
              <option key={type.id} value={type.id}>{type.name}</option>
            ))}
          </select>
        </div>

        {/* نوع المعاملة */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            نوع المعاملة
          </label>
          <select
            value={filters.transactionType || ''}
            onChange={(e) => handleFilterChange('transactionType', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 min-h-[48px]"
          >
            <option value="">جميع المعاملات</option>
            {transactionTypes.map(type => (
              <option key={type.id} value={type.id}>{type.name}</option>
            ))}
          </select>
        </div>

        {/* المحافظة */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            المحافظة
          </label>
          <select
            value={filters.governorate || ''}
            onChange={(e) => handleFilterChange('governorate', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 min-h-[48px]"
          >
            <option value="">جميع المحافظات</option>
            {governorates.map(gov => (
              <option key={gov.id} value={gov.id}>{gov.name}</option>
            ))}
          </select>
        </div>

        {/* العملة */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            العملة
          </label>
          <select
            value={filters.currency || 'SYP'}
            onChange={(e) => handleFilterChange('currency', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 min-h-[48px]"
          >
            {currencies.map(currency => (
              <option key={currency.id} value={currency.id}>
                {currency.name} ({currency.symbol})
              </option>
            ))}
          </select>
        </div>

        {/* السعر الأدنى */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            السعر الأدنى
          </label>
          <input
            type="number"
            value={filters.priceMin || ''}
            onChange={(e) => handleFilterChange('priceMin', e.target.value ? parseInt(e.target.value) : undefined)}
            placeholder="0"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 min-h-[48px]"
          />
        </div>

        {/* السعر الأعلى */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            السعر الأعلى
          </label>
          <input
            type="number"
            value={filters.priceMax || ''}
            onChange={(e) => handleFilterChange('priceMax', e.target.value ? parseInt(e.target.value) : undefined)}
            placeholder="بلا حدود"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 min-h-[48px]"
          />
        </div>
      </div>

      {/* فئات الأسعار السريعة */}
      <div className="mt-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          فئات الأسعار السريعة ({
            filters.currency === 'USD' ? 'دولار' :
            filters.currency === 'EUR' ? 'يورو' :
            'ليرة سورية'
          })
        </label>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
          {getCurrentPriceRanges().map(range => (
            <button
              key={range.id}
              onClick={() => handlePriceRangeSelect(range)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors border ${
                filters.priceMin === range.min && filters.priceMax === range.max
                  ? 'bg-primary-600 text-white border-primary-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
              }`}
            >
              {range.name}
            </button>
          ))}
        </div>
      </div>

      {/* المساحة */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* المساحة الأدنى */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            المساحة الأدنى (م²)
          </label>
          <input
            type="number"
            value={filters.areaMin || ''}
            onChange={(e) => handleFilterChange('areaMin', e.target.value ? parseInt(e.target.value) : undefined)}
            placeholder="0"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 min-h-[48px]"
          />
        </div>

        {/* المساحة الأعلى */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            المساحة الأعلى (م²)
          </label>
          <input
            type="number"
            value={filters.areaMax || ''}
            onChange={(e) => handleFilterChange('areaMax', e.target.value ? parseInt(e.target.value) : undefined)}
            placeholder="بلا حدود"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 min-h-[48px]"
          />
        </div>
      </div>

      {/* عدد الغرف والحمامات */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* عدد الغرف */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            عدد الغرف
          </label>
          <select
            value={filters.rooms || ''}
            onChange={(e) => handleFilterChange('rooms', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 min-h-[48px]"
          >
            <option value="">أي عدد</option>
            {roomOptions.map(room => (
              <option key={room.id} value={room.id}>{room.name}</option>
            ))}
          </select>
        </div>

        {/* عدد الحمامات */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            عدد الحمامات
          </label>
          <select
            value={filters.bathrooms || ''}
            onChange={(e) => handleFilterChange('bathrooms', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 min-h-[48px]"
          >
            <option value="">أي عدد</option>
            {bathroomOptions.map(bathroom => (
              <option key={bathroom.id} value={bathroom.id}>{bathroom.name}</option>
            ))}
          </select>
        </div>
      </div>

      {/* الطابق والحالة */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* الطابق */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            الطابق
          </label>
          <select
            value={filters.floor || ''}
            onChange={(e) => handleFilterChange('floor', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 min-h-[48px]"
          >
            <option value="">أي طابق</option>
            {floorOptions.map(floor => (
              <option key={floor.id} value={floor.id}>{floor.name}</option>
            ))}
          </select>
        </div>

        {/* حالة العقار */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            حالة العقار
          </label>
          <select
            value={filters.condition || ''}
            onChange={(e) => handleFilterChange('condition', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 min-h-[48px]"
          >
            <option value="">جميع الحالات</option>
            {conditionOptions.map(condition => (
              <option key={condition.id} value={condition.id}>{condition.name}</option>
            ))}
          </select>
        </div>
      </div>



      {/* ترتيب النتائج */}
      <div className="mt-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          ترتيب النتائج
        </label>
        <div className="flex flex-wrap gap-2">
          {sortOptions.map(option => (
            <button
              key={option.id}
              onClick={() => handleFilterChange('sortBy', option.id)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                filters.sortBy === option.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {option.name}
            </button>
          ))}
        </div>
      </div>

      {/* أزرار البحث */}
      <div className="flex gap-4 mt-8">
        <button
          onClick={handleSearch}
          className="flex-1 bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 transition-colors font-semibold"
        >
          🏠 بحث في العقارات
        </button>
        <button
          onClick={clearFilters}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          مسح الكل
        </button>
      </div>

      {/* عدد النتائج المتوقعة */}
      <div className="mt-4 text-center text-sm text-gray-600">
        النتائج المتوقعة: <span className="font-semibold text-primary-600">456 عقار</span>
      </div>
    </div>
  );
}
