'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Resume } from '@/lib/jobs';

// بيانات تجريبية للسيرة الذاتية
const sampleResume: Resume = {
  id: '1',
  userId: 'user1',
  personalInfo: {
    firstName: 'أحمد',
    lastName: 'محمد',
    title: 'مطور ويب متقدم',
    summary: 'مطور ويب متخصص في React.js و Node.js مع خبرة 5 سنوات في تطوير التطبيقات الحديثة والمتجاوبة. شغوف بالتقنيات الحديثة وتطوير حلول مبتكرة للمشاكل المعقدة.',
    nationality: 'سوري',
    maritalStatus: 'متزوج'
  },
  contactInfo: {
    phone: '+*********** 401',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/ahmed-mohamed',
    github: 'https://github.com/ahmed-mohamed',
    portfolio: 'https://ahmed-portfolio.com',
    address: 'شارع الثورة، المزة',
    city: 'دمشق'
  },
  experiences: [
    {
      id: '1',
      jobTitle: 'مطور ويب أول',
      company: 'شركة التقنيات المتقدمة',
      location: 'دمشق',
      startDate: '2021-03',
      current: true,
      description: 'تطوير وصيانة تطبيقات الويب باستخدام React.js و Node.js. قيادة فريق من 3 مطورين وتحسين أداء التطبيقات بنسبة 40%. تطوير APIs متقدمة وتحسين تجربة المستخدم.',
      achievements: ['تحسين الأداء بنسبة 40%', 'قيادة فريق من 3 مطورين', 'تطوير 5 مشاريع كبيرة']
    },
    {
      id: '2',
      jobTitle: 'مطور ويب',
      company: 'شركة الحلول الرقمية',
      location: 'دمشق',
      startDate: '2019-06',
      endDate: '2021-02',
      current: false,
      description: 'تطوير واجهات المستخدم التفاعلية وتطبيقات الويب المتجاوبة. العمل مع فرق متعددة التخصصات لتطوير حلول مبتكرة.',
      achievements: ['تطوير 10+ مواقع ويب', 'تحسين تجربة المستخدم']
    },
    {
      id: '3',
      jobTitle: 'مطور ويب مبتدئ',
      company: 'استوديو الإبداع الرقمي',
      location: 'دمشق',
      startDate: '2018-09',
      endDate: '2019-05',
      current: false,
      description: 'تطوير مواقع ويب بسيطة وتعلم أساسيات البرمجة والتطوير. المشاركة في مشاريع صغيرة ومتوسطة.',
      achievements: ['تطوير 5 مواقع ويب', 'تعلم React.js']
    }
  ],
  education: [
    {
      id: '1',
      degree: 'بكالوريوس',
      institution: 'جامعة دمشق',
      field: 'هندسة الحاسوب',
      location: 'دمشق',
      startDate: '2015-09',
      endDate: '2019-06',
      current: false,
      gpa: '3.8/4.0',
      description: 'تخصص في هندسة البرمجيات وقواعد البيانات. مشروع التخرج: تطبيق إدارة المكتبات باستخدام React و Node.js'
    }
  ],
  skills: [
    { id: '1', name: 'React.js', level: 'خبير', category: 'تقني', verified: true },
    { id: '2', name: 'Node.js', level: 'متقدم', category: 'تقني', verified: true },
    { id: '3', name: 'TypeScript', level: 'متقدم', category: 'تقني', verified: false },
    { id: '4', name: 'MongoDB', level: 'متقدم', category: 'تقني', verified: true },
    { id: '5', name: 'AWS', level: 'متوسط', category: 'تقني', verified: false },
    { id: '6', name: 'إدارة المشاريع', level: 'متوسط', category: 'إداري', verified: false },
    { id: '7', name: 'Git', level: 'متقدم', category: 'تقني', verified: true },
    { id: '8', name: 'Docker', level: 'متوسط', category: 'تقني', verified: false }
  ],
  languages: [
    { id: '1', name: 'العربية', level: 'أصلي' },
    { id: '2', name: 'الإنجليزية', level: 'متقدم', certification: 'IELTS 7.0' },
    { id: '3', name: 'الفرنسية', level: 'متوسط' }
  ],
  courses: [
    {
      id: '1',
      name: 'React Advanced Patterns',
      provider: 'Udemy',
      completionDate: '2023-08',
      certificateUrl: 'https://certificate-url.com',
      skills: ['React.js', 'Advanced Patterns']
    },
    {
      id: '2',
      name: 'AWS Cloud Practitioner',
      provider: 'Amazon',
      completionDate: '2023-05',
      skills: ['AWS', 'Cloud Computing']
    },
    {
      id: '3',
      name: 'Node.js Masterclass',
      provider: 'Udemy',
      completionDate: '2022-12',
      skills: ['Node.js', 'Express.js', 'MongoDB']
    }
  ],
  references: [
    {
      name: 'سارة أحمد',
      position: 'مدير التطوير',
      company: 'شركة التقنيات المتقدمة',
      phone: '+*********** 222',
      email: '<EMAIL>'
    },
    {
      name: 'محمد علي',
      position: 'كبير المطورين',
      company: 'شركة الحلول الرقمية',
      phone: '+*********** 444',
      email: '<EMAIL>'
    }
  ],
  createdAt: '2024-01-15',
  updatedAt: '2024-02-15',
  isPublic: true,
  views: 156
};

export default function ResumePreviewPage() {
  const params = useParams();
  const [resume] = useState<Resume>(sampleResume);
  const [printMode, setPrintMode] = useState(false);

  const handlePrint = () => {
    setPrintMode(true);
    setTimeout(() => {
      window.print();
      setPrintMode(false);
    }, 100);
  };

  const handleDownloadPDF = () => {
    // في التطبيق الحقيقي، سيتم تحويل الصفحة إلى PDF
    alert('ميزة تحميل PDF قيد التطوير');
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: `سيرة ${resume.personalInfo.firstName} ${resume.personalInfo.lastName} الذاتية`,
        text: `تصفح سيرة ${resume.personalInfo.firstName} الذاتية - ${resume.personalInfo.title}`,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      alert('تم نسخ الرابط إلى الحافظة');
    }
  };

  return (
    <div className={`min-h-screen ${printMode ? 'bg-white' : 'bg-gray-50'}`}>
      {/* Header - Hidden in print */}
      {!printMode && (
        <div className="bg-white shadow-sm border-b border-gray-200 print:hidden">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Link
                  href="/resume/my-resume"
                  className="text-gray-600 hover:text-gray-800 transition-colors"
                >
                  ← العودة
                </Link>
                <div>
                  <h1 className="text-xl font-bold text-gray-800">
                    معاينة السيرة الذاتية
                  </h1>
                  <p className="text-sm text-gray-600">
                    {resume.personalInfo.firstName} {resume.personalInfo.lastName}
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <button
                  onClick={handleShare}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors flex items-center gap-2"
                >
                  📤 مشاركة
                </button>
                <button
                  onClick={handleDownloadPDF}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                >
                  📄 تحميل PDF
                </button>
                <button
                  onClick={handlePrint}
                  className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center gap-2"
                >
                  🖨️ طباعة
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Resume Content */}
      <div className="container mx-auto px-4 py-8 print:p-0">
        <div className="max-w-4xl mx-auto bg-white shadow-lg print:shadow-none">
          {/* Header */}
          <div className="bg-primary-600 text-white p-8 print:bg-gray-800">
            <div className="text-center">
              <h1 className="text-4xl font-bold mb-2">
                {resume.personalInfo.firstName} {resume.personalInfo.lastName}
              </h1>
              <h2 className="text-xl opacity-90 mb-6">{resume.personalInfo.title}</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center justify-center gap-2">
                  <span>📧</span>
                  <span>{resume.contactInfo.email}</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span>📞</span>
                  <span>{resume.contactInfo.phone}</span>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <span>📍</span>
                  <span>{resume.contactInfo.city}</span>
                </div>
              </div>

              {/* Social Links */}
              <div className="flex justify-center gap-6 mt-4 text-sm">
                {resume.contactInfo.linkedin && (
                  <a href={resume.contactInfo.linkedin} target="_blank" rel="noopener noreferrer" className="hover:underline">
                    🔗 LinkedIn
                  </a>
                )}
                {resume.contactInfo.github && (
                  <a href={resume.contactInfo.github} target="_blank" rel="noopener noreferrer" className="hover:underline">
                    💻 GitHub
                  </a>
                )}
                {resume.contactInfo.portfolio && (
                  <a href={resume.contactInfo.portfolio} target="_blank" rel="noopener noreferrer" className="hover:underline">
                    🌐 Portfolio
                  </a>
                )}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-8">
            {/* Summary */}
            <section className="mb-8">
              <h3 className="text-2xl font-bold text-primary-600 mb-4 border-b-2 border-primary-200 pb-2">
                نبذة شخصية
              </h3>
              <p className="text-gray-700 leading-relaxed text-lg">
                {resume.personalInfo.summary}
              </p>
            </section>

            {/* Experience */}
            <section className="mb-8">
              <h3 className="text-2xl font-bold text-primary-600 mb-4 border-b-2 border-primary-200 pb-2">
                الخبرات العملية
              </h3>
              <div className="space-y-6">
                {resume.experiences.map((exp, index) => (
                  <div key={exp.id} className="border-r-4 border-primary-200 pr-6">
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h4 className="text-xl font-bold text-gray-800">{exp.jobTitle}</h4>
                        <p className="text-primary-600 font-semibold text-lg">{exp.company}</p>
                        <p className="text-gray-600">{exp.location}</p>
                      </div>
                      <div className="text-right">
                        <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium">
                          {exp.startDate} - {exp.current ? 'الآن' : exp.endDate}
                        </span>
                        {exp.current && (
                          <div className="text-green-600 text-sm mt-1">منصب حالي</div>
                        )}
                      </div>
                    </div>
                    <p className="text-gray-700 mb-3 leading-relaxed">{exp.description}</p>
                    {exp.achievements && exp.achievements.length > 0 && (
                      <div>
                        <h5 className="font-semibold text-gray-800 mb-2">الإنجازات الرئيسية:</h5>
                        <ul className="list-disc list-inside text-gray-700 space-y-1">
                          {exp.achievements.map((achievement, i) => (
                            <li key={i}>{achievement}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </section>

            {/* Education */}
            <section className="mb-8">
              <h3 className="text-2xl font-bold text-primary-600 mb-4 border-b-2 border-primary-200 pb-2">
                التعليم والمؤهلات
              </h3>
              <div className="space-y-4">
                {resume.education.map((edu, index) => (
                  <div key={edu.id} className="border-r-4 border-green-200 pr-6">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="text-xl font-bold text-gray-800">
                          {edu.degree} في {edu.field}
                        </h4>
                        <p className="text-green-600 font-semibold">{edu.institution}</p>
                        <p className="text-gray-600">{edu.location}</p>
                      </div>
                      <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                        {edu.startDate} - {edu.endDate}
                      </span>
                    </div>
                    {edu.gpa && (
                      <p className="text-gray-700 mb-2">المعدل: <span className="font-semibold">{edu.gpa}</span></p>
                    )}
                    {edu.description && (
                      <p className="text-gray-700">{edu.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </section>

            {/* Skills */}
            <section className="mb-8">
              <h3 className="text-2xl font-bold text-primary-600 mb-4 border-b-2 border-primary-200 pb-2">
                المهارات التقنية
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {resume.skills.map(skill => (
                  <div key={skill.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className="font-semibold text-gray-800">{skill.name}</span>
                      {skill.verified && (
                        <span className="text-green-500 text-sm">✓ موثق</span>
                      )}
                    </div>
                    <span className={`text-xs px-3 py-1 rounded-full font-medium ${
                      skill.level === 'خبير' ? 'bg-red-100 text-red-600' :
                      skill.level === 'متقدم' ? 'bg-orange-100 text-orange-600' :
                      skill.level === 'متوسط' ? 'bg-yellow-100 text-yellow-600' :
                      'bg-green-100 text-green-600'
                    }`}>
                      {skill.level}
                    </span>
                  </div>
                ))}
              </div>
            </section>

            {/* Languages */}
            <section className="mb-8">
              <h3 className="text-2xl font-bold text-primary-600 mb-4 border-b-2 border-primary-200 pb-2">
                اللغات
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {resume.languages.map(lang => (
                  <div key={lang.id} className="p-4 bg-blue-50 rounded-lg">
                    <div className="font-semibold text-gray-800 mb-1">{lang.name}</div>
                    <div className="text-blue-600 font-medium">{lang.level}</div>
                    {lang.certification && (
                      <div className="text-sm text-gray-600 mt-1">{lang.certification}</div>
                    )}
                  </div>
                ))}
              </div>
            </section>

            {/* Courses */}
            <section className="mb-8">
              <h3 className="text-2xl font-bold text-primary-600 mb-4 border-b-2 border-primary-200 pb-2">
                الدورات والشهادات
              </h3>
              <div className="space-y-4">
                {resume.courses.map(course => (
                  <div key={course.id} className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h4 className="font-semibold text-gray-800">{course.name}</h4>
                        <p className="text-gray-600">{course.provider}</p>
                      </div>
                      <span className="text-sm text-gray-500">{course.completionDate}</span>
                    </div>
                    {course.skills && course.skills.length > 0 && (
                      <div className="flex flex-wrap gap-2 mt-2">
                        {course.skills.map((skill, index) => (
                          <span key={index} className="bg-primary-100 text-primary-700 px-2 py-1 rounded text-xs">
                            {skill}
                          </span>
                        ))}
                      </div>
                    )}
                    {course.certificateUrl && (
                      <a
                        href={course.certificateUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary-600 text-sm hover:underline mt-2 inline-block"
                      >
                        عرض الشهادة →
                      </a>
                    )}
                  </div>
                ))}
              </div>
            </section>

            {/* References */}
            {resume.references && resume.references.length > 0 && (
              <section>
                <h3 className="text-2xl font-bold text-primary-600 mb-4 border-b-2 border-primary-200 pb-2">
                  المراجع
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {resume.references.map((ref, index) => (
                    <div key={index} className="p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-semibold text-gray-800">{ref.name}</h4>
                      <p className="text-gray-600">{ref.position}</p>
                      <p className="text-gray-600">{ref.company}</p>
                      <div className="mt-2 text-sm text-gray-600">
                        <div>📞 {ref.phone}</div>
                        <div>📧 {ref.email}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </section>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
