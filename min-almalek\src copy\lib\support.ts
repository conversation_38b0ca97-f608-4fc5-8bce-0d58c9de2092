// نظام إدارة التذاكر والدعم الفني
export interface SupportTicket {
  id: string;
  userId: number;
  subject: string;
  description: string;
  category: 'technical' | 'billing' | 'account' | 'ads' | 'general' | 'complaint';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'open' | 'in_progress' | 'waiting_response' | 'resolved' | 'closed';
  assignedTo?: number;
  attachments: string[];
  tags: string[];
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  closedAt?: string;
}

export interface TicketMessage {
  id: string;
  ticketId: string;
  userId: number;
  message: string;
  isStaff: boolean;
  attachments: string[];
  createdAt: string;
}

export interface FAQ {
  id: number;
  question: string;
  answer: string;
  category: string;
  tags: string[];
  helpful: number;
  notHelpful: number;
  views: number;
  featured: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface KnowledgeBase {
  id: number;
  title: string;
  content: string;
  category: string;
  tags: string[];
  views: number;
  helpful: number;
  notHelpful: number;
  featured: boolean;
  createdAt: string;
  updatedAt: string;
}

// بيانات تجريبية للتذاكر
const sampleTickets: SupportTicket[] = [
  {
    id: 'ticket_1',
    userId: 1,
    subject: 'مشكلة في رفع الصور',
    description: 'لا أستطيع رفع صور لإعلاني الجديد، تظهر رسالة خطأ',
    category: 'technical',
    priority: 'medium',
    status: 'in_progress',
    assignedTo: 100,
    attachments: [],
    tags: ['upload', 'images'],
    createdAt: '2024-01-20T10:30:00Z',
    updatedAt: '2024-01-20T14:15:00Z'
  },
  {
    id: 'ticket_2',
    userId: 2,
    subject: 'استفسار عن خطط الاشتراك',
    description: 'أريد معرفة الفرق بين الخطة المميزة والذهبية',
    category: 'billing',
    priority: 'low',
    status: 'resolved',
    assignedTo: 101,
    attachments: [],
    tags: ['subscription', 'pricing'],
    createdAt: '2024-01-19T16:45:00Z',
    updatedAt: '2024-01-20T09:30:00Z',
    resolvedAt: '2024-01-20T09:30:00Z'
  }
];

const sampleMessages: TicketMessage[] = [
  {
    id: 'msg_1',
    ticketId: 'ticket_1',
    userId: 1,
    message: 'مرحباً، أواجه مشكلة في رفع الصور. عندما أحاول رفع صورة تظهر رسالة "فشل في الرفع"',
    isStaff: false,
    attachments: [],
    createdAt: '2024-01-20T10:30:00Z'
  },
  {
    id: 'msg_2',
    ticketId: 'ticket_1',
    userId: 100,
    message: 'مرحباً، شكراً لتواصلك معنا. يرجى التأكد من أن حجم الصورة أقل من 5 ميجابايت ونوعها JPG أو PNG',
    isStaff: true,
    attachments: [],
    createdAt: '2024-01-20T14:15:00Z'
  }
];

const sampleFAQs: FAQ[] = [
  {
    id: 1,
    question: 'كيف يمكنني إنشاء حساب جديد؟',
    answer: 'يمكنك إنشاء حساب جديد بالنقر على زر "إنشاء حساب" في أعلى الصفحة، ثم ملء البيانات المطلوبة.',
    category: 'account',
    tags: ['registration', 'account'],
    helpful: 25,
    notHelpful: 2,
    views: 150,
    featured: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: 2,
    question: 'ما هي أنواع الاشتراكات المتاحة؟',
    answer: 'نوفر باقات للأفراد: المجانية (3 إعلانات)، الأساسية (5 إعلانات - 25,000 ل.س)، المميزة (15 إعلان - 50,000 ل.س)، والأعمال (30 إعلان - 100,000 ل.س). وباقات للشركات: خطة البداية (15 إعلان - 500,000 ل.س)، باقة المكاتب العقارية (30 إعلان عقاري - 700,000 ل.س)، والخطة المهنية (50 إعلان - 1,000,000 ل.س).',
    category: 'billing',
    tags: ['subscription', 'pricing'],
    helpful: 18,
    notHelpful: 1,
    views: 89,
    featured: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-10T15:30:00Z'
  }
];

export class SupportService {
  // إنشاء تذكرة جديدة
  static async createTicket(
    userId: number,
    subject: string,
    description: string,
    category: SupportTicket['category'],
    priority: SupportTicket['priority'] = 'medium',
    attachments: string[] = []
  ): Promise<{ success: boolean; data?: SupportTicket; error?: string }> {
    try {
      if (!subject.trim() || !description.trim()) {
        return { success: false, error: 'الموضوع والوصف مطلوبان' };
      }

      const ticket: SupportTicket = {
        id: `ticket_${Date.now()}`,
        userId,
        subject: subject.trim(),
        description: description.trim(),
        category,
        priority,
        status: 'open',
        attachments,
        tags: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      sampleTickets.push(ticket);

      // إضافة الرسالة الأولى
      const initialMessage: TicketMessage = {
        id: `msg_${Date.now()}`,
        ticketId: ticket.id,
        userId,
        message: description,
        isStaff: false,
        attachments,
        createdAt: ticket.createdAt
      };

      sampleMessages.push(initialMessage);

      return { success: true, data: ticket };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في إنشاء التذكرة' };
    }
  }

  // الحصول على تذاكر المستخدم
  static getUserTickets(userId: number): SupportTicket[] {
    return sampleTickets
      .filter(ticket => ticket.userId === userId)
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
  }

  // الحصول على تذكرة بالمعرف
  static getTicketById(ticketId: string, userId?: number): SupportTicket | null {
    const ticket = sampleTickets.find(t => t.id === ticketId);

    if (!ticket) return null;

    // التحقق من الصلاحية
    if (userId && ticket.userId !== userId) return null;

    return ticket;
  }

  // إضافة رسالة للتذكرة
  static async addMessage(
    ticketId: string,
    userId: number,
    message: string,
    isStaff: boolean = false,
    attachments: string[] = []
  ): Promise<{ success: boolean; data?: TicketMessage; error?: string }> {
    try {
      const ticket = this.getTicketById(ticketId, isStaff ? undefined : userId);

      if (!ticket) {
        return { success: false, error: 'التذكرة غير موجودة' };
      }

      if (ticket.status === 'closed') {
        return { success: false, error: 'لا يمكن إضافة رسائل لتذكرة مغلقة' };
      }

      const newMessage: TicketMessage = {
        id: `msg_${Date.now()}`,
        ticketId,
        userId,
        message: message.trim(),
        isStaff,
        attachments,
        createdAt: new Date().toISOString()
      };

      sampleMessages.push(newMessage);

      // تحديث حالة التذكرة
      ticket.updatedAt = newMessage.createdAt;
      if (isStaff) {
        ticket.status = 'waiting_response';
      } else if (ticket.status === 'waiting_response') {
        ticket.status = 'in_progress';
      }

      return { success: true, data: newMessage };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في إضافة الرسالة' };
    }
  }

  // الحصول على رسائل التذكرة
  static getTicketMessages(ticketId: string): TicketMessage[] {
    return sampleMessages
      .filter(msg => msg.ticketId === ticketId)
      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
  }

  // تحديث حالة التذكرة
  static async updateTicketStatus(
    ticketId: string,
    status: SupportTicket['status'],
    userId?: number
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const ticket = sampleTickets.find(t => t.id === ticketId);

      if (!ticket) {
        return { success: false, error: 'التذكرة غير موجودة' };
      }

      // التحقق من الصلاحية
      if (userId && ticket.userId !== userId && status !== 'closed') {
        return { success: false, error: 'غير مصرح بتحديث هذه التذكرة' };
      }

      ticket.status = status;
      ticket.updatedAt = new Date().toISOString();

      if (status === 'resolved') {
        ticket.resolvedAt = ticket.updatedAt;
      } else if (status === 'closed') {
        ticket.closedAt = ticket.updatedAt;
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في تحديث حالة التذكرة' };
    }
  }

  // تعيين التذكرة لموظف
  static async assignTicket(
    ticketId: string,
    staffId: number
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const ticket = sampleTickets.find(t => t.id === ticketId);

      if (!ticket) {
        return { success: false, error: 'التذكرة غير موجودة' };
      }

      ticket.assignedTo = staffId;
      ticket.status = 'in_progress';
      ticket.updatedAt = new Date().toISOString();

      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في تعيين التذكرة' };
    }
  }

  // البحث في التذاكر
  static searchTickets(
    query: string,
    filters?: {
      category?: SupportTicket['category'];
      status?: SupportTicket['status'];
      priority?: SupportTicket['priority'];
      userId?: number;
    }
  ): SupportTicket[] {
    let results = sampleTickets.filter(ticket => {
      const searchText = `${ticket.subject} ${ticket.description}`.toLowerCase();
      return searchText.includes(query.toLowerCase());
    });

    if (filters) {
      if (filters.category) {
        results = results.filter(t => t.category === filters.category);
      }
      if (filters.status) {
        results = results.filter(t => t.status === filters.status);
      }
      if (filters.priority) {
        results = results.filter(t => t.priority === filters.priority);
      }
      if (filters.userId) {
        results = results.filter(t => t.userId === filters.userId);
      }
    }

    return results.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
  }

  // الحصول على الأسئلة الشائعة
  static getFAQs(category?: string): FAQ[] {
    let faqs = sampleFAQs;

    if (category) {
      faqs = faqs.filter(faq => faq.category === category);
    }

    return faqs.sort((a, b) => {
      // الأسئلة المميزة أولاً، ثم حسب المفيدة
      if (a.featured && !b.featured) return -1;
      if (!a.featured && b.featured) return 1;
      return b.helpful - a.helpful;
    });
  }

  // البحث في الأسئلة الشائعة
  static searchFAQs(query: string): FAQ[] {
    return sampleFAQs.filter(faq => {
      const searchText = `${faq.question} ${faq.answer}`.toLowerCase();
      return searchText.includes(query.toLowerCase());
    }).sort((a, b) => b.helpful - a.helpful);
  }

  // تقييم مفيدة السؤال الشائع
  static async rateFAQ(
    faqId: number,
    helpful: boolean
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const faq = sampleFAQs.find(f => f.id === faqId);

      if (!faq) {
        return { success: false, error: 'السؤال غير موجود' };
      }

      if (helpful) {
        faq.helpful += 1;
      } else {
        faq.notHelpful += 1;
      }

      faq.updatedAt = new Date().toISOString();
      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في تقييم السؤال' };
    }
  }

  // الحصول على إحصائيات الدعم
  static getSupportStats() {
    const totalTickets = sampleTickets.length;
    const openTickets = sampleTickets.filter(t => t.status === 'open').length;
    const inProgressTickets = sampleTickets.filter(t => t.status === 'in_progress').length;
    const resolvedTickets = sampleTickets.filter(t => t.status === 'resolved').length;
    const closedTickets = sampleTickets.filter(t => t.status === 'closed').length;

    const categoryStats = {
      technical: sampleTickets.filter(t => t.category === 'technical').length,
      billing: sampleTickets.filter(t => t.category === 'billing').length,
      account: sampleTickets.filter(t => t.category === 'account').length,
      ads: sampleTickets.filter(t => t.category === 'ads').length,
      general: sampleTickets.filter(t => t.category === 'general').length,
      complaint: sampleTickets.filter(t => t.category === 'complaint').length
    };

    const priorityStats = {
      low: sampleTickets.filter(t => t.priority === 'low').length,
      medium: sampleTickets.filter(t => t.priority === 'medium').length,
      high: sampleTickets.filter(t => t.priority === 'high').length,
      urgent: sampleTickets.filter(t => t.priority === 'urgent').length
    };

    // حساب متوسط وقت الحل
    const resolvedWithTime = sampleTickets.filter(t => t.resolvedAt);
    const avgResolutionTime = resolvedWithTime.length > 0
      ? resolvedWithTime.reduce((sum, ticket) => {
          const created = new Date(ticket.createdAt).getTime();
          const resolved = new Date(ticket.resolvedAt!).getTime();
          return sum + (resolved - created);
        }, 0) / resolvedWithTime.length / (1000 * 60 * 60) // بالساعات
      : 0;

    return {
      totalTickets,
      openTickets,
      inProgressTickets,
      resolvedTickets,
      closedTickets,
      categoryStats,
      priorityStats,
      avgResolutionTime: Math.round(avgResolutionTime * 10) / 10,
      resolutionRate: totalTickets > 0 ? (resolvedTickets / totalTickets) * 100 : 0
    };
  }
}
