'use client';

import Link from 'next/link';
import { INDIVIDUAL_PLANS, BUSINESS_PLANS, formatPrice } from '@/lib/pricing';
import { BadgeInfo } from '@/components/VerificationBadge';
import { INDIVIDUAL_BADGES, BUSINESS_BADGES } from '@/lib/verification';

const PricingPlans = () => {
  // استخدام الخطط الموحدة من pricing.ts
  const individualPlans = INDIVIDUAL_PLANS;
  const businessPlans = BUSINESS_PLANS;

  return (
    <div className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">خطط الاشتراك</h2>
          <p className="text-xl text-gray-600">اختر الخطة المناسبة لك واحصل على المزيد من المشاهدات</p>
        </div>

        {/* خطط الأفراد */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">خطط الأفراد</h3>
          <div className="flex flex-col md:flex-row gap-4 justify-center items-stretch">
            {individualPlans.map((plan) => (
              <div
                key={plan.id}
                className={`bg-white rounded-lg shadow-md overflow-hidden relative flex-1 max-w-sm ${
                  plan.popular ? 'ring-2 ring-blue-500 transform scale-105' : ''
                }`}
              >
                {plan.popular && (
                  <div className="absolute top-3 left-1/2 transform -translate-x-1/2 z-10">
                    <span className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-xl">
                      ⭐ الأكثر شعبية
                    </span>
                  </div>
                )}

                <div className={`p-6 ${plan.popular ? 'pt-16' : ''}`}>
                  <h4 className="text-xl font-bold text-gray-800 mb-3">{plan.name}</h4>

                  <div className="mb-4">
                    <div className="flex items-baseline">
                      <span className="text-3xl font-bold text-primary-600">
                        {formatPrice(plan.price, plan.currency)}
                      </span>
                    </div>
                    {plan.period && <div className="text-gray-600 text-sm">{plan.period}</div>}
                  </div>

                  <ul className="space-y-2 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-green-500 mr-2 mt-1">✓</span>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                    {plan.limitations?.map((limitation, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-red-500 mr-2 mt-1">✗</span>
                        <span className="text-gray-500">{limitation}</span>
                      </li>
                    ))}
                  </ul>

                  {plan.price === '0' ? (
                    <Link
                      href="/subscription"
                      className="w-full py-2 rounded-md font-medium bg-green-600 text-white hover:bg-green-700 transition-colors text-center block text-sm"
                    >
                      البدء مجاناً
                    </Link>
                  ) : (
                    <Link
                      href="/subscription"
                      className={`w-full py-2 rounded-md font-medium transition-colors text-center block text-sm ${
                        plan.popular
                          ? 'bg-primary-600 text-white hover:bg-primary-700'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      💎 اشترك الآن
                    </Link>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* خطط الشركات */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">خطط الشركات</h3>
          <div className="flex flex-col md:flex-row gap-4 justify-center items-stretch">
            {businessPlans.map((plan) => (
              <div
                key={plan.id}
                className={`bg-white rounded-lg shadow-md overflow-hidden relative flex-1 max-w-sm ${
                  plan.popular ? 'ring-2 ring-blue-500 transform scale-105' : ''
                }`}
              >
                {plan.popular && (
                  <div className="absolute top-3 left-1/2 transform -translate-x-1/2 z-10">
                    <span className="bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-xl">
                      ⭐ الأكثر شعبية
                    </span>
                  </div>
                )}

                <div className={`p-6 ${plan.popular ? 'pt-16' : ''}`}>
                  <h4 className="text-xl font-bold text-gray-800 mb-3">{plan.name}</h4>

                  <div className="mb-4">
                    <div className="flex items-baseline">
                      <span className="text-3xl font-bold text-primary-600">
                        {formatPrice(plan.price, plan.currency)}
                      </span>
                    </div>
                    {plan.period && <div className="text-gray-600 text-sm">{plan.period}</div>}
                  </div>

                  <ul className="space-y-2 mb-6">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <span className="text-green-500 mr-2 mt-1">✓</span>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  {plan.price === 'حسب الطلب' ? (
                    <button
                      className="w-full py-2 rounded-md font-medium bg-purple-600 text-white hover:bg-purple-700 transition-colors text-sm"
                      onClick={() => {
                        const message = `🔔 استفسار عن خطة مخصصة

🏢 طلب خطة مخصصة للشركات:
• نوع الطلب: خطة مخصصة
• نوع الحساب: شركة
• احتياجات خاصة: نعم

أريد الاستفسار عن خطة مخصصة تناسب احتياجات شركتنا. يرجى التواصل معي لمناقشة التفاصيل والأسعار.

شكراً لكم 🙏`;
                        window.open(`https://wa.me/963988652401?text=${encodeURIComponent(message)}`, '_blank');
                      }}
                    >
                      تواصل معنا
                    </button>
                  ) : (
                    <Link
                      href="/subscription"
                      className={`w-full py-2 rounded-md font-medium transition-colors text-center block text-sm ${
                        plan.popular
                          ? 'bg-primary-600 text-white hover:bg-primary-700'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      💎 اشترك الآن
                    </Link>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* شارات التوثيق */}
        <div className="mt-16">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-800 mb-4">🏆 شارات التوثيق والمصداقية</h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              احصل على شارات التوثيق التي تزيد من ثقة العملاء وتميزك عن المنافسين
            </p>
          </div>

          {/* شارات الأفراد */}
          <div className="mb-12">
            <h4 className="text-2xl font-bold text-gray-800 mb-6 text-center">🧑‍💼 شارات الأفراد</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {INDIVIDUAL_BADGES.map((badge) => (
                <BadgeInfo key={badge.id} badgeId={badge.id} />
              ))}
            </div>
          </div>

          {/* شارات الشركات */}
          <div className="mb-12">
            <h4 className="text-2xl font-bold text-gray-800 mb-6 text-center">🏢 شارات الشركات</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {BUSINESS_BADGES.map((badge) => (
                <BadgeInfo key={badge.id} badgeId={badge.id} />
              ))}
            </div>
          </div>
        </div>

        {/* دعوة للاشتراك */}
        <div className="mt-16 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-lg p-8 border border-blue-200">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-gray-800 mb-4">🚀 جاهز للبدء؟</h3>
            <p className="text-gray-600 mb-6">اختر الخطة المناسبة لك واشترك الآن للحصول على جميع المميزات</p>

            <Link
              href="/subscription"
              className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-lg hover:from-primary-600 hover:to-primary-700 transition-all font-semibold text-lg shadow-lg hover:shadow-xl"
            >
              <span>💎</span>
              <span>ابدأ الاشتراك الآن</span>
              <span>←</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingPlans;
