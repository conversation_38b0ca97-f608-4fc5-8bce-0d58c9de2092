'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';

const BooksEducationPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedSubCategory, setSelectedSubCategory] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState('');
  const [selectedFormat, setSelectedFormat] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [priceRange, setPriceRange] = useState([0, 500000]);
  const [sortBy, setSortBy] = useState('newest');
  const [searchQuery, setSearchQuery] = useState('');

  // بيانات وهمية للإعلانات
  const mockAds = [
    {
      id: '1',
      title: 'كتب طبية جامعية - مجموعة كاملة',
      price: 150000,
      currency: 'SYP',
      location: 'دمشق - المزة',
      images: ['/images/books/medical-books.jpg'],
      category: 'books-education',
      subCategory: 'books-references',
      subject: 'طب',
      language: 'العربية',
      condition: 'جيد جداً',
      views: 189,
      isFavorite: false,
      isPromoted: true,
      postedAt: '2024-01-15',
      description: 'مجموعة كتب طبية جامعية شاملة لجميع السنوات'
    },
    {
      id: '2',
      title: 'دروس خصوصية - رياضيات ثانوي',
      price: 30000,
      currency: 'SYP',
      location: 'دمشق - أبو رمانة',
      images: ['/images/books/math-tutor.jpg'],
      category: 'books-education',
      subCategory: 'private-lessons',
      subject: 'رياضيات',
      language: 'العربية',
      format: 'حضوري',
      views: 245,
      isFavorite: true,
      isPromoted: false,
      postedAt: '2024-01-14',
      description: 'دروس خصوصية في الرياضيات للمرحلة الثانوية مع مدرس خبير'
    },
    {
      id: '3',
      title: 'دورة برمجة Python - مبتدئين',
      price: 75000,
      currency: 'SYP',
      location: 'دمشق - الشعلان',
      images: ['/images/books/python-course.jpg'],
      category: 'books-education',
      subCategory: 'training-courses',
      subject: 'Python',
      language: 'العربية',
      format: 'أونلاين',
      views: 156,
      isFavorite: false,
      isPromoted: false,
      postedAt: '2024-01-13',
      description: 'دورة شاملة لتعلم برمجة Python من الصفر'
    }
  ];

  const categories = [
    { id: 'books-references', name: 'الكتب والمراجع' },
    { id: 'private-lessons', name: 'الدروس الخصوصية' },
    { id: 'technical-education', name: 'التعليم الفني والمهني' },
    { id: 'training-courses', name: 'الدورات التدريبية' },
    { id: 'other-services', name: 'خدمات تعليمية أخرى' }
  ];

  const subCategories = {
    'books-references': [
      { id: 'school-books', name: 'كتب مدرسية' },
      { id: 'university-books', name: 'كتب جامعية' },
      { id: 'language-books', name: 'كتب تعليم اللغات' },
      { id: 'children-books', name: 'كتب أطفال وقصص مصورة' },
      { id: 'academic-references', name: 'مراجع علمية وأكاديمية' }
    ],
    'private-lessons': [
      { id: 'elementary', name: 'دروس خصوصية للمرحلة الابتدائية' },
      { id: 'middle-school', name: 'دروس خصوصية للمرحلة الإعدادية' },
      { id: 'high-school', name: 'دروس خصوصية للمرحلة الثانوية' },
      { id: 'university', name: 'كورسات جامعية' },
      { id: 'languages', name: 'دروس لغات' }
    ],
    'technical-education': [
      { id: 'crafts', name: 'تعليم الحرف' },
      { id: 'sewing', name: 'تعليم الخياطة والتطريز' },
      { id: 'automotive', name: 'تعليم الميكانيك وصيانة السيارات' },
      { id: 'electronics-repair', name: 'تعليم تصليح الموبايلات والحواسيب' },
      { id: 'design-software', name: 'تعليم برامج التصميم' }
    ],
    'training-courses': [
      { id: 'programming', name: 'دورات برمجة' },
      { id: 'web-design', name: 'دورات تصميم مواقع وتطبيقات' },
      { id: 'digital-marketing', name: 'دورات التسويق الإلكتروني' },
      { id: 'photography', name: 'دورات التصوير الفوتوغرافي والفيديو' },
      { id: 'business', name: 'دورات إدارة أعمال وريادة مشاريع' }
    ],
    'other-services': [
      { id: 'centers', name: 'مراكز دروس خصوصية' },
      { id: 'libraries', name: 'مكتبات وأكشاك بيع كتب' },
      { id: 'research-writing', name: 'خدمات كتابة الأبحاث الجامعية' },
      { id: 'translation', name: 'خدمات الترجمة الأكاديمية' },
      { id: 'online-lessons', name: 'دروس أونلاين عبر الإنترنت' }
    ]
  };

  const languages = ['العربية', 'الإنكليزية', 'الفرنسية', 'التركية', 'الألمانية', 'أخرى'];
  const formats = ['حضوري', 'أونلاين', 'مختلط'];
  const conditions = ['جديد', 'ممتاز', 'جيد جداً', 'جيد', 'مقبول'];

  const locations = [
    'دمشق - المزة',
    'دمشق - أبو رمانة',
    'دمشق - الشعلان',
    'دمشق - المالكي',
    'دمشق - الصالحية',
    'حلب - الفرقان',
    'حلب - الجميلية',
    'حمص - الوعر',
    'حمص - الخالدية'
  ];

  const filteredAds = mockAds.filter(ad => {
    const matchesSearch = ad.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         ad.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || ad.subCategory === selectedCategory;
    const matchesSubCategory = !selectedSubCategory || ad.subject?.includes(selectedSubCategory);
    const matchesLanguage = !selectedLanguage || ad.language === selectedLanguage;
    const matchesFormat = !selectedFormat || ad.format === selectedFormat;
    const matchesLocation = !selectedLocation || ad.location.includes(selectedLocation);
    const matchesPrice = ad.price >= priceRange[0] && ad.price <= priceRange[1];

    return matchesSearch && matchesCategory && matchesSubCategory && matchesLanguage && matchesFormat && matchesLocation && matchesPrice;
  });

  const sortedAds = [...filteredAds].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'popular':
        return b.views - a.views;
      case 'newest':
      default:
        return new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime();
    }
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-white">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* العنوان الرئيسي */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
              <span className="text-2xl">📚</span>
            </div>
            <h1 className="text-4xl font-bold text-gray-800">الكتب والتعليم</h1>
          </div>
          <p className="text-gray-600 text-lg">اكتشف أفضل الكتب والدورات التعليمية</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* الفلاتر الجانبية */}
          <div className="lg:col-span-1">
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-blue-200/50 sticky top-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <span className="text-blue-500">🔍</span>
                البحث والفلاتر
              </h3>

              {/* البحث */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="ابحث عن كتاب أو دورة..."
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* الفئة الرئيسية */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الرئيسية</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => {
                    setSelectedCategory(e.target.value);
                    setSelectedSubCategory('');
                  }}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">جميع الفئات</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* الفئة الفرعية */}
              {selectedCategory && subCategories[selectedCategory as keyof typeof subCategories] && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الفرعية</label>
                  <select
                    value={selectedSubCategory}
                    onChange={(e) => setSelectedSubCategory(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">جميع الفئات الفرعية</option>
                    {subCategories[selectedCategory as keyof typeof subCategories].map(subCategory => (
                      <option key={subCategory.id} value={subCategory.name}>
                        {subCategory.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* اللغة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">اللغة</label>
                <select
                  value={selectedLanguage}
                  onChange={(e) => setSelectedLanguage(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">جميع اللغات</option>
                  {languages.map(language => (
                    <option key={language} value={language}>
                      {language}
                    </option>
                  ))}
                </select>
              </div>

              {/* نوع التعليم */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع التعليم</label>
                <select
                  value={selectedFormat}
                  onChange={(e) => setSelectedFormat(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">جميع الأنواع</option>
                  {formats.map(format => (
                    <option key={format} value={format}>
                      {format}
                    </option>
                  ))}
                </select>
              </div>

              {/* الموقع */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                <select
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">جميع المواقع</option>
                  {locations.map(location => (
                    <option key={location} value={location}>
                      {location}
                    </option>
                  ))}
                </select>
              </div>

              {/* نطاق السعر */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نطاق السعر: {priceRange[0].toLocaleString()} - {priceRange[1].toLocaleString()} ل.س
                </label>
                <div className="space-y-2">
                  <input
                    type="range"
                    min="0"
                    max="500000"
                    step="5000"
                    value={priceRange[0]}
                    onChange={(e) => setPriceRange([parseInt(e.target.value), priceRange[1]])}
                    className="w-full"
                  />
                  <input
                    type="range"
                    min="0"
                    max="500000"
                    step="5000"
                    value={priceRange[1]}
                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                    className="w-full"
                  />
                </div>
              </div>

              {/* إعادة تعيين الفلاتر */}
              <button
                onClick={() => {
                  setSelectedCategory('');
                  setSelectedSubCategory('');
                  setSelectedLanguage('');
                  setSelectedFormat('');
                  setSelectedLocation('');
                  setPriceRange([0, 500000]);
                  setSearchQuery('');
                }}
                className="w-full bg-gradient-to-r from-blue-500 to-indigo-500 text-white py-2 px-4 rounded-lg hover:from-blue-600 hover:to-indigo-600 transition-all duration-300"
              >
                إعادة تعيين الفلاتر
              </button>
            </div>
          </div>

          {/* النتائج */}
          <div className="lg:col-span-3">
            {/* شريط الترتيب */}
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 mb-6 shadow-lg border border-blue-200/50">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="text-gray-600">
                  تم العثور على <span className="font-semibold text-blue-600">{sortedAds.length}</span> إعلان
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium text-gray-700">ترتيب حسب:</label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="popular">الأكثر مشاهدة</option>
                    <option value="price-low">السعر: من الأقل للأعلى</option>
                    <option value="price-high">السعر: من الأعلى للأقل</option>
                  </select>
                </div>
              </div>
            </div>

            {/* الإعلانات */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {sortedAds.map(ad => (
                <AdCard key={ad.id} ad={ad} />
              ))}
            </div>

            {/* رسالة عدم وجود نتائج */}
            {sortedAds.length === 0 && (
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-4xl">🔍</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد نتائج</h3>
                <p className="text-gray-600">جرب تعديل الفلاتر للعثور على المزيد من النتائج</p>
              </div>
            )}
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default BooksEducationPage;
