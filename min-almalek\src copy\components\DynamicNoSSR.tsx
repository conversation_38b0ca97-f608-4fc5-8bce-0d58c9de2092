'use client';

import dynamic from 'next/dynamic';
import { ComponentType } from 'react';

interface DynamicNoSSRProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const DynamicWrapper = ({ children, fallback }: DynamicNoSSRProps) => {
  return (
    <div suppressHydrationWarning={true}>
      {children}
    </div>
  );
};

// تصدير مكون ديناميكي بدون SSR
const DynamicNoSSR = dynamic(
  () => Promise.resolve(DynamicWrapper),
  {
    ssr: false,
    loading: () => (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    ),
  }
);

export default DynamicNoSSR;
