'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNotificationHelpers } from '@/hooks/useNotificationHelpers';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ClientOnlyWrapper from '@/components/ClientOnlyWrapper';
import VerificationBadge from '@/components/VerificationBadge';
import { determineUserBadge } from '@/lib/verification';
import { INDIVIDUAL_PLANS, BUSINESS_PLANS, formatPrice } from '@/lib/pricing';

export default function MySubscriptionPage() {
  const { user, isAuthenticated, updateProfile } = useAuth();
  const { notifyPaymentSuccess } = useNotificationHelpers();
  const [selectedPlan, setSelectedPlan] = useState('');
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto bg-white rounded-xl shadow-lg p-8 text-center">
            <div className="text-6xl mb-4">🔒</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">تسجيل الدخول مطلوب</h2>
            <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى الاشتراكات</p>
            <button
              onClick={() => window.location.href = '/'}
              className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              العودة للرئيسية
            </button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  const userBadge = determineUserBadge(user.userType, user.subscription?.planId);
  const currentPlans = user.userType === 'individual' ? INDIVIDUAL_PLANS : BUSINESS_PLANS;

  const handleUpgrade = async (planId: string) => {
    setIsUpgrading(true);
    try {
      const selectedPlanData = currentPlans.find(p => p.id === planId);
      if (!selectedPlanData) return;

      // محاكاة عملية الدفع
      await new Promise(resolve => setTimeout(resolve, 2000));

      // تحديث الاشتراك
      const newSubscription = {
        planId: selectedPlanData.id,
        planName: selectedPlanData.name,
        planType: user.userType === 'individual' ? 'individual' as const : 'business' as const,
        startDate: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 يوم
        isActive: true,
        autoRenew: true,
        features: selectedPlanData.features,
      };

      await updateProfile({ subscription: newSubscription });
      notifyPaymentSuccess(selectedPlanData.price, 'ل.س', selectedPlanData.name);
      setShowUpgradeModal(false);
    } catch (error) {
      console.error('Upgrade error:', error);
    } finally {
      setIsUpgrading(false);
    }
  };

  const canUpgradeToBusinessAccount = () => {
    return user.userType === 'individual' && !user.subscription;
  };

  const handleAccountTypeUpgrade = async (newType: 'business' | 'real-estate-office') => {
    setIsUpgrading(true);
    try {
      // محاكاة عملية التحقق والترقية
      await new Promise(resolve => setTimeout(resolve, 2000));

      const updates: any = {
        userType: newType,
      };

      if (newType === 'business') {
        updates.businessInfo = {
          companyName: user.name + ' - شركة',
          businessType: 'تجارة عامة',
          registrationNumber: 'REG' + Date.now(),
          address: {
            governorate: '',
            city: '',
            area: '',
            street: '',
          },
          contactPerson: {
            name: user.name,
            position: 'مدير عام',
            phone: user.phone,
            email: user.email,
          },
        };
        // إضافة اشتراك أساسي للشركات
        updates.subscription = {
          planId: 'business-starter',
          planName: 'باقة البداية',
          planType: 'business',
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          isActive: true,
          autoRenew: true,
          features: ['15 إعلان شهرياً', 'دعم فني', 'إحصائيات متقدمة', 'شارة موثقة'],
        };
      } else if (newType === 'real-estate-office') {
        updates.realEstateOfficeInfo = {
          officeName: user.name + ' - مكتب عقاري',
          licenseNumber: 'LIC' + Date.now(),
          licenseIssueDate: new Date(),
          licenseExpiryDate: new Date(Date.now() + 5 * 365 * 24 * 60 * 60 * 1000), // 5 سنوات
          ownerName: user.name,
          specializations: ['شقق سكنية', 'فيلات'],
          serviceAreas: ['دمشق'],
          yearsOfExperience: 1,
          teamSize: 1,
          address: {
            governorate: '',
            city: '',
            area: '',
            street: '',
            building: '',
          },
          workingHours: {
            sunday: { open: '09:00', close: '17:00', isOpen: true },
            monday: { open: '09:00', close: '17:00', isOpen: true },
            tuesday: { open: '09:00', close: '17:00', isOpen: true },
            wednesday: { open: '09:00', close: '17:00', isOpen: true },
            thursday: { open: '09:00', close: '17:00', isOpen: true },
            friday: { open: '09:00', close: '12:00', isOpen: true },
            saturday: { open: '09:00', close: '17:00', isOpen: true },
          },
        };
        // إضافة اشتراك المكتب العقاري
        updates.subscription = {
          planId: 'real-estate-office',
          planName: 'باقة المكتب العقاري',
          planType: 'business',
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          isActive: true,
          autoRenew: true,
          features: ['30 إعلان شهرياً', 'شارة موثقة ذهبية', 'أولوية في النتائج', 'دعم فني مخصص'],
        };
      }

      await updateProfile(updates);
      notifyPaymentSuccess(0, 'ل.س', 'ترقية نوع الحساب');
    } catch (error) {
      console.error('Account upgrade error:', error);
    } finally {
      setIsUpgrading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <ClientOnlyWrapper
          fallback={
            <div className="max-w-6xl mx-auto">
              <div className="animate-pulse space-y-6">
                <div className="h-32 bg-gray-200 rounded-xl"></div>
                <div className="grid grid-cols-3 gap-6">
                  {[...Array(3)].map((_, i) => (
                    <div key={i} className="h-96 bg-gray-200 rounded-xl"></div>
                  ))}
                </div>
              </div>
            </div>
          }
        >
          <div className="max-w-6xl mx-auto">
            {/* رأس الصفحة */}
            <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-800 mb-2">إدارة الاشتراك</h1>
                  <p className="text-gray-600">اختر الباقة المناسبة لاحتياجاتك وترقى حسابك</p>
                </div>
                <div className="text-6xl">💎</div>
              </div>
            </div>

            {/* الاشتراك الحالي */}
            {user.subscription ? (
              <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-xl p-6 mb-8">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center">
                      <span className="text-white text-xl">💎</span>
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-primary-800">اشتراكك الحالي</h3>
                      <p className="text-primary-600">{user.subscription.planName}</p>
                    </div>
                  </div>
                  <VerificationBadge type={userBadge.type} size="lg" />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <span className="text-sm text-primary-600">حالة الاشتراك</span>
                    <div className="font-semibold text-primary-800">
                      {user.subscription.isActive ? '✅ نشط' : '❌ غير نشط'}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-primary-600">تاريخ الانتهاء</span>
                    <div className="font-semibold text-primary-800">
                      {new Date(user.subscription.endDate).toLocaleDateString('en-GB', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                      }).split('/').reverse().join('/')}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-primary-600">التجديد التلقائي</span>
                    <div className="font-semibold text-primary-800">
                      {user.subscription.autoRenew ? '✅ مفعل' : '❌ غير مفعل'}
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <h4 className="font-semibold text-primary-800 mb-2">مميزات الباقة:</h4>
                  <div className="flex flex-wrap gap-2">
                    {user.subscription.features.map((feature, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-primary-200 text-primary-800 rounded-full text-sm"
                      >
                        {feature}
                      </span>
                    ))}
                  </div>
                </div>

                {/* مميزات الحساب حسب النوع */}
                <div className="mt-6 p-4 bg-white bg-opacity-50 rounded-lg">
                  <h4 className="font-semibold text-primary-800 mb-3">مميزات حسابك:</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {user.userType === 'business' && (
                      <>
                        <div className="flex items-center gap-2">
                          <span className="text-green-500">✅</span>
                          <span className="text-sm">عرض معلومات الشركة في الإعلانات</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-green-500">✅</span>
                          <span className="text-sm">أولوية في نتائج البحث</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-green-500">✅</span>
                          <span className="text-sm">شارة موثقة للشركة</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-green-500">✅</span>
                          <span className="text-sm">إحصائيات متقدمة</span>
                        </div>
                      </>
                    )}
                    {user.userType === 'real-estate-office' && (
                      <>
                        <div className="flex items-center gap-2">
                          <span className="text-green-500">✅</span>
                          <span className="text-sm">شارة موثقة ذهبية</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-green-500">✅</span>
                          <span className="text-sm">أولوية عالية في البحث</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-green-500">✅</span>
                          <span className="text-sm">عرض معلومات الترخيص</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-green-500">✅</span>
                          <span className="text-sm">دعم فني مخصص</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-gray-50 rounded-xl p-6 mb-8 text-center">
                <div className="text-4xl mb-4">📦</div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">الخطة المجانية</h3>
                <p className="text-gray-600 mb-4">5 إعلانات مجانية شهرياً</p>
                <p className="text-sm text-gray-500">ترقى إلى باقة مدفوعة للحصول على مميزات إضافية</p>
              </div>
            )}

            {/* ترقية نوع الحساب */}
            {canUpgradeToBusinessAccount() && (
              <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-xl p-6 mb-8">
                <h3 className="text-xl font-bold text-orange-800 mb-4">🚀 ترقية نوع الحساب</h3>
                <p className="text-orange-700 mb-6">احصل على مميزات إضافية وأولوية في البحث بترقية حسابك</p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="bg-white rounded-lg p-6 border-2 border-orange-200 hover:border-orange-400 transition-colors">
                    <div className="text-center mb-4">
                      <div className="text-4xl mb-2">🏢</div>
                      <h4 className="text-lg font-bold text-gray-800">حساب شركة</h4>
                      <p className="text-sm text-gray-600">للشركات والأعمال التجارية</p>
                    </div>
                    <ul className="text-sm text-gray-700 mb-4 space-y-2">
                      <li className="flex items-center gap-2">
                        <span className="text-green-500">✅</span>
                        <span>شارة موثقة للشركة</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="text-green-500">✅</span>
                        <span>عرض معلومات الشركة</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="text-green-500">✅</span>
                        <span>أولوية في نتائج البحث</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="text-green-500">✅</span>
                        <span>إحصائيات متقدمة</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="text-green-500">✅</span>
                        <span>دعم فني مخصص</span>
                      </li>
                    </ul>
                    <button
                      onClick={() => handleAccountTypeUpgrade('business')}
                      disabled={isUpgrading}
                      className="w-full px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50 font-medium"
                    >
                      {isUpgrading ? 'جاري الترقية...' : 'ترقية إلى حساب شركة'}
                    </button>
                  </div>

                  <div className="bg-white rounded-lg p-6 border-2 border-orange-200 hover:border-orange-400 transition-colors">
                    <div className="text-center mb-4">
                      <div className="text-4xl mb-2">🏘️</div>
                      <h4 className="text-lg font-bold text-gray-800">مكتب عقاري</h4>
                      <p className="text-sm text-gray-600">للمكاتب العقارية المرخصة</p>
                    </div>
                    <ul className="text-sm text-gray-700 mb-4 space-y-2">
                      <li className="flex items-center gap-2">
                        <span className="text-green-500">✅</span>
                        <span>شارة موثقة ذهبية</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="text-green-500">✅</span>
                        <span>عرض معلومات الترخيص</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="text-green-500">✅</span>
                        <span>أولوية عالية في البحث</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="text-green-500">✅</span>
                        <span>30 إعلان شهرياً</span>
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="text-green-500">✅</span>
                        <span>دعم فني مخصص</span>
                      </li>
                    </ul>
                    <button
                      onClick={() => handleAccountTypeUpgrade('real-estate-office')}
                      disabled={isUpgrading}
                      className="w-full px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50 font-medium"
                    >
                      {isUpgrading ? 'جاري الترقية...' : 'ترقية إلى مكتب عقاري'}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* الباقات المتاحة */}
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-gray-800 mb-6">
                {user.userType === 'individual' ? 'الباقات الفردية المتاحة' : 'باقات الأعمال المتاحة'}
              </h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {currentPlans.map((plan) => {
                  const isCurrentPlan = user.subscription?.planId === plan.id;
                  const badge = determineUserBadge(user.userType, plan.id);

                  return (
                    <div
                      key={plan.id}
                      className={`bg-white rounded-xl shadow-lg p-6 border-2 transition-all ${
                        isCurrentPlan
                          ? 'border-primary-500 ring-2 ring-primary-200'
                          : 'border-gray-200 hover:border-primary-300'
                      } ${plan.popular ? 'relative' : ''}`}
                    >
                      {plan.popular && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <span className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                            الأكثر شعبية
                          </span>
                        </div>
                      )}

                      <div className="text-center mb-6">
                        <div className="flex items-center justify-center gap-2 mb-2">
                          <h4 className="text-xl font-bold text-gray-800">{plan.name}</h4>
                          <VerificationBadge type={badge.type} size="sm" />
                        </div>
                        <div className="text-3xl font-bold text-primary-600 mb-2">
                          {formatPrice(plan.price)}
                        </div>
                        <p className="text-gray-600">{plan.duration}</p>
                      </div>

                      <ul className="space-y-3 mb-6">
                        {plan.features.map((feature, index) => (
                          <li key={index} className="flex items-center gap-2 text-sm">
                            <span className="text-green-500">✅</span>
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>

                      {isCurrentPlan ? (
                        <div className="text-center py-3 bg-primary-100 text-primary-800 rounded-lg font-medium">
                          الباقة الحالية
                        </div>
                      ) : (
                        <button
                          onClick={() => {
                            setSelectedPlan(plan.id);
                            setShowUpgradeModal(true);
                          }}
                          className="w-full py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium"
                        >
                          {user.subscription ? 'ترقية إلى هذه الباقة' : 'اختيار هذه الباقة'}
                        </button>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </ClientOnlyWrapper>
      </main>

      {/* مودال تأكيد الترقية */}
      {showUpgradeModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-md w-full p-6">
            <h3 className="text-xl font-bold text-gray-800 mb-4">تأكيد الترقية</h3>
            <p className="text-gray-600 mb-6">
              هل أنت متأكد من ترقية اشتراكك إلى الباقة المختارة؟
            </p>
            <div className="flex gap-4">
              <button
                onClick={() => handleUpgrade(selectedPlan)}
                disabled={isUpgrading}
                className="flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors disabled:opacity-50"
              >
                {isUpgrading ? 'جاري المعالجة...' : 'تأكيد الترقية'}
              </button>
              <button
                onClick={() => setShowUpgradeModal(false)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
}
