'use client';

import { useState, useEffect } from 'react';

interface Question {
  id: string;
  question: string;
  type: 'multiple_choice' | 'true_false' | 'coding' | 'essay';
  options?: string[];
  correctAnswer?: string | number;
  points: number;
}

interface SkillTest {
  id: string;
  title: string;
  description: string;
  category: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // in minutes
  questions: Question[];
  passingScore: number;
  totalPoints: number;
  attempts: number;
  maxAttempts: number;
  isCompleted: boolean;
  lastScore?: number;
  bestScore?: number;
  certificate?: boolean;
}

interface TestResult {
  testId: string;
  score: number;
  percentage: number;
  passed: boolean;
  completedAt: string;
  timeSpent: number;
  answers: Record<string, any>;
}

interface SkillTestsProps {
  userId: string;
}

export default function SkillTests({ userId }: SkillTestsProps) {
  const [tests, setTests] = useState<SkillTest[]>([]);
  const [activeTest, setActiveTest] = useState<SkillTest | null>(null);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [timeLeft, setTimeLeft] = useState(0);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [showResults, setShowResults] = useState(false);

  // بيانات وهمية للاختبارات
  useEffect(() => {
    const sampleTests: SkillTest[] = [
      {
        id: '1',
        title: 'اختبار JavaScript الأساسي',
        description: 'اختبار شامل لمهارات JavaScript الأساسية والمتقدمة',
        category: 'البرمجة',
        difficulty: 'intermediate',
        duration: 30,
        questions: [
          {
            id: 'q1',
            question: 'ما هو الناتج من الكود التالي؟ console.log(typeof null)',
            type: 'multiple_choice',
            options: ['null', 'undefined', 'object', 'boolean'],
            correctAnswer: 2,
            points: 10
          },
          {
            id: 'q2',
            question: 'هل يمكن تغيير قيمة متغير تم تعريفه بـ const؟',
            type: 'true_false',
            correctAnswer: 'false',
            points: 5
          },
          {
            id: 'q3',
            question: 'اكتب دالة تقوم بعكس نص معطى',
            type: 'coding',
            points: 20
          }
        ],
        passingScore: 70,
        totalPoints: 35,
        attempts: 0,
        maxAttempts: 3,
        isCompleted: false
      },
      {
        id: '2',
        title: 'اختبار React المتقدم',
        description: 'اختبار للمطورين المتقدمين في React وإدارة الحالة',
        category: 'البرمجة',
        difficulty: 'advanced',
        duration: 45,
        questions: [
          {
            id: 'q1',
            question: 'ما هو الفرق بين useState و useReducer؟',
            type: 'essay',
            points: 25
          },
          {
            id: 'q2',
            question: 'متى نستخدم useCallback؟',
            type: 'multiple_choice',
            options: [
              'لتحسين الأداء عند تمرير دوال للمكونات الفرعية',
              'لحفظ البيانات في localStorage',
              'لإدارة الحالة العامة',
              'لتنفيذ العمليات غير المتزامنة'
            ],
            correctAnswer: 0,
            points: 15
          }
        ],
        passingScore: 80,
        totalPoints: 40,
        attempts: 1,
        maxAttempts: 2,
        isCompleted: true,
        lastScore: 85,
        bestScore: 85,
        certificate: true
      },
      {
        id: '3',
        title: 'اختبار التصميم الجرافيكي',
        description: 'اختبار أساسيات التصميم ونظرية الألوان',
        category: 'التصميم',
        difficulty: 'beginner',
        duration: 20,
        questions: [
          {
            id: 'q1',
            question: 'ما هي الألوان الأساسية؟',
            type: 'multiple_choice',
            options: ['أحمر، أزرق، أصفر', 'أحمر، أخضر، أزرق', 'أسود، أبيض، رمادي', 'برتقالي، بنفسجي، أخضر'],
            correctAnswer: 0,
            points: 10
          }
        ],
        passingScore: 60,
        totalPoints: 10,
        attempts: 0,
        maxAttempts: 5,
        isCompleted: false
      }
    ];

    setTests(sampleTests);
  }, [userId]);

  const startTest = (test: SkillTest) => {
    if (test.attempts >= test.maxAttempts) return;
    
    setActiveTest(test);
    setCurrentQuestion(0);
    setAnswers({});
    setTimeLeft(test.duration * 60); // Convert to seconds
    setShowResults(false);
  };

  const submitAnswer = (questionId: string, answer: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const nextQuestion = () => {
    if (activeTest && currentQuestion < activeTest.questions.length - 1) {
      setCurrentQuestion(prev => prev + 1);
    }
  };

  const previousQuestion = () => {
    if (currentQuestion > 0) {
      setCurrentQuestion(prev => prev - 1);
    }
  };

  const submitTest = () => {
    if (!activeTest) return;

    // حساب النتيجة
    let totalScore = 0;
    activeTest.questions.forEach(question => {
      const userAnswer = answers[question.id];
      if (question.type === 'multiple_choice' || question.type === 'true_false') {
        if (userAnswer === question.correctAnswer) {
          totalScore += question.points;
        }
      }
      // للأسئلة المفتوحة والبرمجة، نحتاج لتقييم يدوي
    });

    const percentage = (totalScore / activeTest.totalPoints) * 100;
    const passed = percentage >= activeTest.passingScore;

    const result: TestResult = {
      testId: activeTest.id,
      score: totalScore,
      percentage,
      passed,
      completedAt: new Date().toISOString(),
      timeSpent: (activeTest.duration * 60) - timeLeft,
      answers
    };

    setTestResults(prev => [...prev, result]);
    
    // تحديث الاختبار
    setTests(prev => prev.map(test => 
      test.id === activeTest.id 
        ? {
            ...test,
            attempts: test.attempts + 1,
            isCompleted: true,
            lastScore: percentage,
            bestScore: Math.max(test.bestScore || 0, percentage)
          }
        : test
    ));

    setShowResults(true);
    setActiveTest(null);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifficultyText = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'مبتدئ';
      case 'intermediate': return 'متوسط';
      case 'advanced': return 'متقدم';
      default: return 'غير محدد';
    }
  };

  // Timer effect
  useEffect(() => {
    if (activeTest && timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (activeTest && timeLeft === 0) {
      submitTest();
    }
  }, [timeLeft, activeTest]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (activeTest) {
    const question = activeTest.questions[currentQuestion];
    const progress = ((currentQuestion + 1) / activeTest.questions.length) * 100;

    return (
      <div className="bg-white rounded-xl shadow-lg p-6">
        {/* رأس الاختبار */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-800">{activeTest.title}</h2>
          <div className="flex items-center gap-4">
            <div className="text-lg font-semibold text-blue-600">
              ⏰ {formatTime(timeLeft)}
            </div>
            <button
              onClick={() => setActiveTest(null)}
              className="text-red-600 hover:text-red-800"
            >
              إنهاء الاختبار
            </button>
          </div>
        </div>

        {/* شريط التقدم */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-600">
              السؤال {currentQuestion + 1} من {activeTest.questions.length}
            </span>
            <span className="text-sm text-gray-600">
              {progress.toFixed(0)}% مكتمل
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            ></div>
          </div>
        </div>

        {/* السؤال */}
        <div className="mb-8">
          <h3 className="text-xl font-semibold text-gray-800 mb-4">
            {question.question}
          </h3>

          {question.type === 'multiple_choice' && question.options && (
            <div className="space-y-3">
              {question.options.map((option, index) => (
                <label key={index} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="radio"
                    name={question.id}
                    value={index}
                    checked={answers[question.id] === index}
                    onChange={(e) => submitAnswer(question.id, parseInt(e.target.value))}
                    className="mr-3"
                  />
                  <span>{option}</span>
                </label>
              ))}
            </div>
          )}

          {question.type === 'true_false' && (
            <div className="space-y-3">
              <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input
                  type="radio"
                  name={question.id}
                  value="true"
                  checked={answers[question.id] === 'true'}
                  onChange={(e) => submitAnswer(question.id, e.target.value)}
                  className="mr-3"
                />
                <span>صحيح</span>
              </label>
              <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                <input
                  type="radio"
                  name={question.id}
                  value="false"
                  checked={answers[question.id] === 'false'}
                  onChange={(e) => submitAnswer(question.id, e.target.value)}
                  className="mr-3"
                />
                <span>خطأ</span>
              </label>
            </div>
          )}

          {(question.type === 'coding' || question.type === 'essay') && (
            <textarea
              value={answers[question.id] || ''}
              onChange={(e) => submitAnswer(question.id, e.target.value)}
              placeholder={question.type === 'coding' ? 'اكتب الكود هنا...' : 'اكتب إجابتك هنا...'}
              className="w-full h-40 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              style={{ fontFamily: question.type === 'coding' ? 'monospace' : 'inherit' }}
            />
          )}
        </div>

        {/* أزرار التنقل */}
        <div className="flex items-center justify-between">
          <button
            onClick={previousQuestion}
            disabled={currentQuestion === 0}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            السؤال السابق
          </button>

          <div className="text-sm text-gray-600">
            {question.points} نقطة
          </div>

          {currentQuestion === activeTest.questions.length - 1 ? (
            <button
              onClick={submitTest}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              إنهاء الاختبار
            </button>
          ) : (
            <button
              onClick={nextQuestion}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              السؤال التالي
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-2">
          🧠 اختبارات المهارات
        </h2>

        {/* إحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {tests.length}
            </div>
            <div className="text-sm text-blue-700">اختبارات متاحة</div>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {tests.filter(t => t.isCompleted).length}
            </div>
            <div className="text-sm text-green-700">اختبارات مكتملة</div>
          </div>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {tests.filter(t => t.certificate && t.isCompleted).length}
            </div>
            <div className="text-sm text-yellow-700">شهادات حاصل عليها</div>
          </div>
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">
              {tests.filter(t => t.isCompleted && t.bestScore && t.bestScore >= t.passingScore).length}
            </div>
            <div className="text-sm text-purple-700">اختبارات ناجحة</div>
          </div>
        </div>

        {/* قائمة الاختبارات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tests.map((test) => (
            <div key={test.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-3">
                <h3 className="text-lg font-semibold text-gray-800">{test.title}</h3>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(test.difficulty)}`}>
                  {getDifficultyText(test.difficulty)}
                </span>
              </div>

              <p className="text-gray-600 text-sm mb-4">{test.description}</p>

              <div className="space-y-2 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">المدة:</span>
                  <span className="font-medium">{test.duration} دقيقة</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">الأسئلة:</span>
                  <span className="font-medium">{test.questions.length} سؤال</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">درجة النجاح:</span>
                  <span className="font-medium">{test.passingScore}%</span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">المحاولات:</span>
                  <span className="font-medium">{test.attempts}/{test.maxAttempts}</span>
                </div>
              </div>

              {test.isCompleted && test.bestScore && (
                <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">أفضل نتيجة:</span>
                    <span className={`font-bold ${test.bestScore >= test.passingScore ? 'text-green-600' : 'text-red-600'}`}>
                      {test.bestScore.toFixed(1)}%
                    </span>
                  </div>
                  {test.certificate && test.bestScore >= test.passingScore && (
                    <div className="text-xs text-green-600 mt-1">
                      🏆 مؤهل للحصول على شهادة
                    </div>
                  )}
                </div>
              )}

              <button
                onClick={() => startTest(test)}
                disabled={test.attempts >= test.maxAttempts}
                className={`w-full py-2 px-4 rounded-lg font-medium transition-colors ${
                  test.attempts >= test.maxAttempts
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : test.isCompleted
                    ? 'bg-blue-600 text-white hover:bg-blue-700'
                    : 'bg-green-600 text-white hover:bg-green-700'
                }`}
              >
                {test.attempts >= test.maxAttempts
                  ? 'تم استنفاد المحاولات'
                  : test.isCompleted
                  ? 'إعادة الاختبار'
                  : 'بدء الاختبار'
                }
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
