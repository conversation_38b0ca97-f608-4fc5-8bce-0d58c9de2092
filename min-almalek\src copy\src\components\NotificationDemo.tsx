'use client';

import { useNotifications } from './NotificationSystem';

const NotificationDemo = () => {
  const { addNotification } = useNotifications();

  const addSampleNotifications = () => {
    // إشعار نجاح
    addNotification({
      type: 'success',
      title: 'تم نشر إعلانك بنجاح!',
      message: 'تم نشر إعلان "سيارة تويوتا كامري 2020" وهو الآن مرئي للمستخدمين.',
      category: 'ad',
      icon: '🎉',
      actionUrl: '/ad/123',
      actionText: 'عرض الإعلان'
    });

    // إشعار دفع
    addNotification({
      type: 'info',
      title: 'تم استلام الدفعة',
      message: 'تم استلام دفعة بقيمة 50,000 ل.س لترقية إعلانك إلى الباقة المميزة.',
      category: 'payment',
      icon: '💳',
      actionUrl: '/subscription',
      actionText: 'عرض الاشتراكات'
    });

    // إشعار تحذير
    addNotification({
      type: 'warning',
      title: 'إعلانك يحتاج مراجعة',
      message: 'إعلان "شقة للبيع في دمشق" يحتاج إلى مراجعة بعض التفاصيل قبل النشر.',
      category: 'ad',
      icon: '⚠️',
      actionUrl: '/ad/edit/456',
      actionText: 'تعديل الإعلان'
    });

    // إشعار خطأ
    addNotification({
      type: 'error',
      title: 'فشل في رفع الصورة',
      message: 'لم نتمكن من رفع إحدى الصور. يرجى المحاولة مرة أخرى.',
      category: 'system',
      icon: '❌'
    });

    // إشعار عام
    addNotification({
      type: 'info',
      title: 'مرحباً بك في من المالك!',
      message: 'نشكرك لانضمامك إلى منصة من المالك. استكشف جميع الميزات المتاحة.',
      category: 'general',
      icon: '👋',
      actionUrl: '/about',
      actionText: 'تعرف أكثر'
    });

    // إشعار مستخدم
    addNotification({
      type: 'success',
      title: 'تم تحديث ملفك الشخصي',
      message: 'تم حفظ التغييرات على ملفك الشخصي بنجاح.',
      category: 'user',
      icon: '👤'
    });
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">تجربة نظام الإشعارات</h3>
      <p className="text-gray-600 mb-4">
        اضغط على الزر أدناه لإضافة إشعارات تجريبية ومشاهدة كيفية عمل النظام.
      </p>
      <button
        onClick={addSampleNotifications}
        className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium"
      >
        إضافة إشعارات تجريبية
      </button>
    </div>
  );
};

export default NotificationDemo;
