'use client';

import { useState } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  target: 'all' | 'users' | 'companies' | 'real-estate-offices';
  status: 'draft' | 'sent' | 'scheduled';
  createdAt: string;
  sentAt?: string;
  scheduledFor?: string;
  recipients: number;
  readCount: number;
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      title: 'تحديث في شروط الخدمة',
      message: 'تم تحديث شروط الخدمة. يرجى مراجعة التحديثات الجديدة.',
      type: 'info',
      target: 'all',
      status: 'sent',
      createdAt: '2024-01-20T10:30:00Z',
      sentAt: '2024-01-20T11:00:00Z',
      recipients: 1247,
      readCount: 892
    },
    {
      id: '2',
      title: 'عرض خاص للشركات',
      message: 'خصم 20% على جميع باقات الشركات لفترة محدودة!',
      type: 'success',
      target: 'companies',
      status: 'sent',
      createdAt: '2024-01-18T14:15:00Z',
      sentAt: '2024-01-18T15:00:00Z',
      recipients: 156,
      readCount: 98
    },
    {
      id: '3',
      title: 'صيانة مجدولة',
      message: 'سيتم إجراء صيانة على الموقع يوم الجمعة من 2-4 صباحاً',
      type: 'warning',
      target: 'all',
      status: 'scheduled',
      createdAt: '2024-01-21T09:45:00Z',
      scheduledFor: '2024-01-26T02:00:00Z',
      recipients: 0,
      readCount: 0
    },
    {
      id: '4',
      title: 'ميزة جديدة: المقارنة بين الإعلانات',
      message: 'يمكنك الآن مقارنة الإعلانات المختلفة لاتخاذ قرار أفضل',
      type: 'success',
      target: 'all',
      status: 'sent',
      createdAt: '2024-01-19T16:20:00Z',
      sentAt: '2024-01-19T17:00:00Z',
      recipients: 1247,
      readCount: 1156
    },
    {
      id: '5',
      title: 'تذكير: تجديد الاشتراك',
      message: 'ينتهي اشتراكك خلال 3 أيام. جدد الآن للاستمرار في الاستفادة من الخدمات المميزة',
      type: 'warning',
      target: 'companies',
      status: 'sent',
      createdAt: '2024-01-17T12:00:00Z',
      sentAt: '2024-01-17T12:30:00Z',
      recipients: 156,
      readCount: 134
    },
    {
      id: '6',
      title: 'مشكلة في الدفع',
      message: 'فشل في معالجة الدفعة. يرجى التحقق من بيانات البطاقة الائتمانية',
      type: 'error',
      target: 'users',
      status: 'sent',
      createdAt: '2024-01-16T08:45:00Z',
      sentAt: '2024-01-16T09:00:00Z',
      recipients: 23,
      readCount: 21
    },
    {
      id: '7',
      title: 'عروض رمضان الخاصة',
      message: 'استعد لشهر رمضان مع عروضنا الخاصة على جميع الباقات',
      type: 'info',
      target: 'all',
      status: 'scheduled',
      createdAt: '2024-01-15T14:30:00Z',
      scheduledFor: '2024-03-10T18:00:00Z',
      recipients: 0,
      readCount: 0
    },
    {
      id: '8',
      title: 'تحديث تطبيق الموبايل',
      message: 'متوفر الآن إصدار جديد من التطبيق مع تحسينات في الأداء وميزات جديدة',
      type: 'success',
      target: 'all',
      status: 'sent',
      createdAt: '2024-01-14T11:15:00Z',
      sentAt: '2024-01-14T12:00:00Z',
      recipients: 1247,
      readCount: 987
    }
  ]);

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newNotification, setNewNotification] = useState({
    title: '',
    message: '',
    type: 'info' as const,
    target: 'all' as const,
    scheduledFor: ''
  });

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'info': return 'bg-blue-100 text-blue-800';
      case 'success': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'bg-green-100 text-green-800';
      case 'scheduled': return 'bg-yellow-100 text-yellow-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTargetLabel = (target: string) => {
    switch (target) {
      case 'all': return 'جميع المستخدمين';
      case 'users': return 'الأفراد';
      case 'companies': return 'الشركات';
      case 'real-estate-offices': return 'المكاتب العقارية';
      default: return target;
    }
  };

  const handleCreateNotification = () => {
    const notification: Notification = {
      id: Date.now().toString(),
      ...newNotification,
      status: newNotification.scheduledFor ? 'scheduled' : 'sent',
      createdAt: new Date().toISOString(),
      sentAt: newNotification.scheduledFor ? undefined : new Date().toISOString(),
      recipients: newNotification.target === 'all' ? 1247 : 
                  newNotification.target === 'companies' ? 156 : 
                  newNotification.target === 'real-estate-offices' ? 89 : 1002,
      readCount: 0
    };

    setNotifications([notification, ...notifications]);
    setNewNotification({
      title: '',
      message: '',
      type: 'info',
      target: 'all',
      scheduledFor: ''
    });
    setShowCreateModal(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <AdminLayout>
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="w-full space-y-4">
        {/* العنوان */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'Cairo, sans-serif' }}>
              إدارة الإشعارات
            </h1>
            <p className="text-gray-600 mt-1">
              إرسال وإدارة الإشعارات للمستخدمين
            </p>
          </div>
          
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
          >
            إنشاء إشعار جديد
          </button>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الإشعارات</p>
                <p className="text-2xl font-bold text-blue-600">{notifications.length}</p>
                <p className="text-xs text-gray-500 mt-1">+12% من الشهر الماضي</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 text-xl">📢</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">مرسلة</p>
                <p className="text-2xl font-bold text-green-600">
                  {notifications.filter(n => n.status === 'sent').length}
                </p>
                <p className="text-xs text-gray-500 mt-1">نجح الإرسال 100%</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 text-xl">✅</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">مجدولة</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {notifications.filter(n => n.status === 'scheduled').length}
                </p>
                <p className="text-xs text-gray-500 mt-1">في الانتظار</p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <span className="text-yellow-600 text-xl">⏰</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">معدل القراءة</p>
                <p className="text-2xl font-bold text-purple-600">
                  {Math.round((notifications.reduce((sum, n) => sum + n.readCount, 0) /
                    notifications.reduce((sum, n) => sum + n.recipients, 0)) * 100) || 0}%
                </p>
                <p className="text-xs text-gray-500 mt-1">متوسط ممتاز</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-600 text-xl">👁️</span>
              </div>
            </div>
          </div>
        </div>

        {/* إحصائيات إضافية */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* رسم بياني للإشعارات الشهرية */}
          <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">الإشعارات الشهرية</h3>
              <select className="text-sm border border-gray-300 rounded-lg px-3 py-1">
                <option>آخر 6 أشهر</option>
                <option>آخر 12 شهر</option>
              </select>
            </div>
            <div className="grid grid-cols-6 gap-2 h-40 items-end">
              {[45, 52, 38, 61, 47, 58].map((height, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div
                    className="bg-primary-500 rounded-t-sm transition-all duration-1000 hover:bg-primary-600 cursor-pointer w-full"
                    style={{ height: `${height}%` }}
                    title={`الشهر ${index + 1}: ${height} إشعار`}
                  ></div>
                  <span className="text-xs text-gray-500 mt-2">
                    {['ديسمبر', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو'][index]}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* أنواع الإشعارات */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">أنواع الإشعارات</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-gray-700">معلومات</span>
                </div>
                <span className="text-sm font-medium text-gray-900">45%</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-gray-700">نجاح</span>
                </div>
                <span className="text-sm font-medium text-gray-900">30%</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm text-gray-700">تحذير</span>
                </div>
                <span className="text-sm font-medium text-gray-900">20%</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm text-gray-700">خطأ</span>
                </div>
                <span className="text-sm font-medium text-gray-900">5%</span>
              </div>
            </div>
          </div>
        </div>

        {/* إحصائيات الجمهور */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">توزيع الجمهور</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">جميع المستخدمين</span>
                <div className="flex items-center gap-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: '60%' }}></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900">1,247</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">الأفراد</span>
                <div className="flex items-center gap-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '48%' }}></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900">1,002</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">الشركات</span>
                <div className="flex items-center gap-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-purple-500 h-2 rounded-full" style={{ width: '12%' }}></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900">156</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">المكاتب العقارية</span>
                <div className="flex items-center gap-2">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <div className="bg-orange-500 h-2 rounded-full" style={{ width: '7%' }}></div>
                  </div>
                  <span className="text-sm font-medium text-gray-900">89</span>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">أوقات الإرسال المثلى</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">9:00 - 11:00 صباحاً</span>
                <span className="text-sm font-medium text-green-600">معدل قراءة 85%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">2:00 - 4:00 مساءً</span>
                <span className="text-sm font-medium text-blue-600">معدل قراءة 78%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-700">7:00 - 9:00 مساءً</span>
                <span className="text-sm font-medium text-purple-600">معدل قراءة 72%</span>
              </div>
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4">
                <p className="text-sm text-yellow-800">
                  💡 <strong>نصيحة:</strong> أفضل وقت لإرسال الإشعارات هو في الصباح الباكر أو بعد الظهر
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* أدوات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold mb-2">إشعار سريع</h3>
                <p className="text-blue-100 text-sm">إرسال إشعار فوري لجميع المستخدمين</p>
              </div>
              <button
                onClick={() => setShowCreateModal(true)}
                className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg p-3 transition-all"
              >
                <span className="text-2xl">⚡</span>
              </button>
            </div>
          </div>

          <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold mb-2">قوالب جاهزة</h3>
                <p className="text-green-100 text-sm">استخدم قوالب محفوظة للإشعارات</p>
              </div>
              <button className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg p-3 transition-all">
                <span className="text-2xl">📝</span>
              </button>
            </div>
          </div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-6 text-white">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold mb-2">تقارير مفصلة</h3>
                <p className="text-purple-100 text-sm">عرض تقارير شاملة للإشعارات</p>
              </div>
              <button className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg p-3 transition-all">
                <span className="text-2xl">📊</span>
              </button>
            </div>
          </div>
        </div>

        {/* قائمة الإشعارات */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="p-6 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-semibold text-gray-900">الإشعارات المرسلة</h2>
              <div className="flex items-center gap-3">
                <select className="text-sm border border-gray-300 rounded-lg px-3 py-1">
                  <option>جميع الأنواع</option>
                  <option>معلومات</option>
                  <option>نجاح</option>
                  <option>تحذير</option>
                  <option>خطأ</option>
                </select>
                <select className="text-sm border border-gray-300 rounded-lg px-3 py-1">
                  <option>جميع الحالات</option>
                  <option>مرسل</option>
                  <option>مجدول</option>
                  <option>مسودة</option>
                </select>
              </div>
            </div>
          </div>
          
          <div className="divide-y divide-gray-200">
            {notifications.map((notification) => (
              <div key={notification.id} className="p-6 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-medium text-gray-900">{notification.title}</h3>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(notification.type)}`}>
                        {notification.type === 'info' ? 'معلومات' :
                         notification.type === 'success' ? 'نجاح' :
                         notification.type === 'warning' ? 'تحذير' : 'خطأ'}
                      </span>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(notification.status)}`}>
                        {notification.status === 'sent' ? 'مرسل' :
                         notification.status === 'scheduled' ? 'مجدول' : 'مسودة'}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 mb-3">{notification.message}</p>
                    
                    <div className="flex items-center gap-6 text-sm text-gray-500">
                      <span>الهدف: {getTargetLabel(notification.target)}</span>
                      <span>المستلمين: {notification.recipients.toLocaleString()}</span>
                      {notification.status === 'sent' && (
                        <span>القراءة: {notification.readCount.toLocaleString()} ({Math.round((notification.readCount / notification.recipients) * 100)}%)</span>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4 text-xs text-gray-400 mt-2">
                      <span>تم الإنشاء: {formatDate(notification.createdAt)}</span>
                      {notification.sentAt && (
                        <span>تم الإرسال: {formatDate(notification.sentAt)}</span>
                      )}
                      {notification.scheduledFor && (
                        <span>مجدول لـ: {formatDate(notification.scheduledFor)}</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <button className="text-blue-600 hover:text-blue-800 text-sm">
                      عرض التفاصيل
                    </button>
                    {notification.status === 'scheduled' && (
                      <button className="text-green-600 hover:text-green-800 text-sm">
                        إرسال الآن
                      </button>
                    )}
                    <button className="text-red-600 hover:text-red-800 text-sm">
                      حذف
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* تذييل الجدول */}
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600">
                عرض 1-{notifications.length} من {notifications.length} إشعار
              </div>
              <div className="flex items-center gap-2">
                <button className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-100 disabled:opacity-50" disabled>
                  السابق
                </button>
                <span className="px-3 py-1 text-sm bg-primary-600 text-white rounded-lg">1</span>
                <button className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-100 disabled:opacity-50" disabled>
                  التالي
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* قسم النصائح والإرشادات */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <span className="text-xl">💡</span>
              نصائح لإشعارات فعالة
            </h3>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-gray-700">اكتب عناوين واضحة ومختصرة (أقل من 50 حرف)</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-gray-700">استخدم لغة بسيطة ومفهومة للجميع</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-gray-700">حدد الجمهور المناسب لكل إشعار</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-gray-700">اختر التوقيت المناسب للإرسال</p>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                <p className="text-sm text-gray-700">تجنب الإفراط في الإشعارات</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <span className="text-xl">📈</span>
              إحصائيات الأداء
            </h3>
            <div className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-green-800">معدل الفتح</span>
                  <span className="text-lg font-bold text-green-600">78.5%</span>
                </div>
                <div className="w-full bg-green-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '78.5%' }}></div>
                </div>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-blue-800">معدل التفاعل</span>
                  <span className="text-lg font-bold text-blue-600">45.2%</span>
                </div>
                <div className="w-full bg-blue-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: '45.2%' }}></div>
                </div>
              </div>

              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-purple-800">معدل النقر</span>
                  <span className="text-lg font-bold text-purple-600">12.8%</span>
                </div>
                <div className="w-full bg-purple-200 rounded-full h-2">
                  <div className="bg-purple-500 h-2 rounded-full" style={{ width: '12.8%' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* قسم الإجراءات السريعة */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">الإجراءات السريعة</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 text-lg">📢</span>
              </div>
              <div className="text-left">
                <p className="font-medium text-gray-900">إشعار عاجل</p>
                <p className="text-sm text-gray-600">إرسال فوري</p>
              </div>
            </button>

            <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 text-lg">📝</span>
              </div>
              <div className="text-left">
                <p className="font-medium text-gray-900">قالب جديد</p>
                <p className="text-sm text-gray-600">إنشاء قالب</p>
              </div>
            </button>

            <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-600 text-lg">📊</span>
              </div>
              <div className="text-left">
                <p className="font-medium text-gray-900">تقرير شامل</p>
                <p className="text-sm text-gray-600">تصدير البيانات</p>
              </div>
            </button>

            <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                <span className="text-orange-600 text-lg">⚙️</span>
              </div>
              <div className="text-left">
                <p className="font-medium text-gray-900">إعدادات</p>
                <p className="text-sm text-gray-600">تخصيص النظام</p>
              </div>
            </button>
          </div>
        </div>
      </div>
      </div>

      {/* مودال إنشاء إشعار جديد */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full mx-4">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-bold text-gray-900">إنشاء إشعار جديد</h2>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
            </div>
            
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  العنوان
                </label>
                <input
                  type="text"
                  value={newNotification.title}
                  onChange={(e) => setNewNotification({ ...newNotification, title: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="أدخل عنوان الإشعار"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الرسالة
                </label>
                <textarea
                  value={newNotification.message}
                  onChange={(e) => setNewNotification({ ...newNotification, message: e.target.value })}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="أدخل نص الإشعار"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    نوع الإشعار
                  </label>
                  <select
                    value={newNotification.type}
                    onChange={(e) => setNewNotification({ ...newNotification, type: e.target.value as any })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="info">معلومات</option>
                    <option value="success">نجاح</option>
                    <option value="warning">تحذير</option>
                    <option value="error">خطأ</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الهدف
                  </label>
                  <select
                    value={newNotification.target}
                    onChange={(e) => setNewNotification({ ...newNotification, target: e.target.value as any })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  >
                    <option value="all">جميع المستخدمين</option>
                    <option value="users">الأفراد</option>
                    <option value="companies">الشركات</option>
                    <option value="real-estate-offices">المكاتب العقارية</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  جدولة الإرسال (اختياري)
                </label>
                <input
                  type="datetime-local"
                  value={newNotification.scheduledFor}
                  onChange={(e) => setNewNotification({ ...newNotification, scheduledFor: e.target.value })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
                <p className="text-xs text-gray-500 mt-1">
                  اتركه فارغاً للإرسال الفوري
                </p>
              </div>
            </div>

            <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={handleCreateNotification}
                disabled={!newNotification.title || !newNotification.message}
                className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {newNotification.scheduledFor ? 'جدولة الإرسال' : 'إرسال الآن'}
              </button>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
}
