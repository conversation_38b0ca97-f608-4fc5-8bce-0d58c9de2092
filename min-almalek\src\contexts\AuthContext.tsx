'use client';

import { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { User, AuthState, LoginCredentials, RegisterData, UserSession } from '@/types/user';

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  updateProfile: (updates: Partial<User>) => Promise<void>;
  refreshToken: () => Promise<void>;
  deleteAccount: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

type AuthAction =
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGIN_FAILURE'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'UPDATE_PROFILE'; payload: Partial<User> }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null };

const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'LOGIN_START':
      return { ...state, isLoading: true, error: null };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'LOGIN_FAILURE':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    case 'UPDATE_PROFILE':
      return {
        ...state,
        user: state.user ? { ...state.user, ...action.payload } : null,
      };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    default:
      return state;
  }
};

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // تحميل بيانات المستخدم من localStorage عند بدء التطبيق
  useEffect(() => {
    const loadUserFromStorage = () => {
      try {
        if (typeof window === 'undefined') return;

        // التحقق من النظام الجديد أولاً
        const isLoggedIn = localStorage.getItem('isLoggedIn');
        const currentUser = localStorage.getItem('currentUser');

        if (isLoggedIn === 'true' && currentUser) {
          const userData = JSON.parse(currentUser);

          // تحويل البيانات إلى تنسيق User
          const user: User = {
            id: userData.id || 'user-mahmut-001',
            email: userData.email || '<EMAIL>',
            phone: '+963988652401',
            name: userData.name || 'Mahmut Madenli',
            userType: userData.userType || 'individual',
            isVerified: true,
            createdAt: new Date(userData.createdAt || '2024-01-15'),
            lastLogin: new Date(),
            stats: {
              totalAds: 12,
              activeAds: 8,
              expiredAds: 4,
              totalViews: 2450,
              totalContacts: 89,
              successfulDeals: 15,
            },
            settings: {
              language: 'ar',
              notifications: {
                email: true,
                sms: true,
                push: true,
                marketing: false,
              },
              privacy: {
                showPhone: true,
                showEmail: false,
                allowMessages: true,
                showOnlineStatus: true,
              },
              preferences: {
                currency: 'SYP',
                theme: 'light',
                autoSave: true,
              },
            },
            individualInfo: {
              firstName: userData.individualInfo?.firstName || 'Mahmut',
              lastName: userData.individualInfo?.lastName || 'Madenli',
              gender: 'male',
              address: {
                governorate: 'دمشق',
                city: 'دمشق',
                area: 'المزة',
              },
            },
            subscription: {
              planId: userData.subscriptionPlan || 'premium',
              planName: 'الباقة المميزة',
              planType: 'individual',
              startDate: new Date('2024-01-15'),
              endDate: new Date('2024-12-31'),
              isActive: true,
              autoRenew: true,
              features: ['15 إعلان شهرياً', 'إعلانات مميزة', 'دعم فني'],
            },
          };

          dispatch({ type: 'LOGIN_SUCCESS', payload: user });
          return;
        }

        // النظام القديم
        const sessionData = localStorage.getItem('userSession');
        if (sessionData) {
          const session: UserSession = JSON.parse(sessionData);

          // التحقق من انتهاء صلاحية الجلسة
          if (new Date(session.expiresAt) > new Date()) {
            dispatch({ type: 'LOGIN_SUCCESS', payload: session.user });
          } else {
            localStorage.removeItem('userSession');
          }
        }
      } catch (error) {
        console.error('Error loading user session:', error);
        if (typeof window !== 'undefined') {
          localStorage.removeItem('userSession');
          localStorage.removeItem('isLoggedIn');
          localStorage.removeItem('currentUser');
        }
      }
    };

    loadUserFromStorage();
  }, []);

  const login = async (credentials: LoginCredentials) => {
    dispatch({ type: 'LOGIN_START' });

    try {
      // التحقق من بيانات Mahmut Madenli
      if (credentials.email === '<EMAIL>' && credentials.password === 'Ma123456') {
        const mahmutUser: User = {
          id: 'user-mahmut-001',
          email: '<EMAIL>',
          phone: '+963988652401',
          name: 'Mahmut Madenli',
          userType: 'individual',
          isVerified: true,
          createdAt: new Date('2024-01-15'),
          lastLogin: new Date(),
          stats: {
            totalAds: 12,
            activeAds: 8,
            expiredAds: 4,
            totalViews: 2450,
            totalContacts: 89,
            successfulDeals: 15,
          },
          settings: {
            language: 'ar',
            notifications: {
              email: true,
              sms: true,
              push: true,
              marketing: false,
            },
            privacy: {
              showPhone: true,
              showEmail: false,
              allowMessages: true,
              showOnlineStatus: true,
            },
            preferences: {
              currency: 'SYP',
              theme: 'light',
              autoSave: true,
            },
          },
          individualInfo: {
            firstName: 'Mahmut',
            lastName: 'Madenli',
            gender: 'male',
            address: {
              governorate: 'دمشق',
              city: 'دمشق',
              area: 'المزة',
            },
          },
          subscription: {
            planId: 'premium',
            planName: 'الباقة المميزة',
            planType: 'individual',
            startDate: new Date('2024-01-15'),
            endDate: new Date('2024-12-31'),
            isActive: true,
            autoRenew: true,
            features: ['15 إعلان شهرياً', 'إعلانات مميزة', 'دعم فني'],
          },
        };

        // حفظ في النظام الجديد
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('currentUser', JSON.stringify({
          id: mahmutUser.id,
          name: mahmutUser.name,
          email: mahmutUser.email,
          userType: mahmutUser.userType,
          individualInfo: mahmutUser.individualInfo,
          membershipId: 'MIN-2024-001247',
          createdAt: mahmutUser.createdAt,
          subscriptionPlan: 'premium',
          verificationBadge: 'silver'
        }));

        dispatch({ type: 'LOGIN_SUCCESS', payload: mahmutUser });
        return;
      }

      // التحقق من بيانات الشركة
      if (credentials.email === '<EMAIL>' && credentials.password === 'Gr123456') {
        const businessUser: User = {
          id: 'user-business-001',
          email: '<EMAIL>',
          phone: '+963988652401',
          name: 'Grand Mark Turkey',
          userType: 'business',
          isVerified: true,
          createdAt: new Date('2024-01-10'),
          lastLogin: new Date(),
          stats: {
            totalAds: 25,
            activeAds: 18,
            expiredAds: 7,
            totalViews: 5200,
            totalContacts: 156,
            successfulDeals: 32,
          },
          settings: {
            language: 'ar',
            notifications: {
              email: true,
              sms: true,
              push: true,
              marketing: true,
            },
            privacy: {
              showPhone: true,
              showEmail: true,
              allowMessages: true,
              showOnlineStatus: true,
            },
            preferences: {
              currency: 'SYP',
              theme: 'light',
              autoSave: true,
            },
          },
          businessInfo: {
            companyName: 'Grand Mark Turkey',
            businessType: 'استيراد وتصدير',
            registrationNumber: 'GMT123456',
            taxNumber: 'TAX789012',
            establishedYear: 2018,
            employeeCount: '50-100',
            website: 'https://grandmarkturkey.com',
            description: 'شركة رائدة في مجال الاستيراد والتصدير مع تركيا',
            address: {
              governorate: 'دمشق',
              city: 'دمشق',
              area: 'المزة',
              street: 'شارع التجارة',
              building: 'مبنى التجارة الدولية',
            },
            contactPerson: {
              name: 'أحمد محمد',
              position: 'المدير العام',
              phone: '+963988652401',
              email: '<EMAIL>',
            },
          },
          subscription: {
            planId: 'business-professional',
            planName: 'الخطة المهنية',
            planType: 'business',
            startDate: new Date('2024-01-10'),
            endDate: new Date('2025-01-10'),
            isActive: true,
            autoRenew: true,
            features: ['إعلانات غير محدودة', 'دعم أولوية', 'تحليلات متقدمة', 'شارة التوثيق الذهبية'],
          },
        };

        const session: UserSession = {
          token: 'mock-token-business',
          refreshToken: 'mock-refresh-token-business',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
          user: businessUser,
        };

        localStorage.setItem('userSession', JSON.stringify(session));
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('currentUser', JSON.stringify(businessUser));

        dispatch({ type: 'LOGIN_SUCCESS', payload: businessUser });
        return;
      }

      // التحقق من بيانات المكتب العقاري
      if (credentials.email === '<EMAIL>' && credentials.password === 'Du123456') {
        const realEstateUser: User = {
          id: 'user-realestate-001',
          email: '<EMAIL>',
          phone: '+963988652401',
          name: 'مكتب دنيا زاد العقاري',
          userType: 'real-estate-office',
          isVerified: true,
          createdAt: new Date('2024-01-05'),
          lastLogin: new Date(),
          stats: {
            totalAds: 45,
            activeAds: 32,
            expiredAds: 13,
            totalViews: 8500,
            totalContacts: 245,
            successfulDeals: 58,
          },
          settings: {
            language: 'ar',
            notifications: {
              email: true,
              sms: true,
              push: true,
              marketing: true,
            },
            privacy: {
              showPhone: true,
              showEmail: true,
              allowMessages: true,
              showOnlineStatus: true,
            },
            preferences: {
              currency: 'SYP',
              theme: 'light',
              autoSave: true,
            },
          },
          realEstateOfficeInfo: {
            officeName: 'مكتب دنيا زاد العقاري',
            licenseNumber: 'RE123456',
            establishedYear: 2020,
            specialization: ['بيع', 'شراء', 'إيجار', 'استثمار عقاري'],
            serviceAreas: ['دمشق', 'ريف دمشق', 'حلب'],
            ownerName: 'محمد دنيا زاد',
            managerName: 'أحمد دنيا زاد',
            yearsOfExperience: 8,
            teamSize: 12,
            description: 'مكتب عقاري متخصص في جميع أنواع العقارات السكنية والتجارية',
            address: {
              governorate: 'دمشق',
              city: 'دمشق',
              area: 'المالكي',
              street: 'شارع العقارات',
              building: 'مبنى دنيا زاد',
            },
          },
          subscription: {
            planId: 'real-estate-office',
            planName: 'خطة المكتب العقاري',
            planType: 'business',
            startDate: new Date('2024-01-05'),
            endDate: new Date('2025-01-05'),
            isActive: true,
            autoRenew: true,
            features: ['إعلانات عقارية غير محدودة', 'أدوات تحليل السوق', 'دعم متخصص', 'شارة التوثيق الفضية'],
          },
        };

        const session: UserSession = {
          token: 'mock-token-realestate',
          refreshToken: 'mock-refresh-token-realestate',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
          user: realEstateUser,
        };

        localStorage.setItem('userSession', JSON.stringify(session));
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('currentUser', JSON.stringify(realEstateUser));

        dispatch({ type: 'LOGIN_SUCCESS', payload: realEstateUser });
        return;
      }

      // محاكاة API call للمستخدمين الآخرين
      await new Promise(resolve => setTimeout(resolve, 1000));

      // بيانات مستخدم تجريبية
      const mockUser: User = {
        id: 'user-' + Date.now(),
        email: credentials.email,
        phone: '+963988123456',
        name: credentials.email.split('@')[0],
        userType: credentials.email.includes('business') ? 'business' :
                  credentials.email.includes('office') ? 'real-estate-office' : 'individual',
        isVerified: true,
        createdAt: new Date(),
        lastLogin: new Date(),
        stats: {
          totalAds: 5,
          activeAds: 3,
          expiredAds: 2,
          totalViews: 1250,
          totalContacts: 45,
          successfulDeals: 8,
        },
        settings: {
          language: 'ar',
          notifications: {
            email: true,
            sms: true,
            push: true,
            marketing: false,
          },
          privacy: {
            showPhone: true,
            showEmail: false,
            allowMessages: true,
            showOnlineStatus: true,
          },
          preferences: {
            currency: 'SYP',
            theme: 'light',
            autoSave: true,
          },
        },
      };

      // إضافة معلومات حسب نوع المستخدم
      if (mockUser.userType === 'individual') {
        mockUser.individualInfo = {
          firstName: 'أحمد',
          lastName: 'محمد',
          gender: 'male',
          address: {
            governorate: 'دمشق',
            city: 'دمشق',
            area: 'المزة',
          },
        };
      } else if (mockUser.userType === 'business') {
        mockUser.businessInfo = {
          companyName: 'شركة التجارة المتقدمة',
          businessType: 'تجارة عامة',
          registrationNumber: 'REG123456',
          establishedYear: 2015,
          employeeCount: '10-50',
          address: {
            governorate: 'دمشق',
            city: 'دمشق',
            area: 'المزة',
            street: 'شارع الثورة',
          },
          contactPerson: {
            name: 'محمد أحمد',
            position: 'مدير عام',
            phone: '+963988123456',
            email: credentials.email,
          },
        };
        mockUser.subscription = {
          planId: 'business-starter',
          planName: 'باقة البداية',
          planType: 'business',
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          isActive: true,
          autoRenew: true,
          features: ['50 إعلان شهرياً', 'دعم فني', 'إحصائيات متقدمة'],
        };
      } else if (mockUser.userType === 'real-estate-office') {
        mockUser.realEstateOfficeInfo = {
          officeName: 'مكتب العقارات المتميز',
          licenseNumber: 'LIC789012',
          licenseIssueDate: new Date('2020-01-01'),
          licenseExpiryDate: new Date('2025-01-01'),
          ownerName: 'خالد السوري',
          specializations: ['شقق سكنية', 'فيلات', 'محلات تجارية'],
          serviceAreas: ['دمشق', 'ريف دمشق'],
          yearsOfExperience: 10,
          teamSize: 5,
          address: {
            governorate: 'دمشق',
            city: 'دمشق',
            area: 'المزة',
            street: 'شارع المتنبي',
            building: 'بناء رقم 15',
            floor: 'الطابق الثاني',
          },
          workingHours: {
            sunday: { open: '09:00', close: '17:00', isOpen: true },
            monday: { open: '09:00', close: '17:00', isOpen: true },
            tuesday: { open: '09:00', close: '17:00', isOpen: true },
            wednesday: { open: '09:00', close: '17:00', isOpen: true },
            thursday: { open: '09:00', close: '17:00', isOpen: true },
            friday: { open: '09:00', close: '12:00', isOpen: true },
            saturday: { open: '09:00', close: '17:00', isOpen: true },
          },
        };
        mockUser.subscription = {
          planId: 'real-estate-office',
          planName: 'باقة المكتب العقاري',
          planType: 'business',
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
          isActive: true,
          autoRenew: true,
          features: ['30 إعلان شهرياً', 'شارة موثقة', 'أولوية في النتائج', 'دعم فني مخصص'],
        };
      }

      // حفظ الجلسة
      const session: UserSession = {
        token: 'mock-token-' + Date.now(),
        refreshToken: 'mock-refresh-token-' + Date.now(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 ساعة
        user: mockUser,
      };

      if (credentials.rememberMe) {
        localStorage.setItem('userSession', JSON.stringify(session));
      }

      dispatch({ type: 'LOGIN_SUCCESS', payload: mockUser });
    } catch (error) {
      dispatch({ type: 'LOGIN_FAILURE', payload: 'فشل في تسجيل الدخول' });
    }
  };

  const register = async (data: RegisterData) => {
    dispatch({ type: 'LOGIN_START' });

    try {
      // محاكاة API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // التحقق من صحة البيانات
      if (data.password !== data.confirmPassword) {
        throw new Error('كلمات المرور غير متطابقة');
      }

      // إنشاء مستخدم جديد
      const newUser: User = {
        id: 'user-' + Date.now(),
        email: data.email,
        phone: data.phone,
        name: data.name,
        userType: data.userType,
        isVerified: false,
        createdAt: new Date(),
        lastLogin: new Date(),
        stats: {
          totalAds: 0,
          activeAds: 0,
          expiredAds: 0,
          totalViews: 0,
          totalContacts: 0,
          successfulDeals: 0,
        },
        settings: {
          language: 'ar',
          notifications: {
            email: true,
            sms: true,
            push: true,
            marketing: false,
          },
          privacy: {
            showPhone: true,
            showEmail: false,
            allowMessages: true,
            showOnlineStatus: true,
          },
          preferences: {
            currency: 'SYP',
            theme: 'light',
            autoSave: true,
          },
        },
        individualInfo: data.individualInfo,
        businessInfo: data.businessInfo,
        realEstateOfficeInfo: data.realEstateOfficeInfo,
      };

      dispatch({ type: 'LOGIN_SUCCESS', payload: newUser });
    } catch (error) {
      dispatch({ type: 'LOGIN_FAILURE', payload: error instanceof Error ? error.message : 'فشل في إنشاء الحساب' });
    }
  };

  const logout = () => {
    localStorage.removeItem('userSession');
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('currentUser');
    dispatch({ type: 'LOGOUT' });
  };

  const updateProfile = async (updates: Partial<User>) => {
    try {
      dispatch({ type: 'UPDATE_PROFILE', payload: updates });

      // تحديث الجلسة المحفوظة
      const sessionData = localStorage.getItem('userSession');
      if (sessionData) {
        const session: UserSession = JSON.parse(sessionData);
        session.user = { ...session.user, ...updates };
        localStorage.setItem('userSession', JSON.stringify(session));
      }
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'فشل في تحديث الملف الشخصي' });
    }
  };

  const refreshToken = async () => {
    // محاكاة تجديد الرمز المميز
    try {
      await new Promise(resolve => setTimeout(resolve, 500));
      // تحديث الجلسة
    } catch (error) {
      logout();
    }
  };

  const deleteAccount = async () => {
    try {
      // محاكاة حذف الحساب
      await new Promise(resolve => setTimeout(resolve, 1000));
      logout();
    } catch (error) {
      dispatch({ type: 'SET_ERROR', payload: 'فشل في حذف الحساب' });
    }
  };

  return (
    <AuthContext.Provider
      value={{
        ...state,
        login,
        register,
        logout,
        updateProfile,
        refreshToken,
        deleteAccount,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
