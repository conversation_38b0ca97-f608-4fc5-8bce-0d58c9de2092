'use client';

import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/useToast';
import { useAuth } from '@/contexts/AuthContext';
import { useMessageAction } from '@/hooks/useAuthAction';

interface MessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  recipientName: string;
  recipientPhone: string;
  adTitle: string;
  adId: number;
}

const MessageModal = ({
  isOpen,
  onClose,
  recipientName,
  recipientPhone,
  adTitle,
  adId
}: MessageModalProps) => {
  const { showSuccess, showError } = useToast();
  const { isAuthenticated } = useAuth();
  const { executeAsyncAction } = useMessageAction();
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [messageType, setMessageType] = useState<'inquiry' | 'offer' | 'custom'>('inquiry');
  const [offerAmount, setOfferAmount] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [includePhone, setIncludePhone] = useState(false);

  // رسائل جاهزة
  const predefinedMessages = {
    inquiry: [
      'مرحباً، أرغب في معرفة المزيد من التفاصيل حول هذا الإعلان.',
      'هل هذا العرض ما زال متاحاً؟',
      'يرجى إرسال المزيد من الصور والتفاصيل.',
      'متى يمكنني المعاينة؟',
      'ما هي شروط الدفع المتاحة؟'
    ],
    offer: [
      'أرغب في تقديم عرض لشراء هذا العقار/المنتج.',
      'هل تقبل التفاوض على السعر؟',
      'أرغب في الشراء نقداً، هل يوجد خصم؟'
    ],
    custom: []
  };

  useEffect(() => {
    if (isOpen) {
      setMessage('');
      setMessageType('inquiry');
      setOfferAmount('');
      setPhoneNumber('');
      setIncludePhone(false);
    }
  }, [isOpen]);

  const handleSendMessage = async () => {
    // التحقق من المصادقة أولاً
    const success = await executeAsyncAction(async () => {
      if (!message.trim()) {
        showError('يرجى كتابة رسالة');
        return;
      }

      if (messageType === 'offer' && !offerAmount.trim()) {
        showError('يرجى إدخال مبلغ العرض');
        return;
      }

    setIsSending(true);
    try {
      // محاكاة إرسال الرسالة
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      let finalMessage = message;
      if (messageType === 'offer' && offerAmount) {
        finalMessage += `\n\nالعرض المقترح: ${offerAmount} ل.س`;
      }
      if (includePhone && phoneNumber) {
        finalMessage += `\n\nرقم الهاتف: ${phoneNumber}`;
      }

      console.log('إرسال رسالة:', {
        to: recipientName,
        adId,
        adTitle,
        message: finalMessage,
        type: messageType
      });

      showSuccess('تم إرسال الرسالة بنجاح! 📨');
      onClose();
    } catch (error) {
      showError('حدث خطأ أثناء إرسال الرسالة');
      } finally {
        setIsSending(false);
      }
    });

    // إذا لم يكن المستخدم مصادق عليه، إغلاق النافذة
    if (!success) {
      onClose();
    }
  };

  const handleWhatsAppMessage = () => {
    let finalMessage = message;
    if (messageType === 'offer' && offerAmount) {
      finalMessage += `\n\nالعرض المقترح: ${offerAmount} ل.س`;
    }
    if (includePhone && phoneNumber) {
      finalMessage += `\n\nرقم الهاتف: ${phoneNumber}`;
    }
    
    const whatsappMessage = `مرحباً ${recipientName}،\n\nبخصوص إعلان: ${adTitle}\n\n${finalMessage}`;
    const phoneFormatted = recipientPhone.replace(/[^0-9]/g, '');
    const whatsappUrl = `https://wa.me/${phoneFormatted}?text=${encodeURIComponent(whatsappMessage)}`;
    window.open(whatsappUrl, '_blank');
  };

  const selectPredefinedMessage = (msg: string) => {
    setMessage(msg);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-t-2xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-bold">إرسال رسالة</h3>
                <p className="text-blue-100 text-sm">إلى: {recipientName}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
              </svg>
            </button>
          </div>
          <div className="mt-3 p-3 bg-white/10 rounded-lg">
            <p className="text-blue-100 text-sm font-medium">الإعلان: {adTitle}</p>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Message Type Selection */}
          <div className="mb-6">
            <h4 className="font-semibold text-gray-800 mb-3">نوع الرسالة</h4>
            <div className="flex gap-3">
              {[
                { key: 'inquiry', label: 'استفسار', icon: '❓' },
                { key: 'offer', label: 'عرض سعر', icon: '💰' },
                { key: 'custom', label: 'رسالة مخصصة', icon: '✏️' }
              ].map(({ key, label, icon }) => (
                <button
                  key={key}
                  onClick={() => setMessageType(key as any)}
                  className={`flex-1 p-3 rounded-lg border-2 transition-all duration-300 ${
                    messageType === key
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="text-center">
                    <div
                      className={`text-2xl mb-1 transition-all duration-300 ${
                        messageType === key
                          ? 'opacity-100 filter-none'
                          : 'opacity-50 grayscale'
                      }`}
                      style={{
                        filter: messageType === key
                          ? 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.5))'
                          : 'grayscale(1) opacity(0.5)'
                      }}
                    >
                      {icon}
                    </div>
                    <div className="text-sm font-medium">{label}</div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Predefined Messages */}
          {messageType !== 'custom' && predefinedMessages[messageType].length > 0 && (
            <div className="mb-6">
              <h4 className="font-semibold text-gray-800 mb-3">رسائل جاهزة</h4>
              <div className="space-y-2">
                {predefinedMessages[messageType].map((msg, index) => (
                  <button
                    key={index}
                    onClick={() => selectPredefinedMessage(msg)}
                    className="w-full text-right p-3 bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors text-sm"
                  >
                    {msg}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Offer Amount */}
          {messageType === 'offer' && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                مبلغ العرض (ل.س)
              </label>
              <input
                type="number"
                value={offerAmount}
                onChange={(e) => setOfferAmount(e.target.value)}
                placeholder="أدخل مبلغ العرض"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}

          {/* Message Input */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              نص الرسالة
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="اكتب رسالتك هنا..."
              rows={5}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
            />
          </div>

          {/* Phone Number Option */}
          <div className="mb-6">
            <div className="flex items-center gap-3 mb-3">
              <input
                type="checkbox"
                id="includePhone"
                checked={includePhone}
                onChange={(e) => setIncludePhone(e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="includePhone" className="text-sm font-medium text-gray-700">
                إضافة رقم الهاتف للرسالة
              </label>
            </div>
            {includePhone && (
              <input
                type="tel"
                value={phoneNumber}
                onChange={(e) => setPhoneNumber(e.target.value)}
                placeholder="رقم الهاتف"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              onClick={handleSendMessage}
              disabled={isSending}
              className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isSending ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  جاري الإرسال...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                  </svg>
                  إرسال الرسالة
                </>
              )}
            </button>
            
            <button
              onClick={handleWhatsAppMessage}
              className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.785"/>
              </svg>
              واتساب
            </button>
            
            <button
              onClick={onClose}
              className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              إلغاء
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MessageModal;
