import Link from 'next/link';
import { JobPosting, JobUtils } from '@/lib/jobs';
import { AdBadge } from '@/components/VerificationBadge';
import { determineUserBadge } from '@/lib/verification';

interface JobCardProps {
  job: JobPosting;
  onSave?: (jobId: string) => void;
  onApply?: (jobId: string) => void;
  isSaved?: boolean;
  showCompanyBadge?: boolean;
}

const JobCard = ({ job, onSave, onApply, isSaved = false, showCompanyBadge = true }: JobCardProps) => {
  const handleSave = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onSave?.(job.id);
  };

  const handleApply = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    onApply?.(job.id);
  };

  // تحديد شارة الشركة (مؤقت - سيتم ربطه بنظام الشركات)
  const companyBadges = determineUserBadge('business-professional', 50, 4.5, 12, true);

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-all duration-300 group">
      <Link href={`/jobs/${job.id}`} className="block p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              {job.companyLogo && (
                <img
                  src={job.companyLogo}
                  alt={job.companyName}
                  className="w-12 h-12 rounded-lg object-cover"
                />
              )}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 group-hover:text-primary-600 transition-colors">
                  {job.title}
                </h3>
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">{job.companyName}</span>
                  {showCompanyBadge && (
                    <AdBadge userBadges={companyBadges} size="xs" />
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Badges */}
          <div className="flex flex-col gap-2">
            {job.featured && (
              <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full font-medium">
                ⭐ مميز
              </span>
            )}
            {job.urgent && (
              <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium">
                🔥 عاجل
              </span>
            )}
          </div>
        </div>

        {/* Job Details */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span>📍</span>
            <span>{job.location}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span>💼</span>
            <span>{job.workType}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <span>🏠</span>
            <span>{job.workModel}</span>
          </div>
        </div>

        {/* Salary */}
        <div className="mb-4">
          <div className="text-lg font-bold text-primary-600">
            {JobUtils.formatSalaryRange(job.salaryRange)}
          </div>
        </div>

        {/* Description Preview */}
        <p className="text-gray-700 text-sm mb-4 line-clamp-2">
          {job.description}
        </p>

        {/* Skills */}
        <div className="flex flex-wrap gap-2 mb-4">
          {job.skills.slice(0, 4).map((skill, index) => (
            <span
              key={index}
              className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full"
            >
              {skill}
            </span>
          ))}
          {job.skills.length > 4 && (
            <span className="bg-gray-100 text-gray-500 text-xs px-2 py-1 rounded-full">
              +{job.skills.length - 4} أخرى
            </span>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center gap-4 text-sm text-gray-500">
            <span>{JobUtils.getDaysAgo(job.postedDate)}</span>
            <span>👥 {job.applicationsCount} متقدم</span>
            <span>📊 {job.experienceLevel}</span>
          </div>

          <div className="flex items-center gap-2">
            {/* Save Button */}
            <button
              onClick={handleSave}
              className={`p-2 rounded-full transition-colors ${
                isSaved
                  ? 'bg-primary-100 text-primary-600'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              title={isSaved ? 'محفوظ' : 'حفظ الوظيفة'}
            >
              {isSaved ? '❤️' : '🤍'}
            </button>

            {/* Apply Button */}
            <button
              onClick={handleApply}
              className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors text-sm font-medium"
            >
              تقدم الآن
            </button>
          </div>
        </div>
      </Link>
    </div>
  );
};

// مكون مبسط للوظائف في القوائم
export const JobCardCompact = ({ job }: { job: JobPosting }) => {
  return (
    <Link
      href={`/jobs/${job.id}`}
      className="block bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200 group"
    >
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <h4 className="font-medium text-gray-800 group-hover:text-primary-600 transition-colors">
              {job.title}
            </h4>
            {job.featured && <span className="text-yellow-500">⭐</span>}
            {job.urgent && <span className="text-red-500">🔥</span>}
          </div>
          
          <div className="text-sm text-gray-600 mb-2">
            {job.companyName} • {job.location}
          </div>
          
          <div className="flex items-center gap-4 text-xs text-gray-500">
            <span>{job.workType}</span>
            <span>{JobUtils.getDaysAgo(job.postedDate)}</span>
            <span>{job.applicationsCount} متقدم</span>
          </div>
        </div>

        <div className="text-right">
          <div className="text-sm font-semibold text-primary-600 mb-1">
            {JobUtils.formatSalaryRange(job.salaryRange)}
          </div>
          <div className="text-xs text-gray-500">
            {job.experienceLevel}
          </div>
        </div>
      </div>
    </Link>
  );
};

// مكون للوظائف المميزة
export const FeaturedJobCard = ({ job }: { job: JobPosting }) => {
  return (
    <div className="bg-gradient-to-r from-primary-50 to-blue-50 border-2 border-primary-200 rounded-xl p-6 relative overflow-hidden">
      {/* Featured Badge */}
      <div className="absolute top-0 right-0 bg-yellow-400 text-yellow-900 px-3 py-1 rounded-bl-lg text-xs font-bold">
        ⭐ مميز
      </div>

      <Link href={`/jobs/${job.id}`} className="block">
        <div className="flex items-start gap-4">
          {job.companyLogo && (
            <img
              src={job.companyLogo}
              alt={job.companyName}
              className="w-16 h-16 rounded-xl object-cover"
            />
          )}
          
          <div className="flex-1">
            <h3 className="text-xl font-bold text-gray-800 mb-2 hover:text-primary-600 transition-colors">
              {job.title}
            </h3>
            
            <div className="flex items-center gap-2 mb-3">
              <span className="text-gray-700 font-medium">{job.companyName}</span>
              <AdBadge userBadges={determineUserBadge('business-professional', 50, 4.5, 12, true)} size="xs" />
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 text-sm">
              <div className="flex items-center gap-1 text-gray-600">
                <span>📍</span>
                <span>{job.location}</span>
              </div>
              <div className="flex items-center gap-1 text-gray-600">
                <span>💼</span>
                <span>{job.workType}</span>
              </div>
              <div className="flex items-center gap-1 text-gray-600">
                <span>🏠</span>
                <span>{job.workModel}</span>
              </div>
              <div className="flex items-center gap-1 text-gray-600">
                <span>📊</span>
                <span>{job.experienceLevel}</span>
              </div>
            </div>

            <div className="text-xl font-bold text-primary-600 mb-3">
              {JobUtils.formatSalaryRange(job.salaryRange)}
            </div>

            <p className="text-gray-700 mb-4 line-clamp-2">
              {job.description}
            </p>

            <div className="flex flex-wrap gap-2 mb-4">
              {job.skills.slice(0, 5).map((skill, index) => (
                <span
                  key={index}
                  className="bg-white text-primary-700 text-xs px-3 py-1 rounded-full border border-primary-200"
                >
                  {skill}
                </span>
              ))}
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                {JobUtils.getDaysAgo(job.postedDate)} • {job.applicationsCount} متقدم
              </div>
              
              <button className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors font-medium">
                تقدم الآن
              </button>
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
};

export default JobCard;
