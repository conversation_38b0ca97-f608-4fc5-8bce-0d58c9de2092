'use client';

import { useState } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ContactButtons from '@/components/ContactButtons';
import { Resume } from '@/lib/jobs';
import { AdBadge } from '@/components/VerificationBadge';
import { determineUserBadge } from '@/lib/verification';

// بيانات تجريبية للمرشحين
const sampleCandidates: Resume[] = [
  {
    id: '1',
    userId: 'user1',
    personalInfo: {
      firstName: 'أحمد',
      lastName: 'محمد',
      title: 'مطور ويب متقدم',
      summary: 'مطور ويب متخصص في React.js و Node.js مع خبرة 5 سنوات في تطوير التطبيقات الحديثة والمتجاوبة.',
      nationality: 'سوري',
      maritalStatus: 'متزوج'
    },
    contactInfo: {
      phone: '+*********** 401',
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/ahmed-mohamed',
      github: 'https://github.com/ahmed-mohamed',
      city: 'دمشق'
    },
    experiences: [
      {
        id: '1',
        jobTitle: 'مطور ويب أول',
        company: 'شركة التقنيات المتقدمة',
        location: 'دمشق',
        startDate: '2021-03',
        current: true,
        description: 'تطوير وصيانة تطبيقات الويب باستخدام React.js و Node.js'
      }
    ],
    education: [
      {
        id: '1',
        degree: 'بكالوريوس',
        institution: 'جامعة دمشق',
        field: 'هندسة الحاسوب',
        location: 'دمشق',
        startDate: '2015-09',
        endDate: '2019-06',
        current: false
      }
    ],
    skills: [
      { id: '1', name: 'React.js', level: 'خبير', category: 'تقني', verified: true },
      { id: '2', name: 'Node.js', level: 'متقدم', category: 'تقني', verified: true },
      { id: '3', name: 'TypeScript', level: 'متقدم', category: 'تقني', verified: false }
    ],
    languages: [
      { id: '1', name: 'العربية', level: 'أصلي' },
      { id: '2', name: 'الإنجليزية', level: 'متقدم', certification: 'IELTS 7.0' }
    ],
    courses: [],
    createdAt: '2024-01-15',
    updatedAt: '2024-02-15',
    isPublic: true,
    views: 156
  },
  {
    id: '2',
    userId: 'user2',
    personalInfo: {
      firstName: 'سارة',
      lastName: 'أحمد',
      title: 'مصممة UX/UI',
      summary: 'مصممة UX/UI مبدعة مع خبرة 4 سنوات في تصميم واجهات المستخدم الحديثة والتفاعلية.',
      nationality: 'سورية',
      maritalStatus: 'عزباء'
    },
    contactInfo: {
      phone: '+*********** 456',
      email: '<EMAIL>',
      linkedin: 'https://linkedin.com/in/sara-ahmed',
      city: 'حلب'
    },
    experiences: [
      {
        id: '1',
        jobTitle: 'مصممة UX/UI أولى',
        company: 'استوديو الإبداع الرقمي',
        location: 'حلب',
        startDate: '2020-06',
        current: true,
        description: 'تصميم واجهات المستخدم وتجربة المستخدم للتطبيقات الجوالة والويب'
      }
    ],
    education: [
      {
        id: '1',
        degree: 'بكالوريوس',
        institution: 'جامعة حلب',
        field: 'فنون جميلة - تصميم جرافيك',
        location: 'حلب',
        startDate: '2016-09',
        endDate: '2020-06',
        current: false
      }
    ],
    skills: [
      { id: '1', name: 'Figma', level: 'خبير', category: 'تقني', verified: true },
      { id: '2', name: 'Adobe XD', level: 'متقدم', category: 'تقني', verified: true },
      { id: '3', name: 'Sketch', level: 'متوسط', category: 'تقني', verified: false }
    ],
    languages: [
      { id: '1', name: 'العربية', level: 'أصلي' },
      { id: '2', name: 'الإنجليزية', level: 'متوسط' }
    ],
    courses: [],
    createdAt: '2024-01-20',
    updatedAt: '2024-02-10',
    isPublic: true,
    views: 89
  }
];

export default function CandidatesPage() {
  const [candidates] = useState<Resume[]>(sampleCandidates);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState({
    location: '',
    experienceLevel: '',
    skills: '',
    availability: 'all'
  });

  const CandidateCard = ({ candidate }: { candidate: Resume }) => {
    const userBadges = determineUserBadge('individual', 1, 4.5, 2, false);

    return (
      <div className="bg-white rounded-lg shadow-md border border-gray-200 hover:shadow-lg transition-all duration-300 p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-4">
            <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center">
              <span className="text-2xl">👤</span>
            </div>
            <div>
              <div className="flex items-center gap-2 mb-1">
                <h3 className="text-lg font-semibold text-gray-800">
                  {candidate.personalInfo.firstName} {candidate.personalInfo.lastName}
                </h3>
                <AdBadge userBadges={userBadges} size="xs" />
              </div>
              <p className="text-primary-600 font-medium">{candidate.personalInfo.title}</p>
              <p className="text-gray-600 text-sm">📍 {candidate.contactInfo.city}</p>
            </div>
          </div>

          <div className="text-right">
            <div className="text-sm text-gray-500 mb-1">{candidate.views} مشاهدة</div>
            <div className="text-xs text-gray-400">آخر تحديث: {candidate.updatedAt}</div>
          </div>
        </div>

        <p className="text-gray-700 text-sm mb-4 line-clamp-2">
          {candidate.personalInfo.summary}
        </p>

        {/* Skills */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">المهارات الرئيسية:</h4>
          <div className="flex flex-wrap gap-2">
            {candidate.skills.slice(0, 4).map((skill) => (
              <span
                key={skill.id}
                className={`text-xs px-2 py-1 rounded-full ${
                  skill.verified
                    ? 'bg-green-100 text-green-700'
                    : 'bg-gray-100 text-gray-700'
                }`}
              >
                {skill.name} {skill.verified && '✓'}
              </span>
            ))}
            {candidate.skills.length > 4 && (
              <span className="text-xs text-gray-500 px-2 py-1">
                +{candidate.skills.length - 4} أخرى
              </span>
            )}
          </div>
        </div>

        {/* Experience */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">الخبرة الحالية:</h4>
          {candidate.experiences.length > 0 && (
            <div className="text-sm text-gray-600">
              <span className="font-medium">{candidate.experiences[0].jobTitle}</span>
              <span className="mx-1">في</span>
              <span>{candidate.experiences[0].company}</span>
              {candidate.experiences[0].current && (
                <span className="text-green-600 mr-2">• حالياً</span>
              )}
            </div>
          )}
        </div>

        {/* Languages */}
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-700 mb-2">اللغات:</h4>
          <div className="flex gap-2">
            {candidate.languages.map((lang) => (
              <span key={lang.id} className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                {lang.name} ({lang.level})
              </span>
            ))}
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-3">
          <Link
            href={`/resume/preview/${candidate.id}`}
            className="flex-1 bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors text-center text-sm font-medium"
          >
            عرض السيرة الذاتية
          </Link>
          <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-colors text-sm">
            💬 تواصل
          </button>
          <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 transition-colors text-sm">
            ⭐ حفظ
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            🔍 البحث عن المواهب
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            اكتشف أفضل المواهب والمرشحين المؤهلين لشركتك من قاعدة بيانات شاملة
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-xl shadow-md p-6 mb-8">
          <div className="space-y-6">
            {/* Search Bar */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">البحث في المواهب</label>
              <div className="flex gap-4">
                <input
                  type="text"
                  placeholder="ابحث بالاسم، المسمى الوظيفي، المهارات..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
                <button className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors">
                  🔍 بحث
                </button>
              </div>
            </div>

            {/* Filters */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                <select
                  value={filters.location}
                  onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">جميع المواقع</option>
                  <option value="دمشق">دمشق</option>
                  <option value="حلب">حلب</option>
                  <option value="حمص">حمص</option>
                  <option value="حماة">حماة</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">مستوى الخبرة</label>
                <select
                  value={filters.experienceLevel}
                  onChange={(e) => setFilters(prev => ({ ...prev, experienceLevel: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="">جميع المستويات</option>
                  <option value="مبتدئ">مبتدئ</option>
                  <option value="متوسط">متوسط</option>
                  <option value="متقدم">متقدم</option>
                  <option value="خبير">خبير</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المهارات</label>
                <input
                  type="text"
                  placeholder="React, Node.js, Python..."
                  value={filters.skills}
                  onChange={(e) => setFilters(prev => ({ ...prev, skills: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">التوفر</label>
                <select
                  value={filters.availability}
                  onChange={(e) => setFilters(prev => ({ ...prev, availability: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="all">الجميع</option>
                  <option value="available">متاح للعمل</option>
                  <option value="open">مفتوح للفرص</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-primary-600 mb-2">{candidates.length}</div>
            <div className="text-gray-600">مرشح متاح</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-green-600 mb-2">85%</div>
            <div className="text-gray-600">معدل الاستجابة</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-blue-600 mb-2">24</div>
            <div className="text-gray-600">ساعة متوسط الرد</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-purple-600 mb-2">92%</div>
            <div className="text-gray-600">معدل التوظيف</div>
          </div>
        </div>

        {/* Results */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-800">نتائج البحث</h2>
          <div className="flex items-center gap-4">
            <span className="text-gray-600">{candidates.length} مرشح</span>
            <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
              <option>الأحدث</option>
              <option>الأكثر مشاهدة</option>
              <option>الأعلى تقييماً</option>
            </select>
          </div>
        </div>

        {/* Candidates Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {candidates.map(candidate => (
            <CandidateCard key={candidate.id} candidate={candidate} />
          ))}
        </div>

        {/* Upgrade CTA */}
        <div className="bg-gradient-to-r from-primary-600 to-blue-600 rounded-2xl text-white p-8 text-center">
          <h3 className="text-2xl font-bold mb-4">🚀 احصل على وصول كامل لقاعدة المواهب</h3>
          <p className="text-xl mb-6 text-primary-100">
            ترقية حسابك للوصول لآلاف المرشحين المؤهلين وأدوات البحث المتقدمة
          </p>
          <div className="flex justify-center gap-4">
            <Link
              href="/company/pricing"
              className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              عرض الخطط
            </Link>
            <Link
              href="/contact"
              className="bg-primary-700 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-800 transition-colors border border-primary-500"
            >
              تواصل معنا
            </Link>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
