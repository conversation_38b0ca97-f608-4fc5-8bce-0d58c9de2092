'use client';

import { useState } from 'react';

interface BooksEducationData {
  category: string;
  subCategory: string;
  educationLevel: string;
  subject: string;
  language: string;
  condition: string;
  format: string;
  duration: string;
  location: string;
  onlineAvailable: boolean;
  groupSize: string;
  description: string;
}

interface BooksEducationSpecificFieldsProps {
  data: BooksEducationData;
  onChange: (data: BooksEducationData) => void;
}

const booksEducationCategories = {
  'books-references': {
    name: 'الكتب والمراجع',
    subCategories: {
      'school-books': {
        name: 'كتب مدرسية',
        items: ['ابتدائي', 'إعدادي', 'ثانوي']
      },
      'university-books': {
        name: 'كتب جامعية',
        items: ['طب', 'هندسة', 'حقوق', 'اقتصاد', 'آداب', 'علوم', 'تربية']
      },
      'language-books': {
        name: 'كتب تعليم اللغات',
        items: ['إنكليزي', 'تركي', 'فرنسي', 'ألماني', 'عربي لغير الناطقين']
      },
      'children-books': {
        name: 'كتب أطفال وقصص مصورة',
        items: ['قصص مصورة', 'كتب تعليمية للأطفال', 'كتب تلوين']
      },
      'academic-references': {
        name: 'مراجع علمية وأكاديمية',
        items: ['أبحاث', 'أطروحات', 'مراجع علمية']
      },
      'religious-books': {
        name: 'كتب دينية',
        items: ['تفاسير', 'فقه', 'تاريخ إسلامي', 'أحاديث']
      },
      'self-development': {
        name: 'كتب تنمية بشرية وتطوير الذات',
        items: ['تطوير الذات', 'إدارة الوقت', 'القيادة', 'التحفيز']
      },
      'ebooks': {
        name: 'كتب إلكترونية',
        items: ['E-books', 'نسخ PDF', 'كتب صوتية']
      },
      'magazines': {
        name: 'مجلات وصحف',
        items: ['مجلات علمية', 'صحف قديمة', 'مجلات أطفال']
      },
      'literature': {
        name: 'روايات وأدب',
        items: ['روايات عربية', 'أدب عالمي', 'شعر', 'مسرح']
      }
    }
  },
  'private-lessons': {
    name: 'الدروس الخصوصية',
    subCategories: {
      'elementary': {
        name: 'دروس خصوصية للمرحلة الابتدائية',
        items: ['رياضيات', 'عربي', 'إنكليزي', 'علوم', 'جميع المواد']
      },
      'middle-school': {
        name: 'دروس خصوصية للمرحلة الإعدادية',
        items: ['رياضيات', 'فيزياء', 'كيمياء', 'أحياء', 'عربي', 'إنكليزي', 'تاريخ', 'جغرافيا']
      },
      'high-school': {
        name: 'دروس خصوصية للمرحلة الثانوية',
        items: ['علمي - رياضيات', 'علمي - فيزياء', 'علمي - كيمياء', 'أدبي - تاريخ', 'أدبي - جغرافيا', 'تجاري', 'صناعي']
      },
      'remedial': {
        name: 'دروس تقوية للطلاب المتعثرين',
        items: ['صعوبات تعلم', 'تقوية عامة', 'مراجعة امتحانات']
      },
      'university': {
        name: 'كورسات جامعية',
        items: ['رياضيات', 'فيزياء', 'كيمياء', 'طب', 'هندسة', 'برمجة']
      },
      'languages': {
        name: 'دروس لغات',
        items: ['إنكليزي', 'فرنسي', 'تركي', 'ألماني', 'روسي', 'إسباني']
      },
      'exam-prep': {
        name: 'تحضير لاختبارات القبول الجامعي',
        items: ['اختبارات محلية', 'اختبارات دولية']
      },
      'international-certs': {
        name: 'تحضير لشهادات دولية',
        items: ['TOEFL', 'IELTS', 'SAT', 'YÖS', 'GRE', 'GMAT']
      }
    }
  },
  'technical-education': {
    name: 'التعليم الفني والمهني',
    subCategories: {
      'crafts': {
        name: 'تعليم الحرف',
        items: ['نجارة', 'حدادة', 'كهرباء', 'ميكانيك', 'سباكة']
      },
      'sewing': {
        name: 'تعليم الخياطة والتطريز',
        items: ['خياطة أساسية', 'تطريز', 'تصميم أزياء']
      },
      'automotive': {
        name: 'تعليم الميكانيك وصيانة السيارات',
        items: ['ميكانيك عام', 'كهرباء سيارات', 'صيانة محركات']
      },
      'electronics-repair': {
        name: 'تعليم تصليح الموبايلات والحواسيب',
        items: ['تصليح موبايلات', 'صيانة حواسيب', 'شبكات']
      },
      'design-software': {
        name: 'تعليم برامج التصميم',
        items: ['Photoshop', 'Illustrator', 'InDesign', 'AutoCAD']
      },
      'video-editing': {
        name: 'تعليم المونتاج وصناعة الفيديو',
        items: ['مونتاج فيديو', 'تصوير', 'إنتاج محتوى']
      },
      'music': {
        name: 'تعليم الموسيقى',
        items: ['غيتار', 'عود', 'كمان', 'بيانو', 'طبلة']
      },
      'performing-arts': {
        name: 'تعليم الغناء والتمثيل والمسرح',
        items: ['غناء', 'تمثيل', 'مسرح', 'إلقاء']
      }
    }
  },
  'training-courses': {
    name: 'الدورات التدريبية',
    subCategories: {
      'programming': {
        name: 'دورات برمجة',
        items: ['Python', 'JavaScript', 'PHP', 'Java', 'C++', 'React', 'Node.js']
      },
      'web-design': {
        name: 'دورات تصميم مواقع وتطبيقات',
        items: ['تصميم مواقع', 'تطوير تطبيقات', 'UI/UX']
      },
      'digital-marketing': {
        name: 'دورات التسويق الإلكتروني',
        items: ['سوشيال ميديا', 'SEO', 'إعلانات جوجل', 'التسويق بالمحتوى']
      },
      'photography': {
        name: 'دورات التصوير الفوتوغرافي والفيديو',
        items: ['تصوير فوتوغرافي', 'تصوير فيديو', 'إضاءة']
      },
      'driving': {
        name: 'دورات القيادة',
        items: ['سواقة سيارات', 'سواقة دراجات نارية']
      },
      'arts': {
        name: 'دورات رسم وفنون تشكيلية',
        items: ['رسم', 'نحت', 'خزف', 'فنون تشكيلية']
      },
      'soft-skills': {
        name: 'دورات تطوير مهارات العمل',
        items: ['مهارات التواصل', 'العمل الجماعي', 'حل المشاكل']
      },
      'business': {
        name: 'دورات إدارة أعمال وريادة مشاريع',
        items: ['إدارة أعمال', 'ريادة مشاريع', 'إدارة مشاريع']
      },
      'accounting': {
        name: 'دورات محاسبة واستخدام Excel',
        items: ['محاسبة', 'Excel متقدم', 'إدارة مالية']
      }
    }
  },
  'other-services': {
    name: 'خدمات تعليمية أخرى',
    subCategories: {
      'centers': {
        name: 'مراكز دروس خصوصية',
        items: ['مراكز تعليمية', 'معاهد']
      },
      'libraries': {
        name: 'مكتبات وأكشاك بيع كتب',
        items: ['مكتبات', 'أكشاك كتب']
      },
      'book-exchange': {
        name: 'تبادل كتب مستعملة',
        items: ['تبادل كتب', 'بيع كتب مستعملة']
      },
      'research-writing': {
        name: 'خدمات كتابة الأبحاث الجامعية',
        items: ['كتابة أبحاث', 'مراجعة أبحاث']
      },
      'translation': {
        name: 'خدمات الترجمة الأكاديمية',
        items: ['ترجمة أكاديمية', 'ترجمة معتمدة']
      },
      'printing': {
        name: 'خدمات الطباعة والتجليد',
        items: ['طباعة', 'تجليد', 'تصوير']
      },
      'presentations': {
        name: 'خدمات إعداد العروض التقديمية',
        items: ['PowerPoint', 'عروض تقديمية']
      },
      'online-lessons': {
        name: 'دروس أونلاين عبر الإنترنت',
        items: ['Zoom', 'Skype', 'دروس مباشرة']
      },
      'group-study': {
        name: 'دروس تقوية جماعية',
        items: ['دراسة جماعية', 'مجموعات دراسية']
      }
    }
  }
};

const BooksEducationSpecificFields = ({ data, onChange }: BooksEducationSpecificFieldsProps) => {
  const [selectedCategory, setSelectedCategory] = useState(data?.category || '');
  const [selectedSubCategory, setSelectedSubCategory] = useState(data?.subCategory || '');

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setSelectedSubCategory('');
    onChange({
      ...data,
      category,
      subCategory: '',
      subject: ''
    });
  };

  const handleSubCategoryChange = (subCategory: string) => {
    setSelectedSubCategory(subCategory);
    onChange({
      ...data,
      subCategory,
      subject: ''
    });
  };

  const handleChange = (field: keyof BooksEducationData, value: string | boolean) => {
    onChange({
      category: '',
      subCategory: '',
      educationLevel: '',
      subject: '',
      language: '',
      condition: '',
      format: '',
      duration: '',
      location: '',
      onlineAvailable: false,
      groupSize: '',
      description: '',
      ...data,
      [field]: value
    });
  };

  const currentCategory = selectedCategory ? booksEducationCategories[selectedCategory as keyof typeof booksEducationCategories] : null;
  const currentSubCategory = selectedSubCategory && currentCategory ? 
    currentCategory.subCategories[selectedSubCategory as keyof typeof currentCategory.subCategories] : null;

  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200">
        <h3 className="text-lg font-semibold text-blue-800 mb-4">
          تفاصيل الكتب والتعليم
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* الفئة الرئيسية */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الرئيسية</label>
            <select
              value={selectedCategory}
              onChange={(e) => handleCategoryChange(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">اختر الفئة</option>
              {Object.entries(booksEducationCategories).map(([key, category]) => (
                <option key={key} value={key}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* الفئة الفرعية */}
          {currentCategory && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الفرعية</label>
              <select
                value={selectedSubCategory}
                onChange={(e) => handleSubCategoryChange(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">اختر الفئة الفرعية</option>
                {Object.entries(currentCategory.subCategories).map(([key, subCategory]) => (
                  <option key={key} value={key}>
                    {subCategory.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* المادة/التخصص */}
          {currentSubCategory && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المادة/التخصص</label>
              <select
                value={data?.subject || ''}
                onChange={(e) => handleChange('subject', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">اختر المادة/التخصص</option>
                {currentSubCategory.items.map((item, index) => (
                  <option key={index} value={item}>
                    {item}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* اللغة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">اللغة</label>
            <select
              value={data?.language || ''}
              onChange={(e) => handleChange('language', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">اختر اللغة</option>
              <option value="العربية">العربية</option>
              <option value="الإنكليزية">الإنكليزية</option>
              <option value="الفرنسية">الفرنسية</option>
              <option value="التركية">التركية</option>
              <option value="الألمانية">الألمانية</option>
              <option value="أخرى">أخرى</option>
            </select>
          </div>

          {/* حالة الكتاب */}
          {selectedCategory === 'books-references' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">حالة الكتاب</label>
              <select
                value={data?.condition || ''}
                onChange={(e) => handleChange('condition', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">اختر الحالة</option>
                <option value="جديد">جديد</option>
                <option value="ممتاز">ممتاز</option>
                <option value="جيد جداً">جيد جداً</option>
                <option value="جيد">جيد</option>
                <option value="مقبول">مقبول</option>
              </select>
            </div>
          )}

          {/* نوع التعليم */}
          {(selectedCategory === 'private-lessons' || selectedCategory === 'training-courses') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">نوع التعليم</label>
              <select
                value={data?.format || ''}
                onChange={(e) => handleChange('format', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">اختر نوع التعليم</option>
                <option value="حضوري">حضوري</option>
                <option value="أونلاين">أونلاين</option>
                <option value="مختلط">مختلط (حضوري + أونلاين)</option>
              </select>
            </div>
          )}

          {/* مدة الدورة */}
          {selectedCategory === 'training-courses' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">مدة الدورة</label>
              <select
                value={data?.duration || ''}
                onChange={(e) => handleChange('duration', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">اختر المدة</option>
                <option value="أسبوع">أسبوع</option>
                <option value="أسبوعين">أسبوعين</option>
                <option value="شهر">شهر</option>
                <option value="شهرين">شهرين</option>
                <option value="3 أشهر">3 أشهر</option>
                <option value="6 أشهر">6 أشهر</option>
                <option value="سنة">سنة</option>
              </select>
            </div>
          )}

          {/* حجم المجموعة */}
          {(selectedCategory === 'private-lessons' || selectedCategory === 'training-courses') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">حجم المجموعة</label>
              <select
                value={data?.groupSize || ''}
                onChange={(e) => handleChange('groupSize', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">اختر حجم المجموعة</option>
                <option value="فردي">فردي (1 طالب)</option>
                <option value="صغيرة">مجموعة صغيرة (2-5 طلاب)</option>
                <option value="متوسطة">مجموعة متوسطة (6-15 طالب)</option>
                <option value="كبيرة">مجموعة كبيرة (أكثر من 15 طالب)</option>
              </select>
            </div>
          )}

          {/* متاح أونلاين */}
          <div className="md:col-span-2">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={data?.onlineAvailable || false}
                onChange={(e) => handleChange('onlineAvailable', e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="text-sm font-medium text-gray-700">متاح أونلاين</span>
            </label>
          </div>

          {/* وصف إضافي */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">وصف إضافي</label>
            <textarea
              value={data?.description || ''}
              onChange={(e) => handleChange('description', e.target.value)}
              placeholder="اكتب تفاصيل إضافية..."
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BooksEducationSpecificFields;
