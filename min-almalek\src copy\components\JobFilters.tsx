'use client';

import { useState, useEffect } from 'react';
import { JOB_CATEGORIES, EXPERIENCE_LEVELS, WORK_TYPES, WORK_MODELS, EDUCATION_LEVELS } from '@/lib/jobs';

interface JobFiltersProps {
  onFiltersChange: (filters: JobFilters) => void;
  initialFilters?: Partial<JobFilters>;
}

export interface JobFilters {
  category?: string;
  subcategory?: string;
  workType?: string;
  workModel?: string;
  experienceLevel?: string;
  educationLevel?: string;
  location?: string;
  salaryMin?: number;
  salaryMax?: number;
  salaryCurrency?: string;
  keywords?: string;
  postedWithin?: string;
  companySize?: string;
  benefits?: string[];

  languages?: string[];
  contractType?: string;
  workSchedule?: string;
  travelRequirement?: string;
  department?: string;
  jobLevel?: string;
  salaryPeriod?: string;
  urgent?: boolean;
  featured?: boolean;
  experienceYearsMin?: number;
  experienceYearsMax?: number;
}

const JobFilters = ({ onFiltersChange, initialFilters = {} }: JobFiltersProps) => {
  const [filters, setFilters] = useState<JobFilters>(initialFilters);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (isMounted) {
      onFiltersChange(filters);
    }
  }, [isMounted, filters, onFiltersChange]);

  const updateFilter = (key: keyof JobFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
  };

  const clearFilters = () => {
    const emptyFilters: JobFilters = {};
    setFilters(emptyFilters);
  };

  const selectedCategory = JOB_CATEGORIES.find(cat => cat.id === filters.category);

  // الحصول على الخيارات السريعة حسب العملة
  const getQuickOptions = () => {
    const currency = filters.salaryCurrency || 'SYP';
    switch (currency) {
      case 'USD':
        return usdQuickOptions;
      case 'EUR':
        return eurQuickOptions;
      default:
        return sypQuickOptions;
    }
  };

  const postedWithinOptions = [
    { value: '1', label: 'آخر 24 ساعة' },
    { value: '7', label: 'آخر أسبوع' },
    { value: '30', label: 'آخر شهر' },
    { value: '90', label: 'آخر 3 أشهر' }
  ];

  const companySizeOptions = [
    { value: 'startup', label: 'شركة ناشئة (1-10)' },
    { value: 'small', label: 'شركة صغيرة (11-50)' },
    { value: 'medium', label: 'شركة متوسطة (51-200)' },
    { value: 'large', label: 'شركة كبيرة (201-1000)' },
    { value: 'enterprise', label: 'مؤسسة كبرى (1000+)' }
  ];

  const currencyOptions = [
    { value: 'SYP', label: 'ليرة سورية (ل.س)', symbol: 'ل.س' },
    { value: 'USD', label: 'دولار أمريكي ($)', symbol: '$' },
    { value: 'EUR', label: 'يورو (€)', symbol: '€' }
  ];

  // خيارات سريعة للراتب بالليرة السورية
  const sypQuickOptions = [
    { value: 500000, label: '500 ألف' },
    { value: 750000, label: '750 ألف' },
    { value: 1000000, label: '1 مليون' },
    { value: 1500000, label: '1.5 مليون' },
    { value: 2000000, label: '2 مليون' },
    { value: 2500000, label: '2.5 مليون' },
    { value: 3000000, label: '3 مليون' },
    { value: 4000000, label: '4 مليون' },
    { value: 5000000, label: '5 مليون' },
    { value: 7500000, label: '7.5 مليون' },
    { value: 10000000, label: '10 مليون' },
    { value: 15000000, label: '15 مليون' },
    { value: 20000000, label: '20 مليون' },
    { value: 30000000, label: '30 مليون' }
  ];

  // خيارات سريعة للراتب بالدولار
  const usdQuickOptions = [
    { value: 200, label: '$200' },
    { value: 300, label: '$300' },
    { value: 500, label: '$500' },
    { value: 750, label: '$750' },
    { value: 1000, label: '$1,000' },
    { value: 1500, label: '$1,500' },
    { value: 2000, label: '$2,000' },
    { value: 3000, label: '$3,000' },
    { value: 5000, label: '$5,000' },
    { value: 7500, label: '$7,500' },
    { value: 10000, label: '$10,000' }
  ];

  // خيارات سريعة للراتب باليورو
  const eurQuickOptions = [
    { value: 200, label: '€200' },
    { value: 300, label: '€300' },
    { value: 500, label: '€500' },
    { value: 750, label: '€750' },
    { value: 1000, label: '€1,000' },
    { value: 1500, label: '€1,500' },
    { value: 2000, label: '€2,000' },
    { value: 3000, label: '€3,000' },
    { value: 5000, label: '€5,000' },
    { value: 7500, label: '€7,500' },
    { value: 10000, label: '€10,000' }
  ];

  // خيارات إضافية للفلاتر المتقدمة
  const contractTypeOptions = [
    { value: 'permanent', label: 'دائم' },
    { value: 'temporary', label: 'مؤقت' },
    { value: 'contract', label: 'عقد محدد المدة' },
    { value: 'freelance', label: 'عمل حر' },
    { value: 'internship', label: 'تدريب' },
    { value: 'probation', label: 'فترة تجريبية' }
  ];

  const workScheduleOptions = [
    { value: 'standard', label: 'دوام عادي (9-5)' },
    { value: 'flexible', label: 'ساعات مرنة' },
    { value: 'shift', label: 'نظام ورديات' },
    { value: 'night', label: 'دوام ليلي' },
    { value: 'weekend', label: 'عطلة نهاية الأسبوع' },
    { value: 'compressed', label: 'أسبوع مضغوط' }
  ];

  const travelOptions = [
    { value: 'none', label: 'لا يوجد سفر' },
    { value: 'minimal', label: 'سفر محدود (أقل من 25%)' },
    { value: 'moderate', label: 'سفر متوسط (25-50%)' },
    { value: 'frequent', label: 'سفر متكرر (50-75%)' },
    { value: 'extensive', label: 'سفر مكثف (أكثر من 75%)' }
  ];

  const departmentOptions = [
    'التطوير',
    'التصميم',
    'التسويق',
    'المبيعات',
    'الموارد البشرية',
    'المحاسبة',
    'خدمة العملاء',
    'الإدارة',
    'الهندسة',
    'الطب',
    'التعليم',
    'الأمن',
    'الصيانة'
  ];

  const jobLevelOptions = [
    { value: 'entry', label: 'مستوى مبتدئ' },
    { value: 'junior', label: 'مستوى مبتدئ متقدم' },
    { value: 'mid', label: 'مستوى متوسط' },
    { value: 'senior', label: 'مستوى متقدم' },
    { value: 'lead', label: 'قائد فريق' },
    { value: 'manager', label: 'مدير' },
    { value: 'director', label: 'مدير عام' },
    { value: 'executive', label: 'تنفيذي' }
  ];

  const salaryPeriodOptions = [
    { value: 'hourly', label: 'ساعي' },
    { value: 'daily', label: 'يومي' },
    { value: 'monthly', label: 'شهري' },
    { value: 'yearly', label: 'سنوي' }
  ];



  const languageOptions = [
    'الإنجليزية', 'الفرنسية', 'التركية', 'الروسية'
  ];

  const benefitsOptions = [
    'تأمين صحي',
    'تأمين اجتماعي',
    'تأمين حياة',
    'إجازة سنوية',
    'إجازة مرضية',
    'مكافآت أداء',
    'مكافآت سنوية',
    'تدريب وتطوير',
    'شهادات مهنية',
    'عمل مرن',
    'عمل من المنزل',
    'ساعات عمل مرنة',
    'وجبات مجانية',
    'مواصلات',
    'بدل مواصلات',
    'سكن',
    'بدل سكن',
    'عمولات',
    'حوافز مبيعات',
    'خصومات موظفين',
    'رعاية أطفال',
    'صالة رياضية',
    'تأمين عائلي'
  ];

  const cities = [
    'دمشق',
    'حلب',
    'حمص',
    'حماة',
    'اللاذقية',
    'طرطوس',
    'درعا',
    'السويداء',
    'القنيطرة',
    'دير الزور',
    'الرقة',
    'الحسكة',
    'إدلب',
    'ريف دمشق'
  ];

  if (!isMounted) {
    return (
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
              <span>🔍</span>
              <span>فلترة الوظائف</span>
            </h3>
          </div>
        </div>
        <div className="p-4">
          <div className="animate-pulse">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="h-10 bg-gray-200 rounded"></div>
              <div className="h-10 bg-gray-200 rounded"></div>
              <div className="h-10 bg-gray-200 rounded"></div>
              <div className="h-10 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
            <span>🔍</span>
            <span>فلترة الوظائف</span>
          </h3>
          <div className="flex items-center gap-2">
            <button
              onClick={clearFilters}
              className="text-sm text-gray-500 hover:text-gray-700"
            >
              مسح الكل
            </button>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-primary-600 hover:text-primary-700"
            >
              {isExpanded ? '▲' : '▼'}
            </button>
          </div>
        </div>
      </div>

      {/* Quick Filters */}
      <div className="p-4 border-b border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Category */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">المجال</label>
            <select
              value={filters.category || ''}
              onChange={(e) => {
                updateFilter('category', e.target.value);
                updateFilter('subcategory', ''); // Reset subcategory
              }}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
            >
              <option value="">جميع المجالات</option>
              {JOB_CATEGORIES.map(category => (
                <option key={category.id} value={category.id}>
                  {category.icon} {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* Work Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">نوع العمل</label>
            <select
              value={filters.workType || ''}
              onChange={(e) => updateFilter('workType', e.target.value)}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
            >
              <option value="">جميع الأنواع</option>
              {WORK_TYPES.map(type => (
                <option key={type.id} value={type.name}>
                  {type.icon} {type.name}
                </option>
              ))}
            </select>
          </div>

          {/* Work Model */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">نموذج العمل</label>
            <select
              value={filters.workModel || ''}
              onChange={(e) => updateFilter('workModel', e.target.value)}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
            >
              <option value="">جميع النماذج</option>
              {WORK_MODELS.map(model => (
                <option key={model.id} value={model.name}>
                  {model.icon} {model.name}
                </option>
              ))}
            </select>
          </div>

          {/* Location */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
            <select
              value={filters.location || ''}
              onChange={(e) => updateFilter('location', e.target.value)}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
            >
              <option value="">جميع المدن</option>
              {cities.map(city => (
                <option key={city} value={city}>{city}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Advanced Filters */}
      {isExpanded && (
        <div className="p-4 space-y-6">
          {/* Subcategory */}
          {selectedCategory && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">التخصص</label>
              <select
                value={filters.subcategory || ''}
                onChange={(e) => updateFilter('subcategory', e.target.value)}
                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
              >
                <option value="">جميع التخصصات</option>
                {selectedCategory.subcategories.map(sub => (
                  <option key={sub} value={sub}>{sub}</option>
                ))}
              </select>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Experience Level */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">مستوى الخبرة</label>
              <select
                value={filters.experienceLevel || ''}
                onChange={(e) => updateFilter('experienceLevel', e.target.value)}
                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
              >
                <option value="">جميع المستويات</option>
                {EXPERIENCE_LEVELS.map(level => (
                  <option key={level.id} value={level.name}>
                    {level.name} ({level.years})
                  </option>
                ))}
              </select>
            </div>

            {/* Education Level */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المستوى التعليمي</label>
              <select
                value={filters.educationLevel || ''}
                onChange={(e) => updateFilter('educationLevel', e.target.value)}
                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
              >
                <option value="">جميع المستويات</option>
                {EDUCATION_LEVELS.map(level => (
                  <option key={level.id} value={level.name}>{level.name}</option>
                ))}
              </select>
            </div>

            {/* Posted Within */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ النشر</label>
              <select
                value={filters.postedWithin || ''}
                onChange={(e) => updateFilter('postedWithin', e.target.value)}
                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
              >
                <option value="">أي وقت</option>
                {postedWithinOptions.map(option => (
                  <option key={option.value} value={option.value}>{option.label}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Salary Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">نطاق الراتب</label>

            {/* Currency Selection */}
            <div className="mb-3">
              <select
                value={filters.salaryCurrency || 'SYP'}
                onChange={(e) => updateFilter('salaryCurrency', e.target.value)}
                className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
              >
                {currencyOptions.map(currency => (
                  <option key={currency.value} value={currency.value}>
                    {currency.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Quick Salary Options */}
            <div className="mb-3">
              <label className="block text-xs font-medium text-gray-600 mb-2">خيارات سريعة:</label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {getQuickOptions().map(option => (
                  <button
                    key={option.value}
                    type="button"
                    onClick={() => {
                      if (!filters.salaryMin || filters.salaryMin > option.value) {
                        updateFilter('salaryMin', option.value);
                      } else {
                        updateFilter('salaryMax', option.value);
                      }
                    }}
                    className="px-2 py-1 text-xs border border-gray-300 rounded-md hover:bg-primary-50 hover:border-primary-300 transition-colors"
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Salary Range Inputs */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <input
                  type="number"
                  placeholder="الحد الأدنى"
                  value={filters.salaryMin || ''}
                  onChange={(e) => updateFilter('salaryMin', e.target.value ? parseInt(e.target.value) : undefined)}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
                />
              </div>
              <div>
                <input
                  type="number"
                  placeholder="الحد الأقصى"
                  value={filters.salaryMax || ''}
                  onChange={(e) => updateFilter('salaryMax', e.target.value ? parseInt(e.target.value) : undefined)}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
                />
              </div>
            </div>

            {/* Currency Display Helper */}
            <p className="text-xs text-gray-500 mt-1">
              {filters.salaryCurrency === 'USD' && 'المبالغ بالدولار الأمريكي'}
              {filters.salaryCurrency === 'EUR' && 'المبالغ باليورو'}
              {(!filters.salaryCurrency || filters.salaryCurrency === 'SYP') && 'المبالغ بالليرة السورية'}
            </p>
          </div>

          {/* Company Size */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">حجم الشركة</label>
            <select
              value={filters.companySize || ''}
              onChange={(e) => updateFilter('companySize', e.target.value)}
              className="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-gray-50 hover:bg-white transition-colors"
            >
              <option value="">جميع الأحجام</option>
              {companySizeOptions.map(size => (
                <option key={size.value} value={size.value}>{size.label}</option>
              ))}
            </select>
          </div>

          {/* Benefits */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">المزايا المطلوبة</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {benefitsOptions.map(benefit => (
                <label key={benefit} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.benefits?.includes(benefit) || false}
                    onChange={(e) => {
                      const currentBenefits = filters.benefits || [];
                      if (e.target.checked) {
                        updateFilter('benefits', [...currentBenefits, benefit]);
                      } else {
                        updateFilter('benefits', currentBenefits.filter(b => b !== benefit));
                      }
                    }}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="mr-2 text-sm text-gray-700">{benefit}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Keywords */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">كلمات مفتاحية</label>
            <input
              type="text"
              placeholder="مثال: React, JavaScript, تسويق رقمي"
              value={filters.keywords || ''}
              onChange={(e) => updateFilter('keywords', e.target.value)}
              className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
            />
            <p className="text-xs text-gray-500 mt-1">
              ابحث في العنوان، الوصف، المهارات المطلوبة
            </p>
          </div>

          {/* Professional Filters Section */}
          <div className="border-t border-gray-200 pt-6 mt-6">
            <h5 className="text-sm font-medium text-gray-700 mb-4 flex items-center gap-2">
              <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
              فلاتر احترافية متقدمة
            </h5>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Contract Type */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع العقد</label>
                <select
                  value={filters.contractType || ''}
                  onChange={(e) => updateFilter('contractType', e.target.value)}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
                >
                  <option value="">جميع أنواع العقود</option>
                  {contractTypeOptions.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>

              {/* Department */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">القسم</label>
                <select
                  value={filters.department || ''}
                  onChange={(e) => updateFilter('department', e.target.value)}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
                >
                  <option value="">جميع الأقسام</option>
                  {departmentOptions.map(dept => (
                    <option key={dept} value={dept}>{dept}</option>
                  ))}
                </select>
              </div>

              {/* Job Level */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">المستوى الوظيفي</label>
                <select
                  value={filters.jobLevel || ''}
                  onChange={(e) => updateFilter('jobLevel', e.target.value)}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
                >
                  <option value="">جميع المستويات</option>
                  {jobLevelOptions.map(level => (
                    <option key={level.value} value={level.value}>{level.label}</option>
                  ))}
                </select>
              </div>

              {/* Work Schedule */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">جدول العمل</label>
                <select
                  value={filters.workSchedule || ''}
                  onChange={(e) => updateFilter('workSchedule', e.target.value)}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
                >
                  <option value="">جميع الجداول</option>
                  {workScheduleOptions.map(schedule => (
                    <option key={schedule.value} value={schedule.value}>{schedule.label}</option>
                  ))}
                </select>
              </div>

              {/* Travel Requirement */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">متطلبات السفر</label>
                <select
                  value={filters.travelRequirement || ''}
                  onChange={(e) => updateFilter('travelRequirement', e.target.value)}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
                >
                  <option value="">جميع المتطلبات</option>
                  {travelOptions.map(travel => (
                    <option key={travel.value} value={travel.value}>{travel.label}</option>
                  ))}
                </select>
              </div>

              {/* Salary Period */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">فترة الراتب</label>
                <select
                  value={filters.salaryPeriod || ''}
                  onChange={(e) => updateFilter('salaryPeriod', e.target.value)}
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
                >
                  <option value="">جميع الفترات</option>
                  {salaryPeriodOptions.map(period => (
                    <option key={period.value} value={period.value}>{period.label}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Experience Years Range */}
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">سنوات الخبرة المطلوبة</label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <input
                    type="number"
                    placeholder="الحد الأدنى (سنوات)"
                    value={filters.experienceYearsMin || ''}
                    onChange={(e) => updateFilter('experienceYearsMin', e.target.value ? parseInt(e.target.value) : undefined)}
                    className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
                    min="0"
                    max="50"
                  />
                </div>
                <div>
                  <input
                    type="number"
                    placeholder="الحد الأقصى (سنوات)"
                    value={filters.experienceYearsMax || ''}
                    onChange={(e) => updateFilter('experienceYearsMax', e.target.value ? parseInt(e.target.value) : undefined)}
                    className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 min-h-[44px] text-sm"
                    min="0"
                    max="50"
                  />
                </div>
              </div>
            </div>



            {/* Languages */}
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">اللغات المطلوبة</label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {languageOptions.map(language => (
                  <label key={language} className="flex items-center p-2 border border-gray-200 rounded-lg hover:bg-primary-50 hover:border-primary-300 cursor-pointer transition-colors">
                    <input
                      type="checkbox"
                      checked={filters.languages?.includes(language) || false}
                      onChange={(e) => {
                        const currentLanguages = filters.languages || [];
                        if (e.target.checked) {
                          updateFilter('languages', [...currentLanguages, language]);
                        } else {
                          updateFilter('languages', currentLanguages.filter(l => l !== language));
                        }
                      }}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="mr-2 text-sm text-gray-700">{language}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Special Filters */}
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">فلاتر خاصة</label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-yellow-50 hover:border-yellow-300 cursor-pointer transition-colors">
                  <input
                    type="checkbox"
                    checked={filters.urgent || false}
                    onChange={(e) => updateFilter('urgent', e.target.checked)}
                    className="rounded border-gray-300 text-yellow-600 focus:ring-yellow-500"
                  />
                  <span className="mr-2 text-sm text-gray-700 flex items-center gap-2">
                    <span className="text-yellow-500">⚡</span>
                    وظائف عاجلة
                  </span>
                </label>

                <label className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 cursor-pointer transition-colors">
                  <input
                    type="checkbox"
                    checked={filters.featured || false}
                    onChange={(e) => updateFilter('featured', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="mr-2 text-sm text-gray-700 flex items-center gap-2">
                    <span className="text-blue-500">⭐</span>
                    وظائف مميزة
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {Object.keys(filters).length > 0 && (
        <div className="p-4 bg-gray-50 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            {Object.entries(filters).map(([key, value]) => {
              if (!value || (Array.isArray(value) && value.length === 0)) return null;

              let displayValue = value;
              if (Array.isArray(value)) {
                displayValue = value.join(', ');
              }

              return (
                <span
                  key={key}
                  className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary-100 text-primary-800"
                >
                  {displayValue}
                  <button
                    onClick={() => updateFilter(key as keyof JobFilters, undefined)}
                    className="mr-1 text-primary-600 hover:text-primary-800"
                  >
                    ×
                  </button>
                </span>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default JobFilters;
