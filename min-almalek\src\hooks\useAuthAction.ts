'use client';

import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';
import { useState } from 'react';

interface AuthActionOptions {
  requireAuth?: boolean;
  showLoginPrompt?: boolean;
  loginMessage?: string;
  onAuthRequired?: () => void;
}

export function useAuthAction(options: AuthActionOptions = {}) {
  const { 
    requireAuth = true, 
    showLoginPrompt = true, 
    loginMessage = 'يجب تسجيل الدخول لاستخدام هذه الميزة',
    onAuthRequired 
  } = options;
  
  const { user, isAuthenticated } = useAuth();
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);

  const executeAction = (action: () => void | Promise<void>) => {
    if (requireAuth && !isAuthenticated) {
      // إظهار رسالة تسجيل الدخول
      if (showLoginPrompt) {
        toast.error(loginMessage);
      }
      
      // تنفيذ callback مخصص إذا كان متاحاً
      if (onAuthRequired) {
        onAuthRequired();
      } else {
        // فتح modal تسجيل الدخول (يمكن تخصيصه لاحقاً)
        setIsAuthModalOpen(true);
      }
      
      return false;
    }

    // تنفيذ الإجراء إذا كان المستخدم مصادق عليه
    try {
      action();
      return true;
    } catch (error) {
      console.error('خطأ في تنفيذ الإجراء:', error);
      toast.error('حدث خطأ أثناء تنفيذ العملية');
      return false;
    }
  };

  const executeAsyncAction = async (action: () => Promise<void>) => {
    if (requireAuth && !isAuthenticated) {
      if (showLoginPrompt) {
        toast.error(loginMessage);
      }
      
      if (onAuthRequired) {
        onAuthRequired();
      } else {
        setIsAuthModalOpen(true);
      }
      
      return false;
    }

    try {
      await action();
      return true;
    } catch (error) {
      console.error('خطأ في تنفيذ الإجراء:', error);
      toast.error('حدث خطأ أثناء تنفيذ العملية');
      return false;
    }
  };

  return {
    executeAction,
    executeAsyncAction,
    isAuthenticated,
    user,
    isAuthModalOpen,
    setIsAuthModalOpen,
    canExecute: !requireAuth || isAuthenticated
  };
}

// Hook مخصص للرسائل
export function useMessageAction() {
  return useAuthAction({
    requireAuth: true,
    showLoginPrompt: true,
    loginMessage: 'يجب تسجيل الدخول لإرسال الرسائل 📨'
  });
}

// Hook مخصص للمفضلة
export function useFavoriteAction() {
  return useAuthAction({
    requireAuth: true,
    showLoginPrompt: true,
    loginMessage: 'يجب تسجيل الدخول لإضافة الإعلانات للمفضلة ❤️'
  });
}

// Hook مخصص لإضافة الإعلانات
export function useAddAdAction() {
  return useAuthAction({
    requireAuth: true,
    showLoginPrompt: true,
    loginMessage: 'يجب تسجيل الدخول لإضافة إعلان جديد 📝'
  });
}

// Hook مخصص للتقييمات
export function useRatingAction() {
  return useAuthAction({
    requireAuth: true,
    showLoginPrompt: true,
    loginMessage: 'يجب تسجيل الدخول لإضافة تقييم ⭐'
  });
}

// Hook مخصص للتعليقات
export function useCommentAction() {
  return useAuthAction({
    requireAuth: true,
    showLoginPrompt: true,
    loginMessage: 'يجب تسجيل الدخول لإضافة تعليق 💬'
  });
}

// Hook مخصص للمتابعة
export function useFollowAction() {
  return useAuthAction({
    requireAuth: true,
    showLoginPrompt: true,
    loginMessage: 'يجب تسجيل الدخول لمتابعة البائعين 👥'
  });
}

// Hook مخصص للإشعارات
export function useNotificationAction() {
  return useAuthAction({
    requireAuth: true,
    showLoginPrompt: true,
    loginMessage: 'يجب تسجيل الدخول لإدارة الإشعارات 🔔'
  });
}

// Hook مخصص للدفع والاشتراكات
export function usePaymentAction() {
  return useAuthAction({
    requireAuth: true,
    showLoginPrompt: true,
    loginMessage: 'يجب تسجيل الدخول للوصول لخدمات الدفع 💳'
  });
}

// Hook مخصص للبحث المحفوظ
export function useSavedSearchAction() {
  return useAuthAction({
    requireAuth: true,
    showLoginPrompt: true,
    loginMessage: 'يجب تسجيل الدخول لحفظ عمليات البحث 🔍'
  });
}

// Hook مخصص للإعدادات
export function useSettingsAction() {
  return useAuthAction({
    requireAuth: true,
    showLoginPrompt: true,
    loginMessage: 'يجب تسجيل الدخول للوصول للإعدادات ⚙️'
  });
}
