'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import FilterModal from '@/components/FilterModal';
import CategoryIcon from '@/components/CategoryIcon';

const BooksEducationPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedSubCategory, setSelectedSubCategory] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState('');
  const [selectedFormat, setSelectedFormat] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [priceRange, setPriceRange] = useState([0, 500000]);
  const [sortBy, setSortBy] = useState('newest');
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);

  // فلاتر للـ FilterModal
  const [filters, setFilters] = useState({
    mainCategory: '',
    subCategory: '',
    language: '',
    format: '',
    condition: '',
    author: '',
    publisher: '',
    priceFrom: '',
    priceTo: '',
    location: ''
  });

  // بيانات وهمية للإعلانات
  const mockAds = [
    {
      id: '1',
      title: 'كتب طبية جامعية - مجموعة كاملة',
      price: 150000,
      currency: 'SYP',
      location: 'دمشق - المزة',
      images: ['/images/books/medical-books.jpg'],
      category: 'books-education',
      subCategory: 'books-references',
      subject: 'طب',
      language: 'العربية',
      condition: 'جيد جداً',
      views: 189,
      isFavorite: false,
      isPromoted: true,
      postedAt: '2024-01-15',
      description: 'مجموعة كتب طبية جامعية شاملة لجميع السنوات'
    },
    {
      id: '2',
      title: 'دروس خصوصية - رياضيات ثانوي',
      price: 30000,
      currency: 'SYP',
      location: 'دمشق - أبو رمانة',
      images: ['/images/books/math-tutor.jpg'],
      category: 'books-education',
      subCategory: 'private-lessons',
      subject: 'رياضيات',
      language: 'العربية',
      format: 'حضوري',
      views: 245,
      isFavorite: true,
      isPromoted: false,
      postedAt: '2024-01-14',
      description: 'دروس خصوصية في الرياضيات للمرحلة الثانوية مع مدرس خبير'
    },
    {
      id: '3',
      title: 'دورة برمجة Python - مبتدئين',
      price: 75000,
      currency: 'SYP',
      location: 'دمشق - الشعلان',
      images: ['/images/books/python-course.jpg'],
      category: 'books-education',
      subCategory: 'training-courses',
      subject: 'Python',
      language: 'العربية',
      format: 'أونلاين',
      views: 156,
      isFavorite: false,
      isPromoted: false,
      postedAt: '2024-01-13',
      description: 'دورة شاملة لتعلم برمجة Python من الصفر'
    }
  ];

  const categories = [
    { id: 'books-references', name: 'الكتب والمراجع' },
    { id: 'private-lessons', name: 'الدروس الخصوصية' },
    { id: 'technical-education', name: 'التعليم الفني والمهني' },
    { id: 'training-courses', name: 'الدورات التدريبية' },
    { id: 'other-services', name: 'خدمات تعليمية أخرى' }
  ];

  const subCategories = {
    'books-references': [
      { id: 'school-books', name: 'كتب مدرسية' },
      { id: 'university-books', name: 'كتب جامعية' },
      { id: 'language-books', name: 'كتب تعليم اللغات' },
      { id: 'children-books', name: 'كتب أطفال وقصص مصورة' },
      { id: 'academic-references', name: 'مراجع علمية وأكاديمية' }
    ],
    'private-lessons': [
      { id: 'elementary', name: 'دروس خصوصية للمرحلة الابتدائية' },
      { id: 'middle-school', name: 'دروس خصوصية للمرحلة الإعدادية' },
      { id: 'high-school', name: 'دروس خصوصية للمرحلة الثانوية' },
      { id: 'university', name: 'كورسات جامعية' },
      { id: 'languages', name: 'دروس لغات' }
    ],
    'technical-education': [
      { id: 'crafts', name: 'تعليم الحرف' },
      { id: 'sewing', name: 'تعليم الخياطة والتطريز' },
      { id: 'automotive', name: 'تعليم الميكانيك وصيانة السيارات' },
      { id: 'electronics-repair', name: 'تعليم تصليح الموبايلات والحواسيب' },
      { id: 'design-software', name: 'تعليم برامج التصميم' }
    ],
    'training-courses': [
      { id: 'programming', name: 'دورات برمجة' },
      { id: 'web-design', name: 'دورات تصميم مواقع وتطبيقات' },
      { id: 'digital-marketing', name: 'دورات التسويق الإلكتروني' },
      { id: 'photography', name: 'دورات التصوير الفوتوغرافي والفيديو' },
      { id: 'business', name: 'دورات إدارة أعمال وريادة مشاريع' }
    ],
    'other-services': [
      { id: 'centers', name: 'مراكز دروس خصوصية' },
      { id: 'libraries', name: 'مكتبات وأكشاك بيع كتب' },
      { id: 'research-writing', name: 'خدمات كتابة الأبحاث الجامعية' },
      { id: 'translation', name: 'خدمات الترجمة الأكاديمية' },
      { id: 'online-lessons', name: 'دروس أونلاين عبر الإنترنت' }
    ]
  };

  const languages = ['العربية', 'الإنكليزية', 'الفرنسية', 'التركية', 'الألمانية', 'أخرى'];
  const formats = ['حضوري', 'أونلاين', 'مختلط'];
  const conditions = ['جديد', 'ممتاز', 'جيد جداً', 'جيد', 'مقبول'];

  const locations = [
    'دمشق - المزة',
    'دمشق - أبو رمانة',
    'دمشق - الشعلان',
    'دمشق - المالكي',
    'دمشق - الصالحية',
    'حلب - الفرقان',
    'حلب - الجميلية',
    'حمص - الوعر',
    'حمص - الخالدية'
  ];

  // دوال للفلاتر
  const handleFilterChange = (key: string, value: string | string[]) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      mainCategory: '',
      subCategory: '',
      language: '',
      format: '',
      condition: '',
      author: '',
      publisher: '',
      priceFrom: '',
      priceTo: '',
      location: ''
    });
  };

  const filteredAds = mockAds.filter(ad => {
    const matchesSearch = ad.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         ad.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || ad.subCategory === selectedCategory;
    const matchesSubCategory = !selectedSubCategory || ad.subject?.includes(selectedSubCategory);
    const matchesLanguage = !selectedLanguage || ad.language === selectedLanguage;
    const matchesFormat = !selectedFormat || ad.format === selectedFormat;
    const matchesLocation = !selectedLocation || ad.location.includes(selectedLocation);
    const matchesPrice = ad.price >= priceRange[0] && ad.price <= priceRange[1];

    return matchesSearch && matchesCategory && matchesSubCategory && matchesLanguage && matchesFormat && matchesLocation && matchesPrice;
  });

  const sortedAds = [...filteredAds].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'popular':
        return b.views - a.views;
      case 'newest':
      default:
        return new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime();
    }
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-white">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          {/* العنوان الرئيسي */}
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 flex items-center justify-center">
              <CategoryIcon
                category="books"
                className="w-10 h-10"
                color="#3b82f6"
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">الكتب والتعليم</h1>
              <p className="text-gray-600">اكتشف أفضل الكتب والدورات التعليمية</p>
            </div>
          </div>
        </div>

        {/* زر فتح الفلاتر على الموبايل */}
        <div className="lg:hidden mb-6">
          <button
            onClick={() => setIsFilterModalOpen(true)}
            className="w-full bg-white rounded-xl shadow-lg p-4 flex items-center justify-center gap-3 border border-gray-200 hover:bg-gray-50 transition-colors"
          >
            <img
              src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
              alt="فلاتر"
              className="w-6 h-6 opacity-80"
              style={{
                filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.6))'
              }}
            />
            <span className="text-lg font-semibold text-gray-800">البحث والفلاتر</span>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* فلاتر الكتب والتعليم */}
          <div className="lg:col-span-1 hidden lg:block">
            <div className="bg-white rounded-xl shadow-lg p-6 sticky top-8 border border-gray-200">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                <img
                  src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="فلاتر"
                  className="w-6 h-6 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.6))'
                  }}
                />
                فلاتر البحث
              </h2>

              {/* البحث */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="ابحث عن كتاب أو دورة..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* الفئة الرئيسية */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الرئيسية</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => {
                    setSelectedCategory(e.target.value);
                    setSelectedSubCategory('');
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">جميع الفئات</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* الفئة الفرعية */}
              {selectedCategory && subCategories[selectedCategory as keyof typeof subCategories] && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الفرعية</label>
                  <select
                    value={selectedSubCategory}
                    onChange={(e) => setSelectedSubCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">جميع الفئات الفرعية</option>
                    {subCategories[selectedCategory as keyof typeof subCategories].map(subCategory => (
                      <option key={subCategory.id} value={subCategory.name}>
                        {subCategory.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* اللغة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">اللغة</label>
                <select
                  value={selectedLanguage}
                  onChange={(e) => setSelectedLanguage(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">جميع اللغات</option>
                  {languages.map(language => (
                    <option key={language} value={language}>
                      {language}
                    </option>
                  ))}
                </select>
              </div>

              {/* نوع التعليم */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع التعليم</label>
                <select
                  value={selectedFormat}
                  onChange={(e) => setSelectedFormat(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">جميع الأنواع</option>
                  {formats.map(format => (
                    <option key={format} value={format}>
                      {format}
                    </option>
                  ))}
                </select>
              </div>

              {/* المحافظة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">المحافظة</label>
                <select
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">جميع المحافظات</option>
                  <option value="دمشق">دمشق</option>
                  <option value="ريف دمشق">ريف دمشق</option>
                  <option value="حلب">حلب</option>
                  <option value="حمص">حمص</option>
                  <option value="حماة">حماة</option>
                  <option value="اللاذقية">اللاذقية</option>
                  <option value="طرطوس">طرطوس</option>
                  <option value="إدلب">إدلب</option>
                  <option value="درعا">درعا</option>
                  <option value="السويداء">السويداء</option>
                  <option value="القنيطرة">القنيطرة</option>
                  <option value="دير الزور">دير الزور</option>
                  <option value="الرقة">الرقة</option>
                  <option value="الحسكة">الحسكة</option>
                </select>
              </div>

              {/* نطاق السعر */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    value={priceRange[0]}
                    onChange={(e) => setPriceRange([parseInt(e.target.value) || 0, priceRange[1]])}
                    placeholder="السعر من"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <input
                    type="number"
                    value={priceRange[1]}
                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value) || 500000])}
                    placeholder="السعر إلى"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* إعادة تعيين الفلاتر */}
              <button
                onClick={() => {
                  setSelectedCategory('');
                  setSelectedSubCategory('');
                  setSelectedLanguage('');
                  setSelectedFormat('');
                  setSelectedLocation('');
                  setPriceRange([0, 500000]);
                  setSearchQuery('');
                }}
                className="w-full bg-gradient-to-r from-blue-500 to-blue-700 text-white py-3 px-4 rounded-lg hover:from-blue-600 hover:to-blue-800 transition-all duration-300 font-medium"
              >
                إعادة تعيين الفلاتر
              </button>
            </div>
          </div>

          {/* النتائج */}
          <div className="lg:col-span-3">
            {/* شريط الترتيب */}
            <div className="bg-white rounded-xl shadow-lg p-4 mb-6 border border-gray-200">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="text-gray-600">
                  عرض <span className="font-semibold text-blue-600">{sortedAds.length}</span> من أصل {sortedAds.length} إعلان
                </div>

                <div className="flex items-center gap-4">
                  {/* طريقة العرض */}
                  <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'grid'
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      🔲 شبكة
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'list'
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      📋 قائمة
                    </button>
                  </div>

                  {/* الترتيب */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="popular">الأكثر مشاهدة</option>
                    <option value="price-low">السعر: من الأقل للأعلى</option>
                    <option value="price-high">السعر: من الأعلى للأقل</option>
                  </select>
                </div>
              </div>
            </div>

            {/* الإعلانات */}
            <div className={
              viewMode === 'grid'
                ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                : 'space-y-4'
            }>
              {sortedAds.map(ad => (
                <AdCard
                  key={ad.id}
                  ad={ad}
                  viewMode={viewMode}
                />
              ))}
            </div>

            {/* رسالة عدم وجود نتائج */}
            {sortedAds.length === 0 && (
              <div className="text-center py-12">
                <div className="w-24 h-24 flex items-center justify-center mx-auto mb-4">
                  <CategoryIcon
                    category="books"
                    className="w-20 h-20"
                    color="#3b82f6"
                  />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد كتب</h3>
                <p className="text-gray-600">جرب تعديل الفلاتر للعثور على المزيد من النتائج</p>
              </div>
            )}
          </div>
        </div>
        {/* Modal الفلاتر للموبايل */}
        <FilterModal
          isOpen={isFilterModalOpen}
          onClose={() => setIsFilterModalOpen(false)}
          filters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={clearFilters}
          category="books"
        />
      </main>

      <Footer />
    </div>
  );
};

export default BooksEducationPage;
