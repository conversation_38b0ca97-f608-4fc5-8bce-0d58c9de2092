'use client';

import React from 'react';

interface MembershipBadgeProps {
  planId: string;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

const MembershipBadge: React.FC<MembershipBadgeProps> = ({ 
  planId, 
  size = 'md', 
  showLabel = true 
}) => {
  const getBadgeInfo = (planId: string) => {
    switch (planId) {
      // باقات الأفراد
      case 'individual-free':
        return null; // الباقة المجانية بدون هوية عضوية مصغرة
      case 'individual-basic':
        return {
          image: '/images/هوية العضوية المصغرة للأفراد/هوية العضوية المصغرة الباقة الأساسية للأفراد.png',
          label: 'هوية العضوية - أساسية',
          color: 'text-blue-600'
        };
      case 'individual-premium':
        return {
          image: '/images/هوية العضوية المصغرة للأفراد/هوية العضوية المصغرة الباقة المميزة للأفراد.png',
          label: 'هوية العضوية - مميزة',
          color: 'text-yellow-600'
        };
      case 'individual-vip':
        return {
          image: '/images/هوية العضوية المصغرة للأفراد/هوية العضوية المصغرة باقة الأعمال للأفراد.png',
          label: 'هوية العضوية - أعمال',
          color: 'text-purple-600'
        };

      // باقات الشركات
      case 'business-starter':
        return {
          image: '/images/هوية العضوية المصغرة للشركات والمكاتب العقارية/باقة البداية التجريبية (المجانية ( -ID-هوية العضوية المصغرة.png',
          label: 'هوية العضوية - البداية',
          color: 'text-green-600'
        };
      case 'business-professional':
        return {
          image: '/images/هوية العضوية المصغرة للشركات والمكاتب العقارية/الباقة المهنية (الفضية)-ID-هوية العضوية المصغرة.png',
          label: 'هوية العضوية - المهنية',
          color: 'text-gray-600'
        };
      case 'business-gold':
        return {
          image: '/images/هوية العضوية المصغرة للشركات والمكاتب العقارية/الباقة الذهبية -ID-هوية العضوية المصغرة.png',
          label: 'هوية العضوية - الذهبية',
          color: 'text-yellow-600'
        };

      // باقة المكاتب العقارية
      case 'real-estate-office':
        return {
          image: '/images/هوية العضوية المصغرة للشركات والمكاتب العقارية/ID- باقة المكاتب العقارية-هوية العضوية المصغرة.png',
          label: 'هوية العضوية - مكتب عقاري',
          color: 'text-blue-600'
        };

      default:
        return null;
    }
  };

  const badgeInfo = getBadgeInfo(planId);
  
  if (!badgeInfo) {
    return null;
  }

  const sizeClasses = {
    sm: 'w-12 h-8',
    md: 'w-16 h-12',
    lg: 'w-20 h-16'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  return (
    <div className="flex flex-col items-center gap-2">
      <div className={`${sizeClasses[size]} relative`}>
        <img
          src={badgeInfo.image}
          alt={badgeInfo.label}
          className="w-full h-full object-contain rounded-lg shadow-sm"
        />
      </div>
      {showLabel && (
        <span className={`${textSizeClasses[size]} ${badgeInfo.color} font-medium text-center`}>
          {badgeInfo.label}
        </span>
      )}
    </div>
  );
};

export default MembershipBadge;
