'use client';

import { Resume } from '@/lib/jobs';

interface ResumePrintViewProps {
  resume: Resume;
}

const ResumePrintView = ({ resume }: ResumePrintViewProps) => {
  return (
    <>
      <style jsx>{`
        @media print {
          .print-container {
            margin: 0;
            padding: 20px;
            font-family: 'Arial', sans-serif;
            color: #000;
            background: white;
          }
          
          .mycv-logo {
            position: fixed;
            bottom: 10px;
            right: 10px;
            opacity: 0.7;
            z-index: 1000;
          }
          
          .no-print {
            display: none !important;
          }
          
          .page-break {
            page-break-before: always;
          }
        }
      `}</style>
      
      <div className="print-container">
        {/* شعار MyCv في الطباعة */}
        <div className="mycv-logo">
          <img 
            src="/images/MyCV Logo.jpg" 
            alt="MyCv" 
            style={{ width: '60px', height: 'auto' }}
          />
        </div>

        {/* Header */}
        <div className="mb-6">
          <div className="flex items-start gap-6">
            {/* الصورة الشخصية */}
            {resume.personalInfo?.profileImage && (
              <div className="flex-shrink-0">
                <img
                  src={resume.personalInfo.profileImage}
                  alt="الصورة الشخصية"
                  className="w-24 h-24 rounded-full object-cover border-2 border-gray-300"
                />
              </div>
            )}
            
            <div className="flex-1">
              <h1 className="text-3xl font-bold text-gray-800 mb-2">
                {resume.personalInfo?.firstName} {resume.personalInfo?.lastName}
              </h1>
              <h2 className="text-xl text-gray-600 mb-3">{resume.personalInfo?.title}</h2>
              <p className="text-gray-700 leading-relaxed">{resume.personalInfo?.summary}</p>
            </div>
          </div>
        </div>

        {/* معلومات التواصل */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">معلومات التواصل</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div><strong>الهاتف:</strong> {resume.contactInfo?.phone}</div>
            <div><strong>البريد الإلكتروني:</strong> {resume.contactInfo?.email}</div>
            <div><strong>العنوان:</strong> {resume.contactInfo?.address}</div>
            <div><strong>المدينة:</strong> {resume.contactInfo?.city}</div>
          </div>
        </div>

        {/* الخبرات العملية */}
        {resume.experiences && resume.experiences.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b-2 border-gray-300 pb-1">
              الخبرات العملية
            </h3>
            {resume.experiences.map((exp, index) => (
              <div key={exp.id} className="mb-4">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h4 className="font-semibold text-gray-800">{exp.position}</h4>
                    <p className="text-gray-600">{exp.company}</p>
                  </div>
                  <div className="text-sm text-gray-500">
                    {exp.startDate} - {exp.current ? 'حتى الآن' : exp.endDate}
                  </div>
                </div>
                <p className="text-gray-700 text-sm mb-2">{exp.description}</p>
                {exp.achievements && exp.achievements.length > 0 && (
                  <ul className="list-disc list-inside text-sm text-gray-600">
                    {exp.achievements.map((achievement, i) => (
                      <li key={i}>{achievement}</li>
                    ))}
                  </ul>
                )}
              </div>
            ))}
          </div>
        )}

        {/* التعليم */}
        {resume.education && resume.education.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b-2 border-gray-300 pb-1">
              التعليم والشهادات
            </h3>
            {resume.education.map((edu, index) => (
              <div key={edu.id} className="mb-3">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-semibold text-gray-800">{edu.degree}</h4>
                    <p className="text-gray-600">{edu.institution}</p>
                    {edu.gpa && <p className="text-sm text-gray-500">المعدل: {edu.gpa}</p>}
                  </div>
                  <div className="text-sm text-gray-500">
                    {edu.startDate} - {edu.current ? 'حتى الآن' : edu.endDate}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* المهارات */}
        {resume.skills && resume.skills.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b-2 border-gray-300 pb-1">
              المهارات
            </h3>
            <div className="grid grid-cols-2 gap-2">
              {resume.skills.map((skill, index) => (
                <div key={skill.id} className="flex justify-between items-center">
                  <span className="text-gray-700">{skill.name}</span>
                  <span className="text-sm text-gray-500">{skill.level}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* اللغات */}
        {resume.languages && resume.languages.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b-2 border-gray-300 pb-1">
              اللغات
            </h3>
            <div className="grid grid-cols-2 gap-2">
              {resume.languages.map((lang, index) => (
                <div key={lang.id} className="flex justify-between items-center">
                  <span className="text-gray-700">{lang.name}</span>
                  <span className="text-sm text-gray-500">{lang.level}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* الدورات والشهادات */}
        {resume.courses && resume.courses.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3 border-b-2 border-gray-300 pb-1">
              الدورات والشهادات
            </h3>
            {resume.courses.map((course, index) => (
              <div key={course.id} className="mb-3">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-semibold text-gray-800">{course.name}</h4>
                    <p className="text-gray-600">{course.provider}</p>
                  </div>
                  <div className="text-sm text-gray-500">{course.completionDate}</div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Footer مع شعار MyCv */}
        <div className="mt-8 pt-4 border-t border-gray-300 text-center">
          <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
            <span>تم إنشاء هذه السيرة الذاتية باستخدام</span>
            <img 
              src="/images/MyCV Logo.jpg" 
              alt="MyCv" 
              className="h-6 w-auto"
            />
            <span>- منصة متكاملة للسير الذاتية والتوظيف</span>
          </div>
        </div>
      </div>
    </>
  );
};

export default ResumePrintView;
