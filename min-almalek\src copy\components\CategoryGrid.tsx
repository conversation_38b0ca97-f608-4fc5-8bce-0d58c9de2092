'use client';

import Link from 'next/link';
import { useState } from 'react';
import SafeNavigationButton from './SafeNavigationButton';
import MyCvLogo from './MyCvLogo';

const categories = [
  {
    id: 'real-estate',
    name: 'العقارات',
    icon: '🏠',
    description: 'شقق، فيلات، أراضي',
    count: '12,450',
    subcategories: ['للبيع', 'للإيجار', 'تجاري', 'أراضي'],
    color: 'bg-blue-500'
  },
  {
    id: 'cars',
    name: 'السيارات',
    icon: '🚗',
    description: 'سيارات جديدة ومستعملة',
    count: '8,230',
    subcategories: ['جديدة', 'مستعملة', 'دراجات', 'قطع غيار'],
    color: 'bg-red-500'
  },
  {
    id: 'electronics',
    name: 'الإلكترونيات',
    icon: '📱',
    description: 'هواتف، حاسوب، أجهزة',
    count: '15,670',
    subcategories: ['هواتف', 'حاسوب', 'تلفزيون', 'ألعاب'],
    color: 'bg-purple-500'
  },
  {
    id: 'jobs',
    name: 'الوظائف',
    icon: '💼',
    description: 'فرص عمل متنوعة',
    count: '3,890',
    subcategories: ['دوام كامل', 'دوام جزئي', 'عمل حر', 'تدريب'],
    color: 'bg-green-500'
  },
  {
    id: 'business-plans',
    name: 'خطط الشركات',
    icon: '🏢',
    description: 'باقات النشر والترقية للشركات',
    count: '3 باقات',
    subcategories: ['أساسية', 'مميزة', 'أعمال', 'مخصصة'],
    color: 'bg-gradient-to-r from-yellow-400 to-amber-500',
    isSpecial: true
  },
  {
    id: 'services',
    name: 'الخدمات',
    icon: '🔧',
    description: 'خدمات متنوعة',
    count: '5,420',
    subcategories: ['صيانة', 'تنظيف', 'توصيل', 'تعليم'],
    color: 'bg-orange-500'
  },
  {
    id: 'fashion',
    name: 'الأزياء والموضة',
    icon: '👗',
    description: 'ملابس، أحذية، إكسسوارات',
    count: '7,650',
    subcategories: ['رجالي', 'نسائي', 'أطفال', 'أحذية'],
    color: 'bg-pink-500'
  },
  {
    id: 'furniture',
    name: 'الأثاث والمنزل',
    icon: '🛋️',
    description: 'أثاث، ديكور، أدوات منزلية',
    count: '4,320',
    subcategories: ['غرف نوم', 'صالونات', 'مطبخ', 'ديكور'],
    color: 'bg-yellow-500'
  },
  {
    id: 'sports',
    name: 'الرياضة والترفيه',
    icon: '⚽',
    description: 'معدات رياضية، ألعاب',
    count: '2,180',
    subcategories: ['كرة قدم', 'لياقة', 'ألعاب', 'رحلات'],
    color: 'bg-indigo-500'
  },
  {
    id: 'books',
    name: 'الكتب والتعليم',
    icon: '📚',
    description: 'كتب، دورات، تعليم',
    count: '1,890',
    subcategories: ['كتب', 'دورات', 'جامعة', 'مدرسة'],
    color: 'bg-teal-500'
  },
  {
    id: 'pets',
    name: 'الحيوانات الأليفة',
    icon: '🐕',
    description: 'حيوانات، مستلزمات',
    count: '980',
    subcategories: ['قطط', 'كلاب', 'طيور', 'مستلزمات'],
    color: 'bg-amber-500'
  },
  {
    id: 'health',
    name: 'الصحة والجمال',
    icon: '💊',
    description: 'منتجات صحية وتجميل',
    count: '1,560',
    subcategories: ['أدوية', 'تجميل', 'عطور', 'صحة'],
    color: 'bg-emerald-500'
  },
  {
    id: 'food',
    name: 'الطعام والمشروبات',
    icon: '🍕',
    description: 'مطاعم، طعام، مشروبات',
    count: '2,340',
    subcategories: ['مطاعم', 'حلويات', 'مشروبات', 'طبخ'],
    color: 'bg-rose-500'
  }
];

const CategoryGrid = () => {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);

  return (
    <section className="py-12 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">تصفح حسب التصنيف</h2>
          <p className="text-gray-600 text-lg">اختر التصنيف المناسب لك وابدأ البحث أو أضف إعلانك الخاص</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {categories.map((category) => (
            <div
              key={category.id}
              className={`group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border overflow-hidden relative ${
                category.isSpecial
                  ? 'border-yellow-300 hover:border-yellow-400 ring-2 ring-yellow-100'
                  : 'border-gray-100 hover:border-primary-200'
              }`}
              onMouseEnter={() => setHoveredCategory(category.id)}
              onMouseLeave={() => setHoveredCategory(null)}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className={`w-12 h-12 ${category.color} rounded-lg flex items-center justify-center text-white text-2xl group-hover:scale-110 transition-transform`}>
                    {category.id === 'jobs' ? (
                      <MyCvLogo size="sm" variant="square" className="filter brightness-0 invert" />
                    ) : (
                      category.icon
                    )}
                  </div>
                  <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                    {category.count}
                  </span>
                </div>

                <h3 className="text-lg font-semibold text-gray-800 mb-2 group-hover:text-primary-600 transition-colors">
                  {category.name}
                </h3>

                <p className="text-gray-600 text-sm mb-4">
                  {category.description}
                </p>

                {/* Special content for Jobs category */}
                {category.id === 'jobs' && (
                  <div className="mb-4 p-3 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-lg border border-yellow-200 shadow-sm">
                    <div className="flex items-center gap-2 mb-2">
                      <MyCvLogo size="sm" variant="square" />
                      <span className="text-xs text-amber-800 font-medium">
                        مدعوم من قبل تطبيق MyCv
                      </span>
                    </div>
                    <p className="text-xs text-amber-700">
                      منصة متكاملة للسير الذاتية والتوظيف
                    </p>
                  </div>
                )}

                {/* Special content for Business Plans category */}
                {category.id === 'business-plans' && (
                  <div className="mb-4 space-y-3">
                    {/* Payment Methods */}
                    <div className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                      <h4 className="text-xs font-semibold text-blue-800 mb-2 flex items-center gap-1">
                        💳 وسائل الدفع المتاحة
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">Visa</span>
                        <span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded-full">MasterCard</span>
                        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">PayPal</span>
                        <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full">Apple Pay</span>
                        <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">Cash App</span>
                      </div>
                    </div>

                    {/* Pricing Preview */}
                    <div className="p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
                      <h4 className="text-xs font-semibold text-green-800 mb-2 flex items-center gap-1">
                        💰 الأسعار تبدأ من
                      </h4>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-bold text-green-700">25,000 ل.س</span>
                        <span className="text-xs text-green-600">/شهرياً</span>
                      </div>
                    </div>

                    {/* Features Preview */}
                    <div className="p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200">
                      <h4 className="text-xs font-semibold text-purple-800 mb-2 flex items-center gap-1">
                        ⭐ المميزات الرئيسية
                      </h4>
                      <ul className="text-xs text-purple-700 space-y-1">
                        <li>• إعلانات مميزة</li>
                        <li>• أولوية في النتائج</li>
                        <li>• إحصائيات مفصلة</li>
                      </ul>
                    </div>
                  </div>
                )}

                <div className="flex flex-wrap gap-1">
                  {category.subcategories.slice(0, 3).map((sub, index) => (
                    <span
                      key={index}
                      className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                    >
                      {sub}
                    </span>
                  ))}
                  {category.subcategories.length > 3 && (
                    <span className="text-xs text-gray-400 px-2 py-1">
                      +{category.subcategories.length - 3}
                    </span>
                  )}
                </div>
              </div>

              {/* أيقونة إضافة إعلان */}
              {hoveredCategory === category.id && category.id !== 'business-plans' && (
                <div className="absolute top-4 left-4">
                  <SafeNavigationButton
                    href={`/add-ad?category=${category.id}`}
                    className="bg-green-500 hover:bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-lg transition-all duration-200 shadow-lg hover:shadow-xl"
                    title="إضافة إعلان"
                  >
                    +
                  </SafeNavigationButton>
                </div>
              )}

              {/* أزرار التصفح والإضافة */}
              <div className={`px-6 py-3 transition-colors ${
                category.isSpecial
                  ? 'bg-gradient-to-r from-yellow-50 to-amber-50 group-hover:from-yellow-100 group-hover:to-amber-100'
                  : 'bg-gray-50 group-hover:bg-primary-50'
              }`}>
                {category.id === 'business-plans' ? (
                  <Link
                    href="/pricing"
                    className={`block text-sm font-medium ${
                      category.isSpecial
                        ? 'text-amber-700 group-hover:text-amber-800'
                        : 'text-gray-600 group-hover:text-primary-600'
                    }`}
                  >
                    اشترك الآن ←
                  </Link>
                ) : (
                  <div className="flex items-center justify-between">
                    <Link
                      href={
                        category.id === 'jobs' ? '/jobs' :
                        `/category/${category.id}`
                      }
                      className={`text-sm font-medium ${
                        category.isSpecial
                          ? 'text-amber-700 group-hover:text-amber-800'
                          : 'text-gray-600 group-hover:text-primary-600'
                      }`}
                    >
                      تصفح الإعلانات ←
                    </Link>
                    <SafeNavigationButton
                      href={`/add-ad?category=${category.id}`}
                      className="bg-primary-600 hover:bg-primary-700 text-white text-xs px-3 py-1 rounded-full transition-colors"
                    >
                      + إضافة
                    </SafeNavigationButton>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-10">
          <Link
            href="/categories"
            className="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium"
          >
            عرض جميع التصنيفات
            <span className="mr-2">→</span>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default CategoryGrid;
