import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';
import { UploadService, uploadConfigs } from '@/lib/upload';

export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    // التحقق من صحة الجلسة
    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const type = formData.get('type') as keyof typeof uploadConfigs;
    const adIdValue = formData.get('adId');
    const adId = adIdValue ? parseInt(adIdValue as string) : undefined;

    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, error: 'لم يتم اختيار أي ملفات' },
        { status: 400 }
      );
    }

    if (!type || !uploadConfigs[type]) {
      return NextResponse.json(
        { success: false, error: 'نوع الرفع غير صحيح' },
        { status: 400 }
      );
    }

    // رفع الملفات
    const result = await UploadService.uploadMultipleFiles(
      files,
      sessionResult.data.id,
      type,
      adId
    );

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data,
        message: `تم رفع ${result.data?.length || 0} ملف بنجاح`,
        errors: result.errors
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'فشل في رفع الملفات',
          errors: result.errors
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('خطأ في رفع الملفات:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    // التحقق من صحة الجلسة
    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const adIdValue = searchParams.get('adId');
    const adId = adIdValue ? parseInt(adIdValue) : undefined;
    const action = searchParams.get('action');

    if (action === 'stats') {
      const stats = UploadService.getFileStats(sessionResult.data.id);
      return NextResponse.json({
        success: true,
        data: stats
      });
    }

    // الحصول على ملفات المستخدم
    const files = UploadService.getUserFiles(sessionResult.data.id, adId);

    return NextResponse.json({
      success: true,
      data: files
    });

  } catch (error) {
    console.error('خطأ في جلب الملفات:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    // التحقق من صحة الجلسة
    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { fileId } = await request.json();

    if (!fileId) {
      return NextResponse.json(
        { success: false, error: 'معرف الملف مطلوب' },
        { status: 400 }
      );
    }

    // حذف الملف
    const result = UploadService.deleteFile(fileId, sessionResult.data.id);

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'تم حذف الملف بنجاح'
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('خطأ في حذف الملف:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
