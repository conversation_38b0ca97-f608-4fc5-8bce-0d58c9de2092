'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import CategoryIcon from '@/components/CategoryIcon';

const HealthBeautyPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedSubCategory, setSelectedSubCategory] = useState('');
  const [selectedGender, setSelectedGender] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [priceRange, setPriceRange] = useState([0, 1000000]);
  const [sortBy, setSortBy] = useState('newest');
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // بيانات وهمية للإعلانات
  const mockAds = [
    {
      id: '1',
      title: 'صالون تجميل نسائي - خدمات شاملة',
      price: 50000,
      currency: 'SYP',
      location: 'دمشق - المزة',
      images: ['/images/health/salon1.jpg'],
      category: 'health-beauty',
      subCategory: 'personal-care',
      serviceType: 'مكياج عرائس',
      targetGender: 'نساء',
      views: 245,
      isFavorite: false,
      isPromoted: true,
      postedAt: '2024-01-15',
      description: 'صالون تجميل متخصص في خدمات العرائس والمكياج الاحترافي'
    },
    {
      id: '2',
      title: 'جلسات ليزر إزالة الشعر',
      price: 75000,
      currency: 'SYP',
      location: 'دمشق - أبو رمانة',
      images: ['/images/health/laser1.jpg'],
      category: 'health-beauty',
      subCategory: 'personal-care',
      serviceType: 'إزالة بالليزر',
      targetGender: 'الجنسين',
      views: 189,
      isFavorite: false,
      isPromoted: false,
      postedAt: '2024-01-14',
      description: 'جلسات ليزر احترافية لإزالة الشعر بأحدث التقنيات'
    },
    {
      id: '3',
      title: 'مدرب شخصي - تدريب منزلي',
      price: 25000,
      currency: 'SYP',
      location: 'دمشق - الشعلان',
      images: ['/images/health/trainer1.jpg'],
      category: 'health-beauty',
      subCategory: 'fitness-aesthetic',
      serviceType: 'مدربين شخصيين',
      targetGender: 'الجنسين',
      views: 156,
      isFavorite: true,
      isPromoted: false,
      postedAt: '2024-01-13',
      description: 'مدرب شخصي معتمد لتدريب اللياقة البدنية في المنزل'
    }
  ];

  const categories = [
    { id: 'personal-care', name: 'العناية الشخصية' },
    { id: 'fitness-aesthetic', name: 'اللياقة والتجميل الطبي' },
    { id: 'medical-health', name: 'الطب والصحة' },
    { id: 'body-care', name: 'العناية بالجسم والاسترخاء' },
    { id: 'products', name: 'منتجات الصحة والجمال' }
  ];

  const subCategories = {
    'personal-care': [
      { id: 'hair', name: 'الشعر' },
      { id: 'makeup', name: 'المكياج' },
      { id: 'skincare', name: 'البشرة' },
      { id: 'nails', name: 'الأظافر' },
      { id: 'hair-removal', name: 'إزالة الشعر' }
    ],
    'fitness-aesthetic': [
      { id: 'fitness', name: 'اللياقة' },
      { id: 'medical-aesthetic', name: 'التجميل الطبي' }
    ],
    'medical-health': [
      { id: 'clinics', name: 'العيادات' },
      { id: 'home-medical', name: 'الخدمات الطبية المنزلية' }
    ],
    'body-care': [
      { id: 'massage', name: 'المساج والاسترخاء' },
      { id: 'slimming', name: 'التنحيف وتشكيل الجسم' }
    ],
    'products': [
      { id: 'cosmetics', name: 'مستحضرات التجميل' },
      { id: 'devices', name: 'أجهزة العناية' },
      { id: 'supplements', name: 'المكملات والأعشاب' }
    ]
  };

  const locations = [
    'دمشق - المزة',
    'دمشق - أبو رمانة',
    'دمشق - الشعلان',
    'دمشق - المالكي',
    'دمشق - الصالحية',
    'حلب - الفرقان',
    'حلب - الجميلية',
    'حمص - الوعر',
    'حمص - الخالدية'
  ];

  const filteredAds = mockAds.filter(ad => {
    const matchesSearch = ad.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         ad.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || ad.subCategory === selectedCategory;
    const matchesSubCategory = !selectedSubCategory || ad.serviceType?.includes(selectedSubCategory);
    const matchesGender = !selectedGender || ad.targetGender === selectedGender || ad.targetGender === 'الجنسين';
    const matchesLocation = !selectedLocation || ad.location.includes(selectedLocation);
    const matchesPrice = ad.price >= priceRange[0] && ad.price <= priceRange[1];

    return matchesSearch && matchesCategory && matchesSubCategory && matchesGender && matchesLocation && matchesPrice;
  });

  const sortedAds = [...filteredAds].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'popular':
        return b.views - a.views;
      case 'newest':
      default:
        return new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime();
    }
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-white">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          {/* العنوان الرئيسي */}
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 flex items-center justify-center">
              <CategoryIcon
                category="health"
                className="w-10 h-10"
                color="#ec4899"
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">الصحة والجمال</h1>
              <p className="text-gray-600">اكتشف أفضل خدمات الصحة والجمال في منطقتك</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* فلاتر الصحة والجمال */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-lg p-6 sticky top-8 border border-gray-200">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                <img
                  src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="فلاتر"
                  className="w-6 h-6 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(236, 72, 153, 0.6))'
                  }}
                />
                فلاتر البحث
              </h2>

              {/* البحث */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="ابحث عن خدمة..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                />
              </div>

              {/* الفئة الرئيسية */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الرئيسية</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => {
                    setSelectedCategory(e.target.value);
                    setSelectedSubCategory('');
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                >
                  <option value="">جميع الفئات</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* الفئة الفرعية */}
              {selectedCategory && subCategories[selectedCategory as keyof typeof subCategories] && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الفرعية</label>
                  <select
                    value={selectedSubCategory}
                    onChange={(e) => setSelectedSubCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    <option value="">جميع الفئات الفرعية</option>
                    {subCategories[selectedCategory as keyof typeof subCategories].map(subCategory => (
                      <option key={subCategory.id} value={subCategory.name}>
                        {subCategory.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* الجنس المستهدف */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الجنس المستهدف</label>
                <select
                  value={selectedGender}
                  onChange={(e) => setSelectedGender(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                >
                  <option value="">الجميع</option>
                  <option value="رجال">رجال</option>
                  <option value="نساء">نساء</option>
                  <option value="الجنسين">الجنسين</option>
                </select>
              </div>

              {/* المحافظة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">المحافظة</label>
                <select
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                >
                  <option value="">جميع المحافظات</option>
                  <option value="دمشق">دمشق</option>
                  <option value="ريف دمشق">ريف دمشق</option>
                  <option value="حلب">حلب</option>
                  <option value="حمص">حمص</option>
                  <option value="حماة">حماة</option>
                  <option value="اللاذقية">اللاذقية</option>
                  <option value="طرطوس">طرطوس</option>
                  <option value="إدلب">إدلب</option>
                  <option value="درعا">درعا</option>
                  <option value="السويداء">السويداء</option>
                  <option value="القنيطرة">القنيطرة</option>
                  <option value="دير الزور">دير الزور</option>
                  <option value="الرقة">الرقة</option>
                  <option value="الحسكة">الحسكة</option>
                </select>
              </div>

              {/* نطاق السعر */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    value={priceRange[0]}
                    onChange={(e) => setPriceRange([parseInt(e.target.value) || 0, priceRange[1]])}
                    placeholder="السعر من"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  />
                  <input
                    type="number"
                    value={priceRange[1]}
                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value) || 1000000])}
                    placeholder="السعر إلى"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  />
                </div>
              </div>

              {/* إعادة تعيين الفلاتر */}
              <button
                onClick={() => {
                  setSelectedCategory('');
                  setSelectedSubCategory('');
                  setSelectedGender('');
                  setSelectedLocation('');
                  setPriceRange([0, 1000000]);
                  setSearchQuery('');
                }}
                className="w-full bg-gradient-to-r from-pink-500 to-pink-700 text-white py-3 px-4 rounded-lg hover:from-pink-600 hover:to-pink-800 transition-all duration-300 font-medium"
              >
                إعادة تعيين الفلاتر
              </button>
            </div>
          </div>

          {/* النتائج */}
          <div className="lg:col-span-3">
            {/* شريط الترتيب */}
            <div className="bg-white rounded-xl shadow-lg p-4 mb-6 border border-gray-200">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="text-gray-600">
                  عرض <span className="font-semibold text-pink-600">{sortedAds.length}</span> من أصل {sortedAds.length} إعلان
                </div>

                <div className="flex items-center gap-4">
                  {/* طريقة العرض */}
                  <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'grid'
                          ? 'bg-pink-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      🔲 شبكة
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'list'
                          ? 'bg-pink-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      📋 قائمة
                    </button>
                  </div>

                  {/* الترتيب */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="popular">الأكثر مشاهدة</option>
                    <option value="price-low">السعر: من الأقل للأعلى</option>
                    <option value="price-high">السعر: من الأعلى للأقل</option>
                  </select>
                </div>
              </div>
            </div>

            {/* الإعلانات */}
            <div className={
              viewMode === 'grid'
                ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                : 'space-y-4'
            }>
              {sortedAds.map(ad => (
                <AdCard
                  key={ad.id}
                  ad={ad}
                  viewMode={viewMode}
                />
              ))}
            </div>

            {/* رسالة عدم وجود نتائج */}
            {sortedAds.length === 0 && (
              <div className="text-center py-12">
                <div className="w-24 h-24 flex items-center justify-center mx-auto mb-4">
                  <CategoryIcon
                    category="health"
                    className="w-20 h-20"
                    color="#ec4899"
                  />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد نتائج</h3>
                <p className="text-gray-600">جرب تعديل الفلاتر للعثور على المزيد من النتائج</p>
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default HealthBeautyPage;
