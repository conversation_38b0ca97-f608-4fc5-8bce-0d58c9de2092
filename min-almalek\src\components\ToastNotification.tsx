'use client';

import { useState, useEffect } from 'react';
import { AdvancedNotification } from './AdvancedNotificationSystem';

interface ToastNotificationProps {
  notification: AdvancedNotification;
  onClose: () => void;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

export default function ToastNotification({ 
  notification, 
  onClose, 
  position = 'top-right' 
}: ToastNotificationProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    // إظهار التوست مع تأخير بسيط للحصول على تأثير الانزلاق
    const showTimer = setTimeout(() => setIsVisible(true), 100);
    
    // إخفاء التوست تلقائياً إذا كان مؤقتاً
    let hideTimer: NodeJS.Timeout;
    if (notification.autoHide !== false) {
      const duration = notification.duration || getDurationByType(notification.type);
      hideTimer = setTimeout(() => {
        handleClose();
      }, duration);
    }

    return () => {
      clearTimeout(showTimer);
      if (hideTimer) clearTimeout(hideTimer);
    };
  }, [notification]);

  const handleClose = () => {
    setIsLeaving(true);
    setTimeout(() => {
      onClose();
    }, 300); // مدة الانيميشن
  };

  const getDurationByType = (type: string): number => {
    switch (type) {
      case 'success': return 4000;
      case 'error': return 8000;
      case 'warning': return 6000;
      case 'welcome': return 10000;
      case 'payment': return 6000;
      case 'logout': return 3000;
      default: return 5000;
    }
  };

  const getToastStyles = () => {
    const baseStyles = `
      fixed z-50 max-w-sm w-full transition-all duration-300 ease-in-out transform
      ${isVisible && !isLeaving ? 'translate-x-0 opacity-100' : ''}
      ${!isVisible || isLeaving ? getHiddenTransform() : ''}
    `;

    const positionStyles = {
      'top-right': 'top-4 right-4',
      'top-left': 'top-4 left-4',
      'bottom-right': 'bottom-4 right-4',
      'bottom-left': 'bottom-4 left-4',
      'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
      'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2'
    };

    return `${baseStyles} ${positionStyles[position]}`;
  };

  const getHiddenTransform = () => {
    switch (position) {
      case 'top-right':
      case 'bottom-right':
        return 'translate-x-full opacity-0';
      case 'top-left':
      case 'bottom-left':
        return '-translate-x-full opacity-0';
      case 'top-center':
        return '-translate-y-full opacity-0';
      case 'bottom-center':
        return 'translate-y-full opacity-0';
      default:
        return 'translate-x-full opacity-0';
    }
  };

  const getTypeStyles = () => {
    switch (notification.type) {
      case 'success':
      case 'payment':
      case 'ad_posted':
      case 'welcome':
        return {
          container: 'bg-green-50/95 backdrop-blur-md border border-green-200/50 shadow-lg shadow-green-500/20',
          icon: 'text-green-600',
          title: 'text-green-800',
          message: 'text-green-700',
          button: 'text-green-600 hover:text-green-800 hover:bg-green-100',
          glow: 'shadow-green-400/30',
          animation: 'animate-pulse'
        };
      
      case 'error':
        return {
          container: 'bg-red-50/95 backdrop-blur-md border border-red-200/50 shadow-lg shadow-red-500/20',
          icon: 'text-red-600',
          title: 'text-red-800',
          message: 'text-red-700',
          button: 'text-red-600 hover:text-red-800 hover:bg-red-100',
          glow: 'shadow-red-400/30',
          animation: 'animate-bounce'
        };
      
      case 'warning':
        return {
          container: 'bg-orange-50/95 backdrop-blur-md border border-orange-200/50 shadow-lg shadow-orange-500/20',
          icon: 'text-orange-600',
          title: 'text-orange-800',
          message: 'text-orange-700',
          button: 'text-orange-600 hover:text-orange-800 hover:bg-orange-100',
          glow: 'shadow-orange-400/30',
          animation: 'animate-pulse'
        };
      
      case 'info':
      case 'message':
      case 'search_alert':
        return {
          container: 'bg-blue-50/95 backdrop-blur-md border border-blue-200/50 shadow-lg shadow-blue-500/20',
          icon: 'text-blue-600',
          title: 'text-blue-800',
          message: 'text-blue-700',
          button: 'text-blue-600 hover:text-blue-800 hover:bg-blue-100',
          glow: 'shadow-blue-400/30',
          animation: 'animate-pulse'
        };
      
      case 'favorite':
        return {
          container: 'bg-pink-50/95 backdrop-blur-md border border-pink-200/50 shadow-lg shadow-pink-500/20',
          icon: 'text-pink-600',
          title: 'text-pink-800',
          message: 'text-pink-700',
          button: 'text-pink-600 hover:text-pink-800 hover:bg-pink-100',
          glow: 'shadow-pink-400/30',
          animation: 'animate-pulse'
        };
      
      case 'logout':
        return {
          container: 'bg-gray-50/95 backdrop-blur-md border border-gray-200/50 shadow-lg shadow-gray-500/20',
          icon: 'text-gray-600',
          title: 'text-gray-800',
          message: 'text-gray-700',
          button: 'text-gray-600 hover:text-gray-800 hover:bg-gray-100',
          glow: 'shadow-gray-400/30',
          animation: 'animate-pulse'
        };
      
      default:
        return {
          container: 'bg-white/95 backdrop-blur-md border border-gray-200/50 shadow-lg',
          icon: 'text-gray-600',
          title: 'text-gray-800',
          message: 'text-gray-700',
          button: 'text-gray-600 hover:text-gray-800 hover:bg-gray-100',
          glow: 'shadow-gray-400/30',
          animation: ''
        };
    }
  };

  const styles = getTypeStyles();

  const getPriorityIndicator = () => {
    if (notification.priority === 'urgent') {
      return (
        <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping"></div>
      );
    }
    if (notification.priority === 'high') {
      return (
        <div className="absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></div>
      );
    }
    return null;
  };

  return (
    <div className={getToastStyles()}>
      <div className={`
        relative rounded-xl p-4 ${styles.container} ${styles.glow}
        hover:shadow-xl transition-all duration-300
        border-l-4 ${notification.type === 'success' || notification.type === 'payment' || notification.type === 'welcome' ? 'border-l-green-500' : 
                     notification.type === 'error' ? 'border-l-red-500' :
                     notification.type === 'warning' ? 'border-l-orange-500' : 'border-l-blue-500'}
      `}>
        {getPriorityIndicator()}
        
        <div className="flex items-start gap-3">
          {/* الأيقونة */}
          <div className={`flex-shrink-0 ${styles.icon} ${styles.animation}`}>
            <span className="text-2xl">
              {notification.icon || getDefaultIcon(notification.type)}
            </span>
          </div>

          {/* المحتوى */}
          <div className="flex-1 min-w-0">
            <h4 className={`font-semibold text-sm ${styles.title} mb-1`}>
              {notification.title}
            </h4>
            <p className={`text-sm ${styles.message} leading-relaxed`}>
              {notification.message}
            </p>
            
            {/* زر الإجراء */}
            {notification.actionUrl && notification.actionText && (
              <div className="mt-3">
                <a
                  href={notification.actionUrl}
                  className={`inline-flex items-center text-xs font-medium ${styles.button} 
                    px-3 py-1 rounded-lg transition-colors duration-200`}
                  onClick={handleClose}
                >
                  {notification.actionText}
                  <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </div>
            )}
          </div>

          {/* زر الإغلاق */}
          <button
            onClick={handleClose}
            className={`flex-shrink-0 ${styles.button} p-1 rounded-lg transition-colors duration-200`}
            aria-label="إغلاق الإشعار"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* شريط التقدم للإشعارات المؤقتة */}
        {notification.autoHide !== false && (
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200/30 rounded-b-xl overflow-hidden">
            <div 
              className={`h-full transition-all ease-linear ${
                notification.type === 'success' || notification.type === 'payment' || notification.type === 'welcome' ? 'bg-green-500' :
                notification.type === 'error' ? 'bg-red-500' :
                notification.type === 'warning' ? 'bg-orange-500' : 'bg-blue-500'
              }`}
              style={{
                width: '100%',
                animation: `shrink ${getDurationByType(notification.type)}ms linear forwards`
              }}
            />
          </div>
        )}
      </div>

      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </div>
  );
}

function getDefaultIcon(type: string): string {
  switch (type) {
    case 'success': return '✅';
    case 'error': return '❌';
    case 'warning': return '⚠️';
    case 'info': return 'ℹ️';
    case 'welcome': return '🎉';
    case 'payment': return '💳';
    case 'message': return '💬';
    case 'favorite': return '❤️';
    case 'ad_posted': return '📢';
    case 'search_alert': return '🔍';
    case 'logout': return '👋';
    default: return '🔔';
  }
}
