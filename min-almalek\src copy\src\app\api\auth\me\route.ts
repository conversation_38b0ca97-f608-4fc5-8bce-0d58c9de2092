import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    // التحقق من صحة الجلسة
    const result = await AuthService.verifySession(token);

    if (result.success && result.data) {
      return NextResponse.json({
        success: true,
        data: result.data
      });
    } else {
      // حذف كوكي الجلسة المنتهية الصلاحية
      const response = NextResponse.json(
        { success: false, error: result.error },
        { status: 401 }
      );

      response.cookies.set('auth-token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 0
      });

      return response;
    }

  } catch (error) {
    console.error('خطأ في التحقق من الجلسة:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    // التحقق من صحة الجلسة
    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const updateData = await request.json();
    
    // تحديث الملف الشخصي
    const result = await AuthService.updateProfile(sessionResult.data.id, updateData);

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data,
        message: 'تم تحديث الملف الشخصي بنجاح'
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('خطأ في تحديث الملف الشخصي:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
