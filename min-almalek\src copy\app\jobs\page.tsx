'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import JobFilters, { JobFilters as JobFiltersType } from '@/components/JobFilters';
import JobCard, { FeaturedJobCard } from '@/components/JobCard';
import MyCvLogo from '@/components/MyCvLogo';
import { JobPosting, JobUtils, JOB_CATEGORIES } from '@/lib/jobs';

// بيانات تجريبية للوظائف
const sampleJobs: JobPosting[] = [
  {
    id: '1',
    companyId: 'comp1',
    companyName: 'شركة التقنيات المتقدمة',
    title: 'مطور React.js متقدم',
    department: 'التطوير',
    location: 'دمشق',
    workType: 'دوام كامل',
    workModel: 'مختلط',
    salaryRange: {
      min: 800000,
      max: 1200000,
      currency: 'ل.س',
      period: 'شهري'
    },
    description: 'نبحث عن مطور React.js متقدم للانضمام إلى فريقنا المتميز. المرشح المثالي يجب أن يكون لديه خبرة قوية في React، Redux، وTypeScript.',
    requirements: [
      'خبرة 3+ سنوات في React.js',
      'إتقان TypeScript',
      'خبرة في Redux أو Context API',
      'معرفة بـ Next.js',
      'خبرة في Git'
    ],
    responsibilities: [
      'تطوير واجهات المستخدم التفاعلية',
      'كتابة كود نظيف وقابل للصيانة',
      'التعاون مع فريق التصميم',
      'مراجعة الكود',
      'تحسين الأداء'
    ],
    benefits: [
      'تأمين صحي',
      'مكافآت سنوية',
      'تدريب وتطوير',
      'عمل مرن'
    ],
    skills: ['React.js', 'TypeScript', 'Redux', 'Next.js', 'Git'],
    experienceLevel: 'متقدم',
    experienceYears: { min: 3, max: 7 },
    educationLevel: 'بكالوريوس',
    applicationDeadline: '2024-03-15',
    postedDate: '2024-02-15',
    status: 'نشط',
    applicationsCount: 45,
    featured: true,
    urgent: false,
    category: 'technology'
  },
  {
    id: '2',
    companyId: 'comp2',
    companyName: 'مستشفى الشام',
    title: 'طبيب أطفال',
    department: 'الطب',
    location: 'حلب',
    workType: 'دوام كامل',
    workModel: 'حضوري',
    salaryRange: {
      min: 1500000,
      max: 2500000,
      currency: 'ل.س',
      period: 'شهري'
    },
    description: 'مطلوب طبيب أطفال مختص للعمل في مستشفى الشام. يجب أن يكون حاصل على شهادة الاختصاص في طب الأطفال.',
    requirements: [
      'شهادة طب عام',
      'شهادة اختصاص في طب الأطفال',
      'خبرة 2+ سنوات',
      'إجادة اللغة الإنجليزية',
      'مهارات تواصل ممتازة'
    ],
    responsibilities: [
      'فحص وعلاج الأطفال',
      'كتابة التقارير الطبية',
      'التعاون مع الفريق الطبي',
      'متابعة الحالات المزمنة',
      'تقديم الاستشارات الطبية'
    ],
    benefits: [
      'تأمين صحي شامل',
      'تأمين اجتماعي',
      'إجازة سنوية',
      'مكافآت'
    ],
    skills: ['طب الأطفال', 'التشخيص', 'العلاج', 'التواصل', 'الطوارئ'],
    experienceLevel: 'متوسط',
    experienceYears: { min: 2, max: 8 },
    educationLevel: 'ماجستير',
    applicationDeadline: '2024-03-20',
    postedDate: '2024-02-10',
    status: 'نشط',
    applicationsCount: 23,
    featured: false,
    urgent: true,
    category: 'medical'
  },
  {
    id: '3',
    companyId: 'comp3',
    companyName: 'شركة الهندسة الحديثة',
    title: 'مهندس مدني أول',
    department: 'الهندسة',
    location: 'دمشق',
    workType: 'دوام كامل',
    workModel: 'حضوري',
    salaryRange: {
      min: 1000000,
      max: 1800000,
      currency: 'ل.س',
      period: 'شهري'
    },
    description: 'نبحث عن مهندس مدني أول للإشراف على المشاريع الإنشائية الكبيرة. المرشح المثالي يجب أن يكون لديه خبرة واسعة في إدارة المشاريع.',
    requirements: [
      'شهادة هندسة مدنية',
      'خبرة 5+ سنوات في المشاريع الإنشائية',
      'إتقان AutoCAD و Revit',
      'خبرة في إدارة المشاريع',
      'رخصة مزاولة مهنة'
    ],
    responsibilities: [
      'الإشراف على المشاريع الإنشائية',
      'مراجعة المخططات الهندسية',
      'إدارة فرق العمل',
      'ضمان الجودة والسلامة',
      'إعداد التقارير الفنية'
    ],
    benefits: [
      'راتب تنافسي',
      'تأمين صحي',
      'مواصلات',
      'تدريب متخصص'
    ],
    skills: ['الهندسة المدنية', 'AutoCAD', 'Revit', 'إدارة المشاريع', 'الإشراف'],
    experienceLevel: 'متقدم',
    experienceYears: { min: 5, max: 12 },
    educationLevel: 'بكالوريوس',
    applicationDeadline: '2024-03-25',
    postedDate: '2024-02-12',
    status: 'نشط',
    applicationsCount: 18,
    featured: true,
    urgent: false,
    category: 'engineering'
  }
];

export default function JobsPage() {
  const [jobs, setJobs] = useState<JobPosting[]>(sampleJobs);
  const [filteredJobs, setFilteredJobs] = useState<JobPosting[]>(sampleJobs);
  const [filters, setFilters] = useState<JobFiltersType>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    let result = [...jobs];

    // تطبيق البحث
    if (searchQuery) {
      result = JobUtils.searchJobs(result, searchQuery);
    }

    // تطبيق الفلاتر
    result = JobUtils.filterJobs(result, filters);

    // ترتيب النتائج
    switch (sortBy) {
      case 'newest':
        result.sort((a, b) => new Date(b.postedDate).getTime() - new Date(a.postedDate).getTime());
        break;
      case 'salary-high':
        result.sort((a, b) => b.salaryRange.max - a.salaryRange.max);
        break;
      case 'salary-low':
        result.sort((a, b) => a.salaryRange.min - b.salaryRange.min);
        break;
      case 'applications':
        result.sort((a, b) => b.applicationsCount - a.applicationsCount);
        break;
    }

    setFilteredJobs(result);
  }, [jobs, filters, searchQuery, sortBy]);

  const featuredJobs = filteredJobs.filter(job => job.featured);
  const regularJobs = filteredJobs.filter(job => !job.featured);

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="bg-gradient-to-r from-primary-600 to-blue-600 rounded-2xl text-white p-8 mb-8">
          <div className="max-w-4xl">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-4xl font-bold mb-4">🚀 ابحث عن وظيفة أحلامك</h1>
                <p className="text-xl text-primary-100">
                  اكتشف آلاف الفرص الوظيفية في جميع المجالات والتخصصات
                </p>
              </div>

              {/* Resume Actions */}
              <div className="hidden lg:flex flex-col gap-3">
                <Link
                  href="/resume/create"
                  className="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center gap-2 whitespace-nowrap"
                >
                  📄 إنشاء سيرة ذاتية
                </Link>
                <Link
                  href="/resume/my-resume"
                  className="bg-primary-700 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-800 transition-colors flex items-center gap-2 whitespace-nowrap border border-primary-500"
                >
                  👤 سيرتي الذاتية
                </Link>
              </div>
            </div>

            {/* Search Bar */}
            <div className="flex gap-4 mb-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="ابحث عن وظيفة، شركة، أو مهارة..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg text-gray-800 focus:ring-2 focus:ring-white focus:outline-none"
                />
              </div>
              <button className="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                🔍 بحث
              </button>
            </div>

            {/* Mobile Resume Actions */}
            <div className="lg:hidden flex gap-3">
              <Link
                href="/resume/create"
                className="flex-1 bg-white text-primary-600 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors text-center"
              >
                📄 إنشاء سيرة ذاتية
              </Link>
              <Link
                href="/resume/my-resume"
                className="flex-1 bg-primary-700 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-800 transition-colors text-center border border-primary-500"
              >
                👤 سيرتي الذاتية
              </Link>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-primary-600 mb-2">{jobs.length}</div>
            <div className="text-gray-600">وظيفة متاحة</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-green-600 mb-2">150+</div>
            <div className="text-gray-600">شركة</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-blue-600 mb-2">2.5K</div>
            <div className="text-gray-600">باحث عن عمل</div>
          </div>
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-3xl font-bold text-purple-600 mb-2">85%</div>
            <div className="text-gray-600">معدل التوظيف</div>
          </div>
        </div>

        {/* Resume Section */}
        <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-8 mb-8 border border-green-200">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <div className="flex items-center gap-3 mb-4">
                  <h2 className="text-3xl font-bold text-gray-800">
                    📄 أنشئ سيرتك الذاتية الاحترافية
                  </h2>
                </div>

                {/* MyCv Branding */}
                <div className="flex items-center gap-2 mb-4 p-3 bg-gradient-to-r from-yellow-100 to-amber-100 rounded-lg border border-yellow-200">
                  <MyCvLogo size="md" variant="square" />
                  <div>
                    <span className="text-sm font-medium text-amber-800">مدعوم من قبل تطبيق MyCv</span>
                    <p className="text-xs text-amber-700">منصة متكاملة للسير الذاتية والتوظيف</p>
                  </div>
                </div>

                <p className="text-gray-600 mb-6">
                  أنشئ سيرة ذاتية مميزة تساعدك في الحصول على وظيفة أحلامك.
                  استخدم أدواتنا المتقدمة لإنشاء سيرة ذاتية احترافية في دقائق معدودة.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center gap-3">
                    <span className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm">✓</span>
                    <span className="text-gray-700">تصميم احترافي</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm">✓</span>
                    <span className="text-gray-700">سهل الاستخدام</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm">✓</span>
                    <span className="text-gray-700">قابل للتخصيص</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center text-sm">✓</span>
                    <span className="text-gray-700">مرئي للشركات</span>
                  </div>
                </div>

                <div className="flex gap-4">
                  <Link
                    href="/resume/create"
                    className="bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors flex items-center gap-2"
                  >
                    📄 إنشاء سيرة ذاتية جديدة
                  </Link>
                  <Link
                    href="/resume/templates"
                    className="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors border border-primary-200 flex items-center gap-2"
                  >
                    🎨 عرض القوالب
                  </Link>
                </div>
              </div>

              <div className="text-center">
                <div className="bg-white rounded-xl shadow-lg p-6 max-w-sm mx-auto">
                  <div className="text-6xl mb-4">📄</div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">سيرة ذاتية احترافية</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    في 8 خطوات بسيطة
                  </p>
                  <div className="flex justify-center gap-2">
                    <div className="w-2 h-2 bg-primary-600 rounded-full"></div>
                    <div className="w-2 h-2 bg-primary-400 rounded-full"></div>
                    <div className="w-2 h-2 bg-primary-200 rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Company Section */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-8 mb-8 border border-purple-200">
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div className="text-center lg:text-right">
                <div className="bg-white rounded-xl shadow-lg p-6 max-w-sm mx-auto">
                  <div className="text-6xl mb-4">🏢</div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">للشركات وأصحاب العمل</h3>
                  <p className="text-gray-600 text-sm mb-4">
                    ابحث عن أفضل المواهب
                  </p>
                  <div className="flex justify-center gap-2">
                    <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                    <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                    <div className="w-2 h-2 bg-purple-200 rounded-full"></div>
                  </div>
                </div>
              </div>

              <div>
                <h2 className="text-3xl font-bold text-gray-800 mb-4">
                  🚀 انشر وظائفك واعثر على أفضل المواهب
                </h2>

                {/* MyCv Branding للشركات */}
                <div className="flex items-center gap-2 mb-4 p-3 bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg border border-purple-200">
                  <MyCvLogo size="md" variant="square" />
                  <div>
                    <span className="text-sm font-medium text-purple-800">مدعوم من قبل تطبيق MyCv</span>
                    <p className="text-xs text-purple-700">منصة متكاملة للسير الذاتية والتوظيف</p>
                  </div>
                </div>

                <p className="text-gray-600 mb-6">
                  انشر إعلانات وظائف احترافية واستقطب أفضل المرشحين.
                  استخدم أدواتنا المتقدمة للوصول إلى آلاف الباحثين عن عمل.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center gap-3">
                    <span className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm">✓</span>
                    <span className="text-gray-700">وصول لآلاف المرشحين</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm">✓</span>
                    <span className="text-gray-700">فلترة متقدمة</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm">✓</span>
                    <span className="text-gray-700">إدارة التقديمات</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center text-sm">✓</span>
                    <span className="text-gray-700">شارات توثيق</span>
                  </div>
                </div>

                <div className="flex gap-4">
                  <Link
                    href="/jobs/post"
                    className="bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors flex items-center gap-2"
                  >
                    💼 انشر وظيفة جديدة
                  </Link>
                  <Link
                    href="/company/dashboard"
                    className="bg-white text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors border border-purple-200 flex items-center gap-2"
                  >
                    📊 لوحة التحكم
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <JobFilters
              onFiltersChange={setFilters}
              initialFilters={filters}
            />
          </div>

          {/* Jobs Content */}
          <div className="lg:col-span-3">
            {/* Controls */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <span className="text-gray-600">
                  {filteredJobs.length} وظيفة متاحة
                </span>
                {Object.keys(filters).length > 0 && (
                  <span className="text-sm text-primary-600">
                    (مفلترة)
                  </span>
                )}
              </div>

              <div className="flex items-center gap-4">
                {/* Sort */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                >
                  <option value="newest">الأحدث</option>
                  <option value="salary-high">الراتب (الأعلى)</option>
                  <option value="salary-low">الراتب (الأقل)</option>
                  <option value="applications">الأكثر تقديماً</option>
                </select>

                {/* View Mode */}
                <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`px-3 py-2 ${viewMode === 'grid' ? 'bg-primary-600 text-white' : 'bg-white text-gray-600'}`}
                  >
                    ⊞
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`px-3 py-2 ${viewMode === 'list' ? 'bg-primary-600 text-white' : 'bg-white text-gray-600'}`}
                  >
                    ☰
                  </button>
                </div>
              </div>
            </div>

            {/* Featured Jobs */}
            {featuredJobs.length > 0 && (
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-800 mb-4">⭐ الوظائف المميزة</h2>
                <div className="space-y-6">
                  {featuredJobs.map(job => (
                    <FeaturedJobCard key={job.id} job={job} />
                  ))}
                </div>
              </div>
            )}

            {/* Regular Jobs */}
            {regularJobs.length > 0 && (
              <div>
                <h2 className="text-2xl font-bold text-gray-800 mb-4">💼 جميع الوظائف</h2>
                <div className={viewMode === 'grid' ? 'grid grid-cols-1 gap-6' : 'space-y-4'}>
                  {regularJobs.map(job => (
                    <JobCard key={job.id} job={job} />
                  ))}
                </div>
              </div>
            )}

            {/* No Results */}
            {filteredJobs.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد وظائف مطابقة</h3>
                <p className="text-gray-600 mb-4">جرب تعديل معايير البحث أو الفلاتر</p>
                <button
                  onClick={() => {
                    setFilters({});
                    setSearchQuery('');
                  }}
                  className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  مسح جميع الفلاتر
                </button>
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
