'use client';

import { useState } from 'react';

interface ServicesData {
  serviceType?: string;
  subService?: string;
  experienceLevel?: string;
  availability?: string;
  serviceArea?: string[];
  workingHours?: {
    start?: string;
    end?: string;
    days?: string[];
  };
  qualifications?: string[];
  languages?: string[];
  tools?: string[];
  warranty?: string;
  emergencyService?: boolean;
  homeService?: boolean;
  remoteService?: boolean;
}

interface ServicesSpecificFieldsProps {
  data: ServicesData;
  onChange: (data: ServicesData) => void;
}

const servicesCategories = {
  'cleaning': {
    name: 'خدمات التنظيف',
    icon: '🧹',
    subServices: [
      'تنظيف منازل وشقق',
      'تنظيف مكاتب وشركات',
      'تنظيف سجاد وموكيت',
      'تنظيف خزانات مياه',
      'تنظيف واجهات زجاج',
      'تعقيم منازل ومؤسسات'
    ]
  },
  'maintenance': {
    name: 'خدمات الصيانة والإصلاح',
    icon: '🔧',
    subServices: [
      'صيانة كهرباء عامة',
      'صيانة تمديدات المياه',
      'صيانة الصرف الصحي',
      'صيانة الأقفال',
      'تصليح أجهزة كهربائية (غسالات، برادات، أفران...)',
      'صيانة كمبيوترات ولابتوبات',
      'صيانة موبايلات',
      'صيانة لابتوبات',
      'صيانة الشاشات',
      'تركيب ستلايت',
      'تركيب انترنت',
      'تصليح أثاث منزلي',
      'تركيب ألمنيوم',
      'تركيب زجاج',
      'صيانة وصبغ أبواب وشبابيك',
      'تنجيد مفروشات',
      'صيانة مصاعد'
    ]
  },
  'construction': {
    name: 'خدمات البناء والتشطيب',
    icon: '🏗️',
    subServices: [
      'أعمال الدهان والديكور',
      'بلاط وسيراميك',
      'تركيب جبصين',
      'تمديدات صحية',
      'تمديدات كهربائية',
      'تركيب مطابخ وأعمال نجارة',
      'تلبيس حجر وعزل أسطح',
      'بناء وتشطيب منازل',
      'تفصيل وتركيب أبواب'
    ]
  },
  'education': {
    name: 'الدروس الخصوصية والتعليم',
    icon: '📚',
    subServices: [
      'دروس خصوصية (رياضيات، فيزياء، كيمياء...)',
      'تعليم لغات (إنكليزي، فرنسي، تركي، ألماني...)',
      'تعليم برمجة',
      'تعليم برامج تصميم (Photoshop, Illustrator…)',
      'دروس دعم لطلاب المدارس والجامعات',
      'دورات تقوية لطلاب الشهادات (تاسع، بكالوريا)',
      'خدمات الترجمة والتحرير',
      'ترجمة (عربي - إنكليزي - فرنسي - تركي...)',
      'تدقيق لغوي وكتابة مقالات'
    ]
  },
  'photography': {
    name: 'التصوير والمونتاج',
    icon: '📸',
    subServices: [
      'تصوير مناسبات (أعراس، خطوبة، تخرج...)',
      'تصوير منتجات تجارية',
      'تصوير فيديو إعلاني',
      'خدمات مونتاج احترافي',
      'تصميم فيديوهات سوشيال ميديا'
    ]
  },
  'beauty': {
    name: 'الخدمات التجميلية',
    icon: '💄',
    subServices: [
      'كوافير نسائي / رجالي',
      'تركيب أظافر ورموش',
      'ميك أب وتسريحات',
      'خدمات عناية بالبشرة والشعر',
      'حلاقة أطفال'
    ]
  },
  'automotive': {
    name: 'خدمات السيارات',
    icon: '🚗',
    subServices: [
      'غسيل سيارات',
      'صيانة كهرباء سيارات',
      'ميكانيكي متنقل',
      'تبديل زيوت وفلاتر',
      'تركيب إكسسوارات',
      'نقل سيارات (سحب)'
    ]
  },
  'logistics': {
    name: 'النقل والخدمات اللوجستية',
    icon: '🚚',
    subServices: [
      'نقل أثاث داخل وخارج المدينة',
      'تغليف وتوضيب الأثاث',
      'شحن داخلي بين المحافظات',
      'شحن خارجي (لبنان، العراق، تركيا...)',
      'استيراد وتصدير',
      'تخليص جمركي',
      'نقل السيارات (الشطحة)',
      'نقل مواد البناء',
      'نقل عام',
      'نقل خاص (سائق خاص)'
    ]
  },
  'childcare': {
    name: 'رعاية الأطفال والمسنين',
    icon: '👶',
    subServices: [
      'جليسات أطفال',
      'مربية أطفال في المنزل',
      'جليسات مسنين',
      'رعاية مرضى'
    ]
  },
  'pets': {
    name: 'خدمات الحيوانات الأليفة',
    icon: '🐕',
    subServices: [
      'تدريب كلاب',
      'استضافة ورعاية حيوانات',
      'تنظيف وتجميل الحيوانات',
      'بيع وشراء مستلزمات'
    ]
  },
  'cooking': {
    name: 'الطبخ المنزلي',
    icon: '🍳',
    subServices: [
      'طبخ منزلي للمناسبات',
      'تحضير وجبات للدوام',
      'حلويات منزلية',
      'معجنات ومونة'
    ]
  },
  'design': {
    name: 'خدمات التصميم والتقنية',
    icon: '🎨',
    subServices: [
      'تصميم جرافيك',
      'تصميم مواقع وتطبيقات',
      'تسويق إلكتروني',
      'إدارة صفحات سوشيال ميديا',
      'خدمات SEO'
    ]
  },
  'legal': {
    name: 'خدمات إدارية وقانونية',
    icon: '⚖️',
    subServices: [
      'إنشاء شركات',
      'خدمات استخراج أوراق رسمية',
      'معاملات سفر وإقامة',
      'استشارات قانونية',
      'تعقيب معاملات قانونية',
      'تصديق وثائق',
      'محاماة'
    ]
  },
  'entertainment': {
    name: 'خدمات ترفيهية وفنية',
    icon: '🎭',
    subServices: [
      'تنظيم حفلات ومناسبات',
      'DJ وموسيقيين للمناسبات',
      'تأجير بدلات عرائس',
      'كوش وتزيين حفلات'
    ]
  }
};

export default function ServicesSpecificFields({ data, onChange }: ServicesSpecificFieldsProps) {
  const [selectedQualifications, setSelectedQualifications] = useState<string[]>(data.qualifications || []);
  const [selectedLanguages, setSelectedLanguages] = useState<string[]>(data.languages || []);
  const [selectedTools, setSelectedTools] = useState<string[]>(data.tools || []);
  const [selectedDays, setSelectedDays] = useState<string[]>(data.workingHours?.days || []);

  const handleChange = (field: string, value: any) => {
    const newData = { ...data, [field]: value };
    onChange(newData);
  };

  const handleWorkingHoursChange = (field: string, value: any) => {
    const newWorkingHours = { ...data.workingHours, [field]: value };
    handleChange('workingHours', newWorkingHours);
  };

  const toggleArrayItem = (array: string[], item: string, setter: (arr: string[]) => void, field: string) => {
    const newArray = array.includes(item) 
      ? array.filter(i => i !== item)
      : [...array, item];
    setter(newArray);
    handleChange(field, newArray);
  };

  const qualificationsList = [
    'شهادة جامعية',
    'دبلوم تقني',
    'شهادات مهنية',
    'خبرة عملية',
    'دورات تدريبية',
    'رخصة مهنية',
    'عضوية نقابة'
  ];

  const languagesList = [
    'العربية',
    'الإنجليزية',
    'الفرنسية',
    'التركية',
    'الألمانية',
    'الروسية',
    'الإسبانية'
  ];

  const toolsList = [
    'أدوات يدوية',
    'أدوات كهربائية',
    'معدات متخصصة',
    'مواد تنظيف',
    'أجهزة قياس',
    'برامج حاسوبية',
    'مركبة خاصة'
  ];

  const daysList = [
    'السبت',
    'الأحد',
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
    'الجمعة'
  ];

  return (
    <div className="space-y-6">
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-orange-800 mb-4 flex items-center gap-2">
          🛠️ تفاصيل الخدمة
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* نوع الخدمة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              نوع الخدمة *
            </label>
            <select
              value={data.serviceType || ''}
              onChange={(e) => handleChange('serviceType', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              required
            >
              <option value="">اختر نوع الخدمة</option>
              {Object.entries(servicesCategories).map(([key, category]) => (
                <option key={key} value={key}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* الخدمة الفرعية */}
          {data.serviceType && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الخدمة المحددة *
              </label>
              <select
                value={data.subService || ''}
                onChange={(e) => handleChange('subService', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                required
              >
                <option value="">اختر الخدمة المحددة</option>
                {servicesCategories[data.serviceType as keyof typeof servicesCategories]?.subServices.map((service, index) => (
                  <option key={index} value={service}>
                    {service}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>
      </div>

      {/* معلومات إضافية */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="text-md font-semibold text-gray-800 mb-4">معلومات إضافية</h4>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* مستوى الخبرة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              مستوى الخبرة
            </label>
            <select
              value={data.experienceLevel || ''}
              onChange={(e) => handleChange('experienceLevel', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="">اختر مستوى الخبرة</option>
              <option value="beginner">مبتدئ (أقل من سنة)</option>
              <option value="intermediate">متوسط (1-3 سنوات)</option>
              <option value="advanced">متقدم (3-5 سنوات)</option>
              <option value="expert">خبير (أكثر من 5 سنوات)</option>
            </select>
          </div>

          {/* التوفر */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              التوفر
            </label>
            <select
              value={data.availability || ''}
              onChange={(e) => handleChange('availability', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="">اختر التوفر</option>
              <option value="available">متاح الآن</option>
              <option value="morning">صباحاً</option>
              <option value="afternoon">بعد الظهر</option>
              <option value="evening">مساءً</option>
              <option value="weekend">نهاية الأسبوع</option>
              <option value="flexible">مرن</option>
            </select>
          </div>

          {/* ضمان الخدمة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              ضمان الخدمة
            </label>
            <select
              value={data.warranty || ''}
              onChange={(e) => handleChange('warranty', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
            >
              <option value="">بدون ضمان</option>
              <option value="1-week">أسبوع واحد</option>
              <option value="1-month">شهر واحد</option>
              <option value="3-months">3 أشهر</option>
              <option value="6-months">6 أشهر</option>
              <option value="1-year">سنة واحدة</option>
            </select>
          </div>
        </div>

        {/* ساعات العمل */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            ساعات العمل
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-xs text-gray-600 mb-1">من الساعة</label>
              <input
                type="time"
                value={data.workingHours?.start || ''}
                onChange={(e) => handleWorkingHoursChange('start', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            </div>
            <div>
              <label className="block text-xs text-gray-600 mb-1">إلى الساعة</label>
              <input
                type="time"
                value={data.workingHours?.end || ''}
                onChange={(e) => handleWorkingHoursChange('end', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              />
            </div>
          </div>


        </div>





        {/* الأدوات والمعدات */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            الأدوات والمعدات المتوفرة
          </label>
          <div className="flex flex-wrap gap-2">
            {toolsList.map((tool) => (
              <button
                key={tool}
                type="button"
                onClick={() => toggleArrayItem(selectedTools, tool, setSelectedTools, 'tools')}
                className={`px-3 py-2 rounded-lg text-sm transition-colors ${
                  selectedTools.includes(tool)
                    ? 'bg-orange-500 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {tool}
              </button>
            ))}
          </div>
        </div>

        {/* خيارات الخدمة */}
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            خيارات الخدمة
          </label>
          <div className="space-y-3">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={data.emergencyService || false}
                onChange={(e) => handleChange('emergencyService', e.target.checked)}
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="mr-2 text-sm text-gray-700">خدمة طوارئ (24/7)</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={data.homeService || false}
                onChange={(e) => handleChange('homeService', e.target.checked)}
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="mr-2 text-sm text-gray-700">خدمة منزلية</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={data.remoteService || false}
                onChange={(e) => handleChange('remoteService', e.target.checked)}
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="mr-2 text-sm text-gray-700">خدمة عن بُعد</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}
