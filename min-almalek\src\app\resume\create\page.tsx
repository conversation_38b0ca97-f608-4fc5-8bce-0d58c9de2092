'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ResumeBuilder from '@/components/ResumeBuilder';
import MyCvLogo from '@/components/MyCvLogo';
import ConfirmDialog from '@/components/ConfirmDialog';
import { useConfirmDialog } from '@/hooks/useConfirmDialog';
import { Resume } from '@/lib/jobs';

export default function CreateResumePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { confirmState, confirmCancel, hideConfirm } = useConfirmDialog();

  const handleSave = async (resume: Resume) => {
    setIsLoading(true);
    try {
      // حفظ السيرة الذاتية في localStorage
      localStorage.setItem('userResume', JSON.stringify(resume));
      console.log('Saving resume:', resume);

      // محاكاة عملية الحفظ
      await new Promise(resolve => setTimeout(resolve, 1000));

      alert('تم حفظ السيرة الذاتية بنجاح!');
      // إعادة توجيه إلى صفحة السيرة الذاتية
      router.push('/resume/my-resume');
    } catch (error) {
      console.error('Error saving resume:', error);
      alert('حدث خطأ أثناء حفظ السيرة الذاتية');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = async () => {
    const confirmed = await confirmCancel('إنشاء السيرة الذاتية');
    if (confirmed) {
      router.push('/jobs');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-4 mb-4">
            <button
              onClick={() => router.push('/jobs')}
              className="bg-orange-100/60 text-orange-700 px-6 py-3 rounded-lg font-semibold border border-orange-300 flex items-center gap-2 shadow-sm hover:bg-orange-200/80 focus:outline-none active:shadow-lg active:shadow-orange-400/70 active:scale-105 transition-all duration-200"
              style={{ boxShadow: '0 0 0px rgba(251, 146, 60, 0.2)' }}
            >
              <span className="text-lg">←</span>
              <span className="font-semibold">عودة</span>
            </button>
            <h1 className="text-4xl font-bold text-gray-800">
              📄 إنشاء السيرة الذاتية
            </h1>
          </div>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-6">
            أنشئ سيرة ذاتية احترافية تساعدك في الحصول على وظيفة أحلامك
          </p>

          {/* MyCv Branding */}
          <div className="flex items-center justify-center gap-2 mb-4 p-3 bg-gradient-to-r from-yellow-100 to-amber-100 rounded-lg border border-yellow-200 max-w-md mx-auto">
            <MyCvLogo size="md" variant="square" />
            <div className="text-left">
              <span className="text-sm font-medium text-amber-800 block">مدعوم من قبل تطبيق MyCv</span>
              <p className="text-xs text-amber-700">منصة متكاملة للسير الذاتية والتوظيف</p>
            </div>
          </div>
        </div>

        {/* Benefits */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-white rounded-lg p-4 text-center shadow-sm border border-gray-100 hover:border-green-300 hover:shadow-md transition-all duration-300">
            <div className="w-12 h-12 mx-auto mb-3 flex items-center justify-center bg-green-50 rounded-full">
              <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-800 mb-2 text-sm">سريع وسهل</h3>
            <p className="text-gray-600 text-xs">
              أنشئ سيرتك الذاتية في دقائق معدودة
            </p>
          </div>

          <div className="bg-white rounded-lg p-4 text-center shadow-sm border border-gray-100 hover:border-green-300 hover:shadow-md transition-all duration-300">
            <div className="w-12 h-12 mx-auto mb-3 flex items-center justify-center bg-green-50 rounded-full">
              <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L15.09 8.26L22 9L15.09 9.74L12 16L8.91 9.74L2 9L8.91 8.26L12 2Z"/>
                <path d="M12 6.5L10.5 10.5L6.5 11L10.5 11.5L12 15.5L13.5 11.5L17.5 11L13.5 10.5L12 6.5Z"/>
                <circle cx="12" cy="11" r="2" fill="white"/>
                <path d="M10.5 10.5L11.5 11.5L13.5 9.5" stroke="currentColor" strokeWidth="0.5" fill="none"/>
              </svg>
            </div>
            <h3 className="font-semibold text-gray-800 mb-2 text-sm">احترافية</h3>
            <p className="text-gray-600 text-xs">
              تصميم احترافي يلفت انتباه أصحاب العمل
            </p>
          </div>

          <div className="bg-white rounded-lg p-4 text-center shadow-sm border border-gray-100 hover:border-green-300 hover:shadow-md transition-all duration-300">
            <div className="w-12 h-12 mx-auto mb-3 flex items-center justify-center bg-green-50 rounded-full">
              <svg className="w-6 h-6 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
              </svg>
            </div>
            <h3 className="font-semibold text-gray-800 mb-2 text-sm">قابلة للاكتشاف</h3>
            <p className="text-gray-600 text-xs">
              تظهر للشركات الباحثة عن مواهب مثلك
            </p>
          </div>
        </div>

        {/* Resume Builder */}
        {isLoading ? (
          <div className="bg-white rounded-xl shadow-lg p-12 text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">جاري حفظ السيرة الذاتية...</h3>
            <p className="text-gray-600">يرجى الانتظار قليلاً</p>
          </div>
        ) : (
          <ResumeBuilder
            onSave={handleSave}
            onCancel={handleCancel}
          />
        )}

        {/* Tips */}
        <div className="mt-12 bg-blue-50 rounded-xl p-8">
          <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            💡 نصائح لسيرة ذاتية مميزة
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">كن مختصراً ومحدداً</h4>
                  <p className="text-gray-600 text-sm">
                    استخدم جمل قصيرة وواضحة لوصف خبراتك ومهاراتك
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">أبرز إنجازاتك</h4>
                  <p className="text-gray-600 text-sm">
                    اذكر الإنجازات المحددة والنتائج التي حققتها
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">استخدم كلمات مفتاحية</h4>
                  <p className="text-gray-600 text-sm">
                    أضف المهارات والتقنيات المطلوبة في مجال عملك
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">حدث معلوماتك</h4>
                  <p className="text-gray-600 text-sm">
                    تأكد من أن جميع المعلومات حديثة ودقيقة
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">راجع الأخطاء</h4>
                  <p className="text-gray-600 text-sm">
                    تأكد من عدم وجود أخطاء إملائية أو نحوية
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">كن صادقاً</h4>
                  <p className="text-gray-600 text-sm">
                    لا تبالغ في وصف مهاراتك أو خبراتك
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />

      {/* Confirm Dialog */}
      <ConfirmDialog
        isOpen={confirmState.isOpen}
        onClose={hideConfirm}
        onConfirm={confirmState.onConfirm}
        title={confirmState.title}
        message={confirmState.message}
        confirmText={confirmState.confirmText}
        cancelText={confirmState.cancelText}
        type={confirmState.type}
        icon={confirmState.icon}
      />
    </div>
  );
}
