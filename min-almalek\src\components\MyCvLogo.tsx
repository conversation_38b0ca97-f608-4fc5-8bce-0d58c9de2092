import React from 'react';
import Image from 'next/image';

interface MyCvLogoProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'icon' | 'text' | 'full' | 'square';
  className?: string;
  showText?: boolean;
}

const MyCvLogo: React.FC<MyCvLogoProps> = ({
  size = 'md',
  variant = 'full',
  className = '',
  showText = true
}) => {
  const sizeClasses = {
    xs: 'w-4 h-4',
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  const textSizes = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  const LogoImage = ({ isSquare = false }: { isSquare?: boolean }) => (
    <div className={`${sizeClasses[size]} relative ${isSquare ? 'rounded-lg overflow-hidden' : ''} ${className}`}>
      <Image
        src="/images/MyCV (1).jpg"
        alt="MyCv - منصة متكاملة للسير الذاتية والتوظيف"
        fill
        className="object-contain"
        priority
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      />
    </div>
  );

  const TextComponent = () => (
    <span className={`${textSizes[size]} font-bold text-gray-800 ${className}`}>
      MyCv
    </span>
  );

  const FullComponent = () => (
    <div className={`flex items-center gap-2 ${className}`}>
      <LogoImage />
      {showText && <TextComponent />}
    </div>
  );

  switch (variant) {
    case 'icon':
      return <LogoImage />;
    case 'square':
      return <LogoImage isSquare={true} />;
    case 'text':
      return <TextComponent />;
    case 'full':
    default:
      return <FullComponent />;
  }
};

export default MyCvLogo;
