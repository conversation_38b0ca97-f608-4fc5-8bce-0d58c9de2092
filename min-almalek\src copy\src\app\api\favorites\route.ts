import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';
import { FavoritesService } from '@/lib/favorites';

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    // التحقق من صحة الجلسة
    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'ads';

    switch (type) {
      case 'ads':
        const favorites = FavoritesService.getUserFavorites(sessionResult.data.id);
        return NextResponse.json({
          success: true,
          data: favorites
        });

      case 'searches':
        const savedSearches = FavoritesService.getUserSavedSearches(sessionResult.data.id);
        return NextResponse.json({
          success: true,
          data: savedSearches
        });

      case 'sellers':
        const followedSellers = FavoritesService.getFollowedSellers(sessionResult.data.id);
        return NextResponse.json({
          success: true,
          data: followedSellers
        });

      case 'stats':
        const stats = FavoritesService.getFavoritesStats(sessionResult.data.id);
        return NextResponse.json({
          success: true,
          data: stats
        });

      default:
        return NextResponse.json(
          { success: false, error: 'نوع غير صحيح' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('خطأ في جلب المفضلة:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    // التحقق من صحة الجلسة
    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { type, adId, sellerId, name, filters, alertsEnabled } = await request.json();

    switch (type) {
      case 'add_ad':
        if (!adId) {
          return NextResponse.json(
            { success: false, error: 'معرف الإعلان مطلوب' },
            { status: 400 }
          );
        }

        const addResult = FavoritesService.addToFavorites(sessionResult.data.id, adId);
        
        if (addResult.success) {
          return NextResponse.json({
            success: true,
            message: 'تم إضافة الإعلان للمفضلة'
          });
        } else {
          return NextResponse.json(
            { success: false, error: addResult.error },
            { status: 400 }
          );
        }

      case 'save_search':
        if (!name || !filters) {
          return NextResponse.json(
            { success: false, error: 'اسم البحث والفلاتر مطلوبة' },
            { status: 400 }
          );
        }

        const saveResult = FavoritesService.saveSearch(
          sessionResult.data.id,
          name,
          filters,
          alertsEnabled || false
        );

        if (saveResult.success) {
          return NextResponse.json({
            success: true,
            data: saveResult.data,
            message: 'تم حفظ البحث بنجاح'
          });
        } else {
          return NextResponse.json(
            { success: false, error: saveResult.error },
            { status: 400 }
          );
        }

      case 'follow_seller':
        if (!sellerId) {
          return NextResponse.json(
            { success: false, error: 'معرف البائع مطلوب' },
            { status: 400 }
          );
        }

        const followResult = FavoritesService.followSeller(sessionResult.data.id, sellerId);

        if (followResult.success) {
          return NextResponse.json({
            success: true,
            message: 'تم متابعة البائع بنجاح'
          });
        } else {
          return NextResponse.json(
            { success: false, error: followResult.error },
            { status: 400 }
          );
        }

      default:
        return NextResponse.json(
          { success: false, error: 'نوع العملية غير صحيح' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('خطأ في إضافة للمفضلة:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    // التحقق من صحة الجلسة
    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { type, adId, sellerId, searchId } = await request.json();

    switch (type) {
      case 'remove_ad':
        if (!adId) {
          return NextResponse.json(
            { success: false, error: 'معرف الإعلان مطلوب' },
            { status: 400 }
          );
        }

        const removeResult = FavoritesService.removeFromFavorites(sessionResult.data.id, adId);

        if (removeResult.success) {
          return NextResponse.json({
            success: true,
            message: 'تم إزالة الإعلان من المفضلة'
          });
        } else {
          return NextResponse.json(
            { success: false, error: removeResult.error },
            { status: 400 }
          );
        }

      case 'delete_search':
        if (!searchId) {
          return NextResponse.json(
            { success: false, error: 'معرف البحث مطلوب' },
            { status: 400 }
          );
        }

        const deleteResult = FavoritesService.deleteSavedSearch(sessionResult.data.id, searchId);

        if (deleteResult.success) {
          return NextResponse.json({
            success: true,
            message: 'تم حذف البحث المحفوظ'
          });
        } else {
          return NextResponse.json(
            { success: false, error: deleteResult.error },
            { status: 400 }
          );
        }

      case 'unfollow_seller':
        if (!sellerId) {
          return NextResponse.json(
            { success: false, error: 'معرف البائع مطلوب' },
            { status: 400 }
          );
        }

        const unfollowResult = FavoritesService.unfollowSeller(sessionResult.data.id, sellerId);

        if (unfollowResult.success) {
          return NextResponse.json({
            success: true,
            message: 'تم إلغاء متابعة البائع'
          });
        } else {
          return NextResponse.json(
            { success: false, error: unfollowResult.error },
            { status: 400 }
          );
        }

      default:
        return NextResponse.json(
          { success: false, error: 'نوع العملية غير صحيح' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('خطأ في حذف من المفضلة:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
