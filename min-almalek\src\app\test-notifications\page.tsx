'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useToast, useQuickToast } from '@/components/ToastManager';
import { useModal } from '@/components/NotificationModal';
import { useAdvancedNotifications } from '@/components/AdvancedNotificationSystem';

export default function TestNotificationsPage() {
  const [userName, setUserName] = useState('أحمد محمد');
  const [amount, setAmount] = useState('50000');
  const [service, setService] = useState('ترقية الحساب');
  const [adTitle, setAdTitle] = useState('شقة للبيع في دمشق');
  const [senderName, setSenderName] = useState('فاطمة أحمد');
  const [messagePreview, setMessagePreview] = useState('مرحباً، أنا مهتمة بالإعلان الخاص بك');
  const [itemTitle, setItemTitle] = useState('iPhone 14 Pro Max');

  const toast = useToast();
  const quickToast = useQuickToast();
  const modal = useModal();
  const { addNotification } = useAdvancedNotifications();

  const testToastNotifications = () => {
    // اختبار التوست المختلفة
    toast.showSuccess('تم الحفظ بنجاح!', 'تم حفظ بياناتك بنجاح في قاعدة البيانات');
    
    setTimeout(() => {
      toast.showError('فشل في الاتصال', 'تعذر الاتصال بالخادم. يرجى المحاولة مرة أخرى');
    }, 1000);

    setTimeout(() => {
      toast.showWarning('تحذير أمني', 'تم اكتشاف محاولة دخول مشبوهة لحسابك');
    }, 2000);

    setTimeout(() => {
      toast.showInfo('معلومة مهمة', 'سيتم إجراء صيانة على الموقع غداً من الساعة 2-4 صباحاً');
    }, 3000);
  };

  const testSpecialToasts = () => {
    toast.showWelcome(userName);
    
    setTimeout(() => {
      toast.showPaymentSuccess(amount, service);
    }, 1500);

    setTimeout(() => {
      toast.showAdPosted(adTitle);
    }, 3000);

    setTimeout(() => {
      toast.showMessage(senderName, messagePreview);
    }, 4500);

    setTimeout(() => {
      toast.showFavorite(itemTitle, 'product');
    }, 6000);

    setTimeout(() => {
      toast.showLogout();
    }, 7500);
  };

  const testQuickToasts = () => {
    quickToast.success('تمت العملية بنجاح!');
    
    setTimeout(() => {
      quickToast.error('حدث خطأ غير متوقع');
    }, 1000);

    setTimeout(() => {
      quickToast.warning('يرجى التحقق من البيانات');
    }, 2000);

    setTimeout(() => {
      quickToast.info('تم تحديث النظام');
    }, 3000);
  };

  const testModals = () => {
    modal.showWelcomeModal(userName, () => {
      console.log('تم إكمال الإعداد');
    });
  };

  const testConfirmationModal = () => {
    modal.showConfirmation(
      'حذف الإعلان',
      'هل أنت متأكد من أنك تريد حذف هذا الإعلان؟ لا يمكن التراجع عن هذا الإجراء.',
      () => {
        quickToast.success('تم حذف الإعلان بنجاح');
      },
      () => {
        quickToast.info('تم إلغاء العملية');
      }
    );
  };

  const testLogoutModal = () => {
    modal.showLogoutConfirmation(() => {
      quickToast.success('تم تسجيل الخروج بنجاح');
    });
  };

  const testAdvancedNotifications = () => {
    // إضافة إشعارات للنظام المتقدم
    addNotification({
      type: 'search_alert',
      title: 'نتائج بحث جديدة!',
      message: 'تم العثور على 5 عناصر جديدة تطابق بحثك عن "سيارات BMW"',
      priority: 'medium',
      category: 'system',
      actionUrl: '/search?q=BMW',
      actionText: 'عرض النتائج',
      icon: '🔍'
    });

    setTimeout(() => {
      addNotification({
        type: 'payment',
        title: 'فاتورة جديدة',
        message: 'تم إنشاء فاتورة جديدة بقيمة 25,000 ل.س لخدمة الإعلان المميز',
        priority: 'high',
        category: 'commerce',
        actionUrl: '/billing',
        actionText: 'عرض الفاتورة',
        autoHide: false,
        icon: '💳'
      });
    }, 1000);

    setTimeout(() => {
      addNotification({
        type: 'message',
        title: 'رسالة عاجلة',
        message: 'لديك رسالة عاجلة من إدارة الموقع بخصوص حسابك',
        priority: 'urgent',
        category: 'social',
        actionUrl: '/messages/admin',
        actionText: 'قراءة الرسالة',
        autoHide: false,
        icon: '🚨'
      });
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-800 mb-8 text-center">
            اختبار نظام الإشعارات والرسائل
          </h1>

          {/* إعدادات الاختبار */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">إعدادات الاختبار</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                <input
                  type="text"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">مبلغ الدفع</label>
                <input
                  type="text"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">اسم الخدمة</label>
                <input
                  type="text"
                  value={service}
                  onChange={(e) => setService(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">عنوان الإعلان</label>
                <input
                  type="text"
                  value={adTitle}
                  onChange={(e) => setAdTitle(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">اسم المرسل</label>
                <input
                  type="text"
                  value={senderName}
                  onChange={(e) => setSenderName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">معاينة الرسالة</label>
                <input
                  type="text"
                  value={messagePreview}
                  onChange={(e) => setMessagePreview(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* أزرار الاختبار */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* اختبار Toast الأساسي */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Toast الأساسي</h3>
              <div className="space-y-3">
                <button
                  onClick={testToastNotifications}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                >
                  اختبار Toast متنوع
                </button>
                <button
                  onClick={testQuickToasts}
                  className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors"
                >
                  اختبار Quick Toast
                </button>
              </div>
            </div>

            {/* اختبار Toast المتخصص */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Toast متخصص</h3>
              <div className="space-y-3">
                <button
                  onClick={testSpecialToasts}
                  className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors"
                >
                  اختبار Toast متخصص
                </button>
                <button
                  onClick={() => toast.showWelcome(userName)}
                  className="w-full bg-yellow-600 text-white py-2 px-4 rounded-lg hover:bg-yellow-700 transition-colors"
                >
                  ترحيب فقط
                </button>
              </div>
            </div>

            {/* اختبار Modal */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Modal</h3>
              <div className="space-y-3">
                <button
                  onClick={testModals}
                  className="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors"
                >
                  مودال الترحيب
                </button>
                <button
                  onClick={testConfirmationModal}
                  className="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors"
                >
                  مودال التأكيد
                </button>
                <button
                  onClick={testLogoutModal}
                  className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  مودال الخروج
                </button>
              </div>
            </div>

            {/* اختبار الإشعارات المتقدمة */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">إشعارات متقدمة</h3>
              <div className="space-y-3">
                <button
                  onClick={testAdvancedNotifications}
                  className="w-full bg-teal-600 text-white py-2 px-4 rounded-lg hover:bg-teal-700 transition-colors"
                >
                  إشعارات النظام
                </button>
                <button
                  onClick={() => toast.clearAllToasts()}
                  className="w-full bg-orange-600 text-white py-2 px-4 rounded-lg hover:bg-orange-700 transition-colors"
                >
                  مسح جميع Toast
                </button>
              </div>
            </div>
          </div>

          {/* معلومات النظام */}
          <div className="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">معلومات النظام</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-semibold text-gray-700 mb-2">أنواع Toast:</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• Success (أخضر شفاف)</li>
                  <li>• Error (أحمر شفاف)</li>
                  <li>• Warning (برتقالي شفاف)</li>
                  <li>• Info (أزرق شفاف)</li>
                  <li>• Welcome (أخضر مع تأثيرات خاصة)</li>
                  <li>• Payment (أخضر مع أيقونة دفع)</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-gray-700 mb-2">الميزات:</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>• تأثيرات glassmorphism</li>
                  <li>• انيميشن وتوهج</li>
                  <li>• إغلاق تلقائي قابل للتخصيص</li>
                  <li>• أولويات مختلفة</li>
                  <li>• أصوات إشعارات</li>
                  <li>• حفظ في localStorage</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
