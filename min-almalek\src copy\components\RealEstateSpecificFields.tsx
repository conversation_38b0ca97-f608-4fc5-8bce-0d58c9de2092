'use client';

import { useState } from 'react';

interface RealEstateData {
  listingType: 'sale' | 'rent';
  rentPeriod?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  propertyType: string;
  area?: number;
  rooms?: number;
  bathrooms?: number;
  floor?: string;
  condition?: string;
  features: string[];
}

interface RealEstateSpecificFieldsProps {
  onDataChange: (data: Partial<RealEstateData>) => void;
  initialData?: Partial<RealEstateData>;
}

export default function RealEstateSpecificFields({ onDataChange, initialData }: RealEstateSpecificFieldsProps) {
  const [realEstateData, setRealEstateData] = useState<Partial<RealEstateData>>({
    listingType: 'sale',
    features: [],
    ...initialData
  });

  const handleChange = (field: keyof RealEstateData, value: any) => {
    const updatedData = { ...realEstateData, [field]: value };
    setRealEstateData(updatedData);
    onDataChange(updatedData);
  };

  const handleFeatureToggle = (featureId: string) => {
    const currentFeatures = realEstateData.features || [];
    const updatedFeatures = currentFeatures.includes(featureId)
      ? currentFeatures.filter(f => f !== featureId)
      : [...currentFeatures, featureId];

    handleChange('features', updatedFeatures);
  };

  const listingTypes = [
    { id: 'sale', name: 'للبيع', icon: '💰', description: 'بيع العقار نهائياً' },
    { id: 'rent', name: 'للإيجار', icon: '🏠', description: 'إيجار العقار لفترة محددة' }
  ];

  const rentPeriods = [
    { id: 'daily', name: 'إيجار يومي', icon: '📅', description: 'إيجار لمدة يوم أو أكثر' },
    { id: 'weekly', name: 'إيجار أسبوعي', icon: '📆', description: 'إيجار لمدة أسبوع أو أكثر' },
    { id: 'monthly', name: 'إيجار شهري', icon: '🗓️', description: 'إيجار لمدة شهر أو أكثر' },
    { id: 'yearly', name: 'إيجار سنوي', icon: '📋', description: 'إيجار لمدة سنة أو أكثر' }
  ];

  const propertyTypes = [
    { id: 'apartment', name: 'شقة', icon: '🏢' },
    { id: 'villa', name: 'فيلا', icon: '🏘️' },
    { id: 'house', name: 'منزل', icon: '🏠' },
    { id: 'office', name: 'مكتب', icon: '🏢' },
    { id: 'shop', name: 'محل تجاري', icon: '🏪' },
    { id: 'warehouse', name: 'مستودع', icon: '🏭' },
    { id: 'land', name: 'أرض', icon: '🌍' },
    { id: 'farm', name: 'مزرعة', icon: '🚜' },
    { id: 'chalet', name: 'شاليه', icon: '🏖️' }
  ];

  const conditions = [
    { id: 'new', name: 'جديد', icon: '✨' },
    { id: 'excellent', name: 'ممتاز', icon: '⭐' },
    { id: 'very_good', name: 'جيد جداً', icon: '👍' },
    { id: 'good', name: 'جيد', icon: '👌' },
    { id: 'needs_renovation', name: 'يحتاج تجديد', icon: '🔨' }
  ];

  const floors = [
    { id: 'ground', name: 'أرضي' },
    { id: 'first', name: 'أول' },
    { id: 'second', name: 'ثاني' },
    { id: 'third', name: 'ثالث' },
    { id: 'fourth', name: 'رابع' },
    { id: 'fifth', name: 'خامس' },
    { id: 'sixth', name: 'سادس' },
    { id: 'seventh', name: 'سابع' },
    { id: 'eighth', name: 'ثامن' },
    { id: '9+', name: 'أكثر من 8 طوابق' },
    { id: 'roof', name: 'أخير (روف)' }
  ];

  // خيارات عدد الغرف مع الصالون
  const roomOptions = [
    { value: '1', label: '1 غرفة', description: 'غرفة واحدة' },
    { value: '1+1', label: '1+1 (غرفة + صالون)', description: 'غرفة واحدة مع صالون منفصل' },
    { value: '2', label: '2 غرفة', description: 'غرفتين' },
    { value: '2+1', label: '2+1 (غرفتين + صالون)', description: 'غرفتين مع صالون منفصل' },
    { value: '3', label: '3 غرف', description: 'ثلاث غرف' },
    { value: '3+1', label: '3+1 (3 غرف + صالون)', description: 'ثلاث غرف مع صالون منفصل' },
    { value: '4', label: '4 غرف', description: 'أربع غرف' },
    { value: '4+1', label: '4+1 (4 غرف + صالون)', description: 'أربع غرف مع صالون منفصل' },
    { value: '5', label: '5 غرف', description: 'خمس غرف' },
    { value: '5+1', label: '5+1 (5 غرف + صالون)', description: 'خمس غرف مع صالون منفصل' },
    { value: '6', label: '6 غرف', description: 'ست غرف' },
    { value: '6+1', label: '6+1 (6 غرف + صالون)', description: 'ست غرف مع صالون منفصل' },
    { value: '7', label: '7 غرف', description: 'سبع غرف' },
    { value: '7+1', label: '7+1 (7 غرف + صالون)', description: 'سبع غرف مع صالون منفصل' },
    { value: '8', label: '8 غرف', description: 'ثمان غرف' },
    { value: '8+1', label: '8+1 (8 غرف + صالون)', description: 'ثمان غرف مع صالون منفصل' },
    { value: '9+', label: 'أكثر من 8 غرف', description: 'للعقارات الكبيرة والفيلات الفاخرة' }
  ];

  // دالة للتحقق من إظهار حقول الغرف والطوابق
  const shouldShowRoomsAndFloors = () => {
    const propertyType = realEstateData.propertyType;
    return ['apartment', 'villa', 'house', 'office'].includes(propertyType || '');
  };

  // مميزات مشتركة لجميع العقارات
  const commonFeatures = [
    { id: 'green_title', name: 'طابو أخضر', icon: '📋' },
    { id: 'prime_location', name: 'موقع مميز', icon: '⭐' },
    { id: 'parking', name: 'مرآب سيارات', icon: '🚗' },
    { id: 'security', name: 'حراسة', icon: '🔒' },
    { id: 'air_conditioning', name: 'تكييف', icon: '❄️' },
    { id: 'heating', name: 'تدفئة', icon: '🔥' },
    { id: 'solar_energy', name: 'طاقة شمسية', icon: '☀️' },
    { id: 'sea_view', name: 'إطلالة بحر', icon: '🌊' },
    { id: 'mountain_view', name: 'إطلالة جبل', icon: '⛰️' },
    { id: 'city_view', name: 'إطلالة مدينة', icon: '🏙️' },
    { id: 'deluxe_finishing', name: 'كسوة ديلوكس', icon: '✨' },
    { id: 'unfinished', name: 'غير مكسي', icon: '🏗️' }
  ];

  // مميزات خاصة بالشقق والمنازل والفيلات
  const residentialFeatures = [
    { id: 'elevator', name: 'مصعد', icon: '🛗' },
    { id: 'balcony', name: 'بلكونة', icon: '🏠' },
    { id: 'garden', name: 'حديقة', icon: '🌳' },
    { id: 'furnished', name: 'مفروش', icon: '🛋️' },
    { id: 'swimming_pool', name: 'مسبح', icon: '🏊' },
    { id: 'gym', name: 'نادي رياضي', icon: '💪' },
    { id: 'new_finishing', name: 'إكساء جديد', icon: '✨' },
    { id: 'old_finishing', name: 'إكساء قديم', icon: '🔧' },
    { id: 'tiled', name: 'مكسي', icon: '🔲' },
    { id: 'marble', name: 'رخام', icon: '💎' },
    { id: 'ceramic', name: 'سيراميك', icon: '🔳' },
    { id: 'central_heating', name: 'تدفئة مركزية', icon: '🌡️' },
    { id: 'intercom', name: 'انتركوم', icon: '📞' },
    { id: 'storage_room', name: 'غرفة تخزين', icon: '📦' }
  ];

  // مميزات خاصة بالشاليهات
  const chaletFeatures = [
    { id: 'sea_access', name: 'وصول للبحر', icon: '🏖️' },
    { id: 'private_beach', name: 'شاطئ خاص', icon: '🏝️' },
    { id: 'bbq_area', name: 'منطقة شواء', icon: '🔥' },
    { id: 'outdoor_seating', name: 'جلسة خارجية', icon: '🪑' },
    { id: 'terrace', name: 'تراس', icon: '🌅' },
    { id: 'jacuzzi', name: 'جاكوزي', icon: '🛁' },
    { id: 'boat_dock', name: 'رصيف قوارب', icon: '⛵' },
    { id: 'beach_volleyball', name: 'ملعب كرة شاطئية', icon: '🏐' },
    { id: 'outdoor_shower', name: 'دش خارجي', icon: '🚿' },
    { id: 'fire_pit', name: 'موقد نار', icon: '🔥' },
    { id: 'gazebo', name: 'كشك', icon: '🏛️' },
    { id: 'outdoor_kitchen', name: 'مطبخ خارجي', icon: '🍳' }
  ];

  // مميزات خاصة بالمكاتب
  const officeFeatures = [
    { id: 'elevator', name: 'مصعد', icon: '🛗' },
    { id: 'reception_area', name: 'منطقة استقبال', icon: '🏢' },
    { id: 'meeting_rooms', name: 'غرف اجتماعات', icon: '👥' },
    { id: 'internet_fiber', name: 'إنترنت فايبر', icon: '🌐' },
    { id: 'phone_lines', name: 'خطوط هاتف', icon: '☎️' },
    { id: 'kitchen_area', name: 'منطقة مطبخ', icon: '🍽️' },
    { id: 'server_room', name: 'غرفة خوادم', icon: '💻' },
    { id: 'conference_room', name: 'قاعة مؤتمرات', icon: '🎤' },
    { id: 'business_district', name: 'منطقة أعمال', icon: '🏢' },
    { id: 'bank_nearby', name: 'قريب من بنوك', icon: '🏦' },
    { id: 'public_transport', name: 'قريب من المواصلات', icon: '🚌' }
  ];

  // مميزات خاصة بالمحلات التجارية
  const shopFeatures = [
    { id: 'commercial_market', name: 'ضمن سوق تجاري', icon: '🏪' },
    { id: 'shopping_mall', name: 'ضمن مول', icon: '🏬' },
    { id: 'street_front', name: 'واجهة شارع رئيسي', icon: '🛣️' },
    { id: 'corner_shop', name: 'محل زاوية', icon: '📐' },
    { id: 'high_traffic', name: 'حركة مرور عالية', icon: '🚶' },
    { id: 'glass_front', name: 'واجهة زجاجية', icon: '🪟' },
    { id: 'storage_back', name: 'مخزن خلفي', icon: '📦' },
    { id: 'tourist_area', name: 'منطقة سياحية', icon: '🗺️' },
    { id: 'residential_area', name: 'منطقة سكنية', icon: '🏘️' },
    { id: 'university_nearby', name: 'قريب من جامعة', icon: '🎓' },
    { id: 'hospital_nearby', name: 'قريب من مستشفى', icon: '🏥' },
    { id: 'restaurant_area', name: 'منطقة مطاعم', icon: '🍽️' },
    { id: 'delivery_access', name: 'مدخل توصيل', icon: '🚚' }
  ];

  // مميزات خاصة بالمزارع والشاليهات
  const farmFeatures = [
    { id: 'bbq_area', name: 'منطقة شواء', icon: '🔥' },
    { id: 'playground', name: 'ملعب أطفال', icon: '🎪' },
    { id: 'fruit_trees', name: 'أشجار مثمرة', icon: '🌳' },
    { id: 'water_well', name: 'بئر ماء', icon: '💧' },
    { id: 'farm_animals', name: 'حيوانات مزرعة', icon: '🐄' },
    { id: 'greenhouse', name: 'بيت زجاجي', icon: '🏡' },
    { id: 'irrigation_system', name: 'نظام ري', icon: '💦' },
    { id: 'guest_house', name: 'بيت ضيافة', icon: '🏠' },
    { id: 'horse_stable', name: 'إسطبل خيول', icon: '🐎' },
    { id: 'farm_equipment', name: 'معدات زراعية', icon: '🚜' },
    { id: 'fishing_pond', name: 'بركة أسماك', icon: '🐟' },
    { id: 'picnic_area', name: 'منطقة نزهة', icon: '🧺' },
    { id: 'camping_area', name: 'منطقة تخييم', icon: '⛺' }
  ];

  // مميزات خاصة بالمستودعات
  const warehouseFeatures = [
    { id: 'loading_dock', name: 'رصيف تحميل', icon: '🚛' },
    { id: 'high_ceiling', name: 'سقف عالي', icon: '📏' },
    { id: 'crane_system', name: 'نظام رافعة', icon: '🏗️' },
    { id: 'office_space', name: 'مساحة مكتبية', icon: '🏢' },
    { id: 'truck_access', name: 'مدخل شاحنات', icon: '🚚' },
    { id: 'industrial_area', name: 'منطقة صناعية', icon: '🏭' },
    { id: 'rail_access', name: 'وصول سكة حديد', icon: '🚂' },
    { id: 'cold_storage', name: 'تبريد', icon: '🧊' },
    { id: 'fire_system', name: 'نظام إطفاء', icon: '🚨' }
  ];

  // مميزات خاصة بالأراضي
  const landFeatures = [
    { id: 'flat_land', name: 'أرض مستوية', icon: '📏' },
    { id: 'sloped_land', name: 'أرض منحدرة', icon: '⛰️' },
    { id: 'corner_land', name: 'أرض زاوية', icon: '📐' },
    { id: 'main_road', name: 'على شارع رئيسي', icon: '🛣️' },
    { id: 'residential_zone', name: 'منطقة سكنية', icon: '🏘️' },
    { id: 'commercial_zone', name: 'منطقة تجارية', icon: '🏪' },
    { id: 'industrial_zone', name: 'منطقة صناعية', icon: '🏭' },
    { id: 'agricultural_zone', name: 'منطقة زراعية', icon: '🌾' },
    { id: 'water_access', name: 'وصول للمياه', icon: '💧' },
    { id: 'electricity_access', name: 'وصول للكهرباء', icon: '⚡' },
    { id: 'sewage_access', name: 'وصول للصرف الصحي', icon: '🚰' }
  ];

  // دالة لجلب المميزات حسب نوع العقار
  const getPropertyFeatures = () => {
    const propertyType = realEstateData.propertyType;
    let specificFeatures: any[] = [];

    switch (propertyType) {
      case 'apartment':
      case 'villa':
      case 'house':
        specificFeatures = residentialFeatures;
        break;
      case 'chalet':
        specificFeatures = chaletFeatures;
        break;
      case 'office':
        specificFeatures = officeFeatures;
        break;
      case 'shop':
        specificFeatures = shopFeatures;
        break;
      case 'farm':
        specificFeatures = farmFeatures;
        break;
      case 'warehouse':
        specificFeatures = warehouseFeatures;
        break;
      case 'land':
        specificFeatures = landFeatures;
        break;
      default:
        specificFeatures = residentialFeatures;
    }

    return [...commonFeatures, ...specificFeatures];
  };

  return (
    <div className="space-y-6">
      {/* نوع الإعلان */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">نوع الإعلان</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {listingTypes.map(type => (
            <button
              key={type.id}
              type="button"
              onClick={() => handleChange('listingType', type.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                realEstateData.listingType === type.id
                  ? 'border-primary-500 bg-primary-50 text-primary-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-3xl mb-2">{type.icon}</div>
              <div className="font-medium text-lg">{type.name}</div>
              <div className="text-xs text-gray-500 mt-1">{type.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* فترة الإيجار - تظهر فقط عند اختيار الإيجار */}
      {realEstateData.listingType === 'rent' && (
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4">فترة الإيجار</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {rentPeriods.map(period => (
              <button
                key={period.id}
                type="button"
                onClick={() => handleChange('rentPeriod', period.id)}
                className={`p-4 rounded-lg border-2 transition-all ${
                  realEstateData.rentPeriod === period.id
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="text-2xl mb-2">{period.icon}</div>
                <div className="font-medium text-sm">{period.name}</div>
                <div className="text-xs text-gray-500 mt-1">{period.description}</div>
              </button>
            ))}
          </div>

          {/* نصيحة للإيجار */}
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800 flex items-center gap-2">
              <span className="text-lg">💡</span>
              <span>
                <strong>نصيحة:</strong> حدد السعر حسب فترة الإيجار المختارة (يومي/أسبوعي/شهري/سنوي)
              </span>
            </p>
          </div>
        </div>
      )}

      {/* نوع العقار */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">نوع العقار</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {propertyTypes.map(type => (
            <button
              key={type.id}
              type="button"
              onClick={() => handleChange('propertyType', type.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                realEstateData.propertyType === type.id
                  ? 'border-purple-500 bg-purple-50 text-purple-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-2xl mb-2">{type.icon}</div>
              <div className="font-medium text-sm">{type.name}</div>
            </button>
          ))}
        </div>
      </div>

      {/* المعلومات الأساسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* المساحة */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            المساحة (م²)
          </label>
          <input
            type="number"
            value={realEstateData.area || ''}
            onChange={(e) => handleChange('area', parseInt(e.target.value) || 0)}
            placeholder="مثال: 120"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>

        {/* عدد الغرف - يظهر فقط للشقق والفيلات والمنازل والمكاتب */}
        {shouldShowRoomsAndFloors() && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              عدد الغرف
            </label>
            <select
              value={realEstateData.rooms || ''}
              onChange={(e) => handleChange('rooms', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">اختر عدد الغرف</option>
              {roomOptions.map(option => (
                <option key={option.value} value={option.value} title={option.description}>
                  {option.label}
                </option>
              ))}
            </select>
            {/* وصف توضيحي */}
            {realEstateData.rooms && (
              <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
                <span className="font-medium">💡 </span>
                {roomOptions.find(opt => opt.value === realEstateData.rooms)?.description}
              </div>
            )}
          </div>
        )}

        {/* عدد الحمامات */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            عدد الحمامات
          </label>
          <select
            value={realEstateData.bathrooms || ''}
            onChange={(e) => handleChange('bathrooms', parseInt(e.target.value))}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">اختر عدد الحمامات</option>
            {[1, 2, 3, 4, 5].map(num => (
              <option key={num} value={num}>{num} {num === 1 ? 'حمام' : 'حمامات'}</option>
            ))}
          </select>
        </div>

        {/* الطابق - يظهر فقط للشقق والفيلات والمنازل والمكاتب */}
        {shouldShowRoomsAndFloors() && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الطابق
            </label>
            <select
              value={realEstateData.floor || ''}
              onChange={(e) => handleChange('floor', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">اختر الطابق</option>
              {floors.map(floor => (
                <option key={floor.id} value={floor.id}>{floor.name}</option>
              ))}
            </select>
            {/* نصيحة للطوابق العالية */}
            {realEstateData.floor === '9+' && (
              <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-700">
                <span className="font-medium">⚠️ </span>
                للمباني العالية والأبراج السكنية أو التجارية
              </div>
            )}
          </div>
        )}

        {/* رسالة توضيحية عندما لا تظهر حقول الغرف والطوابق */}
        {!shouldShowRoomsAndFloors() && realEstateData.propertyType && (
          <div className="md:col-span-2">
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-gray-600">
                <span className="text-lg">ℹ️</span>
                <span className="font-medium">
                  حقول عدد الغرف والطوابق غير متاحة لهذا النوع من العقارات
                </span>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                هذه الحقول تظهر فقط للشقق والفيلات والمنازل والمكاتب
              </p>
            </div>
          </div>
        )}
      </div>

      {/* حالة العقار */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">حالة العقار</h3>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
          {conditions.map(condition => (
            <button
              key={condition.id}
              type="button"
              onClick={() => handleChange('condition', condition.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                realEstateData.condition === condition.id
                  ? 'border-orange-500 bg-orange-50 text-orange-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-2xl mb-2">{condition.icon}</div>
              <div className="font-medium text-sm">{condition.name}</div>
            </button>
          ))}
        </div>
      </div>

      {/* المميزات */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">المميزات الإضافية</h3>
        <div className="space-y-6">
          {/* المميزات المشتركة */}
          <div>
            <h4 className="text-md font-medium text-gray-700 mb-3">المميزات العامة</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {commonFeatures.map(feature => (
                <button
                  key={feature.id}
                  type="button"
                  onClick={() => handleFeatureToggle(feature.id)}
                  className={`p-3 rounded-lg border-2 transition-all text-sm ${
                    realEstateData.features?.includes(feature.id)
                      ? 'border-green-500 bg-green-50 text-green-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex flex-col items-center gap-2">
                    <span className="text-lg">{feature.icon}</span>
                    <span className="text-xs text-center">{feature.name}</span>
                    <span className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                      realEstateData.features?.includes(feature.id)
                        ? 'border-green-500 bg-green-500 text-white'
                        : 'border-gray-300'
                    }`}>
                      {realEstateData.features?.includes(feature.id) && '✓'}
                    </span>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* المميزات المخصصة حسب نوع العقار */}
          {realEstateData.propertyType ? (
            <div>
              <h4 className="text-md font-medium text-gray-700 mb-3">
                مميزات خاصة بـ{propertyTypes.find(p => p.id === realEstateData.propertyType)?.name || 'العقار'}
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {getPropertyFeatures().filter(feature => !commonFeatures.find(cf => cf.id === feature.id)).map(feature => (
                  <button
                    key={feature.id}
                    type="button"
                    onClick={() => handleFeatureToggle(feature.id)}
                    className={`p-3 rounded-lg border-2 transition-all text-sm ${
                      realEstateData.features?.includes(feature.id)
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex flex-col items-center gap-2">
                      <span className="text-lg">{feature.icon}</span>
                      <span className="text-xs text-center">{feature.name}</span>
                      <span className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                        realEstateData.features?.includes(feature.id)
                          ? 'border-blue-500 bg-blue-500 text-white'
                          : 'border-gray-300'
                      }`}>
                        {realEstateData.features?.includes(feature.id) && '✓'}
                      </span>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          ) : (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-blue-700">
                <span className="text-lg">💡</span>
                <span className="font-medium">
                  اختر نوع العقار أولاً لعرض المميزات المخصصة
                </span>
              </div>
              <p className="text-sm text-blue-600 mt-1">
                ستظهر مميزات إضافية خاصة بنوع العقار المختار (شقة، فيلا، مكتب، محل تجاري، إلخ)
              </p>
            </div>
          )}

          {/* عداد المميزات المختارة */}
          {realEstateData.features && realEstateData.features.length > 0 && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-green-700">
                <span className="text-lg">✅</span>
                <span className="font-medium">
                  تم اختيار {realEstateData.features.length} ميزة
                </span>
              </div>
              <div className="mt-2 flex flex-wrap gap-2">
                {realEstateData.features.map(featureId => {
                  // البحث في جميع المميزات (المشتركة والمخصصة)
                  let feature = commonFeatures.find(f => f.id === featureId);
                  if (!feature && realEstateData.propertyType) {
                    feature = getPropertyFeatures().find(f => f.id === featureId);
                  }
                  return feature ? (
                    <span key={featureId} className="inline-flex items-center gap-1 px-2 py-1 bg-green-100 text-green-700 rounded text-xs">
                      <span>{feature.icon}</span>
                      <span>{feature.name}</span>
                    </span>
                  ) : (
                    <span key={featureId} className="inline-flex items-center gap-1 px-2 py-1 bg-red-100 text-red-700 rounded text-xs">
                      <span>❌</span>
                      <span>{featureId}</span>
                    </span>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
