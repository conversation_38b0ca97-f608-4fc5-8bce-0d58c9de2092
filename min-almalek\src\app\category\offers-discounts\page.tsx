'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';

import { DataService } from '@/lib/data';
import { Ad } from '@/types';

export default function OffersDiscountsPage() {
  const [ads, setAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const offerCategories = [
    'مطاعم وكافيهات',
    'تسوق وأزياء',
    'خدمات تجميل',
    'ترفيه وسينما',
    'سفر وسياحة',
    'تعليم ودورات',
    'صحة ورياضة',
    'تكنولوجيا',
    'خدمات منزلية',
    'مناسبات وأفراح'
  ];

  const featuredOffers = [
    { title: 'خصم 50% على الوجبات', restaurant: 'مطعم الشام', discount: '50%', expires: '3 أيام', type: 'مطعم' },
    { title: 'تخفيض على الملابس الشتوية', store: 'متجر الأناقة', discount: '30%', expires: 'أسبوع', type: 'أزياء' },
    { title: 'عرض خاص على قص الشعر', salon: 'صالون الجمال', discount: '25%', expires: '5 أيام', type: 'تجميل' },
    { title: 'تذاكر السينما بنصف السعر', cinema: 'سينما دمشق', discount: '50%', expires: 'يومين', type: 'ترفيه' },
    { title: 'رحلات سياحية مخفضة', agency: 'وكالة السفر', discount: '40%', expires: 'أسبوع', type: 'سفر' },
    { title: 'دورات تعليمية مجانية', institute: 'معهد التطوير', discount: '100%', expires: '10 أيام', type: 'تعليم' }
  ];

  const trendingDeals = [
    { category: 'مطاعم', deals: 45, trend: '+25%', icon: '🍽️' },
    { category: 'أزياء', deals: 67, trend: '+18%', icon: '👗' },
    { category: 'تجميل', deals: 34, trend: '+32%', icon: '💄' },
    { category: 'ترفيه', deals: 23, trend: '+15%', icon: '🎬' },
    { category: 'سفر', deals: 29, trend: '+28%', icon: '✈️' },
    { category: 'تعليم', deals: 31, trend: '+22%', icon: '📚' }
  ];

  const popularBrands = [
    'مطعم الشام',
    'متجر الأناقة',
    'صالون الجمال',
    'سينما دمشق',
    'وكالة السفر',
    'معهد التطوير'
  ];

  useEffect(() => {
    loadAds();
  }, [sortBy, currentPage]);

  const loadAds = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    const result = DataService.getAds({
      category: 'offers-discounts',
      page: currentPage,
      limit: 12,
      sortBy: sortBy as any
    });
    setAds(result.ads);
    setTotalPages(result.totalPages);
    setLoading(false);
  };

  const handleSearch = (filters: any) => {
    console.log('تطبيق فلاتر البحث:', filters);
    loadAds();
  };

  const stats = {
    totalOffers: 2150,
    avgDiscount: '35%',
    newToday: 28,
    featuredOffers: 156
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="py-8">
        <div className="container mx-auto px-4">
          {/* العنوان والإحصائيات */}
          <div className="text-center mb-8">
            <h1 className="text-4xl font-bold text-gray-800 mb-4">العروض والتخفيضات</h1>
            <p className="text-lg text-gray-600 mb-6">اكتشف أفضل العروض والتخفيضات الحصرية</p>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
              <div className="bg-white rounded-xl shadow-md p-4">
                <div className="text-2xl font-bold text-primary-600">{stats.totalOffers.toLocaleString()}</div>
                <div className="text-sm text-gray-600">إجمالي العروض</div>
              </div>
              <div className="bg-white rounded-xl shadow-md p-4">
                <div className="text-2xl font-bold text-green-600">{stats.avgDiscount}</div>
                <div className="text-sm text-gray-600">متوسط التخفيض</div>
              </div>
              <div className="bg-white rounded-xl shadow-md p-4">
                <div className="text-2xl font-bold text-blue-600">{stats.newToday}</div>
                <div className="text-sm text-gray-600">عروض جديدة اليوم</div>
              </div>
              <div className="bg-white rounded-xl shadow-md p-4">
                <div className="text-2xl font-bold text-orange-600">{stats.featuredOffers}</div>
                <div className="text-sm text-gray-600">عروض مميزة</div>
              </div>
            </div>
          </div>

          {/* العروض المميزة */}
          <div className="mb-8">
            <h2 className="text-2xl font-semibold text-gray-800 mb-6">العروض المميزة</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredOffers.map((offer, index) => (
                <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                  <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white p-4">
                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold">{offer.discount}</span>
                      <span className="bg-white/20 px-2 py-1 rounded-full text-xs">خصم</span>
                    </div>
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-800 mb-2">{offer.title}</h3>
                    <p className="text-sm text-gray-600 mb-3">
                      {offer.restaurant || offer.store || offer.salon || offer.cinema || offer.agency || offer.institute}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs bg-gray-100 px-2 py-1 rounded-full">{offer.type}</span>
                      <span className="text-xs text-red-600">ينتهي خلال {offer.expires}</span>
                    </div>
                    <button className="w-full mt-3 bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700 transition-colors">
                      احصل على العرض
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* الصفقات الرائجة */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">الصفقات الرائجة</h2>
            <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
              {trendingDeals.map((deal, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md p-4 text-center hover:shadow-lg transition-shadow">
                  <div className="text-2xl mb-2">{deal.icon}</div>
                  <div className="font-semibold text-gray-800 text-sm mb-1">{deal.category}</div>
                  <div className="text-primary-600 font-bold">{deal.deals}</div>
                  <div className="text-xs text-green-600">{deal.trend}</div>
                </div>
              ))}
            </div>
          </div>

          {/* جميع فئات العروض */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">جميع فئات العروض</h2>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
              {offerCategories.map((category, index) => (
                <button
                  key={index}
                  className="bg-white border border-gray-200 rounded-lg p-3 text-sm hover:border-primary-500 hover:bg-primary-50 transition-colors text-center"
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-8">
            <div className="w-full">
              {/* أدوات التحكم */}
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                <div className="flex items-center gap-4">
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="oldest">الأقدم</option>
                    <option value="price-high">أعلى خصم</option>
                    <option value="price-low">أقل خصم</option>
                    <option value="popular">الأكثر شعبية</option>
                  </select>
                  
                  <div className="flex border border-gray-300 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 text-sm ${viewMode === 'grid' ? 'bg-primary-600 text-white' : 'bg-white text-gray-700'}`}
                    >
                      شبكة
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 text-sm ${viewMode === 'list' ? 'bg-primary-600 text-white' : 'bg-white text-gray-700'}`}
                    >
                      قائمة
                    </button>
                  </div>
                </div>
                
                <div className="text-sm text-gray-600">
                  {ads.length} عرض من أصل {stats.totalOffers.toLocaleString()}
                </div>
              </div>

              {/* قائمة العروض */}
              {loading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="bg-white rounded-xl shadow-lg p-6 animate-pulse">
                      <div className="h-48 bg-gray-200 rounded-lg mb-4"></div>
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
                  {ads.map((ad) => (
                    <AdCard key={ad.id} ad={ad} viewMode={viewMode} />
                  ))}
                </div>
              )}

              {/* التصفح */}
              {totalPages > 1 && (
                <div className="flex justify-center mt-8">
                  <div className="flex gap-2">
                    {[...Array(totalPages)].map((_, i) => (
                      <button
                        key={i}
                        onClick={() => setCurrentPage(i + 1)}
                        className={`px-4 py-2 rounded-lg ${
                          currentPage === i + 1
                            ? 'bg-primary-600 text-white'
                            : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {i + 1}
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
