'use client';

import { useState } from 'react';

interface ConfirmOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: 'warning' | 'danger' | 'info';
  icon?: string;
}

interface ConfirmState extends ConfirmOptions {
  isOpen: boolean;
  onConfirm: () => void;
}

export const useConfirmDialog = () => {
  const [confirmState, setConfirmState] = useState<ConfirmState>({
    isOpen: false,
    title: '',
    message: '',
    confirmText: 'تأكيد',
    cancelText: 'إلغاء',
    type: 'warning',
    onConfirm: () => {}
  });

  const showConfirm = (options: ConfirmOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setConfirmState({
        ...options,
        isOpen: true,
        confirmText: options.confirmText || 'تأكيد',
        cancelText: options.cancelText || 'إلغاء',
        type: options.type || 'warning',
        onConfirm: () => {
          setConfirmState(prev => ({ ...prev, isOpen: false }));
          resolve(true);
        }
      });
    });
  };

  const hideConfirm = () => {
    setConfirmState(prev => ({ ...prev, isOpen: false }));
  };

  // Pre-defined confirm dialogs for common scenarios
  const confirmDelete = (itemName: string = 'العنصر'): Promise<boolean> => {
    return showConfirm({
      title: 'تأكيد الحذف',
      message: `هل تريد حقاً حذف ${itemName}؟ لا يمكن التراجع عن هذا الإجراء.`,
      confirmText: 'نعم، احذف',
      cancelText: 'إلغاء',
      type: 'danger',
      icon: '🗑️'
    });
  };

  const confirmCancel = (actionName: string = 'العملية'): Promise<boolean> => {
    return showConfirm({
      title: `إلغاء ${actionName}`,
      message: `هل تريد حقاً إلغاء ${actionName}؟ سيتم فقدان جميع التغييرات غير المحفوظة.`,
      confirmText: 'نعم، إلغاء',
      cancelText: 'لا، متابعة',
      type: 'warning',
      icon: '⚠️'
    });
  };

  const confirmLogout = (): Promise<boolean> => {
    return showConfirm({
      title: 'تسجيل الخروج',
      message: 'هل تريد حقاً تسجيل الخروج من حسابك؟',
      confirmText: 'نعم، تسجيل الخروج',
      cancelText: 'إلغاء',
      type: 'info',
      icon: '🚪'
    });
  };

  const confirmSubmit = (formName: string = 'النموذج'): Promise<boolean> => {
    return showConfirm({
      title: `إرسال ${formName}`,
      message: `هل تريد إرسال ${formName}؟ تأكد من صحة جميع البيانات المدخلة.`,
      confirmText: 'نعم، إرسال',
      cancelText: 'مراجعة',
      type: 'info',
      icon: '📤'
    });
  };

  const confirmPayment = (amount: string, planName: string): Promise<boolean> => {
    return showConfirm({
      title: 'تأكيد الدفع',
      message: `هل تريد المتابعة لدفع ${amount} لـ ${planName}؟ سيتم توجيهك لصفحة الدفع الآمنة.`,
      confirmText: 'نعم، ادفع',
      cancelText: 'إلغاء',
      type: 'info',
      icon: '💳'
    });
  };

  return {
    confirmState,
    showConfirm,
    hideConfirm,
    confirmDelete,
    confirmCancel,
    confirmLogout,
    confirmSubmit,
    confirmPayment
  };
};
