'use client';

import { useEffect, useState } from 'react';

interface HydrationProviderProps {
  children: React.ReactNode;
}

const HydrationProvider = ({ children }: HydrationProviderProps) => {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // إزالة عناصر Grammarly قبل الـ hydration
    const removeGrammarlyElements = () => {
      // إزالة عناصر Grammarly
      const grammarlySelectors = [
        'grammarly-extension',
        'grammarly-popups',
        'grammarly-desktop-integration',
        '[data-grammarly-shadow-root]',
        '[data-grammarly-part]'
      ];

      grammarlySelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => el.remove());
      });

      // إزالة attributes من body
      const bodyAttributes = [
        'data-new-gr-c-s-check-loaded',
        'data-gr-ext-installed',
        'data-new-gr-c-s-loaded',
        'data-grammarly-shadow-root'
      ];

      bodyAttributes.forEach(attr => {
        document.body.removeAttribute(attr);
        document.documentElement.removeAttribute(attr);
      });
    };

    // تشغيل التنظيف فوراً
    removeGrammarlyElements();

    // تشغيل التنظيف بشكل دوري لمنع إعادة الإدراج
    const cleanupInterval = setInterval(removeGrammarlyElements, 100);

    // تعيين الـ hydration بعد تأخير قصير
    const timer = setTimeout(() => {
      setIsHydrated(true);
      clearInterval(cleanupInterval);
    }, 150);

    return () => {
      clearTimeout(timer);
      clearInterval(cleanupInterval);
    };
  }, []);

  // عرض loading skeleton أثناء انتظار hydration
  if (!isHydrated) {
    return (
      <div className="min-h-screen bg-gray-50" suppressHydrationWarning={true}>
        <div className="animate-pulse">
          {/* Header skeleton */}
          <div className="h-16 bg-white border-b border-gray-200 mb-4"></div>

          {/* Hero section skeleton */}
          <div className="container mx-auto px-4 py-8">
            <div className="h-32 bg-gray-200 rounded-lg mb-8"></div>

            {/* Categories skeleton */}
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
              ))}
            </div>

            {/* Content skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-48 bg-gray-200 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div suppressHydrationWarning={true}>
      {children}
    </div>
  );
};

export default HydrationProvider;
