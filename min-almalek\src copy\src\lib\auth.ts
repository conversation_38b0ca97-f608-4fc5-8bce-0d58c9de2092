// نظام المصادقة والجلسات
import { User } from './data';

export interface LoginCredentials {
  email?: string;
  phone?: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email?: string;
  phone: string;
  password: string;
  type: 'individual' | 'business';
  location: {
    governorate: string;
    city: string;
  };
}

export interface AuthSession {
  user: User;
  token: string;
  expiresAt: string;
}

// بيانات تجريبية للمستخدمين
const sampleUsers: User[] = [
  {
    id: 1,
    name: 'أحمد محمد',
    email: '<EMAIL>',
    phone: '+963944123456',
    type: 'individual',
    verified: true,
    subscription: {
      plan: 'premium',
      expiresAt: '2024-12-31T23:59:59Z',
      adsLimit: 15,
      adsUsed: 8
    },
    location: {
      governorate: 'damascus',
      city: 'damascus'
    },
    rating: 4.8,
    totalRatings: 23,
    createdAt: '2023-06-15T10:30:00Z',
    lastActive: '2024-01-20T14:22:00Z'
  },
  {
    id: 2,
    name: 'معرض الفرقان للسيارات',
    email: '<EMAIL>',
    phone: '+963933654321',
    type: 'business',
    verified: true,
    subscription: {
      plan: 'business',
      expiresAt: '2024-12-31T23:59:59Z',
      adsLimit: 100,
      adsUsed: 45
    },
    location: {
      governorate: 'aleppo',
      city: 'aleppo'
    },
    rating: 4.6,
    totalRatings: 156,
    createdAt: '2023-03-10T09:15:00Z',
    lastActive: '2024-01-20T16:45:00Z'
  },
  {
    id: 3,
    name: 'سارة أحمد',
    email: '<EMAIL>',
    phone: '+963955123789',
    type: 'individual',
    verified: false,
    subscription: {
      plan: 'free',
      expiresAt: '2024-12-31T23:59:59Z',
      adsLimit: 3,
      adsUsed: 2
    },
    location: {
      governorate: 'homs',
      city: 'homs'
    },
    rating: 4.3,
    totalRatings: 12,
    createdAt: '2024-01-05T11:20:00Z',
    lastActive: '2024-01-19T20:30:00Z'
  }
];

export class AuthService {
  // تسجيل الدخول
  static async login(credentials: LoginCredentials): Promise<{ success: boolean; data?: AuthSession; error?: string }> {
    try {
      // البحث عن المستخدم
      const user = sampleUsers.find(u =>
        (credentials.email && u.email === credentials.email) ||
        (credentials.phone && u.phone === credentials.phone)
      );

      if (!user) {
        return { success: false, error: 'المستخدم غير موجود' };
      }

      // في التطبيق الحقيقي، ستتحقق من كلمة المرور المشفرة
      if (credentials.password !== 'password123') {
        return { success: false, error: 'كلمة المرور غير صحيحة' };
      }

      // إنشاء جلسة
      const session: AuthSession = {
        user,
        token: this.generateToken(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // أسبوع
      };

      // تحديث آخر نشاط
      user.lastActive = new Date().toISOString();

      return { success: true, data: session };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في تسجيل الدخول' };
    }
  }

  // إنشاء حساب جديد
  static async register(data: RegisterData): Promise<{ success: boolean; data?: AuthSession; error?: string }> {
    try {
      // التحقق من وجود المستخدم
      const existingUser = sampleUsers.find(u =>
        u.email === data.email || u.phone === data.phone
      );

      if (existingUser) {
        return { success: false, error: 'المستخدم موجود مسبقاً' };
      }

      // إنشاء مستخدم جديد
      const newUser: User = {
        id: Date.now(),
        name: data.name,
        email: data.email,
        phone: data.phone,
        type: data.type,
        verified: false,
        subscription: {
          plan: 'free',
          expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          adsLimit: 3,
          adsUsed: 0
        },
        location: data.location,
        rating: 0,
        totalRatings: 0,
        createdAt: new Date().toISOString(),
        lastActive: new Date().toISOString()
      };

      // إضافة المستخدم للقائمة
      sampleUsers.push(newUser);

      // إنشاء جلسة
      const session: AuthSession = {
        user: newUser,
        token: this.generateToken(),
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      };

      return { success: true, data: session };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في إنشاء الحساب' };
    }
  }

  // التحقق من الجلسة
  static async verifySession(token: string): Promise<{ success: boolean; data?: User; error?: string }> {
    try {
      // في التطبيق الحقيقي، ستتحقق من التوكن في قاعدة البيانات
      if (!token || token.length < 10) {
        return { success: false, error: 'جلسة غير صالحة' };
      }

      // محاكاة التحقق من التوكن
      const userId = this.extractUserIdFromToken(token);
      const user = sampleUsers.find(u => u.id === userId);

      if (!user) {
        return { success: false, error: 'المستخدم غير موجود' };
      }

      return { success: true, data: user };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في التحقق من الجلسة' };
    }
  }

  // تسجيل الخروج
  static async logout(_token: string): Promise<{ success: boolean; error?: string }> {
    try {
      // في التطبيق الحقيقي، ستحذف التوكن من قاعدة البيانات
      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في تسجيل الخروج' };
    }
  }

  // تحديث الملف الشخصي
  static async updateProfile(userId: number, updateData: Partial<User>): Promise<{ success: boolean; data?: User; error?: string }> {
    try {
      const userIndex = sampleUsers.findIndex(u => u.id === userId);

      if (userIndex === -1) {
        return { success: false, error: 'المستخدم غير موجود' };
      }

      // تحديث البيانات
      sampleUsers[userIndex] = {
        ...sampleUsers[userIndex],
        ...updateData,
        id: userId // التأكد من عدم تغيير المعرف
      };

      return { success: true, data: sampleUsers[userIndex] };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في تحديث الملف الشخصي' };
    }
  }

  // تغيير كلمة المرور
  static async changePassword(userId: number, oldPassword: string, _newPassword: string): Promise<{ success: boolean; error?: string }> {
    try {
      const user = sampleUsers.find(u => u.id === userId);

      if (!user) {
        return { success: false, error: 'المستخدم غير موجود' };
      }

      // في التطبيق الحقيقي، ستتحقق من كلمة المرور القديمة المشفرة
      if (oldPassword !== 'password123') {
        return { success: false, error: 'كلمة المرور القديمة غير صحيحة' };
      }

      // في التطبيق الحقيقي، ستشفر كلمة المرور الجديدة
      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في تغيير كلمة المرور' };
    }
  }

  // إعادة تعيين كلمة المرور
  static async resetPassword(email: string): Promise<{ success: boolean; error?: string }> {
    try {
      const user = sampleUsers.find(u => u.email === email);

      if (!user) {
        return { success: false, error: 'البريد الإلكتروني غير موجود' };
      }

      // في التطبيق الحقيقي، سترسل رسالة إعادة تعيين
      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في إعادة تعيين كلمة المرور' };
    }
  }

  // الحصول على جميع المستخدمين (للإدارة)
  static getAllUsers(): User[] {
    return sampleUsers;
  }

  // الحصول على مستخدم بالمعرف
  static getUserById(id: number): User | undefined {
    return sampleUsers.find(u => u.id === id);
  }

  // توليد توكن
  private static generateToken(): string {
    return 'token_' + Math.random().toString(36).substring(2, 11) + '_' + Date.now();
  }

  // استخراج معرف المستخدم من التوكن
  private static extractUserIdFromToken(_token: string): number {
    // محاكاة استخراج المعرف من التوكن
    return 1; // في التطبيق الحقيقي، ستفك تشفير التوكن
  }

  // التحقق من صحة البيانات
  static validateRegisterData(data: RegisterData): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!data.name || data.name.length < 2) {
      errors.push('الاسم يجب أن يكون أكثر من حرفين');
    }

    if (!data.phone || !this.isValidSyrianPhone(data.phone)) {
      errors.push('رقم الهاتف السوري غير صحيح');
    }

    if (data.email && !this.isValidEmail(data.email)) {
      errors.push('البريد الإلكتروني غير صحيح');
    }

    if (!data.password || data.password.length < 6) {
      errors.push('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    }

    return { valid: errors.length === 0, errors };
  }

  // التحقق من صحة رقم الهاتف السوري
  private static isValidSyrianPhone(phone: string): boolean {
    const syrianPhoneRegex = /^\+963(9[0-9]{8}|1[0-9]{7})$/;
    return syrianPhoneRegex.test(phone);
  }

  // التحقق من صحة البريد الإلكتروني
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}
