'use client';

import Header from '@/components/Header';
import Footer from '@/components/Footer';
import Logo from '@/components/Logo';
import LogoTest from '@/components/LogoTest';

export default function TestLogoPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-800 mb-8">اختبار الشعار</h1>
          
          {/* اختبار مكون Logo الأساسي */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
            <h2 className="text-xl font-bold text-gray-800 mb-6">مكون Logo الأساسي</h2>
            
            <div className="space-y-6">
              {/* أحجام مختلفة */}
              <div>
                <h3 className="text-lg font-semibold mb-4">أحجام مختلفة:</h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <span className="w-16 text-sm">XS:</span>
                    <Logo variant="transparent" size="xs" showText={true} />
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="w-16 text-sm">SM:</span>
                    <Logo variant="transparent" size="sm" showText={true} />
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="w-16 text-sm">MD:</span>
                    <Logo variant="transparent" size="md" showText={true} />
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="w-16 text-sm">LG:</span>
                    <Logo variant="transparent" size="lg" showText={true} />
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="w-16 text-sm">XL:</span>
                    <Logo variant="transparent" size="xl" showText={true} />
                  </div>
                </div>
              </div>

              {/* أنواع مختلفة */}
              <div>
                <h3 className="text-lg font-semibold mb-4">أنواع مختلفة:</h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <span className="w-24 text-sm">شفاف:</span>
                    <Logo variant="transparent" size="md" showText={true} />
                  </div>
                  <div className="flex items-center gap-4 bg-gray-800 p-4 rounded">
                    <span className="w-24 text-sm text-white">أبيض:</span>
                    <Logo variant="white" size="md" showText={true} />
                  </div>
                </div>
              </div>

              {/* مع وبدون نص */}
              <div>
                <h3 className="text-lg font-semibold mb-4">مع وبدون نص:</h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <span className="w-24 text-sm">مع نص:</span>
                    <Logo variant="transparent" size="md" showText={true} />
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="w-24 text-sm">بدون نص:</span>
                    <Logo variant="transparent" size="md" showText={false} />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* اختبار تفصيلي */}
          <LogoTest />
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
