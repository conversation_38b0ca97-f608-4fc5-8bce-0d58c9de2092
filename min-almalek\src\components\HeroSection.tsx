'use client';

import { useState } from 'react';
import Link from 'next/link';
import SafeNavigationButton from './SafeNavigationButton';
import LiveSearch from './LiveSearch';
import CategoryIcon from './CategoryIcon';

const HeroSection = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLocation, setSelectedLocation] = useState('all');

  const quickCategories = [
    { id: 'real-estate', name: 'عقارات', icon: 'real-estate', color: '#3b82f6' },
    { id: 'cars', name: 'سيارات', icon: 'cars', color: '#ef4444' },
    { id: 'electronics', name: 'إلكترونيات', icon: 'electronics', color: '#8b5cf6' },
    { id: 'jobs', name: 'وظائف', icon: 'jobs', color: '#10b981' },
  ];

  const popularSearches = [
    'شقة للبيع دمشق',
    'سيارة مستعملة',
    'iPhone 15',
    'وظيفة مطور',
    'فيلا للإيجار',
    'لابتوب Dell'
  ];

  return (
    <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="container mx-auto px-4 py-16 relative">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            مرحباً بك في
            <span className="block text-yellow-300">من المالك</span>
          </h1>
          <p className="text-xl md:text-2xl text-primary-100 mb-8 max-w-3xl mx-auto">
            أكبر موقع للإعلانات المبوبة في سوريا - ابحث، اشتري، بع بكل سهولة وأمان
          </p>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-yellow-300 mb-2">50K+</div>
              <div className="text-primary-200 text-sm md:text-base">إعلان نشط</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-yellow-300 mb-2">25K+</div>
              <div className="text-primary-200 text-sm md:text-base">مستخدم</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-yellow-300 mb-2">14</div>
              <div className="text-primary-200 text-sm md:text-base">محافظة</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-yellow-300 mb-2">24/7</div>
              <div className="text-primary-200 text-sm md:text-base">دعم فني</div>
            </div>
          </div>
        </div>

        {/* Search Section */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-2xl shadow-2xl p-6 md:p-8">
            <h2 className="text-2xl font-bold text-gray-800 text-center mb-6">
              ابحث عن أي شيء تريده
            </h2>

            {/* Live Search */}
            <LiveSearch />

            {/* Quick Categories */}
            <div className="mt-8">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 text-center">
                تصنيفات سريعة
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {quickCategories.map((category) => (
                  <Link
                    key={category.id}
                    href={
                      category.id === 'jobs' ? '/jobs' :
                      `/category/${category.id}`
                    }
                    className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg hover:bg-primary-50 hover:text-primary-600 transition-colors group"
                  >
                    <div className="w-8 h-8 flex items-center justify-center group-hover:scale-110 transition-transform">
                      <CategoryIcon
                        category={category.icon}
                        className="w-6 h-6"
                        color={category.color}
                      />
                    </div>
                    <span className="font-medium text-gray-700 group-hover:text-primary-600">
                      {category.name}
                    </span>
                  </Link>
                ))}
              </div>
            </div>

            {/* Popular Searches */}
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-600 mb-3 text-center">
                عمليات البحث الشائعة:
              </h4>
              <div className="flex flex-wrap justify-center gap-2">
                {popularSearches.map((search, index) => (
                  <button
                    key={index}
                    className="text-sm bg-gray-100 text-gray-600 px-3 py-1 rounded-full hover:bg-primary-100 hover:text-primary-700 transition-colors"
                  >
                    {search}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* CTA Buttons */}
        <div className="flex flex-col md:flex-row gap-4 justify-center mt-12">
          <SafeNavigationButton
            href="/jobs"
            className="bg-orange-500 text-white px-8 py-4 rounded-lg hover:bg-orange-600 transition-colors font-semibold text-lg text-center"
          >
            تصفح الوظائف
          </SafeNavigationButton>
          <Link
            href="/jobs"
            className="bg-white text-primary-600 px-8 py-4 rounded-lg hover:bg-gray-100 transition-colors font-semibold text-lg text-center border-2 border-white"
          >
            تصفح الإعلانات
          </Link>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
