{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/app/loading.tsx"], "sourcesContent": ["export default function Loading() {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center px-4\">\n      <div className=\"text-center max-w-sm mx-auto\">\n        {/* شعار الموقع - محسن للموبايل */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-2xl md:text-3xl font-bold text-primary-600 mb-2\">من المالك</h1>\n          <p className=\"text-gray-600 text-sm md:text-base px-4\">موقع الإعلانات المبوب الأول في سوريا</p>\n        </div>\n\n        {/* مؤشر التحميل - أصغر للموبايل */}\n        <div className=\"relative mb-6\">\n          <div className=\"w-12 h-12 md:w-16 md:h-16 border-4 border-primary-200 border-t-primary-600 rounded-full animate-spin mx-auto\"></div>\n        </div>\n\n        {/* نص التحميل - محسن للموبايل */}\n        <div className=\"space-y-2\">\n          <p className=\"text-base md:text-lg font-medium text-gray-800\">جاري التحميل...</p>\n          <p className=\"text-xs md:text-sm text-gray-600\">يرجى الانتظار قليلاً</p>\n        </div>\n\n        {/* نقاط متحركة - أصغر للموبايل */}\n        <div className=\"flex justify-center gap-1 mt-6\">\n          <div className=\"w-1.5 h-1.5 md:w-2 md:h-2 bg-primary-600 rounded-full animate-bounce\"></div>\n          <div className=\"w-1.5 h-1.5 md:w-2 md:h-2 bg-primary-600 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n          <div className=\"w-1.5 h-1.5 md:w-2 md:h-2 bg-primary-600 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAuD;;;;;;sCACrE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAIzD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAiD;;;;;;sCAC9D,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;8BAIlD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;4BAAuE,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;sCACtH,8OAAC;4BAAI,WAAU;4BAAuE,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;;;;;;;;;;;;;;;;;;AAKhI"}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}