'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import NotificationDemo from '@/components/NotificationDemo';
import ClientOnlyWrapper from '@/components/ClientOnlyWrapper';
import { useNotifications, NotificationItem, Notification } from '@/components/NotificationSystem';

export default function NotificationsPage() {
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all');
  const [categoryFilter, setCategoryFilter] = useState<'all' | 'ad' | 'payment' | 'system' | 'user' | 'general'>('all');
  const { notifications, unreadCount, markAsRead, markAllAsRead, removeNotification, clearAll } = useNotifications();

  const filteredNotifications = notifications.filter(notification => {
    const statusMatch = filter === 'all' ||
                       (filter === 'unread' && !notification.read) ||
                       (filter === 'read' && notification.read);

    const categoryMatch = categoryFilter === 'all' || notification.category === categoryFilter;

    return statusMatch && categoryMatch;
  });

  const getCategoryName = (category: string) => {
    switch (category) {
      case 'ad': return 'الإعلانات';
      case 'payment': return 'المدفوعات';
      case 'system': return 'النظام';
      case 'user': return 'المستخدم';
      case 'general': return 'عام';
      default: return 'الكل';
    }
  };

  const getFilterCount = (filterType: 'all' | 'unread' | 'read') => {
    switch (filterType) {
      case 'all': return notifications.length;
      case 'unread': return unreadCount;
      case 'read': return notifications.length - unreadCount;
      default: return 0;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50" suppressHydrationWarning>
      <Header />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <ClientOnlyWrapper
            fallback={
              <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
                <div className="animate-pulse">
                  <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3 mb-6"></div>
                  <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                  </div>
                </div>
              </div>
            }
          >
          {/* رأس الصفحة */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 mb-2">الإشعارات</h1>
                <p className="text-gray-600">
                  {unreadCount > 0 ? `لديك ${unreadCount} إشعار غير مقروء` : 'جميع الإشعارات مقروءة'}
                </p>
              </div>
              <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center shadow-lg">
                <svg
                  className="w-8 h-8 text-white"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 19V20H3V19L5 17V11C5 7.9 7 5.2 10 4.3V4C10 2.9 10.9 2 12 2S14 2.9 14 4V4.3C17 5.2 19 7.9 19 11V17L21 19ZM12 22C10.9 22 10 21.1 10 20H14C14 21.1 13.1 22 12 22Z"/>
                </svg>
              </div>
            </div>

            {/* فلاتر الحالة */}
            <div className="flex flex-wrap gap-3 mb-4">
              <button
                onClick={() => setFilter('all')}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  filter === 'all'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                الكل ({getFilterCount('all')})
              </button>
              <button
                onClick={() => setFilter('unread')}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  filter === 'unread'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                غير مقروء ({getFilterCount('unread')})
              </button>
              <button
                onClick={() => setFilter('read')}
                className={`px-4 py-2 rounded-lg transition-colors ${
                  filter === 'read'
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                مقروء ({getFilterCount('read')})
              </button>
            </div>

            {/* فلاتر الفئة */}
            <div className="flex flex-wrap gap-2 mb-6">
              {['all', 'ad', 'payment', 'system', 'user', 'general'].map((category) => (
                <button
                  key={category}
                  onClick={() => setCategoryFilter(category as any)}
                  className={`px-3 py-1 text-sm rounded-full transition-colors ${
                    categoryFilter === category
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {getCategoryName(category)}
                </button>
              ))}
            </div>

            {/* أزرار التحكم */}
            {notifications.length > 0 && (
              <div className="flex gap-3">
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    تم قراءة الكل
                  </button>
                )}
                <button
                  onClick={clearAll}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  مسح جميع الإشعارات
                </button>
              </div>
            )}
          </div>

          {/* مكون التجربة */}
          <NotificationDemo />

          {/* قائمة الإشعارات */}
          <div className="space-y-4">
            {filteredNotifications.length > 0 ? (
              filteredNotifications.map((notification) => (
                <div key={notification.id} className="bg-white rounded-xl shadow-md p-4">
                  <NotificationItem
                    notification={notification}
                    onMarkAsRead={markAsRead}
                    onRemove={removeNotification}
                  />
                </div>
              ))
            ) : (
              <div className="bg-white rounded-xl shadow-lg p-12 text-center">
                <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-10 h-10 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1.5}
                      d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0L12 9 4 13"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">
                  لا توجد إشعارات
                </h3>
                <p className="text-gray-600">
                  {filter === 'unread' && 'لا توجد إشعارات غير مقروءة'}
                  {filter === 'read' && 'لا توجد إشعارات مقروءة'}
                  {filter === 'all' && 'لم تتلق أي إشعارات بعد'}
                </p>
              </div>
            )}
          </div>
          </ClientOnlyWrapper>
        </div>
      </main>

      <Footer />
    </div>
  );
}
