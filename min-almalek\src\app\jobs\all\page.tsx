'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ContactButtons from '@/components/ContactButtons';

interface Job {
  id: string;
  title: string;
  company: string;
  location: string;
  type: 'دوام كامل' | 'دوام جزئي' | 'عقد' | 'تدريب';
  category: string;
  salary: string;
  experience: string;
  description: string;
  requirements: string[];
  benefits: string[];
  postedDate: string;
  deadline: string;
  isUrgent: boolean;
  isFeatured: boolean;
}

const sampleJobs: Job[] = [
  {
    id: '1',
    title: 'مطور ويب متقدم',
    company: 'شركة التقنيات المتطورة',
    location: 'دمشق، سوريا',
    type: 'دوام كامل',
    category: 'تقنية المعلومات',
    salary: '800,000 - 1,200,000 ل.س',
    experience: '3-5 سنوات',
    description: 'نبحث عن مطور ويب متقدم للانضمام إلى فريقنا المتميز في تطوير تطبيقات الويب الحديثة. المرشح المثالي يجب أن يكون لديه خبرة قوية في تطوير الواجهات الأمامية والخلفية.',
    requirements: ['خبرة في React.js و Node.js', 'معرفة بقواعد البيانات MySQL/MongoDB', 'مهارات تواصل ممتازة', 'خبرة في Git و GitHub', 'معرفة بـ TypeScript'],
    benefits: ['راتب تنافسي', 'تأمين صحي شامل', 'بيئة عمل مرنة', 'فرص تطوير مهني', 'مكافآت أداء'],
    postedDate: '2024-01-15',
    deadline: '2024-02-15',
    isUrgent: true,
    isFeatured: true
  },
  {
    id: '2',
    title: 'مصمم جرافيك',
    company: 'وكالة الإبداع للتسويق',
    location: 'حلب، سوريا',
    type: 'دوام كامل',
    category: 'التصميم والإبداع',
    salary: '600,000 - 900,000 ل.س',
    experience: '2-4 سنوات',
    description: 'مطلوب مصمم جرافيك مبدع للعمل على مشاريع تسويقية متنوعة.',
    requirements: ['إتقان Adobe Creative Suite', 'خبرة في التصميم الرقمي', 'حس فني عالي'],
    benefits: ['بيئة إبداعية', 'فرص تطوير مهني', 'مكافآت أداء'],
    postedDate: '2024-01-10',
    deadline: '2024-02-10',
    isUrgent: false,
    isFeatured: true
  },
  {
    id: '3',
    title: 'محاسب مالي',
    company: 'مجموعة الشركات المالية',
    location: 'دمشق، سوريا',
    type: 'دوام كامل',
    category: 'المحاسبة والمالية',
    salary: '700,000 - 1,000,000 ل.س',
    experience: '3-6 سنوات',
    description: 'نبحث عن محاسب مالي خبير للعمل في قسم المحاسبة والتدقيق.',
    requirements: ['شهادة في المحاسبة', 'خبرة في البرامج المحاسبية', 'دقة في العمل'],
    benefits: ['راتب ثابت', 'تأمين اجتماعي', 'إجازات مدفوعة'],
    postedDate: '2024-01-12',
    deadline: '2024-02-12',
    isUrgent: true,
    isFeatured: false
  },
  {
    id: '4',
    title: 'مهندس برمجيات',
    company: 'شركة الحلول التقنية',
    location: 'دمشق، سوريا',
    type: 'دوام كامل',
    category: 'تقنية المعلومات',
    salary: '900,000 - 1,500,000 ل.س',
    experience: '4-7 سنوات',
    description: 'مطلوب مهندس برمجيات خبير للعمل على تطوير أنظمة معقدة.',
    requirements: ['خبرة في Java أو Python', 'معرفة بالأنظمة الموزعة', 'خبرة في قواعد البيانات'],
    benefits: ['راتب مرتفع', 'تدريب مستمر', 'فرص ترقية'],
    postedDate: '2024-01-08',
    deadline: '2024-02-08',
    isUrgent: false,
    isFeatured: true
  },
  {
    id: '5',
    title: 'مدير تسويق رقمي',
    company: 'وكالة التسويق الذكي',
    location: 'حلب، سوريا',
    type: 'دوام كامل',
    category: 'التسويق',
    salary: '750,000 - 1,100,000 ل.س',
    experience: '3-5 سنوات',
    description: 'نبحث عن مدير تسويق رقمي لقيادة استراتيجيات التسويق الإلكتروني.',
    requirements: ['خبرة في Google Ads و Facebook Ads', 'معرفة بـ SEO و SEM', 'مهارات تحليلية'],
    benefits: ['عمولات على النتائج', 'بيئة إبداعية', 'تدريب متخصص'],
    postedDate: '2024-01-05',
    deadline: '2024-02-05',
    isUrgent: true,
    isFeatured: false
  }
];

export default function AllJobsPage() {


  const [jobs] = useState<Job[]>(sampleJobs);
  const [filteredJobs, setFilteredJobs] = useState<Job[]>(sampleJobs);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('الكل');
  const [selectedType, setSelectedType] = useState('الكل');
  const [selectedLocation, setSelectedLocation] = useState('الكل');
  const [showUrgentOnly, setShowUrgentOnly] = useState(false);
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);
  const [salaryRange, setSalaryRange] = useState('الكل');
  const [experienceLevel, setExperienceLevel] = useState('الكل');
  const [sortBy, setSortBy] = useState('الأحدث');

  const categories = ['الكل', 'تقنية المعلومات', 'التصميم والإبداع', 'المحاسبة والمالية', 'التسويق', 'الموارد البشرية', 'الهندسة', 'الطب', 'التعليم', 'القانون', 'الإدارة', 'الصحة', 'الزراعة', 'الصناعة', 'الخدمات'];
  const jobTypes = ['الكل', 'دوام كامل', 'دوام جزئي', 'عقد', 'تدريب', 'عمل حر'];
  const locations = ['الكل', 'دمشق', 'ريف دمشق', 'حلب', 'حمص', 'اللاذقية', 'طرطوس', 'حماة', 'درعا', 'دير الزور', 'الرقة', 'إدلب', 'القنيطرة', 'السويداء'];
  const salaryRanges = ['الكل', 'أقل من 500,000', '500,000 - 1,000,000', '1,000,000 - 1,500,000', 'أكثر من 1,500,000'];
  const experienceLevels = ['الكل', 'مبتدئ (0-2 سنة)', 'متوسط (2-5 سنوات)', 'خبير (5+ سنوات)'];
  const sortOptions = ['الأحدث', 'الأقدم', 'الراتب (الأعلى)', 'الراتب (الأقل)'];
  const languages = ['الكل', 'العربية', 'الإنجليزية', 'الفرنسية', 'التركية'];
  const privacyOptions = ['الكل', 'خاص', 'عام'];
  const dateFilters = ['الكل', 'اليوم', 'آخر أسبوع'];

  const [selectedLanguage, setSelectedLanguage] = useState('الكل');
  const [selectedPrivacy, setSelectedPrivacy] = useState('الكل');
  const [selectedDate, setSelectedDate] = useState('الكل');

  // تفاصيل الوظيفة داخل الصفحة
  const [selectedJob, setSelectedJob] = useState<Job | null>(null);
  // حالة نموذج التقديم
  const [showApplyForm, setShowApplyForm] = useState(false);
  const [showLogoFlash, setShowLogoFlash] = useState(false);

  const handleSearch = () => {
    let filtered = jobs;
    if (searchTerm) {
      filtered = filtered.filter(job => 
        job.title.includes(searchTerm) || 
        job.company.includes(searchTerm) ||
        job.description.includes(searchTerm)
      );
    }
    if (selectedCategory !== 'الكل') {
      filtered = filtered.filter(job => job.category === selectedCategory);
    }
    if (selectedType !== 'الكل') {
      filtered = filtered.filter(job => job.type === selectedType);
    }
    if (selectedLocation !== 'الكل') {
      filtered = filtered.filter(job => job.location.includes(selectedLocation));
    }
    if (selectedLanguage !== 'الكل') {
      filtered = filtered.filter(job => job.requirements.some(r => r.includes(selectedLanguage)));
    }
    if (selectedPrivacy !== 'الكل') {
      filtered = filtered.filter(job => (selectedPrivacy === 'خاص' ? job.isFeatured : !job.isFeatured));
    }
    if (selectedDate !== 'الكل') {
      const now = new Date();
      filtered = filtered.filter(job => {
        const posted = new Date(job.postedDate);
        if (selectedDate === 'اليوم') {
          return posted.toDateString() === now.toDateString();
        } else if (selectedDate === 'آخر أسبوع') {
          const weekAgo = new Date(now);
          weekAgo.setDate(now.getDate() - 7);
          return posted >= weekAgo;
        }
        return true;
      });
    }
    if (showUrgentOnly) {
      filtered = filtered.filter(job => job.isUrgent);
    }
    if (showFeaturedOnly) {
      filtered = filtered.filter(job => job.isFeatured);
    }
    setFilteredJobs(filtered);
  };

  const handleApply = (jobId: string) => {
    const job = jobs.find(j => j.id === jobId);
    if (job) {
      const message = `🔍 طلب التقديم على وظيفة\n\nأريد التقديم على الوظيفة التالية:\n• المسمى الوظيفي: ${job.title}\n• الشركة: ${job.company}\n• الموقع: ${job.location}\n\nيرجى إرسال تفاصيل عملية التقديم.\n\nشكراً لكم 🙏`;
      window.open(`https://wa.me/963988652401?text=${encodeURIComponent(message)}`, '_blank');
    }
  };

  const handleShare = (jobId: string) => {
    const job = jobs.find(j => j.id === jobId);
    if (job) {
      const url = `${window.location.origin}/jobs/${jobId}`;
      if (navigator.share) {
        navigator.share({
          title: `وظيفة: ${job.title}`,
          text: `${job.title} في ${job.company}`,
          url: url,
        });
      } else {
        navigator.clipboard.writeText(url);
        alert('تم نسخ رابط الوظيفة إلى الحافظة');
      }
    }
  };

  // وظائف مشابهة
  const getSimilarJobs = (job: Job) => {
    return jobs.filter(j =>
      j.id !== job.id &&
      (j.category === job.category || j.location === job.location)
    ).slice(0, 3);
  };

  // Call this function on any main button click
  function handleMainAction() {
    setShowLogoFlash(true);
    setTimeout(() => setShowLogoFlash(false), 700);
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8 relative">
        {/* MyCv Logo with Glow */}
        <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-0 pointer-events-none">
          <img
            src="/images/MyCV Logo.jpg"
            alt="MyCv"
            className="w-40 h-40 opacity-10 transition-all duration-700 animate-pulse"
            style={{
              filter: 'drop-shadow(0 0 40px rgba(251, 146, 60, 0.7)) drop-shadow(0 0 80px rgba(251, 146, 60, 0.4))',
            }}
          />
        </div>

        <div className="flex flex-col lg:flex-row gap-8 relative z-10">
          {/* Filters Sidebar */}
          <div className="lg:w-1/4">
            <div className="bg-white/30 backdrop-blur-sm rounded-xl shadow-lg p-6 sticky top-8 border border-white/40">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                <img
                  src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="فلاتر"
                  className="w-6 h-6 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.6))'
                  }}
                />
                البحث والفلاتر
              </h2>
              
              {/* Search */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                <div className="relative">
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="ابحث عن وظيفة..."
                    className="w-full px-3 py-2 pr-10 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                  />
                  <img
                    src="/images/jobs/Jobs -Personals/icons/search_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                    alt="بحث"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 opacity-60"
                  />
                </div>
                <button
                  onClick={() => {
                    handleSearch();
                    handleMainAction();
                  }}
                  className="mt-2 w-full py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium flex items-center justify-center gap-2"
                  type="button"
                >
                  <img
                    src="/images/jobs/Jobs -Personals/icons/search_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
                    alt="تطبيق"
                    className="w-5 h-5 opacity-80"
                    style={{
                      filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.6))'
                    }}
                  />
                  تطبيق
                </button>
              </div>

              {/* Category Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">التخصص</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* Location Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">المحافظة</label>
                <select
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  {locations.map(location => (
                    <option key={location} value={location}>{location}</option>
                  ))}
                </select>
              </div>

              {/* Language Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">اللغة</label>
                <select
                  value={selectedLanguage}
                  onChange={(e) => setSelectedLanguage(e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  {languages.map(lang => (
                    <option key={lang} value={lang}>{lang}</option>
                  ))}
                </select>
              </div>

              {/* Job Type Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع الدوام</label>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  {jobTypes.map(type => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              {/* Privacy Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع الوظيفة</label>
                <select
                  value={selectedPrivacy}
                  onChange={(e) => setSelectedPrivacy(e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  {privacyOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              {/* Date Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ النشر</label>
                <select
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  {dateFilters.map(date => (
                    <option key={date} value={date}>{date}</option>
                  ))}
                </select>
              </div>

              {/* Salary Range Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نطاق الراتب</label>
                <select
                  value={salaryRange}
                  onChange={(e) => setSalaryRange(e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  {salaryRanges.map(range => (
                    <option key={range} value={range}>{range}</option>
                  ))}
                </select>
              </div>

              {/* Experience Level Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">مستوى الخبرة</label>
                <select
                  value={experienceLevel}
                  onChange={(e) => setExperienceLevel(e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  {experienceLevels.map(level => (
                    <option key={level} value={level}>{level}</option>
                  ))}
                </select>
              </div>

              {/* Sort By */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">ترتيب حسب</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  {sortOptions.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              {/* Special Filters */}
              <div className="mb-6">
                <label className="flex items-center mb-2">
                  <input
                    type="checkbox"
                    checked={showUrgentOnly}
                    onChange={(e) => setShowUrgentOnly(e.target.checked)}
                    className="ml-2 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="text-sm text-gray-700">وظائف عاجلة فقط</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={showFeaturedOnly}
                    onChange={(e) => setShowFeaturedOnly(e.target.checked)}
                    className="ml-2 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className="text-sm text-gray-700">وظائف مميزة فقط</span>
                </label>
              </div>

              {/* Search Button */}
              <button
                onClick={() => {
                  handleSearch();
                  handleMainAction();
                }}
                className="w-full bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium flex items-center justify-center gap-2"
              >
                <img
                  src="/images/jobs/Jobs -Personals/icons/search_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
                  alt="بحث"
                  className="w-5 h-5 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.6))'
                  }}
                />
                بحث
              </button>
            </div>
          </div>

          {/* Jobs List */}
          <div className="lg:w-3/4">
            <div className="mb-6 bg-white/30 backdrop-blur-sm rounded-xl p-6 border border-white/40">
              <div className="flex items-center gap-3 mb-2 justify-between">
                <div className="flex-1 flex justify-end">
                  <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-3">
                    <img
                      src="/images/jobs/Jobs -Personals/icons/demography_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                      alt="وظائف"
                      className="w-8 h-8 opacity-80"
                      style={{
                        filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.6))'
                      }}
                    />
                    جميع الوظائف
                  </h1>
                </div>
                <div className="flex-1 flex justify-start">
                  <button
                    onClick={() => window.location.href = '/jobs'}
                    className="flex items-center gap-2 px-5 py-2 rounded-lg font-bold border-none bg-orange-500/20 text-orange-700 hover:bg-orange-500/40 active:shadow-lg active:shadow-orange-400/60 active:scale-105 transition-all duration-200"
                    style={{ boxShadow: 'none' }}
                  >
                    <img
                      src="/images/jobs/Jobs -Personals/icons/New folder (2)/redo-alt (1).png"
                      alt="عودة"
                      className="w-5 h-5 opacity-80"
                      style={{ filter: 'drop-shadow(0 0 4px rgba(251, 146, 60, 0.7))' }}
                    />
                    عودة
                  </button>
                </div>
              </div>
              <p className="text-gray-600 flex items-center gap-2">
                <img
                  src="/images/jobs/Jobs -Personals/icons/check_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="نتائج"
                  className="w-4 h-4 opacity-60"
                />
                تم العثور على {filteredJobs.length} وظيفة
              </p>
            </div>

            {/* تفاصيل الوظيفة داخل الصفحة */}
            {selectedJob ? (
              <div className="bg-white/90 rounded-xl shadow-2xl p-8 border border-yellow-200 mb-8 animate__animated animate__fadeIn" style={{ position: 'relative', zIndex: 20 }}>
                <div className="header mb-6" style={{ background: 'linear-gradient(90deg, #fffffa 0%, #fffde7 60%, #fff9c4 100%)', border: '2px solid #fffde7', borderRadius: 8, boxShadow: '0 2px 12px rgba(245, 190, 66, 0.08)', padding: 20 }}>
                  <h1 className="text-2xl font-bold text-purple-700 mb-2">{selectedJob.title}</h1>
                  <h2 className="text-xl font-bold text-green-600 mb-2">{selectedJob.company}</h2>
                  <p className="text-gray-700">{selectedJob.location} • {selectedJob.type}</p>
                </div>
                <button
                  onClick={() => document.getElementById('company-info')?.classList.toggle('hidden')}
                  className="company-btn mb-4 bg-yellow-50 text-yellow-700 border border-yellow-400 px-4 py-2 rounded-lg font-bold shadow hover:bg-yellow-100 transition-all"
                >نبذة عن الشركة</button>
                <div id="company-info" className="company-info hidden bg-yellow-50 border border-yellow-400 rounded-lg p-4 mb-6">
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <div className="w-14 h-14 rounded-xl border-4 border-yellow-300 bg-white flex items-center justify-center shadow-lg" style={{boxShadow: '0 0 8px 1px #fbbf24, 0 0 0 2px #fff9c4'}}>
                        <img
                          src="/images/MyCV Logo.jpg"
                          alt={(selectedJob?.company || '') + ' logo'}
                          className="w-10 h-10 object-contain rounded-lg"
                          style={{filter: 'drop-shadow(0 0 6px #fbbf24)'}}
                        />
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-yellow-700 font-bold mb-2">معلومات الشركة</h3>
                      <ul className="list-disc pr-6 text-gray-700">
                        <li><strong>تاريخ الإنشاء:</strong> 2010</li>
                        <li><strong>الاختصاصات:</strong> تقنية المعلومات، تطوير ويب، حلول أعمال</li>
                        <li><strong>الموقع:</strong> دمشق، سوريا</li>
                        <li><strong>أعمال الشركة:</strong> تطوير تطبيقات ويب، استشارات تقنية، مشاريع برمجية للشركات</li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div className="mb-6">
                  <h3 className="text-blue-700 font-bold mb-2">وصف الوظيفة</h3>
                  <p className="text-gray-700">{selectedJob.description}</p>
                </div>
                <div className="mb-6">
                  <h3 className="text-blue-700 font-bold mb-2">المتطلبات</h3>
                  <ul className="list-disc pr-6 text-gray-700">
                    {selectedJob.requirements.map((req, idx) => <li key={idx}>{req}</li>)}
                  </ul>
                </div>
                <div className="mb-6">
                  <h3 className="text-blue-700 font-bold mb-2">المزايا</h3>
                  <ul className="list-disc pr-6 text-gray-700">
                    {selectedJob.benefits.map((benefit, idx) => <li key={idx}>{benefit}</li>)}
                  </ul>
                </div>
                <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-700">الراتب: </span>
                    <span className="text-green-600 font-semibold">{selectedJob.salary}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-700">الخبرة: </span>
                    <span className="text-green-600 font-semibold">{selectedJob.experience}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-gray-700">التخصص: </span>
                    <span className="text-green-600 font-semibold">{selectedJob.category}</span>
                  </div>
                </div>
                <div className="mb-6 flex flex-col text-sm text-gray-500">
                  <span>نُشر في: {new Date(selectedJob.postedDate).toLocaleDateString('en-US')}</span>
                  <span>آخر موعد للتقديم: {new Date(selectedJob.deadline).toLocaleDateString('en-US')}</span>
                </div>
                {/* أزرار التقديم والعودة */}
                <div className="flex flex-wrap gap-3 mb-8 justify-center">
                  <button
                    onClick={() => setSelectedJob(null)}
                    className="px-6 py-2 rounded-lg font-bold border border-yellow-400 bg-white/40 text-yellow-700 hover:bg-yellow-50 active:shadow-lg active:shadow-yellow-400/50 active:scale-105 transition-all focus:outline-none focus:ring-2 focus:ring-yellow-300 flex items-center gap-2"
                    style={{ boxShadow: 'none' }}
                  >
                    <img
                      src="/images/jobs/Jobs -Personals/icons/New folder (2)/redo-alt (1).png"
                      alt="عودة"
                      className="w-5 h-5 opacity-80"
                      style={{ filter: 'drop-shadow(0 0 4px rgba(251, 146, 60, 0.7))' }}
                    />
                    عودة
                  </button>
                  <button
                    onClick={() => {
                      setShowApplyForm(false);
                      handleApply(selectedJob.id);
                      handleMainAction();
                    }}
                    className="px-6 py-2 rounded-lg font-bold border border-green-400 bg-white/40 text-green-700 hover:bg-green-50 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all focus:outline-none focus:ring-2 focus:ring-green-300"
                    style={{ boxShadow: 'none' }}
                  >تقديم سريع</button>
                  <button
                    onClick={() => setShowApplyForm((prev) => !prev)}
                    className="px-6 py-2 rounded-lg font-bold border border-blue-400 bg-white/40 text-blue-700 hover:bg-blue-50 active:shadow-lg active:shadow-blue-400/50 active:scale-105 transition-all focus:outline-none focus:ring-2 focus:ring-blue-300"
                    style={{ boxShadow: 'none' }}
                  >تقديم</button>
                  <button
                    onClick={() => handleShare(selectedJob.id)}
                    className="px-6 py-2 rounded-lg font-bold border border-gray-400 bg-white/40 text-gray-700 hover:bg-gray-50 active:shadow-lg active:shadow-blue-400/50 active:scale-105 transition-all focus:outline-none focus:ring-2 focus:ring-blue-300 flex items-center gap-2"
                    style={{ boxShadow: 'none' }}
                  >
                    <img
                      src="/images/jobs/Jobs -Personals/icons/ios_share_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                      alt="مشاركة"
                      className="w-4 h-4 opacity-80"
                      style={{ filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.6))' }}
                    />
                    مشاركة
                  </button>
                </div>
                {/* نموذج التقديم */}
                {showApplyForm && (
                  <div className="mb-8 bg-blue-50 border border-blue-200 rounded-xl p-6 shadow">
                    <h3 className="text-lg font-bold text-blue-700 mb-4 text-center">نموذج التقديم</h3>
                    <form className="flex flex-col gap-4">
                      <div>
                        <label className="block mb-2 font-medium text-gray-700">السيرة الذاتية</label>
                        <div className="flex flex-col gap-2">
                          <input type="file" accept=".pdf,.doc,.docx" className="w-full border border-gray-300 rounded-lg p-2" />
                          <button
                            type="button"
                            className="bg-green-300 text-green-900 px-4 py-2 rounded-lg font-bold shadow hover:bg-green-400 active:shadow-lg active:shadow-green-400/70 active:scale-105 transition-all flex items-center gap-2 focus:outline-none focus:ring-2 focus:ring-green-300"
                            style={{ boxShadow: 'none' }}
                            onClick={() => alert('تم اختيار السيرة الذاتية المحفوظة!')}
                          >
                            <img src="/images/jobs/Jobs -Personals/icons/New folder (2)/MyCv/person-cv.png" alt="سيرتي الذاتية" className="w-4 h-4 opacity-80" style={{ filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.7))' }} />
                            استخدم سيرتي الذاتية المحفوظة
                          </button>
                        </div>
                      </div>
                      <div>
                        <label className="block mb-2 font-medium text-gray-700">الراتب المتوقع</label>
                        <input type="text" placeholder="مثال: 1,000,000 ل.س" className="w-full border border-gray-300 rounded-lg p-2" />
                      </div>
                      <div>
                        <label className="block mb-2 font-medium text-gray-700">هل يمكنك العمل مساءً أو أيام العطل؟</label>
                        <div className="flex gap-6">
                          <label className="flex items-center gap-2">
                            <input type="radio" name="workEvening" value="نعم" className="accent-blue-600" /> نعم
                          </label>
                          <label className="flex items-center gap-2">
                            <input type="radio" name="workEvening" value="لا" className="accent-blue-600" /> لا
                          </label>
                        </div>
                      </div>
                      <button type="submit" className="bg-blue-600 text-white px-6 py-2 rounded-lg font-bold shadow hover:bg-blue-700 transition-all mt-2">إرسال الطلب</button>
                    </form>
                  </div>
                )}
                {/* إعلانات مشابهة */}
                <div className="mt-8">
                  <h3 className="text-lg font-bold text-red-600 mb-4">إعلانات مشابهة</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {getSimilarJobs(selectedJob).map(job => (
                      <div key={job.id} className="bg-white/60 rounded-lg p-4 border border-gray-200 shadow">
                        <h4 className="font-bold text-purple-700 mb-1">{job.title}</h4>
                        <p className="text-primary-600 font-medium mb-1">{job.company}</p>
                        <p className="text-gray-600 text-sm mb-2">{job.location} • {job.type}</p>
                        <button
                          onClick={() => setSelectedJob(job)}
                          className="bg-blue-600 text-white px-3 py-1 rounded-lg font-medium mt-2"
                        >عرض التفاصيل</button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-6">
                {filteredJobs.map((job) => (
                  <div key={job.id} className="bg-white/30 backdrop-blur-sm rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 border border-white/40 hover:bg-white/50">
                    <div className="flex flex-row items-start justify-between mb-4">
                      {/* Company Logo Frame */}
                      <div className="flex-shrink-0">
                        <div className="w-16 h-16 rounded-xl border-4 border-yellow-300 bg-white flex items-center justify-center shadow-lg" style={{boxShadow: '0 0 12px 2px #fbbf24, 0 0 0 4px #fff9c4'}}>
                          <img
                            src="/images/MyCV Logo.jpg"
                            alt={job.company + ' logo'}
                            className="w-12 h-12 object-contain rounded-lg"
                            style={{filter: 'drop-shadow(0 0 8px #fbbf24)'}}
                          />
                        </div>
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-xl font-bold text-purple-700" style={{marginRight: '4rem'}}>{job.title}</h3>
                          {job.isUrgent && (
                            <span className="bg-red-100/80 backdrop-blur-sm text-red-600 px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1 border border-red-200">
                              <img
                                src="/images/jobs/Jobs -Personals/icons/Adsız tasarım (11).png"
                                alt="عاجل"
                                className="w-3 h-3 opacity-80"
                              />
                              عاجل
                            </span>
                          )}
                          {job.isFeatured && (
                            <span className="bg-yellow-100/80 backdrop-blur-sm text-yellow-600 px-2 py-1 rounded-full text-xs font-bold flex items-center gap-1 border border-yellow-200">
                              <img
                                src="/images/jobs/Jobs -Personals/icons/hotel_class_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
                                alt="مميز"
                                className="w-3 h-3 opacity-80"
                              />
                              مميز
                            </span>
                          )}
                        </div>
                        <p className="text-primary-600 font-medium mb-1" style={{marginRight: '4rem'}}>{job.company}</p>
                        <p className="text-gray-600 text-sm mb-2" style={{marginRight: '4rem'}}>{job.location} • {job.type}</p>
                        <p className="text-gray-700">{job.description}</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 text-sm">
                      <div className="flex items-center gap-2">
                        <img
                          src="/images/jobs/Jobs -Personals/icons/demography_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                          alt="راتب"
                          className="w-4 h-4 opacity-60"
                        />
                        <span className="font-medium text-gray-700">الراتب: </span>
                        <span className="text-green-600 font-semibold">{job.salary}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <img
                          src="/images/jobs/Jobs -Personals/icons/check_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                          alt="خبرة"
                          className="w-4 h-4 opacity-60"
                        />
                        <span className="font-medium text-gray-700">الخبرة: </span>
                        <span className="text-green-600 font-semibold">{job.experience}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <img
                          src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                          alt="تخصص"
                          className="w-4 h-4 opacity-60"
                        />
                        <span className="font-medium text-gray-700">التخصص: </span>
                        <span className="text-green-600 font-semibold">{job.category}</span>
                      </div>
                    </div>
                    {/* المتطلبات والمزايا تظهر فقط في نافذة عرض التفاصيل */}
                    <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                      <div className="flex flex-col text-sm text-gray-500">
                        <span>نُشر في: {new Date(job.postedDate).toLocaleDateString('en-US')}</span>
                        <span>آخر موعد للتقديم: {new Date(job.deadline).toLocaleDateString('en-US')}</span>
                      </div>
                      <div className="flex gap-3">
                        <button
                          onClick={() => setSelectedJob(job)}
                          className="bg-blue-600/80 backdrop-blur-sm text-white px-4 py-2 rounded-lg hover:bg-blue-700/90 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium flex items-center gap-2 border border-blue-500/40"
                        >
                          <img
                            src="/images/jobs/Jobs -Personals/icons/visibility_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
                            alt="عرض"
                            className="w-4 h-4 opacity-80"
                            style={{
                              filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.6))'
                            }}
                          />
                          عرض التفاصيل
                        </button>
                        <button
                          onClick={() => handleApply(job.id)}
                          className="bg-primary-600/80 backdrop-blur-sm text-white px-4 py-2 rounded-lg hover:bg-primary-700/90 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium flex items-center gap-2 border border-primary-500/40"
                        >
                          <img
                            src="/images/jobs/Jobs -Personals/icons/add_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
                            alt="تقديم"
                            className="w-4 h-4 opacity-80"
                            style={{
                              filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.6))'
                            }}
                          />
                          تقديم سريع
                        </button>
                        <button
                          onClick={() => handleShare(job.id)}
                          className="bg-white/30 backdrop-blur-sm text-gray-700 px-4 py-2 rounded-lg hover:bg-white/50 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium flex items-center gap-2 border border-white/40"
                        >
                          <img
                            src="/images/jobs/Jobs -Personals/icons/ios_share_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                            alt="مشاركة"
                            className="w-4 h-4 opacity-80"
                            style={{
                              filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.6))'
                            }}
                          />
                          مشاركة
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {filteredJobs.length === 0 && (
              <div className="text-center py-12 bg-white/30 backdrop-blur-sm rounded-xl border border-white/40">
                <div className="text-6xl mb-4">
                  <img
                    src="/images/jobs/Jobs -Personals/icons/manage_search_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                    alt="لا توجد نتائج"
                    className="w-16 h-16 mx-auto opacity-60"
                  />
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">لا توجد وظائف مطابقة</h3>
                <p className="text-gray-600">جرب تعديل معايير البحث للعثور على وظائف أخرى</p>
              </div>
            )}
          </div>
        </div>
      </main>

      <Footer />
      <ContactButtons variant="floating" />
    </div>
  );
}
