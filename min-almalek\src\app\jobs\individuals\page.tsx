'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import JobFilters, { JobFilters as JobFiltersType } from '@/components/JobFilters';
import JobCard, { FeaturedJobCard } from '@/components/JobCard';
import MyCvLogo from '@/components/MyCvLogo';
import { JobPosting, JobUtils, JOB_CATEGORIES } from '@/lib/jobs';

// بيانات تجريبية للوظائف
const sampleJobs: JobPosting[] = [
  {
    id: '1',
    companyId: 'comp1',
    companyName: 'شركة التقنيات المتقدمة',
    title: 'مطور React.js متقدم',
    department: 'التطوير',
    location: 'دمشق',
    workType: 'دوام كامل',
    workModel: 'مختلط',
    salaryRange: {
      min: 800000,
      max: 1200000,
      currency: 'ل.س',
      period: 'شهري'
    },
    description: 'نبحث عن مطور React.js متقدم للانضمام إلى فريقنا المتميز.',
    requirements: [
      'خبرة 3+ سنوات في React.js',
      'إتقان TypeScript',
      'خبرة في Redux أو Context API',
      'معرفة بـ Next.js',
      'خبرة في Git'
    ],
    responsibilities: [
      'تطوير واجهات المستخدم التفاعلية',
      'كتابة كود نظيف وقابل للصيانة',
      'التعاون مع فريق التصميم'
    ],
    benefits: [
      'تأمين صحي',
      'مكافآت سنوية',
      'تدريب وتطوير',
      'عمل مرن'
    ],
    skills: ['React.js', 'TypeScript', 'Redux', 'Next.js', 'Git'],
    experienceLevel: 'متقدم',
    experienceYears: { min: 3, max: 7 },
    educationLevel: 'بكالوريوس',
    applicationDeadline: '2024-03-15',
    postedDate: '2024-02-15',
    status: 'نشط',
    applicationsCount: 45,
    featured: true,
    urgent: false,
    category: 'technology'
  }
];

export default function IndividualJobsPage() {
  const [jobs, setJobs] = useState<JobPosting[]>(sampleJobs);
  const [filteredJobs, setFilteredJobs] = useState<JobPosting[]>(sampleJobs);
  const [filters, setFilters] = useState<JobFiltersType>({
    search: '',
    location: '',
    category: '',
    workType: '',
    experienceLevel: '',
    salaryRange: { min: 0, max: 5000000 },
    featured: false,
    urgent: false
  });

  // تطبيق الفلاتر
  useEffect(() => {
    const filtered = JobUtils.filterJobs(jobs, filters);
    setFilteredJobs(filtered);
  }, [jobs, filters]);

  const handleFiltersChange = (newFilters: JobFiltersType) => {
    setFilters(newFilters);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="py-8 relative">
        {/* MyCv Logo with Glow */}
        <div className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-0 pointer-events-none">
          <img
            src="/images/MyCV Logo.jpg"
            alt="MyCv"
            className="w-32 h-32 opacity-5 transition-all duration-1000 hover:opacity-10"
            style={{
              filter: 'drop-shadow(0 0 20px rgba(34, 197, 94, 0.3))'
            }}
          />
        </div>

        <div className="container mx-auto px-4 relative z-10">
          {/* العنوان الرئيسي */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              <img
                src="/images/mycv logo/الوظائف/transparent-Photoroom (3).png"
                alt="الوظائف"
                className="w-12 h-12"
                style={{
                  filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.6))'
                }}
              />
              <h1 className="text-4xl font-bold text-gray-800">الوظائف</h1>
            </div>
            <p className="text-xl text-gray-600">أنشئ سيرتك الذاتية الاحترافية - ابحث عن وظيفة أحلامك</p>
          </div>

          {/* أزرار الإجراءات السريعة */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            {/* إنشاء السيرة الذاتية */}
            <div className="bg-white/20 backdrop-blur-sm rounded-xl shadow-lg p-6 hover:shadow-2xl transition-all duration-300 border border-white/30 group hover:bg-white/30">
              <div className="text-center">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-blue-500/50 group-hover:bg-white/40">
                  <img
                    src="/images/mycv logo/أنشئ سيرتك الذاتية/applicant (1).v2.png"
                    alt="إنشاء"
                    className="w-8 h-8 opacity-80 group-hover:opacity-100 transition-opacity filter group-hover:drop-shadow-lg"
                    style={{
                      filter: 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.6))'
                    }}
                  />
                </div>
                <h3 className="text-lg font-bold text-gray-800 mb-2">أنشئ سيرتك الذاتية</h3>
                <p className="text-gray-600 mb-4 text-sm">إنشاء سيرة ذاتية احترافية</p>
                <Link
                  href="/resume/create"
                  className="inline-block w-full py-2 bg-green-400 text-white rounded-lg hover:bg-green-500 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium text-sm border border-green-300"
                >
                  ابدأ الآن
                </Link>
              </div>
            </div>

            {/* الوظائف المميزة */}
            <div className="bg-white/20 backdrop-blur-sm rounded-xl shadow-lg p-6 hover:shadow-2xl transition-all duration-300 border border-white/30 group hover:bg-white/30">
              <div className="text-center">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-yellow-500/50 group-hover:bg-white/40">
                  <img
                    src="/images/jobs/Jobs -Personals/icons/hotel_class_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
                    alt="مميز"
                    className="w-8 h-8 opacity-80 group-hover:opacity-100 transition-opacity filter group-hover:drop-shadow-lg"
                    style={{
                      filter: 'drop-shadow(0 0 8px rgba(234, 179, 8, 0.6))'
                    }}
                  />
                </div>
                <h3 className="text-lg font-bold text-gray-800 mb-2">الوظائف المميزة</h3>
                <p className="text-gray-600 mb-4 text-sm">أفضل الفرص المتاحة</p>
                <Link
                  href="/jobs/individuals?featured=true"
                  className="inline-block w-full py-2 bg-green-400 text-white rounded-lg hover:bg-green-500 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium text-sm border border-green-300"
                >
                  تصفح الآن
                </Link>
              </div>
            </div>

            {/* جميع الوظائف */}
            <div className="bg-white/20 backdrop-blur-sm rounded-xl shadow-lg p-6 hover:shadow-2xl transition-all duration-300 border border-white/30 group hover:bg-white/30">
              <div className="text-center">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-green-500/50 group-hover:bg-white/40">
                  <img
                    src="/images/jobs/Jobs -Personals/icons/search_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
                    alt="بحث"
                    className="w-8 h-8 opacity-80 group-hover:opacity-100 transition-opacity filter group-hover:drop-shadow-lg"
                    style={{
                      filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.6))'
                    }}
                  />
                </div>
                <h3 className="text-lg font-bold text-gray-800 mb-2">جميع الوظائف</h3>
                <p className="text-gray-600 mb-4 text-sm">تصفح كافة الوظائف</p>
                <Link
                  href="/jobs/all"
                  className="inline-block w-full py-2 bg-green-400 text-white rounded-lg hover:bg-green-500 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium text-sm border border-green-300"
                >
                  ابحث الآن
                </Link>
              </div>
            </div>

            {/* الوظائف العاجلة */}
            <div className="bg-white/20 backdrop-blur-sm rounded-xl shadow-lg p-6 hover:shadow-2xl transition-all duration-300 border border-white/30 group hover:bg-white/30">
              <div className="text-center">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg group-hover:shadow-red-500/50 group-hover:bg-white/40">
                  <div className="text-2xl">⚡</div>
                </div>
                <h3 className="text-lg font-bold text-gray-800 mb-2">وظائف عاجلة</h3>
                <p className="text-gray-600 mb-4 text-sm">فرص تحتاج تقديم سريع</p>
                <Link
                  href="/jobs/individuals?urgent=true"
                  className="inline-block w-full py-2 bg-green-400 text-white rounded-lg hover:bg-green-500 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium text-sm border border-green-300"
                >
                  عرض الآن
                </Link>
              </div>
            </div>
          </div>



          {/* إدارة السيرة الذاتية */}
          <div className="bg-white/20 backdrop-blur-sm rounded-2xl shadow-xl p-8 mb-12 border border-white/30">
            <div className="text-center mb-8">
              <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                <img
                  src="/images/jobs/Jobs -Personals/icons/settings_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="إدارة"
                  className="w-10 h-10 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 8px rgba(147, 51, 234, 0.6))'
                  }}
                />
              </div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">إدارة السيرة الذاتية</h2>
              <p className="text-gray-600">تحكم كامل في سيرتك الذاتية الاحترافية</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white/30 backdrop-blur-sm rounded-xl shadow-md p-8 text-center hover:shadow-lg transition-all duration-300 border border-white/40 group hover:bg-white/40 active:shadow-lg active:shadow-green-400/50 active:scale-105">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 active:scale-125 active:shadow-lg active:shadow-green-400/50 active:bg-green-100/30 transition-all duration-300">
                  <img
                    src="/images/jobs/Jobs -Personals/icons/edit_square_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
                    alt="تعديل"
                    className="w-6 h-6 opacity-80 group-hover:opacity-100 transition-opacity"
                    style={{
                      filter: 'drop-shadow(0 0 8px rgba(59, 130, 246, 0.6))'
                    }}
                  />
                </div>
                <h3 className="text-lg font-bold text-gray-800 mb-2">تعديل السيرة الذاتية</h3>
                <p className="text-gray-600 mb-4 text-sm">قم بتحديث وتعديل سيرتك الذاتية</p>
                <Link
                  href="/resume/my-resume"
                  className="inline-block w-full py-2 bg-green-400 text-white rounded-lg hover:bg-green-500 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium text-sm border border-green-300"
                >
                  تعديل الآن
                </Link>
              </div>

              <div className="bg-white/30 backdrop-blur-sm rounded-xl shadow-md p-8 text-center hover:shadow-lg transition-all duration-300 border border-white/40 group hover:bg-white/40 active:shadow-lg active:shadow-green-400/50 active:scale-105">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 active:scale-125 active:shadow-lg active:shadow-green-400/50 active:bg-green-100/30 transition-all duration-300">
                  <img
                    src="/images/jobs/Jobs -Personals/icons/print_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                    alt="طباعة"
                    className="w-6 h-6 opacity-80 group-hover:opacity-100 transition-opacity"
                    style={{
                      filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.6))'
                    }}
                  />
                </div>
                <h3 className="text-lg font-bold text-gray-800 mb-2">طباعة السيرة الذاتية</h3>
                <p className="text-gray-600 mb-4 text-sm">اطبع سيرتك الذاتية بتنسيق احترافي</p>
                <button
                  onClick={() => {
                    const savedResume = localStorage.getItem('userResume');
                    if (savedResume) {
                      const resume = JSON.parse(savedResume);
                      const printWindow = window.open('', '_blank');
                      if (printWindow) {
                        printWindow.document.write(`
                          <!DOCTYPE html>
                          <html dir="rtl">
                            <head>
                              <title>السيرة الذاتية - ${resume.personalInfo?.firstName} ${resume.personalInfo?.lastName}</title>
                              <meta charset="UTF-8">
                              <style>
                                body {
                                  font-family: 'Arial', sans-serif;
                                  margin: 20px;
                                  line-height: 1.6;
                                  color: #333;
                                  direction: rtl;
                                }
                                .header {
                                  text-align: center;
                                  border-bottom: 2px solid #333;
                                  padding-bottom: 20px;
                                  margin-bottom: 30px;
                                }
                                .name {
                                  font-size: 28px;
                                  font-weight: bold;
                                  margin-bottom: 10px;
                                }
                                .mycv-logo {
                                  position: fixed;
                                  bottom: 20px;
                                  left: 20px;
                                  opacity: 0.6;
                                  z-index: 1000;
                                }
                                @media print {
                                  .mycv-logo { display: block !important; }
                                  body { margin: 15px; }
                                }
                              </style>
                            </head>
                            <body>
                              <div class="header">
                                <div class="name">${resume.personalInfo?.firstName} ${resume.personalInfo?.lastName}</div>
                                <div class="title">${resume.personalInfo?.title || ''}</div>
                                <div class="contact">
                                  ${resume.contactInfo?.phone || ''} | ${resume.contactInfo?.email || ''} | ${resume.contactInfo?.city || ''}
                                </div>
                              </div>

                              ${resume.personalInfo?.summary ? `
                                <div class="section">
                                  <div class="section-title">نبذة شخصية</div>
                                  <p>${resume.personalInfo.summary}</p>
                                </div>
                              ` : ''}

                              <div class="mycv-logo">
                                <img src="/images/MyCV Logo.jpg" alt="MyCv" style="width: 50px; height: auto;" />
                              </div>
                            </body>
                          </html>
                        `);
                        printWindow.document.close();
                        printWindow.print();
                      }
                    } else {
                      alert('لا توجد سيرة ذاتية محفوظة للطباعة');
                    }
                  }}
                  className="inline-block w-full py-2 bg-green-400 text-white rounded-lg hover:bg-green-500 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium text-sm border border-green-300"
                >
                  طباعة الآن
                </button>
              </div>

              <div className="bg-white/30 backdrop-blur-sm rounded-xl shadow-md p-8 text-center hover:shadow-lg transition-all duration-300 border border-white/40 group hover:bg-white/40 active:shadow-lg active:shadow-green-400/50 active:scale-105">
                <div className="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 active:scale-125 active:shadow-lg active:shadow-green-400/50 active:bg-green-100/30 transition-all duration-300">
                  <img
                    src="/images/jobs/Jobs -Personals/icons/demography_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                    alt="قوالب"
                    className="w-6 h-6 opacity-80 group-hover:opacity-100 transition-opacity"
                    style={{
                      filter: 'drop-shadow(0 0 8px rgba(147, 51, 234, 0.6))'
                    }}
                  />
                </div>
                <h3 className="text-lg font-bold text-gray-800 mb-2">قوالب السيرة الذاتية</h3>
                <p className="text-gray-600 mb-4 text-sm">اختر من مجموعة متنوعة من القوالب</p>
                <Link
                  href="/resume/templates"
                  className="inline-block w-full py-2 bg-green-400 text-white rounded-lg hover:bg-green-500 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium text-sm border border-green-300"
                >
                  اختر قالب
                </Link>
              </div>
            </div>
          </div>

          {/* شعار MyCv */}
          <div className="mt-16 text-center">
            <div className="bg-white/20 backdrop-blur-sm rounded-xl p-6 border border-white/30 inline-block">
              <div className="inline-flex items-center gap-3 text-gray-700">
                <span className="font-medium">مدعوم من قبل</span>
                <div className="bg-white/30 backdrop-blur-sm rounded-lg p-2">
                  <MyCvLogo size="sm" />
                </div>
                <span className="font-medium">- منصة متكاملة للسير الذاتية والتوظيف</span>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
