// مكون الأيقونات الاحترافية للتواصل

interface IconProps {
  className?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

const sizeClasses = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4', 
  md: 'w-5 h-5',
  lg: 'w-6 h-6',
  xl: 'w-8 h-8'
};

// أيقونة الواتساب الاحترافية
export const WhatsAppIcon = ({ className, size = 'md' }: IconProps) => {
  const sizeClass = className || sizeClasses[size];
  
  return (
    <svg className={sizeClass} viewBox="0 0 24 24" fill="currentColor">
      <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
    </svg>
  );
};

// أيقونة الهاتف الاحترافية
export const PhoneIcon = ({ className, size = 'md' }: IconProps) => {
  const sizeClass = className || sizeClasses[size];
  
  return (
    <svg className={sizeClass} viewBox="0 0 24 24" fill="currentColor">
      <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
    </svg>
  );
};

// أيقونة الرسائل النصية
export const SmsIcon = ({ className, size = 'md' }: IconProps) => {
  const sizeClass = className || sizeClasses[size];
  
  return (
    <svg className={sizeClass} viewBox="0 0 24 24" fill="currentColor">
      <path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
    </svg>
  );
};

// أيقونة البريد الإلكتروني
export const EmailIcon = ({ className, size = 'md' }: IconProps) => {
  const sizeClass = className || sizeClasses[size];
  
  return (
    <svg className={sizeClass} viewBox="0 0 24 24" fill="currentColor">
      <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
    </svg>
  );
};

// أيقونة الموقع
export const LocationIcon = ({ className, size = 'md' }: IconProps) => {
  const sizeClass = className || sizeClasses[size];
  
  return (
    <svg className={sizeClass} viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
    </svg>
  );
};

// أيقونة الوقت
export const TimeIcon = ({ className, size = 'md' }: IconProps) => {
  const sizeClass = className || sizeClasses[size];
  
  return (
    <svg className={sizeClass} viewBox="0 0 24 24" fill="currentColor">
      <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
      <path d="m12.5 7-1 0 0 6 5.25 3.15.75-1.23-4.5-2.67z"/>
    </svg>
  );
};

// مكون زر التواصل الاحترافي
interface ContactButtonProps {
  type: 'whatsapp' | 'phone' | 'sms' | 'email';
  onClick: () => void;
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const ContactButton = ({ 
  type, 
  onClick, 
  children, 
  variant = 'primary',
  size = 'md',
  className = ''
}: ContactButtonProps) => {
  const baseClasses = 'inline-flex items-center justify-center gap-2 font-medium rounded-xl transition-all duration-300 hover:scale-105 transform relative overflow-hidden group';
  
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-6 py-4 text-lg'
  };

  const typeClasses = {
    whatsapp: {
      primary: 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white border border-green-400 shadow-lg hover:shadow-xl',
      secondary: 'bg-green-50 text-green-700 border border-green-200 hover:bg-green-100',
      outline: 'border-2 border-green-500 text-green-500 hover:bg-green-500 hover:text-white'
    },
    phone: {
      primary: 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white border border-blue-400 shadow-lg hover:shadow-xl',
      secondary: 'bg-blue-50 text-blue-700 border border-blue-200 hover:bg-blue-100',
      outline: 'border-2 border-blue-500 text-blue-500 hover:bg-blue-500 hover:text-white'
    },
    sms: {
      primary: 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white border border-purple-400 shadow-lg hover:shadow-xl',
      secondary: 'bg-purple-50 text-purple-700 border border-purple-200 hover:bg-purple-100',
      outline: 'border-2 border-purple-500 text-purple-500 hover:bg-purple-500 hover:text-white'
    },
    email: {
      primary: 'bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white border border-gray-400 shadow-lg hover:shadow-xl',
      secondary: 'bg-gray-50 text-gray-700 border border-gray-200 hover:bg-gray-100',
      outline: 'border-2 border-gray-500 text-gray-500 hover:bg-gray-500 hover:text-white'
    }
  };

  const IconComponent = {
    whatsapp: WhatsAppIcon,
    phone: PhoneIcon,
    sms: SmsIcon,
    email: EmailIcon
  }[type];

  return (
    <button
      onClick={onClick}
      className={`${baseClasses} ${sizeClasses[size]} ${typeClasses[type][variant]} ${className}`}
    >
      {variant === 'primary' && (
        <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
      )}
      <IconComponent size="sm" className="relative z-10" />
      <span className="relative z-10">{children}</span>
    </button>
  );
};

export default {
  WhatsAppIcon,
  PhoneIcon,
  SmsIcon,
  EmailIcon,
  LocationIcon,
  TimeIcon,
  ContactButton
};
