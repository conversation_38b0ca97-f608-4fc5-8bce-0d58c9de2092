import Link from 'next/link';
import Logo from './Logo';
import MyCvLogo from './MyCvLogo';
import ContactButtons, { ContactInfo } from '@/components/ContactButtons';
import { COMPANY_CONTACT } from '@/lib/contact';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <div className="mb-6">
              <Logo variant="transparent" size="lg" showText={true} textColor="yellow" href="/" />
              <p className="text-gray-400 text-sm mt-2">موقع الإعلانات المبوبة</p>
            </div>
            <p className="text-gray-400 mb-4">
              موقع من المالك هو أكبر موقع للإعلانات المبوبة في سوريا، يوفر منصة آمنة وسهلة للبيع والشراء.
            </p>



            {/* مواقع التواصل الاجتماعي */}
            <div className="mt-6">
              <h5 className="font-medium mb-3 text-sm text-gray-300">تابعنا على</h5>
              <div className="flex gap-2">
                <a href="#" className="w-8 h-8 bg-gray-800 hover:bg-blue-600 rounded-md flex items-center justify-center transition-all duration-300 group" title="فيسبوك">
                  <svg viewBox="0 0 24 24" className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" fill="currentColor">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>
                <a href="#" className="w-8 h-8 bg-gray-800 hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 rounded-md flex items-center justify-center transition-all duration-300 group" title="انستغرام">
                  <svg viewBox="0 0 24 24" className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" fill="currentColor">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                  </svg>
                </a>
                <a href="#" className="w-8 h-8 bg-gray-800 hover:bg-red-600 rounded-md flex items-center justify-center transition-all duration-300 group" title="يوتيوب">
                  <svg viewBox="0 0 24 24" className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" fill="currentColor">
                    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6">روابط سريعة</h4>
            <ul className="space-y-3">
              <li><Link href="/about" className="text-gray-400 hover:text-white transition-colors">من نحن</Link></li>
              <li><Link href="/pricing" className="text-gray-400 hover:text-white transition-colors">أسعار الإعلانات</Link></li>
              <li><Link href="/safety" className="text-gray-400 hover:text-white transition-colors">نصائح الأمان</Link></li>
              <li><Link href="/faq" className="text-gray-400 hover:text-white transition-colors">الأسئلة الشائعة</Link></li>
              <li><Link href="/dashboard" className="text-gray-400 hover:text-white transition-colors">لوحة التحكم</Link></li>
              <li><Link href="/contact" className="text-gray-400 hover:text-white transition-colors">اتصل بنا</Link></li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h4 className="text-lg font-semibold mb-6">التصنيفات الرئيسية</h4>
            <ul className="space-y-3">
              <li><Link href="/category/real-estate" className="text-gray-400 hover:text-white transition-colors">العقارات</Link></li>
              <li><Link href="/category/cars" className="text-gray-400 hover:text-white transition-colors">السيارات</Link></li>
              <li><Link href="/category/electronics" className="text-gray-400 hover:text-white transition-colors">الإلكترونيات</Link></li>
              <li><Link href="/category/jobs" className="text-gray-400 hover:text-white transition-colors">الوظائف</Link></li>
              <li><Link href="/category/services" className="text-gray-400 hover:text-white transition-colors">الخدمات</Link></li>
              <li><Link href="/category/fashion" className="text-gray-400 hover:text-white transition-colors">الأزياء</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-6">معلومات التواصل</h4>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <span className="text-primary-400 mt-1">📍</span>
                <div>
                  <p className="text-gray-400">{COMPANY_CONTACT.company.address}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-primary-400">📞</span>
                <div>
                  <p className="text-white font-semibold">{COMPANY_CONTACT.phone.displayNumber}</p>
                  <p className="text-gray-400 text-sm">{COMPANY_CONTACT.info.workingHours}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-primary-400">✉️</span>
                <p className="text-gray-400">{COMPANY_CONTACT.company.email}</p>
              </div>
              <div className="flex items-center gap-3">
                <span className="text-primary-400">🕒</span>
                <div>
                  <p className="text-gray-400">{COMPANY_CONTACT.info.workingDays}</p>
                  <p className="text-gray-400 text-sm">وقت الرد: {COMPANY_CONTACT.info.responseTime}</p>
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>

      {/* Apps Download Section */}
      <div className="border-t border-gray-800 bg-gray-800/50">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center mb-6">
            <h5 className="font-semibold text-white mb-2">حمل التطبيقات - مجاناً</h5>
          </div>
          <div className="flex flex-col lg:flex-row items-center justify-center gap-8">
            {/* Apps Section */}
            <div className="flex flex-col md:flex-row items-center gap-6">
              {/* تطبيق MyCv */}
              <div className="flex items-center gap-4 bg-gray-900 rounded-lg p-4 min-w-[280px]">
                <MyCvLogo size="sm" variant="square" />
                <div className="flex-1">
                  <h6 className="font-medium text-white text-sm">تطبيق MyCv</h6>
                  <p className="text-gray-400 text-xs mb-2">منصة السير الذاتية والتوظيف</p>
                  <div className="flex gap-2">
                    <a href="#" className="bg-black text-white px-3 py-1.5 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs">
                      <svg viewBox="0 0 24 24" className="w-3 h-3 text-white" fill="currentColor">
                        <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                      </svg>
                      App Store
                    </a>
                    <a href="#" className="bg-black text-white px-3 py-1.5 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs">
                      <svg viewBox="0 0 24 24" className="w-3 h-3 text-white" fill="currentColor">
                        <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                      </svg>
                      Google Play
                    </a>
                  </div>
                </div>
              </div>

              {/* تطبيق من المالك */}
              <div className="flex items-center gap-4 bg-gray-900 rounded-lg p-4 min-w-[280px]">
                <Logo variant="transparent" size="sm" showText={false} />
                <div className="flex-1">
                  <h6 className="font-medium text-white text-sm">تطبيق من المالك</h6>
                  <p className="text-gray-400 text-xs mb-2">منصة الإعلانات المبوبة</p>
                  <div className="flex gap-2">
                    <a href="#" className="bg-black text-white px-3 py-1.5 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs">
                      <svg viewBox="0 0 24 24" className="w-3 h-3 text-white" fill="currentColor">
                        <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                      </svg>
                      App Store
                    </a>
                    <a href="#" className="bg-black text-white px-3 py-1.5 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs">
                      <svg viewBox="0 0 24 24" className="w-3 h-3 text-white" fill="currentColor">
                        <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                      </svg>
                      Google Play
                    </a>
                  </div>
                </div>
              </div>
            </div>

            {/* QR Code Section */}
            <div className="flex items-center gap-4 bg-gray-900 rounded-lg p-4 min-w-[200px]">
              <div className="relative flex-shrink-0">
                {/* QR Code Background */}
                <div className="w-20 h-20 bg-white rounded-md p-1 shadow-lg">
                  {/* QR Code Pattern */}
                  <div className="w-full h-full relative bg-white">
                    {/* QR Code Grid Pattern */}
                    <svg viewBox="0 0 80 80" className="w-full h-full">
                      {/* Corner squares */}
                      <rect x="0" y="0" width="15" height="15" fill="black"/>
                      <rect x="2" y="2" width="11" height="11" fill="white"/>
                      <rect x="5" y="5" width="5" height="5" fill="black"/>

                      <rect x="65" y="0" width="15" height="15" fill="black"/>
                      <rect x="67" y="2" width="11" height="11" fill="white"/>
                      <rect x="70" y="5" width="5" height="5" fill="black"/>

                      <rect x="0" y="65" width="15" height="15" fill="black"/>
                      <rect x="2" y="67" width="11" height="11" fill="white"/>
                      <rect x="5" y="70" width="5" height="5" fill="black"/>

                      {/* Data pattern */}
                      <rect x="20" y="5" width="3" height="3" fill="black"/>
                      <rect x="25" y="5" width="3" height="3" fill="black"/>
                      <rect x="30" y="5" width="3" height="3" fill="black"/>
                      <rect x="35" y="5" width="3" height="3" fill="black"/>
                      <rect x="45" y="5" width="3" height="3" fill="black"/>
                      <rect x="50" y="5" width="3" height="3" fill="black"/>
                      <rect x="55" y="5" width="3" height="3" fill="black"/>
                      <rect x="60" y="5" width="3" height="3" fill="black"/>

                      <rect x="5" y="20" width="3" height="3" fill="black"/>
                      <rect x="10" y="20" width="3" height="3" fill="black"/>
                      <rect x="20" y="20" width="3" height="3" fill="black"/>
                      <rect x="30" y="20" width="3" height="3" fill="black"/>
                      <rect x="40" y="20" width="3" height="3" fill="black"/>
                      <rect x="50" y="20" width="3" height="3" fill="black"/>
                      <rect x="60" y="20" width="3" height="3" fill="black"/>
                      <rect x="70" y="20" width="3" height="3" fill="black"/>

                      <rect x="5" y="25" width="3" height="3" fill="black"/>
                      <rect x="15" y="25" width="3" height="3" fill="black"/>
                      <rect x="25" y="25" width="3" height="3" fill="black"/>
                      <rect x="35" y="25" width="3" height="3" fill="black"/>
                      <rect x="45" y="25" width="3" height="3" fill="black"/>
                      <rect x="55" y="25" width="3" height="3" fill="black"/>
                      <rect x="65" y="25" width="3" height="3" fill="black"/>
                      <rect x="75" y="25" width="3" height="3" fill="black"/>

                      <rect x="20" y="30" width="3" height="3" fill="black"/>
                      <rect x="30" y="30" width="3" height="3" fill="black"/>
                      <rect x="40" y="30" width="3" height="3" fill="black"/>
                      <rect x="50" y="30" width="3" height="3" fill="black"/>
                      <rect x="60" y="30" width="3" height="3" fill="black"/>

                      <rect x="10" y="35" width="3" height="3" fill="black"/>
                      <rect x="25" y="35" width="3" height="3" fill="black"/>
                      <rect x="45" y="35" width="3" height="3" fill="black"/>
                      <rect x="55" y="35" width="3" height="3" fill="black"/>
                      <rect x="65" y="35" width="3" height="3" fill="black"/>

                      <rect x="5" y="40" width="3" height="3" fill="black"/>
                      <rect x="15" y="40" width="3" height="3" fill="black"/>
                      <rect x="35" y="40" width="3" height="3" fill="black"/>
                      <rect x="50" y="40" width="3" height="3" fill="black"/>
                      <rect x="60" y="40" width="3" height="3" fill="black"/>
                      <rect x="70" y="40" width="3" height="3" fill="black"/>

                      <rect x="20" y="45" width="3" height="3" fill="black"/>
                      <rect x="30" y="45" width="3" height="3" fill="black"/>
                      <rect x="55" y="45" width="3" height="3" fill="black"/>
                      <rect x="65" y="45" width="3" height="3" fill="black"/>
                      <rect x="75" y="45" width="3" height="3" fill="black"/>

                      <rect x="10" y="50" width="3" height="3" fill="black"/>
                      <rect x="25" y="50" width="3" height="3" fill="black"/>
                      <rect x="35" y="50" width="3" height="3" fill="black"/>
                      <rect x="45" y="50" width="3" height="3" fill="black"/>
                      <rect x="60" y="50" width="3" height="3" fill="black"/>

                      <rect x="5" y="55" width="3" height="3" fill="black"/>
                      <rect x="20" y="55" width="3" height="3" fill="black"/>
                      <rect x="40" y="55" width="3" height="3" fill="black"/>
                      <rect x="50" y="55" width="3" height="3" fill="black"/>
                      <rect x="70" y="55" width="3" height="3" fill="black"/>

                      <rect x="15" y="60" width="3" height="3" fill="black"/>
                      <rect x="30" y="60" width="3" height="3" fill="black"/>
                      <rect x="45" y="60" width="3" height="3" fill="black"/>
                      <rect x="55" y="60" width="3" height="3" fill="black"/>
                      <rect x="65" y="60" width="3" height="3" fill="black"/>

                      <rect x="20" y="70" width="3" height="3" fill="black"/>
                      <rect x="30" y="70" width="3" height="3" fill="black"/>
                      <rect x="40" y="70" width="3" height="3" fill="black"/>
                      <rect x="50" y="70" width="3" height="3" fill="black"/>
                      <rect x="60" y="70" width="3" height="3" fill="black"/>
                      <rect x="70" y="70" width="3" height="3" fill="black"/>

                      <rect x="25" y="75" width="3" height="3" fill="black"/>
                      <rect x="35" y="75" width="3" height="3" fill="black"/>
                      <rect x="45" y="75" width="3" height="3" fill="black"/>
                      <rect x="55" y="75" width="3" height="3" fill="black"/>
                      <rect x="65" y="75" width="3" height="3" fill="black"/>
                    </svg>

                    {/* Logo in center */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-5 h-5 bg-white rounded-full flex items-center justify-center shadow-sm border border-gray-200">
                        <Logo variant="transparent" size="xs" showText={false} className="w-3 h-3" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex-1">
                <h6 className="font-medium text-white text-sm">موقع من المالك</h6>
                <p className="text-gray-400 text-xs mb-1">امسح للوصول السريع</p>
                <p className="text-gray-500 text-xs">min-almalek.com</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="text-gray-400 text-sm">
              © 2024 من المالك. جميع الحقوق محفوظة.
            </div>
            <div className="flex flex-wrap gap-6 text-sm">
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                سياسة الخصوصية
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors">
                شروط الاستخدام
              </Link>
              <Link href="/cookies" className="text-gray-400 hover:text-white transition-colors">
                سياسة الكوكيز
              </Link>
              <Link href="/sitemap" className="text-gray-400 hover:text-white transition-colors">
                خريطة الموقع
              </Link>
            </div>
          </div>
        </div>
      </div>


    </footer>
  );
};

export default Footer;
