'use client';

import Link from 'next/link';

interface LogoSVGProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showText?: boolean;
  href?: string;
}

const LogoSVG = ({ 
  size = 'md', 
  className = '', 
  showText = true,
  href = '/'
}: LogoSVGProps) => {
  const sizeClasses = {
    xs: 'h-6 w-auto',
    sm: 'h-8 w-auto',
    md: 'h-10 w-auto',
    lg: 'h-12 w-auto',
    xl: 'h-16 w-auto'
  };

  const textSizeClasses = {
    xs: 'text-sm',
    sm: 'text-base',
    md: 'text-lg',
    lg: 'text-xl',
    xl: 'text-2xl'
  };

  // شعار SVG بالألوان الصحيحة (أصفر وأخضر)
  const HandshakeSVG = () => (
    <svg 
      viewBox="0 0 200 100" 
      className={`${sizeClasses[size]} object-contain`}
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* اليد اليسرى (أصفر) */}
      <path
        d="M20 30 L80 30 L85 35 L85 45 L80 50 L75 55 L70 60 L65 65 L60 70 L55 75 L50 80 L45 75 L40 70 L35 65 L30 60 L25 55 L20 50 Z"
        fill="#FFD700"
        stroke="#FFF"
        strokeWidth="2"
      />
      
      {/* اليد اليمنى (أخضر) */}
      <path
        d="M180 30 L120 30 L115 35 L115 45 L120 50 L125 55 L130 60 L135 65 L140 70 L145 75 L150 80 L155 75 L160 70 L165 65 L170 60 L175 55 L180 50 Z"
        fill="#22C55E"
        stroke="#FFF"
        strokeWidth="2"
      />
      
      {/* منطقة التقاء الأيدي */}
      <ellipse
        cx="100"
        cy="52"
        rx="25"
        ry="15"
        fill="url(#handshakeGradient)"
        stroke="#FFF"
        strokeWidth="2"
      />
      
      {/* تدرج للمنطقة الوسطى */}
      <defs>
        <linearGradient id="handshakeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" stopColor="#FFD700" />
          <stop offset="50%" stopColor="#32CD32" />
          <stop offset="100%" stopColor="#22C55E" />
        </linearGradient>
      </defs>
      
      {/* أصابع اليد اليسرى */}
      <rect x="45" y="25" width="4" height="15" fill="#FFD700" rx="2" />
      <rect x="50" y="22" width="4" height="18" fill="#FFD700" rx="2" />
      <rect x="55" y="24" width="4" height="16" fill="#FFD700" rx="2" />
      <rect x="60" y="26" width="4" height="14" fill="#FFD700" rx="2" />
      
      {/* أصابع اليد اليمنى */}
      <rect x="136" y="25" width="4" height="15" fill="#22C55E" rx="2" />
      <rect x="141" y="22" width="4" height="18" fill="#22C55E" rx="2" />
      <rect x="146" y="24" width="4" height="16" fill="#22C55E" rx="2" />
      <rect x="151" y="26" width="4" height="14" fill="#22C55E" rx="2" />
    </svg>
  );

  const LogoContent = () => (
    <div className={`flex items-center gap-2 ${className}`}>
      <HandshakeSVG />
      {showText && (
        <span className={`font-bold text-primary-600 ${textSizeClasses[size]} hidden sm:block`}>
          من المالك
        </span>
      )}
    </div>
  );

  if (href) {
    return (
      <Link href={href} className="flex items-center">
        <LogoContent />
      </Link>
    );
  }

  return <LogoContent />;
};

export default LogoSVG;
