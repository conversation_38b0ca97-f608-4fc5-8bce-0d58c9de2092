'use client';

import { useState, useRef } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  isPremium: boolean;
  colors: string[];
  fonts: string[];
  layouts: string[];
  languages: string[];
  preview?: string;
}

const templates: Template[] = [
  // 7 قوالب احترافية بألوان وأشكال مختلفة
  {
    id: 'pro-blue',
    name: 'احترافي أزرق',
    description: 'قالب احترافي بتدرجات الأزرق، مناسب للتقنيين والمدراء.',
    category: 'حديث',
    isPremium: false,
    colors: ['#2563eb', '#60a5fa', '#1e3a8a'],
    fonts: ['Arial', 'Roboto'],
    layouts: ['تخطيط حديث'],
    languages: ['العربية', 'الإنجليزية'],
    preview: '/images/resume/templates/pro-blue.png'
  },
  {
    id: 'pro-green',
    name: 'احترافي أخضر',
    description: 'قالب احترافي بتدرجات الأخضر، مناسب للبيئة والصحة.',
    category: 'مهني',
    isPremium: false,
    colors: ['#16a34a', '#4ade80', '#065f46'],
    fonts: ['Cairo', 'Arial'],
    layouts: ['تخطيط مهني'],
    languages: ['العربية', 'الإنجليزية'],
    preview: '/images/resume/templates/pro-green.png'
  },
  {
    id: 'pro-red',
    name: 'احترافي أحمر',
    description: 'قالب احترافي بتدرجات الأحمر، مناسب للمجالات الإبداعية.',
    category: 'إبداعي',
    isPremium: false,
    colors: ['#dc2626', '#f87171', '#991b1b'],
    fonts: ['Tajawal', 'Arial'],
    layouts: ['تخطيط إبداعي'],
    languages: ['العربية', 'الإنجليزية'],
    preview: '/images/resume/templates/pro-red.png'
  },
  {
    id: 'pro-purple',
    name: 'احترافي بنفسجي',
    description: 'قالب احترافي بتدرجات البنفسجي، مناسب للفنانين والمصممين.',
    category: 'إبداعي',
    isPremium: false,
    colors: ['#7c3aed', '#c4b5fd', '#4c1d95'],
    fonts: ['Amiri', 'Arial'],
    layouts: ['تخطيط فني'],
    languages: ['العربية', 'الإنجليزية'],
    preview: '/images/resume/templates/pro-purple.png'
  },
  {
    id: 'pro-orange',
    name: 'احترافي برتقالي',
    description: 'قالب احترافي بتدرجات البرتقالي، مناسب للتسويق والمبيعات.',
    category: 'حديث',
    isPremium: false,
    colors: ['#f59e42', '#fbbf24', '#b45309'],
    fonts: ['Cairo', 'Arial'],
    layouts: ['تخطيط تسويقي'],
    languages: ['العربية', 'الإنجليزية'],
    preview: '/images/resume/templates/pro-orange.png'
  },
  {
    id: 'pro-gray',
    name: 'احترافي رمادي',
    description: 'قالب احترافي بتدرجات الرمادي، مناسب للوظائف الرسمية.',
    category: 'كلاسيكي',
    isPremium: false,
    colors: ['#6b7280', '#d1d5db', '#111827'],
    fonts: ['Times New Roman', 'Arial'],
    layouts: ['تخطيط كلاسيكي'],
    languages: ['العربية', 'الإنجليزية'],
    preview: '/images/resume/templates/pro-gray.png'
  },
  {
    id: 'pro-multi',
    name: 'احترافي متعدد الألوان',
    description: 'قالب احترافي بألوان متعددة، قابل للتخصيص بالكامل.',
    category: 'إبداعي',
    isPremium: false,
    colors: ['#2563eb', '#16a34a', '#dc2626', '#7c3aed', '#f59e42'],
    fonts: ['Arial', 'Cairo'],
    layouts: ['تخطيط متعدد'],
    languages: ['العربية', 'الإنجليزية'],
    preview: '/images/resume/templates/pro-multi.png'
  },
  {
    id: 'creative-1',
    name: 'القالب الإبداعي',
    description: 'قالب إبداعي مميز للمجالات الفنية والتصميم',
    category: 'إبداعي',
    isPremium: true,
    colors: ['#7c3aed', '#c026d3', '#f59e42'],
    fonts: ['Amiri', 'Arial'],
    layouts: ['تخطيط فني'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'professional-1',
    name: 'القالب المهني',
    description: 'قالب مهني راقي للمناصب الإدارية العليا',
    category: 'مهني',
    isPremium: true,
    colors: ['#323130', '#0078d4', '#16a34a'],
    fonts: ['Arial', 'Cairo'],
    layouts: ['تخطيط مهني'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'executive-1',
    name: 'القالب التنفيذي',
    description: 'قالب فاخر للمدراء التنفيذيين والمناصب العليا',
    category: 'مهني',
    isPremium: true,
    colors: ['#605e5c', '#d1d5db', '#0078d4'],
    fonts: ['Times New Roman', 'Arial'],
    layouts: ['تخطيط تنفيذي'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'minimalist-1',
    name: 'القالب البسيط',
    description: 'تصميم بسيط وأنيق يركز على المحتوى',
    category: 'حديث',
    isPremium: false,
    colors: ['#6b7280', '#d1d5db', '#fff'],
    fonts: ['Arial', 'Roboto'],
    layouts: ['تخطيط بسيط'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'colorful-1',
    name: 'القالب الملون',
    description: 'قالب جذاب بألوان متنوعة للمجالات الإبداعية',
    category: 'إبداعي',
    isPremium: true,
    colors: ['#2563eb', '#16a34a', '#dc2626', '#f59e42'],
    fonts: ['Arial', 'Cairo'],
    layouts: ['تخطيط ملون'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'academic-1',
    name: 'القالب الأكاديمي',
    description: 'قالب مناسب للأكاديميين والباحثين',
    category: 'كلاسيكي',
    isPremium: false,
    colors: ['#374151', '#1f2937', '#fff'],
    fonts: ['Times New Roman', 'Georgia'],
    layouts: ['تخطيط أكاديمي'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'tech-1',
    name: 'القالب التقني',
    description: 'قالب متخصص للمطورين والمهندسين',
    category: 'حديث',
    isPremium: true,
    colors: ['#2563eb', '#1e3a8a', '#fff'],
    fonts: ['Roboto', 'Arial'],
    layouts: ['تخطيط تقني'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'elegant-1',
    name: 'القالب الأنيق',
    description: 'تصميم راقي ومتطور للمهنيين',
    category: 'مهني',
    isPremium: true,
    colors: ['#7c3aed', '#605e5c', '#fff'],
    fonts: ['Amiri', 'Arial'],
    layouts: ['تخطيط أنيق'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'startup-1',
    name: 'قالب الشركات الناشئة',
    description: 'قالب حديث مناسب لرواد الأعمال والشركات الناشئة',
    category: 'حديث',
    isPremium: true,
    colors: ['#f59e42', '#2563eb', '#fff'],
    fonts: ['Cairo', 'Arial'],
    layouts: ['تخطيط شركات ناشئة'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'medical-1',
    name: 'القالب الطبي',
    description: 'قالب متخصص للأطباء والعاملين في المجال الطبي',
    category: 'مهني',
    isPremium: true,
    colors: ['#16a34a', '#2563eb', '#fff'],
    fonts: ['Arial', 'Cairo'],
    layouts: ['تخطيط طبي'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'legal-1',
    name: 'القالب القانوني',
    description: 'قالب مناسب للمحامين والعاملين في المجال القانوني',
    category: 'كلاسيكي',
    isPremium: true,
    colors: ['#374151', '#1f2937', '#fff'],
    fonts: ['Times New Roman', 'Georgia'],
    layouts: ['تخطيط قانوني'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'finance-1',
    name: 'القالب المالي',
    description: 'قالب متخصص للعاملين في المجال المالي والمصرفي',
    category: 'مهني',
    isPremium: true,
    colors: ['#323130', '#f59e42', '#fff'],
    fonts: ['Arial', 'Cairo'],
    layouts: ['تخطيط مالي'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'education-1',
    name: 'القالب التعليمي',
    description: 'قالب مناسب للمعلمين والعاملين في التعليم',
    category: 'كلاسيكي',
    isPremium: false,
    colors: ['#2563eb', '#fbbf24', '#fff'],
    fonts: ['Arial', 'Cairo'],
    layouts: ['تخطيط تعليمي'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'marketing-1',
    name: 'قالب التسويق',
    description: 'قالب إبداعي للعاملين في التسويق والإعلان',
    category: 'إبداعي',
    isPremium: true,
    colors: ['#f59e42', '#dc2626', '#fff'],
    fonts: ['Arial', 'Cairo'],
    layouts: ['تخطيط تسويقي'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'engineering-1',
    name: 'القالب الهندسي',
    description: 'قالب متخصص للمهندسين في جميع التخصصات',
    category: 'حديث',
    isPremium: true,
    colors: ['#2563eb', '#16a34a', '#fff'],
    fonts: ['Arial', 'Roboto'],
    layouts: ['تخطيط هندسي'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'sales-1',
    name: 'قالب المبيعات',
    description: 'قالب ديناميكي للعاملين في المبيعات',
    category: 'حديث',
    isPremium: false,
    colors: ['#f59e42', '#dc2626', '#fff'],
    fonts: ['Arial', 'Cairo'],
    layouts: ['تخطيط مبيعات'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'consulting-1',
    name: 'قالب الاستشارات',
    description: 'قالب احترافي للمستشارين في جميع المجالات',
    category: 'مهني',
    isPremium: true,
    colors: ['#323130', '#0078d4', '#fff'],
    fonts: ['Arial', 'Cairo'],
    layouts: ['تخطيط استشارات'],
    languages: ['العربية', 'الإنجليزية']
  },
  {
    id: 'freelancer-1',
    name: 'قالب العمل الحر',
    description: 'قالب مرن للعاملين المستقلين والمتعاقدين',
    category: 'إبداعي',
    isPremium: false,
    colors: ['#7c3aed', '#16a34a', '#fff'],
    fonts: ['Arial', 'Cairo'],
    layouts: ['تخطيط حر'],
    languages: ['العربية', 'الإنجليزية']
  }
];

export default function TemplatesPage() {
  const [selectedCategory, setSelectedCategory] = useState('الكل');
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [previewTemplate, setPreviewTemplate] = useState<Template | null>(null);
  const [customization, setCustomization] = useState({
    color: '#2563eb',
    font: 'Arial',
    layout: 'تخطيط حديث',
    language: 'العربية'
  });
  const previewRef = useRef<HTMLDivElement>(null);

  const categories = ['الكل', 'حديث', 'كلاسيكي', 'إبداعي', 'مهني', 'طبي', 'قانوني', 'تعليمي'];

  const filteredTemplates = selectedCategory === 'الكل'
    ? templates
    : templates.filter(template => template.category === selectedCategory);

  const handleSelectTemplate = (templateId: string) => {
    setSelectedTemplate(templateId);
    window.location.href = `/resume/create?template=${templateId}`;
  };

  const generatePDF = async (template: Template) => {
    if (previewRef.current) {
      const canvas = await html2canvas(previewRef.current);
      const imgData = canvas.toDataURL('image/png');

      const pdf = new jsPDF();
      const imgWidth = 210;
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;

      let position = 0;

      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      pdf.save(`${template.name}.pdf`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">🎨 قوالب السيرة الذاتية</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            اختر من مجموعة متنوعة من القوالب الاحترافية لإنشاء سيرة ذاتية مميزة
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-8">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                selectedCategory === category
                  ? 'bg-primary-600 text-white shadow-lg shadow-primary-500/30'
                  : 'bg-white/30 backdrop-blur-sm text-gray-700 hover:bg-white/50 border border-white/40 active:shadow-lg active:shadow-green-400/50'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
          {filteredTemplates.slice(0, 7).map((template) => (
            <div
              key={template.id}
              className="bg-white/20 backdrop-blur-sm rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 border border-white/30 group"
            >
              {/* Template Preview */}
              <div className="relative h-64 flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
                <div className="absolute inset-0 flex items-center justify-center">
                  <img
                    src={template.preview}
                    alt={template.name}
                    className="w-32 h-44 object-cover rounded-lg border-2 border-gray-200 shadow-lg mx-auto"
                    style={{ background: '#fff', boxSizing: 'border-box' }}
                  />
                  {/* MyCv Logo Overlay */}
                  <img
                    src="/images/MyCV Logo.jpg"
                    alt="MyCv Logo"
                    className="absolute bottom-2 left-2 w-8 h-8 opacity-70 rounded-full border border-gray-200 bg-white"
                  />
                </div>
                {template.isPremium && (
                  <div className="absolute top-2 right-2">
                    <span className="bg-yellow-500 text-white px-1.5 py-0.5 rounded-full text-xs font-bold shadow-lg">
                      ⭐ مميز
                    </span>
                  </div>
                )}
              </div>

              {/* Template Info */}
              <div className="p-6">
                <h3 className="text-lg font-bold text-gray-800 mb-2">{template.name}</h3>
                <p className="text-gray-600 text-sm mb-4">{template.description}</p>

                {/* Color Options */}
                <div className="mb-3">
                  <p className="text-xs text-gray-600 mb-2">الألوان المتاحة:</p>
                  <div className="flex gap-1">
                    {template.colors?.map((color, index) => (
                      <div
                        key={index}
                        className="w-4 h-4 rounded-full border border-gray-300 cursor-pointer hover:scale-110 transition-transform"
                        style={{ backgroundColor: color }}
                        onClick={() => setCustomization({...customization, color})}
                      ></div>
                    ))}
                  </div>
                </div>

                {/* Languages */}
                <div className="mb-4">
                  <p className="text-xs text-gray-600 mb-1">اللغات:</p>
                  <div className="flex gap-1 flex-wrap">
                    {template.languages?.map((lang, index) => (
                      <span key={index} className="text-xs bg-gray-100 px-2 py-1 rounded">
                        {lang}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    {template.category}
                  </span>
                  <div className="flex gap-2">
                    <button
                      onClick={() => setPreviewTemplate(template)}
                      className="px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 text-sm"
                      title="معاينة"
                    >
                      <img
                        src="/images/jobs/Jobs -Personals/icons/visibility_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
                        alt="معاينة"
                        className="w-4 h-4 opacity-80 inline"
                        style={{
                          filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.6))'
                        }}
                      />
                    </button>
                    <button
                      onClick={() => generatePDF(template)}
                      className="px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 text-sm"
                      title="تحميل PDF"
                    >
                      <img
                        src="/images/jobs/Jobs -Personals/icons/download_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
                        alt="تحميل"
                        className="w-4 h-4 opacity-80 inline"
                        style={{
                          filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.6))'
                        }}
                      />
                    </button>
                    <button
                      onClick={() => handleSelectTemplate(template.id)}
                      className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 active:shadow-lg active:shadow-green-400/50 active:scale-105 text-sm ${
                        template.isPremium
                          ? 'bg-yellow-500 text-white hover:bg-yellow-600'
                          : 'bg-primary-600 text-white hover:bg-primary-700'
                      }`}
                    >
                      {template.isPremium ? '⭐ مميز' : 'اختيار'}
                    </button>
                    <button
                      onClick={() => {
                        window.location.href = `/resume/customize?template=${template.id}`;
                      }}
                      className="px-3 py-2 bg-white/30 backdrop-blur-sm border border-white/40 rounded-lg text-gray-700 hover:bg-white/50 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200"
                      title="تخصيص القالب"
                    >
                      <img
                        src="/images/jobs/Jobs -Personals/icons/settings_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                        alt="تخصيص"
                        className="w-4 h-4 opacity-80"
                        style={{
                          filter: 'drop-shadow(0 0 4px rgba(147, 51, 234, 0.6))'
                        }}
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Custom Template Option */}
        <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-8 text-center border border-purple-200">
          <div className="w-20 h-20 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <span className="text-3xl text-white">✨</span>
          </div>
          <h2 className="text-2xl font-bold text-purple-800 mb-4">هل تريد قالباً مخصصاً؟</h2>
          <p className="text-purple-600 mb-6 max-w-2xl mx-auto">
            يمكننا تصميم قالب خاص بك يناسب احتياجاتك ومجال عملك بشكل مثالي
          </p>
          <button
            onClick={() => {
              const message = `🎨 طلب قالب سيرة ذاتية مخصص

أريد طلب تصميم قالب سيرة ذاتية مخصص:
• نوع المجال: [يرجى تحديد مجال العمل]
• النمط المفضل: [حديث/كلاسيكي/إبداعي]
• متطلبات خاصة: [أي متطلبات إضافية]

يرجى التواصل معي لمناقشة التفاصيل والأسعار.

شكراً لكم 🙏`;
              window.open(`https://wa.me/963988652401?text=${encodeURIComponent(message)}`, '_blank');
            }}
            className="px-8 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 active:shadow-lg active:shadow-green-400/50 transition-all duration-200 font-semibold"
          >
            طلب قالب مخصص
          </button>
        </div>

        {/* Preview Modal */}
        {previewTemplate && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold">معاينة: {previewTemplate.name}</h3>
                  <button
                    onClick={() => setPreviewTemplate(null)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    ✕
                  </button>
                </div>

                {/* Preview Content */}
                <div ref={previewRef} className="border border-gray-200 rounded-lg p-8 bg-white" style={{ fontFamily: customization.font }}>
                  {/* Professional Resume Preview */}
                  <div className="max-w-2xl mx-auto">
                    {/* Header */}
                    <div className="text-center mb-6 pb-4" style={{ borderBottom: `2px solid ${customization.color}` }}>
                      <div className="w-24 h-24 rounded-full mx-auto mb-4" style={{ backgroundColor: `${customization.color}20` }}></div>
                      <h1 className="text-3xl font-bold mb-2" style={{ color: customization.color }}>أحمد محمد علي</h1>
                      <p className="text-xl text-gray-600 mb-2">مطور ويب متقدم</p>
                      <div className="text-sm text-gray-500">
                        <EMAIL> | +963 123 456 789 | دمشق، سوريا
                      </div>
                    </div>

                    {/* Summary */}
                    <div className="mb-6">
                      <h2 className="text-lg font-bold mb-3" style={{ color: customization.color }}>نبذة شخصية</h2>
                      <p className="text-gray-700 leading-relaxed">
                        مطور ويب متخصص بخبرة 5 سنوات في تطوير التطبيقات الحديثة باستخدام React و Node.js.
                        شغوف بالتقنيات الحديثة ولديه خبرة واسعة في تطوير واجهات المستخدم التفاعلية.
                      </p>
                    </div>

                    {/* Experience */}
                    <div className="mb-6">
                      <h2 className="text-lg font-bold mb-3" style={{ color: customization.color }}>الخبرة المهنية</h2>
                      <div className="space-y-4">
                        <div>
                          <h3 className="font-semibold">مطور ويب أول</h3>
                          <p className="text-gray-600">شركة التقنيات المتطورة • 2020 - الآن</p>
                          <ul className="text-sm text-gray-700 mt-2 list-disc list-inside">
                            <li>تطوير تطبيقات ويب متقدمة باستخدام React و TypeScript</li>
                            <li>قيادة فريق من 3 مطورين في مشاريع متعددة</li>
                            <li>تحسين أداء التطبيقات بنسبة 40%</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    {/* Skills */}
                    <div className="mb-6">
                      <h2 className="text-lg font-bold mb-3" style={{ color: customization.color }}>المهارات</h2>
                      <div className="flex flex-wrap gap-2">
                        {['React.js', 'Node.js', 'TypeScript', 'MongoDB', 'Git'].map((skill, index) => (
                          <span key={index} className="px-3 py-1 rounded-full text-sm text-white" style={{ backgroundColor: customization.color }}>
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Education */}
                    <div>
                      <h2 className="text-lg font-bold mb-3" style={{ color: customization.color }}>التعليم</h2>
                      <div>
                        <h3 className="font-semibold">بكالوريوس هندسة المعلوماتية</h3>
                        <p className="text-gray-600">جامعة دمشق • 2015 - 2019</p>
                      </div>
                    </div>

                    {/* MyCv Logo */}
                    <div className="mt-8 text-center">
                      <img src="/images/MyCV Logo.jpg" alt="MyCv" className="w-12 h-12 mx-auto opacity-60" />
                    </div>
                  </div>
                </div>

                <div className="flex justify-center gap-4 mt-6">
                  <button
                    onClick={() => generatePDF(previewTemplate)}
                    className="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200"
                  >
                    تحميل PDF
                  </button>
                  <button
                    onClick={() => handleSelectTemplate(previewTemplate.id)}
                    className="px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200"
                  >
                    استخدام هذا القالب
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Back to Jobs */}
        <div className="text-center mt-12">
          <Link
            href="/jobs/individuals"
            className="inline-flex items-center gap-2 px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 active:shadow-lg active:shadow-green-400/50 transition-all duration-200 font-medium"
          >
            <span>←</span>
            العودة للوظائف
          </Link>
        </div>
      </main>

      <Footer />
    </div>
  );
}
