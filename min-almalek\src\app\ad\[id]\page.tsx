import Header from '@/components/Header';
import AdDetails from '@/components/AdDetails';
import Footer from '@/components/Footer';
import ToastContainer from '@/components/ToastContainer';
import { DataService } from '@/lib/data';
import { notFound } from 'next/navigation';

// بيانات تجريبية للإعلان
const sampleAd = {
  id: 1,
  title: 'شقة للبيع في دمشق - المالكي مع إطلالة رائعة على جبل قاسيون',
  price: '85,000,000',
  currency: 'ل.س',
  location: 'دمشق - المالكي',
  category: 'عقارات',
  subcategory: 'شقق للبيع',
  description: `شقة مميزة للبيع في منطقة المالكي الراقية بدمشق

🏠 تفاصيل الشقة:
- المساحة: 150 متر مربع
- الطابق: الخامس من أصل 8 طوابق
- 3 غرف نوم واسعة
- 2 حمام مع تجهيزات حديثة
- صالون كبير مع إطلالة بانورامية
- مطبخ مجهز بالكامل
- شرفة واسعة مطلة على جبل قاسيون

🌟 المميزات:
- موقع مميز في قلب المالكي
- قريب من الخدمات والمواصلات
- أمن وحراسة 24 ساعة
- مصعد حديث
- موقف سيارة خاص
- تدفئة مركزية

📍 الموقع:
- 5 دقائق من شارع بغداد
- قريب من المدارس والجامعات
- محاط بالمطاعم والمقاهي
- سهولة الوصول لوسط البلد

السعر قابل للتفاوض للجادين فقط.
للاستفسار والمعاينة يرجى التواصل على الأرقام المذكورة.`,
  images: [
    '/api/placeholder/800/600',
    '/api/placeholder/800/600',
    '/api/placeholder/800/600',
    '/api/placeholder/800/600',
    '/api/placeholder/800/600'
  ],
  featured: true,
  urgent: false,
  condition: 'ممتاز',
  specs: {
    'المساحة': '150 م²',
    'عدد الغرف': '3 غرف',
    'عدد الحمامات': '2',
    'الطابق': '5 من 8',
    'عمر البناء': '10 سنوات',
    'نوع التدفئة': 'مركزية',
    'المصعد': 'متوفر',
    'موقف السيارة': 'متوفر',
    'الأمن': '24 ساعة',
    'الإطلالة': 'جبل قاسيون'
  },
  postedDate: 'منذ ساعتين',
  views: 245,
  seller: {
    name: 'أحمد محمد العلي',
    type: 'individual' as const,
    verified: true,
    memberSince: 'يناير 2020',
    totalAds: 12,
    phone: '+963 944 123 456',
    email: '<EMAIL>',
    location: 'دمشق - المالكي'
  }
};

interface PageProps {
  params: {
    id: string;
  };
}

export default async function AdPage({ params }: PageProps) {
  const resolvedParams = await params;
  const adId = parseInt(resolvedParams.id);
  const ad = DataService.getAdById(adId);

  if (!ad) {
    notFound();
  }

  // الحصول على إعلانات مشابهة
  const similarAds = DataService.getAdsByCategory(ad.category)
    .filter(similarAd => similarAd.id !== ad.id)
    .slice(0, 4);

  return (
    <div className="min-h-screen">
      <Header />
      <main className="py-8">
        {/* مسار التنقل */}
        <div className="container mx-auto px-4 mb-6">
          <nav className="text-sm text-gray-600">
            <span>الرئيسية</span>
            <span className="mx-2">←</span>
            <span>{ad.category}</span>
            <span className="mx-2">←</span>
            <span>{ad.subcategory}</span>
            <span className="mx-2">←</span>
            <span className="text-gray-800 font-medium">{ad.title}</span>
          </nav>
        </div>

        <AdDetails ad={ad} />

        {/* إعلانات مشابهة */}
        {similarAds.length > 0 && (
          <div className="container mx-auto px-4 mt-12">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">إعلانات مشابهة</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {similarAds.map((similarAd) => (
                <div key={similarAd.id} className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="h-48 bg-gray-200 flex items-center justify-center">
                    {similarAd.images.length > 0 ? (
                      <div className="w-full h-full bg-gray-300 flex items-center justify-center">
                        <span className="text-gray-500 text-sm">🖼️</span>
                      </div>
                    ) : (
                      <span className="text-gray-400 text-3xl">🖼️</span>
                    )}
                  </div>
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2">
                      {similarAd.title}
                    </h3>
                    <div className="text-lg font-bold text-primary-600 mb-2">
                      {similarAd.price.toLocaleString()} {similarAd.currency === 'SYP' ? 'ل.س' : '$'}
                    </div>
                    <div className="text-sm text-gray-600 mb-3">
                      📍 {similarAd.location.governorate}
                    </div>
                    <a
                      href={`/ad/${similarAd.id}`}
                      className="block w-full bg-primary-600 text-white text-center py-2 rounded-lg hover:bg-primary-700 transition-colors text-sm"
                    >
                      عرض التفاصيل
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </main>
      <Footer />
      <ToastContainer />
    </div>
  );
}
