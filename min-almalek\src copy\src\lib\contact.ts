// ملف مركزي لمعلومات الاتصال الموحدة

export const COMPANY_CONTACT = {
  // رقم الهاتف الموحد
  phone: {
    number: '+963988652401',
    displayNumber: '+*********** 401',
    localNumber: '0988 652 401',
    whatsappNumber: '963988652401', // بدون علامة +
    countryCode: '+963',
    areaCode: '988',
    localPart: '652401'
  },

  // روابط التواصل
  links: {
    whatsapp: 'https://wa.me/963988652401',
    whatsappWithMessage: (message: string) => `https://wa.me/963988652401?text=${encodeURIComponent(message)}`,
    call: 'tel:+963988652401',
    sms: 'sms:+963988652401'
  },

  // معلومات إضافية
  info: {
    workingHours: 'من 9 صباحاً حتى 6 مساءً',
    workingDays: 'من السبت إلى الخميس',
    timezone: 'توقيت دمشق (GMT+3)',
    language: 'العربية والإنجليزية',
    responseTime: 'خلال 24 ساعة'
  },

  // رسائل جاهزة للواتساب
  whatsappMessages: {
    general: 'مرحباً، أريد الاستفسار عن خدمات من الملك',
    support: 'مرحباً، أحتاج مساعدة تقنية',
    subscription: 'مرحباً، أريد الاستفسار عن باقات الاشتراك',
    business: 'مرحباً، أريد الاستفسار عن باقات الشركات',
    enterprise: 'مرحباً، أريد الاستفسار عن خطة المؤسسات',
    complaint: 'مرحباً، أريد تقديم شكوى',
    suggestion: 'مرحباً، لدي اقتراح لتحسين الخدمة',
    partnership: 'مرحباً، أريد الاستفسار عن الشراكات',
    advertising: 'مرحباً، أريد الاستفسار عن الإعلانات المدفوعة'
  },

  // معلومات الشركة
  company: {
    name: 'من الملك',
    nameEn: 'Min Almalek',
    description: 'منصة الإعلانات المبوبة الرائدة في سوريا',
    email: '<EMAIL>',
    website: 'https://min-almalek.com',
    address: 'دمشق، سوريا',
    established: '2024'
  }
};

// دوال مساعدة للتواصل
export const ContactUtils = {
  // فتح واتساب مع رسالة
  openWhatsApp: (message?: string) => {
    const url = message
      ? COMPANY_CONTACT.links.whatsappWithMessage(message)
      : COMPANY_CONTACT.links.whatsapp;
    window.open(url, '_blank');
  },

  // إجراء مكالمة
  makeCall: () => {
    window.open(COMPANY_CONTACT.links.call, '_self');
  },

  // إرسال رسالة نصية
  sendSMS: (message?: string) => {
    const url = message
      ? `${COMPANY_CONTACT.links.sms}&body=${encodeURIComponent(message)}`
      : COMPANY_CONTACT.links.sms;
    window.open(url, '_self');
  },

  // نسخ رقم الهاتف
  copyPhoneNumber: () => {
    navigator.clipboard.writeText(COMPANY_CONTACT.phone.number);
  },

  // تنسيق رقم الهاتف للعرض
  formatPhoneForDisplay: (format: 'international' | 'local' | 'clean' = 'international') => {
    switch (format) {
      case 'international':
        return COMPANY_CONTACT.phone.displayNumber;
      case 'local':
        return COMPANY_CONTACT.phone.localNumber;
      case 'clean':
        return COMPANY_CONTACT.phone.number;
      default:
        return COMPANY_CONTACT.phone.displayNumber;
    }
  }
};

// مكون أزرار التواصل
export const ContactButtons = {
  whatsapp: {
    text: 'واتساب',
    icon: 'WhatsAppIcon',
    color: 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 border border-green-400 shadow-lg hover:shadow-xl',
    action: (message?: string) => ContactUtils.openWhatsApp(message)
  },

  call: {
    text: 'اتصال',
    icon: 'PhoneIcon',
    color: 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 border border-blue-400 shadow-lg hover:shadow-xl',
    action: () => ContactUtils.makeCall()
  },

  sms: {
    text: 'رسالة نصية',
    icon: '💬',
    color: 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 border border-purple-400 shadow-lg hover:shadow-xl',
    action: (message?: string) => ContactUtils.sendSMS(message)
  }
};

// أنواع الاستفسارات
export const InquiryTypes = [
  {
    id: 'general',
    name: 'استفسار عام',
    icon: '❓',
    message: COMPANY_CONTACT.whatsappMessages.general
  },
  {
    id: 'support',
    name: 'دعم فني',
    icon: '🛠️',
    message: COMPANY_CONTACT.whatsappMessages.support
  },
  {
    id: 'subscription',
    name: 'باقات الاشتراك',
    icon: '💎',
    message: COMPANY_CONTACT.whatsappMessages.subscription
  },
  {
    id: 'business',
    name: 'باقات الشركات',
    icon: '🏢',
    message: COMPANY_CONTACT.whatsappMessages.business
  },
  {
    id: 'enterprise',
    name: 'خطة المؤسسات',
    icon: '🌟',
    message: COMPANY_CONTACT.whatsappMessages.enterprise
  },
  {
    id: 'complaint',
    name: 'شكوى',
    icon: '⚠️',
    message: COMPANY_CONTACT.whatsappMessages.complaint
  },
  {
    id: 'suggestion',
    name: 'اقتراح',
    icon: '💡',
    message: COMPANY_CONTACT.whatsappMessages.suggestion
  },
  {
    id: 'partnership',
    name: 'شراكة',
    icon: '🤝',
    message: COMPANY_CONTACT.whatsappMessages.partnership
  },
  {
    id: 'advertising',
    name: 'إعلانات مدفوعة',
    icon: '📢',
    message: COMPANY_CONTACT.whatsappMessages.advertising
  }
];

export default COMPANY_CONTACT;
