'use client';

import { useState } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';

interface SecurityLog {
  id: string;
  type: 'login' | 'failed_login' | 'password_change' | 'admin_action' | 'suspicious_activity';
  user: string;
  action: string;
  ip: string;
  userAgent: string;
  timestamp: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface BackupInfo {
  id: string;
  type: 'full' | 'incremental' | 'database' | 'files';
  size: string;
  status: 'completed' | 'in_progress' | 'failed';
  createdAt: string;
  duration: string;
}

export default function SecurityPage() {
  const [securityLogs] = useState<SecurityLog[]>([
    {
      id: '1',
      type: 'login',
      user: 'admin',
      action: 'تسجيل دخول ناجح للوحة الإدارة',
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      timestamp: '2024-01-21T10:30:00Z',
      severity: 'low'
    },
    {
      id: '2',
      type: 'failed_login',
      user: 'unknown',
      action: 'محاولة تسجيل دخول فاشلة - كلمة مرور خاطئة',
      ip: '************',
      userAgent: 'curl/7.68.0',
      timestamp: '2024-01-21T09:15:00Z',
      severity: 'medium'
    },
    {
      id: '3',
      type: 'admin_action',
      user: 'admin',
      action: 'حذف إعلان مخالف (ID: 12345)',
      ip: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      timestamp: '2024-01-21T08:45:00Z',
      severity: 'low'
    },
    {
      id: '4',
      type: 'suspicious_activity',
      user: 'user123',
      action: 'محاولات متعددة لرفع ملفات مشبوهة',
      ip: '************',
      userAgent: 'Python-requests/2.25.1',
      timestamp: '2024-01-20T22:30:00Z',
      severity: 'high'
    }
  ]);

  const [backups] = useState<BackupInfo[]>([
    {
      id: '1',
      type: 'full',
      size: '2.4 GB',
      status: 'completed',
      createdAt: '2024-01-21T02:00:00Z',
      duration: '45 دقيقة'
    },
    {
      id: '2',
      type: 'database',
      size: '156 MB',
      status: 'completed',
      createdAt: '2024-01-20T02:00:00Z',
      duration: '8 دقائق'
    },
    {
      id: '3',
      type: 'incremental',
      size: '89 MB',
      status: 'in_progress',
      createdAt: '2024-01-21T14:00:00Z',
      duration: 'جاري...'
    }
  ]);

  const [activeTab, setActiveTab] = useState<'logs' | 'backups' | 'settings'>('logs');

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'login': return '🔐';
      case 'failed_login': return '❌';
      case 'password_change': return '🔑';
      case 'admin_action': return '⚡';
      case 'suspicious_activity': return '⚠️';
      default: return '📝';
    }
  };

  const getBackupTypeLabel = (type: string) => {
    switch (type) {
      case 'full': return 'نسخة كاملة';
      case 'incremental': return 'نسخة تزايدية';
      case 'database': return 'قاعدة البيانات';
      case 'files': return 'الملفات';
      default: return type;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'Cairo, sans-serif' }}>
            الأمان والنسخ الاحتياطية
          </h1>
          <p className="text-gray-600 mt-1">
            مراقبة الأمان وإدارة النسخ الاحتياطية
          </p>
        </div>

        {/* إحصائيات الأمان */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">محاولات الدخول اليوم</p>
                <p className="text-2xl font-bold text-blue-600">24</p>
                <p className="text-xs text-green-600">+12% من أمس</p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 text-xl">🔐</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">محاولات فاشلة</p>
                <p className="text-2xl font-bold text-red-600">3</p>
                <p className="text-xs text-red-600">تحتاج مراجعة</p>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <span className="text-red-600 text-xl">❌</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">آخر نسخة احتياطية</p>
                <p className="text-2xl font-bold text-green-600">اليوم</p>
                <p className="text-xs text-green-600">2:00 صباحاً</p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 text-xl">💾</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">حجم النسخ الاحتياطية</p>
                <p className="text-2xl font-bold text-purple-600">2.6 GB</p>
                <p className="text-xs text-gray-600">15 نسخة</p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-600 text-xl">📊</span>
              </div>
            </div>
          </div>
        </div>

        {/* التبويبات */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              {[
                { id: 'logs', name: 'سجلات الأمان', icon: '📋' },
                { id: 'backups', name: 'النسخ الاحتياطية', icon: '💾' },
                { id: 'settings', name: 'إعدادات الأمان', icon: '🔧' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-6 text-sm font-medium border-b-2 flex items-center gap-2 ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span>{tab.icon}</span>
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {/* سجلات الأمان */}
            {activeTab === 'logs' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">سجلات الأمان الأخيرة</h3>
                  <button className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                    تصدير السجلات
                  </button>
                </div>
                
                <div className="space-y-3">
                  {securityLogs.map((log) => (
                    <div key={log.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3">
                          <span className="text-2xl">{getTypeIcon(log.type)}</span>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium text-gray-900">{log.action}</span>
                              <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(log.severity)}`}>
                                {log.severity === 'low' ? 'منخفض' :
                                 log.severity === 'medium' ? 'متوسط' :
                                 log.severity === 'high' ? 'عالي' : 'حرج'}
                              </span>
                            </div>
                            <div className="text-sm text-gray-600 space-y-1">
                              <div>المستخدم: {log.user}</div>
                              <div>IP: {log.ip}</div>
                              <div className="truncate">User Agent: {log.userAgent}</div>
                            </div>
                          </div>
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatDate(log.timestamp)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* النسخ الاحتياطية */}
            {activeTab === 'backups' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <h3 className="text-lg font-medium text-gray-900">النسخ الاحتياطية</h3>
                  <div className="flex gap-2">
                    <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                      إنشاء نسخة احتياطية
                    </button>
                    <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                      جدولة النسخ
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {backups.map((backup) => (
                    <div key={backup.id} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <span className="font-medium text-gray-900">
                          {getBackupTypeLabel(backup.type)}
                        </span>
                        <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusColor(backup.status)}`}>
                          {backup.status === 'completed' ? 'مكتمل' :
                           backup.status === 'in_progress' ? 'جاري' : 'فشل'}
                        </span>
                      </div>
                      
                      <div className="space-y-2 text-sm text-gray-600">
                        <div className="flex justify-between">
                          <span>الحجم:</span>
                          <span className="font-medium">{backup.size}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>المدة:</span>
                          <span className="font-medium">{backup.duration}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>التاريخ:</span>
                          <span className="font-medium">{formatDate(backup.createdAt)}</span>
                        </div>
                      </div>

                      <div className="mt-4 flex gap-2">
                        {backup.status === 'completed' && (
                          <>
                            <button className="flex-1 bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700 transition-colors">
                              تحميل
                            </button>
                            <button className="flex-1 bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors">
                              استعادة
                            </button>
                          </>
                        )}
                        <button className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors">
                          حذف
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* إعدادات الأمان */}
            {activeTab === 'settings' && (
              <div className="space-y-6">
                <h3 className="text-lg font-medium text-gray-900">إعدادات الأمان</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">إعدادات كلمة المرور</h4>
                    
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                        <span className="mr-2 text-sm text-gray-700">طلب كلمة مرور قوية</span>
                      </label>
                      
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                        <span className="mr-2 text-sm text-gray-700">انتهاء صلاحية كلمة المرور كل 90 يوم</span>
                      </label>
                      
                      <label className="flex items-center">
                        <input type="checkbox" className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                        <span className="mr-2 text-sm text-gray-700">المصادقة الثنائية</span>
                      </label>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">إعدادات الجلسة</h4>
                    
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          مدة انتهاء الجلسة (بالدقائق)
                        </label>
                        <input
                          type="number"
                          defaultValue={480}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                        />
                      </div>
                      
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                        <span className="mr-2 text-sm text-gray-700">تسجيل خروج تلقائي عند عدم النشاط</span>
                      </label>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">إعدادات النسخ الاحتياطية</h4>
                    
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                        <span className="mr-2 text-sm text-gray-700">نسخة احتياطية يومية تلقائية</span>
                      </label>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          وقت النسخة الاحتياطية
                        </label>
                        <input
                          type="time"
                          defaultValue="02:00"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          الاحتفاظ بالنسخ لمدة (أيام)
                        </label>
                        <input
                          type="number"
                          defaultValue={30}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">إعدادات المراقبة</h4>
                    
                    <div className="space-y-3">
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                        <span className="mr-2 text-sm text-gray-700">تسجيل جميع العمليات الإدارية</span>
                      </label>
                      
                      <label className="flex items-center">
                        <input type="checkbox" defaultChecked className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                        <span className="mr-2 text-sm text-gray-700">تنبيهات الأنشطة المشبوهة</span>
                      </label>
                      
                      <label className="flex items-center">
                        <input type="checkbox" className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                        <span className="mr-2 text-sm text-gray-700">حظر IP بعد 5 محاولات فاشلة</span>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="pt-6 border-t border-gray-200">
                  <button className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 transition-colors">
                    حفظ الإعدادات
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
