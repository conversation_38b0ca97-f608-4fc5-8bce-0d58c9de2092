'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import CashAppLogo from '@/components/CashAppLogo';

export default function SettingsPage() {
  const { user } = useAuth();
  const router = useRouter();
  
  // Settings states
  const [darkMode, setDarkMode] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [smsNotifications, setSmsNotifications] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [autoSave, setAutoSave] = useState(true);
  const [dataBackup, setDataBackup] = useState(false);
  const [analyticsEnabled, setAnalyticsEnabled] = useState(true);
  const [marketingEmails, setMarketingEmails] = useState(false);
  const [language, setLanguage] = useState('ar');
  const [currency, setCurrency] = useState('SYP');
  const [exchangeRates, setExchangeRates] = useState({
    USD: 1,
    EUR: 0.85,
    SYP: 2500
  });
  
  // Payment methods state
  const [paymentMethods, setPaymentMethods] = useState([
    { id: 1, type: 'visa', last4: '4532', isDefault: true },
    { id: 2, type: 'paypal', email: '<EMAIL>', isDefault: false },
    { id: 3, type: 'mastercard', last4: '8765', isDefault: false }
  ]);

  const availablePaymentMethods = [
    {
      id: 'visa',
      name: 'Visa',
      nameAr: 'فيزا كارد',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/4/41/Visa_Logo.png',
      description: 'ادفع بأمان باستخدام بطاقة الفيزا',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      id: 'mastercard',
      name: 'MasterCard',
      nameAr: 'ماستر كارد',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/0/04/Mastercard-logo.png',
      description: 'ادفع بأمان باستخدام بطاقة الماستر كارد',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    },
    {
      id: 'paypal',
      name: 'PayPal',
      nameAr: 'باي بال',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg',
      description: 'ادفع بسهولة عبر حسابك في PayPal',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      id: 'applepay',
      name: 'Apple Pay',
      nameAr: 'آبل بيه',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg',
      description: 'ادفع بسرعة وأمان باستخدام Apple Pay',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200'
    },
    {
      id: 'cashapp',
      name: 'Cash App',
      nameAr: 'كاش آب',
      description: 'ادفع باستخدام تطبيق Cash App',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    }
  ];

  // Load settings on component mount
  useEffect(() => {
    const savedDarkMode = localStorage.getItem('darkMode') === 'true';
    const savedLanguage = localStorage.getItem('language') || 'ar';
    const savedCurrency = localStorage.getItem('currency') || 'SYP';
    const savedNotifications = localStorage.getItem('notifications') !== 'false';
    const savedEmailNotifications = localStorage.getItem('emailNotifications') !== 'false';
    const savedSmsNotifications = localStorage.getItem('smsNotifications') === 'true';
    const savedTwoFactor = localStorage.getItem('twoFactorEnabled') === 'true';
    const savedAutoSave = localStorage.getItem('autoSave') !== 'false';
    const savedDataBackup = localStorage.getItem('dataBackup') === 'true';
    const savedAnalytics = localStorage.getItem('analyticsEnabled') !== 'false';
    const savedMarketing = localStorage.getItem('marketingEmails') === 'true';

    setDarkMode(savedDarkMode);
    setLanguage(savedLanguage);
    setCurrency(savedCurrency);
    setNotifications(savedNotifications);
    setEmailNotifications(savedEmailNotifications);
    setSmsNotifications(savedSmsNotifications);
    setTwoFactorEnabled(savedTwoFactor);
    setAutoSave(savedAutoSave);
    setDataBackup(savedDataBackup);
    setAnalyticsEnabled(savedAnalytics);
    setMarketingEmails(savedMarketing);

    // Apply dark mode if enabled
    if (savedDarkMode) {
      document.documentElement.classList.add('dark');
    }

    // Fetch exchange rates
    fetchExchangeRates();
  }, []);

  const fetchExchangeRates = async () => {
    try {
      toast.loading('جاري تحديث أسعار الصرف...');

      // Use a free exchange rate API
      const response = await fetch('https://api.exchangerate-api.com/v4/latest/USD');
      const data = await response.json();

      if (data && data.rates) {
        const rates = {
          USD: 1,
          EUR: data.rates.EUR || 0.85,
          SYP: data.rates.SYP || 2500
        };
        setExchangeRates(rates);
        localStorage.setItem('exchangeRates', JSON.stringify(rates));
        toast.dismiss();
        toast.success('تم تحديث أسعار الصرف بنجاح');
      } else {
        throw new Error('Invalid API response');
      }
    } catch (error) {
      console.error('Error fetching exchange rates:', error);
      toast.dismiss();
      toast.error('فشل في تحديث أسعار الصرف، يرجى المحاولة لاحقاً');

      // Fallback to simulated rates
      const fallbackRates = {
        USD: 1,
        EUR: 0.85 + Math.random() * 0.1,
        SYP: 2500 + Math.random() * 100
      };
      setExchangeRates(fallbackRates);
    }
  };

  const handleSaveSettings = () => {
    try {
      // تطبيق الوضع الليلي
      if (darkMode) {
        document.documentElement.classList.add('dark');
        localStorage.setItem('darkMode', 'true');
      } else {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('darkMode', 'false');
      }

      // حفظ جميع الإعدادات في localStorage
      localStorage.setItem('language', language);
      localStorage.setItem('currency', currency);
      localStorage.setItem('twoFactorEnabled', twoFactorEnabled.toString());
      localStorage.setItem('emailNotifications', emailNotifications.toString());
      localStorage.setItem('marketingEmails', marketingEmails.toString());
      localStorage.setItem('notifications', notifications.toString());
      localStorage.setItem('smsNotifications', smsNotifications.toString());
      localStorage.setItem('autoSave', autoSave.toString());
      localStorage.setItem('dataBackup', dataBackup.toString());
      localStorage.setItem('analyticsEnabled', analyticsEnabled.toString());

      // تطبيق إعدادات اللغة
      document.documentElement.lang = language === 'ar' ? 'ar' : 'en';
      document.documentElement.dir = language === 'ar' ? 'rtl' : 'ltr';

      // تطبيق إعدادات الإشعارات
      if ('Notification' in window && notifications) {
        Notification.requestPermission().then(permission => {
          if (permission === 'granted') {
            new Notification('من المالك', {
              body: 'تم تفعيل الإشعارات بنجاح',
              icon: '/logo.png'
            });
          }
        });
      }

      // حفظ أسعار الصرف
      localStorage.setItem('exchangeRates', JSON.stringify(exchangeRates));

      toast.success('✅ تم حفظ وتطبيق جميع الإعدادات بنجاح');
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات:', error);
      toast.error('حدث خطأ في حفظ الإعدادات');
    }
  };

  const handleDeletePaymentMethod = (id: number) => {
    setPaymentMethods(prev => prev.filter(method => method.id !== id));
    toast.success('تم حذف وسيلة الدفع بنجاح');
  };

  const handleSetDefaultPaymentMethod = (id: number) => {
    setPaymentMethods(prev => prev.map(method => ({
      ...method,
      isDefault: method.id === id
    })));
    toast.success('تم تعيين وسيلة الدفع الافتراضية');
  };

  const handleAddPaymentMethod = (methodId: string) => {
    // Navigate to payment method setup page
    router.push(`/settings/payment/${methodId}`);
    toast.success('انتقال إلى صفحة إعداد وسيلة الدفع');
  };

  const getPaymentIcon = (type: string) => {
    switch (type) {
      case 'visa':
        return '💳';
      case 'mastercard':
        return '💳';
      case 'paypal':
        return '💰';
      case 'apple-pay':
        return '📱';
      case 'cash-app':
        return '💸';
      default:
        return '💳';
    }
  };

  const ToggleSwitch = ({ enabled, onToggle, label, description, icon }: {
    enabled: boolean;
    onToggle: () => void;
    label: string;
    description: string;
    icon: string;
  }) => (
    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl border border-gray-200 hover:bg-gray-100 transition-all duration-200">
      <div className="flex items-center gap-3">
        <span
          className={`text-xl transition-all duration-200 cursor-pointer ${enabled ? 'opacity-100' : 'opacity-50'}`}
          style={{
            filter: enabled ? 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.8)) grayscale(0)' : 'grayscale(1)',
            transform: enabled ? 'scale(1.1)' : 'scale(1)'
          }}
          onClick={onToggle}
        >
          {icon}
        </span>
        <div>
          <h4 className="text-gray-800 font-medium">{label}</h4>
          <p className="text-gray-600 text-sm">{description}</p>
        </div>
      </div>
      <div
        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-all duration-300 ease-in-out cursor-pointer ${
          enabled ? 'bg-green-500 shadow-lg shadow-green-500/30' : 'bg-gray-300'
        }`}
        onClick={onToggle}
      >
        <span
          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-300 ease-in-out shadow-md ${
            enabled ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 mb-2">إعدادات الحساب والخصوصية</h1>
                <p className="text-gray-600">إدارة إعدادات حسابك وخصوصيتك وطرق الدفع</p>
              </div>
              <div className="text-6xl opacity-50" style={{filter: 'grayscale(1)'}}>⚙️</div>
            </div>
          </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Security Settings */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <span
                className="text-2xl opacity-50 transition-all duration-200 cursor-pointer"
                style={{ filter: 'grayscale(1)' }}
              >
                🔒
              </span>
              الأمان وكلمة المرور
            </h3>
            
            <div className="space-y-4">
              <ToggleSwitch
                enabled={twoFactorEnabled}
                onToggle={() => setTwoFactorEnabled(!twoFactorEnabled)}
                label="المصادقة الثنائية"
                description="حماية إضافية لحسابك"
                icon="🛡️"
              />
              
              <div className="p-4 bg-gray-50 rounded-xl border border-gray-200 hover:bg-gray-100 transition-all duration-200">
                <Link
                  href="/profile/security/password"
                  className="flex items-center justify-between text-gray-800 hover:text-green-600 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <span
                      className="text-xl opacity-50 transition-all duration-200"
                      style={{ filter: 'grayscale(1)' }}
                    >
                      🔑
                    </span>
                    <div>
                      <h4 className="font-medium">تغيير كلمة المرور</h4>
                      <p className="text-gray-600 text-sm">تحديث كلمة المرور الخاصة بك</p>
                    </div>
                  </div>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            </div>
          </div>

          {/* Email Settings */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <span
                className="text-2xl opacity-50 transition-all duration-200 cursor-pointer"
                style={{ filter: 'grayscale(1)' }}
              >
                📧
              </span>
              البريد الإلكتروني
            </h3>
            
            <div className="space-y-4">
              <ToggleSwitch
                enabled={emailNotifications}
                onToggle={() => setEmailNotifications(!emailNotifications)}
                label="إشعارات البريد الإلكتروني"
                description="تلقي إشعارات عبر البريد الإلكتروني"
                icon="📬"
              />
              
              <ToggleSwitch
                enabled={marketingEmails}
                onToggle={() => setMarketingEmails(!marketingEmails)}
                label="رسائل التسويق"
                description="تلقي عروض وأخبار المنصة"
                icon="📢"
              />
              
              <div className="p-4 bg-gray-50 rounded-xl border border-gray-200 hover:bg-gray-100 transition-all duration-200">
                <Link
                  href="/profile/email/change"
                  className="flex items-center justify-between text-gray-800 hover:text-green-600 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <span
                      className="text-xl opacity-50 transition-all duration-200"
                      style={{ filter: 'grayscale(1)' }}
                    >
                      ✏️
                    </span>
                    <div>
                      <h4 className="font-medium">تغيير البريد الإلكتروني</h4>
                      <p className="text-gray-600 text-sm">تحديث عنوان البريد الإلكتروني</p>
                    </div>
                  </div>
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
            </div>
          </div>

          {/* General Settings */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <span
                className="text-2xl opacity-50 transition-all duration-200 cursor-pointer"
                style={{ filter: 'grayscale(1)' }}
                style={{ filter: 'drop-shadow(0 0 8px rgba(168, 85, 247, 0.6))' }}
              >
                ⚙️
              </span>
              الإعدادات العامة
            </h3>

            <div className="space-y-4">
              {/* Language Selection */}
              <div className="p-4 bg-gray-50 rounded-xl border border-gray-200 hover:bg-gray-100 transition-all duration-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span
                      className="text-xl transition-all duration-200 cursor-pointer opacity-50"
                      style={{ filter: 'grayscale(1)' }}
                    >
                      🌐
                    </span>
                    <div>
                      <h4 className="text-gray-800 font-medium">اللغة</h4>
                      <p className="text-gray-600 text-sm">اختر لغة الواجهة</p>
                    </div>
                  </div>
                  <select
                    value={language}
                    onChange={(e) => setLanguage(e.target.value)}
                    className="bg-white text-gray-800 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    <option value="ar">العربية</option>
                    <option value="en">English</option>
                  </select>
                </div>
              </div>

              {/* Currency Selection */}
              <div className="p-4 bg-gray-50 rounded-xl border border-gray-200 hover:bg-gray-100 transition-all duration-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <span
                      className="text-xl transition-all duration-200 cursor-pointer opacity-50"
                      style={{ filter: 'grayscale(1)' }}
                    >
                      💰
                    </span>
                    <div>
                      <h4 className="text-gray-800 font-medium">العملة</h4>
                      <p className="text-gray-600 text-sm">اختر العملة المفضلة</p>
                    </div>
                  </div>
                  <select
                    value={currency}
                    onChange={(e) => setCurrency(e.target.value)}
                    className="bg-white text-gray-800 border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500"
                  >
                    <option value="SYP">الليرة السورية (SYP)</option>
                    <option value="USD">الدولار الأمريكي (USD)</option>
                    <option value="EUR">اليورو (EUR)</option>
                  </select>
                </div>
              </div>

              {/* Exchange Rates */}
              <div className="p-4 bg-gray-50 rounded-xl border border-gray-200">
                <div className="flex items-center gap-3 mb-3">
                  <span
                    className="text-xl opacity-50"
                    style={{ filter: 'grayscale(1)' }}
                  >
                    📈
                  </span>
                  <div>
                    <h4 className="text-gray-800 font-medium">أسعار الصرف الحالية</h4>
                    <p className="text-gray-600 text-sm">أسعار الصرف العالمية المحدثة</p>
                  </div>
                </div>
                <div className="grid grid-cols-3 gap-3">
                  <div className="text-center p-2 bg-white rounded-lg border">
                    <div className="text-sm font-medium text-gray-800">USD</div>
                    <div className="text-lg font-bold text-green-600">{exchangeRates.USD}</div>
                  </div>
                  <div className="text-center p-2 bg-white rounded-lg border">
                    <div className="text-sm font-medium text-gray-800">EUR</div>
                    <div className="text-lg font-bold text-blue-600">{exchangeRates.EUR}</div>
                  </div>
                  <div className="text-center p-2 bg-white rounded-lg border">
                    <div className="text-sm font-medium text-gray-800">SYP</div>
                    <div className="text-lg font-bold text-purple-600">{exchangeRates.SYP}</div>
                  </div>
                </div>
                <button
                  onClick={fetchExchangeRates}
                  className="mt-3 w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors text-sm"
                >
                  تحديث الأسعار
                </button>
              </div>

              <ToggleSwitch
                enabled={darkMode}
                onToggle={() => setDarkMode(!darkMode)}
                label="الوضع الليلي"
                description="تفعيل المظهر المظلم للموقع"
                icon="🌙"
              />
              
              <ToggleSwitch
                enabled={notifications}
                onToggle={() => setNotifications(!notifications)}
                label="الإشعارات"
                description="تلقي إشعارات حول النشاطات الجديدة"
                icon="🔔"
              />
              
              <ToggleSwitch
                enabled={smsNotifications}
                onToggle={() => setSmsNotifications(!smsNotifications)}
                label="إشعارات الرسائل النصية"
                description="تلقي إشعارات عبر الرسائل النصية"
                icon="📱"
              />

              {(user?.userType === 'business' || user?.userType === 'real-estate-office') && (
                <>
                  <ToggleSwitch
                    enabled={autoSave}
                    onToggle={() => setAutoSave(!autoSave)}
                    label="الحفظ التلقائي"
                    description="حفظ المسودات تلقائياً"
                    icon="💾"
                  />

                  <ToggleSwitch
                    enabled={dataBackup}
                    onToggle={() => setDataBackup(!dataBackup)}
                    label="النسخ الاحتياطي"
                    description="إنشاء نسخ احتياطية من البيانات"
                    icon="☁️"
                  />

                  <ToggleSwitch
                    enabled={analyticsEnabled}
                    onToggle={() => setAnalyticsEnabled(!analyticsEnabled)}
                    label="تحليلات متقدمة"
                    description="عرض إحصائيات مفصلة للأداء"
                    icon="📊"
                  />
                </>
              )}
            </div>
          </div>

          {/* Payment Settings */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-3">
              <span
                className="text-2xl transition-all duration-200 cursor-pointer opacity-50"
                style={{ filter: 'grayscale(1)' }}
              >
                💳
              </span>
              معلومات الدفع
            </h3>

            <div className="space-y-4">
              {paymentMethods.map((method) => (
                <div key={method.id} className="p-4 bg-gray-50 rounded-xl border border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 flex items-center justify-center">
                        {method.type === 'visa' && (
                          <img
                            src="https://upload.wikimedia.org/wikipedia/commons/4/41/Visa_Logo.png"
                            alt="Visa"
                            className="h-4 w-auto object-contain"
                          />
                        )}
                        {method.type === 'mastercard' && (
                          <img
                            src="https://upload.wikimedia.org/wikipedia/commons/0/04/Mastercard-logo.png"
                            alt="MasterCard"
                            className="h-4 w-auto object-contain"
                          />
                        )}
                        {method.type === 'paypal' && (
                          <img
                            src="https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg"
                            alt="PayPal"
                            className="h-4 w-auto object-contain"
                          />
                        )}
                        {method.type === 'apple-pay' && (
                          <img
                            src="https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg"
                            alt="Apple Pay"
                            className="h-4 w-auto object-contain"
                          />
                        )}
                        {method.type === 'cash-app' && (
                          <img
                            src="/images/cash-app-logo.svg"
                            alt="Cash App"
                            className="h-4 w-auto object-contain"
                          />
                        )}
                      </div>
                      <div>
                        <h4 className="text-gray-800 font-medium">
                          {method.type === 'visa' ? 'Visa' :
                           method.type === 'mastercard' ? 'MasterCard' :
                           method.type === 'paypal' ? 'PayPal' :
                           method.type === 'apple-pay' ? 'Apple Pay' :
                           method.type === 'cash-app' ? 'Cash App' : method.type}
                        </h4>
                        <p className="text-gray-600 text-sm">
                          {method.last4 ? `**** ${method.last4}` : method.email}
                        </p>
                      </div>
                      {method.isDefault && (
                        <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                          افتراضي
                        </span>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {!method.isDefault && (
                        <button
                          onClick={() => handleSetDefaultPaymentMethod(method.id)}
                          className="text-green-600 hover:text-green-700 transition-colors p-2 rounded-lg hover:bg-green-50"
                          title="تعيين كافتراضي"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </button>
                      )}
                      <button
                        onClick={() => router.push(`/settings/payment/${method.type}?edit=${method.id}`)}
                        className="text-blue-600 hover:text-blue-700 transition-colors p-2 rounded-lg hover:bg-blue-50"
                        title="تعديل"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      <button
                        onClick={() => handleDeletePaymentMethod(method.id)}
                        className="text-red-600 hover:text-red-700 transition-colors p-2 rounded-lg hover:bg-red-50"
                        title="حذف"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              ))}

              {/* Add New Payment Method */}
              <div className="p-4 bg-gray-50 rounded-xl border border-gray-200">
                <h4 className="text-gray-800 font-medium mb-3">إضافة طريقة دفع جديدة</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {availablePaymentMethods.map((method) => (
                    <button
                      key={method.id}
                      onClick={() => handleAddPaymentMethod(method.id)}
                      className={`border-2 rounded-xl p-4 transition-all duration-300 hover:shadow-md cursor-pointer hover:transform hover:scale-102 ${method.borderColor} ${method.bgColor}`}
                    >
                      <div className="text-center">
                        <div className="w-16 h-12 mx-auto mb-3 flex items-center justify-center">
                          {method.id === 'cashapp' ? (
                            <CashAppLogo size="md" />
                          ) : method.id === 'applepay' ? (
                            <div className="flex items-center gap-1">
                              <img
                                src={method.logo}
                                alt={method.name}
                                className="w-6 h-8 object-contain"
                              />
                              <span className="text-sm font-semibold text-gray-800">Pay</span>
                            </div>
                          ) : (
                            <img
                              src={method.logo}
                              alt={method.name}
                              className="w-full h-full object-contain"
                            />
                          )}
                        </div>
                        <h3 className="font-medium text-gray-800 text-sm mb-1">{method.nameAr}</h3>
                        <p className="text-xs text-gray-600">{method.description}</p>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Payment Methods Icons */}
              <div className="mt-6 p-4 bg-gray-50 rounded-xl border border-gray-200">
                <h4 className="text-gray-800 font-medium mb-3">طرق الدفع المدعومة</h4>
                <div className="flex items-center justify-center gap-4 flex-wrap">
                  {/* Visa */}
                  <img
                    src="https://upload.wikimedia.org/wikipedia/commons/4/41/Visa_Logo.png"
                    alt="Visa"
                    className="h-4 w-auto object-contain"
                  />

                  {/* MasterCard */}
                  <img
                    src="https://upload.wikimedia.org/wikipedia/commons/0/04/Mastercard-logo.png"
                    alt="MasterCard"
                    className="h-4 w-auto object-contain"
                  />

                  {/* PayPal */}
                  <img
                    src="https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg"
                    alt="PayPal"
                    className="h-4 w-auto object-contain"
                  />

                  {/* Cash App */}
                  <img
                    src="/images/cash-app-logo.svg"
                    alt="Cash App"
                    className="h-4 w-auto object-contain"
                  />

                  {/* Apple Pay */}
                  <img
                    src="https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg"
                    alt="Apple Pay"
                    className="h-4 w-auto object-contain"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

          {/* Save Button */}
          <div className="flex justify-center mt-8">
            <button
              onClick={handleSaveSettings}
              className="bg-gradient-to-r from-green-500 to-green-600 text-white px-12 py-4 rounded-xl font-medium hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-lg hover:shadow-green-500/25 flex items-center gap-3"
            >
              <span
                className="text-xl opacity-50 transition-all duration-200"
                style={{ filter: 'grayscale(1)' }}
              >
                💾
              </span>
              حفظ وتطبيق جميع الإعدادات
            </button>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
