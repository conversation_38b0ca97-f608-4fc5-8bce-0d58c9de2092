'use client';

import { useEffect } from 'react';

interface AlertModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  message: string;
  type?: 'warning' | 'error' | 'success' | 'info';
  icon?: string;
}

export default function AlertModal({ 
  isOpen, 
  onClose, 
  title, 
  message, 
  type = 'warning',
  icon 
}: AlertModalProps) {
  useEffect(() => {
    if (isOpen) {
      // إغلاق تلقائي بعد 4 ثوان
      const timer = setTimeout(() => {
        onClose();
      }, 4000);

      return () => clearTimeout(timer);
    }
  }, [isOpen, onClose]);

  useEffect(() => {
    // منع التمرير عند فتح المودال
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const getThemeClasses = () => {
    switch (type) {
      case 'warning':
        return {
          bg: 'bg-gradient-to-br from-yellow-50 to-orange-50',
          border: 'border-yellow-200',
          iconBg: 'bg-yellow-100',
          iconColor: 'text-yellow-600',
          titleColor: 'text-yellow-800',
          messageColor: 'text-yellow-700',
          buttonBg: 'bg-yellow-600 hover:bg-yellow-700',
          defaultIcon: '⚠️'
        };
      case 'error':
        return {
          bg: 'bg-gradient-to-br from-red-50 to-pink-50',
          border: 'border-red-200',
          iconBg: 'bg-red-100',
          iconColor: 'text-red-600',
          titleColor: 'text-red-800',
          messageColor: 'text-red-700',
          buttonBg: 'bg-red-600 hover:bg-red-700',
          defaultIcon: '❌'
        };
      case 'success':
        return {
          bg: 'bg-gradient-to-br from-green-50 to-emerald-50',
          border: 'border-green-200',
          iconBg: 'bg-green-100',
          iconColor: 'text-green-600',
          titleColor: 'text-green-800',
          messageColor: 'text-green-700',
          buttonBg: 'bg-green-600 hover:bg-green-700',
          defaultIcon: '✅'
        };
      case 'info':
        return {
          bg: 'bg-gradient-to-br from-blue-50 to-cyan-50',
          border: 'border-blue-200',
          iconBg: 'bg-blue-100',
          iconColor: 'text-blue-600',
          titleColor: 'text-blue-800',
          messageColor: 'text-blue-700',
          buttonBg: 'bg-blue-600 hover:bg-blue-700',
          defaultIcon: 'ℹ️'
        };
      default:
        return {
          bg: 'bg-gradient-to-br from-gray-50 to-slate-50',
          border: 'border-gray-200',
          iconBg: 'bg-gray-100',
          iconColor: 'text-gray-600',
          titleColor: 'text-gray-800',
          messageColor: 'text-gray-700',
          buttonBg: 'bg-gray-600 hover:bg-gray-700',
          defaultIcon: '💬'
        };
    }
  };

  const theme = getThemeClasses();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`${theme.bg} ${theme.border} border-2 rounded-2xl shadow-2xl max-w-md w-full transform transition-all duration-300 scale-100 animate-pulse-once`}>
        {/* Header */}
        <div className="p-6 pb-4">
          <div className="flex items-center gap-4">
            <div className={`${theme.iconBg} w-12 h-12 rounded-full flex items-center justify-center`}>
              <span className="text-2xl">
                {icon || theme.defaultIcon}
              </span>
            </div>
            <div className="flex-1">
              <h3 className={`text-lg font-bold ${theme.titleColor}`}>
                {title}
              </h3>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 pb-6">
          <p className={`${theme.messageColor} leading-relaxed`}>
            {message}
          </p>
        </div>

        {/* Footer */}
        <div className="px-6 pb-6">
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className={`flex-1 ${theme.buttonBg} text-white py-3 px-4 rounded-lg font-semibold transition-colors`}
            >
              فهمت
            </button>
          </div>
        </div>

        {/* Progress bar */}
        <div className="h-1 bg-gray-200 rounded-b-2xl overflow-hidden">
          <div 
            className={`h-full ${theme.buttonBg.split(' ')[0]} animate-progress`}
            style={{
              animation: 'progress 4s linear forwards'
            }}
          />
        </div>
      </div>

      <style jsx>{`
        @keyframes progress {
          from { width: 100%; }
          to { width: 0%; }
        }
        
        @keyframes pulse-once {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }
        
        .animate-pulse-once {
          animation: pulse-once 0.6s ease-in-out;
        }
        
        .animate-progress {
          animation: progress 4s linear forwards;
        }
      `}</style>
    </div>
  );
}
