'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
  showMessage?: boolean;
}

export default function ProtectedRoute({ 
  children, 
  requireAuth = true, 
  redirectTo = '/',
  showMessage = true 
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    // انتظار تحميل حالة المصادقة
    if (isLoading) {
      return;
    }

    setIsChecking(false);

    // إذا كانت الصفحة تتطلب مصادقة والمستخدم غير مسجل
    if (requireAuth && !isAuthenticated) {
      if (showMessage) {
        toast.error('يجب تسجيل الدخول للوصول لهذه الصفحة');
      }
      
      // إعادة توجيه للصفحة المحددة
      router.push(redirectTo);
      return;
    }

    // إذا كانت الصفحة لا تتطلب مصادقة والمستخدم مسجل (مثل صفحات تسجيل الدخول)
    if (!requireAuth && isAuthenticated) {
      router.push('/dashboard');
      return;
    }
  }, [isAuthenticated, isLoading, requireAuth, router, redirectTo, showMessage]);

  // عرض شاشة تحميل أثناء التحقق
  if (isLoading || isChecking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">جاري التحقق من صلاحية الوصول...</p>
        </div>
      </div>
    );
  }

  // إذا كانت الصفحة تتطلب مصادقة والمستخدم غير مسجل، لا تعرض المحتوى
  if (requireAuth && !isAuthenticated) {
    return null;
  }

  // إذا كانت الصفحة لا تتطلب مصادقة والمستخدم مسجل، لا تعرض المحتوى
  if (!requireAuth && isAuthenticated) {
    return null;
  }

  // عرض المحتوى إذا كانت الشروط مستوفاة
  return <>{children}</>;
}

// مكون مخصص للصفحات التي تتطلب تسجيل دخول
export function RequireAuth({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requireAuth={true} redirectTo="/" showMessage={true}>
      {children}
    </ProtectedRoute>
  );
}

// مكون مخصص للصفحات التي لا تتطلب تسجيل دخول (مثل صفحات المصادقة)
export function RequireGuest({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requireAuth={false} redirectTo="/dashboard" showMessage={false}>
      {children}
    </ProtectedRoute>
  );
}

// Hook مخصص للتحقق من صلاحية الوصول
export function useAuthGuard(requireAuth: boolean = true) {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  const checkAccess = () => {
    if (isLoading) return { canAccess: false, isLoading: true };

    if (requireAuth && !isAuthenticated) {
      return { canAccess: false, isLoading: false, shouldRedirect: true, redirectTo: '/' };
    }

    if (!requireAuth && isAuthenticated) {
      return { canAccess: false, isLoading: false, shouldRedirect: true, redirectTo: '/dashboard' };
    }

    return { canAccess: true, isLoading: false };
  };

  const redirectIfNeeded = () => {
    const { shouldRedirect, redirectTo } = checkAccess();
    if (shouldRedirect && redirectTo) {
      router.push(redirectTo);
    }
  };

  return {
    ...checkAccess(),
    user,
    isAuthenticated,
    redirectIfNeeded
  };
}
