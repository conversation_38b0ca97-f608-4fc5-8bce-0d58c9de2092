'use client';

import { useEffect, useState } from 'react';
import AccountDropdown from './AccountDropdown';

const ClientOnlyAccount = () => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    // عرض placeholder أثناء التحميل
    return (
      <div className="relative p-3 text-gray-600 rounded-full">
        <svg
          className="w-6 h-6"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
        </svg>
      </div>
    );
  }

  return <AccountDropdown />;
};

export default ClientOnlyAccount;
