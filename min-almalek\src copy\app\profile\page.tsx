'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNotificationHelpers } from '@/hooks/useNotificationHelpers';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ClientOnlyWrapper from '@/components/ClientOnlyWrapper';
import VerificationBadge from '@/components/VerificationBadge';
import { determineUserBadge } from '@/lib/verification';

export default function ProfilePage() {
  const { user, isAuthenticated, updateProfile } = useAuth();
  const { notifyProfileUpdated } = useNotificationHelpers();
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');

  const [formData, setFormData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || '',
    // معلومات فردية
    firstName: user?.individualInfo?.firstName || '',
    lastName: user?.individualInfo?.lastName || '',
    gender: user?.individualInfo?.gender || '',
    dateOfBirth: user?.individualInfo?.dateOfBirth || '',
    nationalId: user?.individualInfo?.nationalId || '',
    // معلومات الشركة
    companyName: user?.businessInfo?.companyName || '',
    businessType: user?.businessInfo?.businessType || '',
    registrationNumber: user?.businessInfo?.registrationNumber || '',
    taxNumber: user?.businessInfo?.taxNumber || '',
    establishedYear: user?.businessInfo?.establishedYear || '',
    employeeCount: user?.businessInfo?.employeeCount || '',
    website: user?.businessInfo?.website || '',
    description: user?.businessInfo?.description || '',
    // معلومات المكتب العقاري
    officeName: user?.realEstateOfficeInfo?.officeName || '',
    licenseNumber: user?.realEstateOfficeInfo?.licenseNumber || '',
    ownerName: user?.realEstateOfficeInfo?.ownerName || '',
    managerName: user?.realEstateOfficeInfo?.managerName || '',
    yearsOfExperience: user?.realEstateOfficeInfo?.yearsOfExperience || 0,
    teamSize: user?.realEstateOfficeInfo?.teamSize || 1,
    // العنوان
    governorate: user?.individualInfo?.address?.governorate || user?.businessInfo?.address?.governorate || user?.realEstateOfficeInfo?.address?.governorate || '',
    city: user?.individualInfo?.address?.city || user?.businessInfo?.address?.city || user?.realEstateOfficeInfo?.address?.city || '',
    area: user?.individualInfo?.address?.area || user?.businessInfo?.address?.area || user?.realEstateOfficeInfo?.address?.area || '',
    street: user?.businessInfo?.address?.street || user?.realEstateOfficeInfo?.address?.street || '',
    building: user?.realEstateOfficeInfo?.address?.building || '',
  });

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto bg-white rounded-xl shadow-lg p-8 text-center">
            <div className="text-6xl mb-4">🔒</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">تسجيل الدخول مطلوب</h2>
            <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى الملف الشخصي</p>
            <button
              onClick={() => window.location.href = '/'}
              className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              العودة للرئيسية
            </button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  const userBadge = determineUserBadge(user.userType, user.subscription?.planId);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const updates: any = {
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
      };

      if (user.userType === 'individual') {
        updates.individualInfo = {
          ...user.individualInfo,
          firstName: formData.firstName,
          lastName: formData.lastName,
          gender: formData.gender,
          dateOfBirth: formData.dateOfBirth ? new Date(formData.dateOfBirth) : undefined,
          nationalId: formData.nationalId,
          address: {
            governorate: formData.governorate,
            city: formData.city,
            area: formData.area,
          },
        };
      } else if (user.userType === 'business') {
        updates.businessInfo = {
          ...user.businessInfo,
          companyName: formData.companyName,
          businessType: formData.businessType,
          registrationNumber: formData.registrationNumber,
          taxNumber: formData.taxNumber,
          establishedYear: formData.establishedYear ? parseInt(formData.establishedYear) : undefined,
          employeeCount: formData.employeeCount,
          website: formData.website,
          description: formData.description,
          address: {
            ...user.businessInfo?.address,
            governorate: formData.governorate,
            city: formData.city,
            area: formData.area,
            street: formData.street,
          },
        };
      } else if (user.userType === 'real-estate-office') {
        updates.realEstateOfficeInfo = {
          ...user.realEstateOfficeInfo,
          officeName: formData.officeName,
          licenseNumber: formData.licenseNumber,
          ownerName: formData.ownerName,
          managerName: formData.managerName,
          yearsOfExperience: parseInt(formData.yearsOfExperience.toString()),
          teamSize: parseInt(formData.teamSize.toString()),
          address: {
            ...user.realEstateOfficeInfo?.address,
            governorate: formData.governorate,
            city: formData.city,
            area: formData.area,
            street: formData.street,
            building: formData.building,
          },
        };
      }

      await updateProfile(updates);
      notifyProfileUpdated();
      setIsEditing(false);
    } catch (error) {
      console.error('Error updating profile:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const tabs = [
    { id: 'basic', name: 'المعلومات الأساسية', icon: '👤' },
    { id: 'contact', name: 'معلومات الاتصال', icon: '📞' },
    { id: 'address', name: 'العنوان', icon: '📍' },
    ...(user.userType === 'business' ? [{ id: 'business', name: 'معلومات الشركة', icon: '🏢' }] : []),
    ...(user.userType === 'real-estate-office' ? [{ id: 'office', name: 'معلومات المكتب', icon: '🏘️' }] : []),
    { id: 'account', name: 'إعدادات الحساب', icon: '⚙️' },
  ];

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-800">المعلومات الأساسية</h3>
          <div className="flex items-center gap-2">
            <VerificationBadge type={userBadge.type} size="sm" />
            <span className="text-sm text-gray-600">
              {user.userType === 'individual' && 'حساب فردي'}
              {user.userType === 'business' && 'حساب شركة'}
              {user.userType === 'real-estate-office' && 'مكتب عقاري'}
            </span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {user.userType === 'individual' ? (
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الأول</label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الأخير</label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الجنس</label>
                <select
                  value={formData.gender}
                  onChange={(e) => setFormData({...formData, gender: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                >
                  <option value="">اختر الجنس</option>
                  <option value="male">ذكر</option>
                  <option value="female">أنثى</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الميلاد</label>
                <input
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => setFormData({...formData, dateOfBirth: e.target.value})}
                  disabled={!isEditing}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
                />
              </div>
            </>
          ) : (
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {user.userType === 'business' ? 'اسم الشركة' : 'اسم المكتب'}
              </label>
              <input
                type="text"
                value={user.userType === 'business' ? formData.companyName : formData.officeName}
                onChange={(e) => setFormData({
                  ...formData, 
                  [user.userType === 'business' ? 'companyName' : 'officeName']: e.target.value
                })}
                disabled={!isEditing}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 disabled:bg-gray-50"
              />
            </div>
          )}
        </div>

        <div className="mt-6 flex gap-4">
          {!isEditing ? (
            <button
              onClick={() => setIsEditing(true)}
              className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              تعديل المعلومات
            </button>
          ) : (
            <>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
              >
                {isSaving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </button>
              <button
                onClick={() => setIsEditing(false)}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                إلغاء
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <ClientOnlyWrapper
          fallback={
            <div className="max-w-4xl mx-auto">
              <div className="animate-pulse space-y-6">
                <div className="h-32 bg-gray-200 rounded-xl"></div>
                <div className="h-96 bg-gray-200 rounded-xl"></div>
              </div>
            </div>
          }
        >
          <div className="max-w-4xl mx-auto">
            {/* رأس الصفحة */}
            <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-800 mb-2">الملف الشخصي</h1>
                  <p className="text-gray-600">إدارة معلوماتك الشخصية وإعدادات الحساب</p>
                </div>
                <div className="text-6xl">
                  {user.userType === 'individual' && '👤'}
                  {user.userType === 'business' && '🏢'}
                  {user.userType === 'real-estate-office' && '🏘️'}
                </div>
              </div>
            </div>

            {/* التبويبات */}
            <div className="bg-white rounded-xl shadow-lg mb-6">
              <div className="flex overflow-x-auto">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 px-6 py-4 whitespace-nowrap transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary-600 text-white'
                        : 'text-gray-600 hover:bg-gray-50'
                    } ${tab.id === tabs[0].id ? 'rounded-r-xl' : ''} ${
                      tab.id === tabs[tabs.length - 1].id ? 'rounded-l-xl' : ''
                    }`}
                  >
                    <span className="text-lg">{tab.icon}</span>
                    <span className="font-medium">{tab.name}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* محتوى التبويبات */}
            {activeTab === 'basic' && renderBasicInfo()}
            {activeTab === 'contact' && (
              <div className="bg-white rounded-xl shadow-lg p-6 text-center">
                <div className="text-6xl mb-4">📞</div>
                <h4 className="text-xl font-semibold text-gray-800 mb-2">معلومات الاتصال</h4>
                <p className="text-gray-600">قريباً...</p>
              </div>
            )}
            {activeTab === 'address' && (
              <div className="bg-white rounded-xl shadow-lg p-6 text-center">
                <div className="text-6xl mb-4">📍</div>
                <h4 className="text-xl font-semibold text-gray-800 mb-2">العنوان</h4>
                <p className="text-gray-600">قريباً...</p>
              </div>
            )}
            {activeTab === 'business' && (
              <div className="bg-white rounded-xl shadow-lg p-6 text-center">
                <div className="text-6xl mb-4">🏢</div>
                <h4 className="text-xl font-semibold text-gray-800 mb-2">معلومات الشركة</h4>
                <p className="text-gray-600">قريباً...</p>
              </div>
            )}
            {activeTab === 'office' && (
              <div className="bg-white rounded-xl shadow-lg p-6 text-center">
                <div className="text-6xl mb-4">🏘️</div>
                <h4 className="text-xl font-semibold text-gray-800 mb-2">معلومات المكتب</h4>
                <p className="text-gray-600">قريباً...</p>
              </div>
            )}
            {activeTab === 'account' && (
              <div className="bg-white rounded-xl shadow-lg p-6 text-center">
                <div className="text-6xl mb-4">⚙️</div>
                <h4 className="text-xl font-semibold text-gray-800 mb-2">إعدادات الحساب</h4>
                <p className="text-gray-600">قريباً...</p>
              </div>
            )}
          </div>
        </ClientOnlyWrapper>
      </main>
      
      <Footer />
    </div>
  );
}
