'use client';

import { useEffect, useState } from 'react';

interface NoSSRProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}

const NoSSR = ({ children, fallback = null, className }: NoSSRProps) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    return (
      <div className={className} suppressHydrationWarning={true}>
        {fallback}
      </div>
    );
  }

  return (
    <div className={className} suppressHydrationWarning={true}>
      {children}
    </div>
  );
};

export default NoSSR;
