import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';
import { SubscriptionService } from '@/lib/subscriptions';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    // الحصول على جميع الخطط (لا يحتاج مصادقة)
    if (action === 'plans') {
      const plans = SubscriptionService.getAllPlans();
      return NextResponse.json({
        success: true,
        data: plans
      });
    }

    // باقي العمليات تحتاج مصادقة
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    switch (action) {
      case 'payments':
        const payments = SubscriptionService.getUserPayments(sessionResult.data.id);
        return NextResponse.json({
          success: true,
          data: payments
        });

      case 'stats':
        // التحقق من صلاحيات الإدارة
        if (sessionResult.data.type !== 'business') {
          return NextResponse.json(
            { success: false, error: 'غير مصرح لعرض الإحصائيات' },
            { status: 403 }
          );
        }

        const stats = SubscriptionService.getSubscriptionStats();
        return NextResponse.json({
          success: true,
          data: stats
        });

      default:
        return NextResponse.json(
          { success: false, error: 'إجراء غير صحيح' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('خطأ في جلب بيانات الاشتراكات:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { action, planId, method, notes, paymentId, transactionId, receipt } = await request.json();

    switch (action) {
      case 'create_payment':
        if (!planId || !method) {
          return NextResponse.json(
            { success: false, error: 'معرف الخطة وطريقة الدفع مطلوبة' },
            { status: 400 }
          );
        }

        const paymentResult = await SubscriptionService.createPayment(
          sessionResult.data.id,
          planId,
          method,
          notes
        );

        if (paymentResult.success) {
          return NextResponse.json({
            success: true,
            data: paymentResult.data,
            message: 'تم إنشاء طلب الدفع بنجاح'
          });
        } else {
          return NextResponse.json(
            { success: false, error: paymentResult.error },
            { status: 400 }
          );
        }

      case 'confirm_payment':
        if (!paymentId || !transactionId) {
          return NextResponse.json(
            { success: false, error: 'معرف الدفعة ومعرف المعاملة مطلوبان' },
            { status: 400 }
          );
        }

        const confirmResult = await SubscriptionService.confirmPayment(
          paymentId,
          transactionId,
          receipt
        );

        if (confirmResult.success) {
          return NextResponse.json({
            success: true,
            data: confirmResult.data,
            message: 'تم تأكيد الدفعة بنجاح'
          });
        } else {
          return NextResponse.json(
            { success: false, error: confirmResult.error },
            { status: 400 }
          );
        }

      case 'cancel_payment':
        if (!paymentId) {
          return NextResponse.json(
            { success: false, error: 'معرف الدفعة مطلوب' },
            { status: 400 }
          );
        }

        const cancelResult = await SubscriptionService.cancelPayment(
          paymentId,
          sessionResult.data.id
        );

        if (cancelResult.success) {
          return NextResponse.json({
            success: true,
            message: 'تم إلغاء الدفعة بنجاح'
          });
        } else {
          return NextResponse.json(
            { success: false, error: cancelResult.error },
            { status: 400 }
          );
        }

      case 'renew_subscription':
        if (!planId) {
          return NextResponse.json(
            { success: false, error: 'معرف الخطة مطلوب' },
            { status: 400 }
          );
        }

        const renewResult = await SubscriptionService.renewSubscription(
          sessionResult.data.id,
          planId
        );

        if (renewResult.success) {
          return NextResponse.json({
            success: true,
            data: renewResult.data,
            message: 'تم تجديد الاشتراك بنجاح'
          });
        } else {
          return NextResponse.json(
            { success: false, error: renewResult.error },
            { status: 400 }
          );
        }

      default:
        return NextResponse.json(
          { success: false, error: 'إجراء غير صحيح' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('خطأ في معالجة طلب الاشتراك:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
