'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import FilterModal from '@/components/FilterModal';
import CategoryIcon from '@/components/CategoryIcon';
import { Ad, DataService } from '@/lib/data';

export default function FurniturePage() {
  const [ads, setAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);

  // فلاتر البحث
  const [filters, setFilters] = useState({
    mainCategory: '',
    subCategory: '',
    condition: '',
    material: '',
    color: '',
    brand: '',
    priceFrom: '',
    priceTo: '',
    location: '',
    features: [] as string[]
  });

  const furnitureCategories = {
    'living-room': {
      name: 'أثاث غرف المعيشة',
      subCategories: [
        'كنبات',
        'كراسي',
        'طاولات قهوة',
        'طاولات جانبية',
        'مكتبات وأرفف',
        'وحدات تلفزيون',
        'بوفيهات'
      ]
    },
    'bedroom': {
      name: 'أثاث غرف النوم',
      subCategories: [
        'أسرّة',
        'خزائن ملابس',
        'كومودينات',
        'تسريحات',
        'طاولات جانبية',
        'مرايا'
      ]
    },
    'dining-kitchen': {
      name: 'أثاث غرف الطعام والمطابخ',
      subCategories: [
        'طاولات طعام',
        'كراسي طعام',
        'خزائن مطبخ',
        'بوفيهات طعام'
      ]
    },
    'office': {
      name: 'أثاث المكاتب',
      subCategories: [
        'مكاتب عمل',
        'كراسي مكتب',
        'خزائن ملفات',
        'رفوف كتب'
      ]
    },
    'outdoor-garden': {
      name: 'أثاث خارجي وحدائق',
      subCategories: [
        'طاولات وكراسي خارجية',
        'جلسات حدائق',
        'مظلات وشماسي',
        'شوايات',
        'أرجوحات'
      ]
    },
    'appliances': {
      name: 'أجهزة كهربائية منزلية',
      subCategories: [
        'ثلاجات',
        'أفران',
        'ميكروويف',
        'غسالات',
        'مجففات',
        'مكانس كهربائية',
        'مكيفات'
      ]
    },
    'kitchen-tools': {
      name: 'أدوات المطبخ',
      subCategories: [
        'أواني الطهي (قدور، مقالي)',
        'أواني التقديم (صحون، كاسات، فناجين)',
        'أدوات الخبز',
        'أدوات تقطيع وتحضير'
      ]
    },
    'home-tools': {
      name: 'أدوات منزلية عامة',
      subCategories: [
        'سلل غسيل',
        'سلال قمامة',
        'مراوح',
        'سخانات مياه'
      ]
    },
    'decor': {
      name: 'ديكور وإكسسوارات منزلية',
      subCategories: [
        'لوحات فنية',
        'تماثيل وزينة',
        'فازات وأحواض نباتات',
        'ساعات حائط'
      ]
    },
    'lighting': {
      name: 'إضاءة',
      subCategories: [
        'ثريات',
        'أباجورات',
        'إضاءة جدارية',
        'إضاءة خارجية'
      ]
    },
    'carpets-curtains': {
      name: 'سجاد وستائر',
      subCategories: [
        'سجاد أرضيات',
        'ستائر نوافذ',
        'بسط'
      ]
    },
    'storage': {
      name: 'تخزين وتنظيم',
      subCategories: [
        'صناديق تخزين',
        'منظمات أدراج',
        'رفوف حائط'
      ]
    }
  };

  const conditionOptions = ['جديد', 'مستعمل - ممتاز', 'مستعمل - جيد', 'مستعمل - مقبول'];
  const materialOptions = ['خشب', 'معدن', 'بلاستيك', 'زجاج', 'قماش', 'جلد', 'رخام', 'سيراميك', 'أخرى'];
  const colorOptions = ['أسود', 'أبيض', 'بني', 'بيج', 'رمادي', 'أزرق', 'أحمر', 'أخضر', 'أصفر', 'وردي', 'بنفسجي', 'ذهبي'];
  const brandOptions = ['IKEA', 'Ashley', 'La-Z-Boy', 'West Elm', 'Pottery Barn', 'Crate & Barrel', 'Wayfair', 'محلي', 'أخرى'];

  const trendingItems = [
    { name: 'كنبات مودرن', trend: '+22%', icon: '🛋️' },
    { name: 'طاولات قهوة', trend: '+18%', icon: '☕' },
    { name: 'خزائن ملابس', trend: '+15%', icon: '👔' },
    { name: 'إضاءة LED', trend: '+12%', icon: '💡' }
  ];

  useEffect(() => {
    loadAds();
  }, [sortBy, currentPage]);

  const loadAds = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    const result = DataService.getAds({
      category: 'furniture',
      page: currentPage,
      limit: 12,
      sortBy: sortBy as any
    });
    setAds(result.ads);
    setTotalPages(result.totalPages);
    setLoading(false);
  };

  const handleSearch = (filters: any) => {
    console.log('تطبيق فلاتر البحث:', filters);
    loadAds();
  };

  const handleFilterChange = (key: string, value: string | string[]) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      mainCategory: '',
      subCategory: '',
      condition: '',
      material: '',
      color: '',
      brand: '',
      priceFrom: '',
      priceTo: '',
      location: '',
      features: []
    });
  };

  const stats = {
    totalAds: 1456,
    avgPrice: '185,000',
    newToday: 34,
    featuredAds: 78
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 flex items-center justify-center">
              <CategoryIcon
                category="furniture"
                className="w-10 h-10"
                color="#f59e0b"
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">الأثاث والمنزل</h1>
              <p className="text-gray-600">اكتشف أفضل قطع الأثاث والمستلزمات المنزلية</p>
            </div>
          </div>




        </div>

        {/* زر فتح الفلاتر على الموبايل */}
        <div className="lg:hidden mb-6">
          <button
            onClick={() => setIsFilterModalOpen(true)}
            className="w-full bg-white rounded-xl shadow-lg p-4 flex items-center justify-center gap-3 border border-gray-200 hover:bg-gray-50 transition-colors"
          >
            <img
              src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
              alt="فلاتر"
              className="w-6 h-6 opacity-80"
              style={{
                filter: 'drop-shadow(0 0 4px rgba(139, 69, 19, 0.6))'
              }}
            />
            <span className="text-lg font-semibold text-gray-800">البحث والفلاتر</span>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <div className="lg:col-span-1 hidden lg:block">
            <div className="bg-white/30 backdrop-blur-sm rounded-xl shadow-lg p-6 sticky top-8 border border-white/40">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                <img
                  src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="فلاتر"
                  className="w-6 h-6 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(245, 158, 11, 0.6))'
                  }}
                />
                البحث والفلاتر
              </h2>

              {/* البحث السريع */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">البحث السريع</label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="ابحث عن أثاث..."
                    className="w-full px-3 py-2 pr-10 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-amber-500 focus:bg-white/70"
                  />
                  <img
                    src="/images/jobs/Jobs -Personals/icons/search_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                    alt="بحث"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 opacity-60"
                    style={{
                      filter: 'drop-shadow(0 0 4px rgba(245, 158, 11, 0.6))'
                    }}
                  />
                </div>
              </div>

              {/* الفئة الرئيسية */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الفئة الرئيسية
                </label>
                <select
                  value={filters.mainCategory || ''}
                  onChange={(e) => handleFilterChange('mainCategory', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-amber-500 focus:bg-white/70"
                >
                  <option value="">جميع الفئات</option>
                  {Object.entries(furnitureCategories).map(([key, category]) => (
                    <option key={key} value={key}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* الفئة الفرعية */}
              {filters.mainCategory && furnitureCategories[filters.mainCategory as keyof typeof furnitureCategories] && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    الفئة الفرعية
                  </label>
                  <select
                    value={filters.subCategory || ''}
                    onChange={(e) => handleFilterChange('subCategory', e.target.value)}
                    className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-amber-500 focus:bg-white/70"
                  >
                    <option value="">جميع الفئات الفرعية</option>
                    {furnitureCategories[filters.mainCategory as keyof typeof furnitureCategories].subCategories.map((sub, index) => (
                      <option key={index} value={sub}>
                        {sub}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* الحالة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الحالة
                </label>
                <select
                  value={filters.condition || ''}
                  onChange={(e) => handleFilterChange('condition', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-amber-500 focus:bg-white/70"
                >
                  <option value="">جميع الحالات</option>
                  {conditionOptions.map((condition) => (
                    <option key={condition} value={condition}>
                      {condition}
                    </option>
                  ))}
                </select>
              </div>

              {/* المادة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المادة
                </label>
                <select
                  value={filters.material || ''}
                  onChange={(e) => handleFilterChange('material', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-amber-500 focus:bg-white/70"
                >
                  <option value="">جميع المواد</option>
                  {materialOptions.map((material) => (
                    <option key={material} value={material}>
                      {material}
                    </option>
                  ))}
                </select>
              </div>

              {/* اللون */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  اللون
                </label>
                <select
                  value={filters.color || ''}
                  onChange={(e) => handleFilterChange('color', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-amber-500 focus:bg-white/70"
                >
                  <option value="">جميع الألوان</option>
                  {colorOptions.map((color) => (
                    <option key={color} value={color}>
                      {color}
                    </option>
                  ))}
                </select>
              </div>

              {/* الماركة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الماركة
                </label>
                <select
                  value={filters.brand || ''}
                  onChange={(e) => handleFilterChange('brand', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-amber-500 focus:bg-white/70"
                >
                  <option value="">جميع الماركات</option>
                  {brandOptions.map((brand) => (
                    <option key={brand} value={brand}>
                      {brand}
                    </option>
                  ))}
                </select>
              </div>

              {/* نطاق السعر */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نطاق السعر (ل.س)
                </label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="من"
                    value={filters.priceFrom}
                    onChange={(e) => handleFilterChange('priceFrom', e.target.value)}
                    className="px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-amber-500 focus:bg-white/70"
                  />
                  <input
                    type="number"
                    placeholder="إلى"
                    value={filters.priceTo}
                    onChange={(e) => handleFilterChange('priceTo', e.target.value)}
                    className="px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-amber-500 focus:bg-white/70"
                  />
                </div>
              </div>

              {/* المحافظة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المحافظة
                </label>
                <select
                  value={filters.location || ''}
                  onChange={(e) => handleFilterChange('location', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-amber-500 focus:bg-white/70"
                >
                  <option value="">جميع المحافظات</option>
                  <option value="damascus">دمشق</option>
                  <option value="aleppo">حلب</option>
                  <option value="homs">حمص</option>
                  <option value="hama">حماة</option>
                  <option value="lattakia">اللاذقية</option>
                  <option value="tartous">طرطوس</option>
                  <option value="daraa">درعا</option>
                  <option value="sweida">السويداء</option>
                  <option value="quneitra">القنيطرة</option>
                  <option value="idlib">إدلب</option>
                  <option value="raqqa">الرقة</option>
                  <option value="deir-ez-zor">دير الزور</option>
                  <option value="hasaka">الحسكة</option>
                  <option value="rif-damascus">ريف دمشق</option>
                </select>
              </div>

              {/* زر مسح الفلاتر */}
              <button
                onClick={clearFilters}
                className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
              >
                مسح جميع الفلاتر
              </button>
            </div>
          </div>

          <div className="lg:col-span-3">


            {/* شريط النتائج */}
            <div className="bg-white rounded-xl shadow-lg p-4 mb-6 border border-gray-200">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="text-gray-600">
                  عرض <span className="font-semibold text-amber-600">{ads.length}</span> من أصل {ads.length} إعلان
                </div>

                <div className="flex items-center gap-4">
                  {/* طريقة العرض */}
                  <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'grid'
                          ? 'bg-amber-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      🔲 شبكة
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'list'
                          ? 'bg-amber-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      📋 قائمة
                    </button>
                  </div>

                  {/* الترتيب */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-amber-500"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="popular">الأكثر مشاهدة</option>
                    <option value="price-low">السعر: من الأقل للأعلى</option>
                    <option value="price-high">السعر: من الأعلى للأقل</option>
                  </select>
                </div>
              </div>
            </div>

            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                    <div className="h-48 bg-gray-200"></div>
                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : ads.length > 0 ? (
              <>
                <div className={
                  viewMode === 'grid'
                    ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                    : 'space-y-4'
                }>
                  {ads.map((ad) => (
                    <AdCard
                      key={ad.id}
                      ad={ad}
                      viewMode={viewMode}
                    />
                  ))}
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <CategoryIcon
                    category="furniture"
                    className="w-14 h-14"
                    color="#f59e0b"
                  />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد إعلانات</h3>
                <p className="text-gray-600">لم يتم العثور على إعلانات تطابق معايير البحث</p>
              </div>
            )}
          </div>
        </div>
        {/* Modal الفلاتر للموبايل */}
        <FilterModal
          isOpen={isFilterModalOpen}
          onClose={() => setIsFilterModalOpen(false)}
          filters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={clearFilters}
          category="furniture"
        />
      </main>

      <Footer />
    </div>
  );
}
