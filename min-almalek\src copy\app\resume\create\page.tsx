'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ResumeBuilder from '@/components/ResumeBuilder';
import MyCvLogo from '@/components/MyCvLogo';
import { Resume } from '@/lib/jobs';

export default function CreateResumePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = async (resume: Resume) => {
    setIsLoading(true);
    try {
      // هنا سيتم حفظ السيرة الذاتية في قاعدة البيانات
      console.log('Saving resume:', resume);

      // محاكاة عملية الحفظ
      await new Promise(resolve => setTimeout(resolve, 2000));

      // إعادة توجيه إلى صفحة السيرة الذاتية
      router.push('/resume/my-resume');
    } catch (error) {
      console.error('Error saving resume:', error);
      alert('حدث خطأ أثناء حفظ السيرة الذاتية');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (confirm('هل أنت متأكد من إلغاء إنشاء السيرة الذاتية؟ ستفقد جميع البيانات المدخلة.')) {
      router.push('/jobs');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            📄 إنشاء السيرة الذاتية
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-6">
            أنشئ سيرة ذاتية احترافية تساعدك في الحصول على وظيفة أحلامك
          </p>

          {/* MyCv Branding */}
          <div className="flex items-center justify-center gap-2 mb-4 p-3 bg-gradient-to-r from-yellow-100 to-amber-100 rounded-lg border border-yellow-200 max-w-md mx-auto">
            <MyCvLogo size="md" variant="square" />
            <div className="text-left">
              <span className="text-sm font-medium text-amber-800 block">مدعوم من قبل تطبيق MyCv</span>
              <p className="text-xs text-amber-700">منصة متكاملة للسير الذاتية والتوظيف</p>
            </div>
          </div>
        </div>

        {/* Benefits */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-4xl mb-3">⚡</div>
            <h3 className="font-semibold text-gray-800 mb-2">سريع وسهل</h3>
            <p className="text-gray-600 text-sm">
              أنشئ سيرتك الذاتية في دقائق معدودة
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-4xl mb-3">🎯</div>
            <h3 className="font-semibold text-gray-800 mb-2">احترافية</h3>
            <p className="text-gray-600 text-sm">
              تصميم احترافي يلفت انتباه أصحاب العمل
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-4xl mb-3">🔍</div>
            <h3 className="font-semibold text-gray-800 mb-2">قابلة للاكتشاف</h3>
            <p className="text-gray-600 text-sm">
              تظهر للشركات الباحثة عن مواهب مثلك
            </p>
          </div>
        </div>

        {/* Resume Builder */}
        {isLoading ? (
          <div className="bg-white rounded-xl shadow-lg p-12 text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">جاري حفظ السيرة الذاتية...</h3>
            <p className="text-gray-600">يرجى الانتظار قليلاً</p>
          </div>
        ) : (
          <ResumeBuilder
            onSave={handleSave}
            onCancel={handleCancel}
          />
        )}

        {/* Tips */}
        <div className="mt-12 bg-blue-50 rounded-xl p-8">
          <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            💡 نصائح لسيرة ذاتية مميزة
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">كن مختصراً ومحدداً</h4>
                  <p className="text-gray-600 text-sm">
                    استخدم جمل قصيرة وواضحة لوصف خبراتك ومهاراتك
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">أبرز إنجازاتك</h4>
                  <p className="text-gray-600 text-sm">
                    اذكر الإنجازات المحددة والنتائج التي حققتها
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">استخدم كلمات مفتاحية</h4>
                  <p className="text-gray-600 text-sm">
                    أضف المهارات والتقنيات المطلوبة في مجال عملك
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">حدث معلوماتك</h4>
                  <p className="text-gray-600 text-sm">
                    تأكد من أن جميع المعلومات حديثة ودقيقة
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">راجع الأخطاء</h4>
                  <p className="text-gray-600 text-sm">
                    تأكد من عدم وجود أخطاء إملائية أو نحوية
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">كن صادقاً</h4>
                  <p className="text-gray-600 text-sm">
                    لا تبالغ في وصف مهاراتك أو خبراتك
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
