'use client';

import { useState } from 'react';

const FAQ = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const faqs = [
    {
      category: 'عام',
      questions: [
        {
          question: 'ما هو موقع "من المالك"؟',
          answer: 'موقع "من المالك" هو أكبر منصة للإعلانات المبوبة في سوريا، حيث يمكن للأفراد والشركات نشر إعلاناتهم للبيع والشراء في مختلف التصنيفات مثل العقارات والسيارات والإلكترونيات والوظائف وغيرها.'
        },
        {
          question: 'هل استخدام الموقع مجاني؟',
          answer: 'نعم، يمكنك تصفح الإعلانات والتواصل مع البائعين مجاناً. كما يمكنك نشر عدد محدود من الإعلانات المجانية شهرياً. للحصول على مميزات إضافية مثل الإعلانات المميزة، يمكنك الاشتراك في إحدى خططنا المدفوعة.'
        },
        {
          question: 'كيف يمكنني إنشاء حساب؟',
          answer: 'يمكنك إنشاء حساب بسهولة من خلال النقر على "إنشاء حساب" في أعلى الصفحة، ثم ملء البيانات المطلوبة. يمكنك التسجيل باستخدام البريد الإلكتروني أو رقم الهاتف المحمول.'
        },
        {
          question: 'هل الموقع آمن للاستخدام؟',
          answer: 'نعم، نحن نأخذ الأمان على محمل الجد. نوفر نصائح الأمان للمستخدمين ونراقب الإعلانات للتأكد من جودتها. كما ننصح بالتعامل الحذر واتباع إرشادات الأمان المتوفرة على الموقع.'
        }
      ]
    },
    {
      category: 'نشر الإعلانات',
      questions: [
        {
          question: 'كيف يمكنني نشر إعلان؟',
          answer: 'انقر على "أضف إعلانك" في أعلى الصفحة، ثم اختر التصنيف المناسب واملأ تفاصيل الإعلان مع إضافة الصور. بعد المراجعة، سيتم نشر إعلانك خلال 24 ساعة.'
        },
        {
          question: 'كم عدد الصور التي يمكنني إضافتها؟',
          answer: 'يمكنك إضافة حتى 3 صور لكل إعلان في الخطة المجانية، وحتى 15 صورة في الخطط المدفوعة. ننصح بإضافة صور واضحة وعالية الجودة لجذب المزيد من المشترين.'
        },
        {
          question: 'كم تستغرق مراجعة الإعلان؟',
          answer: 'عادة ما تستغرق مراجعة الإعلانات من 2-24 ساعة. الإعلانات المميزة والعاجلة لها أولوية في المراجعة وقد تظهر خلال ساعات قليلة.'
        },
        {
          question: 'لماذا تم رفض إعلاني؟',
          answer: 'قد يتم رفض الإعلان لعدة أسباب: مخالفة شروط الاستخدام، صور غير واضحة، معلومات ناقصة، أو محتوى غير مناسب. ستصلك رسالة توضح سبب الرفض وكيفية تصحيح المشكلة.'
        }
      ]
    },
    {
      category: 'الدفع والاشتراكات',
      questions: [
        {
          question: 'ما هي طرق الدفع المتاحة؟',
          answer: 'نقبل الدفع عبر البطاقات البنكية، التحويل البنكي، والمحافظ الإلكترونية المحلية. جميع المعاملات آمنة ومشفرة.'
        },
        {
          question: 'هل يمكنني إلغاء اشتراكي؟',
          answer: 'نعم، يمكنك إلغاء اشتراكك في أي وقت من لوحة التحكم. سيستمر الاشتراك حتى نهاية الفترة المدفوعة ولن يتم تجديده تلقائياً.'
        },
        {
          question: 'هل يمكنني استرداد أموالي؟',
          answer: 'نوفر ضمان استرداد الأموال خلال 7 أيام من تاريخ الاشتراك إذا لم تكن راضياً عن الخدمة. بعد هذه المدة، لا يمكن استرداد الأموال.'
        },
        {
          question: 'كيف أقوم بترقية اشتراكي؟',
          answer: 'يمكنك ترقية اشتراكك في أي وقت من صفحة "الأسعار" أو من لوحة التحكم. ستدفع الفرق في السعر وستحصل على المميزات الجديدة فوراً.'
        }
      ]
    },
    {
      category: 'التواصل والأمان',
      questions: [
        {
          question: 'كيف يمكنني التواصل مع البائع؟',
          answer: 'يمكنك التواصل مع البائع من خلال نظام الرسائل في الموقع، أو الاتصال المباشر إذا كان رقم الهاتف متاحاً. ننصح بالتواصل عبر الموقع أولاً للحفاظ على سجل المحادثات.'
        },
        {
          question: 'ماذا أفعل إذا واجهت مشكلة مع بائع؟',
          answer: 'يمكنك الإبلاغ عن المشكلة من خلال زر "إبلاغ عن مشكلة" في صفحة الإعلان، أو التواصل مع فريق الدعم الفني. سنتعامل مع الشكوى خلال 24 ساعة.'
        },
        {
          question: 'كيف أتأكد من صحة الإعلان؟',
          answer: 'تحقق من تفاصيل البائع، اطلب صوراً إضافية، اسأل أسئلة مفصلة، والتقي بالبائع في مكان عام قبل الشراء. تجنب الدفع المسبق قبل معاينة المنتج.'
        },
        {
          question: 'هل يمكنني حفظ الإعلانات المفضلة؟',
          answer: 'نعم، يمكنك إضافة الإعلانات إلى قائمة المفضلة بالنقر على أيقونة القلب. ستجد جميع إعلاناتك المفضلة في لوحة التحكم.'
        }
      ]
    },
    {
      category: 'الدعم الفني',
      questions: [
        {
          question: 'كيف يمكنني التواصل مع الدعم الفني؟',
          answer: 'يمكنك التواصل معنا عبر الهاتف +963-11-123-4567، البريد الإلكتروني <EMAIL>، أو نظام التذاكر في لوحة التحكم. نحن متاحون 24/7 لمساعدتك.'
        },
        {
          question: 'هل يوجد تطبيق للهاتف المحمول؟',
          answer: 'نعم، لدينا تطبيقات مجانية لأنظمة iOS و Android. يمكنك تحميلها من App Store أو Google Play للحصول على تجربة أفضل على الهاتف المحمول.'
        },
        {
          question: 'لماذا لا يعمل الموقع بشكل صحيح؟',
          answer: 'تأكد من اتصالك بالإنترنت وحدث متصفحك. إذا استمرت المشكلة، امسح ذاكرة التخزين المؤقت أو جرب متصفحاً آخر. يمكنك أيضاً التواصل مع الدعم الفني.'
        },
        {
          question: 'كيف يمكنني تغيير كلمة المرور؟',
          answer: 'اذهب إلى لوحة التحكم، ثم "إعدادات الحساب"، واختر "تغيير كلمة المرور". ستحتاج إلى إدخال كلمة المرور الحالية والجديدة.'
        }
      ]
    }
  ];

  const toggleQuestion = (categoryIndex: number, questionIndex: number) => {
    const index = categoryIndex * 1000 + questionIndex;
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">الأسئلة الشائعة</h2>
          <p className="text-lg text-gray-600">إجابات على أكثر الأسئلة شيوعاً حول موقع من المالك</p>
        </div>

        <div className="max-w-4xl mx-auto">
          {faqs.map((category, categoryIndex) => (
            <div key={categoryIndex} className="mb-8">
              <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                <span className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm">
                  {categoryIndex + 1}
                </span>
                {category.category}
              </h3>

              <div className="space-y-4">
                {category.questions.map((faq, questionIndex) => {
                  const index = categoryIndex * 1000 + questionIndex;
                  const isOpen = openIndex === index;

                  return (
                    <div key={questionIndex} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                      <button
                        onClick={() => toggleQuestion(categoryIndex, questionIndex)}
                        className="w-full px-6 py-4 text-right flex items-center justify-between hover:bg-gray-50 transition-colors"
                      >
                        <span className="font-medium text-gray-800">{faq.question}</span>
                        <span className={`text-primary-600 transition-transform ${isOpen ? 'rotate-180' : ''}`}>
                          ▼
                        </span>
                      </button>

                      {isOpen && (
                        <div className="px-6 pb-4 border-t border-gray-100">
                          <p className="text-gray-700 leading-relaxed pt-4">{faq.answer}</p>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* قسم المساعدة الإضافية */}
        <div className="mt-16 bg-white rounded-xl shadow-lg p-8 text-center">
          <h3 className="text-2xl font-bold text-gray-800 mb-4">لم تجد إجابة لسؤالك؟</h3>
          <p className="text-gray-600 mb-6">
            فريق الدعم الفني متاح 24/7 لمساعدتك في أي استفسار
          </p>
          <div className="flex flex-col md:flex-row gap-4 justify-center">
            <a
              href="tel:+963-11-123-4567"
              className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
            >
              📞 اتصل بنا
            </a>
            <a
              href="mailto:<EMAIL>"
              className="border border-primary-600 text-primary-600 px-6 py-3 rounded-lg hover:bg-primary-50 transition-colors"
            >
              ✉️ راسلنا
            </a>
            <a
              href="/contact"
              className="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors"
            >
              💬 دردشة مباشرة
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;
