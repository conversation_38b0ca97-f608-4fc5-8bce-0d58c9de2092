'use client';

import { useState } from 'react';
import { Resume, Experience, Education, Skill, Language, Course } from '@/lib/jobs';

interface ResumeBuilderProps {
  initialResume?: Partial<Resume>;
  onSave: (resume: Resume) => void;
  onCancel: () => void;
}

const ResumeBuilder = ({ initialResume, onSave, onCancel }: ResumeBuilderProps) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [resume, setResume] = useState<Partial<Resume>>({
    personalInfo: {
      firstName: '',
      lastName: '',
      title: '',
      summary: '',
      nationality: 'سوري',
      ...initialResume?.personalInfo
    },
    contactInfo: {
      phone: '',
      email: '',
      address: '',
      city: '',
      ...initialResume?.contactInfo
    },
    experiences: initialResume?.experiences || [],
    education: initialResume?.education || [],
    skills: initialResume?.skills || [
      {
        id: 'communication',
        name: 'التواصل والعمل الجماعي',
        level: 'متقدم',
        category: 'إداري',
        verified: false
      },
      {
        id: 'computer',
        name: 'استخدام الحاسوب',
        level: 'متقدم',
        category: 'تقني',
        verified: false
      }
    ],
    languages: initialResume?.languages || [
      {
        id: 'arabic',
        name: 'العربية',
        level: 'أصلي'
      },
      {
        id: 'english',
        name: 'الإنجليزية',
        level: 'متوسط'
      }
    ],
    courses: initialResume?.courses || [],
    isPublic: initialResume?.isPublic || false
  });

  const steps = [
    { id: 0, title: 'المعلومات الشخصية', icon: '👤' },
    { id: 1, title: 'معلومات التواصل', icon: '📞' },
    { id: 2, title: 'الخبرات العملية', icon: '💼' },
    { id: 3, title: 'التعليم والشهادات', icon: '🎓' },
    { id: 4, title: 'المهارات', icon: '⚡' },
    { id: 5, title: 'اللغات', icon: '🌍' },
    { id: 6, title: 'الدورات والشهادات', icon: '📜' },
    { id: 7, title: 'المراجعة والحفظ', icon: '✅' }
  ];

  const updateResume = (section: string, data: any) => {
    setResume(prev => ({
      ...prev,
      [section]: data
    }));
  };

  const addExperience = () => {
    const newExperience: Experience = {
      id: Date.now().toString(),
      jobTitle: '',
      company: '',
      location: '',
      startDate: '',
      current: false,
      description: '',
      achievements: []
    };
    updateResume('experiences', [...(resume.experiences || []), newExperience]);
  };

  const updateExperience = (id: string, field: string, value: any) => {
    const updated = resume.experiences?.map(exp =>
      exp.id === id ? { ...exp, [field]: value } : exp
    );
    updateResume('experiences', updated);
  };

  const removeExperience = (id: string) => {
    const updated = resume.experiences?.filter(exp => exp.id !== id);
    updateResume('experiences', updated);
  };

  const addEducation = () => {
    const newEducation: Education = {
      id: Date.now().toString(),
      degree: '',
      institution: '',
      field: '',
      location: '',
      startDate: '',
      current: false
    };
    updateResume('education', [...(resume.education || []), newEducation]);
  };

  const addSkill = () => {
    const newSkill: Skill = {
      id: Date.now().toString(),
      name: '',
      level: 'متوسط',
      category: 'تقني',
      verified: false
    };
    updateResume('skills', [...(resume.skills || []), newSkill]);
  };

  const addLanguage = () => {
    const newLanguage: Language = {
      id: Date.now().toString(),
      name: '',
      level: 'متوسط'
    };
    updateResume('languages', [...(resume.languages || []), newLanguage]);
  };

  const addCourse = () => {
    const newCourse: Course = {
      id: Date.now().toString(),
      name: '',
      provider: '',
      completionDate: '',
      skills: []
    };
    updateResume('courses', [...(resume.courses || []), newCourse]);
  };

  const handleSave = () => {
    const completeResume: Resume = {
      id: initialResume?.id || Date.now().toString(),
      userId: 'current-user', // سيتم تحديثه من السياق
      personalInfo: resume.personalInfo!,
      contactInfo: resume.contactInfo!,
      experiences: resume.experiences || [],
      education: resume.education || [],
      skills: resume.skills || [],
      languages: resume.languages || [],
      courses: resume.courses || [],
      createdAt: initialResume?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isPublic: resume.isPublic || false,
      views: initialResume?.views || 0
    };
    onSave(completeResume);
  };

  const renderPersonalInfo = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800 mb-4">المعلومات الشخصية</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الأول *</label>
          <input
            type="text"
            value={resume.personalInfo?.firstName || ''}
            onChange={(e) => updateResume('personalInfo', {
              ...resume.personalInfo,
              firstName: e.target.value
            })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">اسم العائلة *</label>
          <input
            type="text"
            value={resume.personalInfo?.lastName || ''}
            onChange={(e) => updateResume('personalInfo', {
              ...resume.personalInfo,
              lastName: e.target.value
            })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">المسمى الوظيفي *</label>
        <input
          type="text"
          value={resume.personalInfo?.title || ''}
          onChange={(e) => updateResume('personalInfo', {
            ...resume.personalInfo,
            title: e.target.value
          })}
          placeholder="مثال: مطور ويب، مهندس برمجيات، مصمم جرافيك"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">نبذة مختصرة *</label>
        <textarea
          value={resume.personalInfo?.summary || ''}
          onChange={(e) => updateResume('personalInfo', {
            ...resume.personalInfo,
            summary: e.target.value
          })}
          placeholder="اكتب نبذة مختصرة عن خبراتك ومهاراتك (2-3 جمل)"
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">الجنسية</label>
          <select
            value={resume.personalInfo?.nationality || 'سوري'}
            onChange={(e) => updateResume('personalInfo', {
              ...resume.personalInfo,
              nationality: e.target.value
            })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="سوري">سوري</option>
            <option value="لبناني">لبناني</option>
            <option value="أردني">أردني</option>
            <option value="فلسطيني">فلسطيني</option>
            <option value="عراقي">عراقي</option>
            <option value="مصري">مصري</option>
            <option value="أخرى">أخرى</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">الحالة الاجتماعية</label>
          <select
            value={resume.personalInfo?.maritalStatus || ''}
            onChange={(e) => updateResume('personalInfo', {
              ...resume.personalInfo,
              maritalStatus: e.target.value
            })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">اختر الحالة</option>
            <option value="أعزب">أعزب</option>
            <option value="متزوج">متزوج</option>
            <option value="مطلق">مطلق</option>
            <option value="أرمل">أرمل</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderContactInfo = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800 mb-4">معلومات التواصل</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
          <input
            type="tel"
            value={resume.contactInfo?.phone || ''}
            onChange={(e) => updateResume('contactInfo', {
              ...resume.contactInfo,
              phone: e.target.value
            })}
            placeholder="+963 XXX XXX XXX"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
          <input
            type="email"
            value={resume.contactInfo?.email || ''}
            onChange={(e) => updateResume('contactInfo', {
              ...resume.contactInfo,
              email: e.target.value
            })}
            placeholder="<EMAIL>"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">المدينة أو الحي *</label>
          <input
            type="text"
            value={resume.contactInfo?.city || ''}
            onChange={(e) => updateResume('contactInfo', {
              ...resume.contactInfo,
              city: e.target.value
            })}
            placeholder="دمشق، حلب، حمص، المالكي، الحمدانية..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
          <input
            type="text"
            value={resume.contactInfo?.address || ''}
            onChange={(e) => updateResume('contactInfo', {
              ...resume.contactInfo,
              address: e.target.value
            })}
            placeholder="الحي، الشارع"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">LinkedIn</label>
          <input
            type="url"
            value={resume.contactInfo?.linkedin || ''}
            onChange={(e) => updateResume('contactInfo', {
              ...resume.contactInfo,
              linkedin: e.target.value
            })}
            placeholder="https://linkedin.com/in/username"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">GitHub</label>
          <input
            type="url"
            value={resume.contactInfo?.github || ''}
            onChange={(e) => updateResume('contactInfo', {
              ...resume.contactInfo,
              github: e.target.value
            })}
            placeholder="https://github.com/username"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Portfolio</label>
          <input
            type="url"
            value={resume.contactInfo?.portfolio || ''}
            onChange={(e) => updateResume('contactInfo', {
              ...resume.contactInfo,
              portfolio: e.target.value
            })}
            placeholder="https://myportfolio.com"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
      </div>
    </div>
  );

  const renderExperiences = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">الخبرات العملية</h3>
        <button
          onClick={addExperience}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
        >
          + إضافة خبرة
        </button>
      </div>

      {resume.experiences?.map((exp, index) => (
        <div key={exp.id} className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-semibold text-gray-800">خبرة #{index + 1}</h4>
            <button
              onClick={() => removeExperience(exp.id)}
              className="text-red-600 hover:text-red-800"
            >
              🗑️ حذف
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المسمى الوظيفي *</label>
              <input
                type="text"
                value={exp.jobTitle}
                onChange={(e) => updateExperience(exp.id, 'jobTitle', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">اسم الشركة *</label>
              <input
                type="text"
                value={exp.company}
                onChange={(e) => updateExperience(exp.id, 'company', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
              <input
                type="text"
                value={exp.location}
                onChange={(e) => updateExperience(exp.id, 'location', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ البداية *</label>
              <input
                type="month"
                value={exp.startDate}
                onChange={(e) => updateExperience(exp.id, 'startDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ النهاية</label>
              <input
                type="month"
                value={exp.endDate || ''}
                onChange={(e) => updateExperience(exp.id, 'endDate', e.target.value)}
                disabled={exp.current}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-100"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={exp.current}
                onChange={(e) => {
                  updateExperience(exp.id, 'current', e.target.checked);
                  if (e.target.checked) {
                    updateExperience(exp.id, 'endDate', '');
                  }
                }}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="mr-2 text-sm text-gray-700">أعمل حالياً في هذا المنصب</span>
            </label>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">وصف المهام والمسؤوليات</label>
            <textarea
              value={exp.description}
              onChange={(e) => updateExperience(exp.id, 'description', e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="اكتب وصفاً مفصلاً عن مهامك ومسؤولياتك في هذا المنصب..."
            />
          </div>
        </div>
      ))}

      {(!resume.experiences || resume.experiences.length === 0) && (
        <div className="text-center py-8 text-gray-500">
          <div className="text-4xl mb-2">💼</div>
          <p>لم تضف أي خبرات عملية بعد</p>
          <p className="text-sm">انقر على "إضافة خبرة" لبدء إضافة خبراتك العملية</p>
        </div>
      )}
    </div>
  );

  const renderEducation = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">التعليم والشهادات</h3>
        <button
          onClick={addEducation}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
        >
          + إضافة شهادة
        </button>
      </div>

      {resume.education?.map((edu, index) => (
        <div key={edu.id} className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-semibold text-gray-800">شهادة #{index + 1}</h4>
            <button
              onClick={() => {
                const updated = resume.education?.filter(e => e.id !== edu.id);
                updateResume('education', updated);
              }}
              className="text-red-600 hover:text-red-800"
            >
              🗑️ حذف
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الدرجة العلمية *</label>
              <input
                type="text"
                value={edu.degree}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, degree: e.target.value } : item
                  );
                  updateResume('education', updated);
                }}
                placeholder="بكالوريوس، ماجستير، دكتوراه..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">التخصص *</label>
              <input
                type="text"
                value={edu.field}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, field: e.target.value } : item
                  );
                  updateResume('education', updated);
                }}
                placeholder="هندسة الحاسوب، إدارة أعمال..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">اسم المؤسسة *</label>
              <input
                type="text"
                value={edu.institution}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, institution: e.target.value } : item
                  );
                  updateResume('education', updated);
                }}
                placeholder="جامعة دمشق، الجامعة الافتراضية..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
              <input
                type="text"
                value={edu.location}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, location: e.target.value } : item
                  );
                  updateResume('education', updated);
                }}
                placeholder="دمشق، حلب، حمص..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ البداية *</label>
              <input
                type="month"
                value={edu.startDate}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, startDate: e.target.value } : item
                  );
                  updateResume('education', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ التخرج</label>
              <input
                type="month"
                value={edu.endDate || ''}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, endDate: e.target.value } : item
                  );
                  updateResume('education', updated);
                }}
                disabled={edu.current}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المعدل (اختياري)</label>
              <input
                type="text"
                value={edu.gpa || ''}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, gpa: e.target.value } : item
                  );
                  updateResume('education', updated);
                }}
                placeholder="3.5/4.0 أو 85%"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={edu.current}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, current: e.target.checked } : item
                  );
                  updateResume('education', updated);
                  if (e.target.checked) {
                    const updatedWithoutEndDate = resume.education?.map(item =>
                      item.id === edu.id ? { ...item, endDate: '' } : item
                    );
                    updateResume('education', updatedWithoutEndDate);
                  }
                }}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="mr-2 text-sm text-gray-700">أدرس حالياً في هذه المؤسسة</span>
            </label>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">وصف إضافي (اختياري)</label>
            <textarea
              value={edu.description || ''}
              onChange={(e) => {
                const updated = resume.education?.map(item =>
                  item.id === edu.id ? { ...item, description: e.target.value } : item
                );
                updateResume('education', updated);
              }}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="أي معلومات إضافية عن دراستك، مشاريع التخرج، الأنشطة..."
            />
          </div>
        </div>
      ))}
    </div>
  );

  const renderSkills = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">المهارات</h3>
        <button
          onClick={addSkill}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
        >
          + إضافة مهارة
        </button>
      </div>

      {/* رسالة ترحيبية */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-green-600">⭐</span>
          <h4 className="font-semibold text-green-800">تم إضافة المهارات الأساسية تلقائياً</h4>
        </div>
        <p className="text-green-700 text-sm">
          أضفنا بعض المهارات الأساسية المطلوبة في سوق العمل. يمكنك تعديلها أو إضافة مهارات أخرى.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {resume.skills?.map((skill, index) => (
          <div key={skill.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-800">مهارة #{index + 1}</h4>
              <button
                onClick={() => {
                  const updated = resume.skills?.filter(s => s.id !== skill.id);
                  updateResume('skills', updated);
                }}
                className="text-red-600 hover:text-red-800"
              >
                🗑️
              </button>
            </div>

            <div className="space-y-3">
              <input
                type="text"
                placeholder="اسم المهارة"
                value={skill.name}
                onChange={(e) => {
                  const updated = resume.skills?.map(s =>
                    s.id === skill.id ? { ...s, name: e.target.value } : s
                  );
                  updateResume('skills', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />

              <select
                value={skill.level}
                onChange={(e) => {
                  const updated = resume.skills?.map(s =>
                    s.id === skill.id ? { ...s, level: e.target.value as any } : s
                  );
                  updateResume('skills', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="مبتدئ">مبتدئ</option>
                <option value="متوسط">متوسط</option>
                <option value="متقدم">متقدم</option>
                <option value="خبير">خبير</option>
              </select>

              <select
                value={skill.category}
                onChange={(e) => {
                  const updated = resume.skills?.map(s =>
                    s.id === skill.id ? { ...s, category: e.target.value as any } : s
                  );
                  updateResume('skills', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="تقني">تقني</option>
                <option value="لغوي">لغوي</option>
                <option value="إداري">إداري</option>
                <option value="إبداعي">إبداعي</option>
                <option value="أخرى">أخرى</option>
              </select>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderLanguages = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">اللغات</h3>
        <button
          onClick={addLanguage}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
        >
          + إضافة لغة
        </button>
      </div>

      {/* رسالة ترحيبية */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-blue-600">💡</span>
          <h4 className="font-semibold text-blue-800">تم إضافة اللغات الأساسية تلقائياً</h4>
        </div>
        <p className="text-blue-700 text-sm">
          أضفنا العربية والإنجليزية كلغات أساسية. يمكنك تعديل مستوى إتقانك أو إضافة لغات أخرى.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {resume.languages?.map((lang, index) => (
          <div key={lang.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-800">لغة #{index + 1}</h4>
              <button
                onClick={() => {
                  const updated = resume.languages?.filter(l => l.id !== lang.id);
                  updateResume('languages', updated);
                }}
                className="text-red-600 hover:text-red-800"
              >
                🗑️
              </button>
            </div>

            <div className="space-y-3">
              <input
                type="text"
                placeholder="اسم اللغة"
                value={lang.name}
                onChange={(e) => {
                  const updated = resume.languages?.map(l =>
                    l.id === lang.id ? { ...l, name: e.target.value } : l
                  );
                  updateResume('languages', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />

              <select
                value={lang.level}
                onChange={(e) => {
                  const updated = resume.languages?.map(l =>
                    l.id === lang.id ? { ...l, level: e.target.value as any } : l
                  );
                  updateResume('languages', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="مبتدئ">مبتدئ</option>
                <option value="متوسط">متوسط</option>
                <option value="متقدم">متقدم</option>
                <option value="أصلي">أصلي</option>
              </select>

              <input
                type="text"
                placeholder="شهادة اللغة (اختياري)"
                value={lang.certification || ''}
                onChange={(e) => {
                  const updated = resume.languages?.map(l =>
                    l.id === lang.id ? { ...l, certification: e.target.value } : l
                  );
                  updateResume('languages', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderCourses = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">الدورات والشهادات</h3>
        <button
          onClick={addCourse}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
        >
          + إضافة دورة
        </button>
      </div>

      {resume.courses?.map((course, index) => (
        <div key={course.id} className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-semibold text-gray-800">دورة #{index + 1}</h4>
            <button
              onClick={() => {
                const updated = resume.courses?.filter(c => c.id !== course.id);
                updateResume('courses', updated);
              }}
              className="text-red-600 hover:text-red-800"
            >
              🗑️ حذف
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">اسم الدورة *</label>
              <input
                type="text"
                value={course.name}
                onChange={(e) => {
                  const updated = resume.courses?.map(c =>
                    c.id === course.id ? { ...c, name: e.target.value } : c
                  );
                  updateResume('courses', updated);
                }}
                placeholder="مثال: دورة تطوير الويب، شهادة إدارة المشاريع"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">مقدم الدورة *</label>
              <input
                type="text"
                value={course.provider}
                onChange={(e) => {
                  const updated = resume.courses?.map(c =>
                    c.id === course.id ? { ...c, provider: e.target.value } : c
                  );
                  updateResume('courses', updated);
                }}
                placeholder="مثال: Coursera، Udemy، جامعة دمشق"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الإكمال</label>
              <input
                type="month"
                value={course.completionDate}
                onChange={(e) => {
                  const updated = resume.courses?.map(c =>
                    c.id === course.id ? { ...c, completionDate: e.target.value } : c
                  );
                  updateResume('courses', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">رابط الشهادة</label>
              <input
                type="url"
                value={course.certificateUrl || ''}
                onChange={(e) => {
                  const updated = resume.courses?.map(c =>
                    c.id === course.id ? { ...c, certificateUrl: e.target.value } : c
                  );
                  updateResume('courses', updated);
                }}
                placeholder="https://example.com/certificate"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">المهارات المكتسبة</label>
            <input
              type="text"
              value={course.skills?.join(', ') || ''}
              onChange={(e) => {
                const skillsArray = e.target.value.split(',').map(skill => skill.trim()).filter(skill => skill);
                const updated = resume.courses?.map(c =>
                  c.id === course.id ? { ...c, skills: skillsArray } : c
                );
                updateResume('courses', updated);
              }}
              placeholder="مثال: JavaScript, React, Node.js (افصل بفاصلة)"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
            <p className="text-xs text-gray-500 mt-1">افصل المهارات بفاصلة</p>
          </div>
        </div>
      ))}
    </div>
  );

  const renderReview = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800">مراجعة السيرة الذاتية</h3>

      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="font-semibold text-gray-800 mb-4">معاينة السيرة الذاتية</h4>

        {/* Personal Info Preview */}
        <div className="mb-6">
          <h5 className="font-medium text-gray-700 mb-2">المعلومات الشخصية:</h5>
          <p className="text-gray-600">
            {resume.personalInfo?.firstName} {resume.personalInfo?.lastName} - {resume.personalInfo?.title}
          </p>
          <p className="text-gray-600 text-sm">{resume.personalInfo?.summary}</p>
        </div>

        {/* Contact Info Preview */}
        <div className="mb-6">
          <h5 className="font-medium text-gray-700 mb-2">معلومات التواصل:</h5>
          <p className="text-gray-600 text-sm">
            {resume.contactInfo?.email} | {resume.contactInfo?.phone} | {resume.contactInfo?.city}
          </p>
        </div>

        {/* Experience Count */}
        <div className="mb-6">
          <h5 className="font-medium text-gray-700 mb-2">الخبرات:</h5>
          <p className="text-gray-600 text-sm">
            {resume.experiences?.length || 0} خبرة عملية
          </p>
        </div>

        {/* Skills Count */}
        <div className="mb-6">
          <h5 className="font-medium text-gray-700 mb-2">المهارات:</h5>
          <p className="text-gray-600 text-sm">
            {resume.skills?.length || 0} مهارة
          </p>
        </div>

        {/* Privacy Setting */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={resume.isPublic || false}
              onChange={(e) => updateResume('isPublic', e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <span className="mr-2 text-sm text-gray-700">
              جعل السيرة الذاتية مرئية للشركات (يمكن للشركات العثور على ملفك الشخصي)
            </span>
          </label>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-lg">
      {/* Progress Steps */}
      <div className="border-b border-gray-200 p-4 md:p-6">
        {/* Mobile Progress */}
        <div className="md:hidden">
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium text-gray-600">
              الخطوة {currentStep + 1} من {steps.length}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(((currentStep + 1) / steps.length) * 100)}%
            </span>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div
              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            ></div>
          </div>

          <div className="text-center">
            <div className="text-2xl mb-2">{steps[currentStep].icon}</div>
            <div className="text-lg font-semibold text-gray-800">
              {steps[currentStep].title}
            </div>
          </div>
        </div>

        {/* Desktop Progress */}
        <div className="hidden md:block">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={`flex items-center ${index < steps.length - 1 ? 'flex-1' : ''}`}
              >
                <div className="flex flex-col items-center">
                  <div
                    className={`w-12 h-12 rounded-full flex items-center justify-center text-lg font-medium mb-2 ${
                      currentStep >= step.id
                        ? 'bg-primary-600 text-white'
                        : 'bg-gray-200 text-gray-600'
                    }`}
                  >
                    {step.icon}
                  </div>
                  <div className="text-center max-w-24">
                    <div className={`text-xs font-medium leading-tight ${
                      currentStep >= step.id ? 'text-primary-600' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </div>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`flex-1 h-0.5 mx-2 mt-6 ${
                    currentStep > step.id ? 'bg-primary-600' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Step Content */}
      <div className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          {currentStep === 0 && renderPersonalInfo()}
          {currentStep === 1 && renderContactInfo()}
          {currentStep === 2 && renderExperiences()}
          {currentStep === 3 && renderEducation()}
          {currentStep === 4 && renderSkills()}
          {currentStep === 5 && renderLanguages()}
          {currentStep === 6 && renderCourses()}
          {currentStep === 7 && renderReview()}
        </div>
      </div>

      {/* Navigation */}
      <div className="border-t border-gray-200 p-4 md:p-6">
        <div className="flex flex-col sm:flex-row justify-between gap-4">
          <button
            onClick={() => currentStep > 0 ? setCurrentStep(currentStep - 1) : onCancel()}
            className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors flex items-center justify-center gap-2"
          >
            {currentStep === 0 ? (
              <>
                <span>❌</span>
                <span>إلغاء</span>
              </>
            ) : (
              <>
                <span>←</span>
                <span>السابق</span>
              </>
            )}
          </button>

          <div className="hidden sm:flex items-center gap-2 text-sm text-gray-500">
            <span>الخطوة {currentStep + 1} من {steps.length}</span>
            <div className="w-32 bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
              ></div>
            </div>
          </div>

          <button
            onClick={() => {
              if (currentStep < steps.length - 1) {
                setCurrentStep(currentStep + 1);
              } else {
                handleSave();
              }
            }}
            className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center justify-center gap-2 font-medium"
          >
            {currentStep === steps.length - 1 ? (
              <>
                <span>💾</span>
                <span>حفظ السيرة الذاتية</span>
              </>
            ) : (
              <>
                <span>التالي</span>
                <span>→</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ResumeBuilder;
