module.exports = {

"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/components/HydrationProvider.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const HydrationProvider = ({ children })=>{
    const [isHydrated, setIsHydrated] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // إزالة عناصر Grammarly قبل الـ hydration
        const removeGrammarlyElements = ()=>{
            // إزالة عناصر Grammarly
            const grammarlySelectors = [
                'grammarly-extension',
                'grammarly-popups',
                'grammarly-desktop-integration',
                '[data-grammarly-shadow-root]',
                '[data-grammarly-part]'
            ];
            grammarlySelectors.forEach((selector)=>{
                const elements = document.querySelectorAll(selector);
                elements.forEach((el)=>el.remove());
            });
            // إزالة attributes من body
            const bodyAttributes = [
                'data-new-gr-c-s-check-loaded',
                'data-gr-ext-installed',
                'data-new-gr-c-s-loaded',
                'data-grammarly-shadow-root'
            ];
            bodyAttributes.forEach((attr)=>{
                document.body.removeAttribute(attr);
                document.documentElement.removeAttribute(attr);
            });
        };
        // تشغيل التنظيف فوراً
        removeGrammarlyElements();
        // تشغيل التنظيف بشكل دوري لمنع إعادة الإدراج
        const cleanupInterval = setInterval(removeGrammarlyElements, 100);
        // تعيين الـ hydration بعد تأخير قصير
        const timer = setTimeout(()=>{
            setIsHydrated(true);
            clearInterval(cleanupInterval);
        }, 150);
        return ()=>{
            clearTimeout(timer);
            clearInterval(cleanupInterval);
        };
    }, []);
    // عرض loading skeleton أثناء انتظار hydration
    if (!isHydrated) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen bg-gray-50",
            suppressHydrationWarning: true,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-pulse",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-16 bg-white border-b border-gray-200 mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/HydrationProvider.tsx",
                        lineNumber: 67,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "container mx-auto px-4 py-8",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "h-32 bg-gray-200 rounded-lg mb-8"
                            }, void 0, false, {
                                fileName: "[project]/src/components/HydrationProvider.tsx",
                                lineNumber: 71,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8",
                                children: Array.from({
                                    length: 6
                                }).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "h-24 bg-gray-200 rounded-lg"
                                    }, i, false, {
                                        fileName: "[project]/src/components/HydrationProvider.tsx",
                                        lineNumber: 76,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/HydrationProvider.tsx",
                                lineNumber: 74,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-1 md:grid-cols-3 gap-6",
                                children: Array.from({
                                    length: 6
                                }).map((_, i)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "h-48 bg-gray-200 rounded-lg"
                                    }, i, false, {
                                        fileName: "[project]/src/components/HydrationProvider.tsx",
                                        lineNumber: 83,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/HydrationProvider.tsx",
                                lineNumber: 81,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/HydrationProvider.tsx",
                        lineNumber: 70,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/HydrationProvider.tsx",
                lineNumber: 65,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/HydrationProvider.tsx",
            lineNumber: 64,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        suppressHydrationWarning: true,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/HydrationProvider.tsx",
        lineNumber: 93,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = HydrationProvider;
}}),
"[project]/src/components/LiveChat.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const LiveChat = ()=>{
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [messages, setMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([
        {
            id: 1,
            text: 'مرحباً! كيف يمكنني مساعدتك اليوم؟',
            sender: 'support',
            timestamp: new Date().toLocaleTimeString('ar-SY', {
                hour: '2-digit',
                minute: '2-digit'
            })
        }
    ]);
    const [newMessage, setNewMessage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [isTyping, setIsTyping] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isOnline, setIsOnline] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const messagesEndRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const quickReplies = [
        'كيف أنشر إعلان؟',
        'ما هي أنواع الحسابات؟',
        'كيف أدفع؟',
        'مشكلة في تسجيل الدخول',
        'كيف أحذف حسابي؟',
        'ما هي الباقات المتاحة؟',
        'كيف أغير معلوماتي؟',
        'مشكلة في الموقع'
    ];
    const scrollToBottom = ()=>{
        messagesEndRef.current?.scrollIntoView({
            behavior: 'smooth'
        });
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        scrollToBottom();
    }, [
        messages
    ]);
    const sendMessage = (text)=>{
        if (!text.trim()) return;
        const userMessage = {
            id: Date.now(),
            text: text.trim(),
            sender: 'user',
            timestamp: new Date().toLocaleTimeString('ar-SY', {
                hour: '2-digit',
                minute: '2-digit'
            })
        };
        setMessages((prev)=>[
                ...prev,
                userMessage
            ]);
        setNewMessage('');
        setIsTyping(true);
        // محاكاة رد الدعم الفني
        setTimeout(()=>{
            setIsTyping(false);
            const supportMessage = {
                id: Date.now() + 1,
                text: getAutoReply(text),
                sender: 'support',
                timestamp: new Date().toLocaleTimeString('ar-SY', {
                    hour: '2-digit',
                    minute: '2-digit'
                })
            };
            setMessages((prev)=>[
                    ...prev,
                    supportMessage
                ]);
        }, 1500);
    };
    const getAutoReply = (userMessage)=>{
        const message = userMessage.toLowerCase();
        // أسئلة حول نشر الإعلانات
        if (message.includes('إعلان') || message.includes('نشر') || message.includes('أضيف')) {
            return '📝 لنشر إعلان:\n1. اضغط على "أضف إعلانك" من القائمة الرئيسية\n2. اختر التصنيف المناسب (عقارات، سيارات، إلخ)\n3. املأ جميع البيانات المطلوبة\n4. ارفع صور واضحة (حتى 10 صور)\n5. اضغط "نشر الإعلان"\n\nملاحظة: الإعلانات المجانية تظهر لمدة 30 يوم.';
        } else if (message.includes('حساب') || message.includes('نوع') || message.includes('فرق')) {
            return '👤 أنواع الحسابات:\n\n🔹 حساب فردي: للأشخاص العاديين\n🔹 حساب تجاري: للشركات والأعمال\n🔹 مكتب عقاري: متخصص للعقارات فقط\n\nكل نوع له باقات مختلفة وميزات خاصة. يمكنك ترقية حسابك في أي وقت من الملف الشخصي.';
        } else if (message.includes('باقة') || message.includes('سعر') || message.includes('تكلفة') || message.includes('مجاني')) {
            return '💰 الباقات المتاحة:\n\n🆓 مجاني: 5 إعلانات شهرياً\n🥈 فضي: 15 إعلان + ميزات إضافية\n🥇 ذهبي: 25 إعلان + أولوية في البحث\n💎 ماسي: إعلانات لا محدودة + جميع الميزات\n\nالأسعار تبدأ من 10$ شهرياً. اطلع على التفاصيل في صفحة الباقات.';
        } else if (message.includes('دفع') || message.includes('فيزا') || message.includes('ماستر') || message.includes('باي بال') || message.includes('فوترة')) {
            return '💳 طرق الدفع المقبولة:\n\n✅ فيزا (Visa)\n✅ ماستركارد (MasterCard)\n✅ باي بال (PayPal)\n✅ كاش آب (Cash App)\n✅ آبل باي (Apple Pay)\n\nجميع المدفوعات آمنة ومشفرة. يمكنك إضافة طريقة دفع من الإعدادات.';
        } else if (message.includes('دخول') || message.includes('تسجيل') || message.includes('لوجين')) {
            return '🔐 مشاكل تسجيل الدخول:\n\n1. تأكد من صحة البريد الإلكتروني\n2. تأكد من كلمة المرور\n3. استخدم "نسيت كلمة المرور" إذا لزم الأمر\n4. تأكد من تفعيل حسابك عبر البريد\n5. امسح ذاكرة التخزين المؤقت\n\nإذا استمرت المشكلة، اتصل بنا مباشرة.';
        } else if (message.includes('كلمة المرور') || message.includes('باسورد') || message.includes('رقم سري')) {
            return '🔑 تغيير كلمة المرور:\n\n1. اذهب إلى الملف الشخصي\n2. اضغط على "إعدادات الحساب"\n3. اختر "تغيير كلمة المرور"\n4. أدخل كلمة المرور الحالية\n5. أدخل كلمة المرور الجديدة\n6. أكد كلمة المرور الجديدة\n\nكلمة المرور يجب أن تحتوي على 8 أحرف على الأقل.';
        } else if (message.includes('حذف') || message.includes('إلغاء') || message.includes('إغلاق')) {
            return '🗑️ حذف الحساب:\n\n⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!\n\n1. اذهب إلى إعدادات الحساب\n2. اضغط على "حذف الحساب"\n3. أدخل كلمة المرور للتأكيد\n4. اكتب "حذف" في المربع\n5. اضغط "تأكيد الحذف"\n\nسيتم حذف جميع بياناتك وإعلاناتك نهائياً.';
        } else if (message.includes('تعديل') || message.includes('تغيير') || message.includes('تحديث')) {
            return '✏️ تعديل المعلومات الشخصية:\n\n1. اذهب إلى "الملف الشخصي"\n2. اضغط على "تعديل"\n3. غيّر المعلومات المطلوبة\n4. اضغط "حفظ التغييرات"\n\nيمكنك تعديل: الاسم، الهاتف، العنوان، الصورة الشخصية، ومعلومات الشركة.';
        } else if (message.includes('صور') || message.includes('صورة') || message.includes('رفع')) {
            return '📸 رفع الصور:\n\n✅ الحد الأقصى: 10 صور لكل إعلان\n✅ الحجم الأقصى: 5 ميجابايت لكل صورة\n✅ الصيغ المقبولة: JPG, PNG, WEBP\n✅ الدقة المنصوح بها: 1200x800 بكسل\n\nنصائح:\n• استخدم صور واضحة وعالية الجودة\n• صوّر من زوايا مختلفة\n• تجنب الصور المكررة';
        } else if (message.includes('بحث') || message.includes('أجد') || message.includes('أبحث')) {
            return '🔍 البحث في الموقع:\n\n1. استخدم شريط البحث في الأعلى\n2. اختر التصنيف المناسب\n3. استخدم الفلاتر المتقدمة:\n   • السعر\n   • الموقع\n   • تاريخ النشر\n   • نوع الإعلان\n\n💡 نصيحة: استخدم كلمات مفتاحية محددة للحصول على نتائج أفضل.';
        } else if (message.includes('تواصل') || message.includes('اتصال') || message.includes('رسالة')) {
            return '📞 التواصل مع المعلنين:\n\n✅ الهاتف: اضغط على رقم الهاتف للاتصال\n✅ واتساب: اضغط على أيقونة واتساب\n✅ رسائل الموقع: استخدم نظام الرسائل الداخلي\n\n⚠️ نصائح الأمان:\n• تواصل عبر الموقع أولاً\n• لا تشارك معلومات مالية\n• قابل في أماكن عامة';
        } else if (message.includes('أمان') || message.includes('احتيال') || message.includes('نصب')) {
            return '🛡️ نصائح الأمان:\n\n❌ لا تدفع مقدماً قبل المعاينة\n❌ لا تشارك معلومات بنكية\n❌ احذر من العروض المشبوهة\n✅ قابل في أماكن عامة\n✅ تأكد من هوية البائع\n✅ استخدم طرق دفع آمنة\n\n📞 للإبلاغ عن احتيال: +963988652401';
        } else if (message.includes('تطبيق') || message.includes('موبايل') || message.includes('هاتف')) {
            return '📱 تطبيق من المالك:\n\n🔜 قريباً على:\n• متجر آبل (App Store)\n• متجر جوجل (Google Play)\n\nحالياً يمكنك استخدام الموقع من المتصفح على الهاتف. التطبيق سيوفر:\n• إشعارات فورية\n• سرعة أكبر\n• سهولة في الاستخدام';
        } else if (message.includes('مشكلة') || message.includes('خطأ') || message.includes('لا يعمل')) {
            return '🔧 حل المشاكل التقنية:\n\n1. أعد تحديث الصفحة (F5)\n2. امسح ذاكرة التخزين المؤقت\n3. جرب متصفح آخر\n4. تأكد من اتصال الإنترنت\n5. أعد تشغيل المتصفح\n\nإذا استمرت المشكلة:\n📞 اتصل بنا: +963988652401\n📧 أو راسلنا عبر النموذج';
        } else if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {
            return '👋 أهلاً وسهلاً بك في موقع من المالك!\n\nأنا المساعد الذكي، يمكنني مساعدتك في:\n• نشر الإعلانات\n• إدارة الحساب\n• حل المشاكل التقنية\n• الإجابة على استفساراتك\n\nكيف يمكنني مساعدتك اليوم؟ 😊';
        } else {
            return '🤖 شكراً لتواصلك معنا!\n\nلم أتمكن من فهم استفسارك بالضبط. يمكنك:\n\n1. إعادة صياغة السؤال\n2. اختيار من المواضيع الشائعة أدناه\n3. التواصل مع فريق الدعم مباشرة\n\n📞 للدعم الفوري: +963988652401\n\nسأكون سعيداً لمساعدتك! 😊';
        }
    };
    const handleKeyPress = (e)=>{
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage(newMessage);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            !isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: ()=>setIsOpen(true),
                className: "fixed bottom-6 left-6 w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-50 flex items-center justify-center group hover:scale-110",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-8 h-8 bg-white/90 rounded-lg flex items-center justify-center relative animate-pulse",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex gap-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-1.5 h-1.5 bg-blue-600 rounded-full animate-ping"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/LiveChat.tsx",
                                                lineNumber: 179,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-1.5 h-1.5 bg-blue-600 rounded-full animate-ping",
                                                style: {
                                                    animationDelay: '0.5s'
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/LiveChat.tsx",
                                                lineNumber: 180,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/LiveChat.tsx",
                                        lineNumber: 178,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "absolute bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-0.5 bg-blue-600 rounded-full"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/LiveChat.tsx",
                                        lineNumber: 184,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/LiveChat.tsx",
                                lineNumber: 176,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute -left-1 top-1 w-2 h-2 bg-gray-300 rounded-full border border-gray-400"
                            }, void 0, false, {
                                fileName: "[project]/src/components/LiveChat.tsx",
                                lineNumber: 188,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute -right-1 top-1 w-2 h-2 bg-gray-300 rounded-full border border-gray-400"
                            }, void 0, false, {
                                fileName: "[project]/src/components/LiveChat.tsx",
                                lineNumber: 189,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute -left-2 top-0 w-1 h-1 bg-green-400 rounded-full animate-bounce opacity-70"
                            }, void 0, false, {
                                fileName: "[project]/src/components/LiveChat.tsx",
                                lineNumber: 192,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute -right-2 top-0 w-1 h-1 bg-green-400 rounded-full animate-bounce opacity-70",
                                style: {
                                    animationDelay: '0.3s'
                                }
                            }, void 0, false, {
                                fileName: "[project]/src/components/LiveChat.tsx",
                                lineNumber: 193,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/LiveChat.tsx",
                        lineNumber: 174,
                        columnNumber: 11
                    }, this),
                    isOnline && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"
                    }, void 0, false, {
                        fileName: "[project]/src/components/LiveChat.tsx",
                        lineNumber: 198,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/LiveChat.tsx",
                lineNumber: 169,
                columnNumber: 9
            }, this),
            isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed bottom-6 left-6 w-80 h-96 bg-white rounded-xl shadow-2xl z-50 flex flex-col overflow-hidden border border-gray-200",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-primary-600 text-white p-4 flex items-center justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-8 h-8 bg-white/20 rounded-full flex items-center justify-center relative",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-5 h-5 bg-white/90 rounded flex items-center justify-center relative",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex gap-0.5",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "w-1 h-1 bg-blue-600 rounded-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/LiveChat.tsx",
                                                                lineNumber: 213,
                                                                columnNumber: 21
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "w-1 h-1 bg-blue-600 rounded-full"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/LiveChat.tsx",
                                                                lineNumber: 214,
                                                                columnNumber: 21
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/LiveChat.tsx",
                                                        lineNumber: 212,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "absolute bottom-0.5 left-1/2 transform -translate-x-1/2 w-1 h-0.5 bg-blue-600 rounded-full"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/LiveChat.tsx",
                                                        lineNumber: 216,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/LiveChat.tsx",
                                                lineNumber: 211,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute -left-0.5 top-1 w-1 h-1 bg-gray-300 rounded-full"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/LiveChat.tsx",
                                                lineNumber: 219,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "absolute -right-0.5 top-1 w-1 h-1 bg-gray-300 rounded-full"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/LiveChat.tsx",
                                                lineNumber: 220,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/LiveChat.tsx",
                                        lineNumber: 209,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "font-semibold",
                                                children: "المساعد الذكي"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/LiveChat.tsx",
                                                lineNumber: 223,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center gap-2 text-xs",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `w-2 h-2 rounded-full ${isOnline ? 'bg-green-400' : 'bg-gray-400'}`
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/LiveChat.tsx",
                                                        lineNumber: 225,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        children: isOnline ? 'متصل الآن' : 'غير متصل'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/LiveChat.tsx",
                                                        lineNumber: 226,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/components/LiveChat.tsx",
                                                lineNumber: 224,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/LiveChat.tsx",
                                        lineNumber: 222,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/LiveChat.tsx",
                                lineNumber: 208,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: ()=>setIsOpen(false),
                                className: "text-white/80 hover:text-white text-xl",
                                children: "×"
                            }, void 0, false, {
                                fileName: "[project]/src/components/LiveChat.tsx",
                                lineNumber: 230,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/LiveChat.tsx",
                        lineNumber: 207,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1 overflow-y-auto p-4 space-y-4",
                        children: [
                            messages.map((message)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: `flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: `max-w-xs px-4 py-2 rounded-lg ${message.sender === 'user' ? 'bg-primary-600 text-white' : 'bg-gray-100 text-gray-800'}`,
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm",
                                                children: message.text
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/LiveChat.tsx",
                                                lineNumber: 252,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: `text-xs ${message.sender === 'user' ? 'text-primary-200' : 'text-gray-500'} block mt-1`,
                                                children: message.timestamp
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/LiveChat.tsx",
                                                lineNumber: 253,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/LiveChat.tsx",
                                        lineNumber: 245,
                                        columnNumber: 17
                                    }, this)
                                }, message.id, false, {
                                    fileName: "[project]/src/components/LiveChat.tsx",
                                    lineNumber: 241,
                                    columnNumber: 15
                                }, this)),
                            isTyping && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-start",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-gray-100 px-4 py-2 rounded-lg",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex gap-1",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/LiveChat.tsx",
                                                lineNumber: 267,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-2 h-2 bg-gray-400 rounded-full animate-bounce",
                                                style: {
                                                    animationDelay: '0.1s'
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/LiveChat.tsx",
                                                lineNumber: 268,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "w-2 h-2 bg-gray-400 rounded-full animate-bounce",
                                                style: {
                                                    animationDelay: '0.2s'
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/LiveChat.tsx",
                                                lineNumber: 269,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/LiveChat.tsx",
                                        lineNumber: 266,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/LiveChat.tsx",
                                    lineNumber: 265,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/LiveChat.tsx",
                                lineNumber: 264,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                ref: messagesEndRef
                            }, void 0, false, {
                                fileName: "[project]/src/components/LiveChat.tsx",
                                lineNumber: 274,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/LiveChat.tsx",
                        lineNumber: 239,
                        columnNumber: 11
                    }, this),
                    messages.length === 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-3 border-t border-gray-200",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-gray-600 mb-2",
                                children: "مواضيع شائعة:"
                            }, void 0, false, {
                                fileName: "[project]/src/components/LiveChat.tsx",
                                lineNumber: 280,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap gap-1",
                                children: quickReplies.slice(0, 3).map((reply, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>sendMessage(reply),
                                        className: "text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full hover:bg-gray-200 transition-colors",
                                        children: reply
                                    }, index, false, {
                                        fileName: "[project]/src/components/LiveChat.tsx",
                                        lineNumber: 283,
                                        columnNumber: 19
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/components/LiveChat.tsx",
                                lineNumber: 281,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/LiveChat.tsx",
                        lineNumber: 279,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-4 border-t border-gray-200",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex gap-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                        type: "text",
                                        value: newMessage,
                                        onChange: (e)=>setNewMessage(e.target.value),
                                        onKeyPress: handleKeyPress,
                                        placeholder: "اكتب رسالتك...",
                                        className: "flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/LiveChat.tsx",
                                        lineNumber: 298,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>sendMessage(newMessage),
                                        disabled: !newMessage.trim(),
                                        className: "bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-sm",
                                            children: "📤"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/LiveChat.tsx",
                                            lineNumber: 311,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/LiveChat.tsx",
                                        lineNumber: 306,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/LiveChat.tsx",
                                lineNumber: 297,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-xs text-gray-500 mt-2 text-center",
                                children: "عادة ما نرد خلال دقائق قليلة"
                            }, void 0, false, {
                                fileName: "[project]/src/components/LiveChat.tsx",
                                lineNumber: 314,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/LiveChat.tsx",
                        lineNumber: 296,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/LiveChat.tsx",
                lineNumber: 205,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true);
};
const __TURBOPACK__default__export__ = LiveChat;
}}),
"[project]/src/components/ClientSideProtection.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>ClientSideProtection)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
function ClientSideProtection() {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // منع إضافات المتصفح من التدخل
        const preventExtensions = ()=>{
            // منع Grammarly
            window.grammarly = false;
            window.grammarlyExtension = false;
            // إزالة خصائص الإضافات
            const elements = document.querySelectorAll('*');
            elements.forEach((el)=>{
                // إزالة خصائص Grammarly
                if (el.hasAttribute('data-new-gr-c-s-check-loaded')) {
                    el.removeAttribute('data-new-gr-c-s-check-loaded');
                }
                if (el.hasAttribute('data-gr-ext-installed')) {
                    el.removeAttribute('data-gr-ext-installed');
                }
                // منع تحرير النصوص
                if (el.contentEditable === 'true' && !el.matches('input, textarea, [data-editable="true"]')) {
                    el.contentEditable = 'false';
                }
                // إزالة خاصية user-modify
                const htmlEl = el;
                if (htmlEl.style) {
                    htmlEl.style.webkitUserModify = 'read-only';
                    htmlEl.style.userModify = 'read-only';
                }
            });
        };
        // تشغيل فوري
        preventExtensions();
        // مراقبة مستمرة للتغييرات
        const observer = new MutationObserver(preventExtensions);
        observer.observe(document.body, {
            attributes: true,
            subtree: true,
            childList: true,
            attributeFilter: [
                'data-new-gr-c-s-check-loaded',
                'data-gr-ext-installed',
                'contenteditable'
            ]
        });
        // تنظيف عند إلغاء التحميل
        return ()=>{
            observer.disconnect();
        };
    }, []);
    return null; // هذا المكون لا يعرض أي شيء
}
}}),
"[project]/src/components/ScrollIndicators.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>ScrollIndicators)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
function ScrollIndicators({ className = '' }) {
    // حالات التمرير الذكي
    const [showScrollToTop, setShowScrollToTop] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showScrollToBottom, setShowScrollToBottom] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [lastScrollY, setLastScrollY] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [isScrolling, setIsScrolling] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isMobileDevice, setIsMobileDevice] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isTabletDevice, setIsTabletDevice] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [scrollProgress, setScrollProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [scrollDirection, setScrollDirection] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isAtTop, setIsAtTop] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [isAtBottom, setIsAtBottom] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showScrollIndicator, setShowScrollIndicator] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [scrollVelocity, setScrollVelocity] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [isClient, setIsClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // إصلاح مشكلة Hydration
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setIsClient(true);
        // تحديد نوع الجهاز
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    }, []);
    // مراقبة التمرير لإظهار/إخفاء أزرار التنقل
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
        const handleScroll = undefined;
        // إضافة مستمع التمرير مع throttling
        let ticking;
        const throttledHandleScroll = undefined;
    }, [
        isClient,
        lastScrollY
    ]);
    // دالة للتمرير إلى الأعلى
    const scrollToTop = ()=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    };
    // دالة للتمرير إلى الأسفل
    const scrollToBottom = ()=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
    };
    // دالة للتمرير إلى الخطوة التالية
    const scrollToNextSection = ()=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
        const currentScrollY = undefined;
        const windowHeight = undefined;
        const nextScrollPosition = undefined; // تمرير 70% من ارتفاع الشاشة
    };
    // دالة للتمرير إلى الخطوة السابقة
    const scrollToPrevSection = ()=>{
        if ("TURBOPACK compile-time truthy", 1) return;
        "TURBOPACK unreachable";
        const currentScrollY = undefined;
        const windowHeight = undefined;
        const prevScrollPosition = undefined; // تمرير 70% من ارتفاع الشاشة للخلف
    };
    if (!isClient) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed top-0 left-0 w-full h-0.5 bg-gray-200/30 z-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "h-full bg-gradient-to-r from-primary-500/70 to-primary-600/70 transition-all duration-300 ease-out shadow-sm",
                    style: {
                        width: `${scrollProgress}%`
                    }
                }, void 0, false, {
                    fileName: "[project]/src/components/ScrollIndicators.tsx",
                    lineNumber: 184,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ScrollIndicators.tsx",
                lineNumber: 183,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    showScrollToTop && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: scrollToTop,
                        className: `fixed bottom-12 right-3 z-50 bg-gradient-to-r from-primary-600/60 to-primary-700/60 hover:from-primary-700/80 hover:to-primary-800/80 text-white p-2 rounded-full shadow-md transition-all duration-300 transform ${isScrolling ? 'scale-105 rotate-1' : 'scale-100'} hover:scale-105 hover:-translate-y-0.5 focus:outline-none focus:ring-1 focus:ring-primary-300 group backdrop-blur-sm`,
                        style: {
                            opacity: showScrollToTop ? 0.7 : 0,
                            visibility: showScrollToTop ? 'visible' : 'hidden'
                        },
                        "aria-label": "العودة للأعلى",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                className: "w-3.5 h-3.5 group-hover:animate-bounce",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2.5,
                                    d: "M5 10l7-7m0 0l7 7m-7-7v18"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ScrollIndicators.tsx",
                                    lineNumber: 206,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ScrollIndicators.tsx",
                                lineNumber: 205,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ScrollIndicators.tsx",
                                lineNumber: 208,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ScrollIndicators.tsx",
                        lineNumber: 194,
                        columnNumber: 11
                    }, this),
                    showScrollToBottom && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: scrollToBottom,
                        className: `fixed bottom-20 right-3 z-50 bg-gradient-to-r from-gray-600/60 to-gray-700/60 hover:from-gray-700/80 hover:to-gray-800/80 text-white p-2 rounded-full shadow-md transition-all duration-300 transform ${isScrolling ? 'scale-105 -rotate-1' : 'scale-100'} hover:scale-105 hover:translate-y-0.5 focus:outline-none focus:ring-1 focus:ring-gray-300 group backdrop-blur-sm`,
                        style: {
                            opacity: showScrollToBottom ? 0.7 : 0,
                            visibility: showScrollToBottom ? 'visible' : 'hidden'
                        },
                        "aria-label": "التمرير للأسفل",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                className: "w-3.5 h-3.5 group-hover:animate-bounce",
                                fill: "none",
                                stroke: "currentColor",
                                viewBox: "0 0 24 24",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    strokeLinecap: "round",
                                    strokeLinejoin: "round",
                                    strokeWidth: 2.5,
                                    d: "M19 14l-7 7m0 0l-7-7m7 7V3"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ScrollIndicators.tsx",
                                    lineNumber: 226,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ScrollIndicators.tsx",
                                lineNumber: 225,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse"
                            }, void 0, false, {
                                fileName: "[project]/src/components/ScrollIndicators.tsx",
                                lineNumber: 228,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ScrollIndicators.tsx",
                        lineNumber: 214,
                        columnNumber: 11
                    }, this),
                    (isMobileDevice || isTabletDevice) && (showScrollToTop || showScrollToBottom) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "fixed bottom-3 left-3 z-50 flex flex-col gap-1.5",
                        style: {
                            opacity: 0.6
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: scrollToPrevSection,
                                className: "bg-gradient-to-r from-blue-500/70 to-blue-600/70 hover:from-blue-600/90 hover:to-blue-700/90 text-white p-1.5 rounded-full shadow-sm transition-all duration-300 hover:scale-105 hover:-translate-y-0.5 focus:outline-none focus:ring-1 focus:ring-blue-300 group backdrop-blur-sm",
                                "aria-label": "التمرير للأعلى قليلاً",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    className: "w-2.5 h-2.5 group-hover:animate-pulse",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2.5,
                                        d: "M7 11l5-5m0 0l5 5m-5-5v12"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ScrollIndicators.tsx",
                                        lineNumber: 242,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ScrollIndicators.tsx",
                                    lineNumber: 241,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ScrollIndicators.tsx",
                                lineNumber: 236,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: scrollToNextSection,
                                className: "bg-gradient-to-r from-green-500/70 to-green-600/70 hover:from-green-600/90 hover:to-green-700/90 text-white p-1.5 rounded-full shadow-sm transition-all duration-300 hover:scale-105 hover:translate-y-0.5 focus:outline-none focus:ring-1 focus:ring-green-300 group backdrop-blur-sm",
                                "aria-label": "التمرير للأسفل قليلاً",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    className: "w-2.5 h-2.5 group-hover:animate-pulse",
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2.5,
                                        d: "M17 13l-5 5m0 0l-5-5m5 5V6"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ScrollIndicators.tsx",
                                        lineNumber: 253,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ScrollIndicators.tsx",
                                    lineNumber: 252,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ScrollIndicators.tsx",
                                lineNumber: 247,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ScrollIndicators.tsx",
                        lineNumber: 234,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true),
            showScrollIndicator && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    isAtTop && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "fixed top-12 left-1/2 transform -translate-x-1/2 z-50",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-gradient-to-r from-blue-500 to-purple-600 text-white px-3 py-1.5 rounded-full shadow-md animate-bounce text-xs",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-1.5",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-2.5 h-2.5 animate-pulse",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 3,
                                            d: "M5 15l7-7 7 7"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ScrollIndicators.tsx",
                                            lineNumber: 269,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ScrollIndicators.tsx",
                                        lineNumber: 268,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: "أعلى الصفحة"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ScrollIndicators.tsx",
                                        lineNumber: 271,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-2.5 h-2.5 animate-pulse",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 3,
                                            d: "M5 15l7-7 7 7"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ScrollIndicators.tsx",
                                            lineNumber: 273,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ScrollIndicators.tsx",
                                        lineNumber: 272,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ScrollIndicators.tsx",
                                lineNumber: 267,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ScrollIndicators.tsx",
                            lineNumber: 266,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ScrollIndicators.tsx",
                        lineNumber: 265,
                        columnNumber: 13
                    }, this),
                    isAtBottom && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "fixed bottom-12 left-1/2 transform -translate-x-1/2 z-50",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-gradient-to-r from-green-500 to-emerald-600 text-white px-3 py-1.5 rounded-full shadow-md animate-bounce text-xs",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center gap-1.5",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-2.5 h-2.5 animate-pulse",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 3,
                                            d: "M19 9l-7 7-7-7"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ScrollIndicators.tsx",
                                            lineNumber: 286,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ScrollIndicators.tsx",
                                        lineNumber: 285,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: "آخر الصفحة"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ScrollIndicators.tsx",
                                        lineNumber: 288,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-2.5 h-2.5 animate-pulse",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 3,
                                            d: "M19 9l-7 7-7-7"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/ScrollIndicators.tsx",
                                            lineNumber: 290,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ScrollIndicators.tsx",
                                        lineNumber: 289,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ScrollIndicators.tsx",
                                lineNumber: 284,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/ScrollIndicators.tsx",
                            lineNumber: 283,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ScrollIndicators.tsx",
                        lineNumber: 282,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true),
            scrollDirection && isScrolling && !isMobileDevice && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `fixed right-1.5 top-1/2 transform -translate-y-1/2 z-40 transition-all duration-300 ${scrollDirection === 'down' ? 'animate-pulse' : 'animate-bounce'}`,
                style: {
                    opacity: 0.5
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: `w-5 h-5 rounded-full flex items-center justify-center shadow-sm backdrop-blur-sm ${scrollDirection === 'down' ? 'bg-gradient-to-b from-orange-400/70 to-red-500/70 text-white' : 'bg-gradient-to-t from-blue-400/70 to-purple-500/70 text-white'}`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        className: "w-2.5 h-2.5",
                        fill: "none",
                        stroke: "currentColor",
                        viewBox: "0 0 24 24",
                        children: scrollDirection === 'down' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 3,
                            d: "M19 14l-7 7m0 0l-7-7m7 7V3"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ScrollIndicators.tsx",
                            lineNumber: 311,
                            columnNumber: 17
                        }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 3,
                            d: "M5 10l7-7m0 0l7 7m-7-7v18"
                        }, void 0, false, {
                            fileName: "[project]/src/components/ScrollIndicators.tsx",
                            lineNumber: 313,
                            columnNumber: 17
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ScrollIndicators.tsx",
                        lineNumber: 309,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/ScrollIndicators.tsx",
                    lineNumber: 304,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ScrollIndicators.tsx",
                lineNumber: 301,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true);
}
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/components/FloatingActionButton.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>AssistiveTouch)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/styled-jsx/style.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function AssistiveTouch({ onFilterClick }) {
    const [isOpen, setIsOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [position, setPosition] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        x: 20,
        y: 100
    });
    const [isDragging, setIsDragging] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [dragStart, setDragStart] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        x: 0,
        y: 0
    });
    const buttonRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleClickOutside = (event)=>{
            if (buttonRef.current && !buttonRef.current.contains(event.target)) {
                setIsOpen(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return ()=>document.removeEventListener('mousedown', handleClickOutside);
    }, []);
    const handleStart = (clientX, clientY)=>{
        if (isOpen) return;
        setIsDragging(true);
        setDragStart({
            x: clientX - position.x,
            y: clientY - position.y
        });
    };
    const handleMouseDown = (e)=>handleStart(e.clientX, e.clientY);
    const handleTouchStart = (e)=>{
        const touch = e.touches[0];
        handleStart(touch.clientX, touch.clientY);
    };
    const handleMove = (clientX, clientY)=>{
        if (!isDragging) return;
        const newX = clientX - dragStart.x;
        const newY = clientY - dragStart.y;
        const radius = 110; // نصف قطر الدائرة
        const buttonSize = 64; // حجم الزر الرئيسي
        const maxX = window.innerWidth - radius - buttonSize / 2;
        const minX = radius - buttonSize / 2;
        const maxY = window.innerHeight - radius - buttonSize / 2;
        const minY = radius - buttonSize / 2;
        setPosition({
            x: Math.max(minX, Math.min(newX, maxX)),
            y: Math.max(minY, Math.min(newY, maxY))
        });
    };
    const handleMouseMove = (e)=>handleMove(e.clientX, e.clientY);
    const handleTouchMove = (e)=>{
        e.preventDefault();
        const touch = e.touches[0];
        handleMove(touch.clientX, touch.clientY);
    };
    const handleEnd = ()=>setIsDragging(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isDragging) {
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleEnd);
            document.addEventListener('touchmove', handleTouchMove, {
                passive: false
            });
            document.addEventListener('touchend', handleEnd);
        }
        return ()=>{
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleEnd);
            document.removeEventListener('touchmove', handleTouchMove);
            document.removeEventListener('touchend', handleEnd);
        };
    }, [
        isDragging,
        dragStart
    ]);
    const handleGoBack = ()=>{
        window.history.back();
        setIsOpen(false);
    };
    const handleGoHome = ()=>{
        router.push('/');
        setIsOpen(false);
    };
    const handleMyAds = ()=>{
        router.push('/dashboard');
        setIsOpen(false);
    };
    const handleJobs = ()=>{
        router.push('/jobs/all');
        setIsOpen(false);
    };
    const handleClick = (e)=>{
        e.stopPropagation();
        if (!isDragging) setIsOpen(!isOpen);
    };
    const menuItems = [
        {
            icon: '/images/AssistiveTouch/الصفحة الرئيسية.png',
            label: 'الرئيسية',
            action: handleGoHome
        },
        {
            icon: '/images/AssistiveTouch/رجوع.png',
            label: 'رجوع',
            action: handleGoBack
        },
        {
            icon: '/images/AssistiveTouch/إعلاناتي.png',
            label: 'إعلاناتي',
            action: handleMyAds
        },
        {
            icon: '/images/AssistiveTouch/أيقونة الوظائف.png',
            label: 'الوظائف',
            action: handleJobs
        }
    ];
    const containerSize = 320; // w-80 = 320px
    const radius = 110;
    const buttonSize = 64;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: buttonRef,
        style: {
            left: `${position.x}px`,
            top: `${position.y}px`,
            cursor: isDragging ? 'grabbing' : 'grab',
            display: 'block',
            visibility: 'visible'
        },
        className: "jsx-95604b858376a405" + " " + "fixed z-50 transition-all duration-300 block",
        children: [
            isOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "jsx-95604b858376a405" + " " + "relative w-80 h-80",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 50%, rgba(0,0,0,0.1) 100%)',
                            border: '1px solid rgba(255, 255, 255, 0.2)',
                            boxShadow: '0 8px 32px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.1)'
                        },
                        className: "jsx-95604b858376a405" + " " + "absolute inset-0 rounded-full backdrop-blur-lg"
                    }, void 0, false, {
                        fileName: "[project]/src/components/FloatingActionButton.tsx",
                        lineNumber: 122,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-95604b858376a405" + " " + "absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                            src: "/images/AssistiveTouch/شعار موقع من المالك.png",
                            alt: "شعار الموقع",
                            className: "jsx-95604b858376a405" + " " + "w-full h-full object-contain"
                        }, void 0, false, {
                            fileName: "[project]/src/components/FloatingActionButton.tsx",
                            lineNumber: 134,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/FloatingActionButton.tsx",
                        lineNumber: 133,
                        columnNumber: 11
                    }, this),
                    menuItems.map((item, index)=>{
                        let x = containerSize / 2 - buttonSize / 2;
                        let y = containerSize / 2 - buttonSize / 2;
                        switch(item.label){
                            case 'الرئيسية':
                                x += radius;
                                break; // يمين
                            case 'رجوع':
                                x -= radius;
                                break; // يسار
                            case 'الوظائف':
                                y += radius;
                                break; // تحت
                            case 'إعلاناتي':
                                y -= radius;
                                break; // فوق
                        }
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                left: `${x}px`,
                                top: `${y}px`,
                                animation: `fadeInScale 0.4s ease-out ${index * 150}ms forwards`
                            },
                            className: "jsx-95604b858376a405" + " " + "absolute flex flex-col items-center opacity-0",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: item.action,
                                    style: {
                                        background: 'linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.1) 100%)',
                                        border: '1px solid rgba(255,255,255,0.3)',
                                        boxShadow: '0 4px 16px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.2)',
                                        backdropFilter: 'blur(10px)'
                                    },
                                    className: "jsx-95604b858376a405" + " " + "w-16 h-16 rounded-full flex items-center justify-center hover:scale-110 transition-all duration-200 p-2",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                        src: item.icon,
                                        alt: item.label,
                                        className: "jsx-95604b858376a405" + " " + "w-full h-full object-contain"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/FloatingActionButton.tsx",
                                        lineNumber: 173,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FloatingActionButton.tsx",
                                    lineNumber: 163,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "jsx-95604b858376a405" + " " + "text-white text-xs font-medium text-center mt-1 drop-shadow-lg",
                                    children: item.label
                                }, void 0, false, {
                                    fileName: "[project]/src/components/FloatingActionButton.tsx",
                                    lineNumber: 175,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, index, true, {
                            fileName: "[project]/src/components/FloatingActionButton.tsx",
                            lineNumber: 154,
                            columnNumber: 15
                        }, this);
                    })
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/FloatingActionButton.tsx",
                lineNumber: 120,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                onMouseDown: handleMouseDown,
                onTouchStart: handleTouchStart,
                onClick: handleClick,
                style: {
                    border: '2px solid rgba(255, 255, 255, 0.3)',
                    boxShadow: isOpen ? '0 12px 40px rgba(0, 0, 0, 0.5), 0 0 20px rgba(255, 255, 255, 0.1)' : '0 8px 32px rgba(0, 0, 0, 0.3)',
                    userSelect: 'none',
                    touchAction: 'none'
                },
                className: "jsx-95604b858376a405" + " " + `w-16 h-16 bg-black bg-opacity-70 backdrop-blur-md rounded-2xl flex items-center justify-center transition-all duration-300 hover:scale-105 active:scale-95 ${isOpen ? 'scale-110 bg-opacity-80' : 'scale-100'}`,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                    src: "/images/AssistiveTouch/AssistiveTouch.png",
                    alt: "Assistive Touch",
                    style: {
                        filter: isOpen ? 'brightness(1.2)' : 'brightness(1)'
                    },
                    className: "jsx-95604b858376a405" + " " + "w-10 h-10"
                }, void 0, false, {
                    fileName: "[project]/src/components/FloatingActionButton.tsx",
                    lineNumber: 199,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/FloatingActionButton.tsx",
                lineNumber: 183,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                id: "95604b858376a405",
                children: "@keyframes fadeInScale{0%{opacity:0;transform:scale(.5)}to{opacity:1;transform:scale(1)}}"
            }, void 0, false, void 0, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/FloatingActionButton.tsx",
        lineNumber: 108,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/NotificationSystem.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "NotificationItem": (()=>NotificationItem),
    "NotificationProvider": (()=>NotificationProvider),
    "useNotifications": (()=>useNotifications)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const NotificationContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useNotifications = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(NotificationContext);
    if (!context) {
        throw new Error('useNotifications must be used within a NotificationProvider');
    }
    return context;
};
const NotificationProvider = ({ children })=>{
    const [notifications, setNotifications] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isClient, setIsClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // تحديد أننا في العميل
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setIsClient(true);
    }, []);
    // تحميل الإشعارات من localStorage عند بدء التطبيق
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isClient) return;
        const savedNotifications = localStorage.getItem('notifications');
        if (savedNotifications) {
            try {
                const parsed = JSON.parse(savedNotifications);
                // التأكد من أن parsed هو مصفوفة
                if (Array.isArray(parsed)) {
                    setNotifications(parsed.map((n)=>({
                            ...n,
                            timestamp: new Date(n.timestamp)
                        })));
                } else {
                    // إذا لم يكن مصفوفة، قم بمسح البيانات المعطوبة
                    localStorage.removeItem('notifications');
                    setNotifications([]);
                }
            } catch (error) {
                console.error('Error loading notifications:', error);
                // مسح البيانات المعطوبة
                localStorage.removeItem('notifications');
                setNotifications([]);
            }
        }
    }, [
        isClient
    ]);
    // حفظ الإشعارات في localStorage عند تغييرها
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isClient) return;
        localStorage.setItem('notifications', JSON.stringify(notifications));
    }, [
        notifications,
        isClient
    ]);
    const addNotification = (notification)=>{
        const newNotification = {
            ...notification,
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            timestamp: new Date(),
            read: false
        };
        setNotifications((prev)=>[
                newNotification,
                ...prev
            ]);
        // إزالة الإشعارات القديمة (أكثر من 50 إشعار)
        setNotifications((prev)=>prev.slice(0, 50));
    };
    const markAsRead = (id)=>{
        setNotifications((prev)=>prev.map((notification)=>notification.id === id ? {
                    ...notification,
                    read: true
                } : notification));
    };
    const markAllAsRead = ()=>{
        setNotifications((prev)=>prev.map((notification)=>({
                    ...notification,
                    read: true
                })));
    };
    const removeNotification = (id)=>{
        setNotifications((prev)=>prev.filter((notification)=>notification.id !== id));
    };
    const clearAll = ()=>{
        setNotifications([]);
    };
    const unreadCount = notifications.filter((n)=>!n.read).length;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(NotificationContext.Provider, {
        value: {
            notifications,
            unreadCount,
            addNotification,
            markAsRead,
            markAllAsRead,
            removeNotification,
            clearAll
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/NotificationSystem.tsx",
        lineNumber: 124,
        columnNumber: 5
    }, this);
};
const NotificationItem = ({ notification, onMarkAsRead, onRemove })=>{
    const getTypeStyles = ()=>{
        switch(notification.type){
            case 'success':
                return 'bg-gradient-to-br from-green-50/80 via-green-100/60 to-green-50/80 backdrop-blur-sm border border-green-200/50 text-green-800 shadow-lg';
            case 'error':
                return 'bg-gradient-to-br from-red-50/80 via-red-100/60 to-red-50/80 backdrop-blur-sm border border-red-200/50 text-red-800 shadow-lg';
            case 'warning':
                return 'bg-gradient-to-br from-yellow-50/80 via-yellow-100/60 to-yellow-50/80 backdrop-blur-sm border border-yellow-200/50 text-yellow-800 shadow-lg';
            case 'info':
                return 'bg-gradient-to-br from-blue-50/80 via-blue-100/60 to-blue-50/80 backdrop-blur-sm border border-blue-200/50 text-blue-800 shadow-lg';
            default:
                return 'bg-gradient-to-br from-gray-50/80 via-gray-100/60 to-gray-50/80 backdrop-blur-sm border border-gray-200/50 text-gray-800 shadow-lg';
        }
    };
    const getTypeIcon = ()=>{
        if (notification.icon) return notification.icon;
        switch(notification.type){
            case 'success':
                return '✅';
            case 'error':
                return '❌';
            case 'warning':
                return '⚠️';
            case 'info':
                return 'ℹ️';
            default:
                return '📢';
        }
    };
    const getIconGlow = ()=>{
        switch(notification.type){
            case 'success':
                return 'drop-shadow-[0_0_8px_rgba(34,197,94,0.6)]';
            case 'error':
                return 'drop-shadow-[0_0_8px_rgba(239,68,68,0.6)]';
            case 'warning':
                return 'drop-shadow-[0_0_8px_rgba(245,158,11,0.6)]';
            case 'info':
                return 'drop-shadow-[0_0_8px_rgba(59,130,246,0.6)]';
            default:
                return 'drop-shadow-[0_0_8px_rgba(107,114,128,0.6)]';
        }
    };
    const formatTime = (date)=>{
        const now = new Date();
        const diff = now.getTime() - date.getTime();
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        if (minutes < 1) return 'الآن';
        if (minutes < 60) return `منذ ${minutes} دقيقة`;
        if (hours < 24) return `منذ ${hours} ساعة`;
        if (days < 7) return `منذ ${days} يوم`;
        return date.toLocaleDateString('ar-SA');
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `p-4 rounded-xl transition-all duration-300 hover:scale-[1.02] ${getTypeStyles()} ${!notification.read ? 'ring-2 ring-primary-300/50 shadow-xl' : 'shadow-md'}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-start gap-3",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: `text-2xl flex-shrink-0 ${getIconGlow()} opacity-90`,
                    children: getTypeIcon()
                }, void 0, false, {
                    fileName: "[project]/src/components/NotificationSystem.tsx",
                    lineNumber: 216,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-1 min-w-0",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-start justify-between gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: "font-semibold text-sm",
                                    children: notification.title
                                }, void 0, false, {
                                    fileName: "[project]/src/components/NotificationSystem.tsx",
                                    lineNumber: 220,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-2 flex-shrink-0",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-xs opacity-70",
                                            children: formatTime(notification.timestamp)
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/NotificationSystem.tsx",
                                            lineNumber: 222,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>onRemove(notification.id),
                                            className: "text-gray-400 hover:text-gray-600 transition-colors",
                                            title: "حذف الإشعار",
                                            children: "×"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/NotificationSystem.tsx",
                                            lineNumber: 223,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/NotificationSystem.tsx",
                                    lineNumber: 221,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/NotificationSystem.tsx",
                            lineNumber: 219,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm mt-1 opacity-90",
                            children: notification.message
                        }, void 0, false, {
                            fileName: "[project]/src/components/NotificationSystem.tsx",
                            lineNumber: 233,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center gap-3 mt-3",
                            children: [
                                !notification.read && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>onMarkAsRead(notification.id),
                                    className: "text-xs bg-white/70 backdrop-blur-sm hover:bg-white/90 px-3 py-1.5 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md border border-white/30",
                                    children: "تم القراءة"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/NotificationSystem.tsx",
                                    lineNumber: 237,
                                    columnNumber: 15
                                }, this),
                                notification.actionUrl && notification.actionText && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                    href: notification.actionUrl,
                                    className: "text-xs bg-white/70 backdrop-blur-sm hover:bg-white/90 px-3 py-1.5 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md border border-white/30",
                                    children: notification.actionText
                                }, void 0, false, {
                                    fileName: "[project]/src/components/NotificationSystem.tsx",
                                    lineNumber: 246,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/NotificationSystem.tsx",
                            lineNumber: 235,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/NotificationSystem.tsx",
                    lineNumber: 218,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/NotificationSystem.tsx",
            lineNumber: 215,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/NotificationSystem.tsx",
        lineNumber: 210,
        columnNumber: 5
    }, this);
};
}}),
"[project]/src/components/AdvancedNotificationSystem.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AdvancedNotificationProvider": (()=>AdvancedNotificationProvider),
    "createAdPostedNotification": (()=>createAdPostedNotification),
    "createErrorNotification": (()=>createErrorNotification),
    "createFavoriteNotification": (()=>createFavoriteNotification),
    "createLogoutNotification": (()=>createLogoutNotification),
    "createMessageNotification": (()=>createMessageNotification),
    "createPaymentSuccessNotification": (()=>createPaymentSuccessNotification),
    "createSearchAlertNotification": (()=>createSearchAlertNotification),
    "createSuccessNotification": (()=>createSuccessNotification),
    "createWarningNotification": (()=>createWarningNotification),
    "createWelcomeNotification": (()=>createWelcomeNotification),
    "useAdvancedNotifications": (()=>useAdvancedNotifications)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const AdvancedNotificationContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useAdvancedNotifications = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AdvancedNotificationContext);
    if (!context) {
        throw new Error('useAdvancedNotifications must be used within an AdvancedNotificationProvider');
    }
    return context;
};
function AdvancedNotificationProvider({ children }) {
    const [notifications, setNotifications] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [isClient, setIsClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setIsClient(true);
    }, []);
    // تحميل الإشعارات المحفوظة
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isClient) return;
        const savedNotifications = localStorage.getItem('advanced_notifications');
        if (savedNotifications) {
            try {
                const parsed = JSON.parse(savedNotifications);
                // التأكد من أن parsed هو مصفوفة
                if (Array.isArray(parsed)) {
                    setNotifications(parsed.map((n)=>({
                            ...n,
                            timestamp: new Date(n.timestamp)
                        })));
                } else {
                    // إذا لم يكن مصفوفة، قم بمسح البيانات المعطوبة
                    localStorage.removeItem('advanced_notifications');
                    setNotifications([]);
                }
            } catch (error) {
                console.error('Error loading notifications:', error);
                // مسح البيانات المعطوبة
                localStorage.removeItem('advanced_notifications');
                setNotifications([]);
            }
        }
    }, [
        isClient
    ]);
    // حفظ الإشعارات
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isClient) return;
        localStorage.setItem('advanced_notifications', JSON.stringify(notifications));
    }, [
        notifications,
        isClient
    ]);
    const addNotification = (notificationData)=>{
        const newNotification = {
            ...notificationData,
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            timestamp: new Date(),
            isRead: false
        };
        setNotifications((prev)=>[
                newNotification,
                ...prev.slice(0, 99)
            ]); // الاحتفاظ بآخر 100 إشعار
        // إزالة تلقائية للإشعارات المؤقتة
        if (notificationData.autoHide !== false) {
            const duration = notificationData.duration || getDurationByType(notificationData.type);
            setTimeout(()=>{
                removeNotification(newNotification.id);
            }, duration);
        }
        // إشعار صوتي للإشعارات المهمة
        if (notificationData.priority === 'high' || notificationData.priority === 'urgent') {
            playNotificationSound(notificationData.type);
        }
        return newNotification.id;
    };
    const removeNotification = (id)=>{
        setNotifications((prev)=>prev.filter((n)=>n.id !== id));
    };
    const markAsRead = (id)=>{
        setNotifications((prev)=>prev.map((n)=>n.id === id ? {
                    ...n,
                    isRead: true
                } : n));
    };
    const markAllAsRead = ()=>{
        setNotifications((prev)=>prev.map((n)=>({
                    ...n,
                    isRead: true
                })));
    };
    const clearAll = ()=>{
        setNotifications([]);
    };
    const getUnreadCount = ()=>{
        return notifications.filter((n)=>!n.isRead).length;
    };
    const getNotificationsByCategory = (category)=>{
        return notifications.filter((n)=>n.category === category);
    };
    const getDurationByType = (type)=>{
        switch(type){
            case 'success':
                return 4000;
            case 'error':
                return 8000;
            case 'warning':
                return 6000;
            case 'welcome':
                return 10000;
            case 'payment':
                return 6000;
            case 'logout':
                return 3000;
            default:
                return 5000;
        }
    };
    const playNotificationSound = (type)=>{
        if (!isClient) return;
        try {
            const audio = new Audio();
            switch(type){
                case 'success':
                case 'payment':
                    audio.src = '/sounds/success.mp3';
                    break;
                case 'error':
                    audio.src = '/sounds/error.mp3';
                    break;
                case 'warning':
                    audio.src = '/sounds/warning.mp3';
                    break;
                case 'message':
                    audio.src = '/sounds/message.mp3';
                    break;
                default:
                    audio.src = '/sounds/notification.mp3';
            }
            audio.volume = 0.3;
            audio.play().catch(()=>{
            // تجاهل الأخطاء الصوتية
            });
        } catch (error) {
        // تجاهل الأخطاء الصوتية
        }
    };
    const value = {
        notifications,
        addNotification,
        removeNotification,
        markAsRead,
        markAllAsRead,
        clearAll,
        getUnreadCount,
        getNotificationsByCategory
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AdvancedNotificationContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/AdvancedNotificationSystem.tsx",
        lineNumber: 199,
        columnNumber: 5
    }, this);
}
const createWelcomeNotification = (userName)=>({
        type: 'welcome',
        title: `مرحباً بك ${userName}! 🎉`,
        message: 'نحن سعداء لانضمامك إلى منصة من المالك. استكشف الميزات الجديدة وابدأ رحلتك معنا!',
        priority: 'high',
        category: 'system',
        actionUrl: '/profile/setup',
        actionText: 'إكمال الملف الشخصي',
        autoHide: false,
        icon: '🎉'
    });
const createPaymentSuccessNotification = (amount, service)=>({
        type: 'payment',
        title: 'تم الدفع بنجاح! ✅',
        message: `تم دفع ${amount} ل.س لخدمة ${service} بنجاح. ستتلقى إيصالاً عبر البريد الإلكتروني.`,
        priority: 'high',
        category: 'commerce',
        actionUrl: '/payments/history',
        actionText: 'عرض تاريخ المدفوعات',
        icon: '💳'
    });
const createAdPostedNotification = (adTitle)=>({
        type: 'ad_posted',
        title: 'تم نشر إعلانك! 🚀',
        message: `تم نشر إعلان "${adTitle}" بنجاح. سيظهر للمستخدمين خلال دقائق قليلة.`,
        priority: 'medium',
        category: 'user_action',
        actionUrl: '/my-ads',
        actionText: 'عرض إعلاناتي',
        icon: '📢'
    });
const createMessageNotification = (senderName, preview)=>({
        type: 'message',
        title: `رسالة جديدة من ${senderName} 💬`,
        message: preview.length > 50 ? preview.substring(0, 50) + '...' : preview,
        priority: 'medium',
        category: 'social',
        actionUrl: '/messages',
        actionText: 'عرض الرسائل',
        icon: '💬'
    });
const createFavoriteNotification = (itemTitle, type)=>({
        type: 'favorite',
        title: type === 'product' ? 'تمت الإضافة للمفضلة! ❤️' : 'تمت متابعة البائع! 👤',
        message: type === 'product' ? `تم إضافة "${itemTitle}" إلى قائمة المفضلة` : `تم إضافة "${itemTitle}" إلى قائمة البائعين المتابعين`,
        priority: 'low',
        category: 'user_action',
        actionUrl: type === 'product' ? '/favorites' : '/following',
        icon: type === 'product' ? '❤️' : '👤'
    });
const createSearchAlertNotification = (searchTerm, newItemsCount)=>({
        type: 'search_alert',
        title: 'عناصر جديدة تطابق بحثك! 🔍',
        message: `تم العثور على ${newItemsCount} عنصر جديد يطابق بحثك عن "${searchTerm}"`,
        priority: 'medium',
        category: 'system',
        actionUrl: `/search?q=${encodeURIComponent(searchTerm)}`,
        actionText: 'عرض النتائج',
        icon: '🔍'
    });
const createLogoutNotification = ()=>({
        type: 'logout',
        title: 'تم تسجيل الخروج بنجاح 👋',
        message: 'شكراً لاستخدام منصة من المالك. نراك قريباً!',
        priority: 'medium',
        category: 'security',
        icon: '👋'
    });
const createErrorNotification = (title, message)=>({
        type: 'error',
        title,
        message,
        priority: 'high',
        category: 'system',
        icon: '❌'
    });
const createSuccessNotification = (title, message)=>({
        type: 'success',
        title,
        message,
        priority: 'medium',
        category: 'system',
        icon: '✅'
    });
const createWarningNotification = (title, message)=>({
        type: 'warning',
        title,
        message,
        priority: 'medium',
        category: 'system',
        icon: '⚠️'
    });
}}),
"[project]/src/components/ToastNotification.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>ToastNotification)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/styled-jsx/style.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
;
function ToastNotification({ notification, onClose, position = 'top-right' }) {
    const [isVisible, setIsVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLeaving, setIsLeaving] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // إظهار التوست مع تأخير بسيط للحصول على تأثير الانزلاق
        const showTimer = setTimeout(()=>setIsVisible(true), 100);
        // إخفاء التوست تلقائياً إذا كان مؤقتاً
        let hideTimer;
        if (notification.autoHide !== false) {
            const duration = notification.duration || getDurationByType(notification.type);
            hideTimer = setTimeout(()=>{
                handleClose();
            }, duration);
        }
        return ()=>{
            clearTimeout(showTimer);
            if (hideTimer) clearTimeout(hideTimer);
        };
    }, [
        notification
    ]);
    const handleClose = ()=>{
        setIsLeaving(true);
        setTimeout(()=>{
            onClose();
        }, 300); // مدة الانيميشن
    };
    const getDurationByType = (type)=>{
        switch(type){
            case 'success':
                return 4000;
            case 'error':
                return 8000;
            case 'warning':
                return 6000;
            case 'welcome':
                return 10000;
            case 'payment':
                return 6000;
            case 'logout':
                return 3000;
            default:
                return 5000;
        }
    };
    const getToastStyles = ()=>{
        const baseStyles = `
      fixed z-50 max-w-sm w-full transition-all duration-300 ease-in-out transform
      ${isVisible && !isLeaving ? 'translate-x-0 opacity-100' : ''}
      ${!isVisible || isLeaving ? getHiddenTransform() : ''}
    `;
        const positionStyles = {
            'top-right': 'top-4 right-4',
            'top-left': 'top-4 left-4',
            'bottom-right': 'bottom-4 right-4',
            'bottom-left': 'bottom-4 left-4',
            'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
            'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2'
        };
        return `${baseStyles} ${positionStyles[position]}`;
    };
    const getHiddenTransform = ()=>{
        switch(position){
            case 'top-right':
            case 'bottom-right':
                return 'translate-x-full opacity-0';
            case 'top-left':
            case 'bottom-left':
                return '-translate-x-full opacity-0';
            case 'top-center':
                return '-translate-y-full opacity-0';
            case 'bottom-center':
                return 'translate-y-full opacity-0';
            default:
                return 'translate-x-full opacity-0';
        }
    };
    const getTypeStyles = ()=>{
        switch(notification.type){
            case 'success':
            case 'payment':
            case 'ad_posted':
            case 'welcome':
                return {
                    container: 'bg-green-50/95 backdrop-blur-md border border-green-200/50 shadow-lg shadow-green-500/20',
                    icon: 'text-green-600',
                    title: 'text-green-800',
                    message: 'text-green-700',
                    button: 'text-green-600 hover:text-green-800 hover:bg-green-100',
                    glow: 'shadow-green-400/30',
                    animation: 'animate-pulse'
                };
            case 'error':
                return {
                    container: 'bg-red-50/95 backdrop-blur-md border border-red-200/50 shadow-lg shadow-red-500/20',
                    icon: 'text-red-600',
                    title: 'text-red-800',
                    message: 'text-red-700',
                    button: 'text-red-600 hover:text-red-800 hover:bg-red-100',
                    glow: 'shadow-red-400/30',
                    animation: 'animate-bounce'
                };
            case 'warning':
                return {
                    container: 'bg-orange-50/95 backdrop-blur-md border border-orange-200/50 shadow-lg shadow-orange-500/20',
                    icon: 'text-orange-600',
                    title: 'text-orange-800',
                    message: 'text-orange-700',
                    button: 'text-orange-600 hover:text-orange-800 hover:bg-orange-100',
                    glow: 'shadow-orange-400/30',
                    animation: 'animate-pulse'
                };
            case 'info':
            case 'message':
            case 'search_alert':
                return {
                    container: 'bg-blue-50/95 backdrop-blur-md border border-blue-200/50 shadow-lg shadow-blue-500/20',
                    icon: 'text-blue-600',
                    title: 'text-blue-800',
                    message: 'text-blue-700',
                    button: 'text-blue-600 hover:text-blue-800 hover:bg-blue-100',
                    glow: 'shadow-blue-400/30',
                    animation: 'animate-pulse'
                };
            case 'favorite':
                return {
                    container: 'bg-pink-50/95 backdrop-blur-md border border-pink-200/50 shadow-lg shadow-pink-500/20',
                    icon: 'text-pink-600',
                    title: 'text-pink-800',
                    message: 'text-pink-700',
                    button: 'text-pink-600 hover:text-pink-800 hover:bg-pink-100',
                    glow: 'shadow-pink-400/30',
                    animation: 'animate-pulse'
                };
            case 'logout':
                return {
                    container: 'bg-gray-50/95 backdrop-blur-md border border-gray-200/50 shadow-lg shadow-gray-500/20',
                    icon: 'text-gray-600',
                    title: 'text-gray-800',
                    message: 'text-gray-700',
                    button: 'text-gray-600 hover:text-gray-800 hover:bg-gray-100',
                    glow: 'shadow-gray-400/30',
                    animation: 'animate-pulse'
                };
            default:
                return {
                    container: 'bg-white/95 backdrop-blur-md border border-gray-200/50 shadow-lg',
                    icon: 'text-gray-600',
                    title: 'text-gray-800',
                    message: 'text-gray-700',
                    button: 'text-gray-600 hover:text-gray-800 hover:bg-gray-100',
                    glow: 'shadow-gray-400/30',
                    animation: ''
                };
        }
    };
    const styles = getTypeStyles();
    const getPriorityIndicator = ()=>{
        if (notification.priority === 'urgent') {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping"
            }, void 0, false, {
                fileName: "[project]/src/components/ToastNotification.tsx",
                lineNumber: 185,
                columnNumber: 9
            }, this);
        }
        if (notification.priority === 'high') {
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full animate-pulse"
            }, void 0, false, {
                fileName: "[project]/src/components/ToastNotification.tsx",
                lineNumber: 190,
                columnNumber: 9
            }, this);
        }
        return null;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "jsx-e43ea6c4bca847b0" + " " + (getToastStyles() || ""),
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "jsx-e43ea6c4bca847b0" + " " + `
        relative rounded-xl p-4 ${styles.container} ${styles.glow}
        hover:shadow-xl transition-all duration-300
        border-l-4 ${notification.type === 'success' || notification.type === 'payment' || notification.type === 'welcome' ? 'border-l-green-500' : notification.type === 'error' ? 'border-l-red-500' : notification.type === 'warning' ? 'border-l-orange-500' : 'border-l-blue-500'}
      `,
                children: [
                    getPriorityIndicator(),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-e43ea6c4bca847b0" + " " + "flex items-start gap-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-e43ea6c4bca847b0" + " " + `flex-shrink-0 ${styles.icon} ${styles.animation}`,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "jsx-e43ea6c4bca847b0" + " " + "text-2xl",
                                    children: notification.icon || getDefaultIcon(notification.type)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ToastNotification.tsx",
                                    lineNumber: 210,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ToastNotification.tsx",
                                lineNumber: 209,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "jsx-e43ea6c4bca847b0" + " " + "flex-1 min-w-0",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "jsx-e43ea6c4bca847b0" + " " + `font-semibold text-sm ${styles.title} mb-1`,
                                        children: notification.title
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ToastNotification.tsx",
                                        lineNumber: 217,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "jsx-e43ea6c4bca847b0" + " " + `text-sm ${styles.message} leading-relaxed`,
                                        children: notification.message
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ToastNotification.tsx",
                                        lineNumber: 220,
                                        columnNumber: 13
                                    }, this),
                                    notification.actionUrl && notification.actionText && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "jsx-e43ea6c4bca847b0" + " " + "mt-3",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                            href: notification.actionUrl,
                                            onClick: handleClose,
                                            className: "jsx-e43ea6c4bca847b0" + " " + `inline-flex items-center text-xs font-medium ${styles.button} 
                    px-3 py-1 rounded-lg transition-colors duration-200`,
                                            children: [
                                                notification.actionText,
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                    fill: "none",
                                                    stroke: "currentColor",
                                                    viewBox: "0 0 24 24",
                                                    className: "jsx-e43ea6c4bca847b0" + " " + "w-3 h-3 mr-1",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                        strokeLinecap: "round",
                                                        strokeLinejoin: "round",
                                                        strokeWidth: 2,
                                                        d: "M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14",
                                                        className: "jsx-e43ea6c4bca847b0"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/ToastNotification.tsx",
                                                        lineNumber: 235,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/ToastNotification.tsx",
                                                    lineNumber: 234,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/ToastNotification.tsx",
                                            lineNumber: 227,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ToastNotification.tsx",
                                        lineNumber: 226,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/ToastNotification.tsx",
                                lineNumber: 216,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleClose,
                                "aria-label": "إغلاق الإشعار",
                                className: "jsx-e43ea6c4bca847b0" + " " + `flex-shrink-0 ${styles.button} p-1 rounded-lg transition-colors duration-200`,
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    fill: "none",
                                    stroke: "currentColor",
                                    viewBox: "0 0 24 24",
                                    className: "jsx-e43ea6c4bca847b0" + " " + "w-4 h-4",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M6 18L18 6M6 6l12 12",
                                        className: "jsx-e43ea6c4bca847b0"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/ToastNotification.tsx",
                                        lineNumber: 249,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/ToastNotification.tsx",
                                    lineNumber: 248,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/ToastNotification.tsx",
                                lineNumber: 243,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/ToastNotification.tsx",
                        lineNumber: 207,
                        columnNumber: 9
                    }, this),
                    notification.autoHide !== false && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "jsx-e43ea6c4bca847b0" + " " + "absolute bottom-0 left-0 right-0 h-1 bg-gray-200/30 rounded-b-xl overflow-hidden",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            style: {
                                width: '100%',
                                animation: `shrink ${getDurationByType(notification.type)}ms linear forwards`
                            },
                            className: "jsx-e43ea6c4bca847b0" + " " + `h-full transition-all ease-linear ${notification.type === 'success' || notification.type === 'payment' || notification.type === 'welcome' ? 'bg-green-500' : notification.type === 'error' ? 'bg-red-500' : notification.type === 'warning' ? 'bg-orange-500' : 'bg-blue-500'}`
                        }, void 0, false, {
                            fileName: "[project]/src/components/ToastNotification.tsx",
                            lineNumber: 257,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/ToastNotification.tsx",
                        lineNumber: 256,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/ToastNotification.tsx",
                lineNumber: 198,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$styled$2d$jsx$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                id: "e43ea6c4bca847b0",
                children: "@keyframes shrink{0%{width:100%}to{width:0%}}"
            }, void 0, false, void 0, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ToastNotification.tsx",
        lineNumber: 197,
        columnNumber: 5
    }, this);
}
function getDefaultIcon(type) {
    switch(type){
        case 'success':
            return '✅';
        case 'error':
            return '❌';
        case 'warning':
            return '⚠️';
        case 'info':
            return 'ℹ️';
        case 'welcome':
            return '🎉';
        case 'payment':
            return '💳';
        case 'message':
            return '💬';
        case 'favorite':
            return '❤️';
        case 'ad_posted':
            return '📢';
        case 'search_alert':
            return '🔍';
        case 'logout':
            return '👋';
        default:
            return '🔔';
    }
}
}}),
"[project]/src/components/ToastManager.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ToastProvider": (()=>ToastProvider),
    "useQuickToast": (()=>useQuickToast),
    "useToast": (()=>useToast)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ToastNotification$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/ToastNotification.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
const ToastContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useToast = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ToastContext);
    if (!context) {
        throw new Error('useToast must be used within a ToastProvider');
    }
    return context;
};
function ToastProvider({ children, maxToasts = 5, position = 'top-right' }) {
    const [toasts, setToasts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const showToast = (notificationData)=>{
        const newToast = {
            ...notificationData,
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
            timestamp: new Date(),
            isRead: false
        };
        setToasts((prev)=>{
            const updated = [
                newToast,
                ...prev
            ];
            // الاحتفاظ بالحد الأقصى من التوست
            return updated.slice(0, maxToasts);
        });
    };
    const removeToast = (id)=>{
        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));
    };
    const clearAllToasts = ()=>{
        setToasts([]);
    };
    // دوال مساعدة لأنواع مختلفة من التوست
    const showSuccess = (title, message, actionUrl, actionText)=>{
        showToast({
            type: 'success',
            title,
            message,
            priority: 'medium',
            category: 'system',
            actionUrl,
            actionText,
            icon: '✅'
        });
    };
    const showError = (title, message)=>{
        showToast({
            type: 'error',
            title,
            message,
            priority: 'high',
            category: 'system',
            autoHide: false,
            icon: '❌'
        });
    };
    const showWarning = (title, message)=>{
        showToast({
            type: 'warning',
            title,
            message,
            priority: 'medium',
            category: 'system',
            icon: '⚠️'
        });
    };
    const showInfo = (title, message)=>{
        showToast({
            type: 'info',
            title,
            message,
            priority: 'low',
            category: 'system',
            icon: 'ℹ️'
        });
    };
    const showWelcome = (userName)=>{
        showToast({
            type: 'welcome',
            title: `مرحباً بك ${userName}! 🎉`,
            message: 'نحن سعداء لانضمامك إلى منصة من المالك. استكشف الميزات الجديدة وابدأ رحلتك معنا!',
            priority: 'high',
            category: 'system',
            actionUrl: '/profile/setup',
            actionText: 'إكمال الملف الشخصي',
            autoHide: false,
            icon: '🎉'
        });
    };
    const showPaymentSuccess = (amount, service)=>{
        showToast({
            type: 'payment',
            title: 'تم الدفع بنجاح! ✅',
            message: `تم دفع ${amount} ل.س لخدمة ${service} بنجاح. ستتلقى إيصالاً عبر البريد الإلكتروني.`,
            priority: 'high',
            category: 'commerce',
            actionUrl: '/payments/history',
            actionText: 'عرض تاريخ المدفوعات',
            icon: '💳'
        });
    };
    const showAdPosted = (adTitle)=>{
        showToast({
            type: 'ad_posted',
            title: 'تم نشر إعلانك! 🚀',
            message: `تم نشر إعلان "${adTitle}" بنجاح. سيظهر للمستخدمين خلال دقائق قليلة.`,
            priority: 'medium',
            category: 'user_action',
            actionUrl: '/my-ads',
            actionText: 'عرض إعلاناتي',
            icon: '📢'
        });
    };
    const showMessage = (senderName, preview)=>{
        showToast({
            type: 'message',
            title: `رسالة جديدة من ${senderName} 💬`,
            message: preview.length > 50 ? preview.substring(0, 50) + '...' : preview,
            priority: 'medium',
            category: 'social',
            actionUrl: '/messages',
            actionText: 'عرض الرسائل',
            icon: '💬'
        });
    };
    const showFavorite = (itemTitle, type)=>{
        showToast({
            type: 'favorite',
            title: type === 'product' ? 'تمت الإضافة للمفضلة! ❤️' : 'تمت متابعة البائع! 👤',
            message: type === 'product' ? `تم إضافة "${itemTitle}" إلى قائمة المفضلة` : `تم إضافة "${itemTitle}" إلى قائمة البائعين المتابعين`,
            priority: 'low',
            category: 'user_action',
            actionUrl: type === 'product' ? '/favorites' : '/following',
            actionText: type === 'product' ? 'عرض المفضلة' : 'عرض المتابعين',
            icon: type === 'product' ? '❤️' : '👤'
        });
    };
    const showLogout = ()=>{
        showToast({
            type: 'logout',
            title: 'تم تسجيل الخروج بنجاح 👋',
            message: 'شكراً لاستخدام منصة من المالك. نراك قريباً!',
            priority: 'medium',
            category: 'security',
            duration: 3000,
            icon: '👋'
        });
    };
    const value = {
        showToast,
        showSuccess,
        showError,
        showWarning,
        showInfo,
        showWelcome,
        showPaymentSuccess,
        showAdPosted,
        showMessage,
        showFavorite,
        showLogout,
        clearAllToasts
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ToastContext.Provider, {
        value: value,
        children: [
            children,
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 pointer-events-none z-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: `
          absolute flex flex-col gap-2
          ${position === 'top-right' ? 'top-4 right-4' : ''}
          ${position === 'top-left' ? 'top-4 left-4' : ''}
          ${position === 'bottom-right' ? 'bottom-4 right-4' : ''}
          ${position === 'bottom-left' ? 'bottom-4 left-4' : ''}
          ${position === 'top-center' ? 'top-4 left-1/2 transform -translate-x-1/2' : ''}
          ${position === 'bottom-center' ? 'bottom-4 left-1/2 transform -translate-x-1/2' : ''}
        `,
                    children: toasts.map((toast, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "pointer-events-auto",
                            style: {
                                zIndex: 1000 - index,
                                marginTop: position.includes('top') ? `${index * 8}px` : '0',
                                marginBottom: position.includes('bottom') ? `${index * 8}px` : '0'
                            },
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ToastNotification$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                notification: toast,
                                onClose: ()=>removeToast(toast.id),
                                position: position
                            }, void 0, false, {
                                fileName: "[project]/src/components/ToastManager.tsx",
                                lineNumber: 236,
                                columnNumber: 15
                            }, this)
                        }, toast.id, false, {
                            fileName: "[project]/src/components/ToastManager.tsx",
                            lineNumber: 227,
                            columnNumber: 13
                        }, this))
                }, void 0, false, {
                    fileName: "[project]/src/components/ToastManager.tsx",
                    lineNumber: 217,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/ToastManager.tsx",
                lineNumber: 216,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ToastManager.tsx",
        lineNumber: 212,
        columnNumber: 5
    }, this);
}
const useQuickToast = ()=>{
    const { showSuccess, showError, showWarning, showInfo } = useToast();
    return {
        success: (message, title = 'نجح العملية')=>showSuccess(title, message),
        error: (message, title = 'حدث خطأ')=>showError(title, message),
        warning: (message, title = 'تحذير')=>showWarning(title, message),
        info: (message, title = 'معلومة')=>showInfo(title, message)
    };
};
}}),
"[project]/src/components/NotificationModal.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "ModalProvider": (()=>ModalProvider),
    "useModal": (()=>useModal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const ModalContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const useModal = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ModalContext);
    if (!context) {
        throw new Error('useModal must be used within a ModalProvider');
    }
    return context;
};
function ModalProvider({ children }) {
    const [modals, setModals] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const showModal = (modalData)=>{
        const newModal = {
            ...modalData,
            id: Date.now().toString() + Math.random().toString(36).substr(2, 9)
        };
        setModals((prev)=>[
                ...prev,
                newModal
            ]);
        // إغلاق تلقائي إذا كان مطلوباً
        if (modalData.autoClose && modalData.duration) {
            setTimeout(()=>{
                closeModal(newModal.id);
            }, modalData.duration);
        }
    };
    const closeModal = (id)=>{
        if (id) {
            setModals((prev)=>prev.filter((modal)=>modal.id !== id));
        } else {
            // إغلاق آخر modal
            setModals((prev)=>prev.slice(0, -1));
        }
    };
    const closeAllModals = ()=>{
        setModals([]);
    };
    const showConfirmation = (title, message, onConfirm, onCancel)=>{
        showModal({
            type: 'confirmation',
            title,
            message,
            icon: '❓',
            primaryAction: {
                text: 'تأكيد',
                action: ()=>{
                    onConfirm();
                    closeModal();
                },
                style: 'primary'
            },
            secondaryAction: {
                text: 'إلغاء',
                action: ()=>{
                    onCancel?.();
                    closeModal();
                }
            },
            backdrop: true,
            size: 'md'
        });
    };
    const showWelcomeModal = (userName, onComplete)=>{
        showModal({
            type: 'welcome',
            title: `مرحباً بك ${userName}! 🎉`,
            message: 'نحن سعداء لانضمامك إلى منصة من المالك. دعنا نساعدك في إعداد حسابك للحصول على أفضل تجربة.',
            icon: '🎉',
            primaryAction: {
                text: 'ابدأ الإعداد',
                action: ()=>{
                    onComplete();
                    closeModal();
                },
                style: 'success'
            },
            secondaryAction: {
                text: 'لاحقاً',
                action: ()=>closeModal()
            },
            backdrop: true,
            size: 'lg'
        });
    };
    const showLogoutConfirmation = (onConfirm)=>{
        showModal({
            type: 'logout',
            title: 'تأكيد تسجيل الخروج',
            message: 'هل أنت متأكد من أنك تريد تسجيل الخروج من حسابك؟',
            icon: '👋',
            primaryAction: {
                text: 'تسجيل الخروج',
                action: ()=>{
                    onConfirm();
                    closeModal();
                },
                style: 'danger'
            },
            secondaryAction: {
                text: 'البقاء متصلاً',
                action: ()=>closeModal()
            },
            backdrop: true,
            size: 'md'
        });
    };
    const value = {
        showModal,
        showConfirmation,
        showWelcomeModal,
        showLogoutConfirmation,
        closeModal,
        closeAllModals
    };
    // إغلاق المودال بالضغط على Escape
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleEscape = (e)=>{
            if (e.key === 'Escape' && modals.length > 0) {
                closeModal();
            }
        };
        document.addEventListener('keydown', handleEscape);
        return ()=>document.removeEventListener('keydown', handleEscape);
    }, [
        modals.length
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ModalContext.Provider, {
        value: value,
        children: [
            children,
            modals.map((modal, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ModalComponent, {
                    modal: modal,
                    onClose: ()=>closeModal(modal.id),
                    zIndex: 1000 + index
                }, modal.id, false, {
                    fileName: "[project]/src/components/NotificationModal.tsx",
                    lineNumber: 190,
                    columnNumber: 9
                }, this))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/NotificationModal.tsx",
        lineNumber: 185,
        columnNumber: 5
    }, this);
}
function ModalComponent({ modal, onClose, zIndex }) {
    const [isVisible, setIsVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // إظهار المودال مع تأخير بسيط للحصول على تأثير الانزلاق
        const timer = setTimeout(()=>setIsVisible(true), 50);
        return ()=>clearTimeout(timer);
    }, []);
    const handleClose = ()=>{
        setIsVisible(false);
        setTimeout(onClose, 200); // انتظار انتهاء الانيميشن
    };
    const handleBackdropClick = (e)=>{
        if (e.target === e.currentTarget && modal.backdrop) {
            handleClose();
        }
    };
    const getTypeStyles = ()=>{
        switch(modal.type){
            case 'success':
            case 'welcome':
                return {
                    container: 'bg-green-50/95 backdrop-blur-md border-green-200',
                    header: 'text-green-800',
                    icon: 'text-green-600',
                    message: 'text-green-700'
                };
            case 'error':
                return {
                    container: 'bg-red-50/95 backdrop-blur-md border-red-200',
                    header: 'text-red-800',
                    icon: 'text-red-600',
                    message: 'text-red-700'
                };
            case 'warning':
                return {
                    container: 'bg-orange-50/95 backdrop-blur-md border-orange-200',
                    header: 'text-orange-800',
                    icon: 'text-orange-600',
                    message: 'text-orange-700'
                };
            case 'info':
            case 'confirmation':
                return {
                    container: 'bg-blue-50/95 backdrop-blur-md border-blue-200',
                    header: 'text-blue-800',
                    icon: 'text-blue-600',
                    message: 'text-blue-700'
                };
            case 'logout':
                return {
                    container: 'bg-gray-50/95 backdrop-blur-md border-gray-200',
                    header: 'text-gray-800',
                    icon: 'text-gray-600',
                    message: 'text-gray-700'
                };
            default:
                return {
                    container: 'bg-white/95 backdrop-blur-md border-gray-200',
                    header: 'text-gray-800',
                    icon: 'text-gray-600',
                    message: 'text-gray-700'
                };
        }
    };
    const getSizeClasses = ()=>{
        switch(modal.size){
            case 'sm':
                return 'max-w-sm';
            case 'md':
                return 'max-w-md';
            case 'lg':
                return 'max-w-lg';
            case 'xl':
                return 'max-w-xl';
            default:
                return 'max-w-md';
        }
    };
    const getButtonStyle = (style)=>{
        switch(style){
            case 'primary':
                return 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500';
            case 'success':
                return 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500';
            case 'danger':
                return 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500';
            case 'warning':
                return 'bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500';
            default:
                return 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500';
        }
    };
    const styles = getTypeStyles();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `
        fixed inset-0 flex items-center justify-center p-4 transition-all duration-200
        ${isVisible ? 'opacity-100' : 'opacity-0'}
      `,
        style: {
            zIndex
        },
        onClick: handleBackdropClick,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 bg-black/50 backdrop-blur-sm"
            }, void 0, false, {
                fileName: "[project]/src/components/NotificationModal.tsx",
                lineNumber: 318,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `
          relative w-full ${getSizeClasses()} transform transition-all duration-200
          ${isVisible ? 'scale-100 translate-y-0' : 'scale-95 translate-y-4'}
        `,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: `
          rounded-2xl border shadow-2xl ${styles.container}
          ${modal.type === 'success' || modal.type === 'welcome' ? 'shadow-green-500/20' : modal.type === 'error' ? 'shadow-red-500/20' : modal.type === 'warning' ? 'shadow-orange-500/20' : 'shadow-blue-500/20'}
        `,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between p-6 border-b border-gray-200/50",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center gap-3",
                                    children: [
                                        modal.icon && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: `text-3xl ${styles.icon}`,
                                            children: modal.icon
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/NotificationModal.tsx",
                                            lineNumber: 337,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: `text-xl font-bold ${styles.header}`,
                                            children: modal.title
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/NotificationModal.tsx",
                                            lineNumber: 341,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/NotificationModal.tsx",
                                    lineNumber: 335,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: handleClose,
                                    className: "text-gray-400 hover:text-gray-600 transition-colors",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                        className: "w-6 h-6",
                                        fill: "none",
                                        stroke: "currentColor",
                                        viewBox: "0 0 24 24",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                            strokeLinecap: "round",
                                            strokeLinejoin: "round",
                                            strokeWidth: 2,
                                            d: "M6 18L18 6M6 6l12 12"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/NotificationModal.tsx",
                                            lineNumber: 350,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/NotificationModal.tsx",
                                        lineNumber: 349,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/NotificationModal.tsx",
                                    lineNumber: 345,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/NotificationModal.tsx",
                            lineNumber: 334,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: `text-lg leading-relaxed ${styles.message}`,
                                children: modal.message
                            }, void 0, false, {
                                fileName: "[project]/src/components/NotificationModal.tsx",
                                lineNumber: 357,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/NotificationModal.tsx",
                            lineNumber: 356,
                            columnNumber: 11
                        }, this),
                        (modal.primaryAction || modal.secondaryAction) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-end gap-3 p-6 border-t border-gray-200/50",
                            children: [
                                modal.secondaryAction && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: modal.secondaryAction.action,
                                    className: "px-6 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2",
                                    children: modal.secondaryAction.text
                                }, void 0, false, {
                                    fileName: "[project]/src/components/NotificationModal.tsx",
                                    lineNumber: 366,
                                    columnNumber: 17
                                }, this),
                                modal.primaryAction && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: modal.primaryAction.action,
                                    className: `
                    px-6 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2
                    ${getButtonStyle(modal.primaryAction.style)}
                  `,
                                    children: modal.primaryAction.text
                                }, void 0, false, {
                                    fileName: "[project]/src/components/NotificationModal.tsx",
                                    lineNumber: 374,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/NotificationModal.tsx",
                            lineNumber: 364,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/NotificationModal.tsx",
                    lineNumber: 327,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/NotificationModal.tsx",
                lineNumber: 321,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/NotificationModal.tsx",
        lineNumber: 309,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const authReducer = (state, action)=>{
    switch(action.type){
        case 'LOGIN_START':
            return {
                ...state,
                isLoading: true,
                error: null
            };
        case 'LOGIN_SUCCESS':
            return {
                ...state,
                user: action.payload,
                isAuthenticated: true,
                isLoading: false,
                error: null
            };
        case 'LOGIN_FAILURE':
            return {
                ...state,
                user: null,
                isAuthenticated: false,
                isLoading: false,
                error: action.payload
            };
        case 'LOGOUT':
            return {
                ...state,
                user: null,
                isAuthenticated: false,
                isLoading: false,
                error: null
            };
        case 'UPDATE_PROFILE':
            return {
                ...state,
                user: state.user ? {
                    ...state.user,
                    ...action.payload
                } : null
            };
        case 'SET_LOADING':
            return {
                ...state,
                isLoading: action.payload
            };
        case 'SET_ERROR':
            return {
                ...state,
                error: action.payload
            };
        default:
            return state;
    }
};
const initialState = {
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null
};
const AuthProvider = ({ children })=>{
    const [state, dispatch] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useReducer"])(authReducer, initialState);
    // تحميل بيانات المستخدم من localStorage عند بدء التطبيق
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const loadUserFromStorage = ()=>{
            try {
                if ("TURBOPACK compile-time truthy", 1) return;
                "TURBOPACK unreachable";
                // التحقق من النظام الجديد أولاً
                const isLoggedIn = undefined;
                const currentUser = undefined;
                // النظام القديم
                const sessionData = undefined;
            } catch (error) {
                console.error('Error loading user session:', error);
                if ("TURBOPACK compile-time falsy", 0) {
                    "TURBOPACK unreachable";
                }
            }
        };
        loadUserFromStorage();
    }, []);
    const login = async (credentials)=>{
        dispatch({
            type: 'LOGIN_START'
        });
        try {
            // التحقق من بيانات Mahmut Madenli
            if (credentials.email === '<EMAIL>' && credentials.password === 'Ma123456') {
                const mahmutUser = {
                    id: 'user-mahmut-001',
                    email: '<EMAIL>',
                    phone: '+963988652401',
                    name: 'Mahmut Madenli',
                    userType: 'individual',
                    isVerified: true,
                    createdAt: new Date('2024-01-15'),
                    lastLogin: new Date(),
                    stats: {
                        totalAds: 12,
                        activeAds: 8,
                        expiredAds: 4,
                        totalViews: 2450,
                        totalContacts: 89,
                        successfulDeals: 15
                    },
                    settings: {
                        language: 'ar',
                        notifications: {
                            email: true,
                            sms: true,
                            push: true,
                            marketing: false
                        },
                        privacy: {
                            showPhone: true,
                            showEmail: false,
                            allowMessages: true,
                            showOnlineStatus: true
                        },
                        preferences: {
                            currency: 'SYP',
                            theme: 'light',
                            autoSave: true
                        }
                    },
                    individualInfo: {
                        firstName: 'Mahmut',
                        lastName: 'Madenli',
                        gender: 'male',
                        address: {
                            governorate: 'دمشق',
                            city: 'دمشق',
                            area: 'المزة'
                        }
                    },
                    subscription: {
                        planId: 'premium',
                        planName: 'الباقة المميزة',
                        planType: 'individual',
                        startDate: new Date('2024-01-15'),
                        endDate: new Date('2024-12-31'),
                        isActive: true,
                        autoRenew: true,
                        features: [
                            '15 إعلان شهرياً',
                            'إعلانات مميزة',
                            'دعم فني'
                        ]
                    }
                };
                // حفظ في النظام الجديد
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('currentUser', JSON.stringify({
                    id: mahmutUser.id,
                    name: mahmutUser.name,
                    email: mahmutUser.email,
                    userType: mahmutUser.userType,
                    individualInfo: mahmutUser.individualInfo,
                    membershipId: 'MIN-2024-001247',
                    createdAt: mahmutUser.createdAt,
                    subscriptionPlan: 'premium',
                    verificationBadge: 'silver'
                }));
                dispatch({
                    type: 'LOGIN_SUCCESS',
                    payload: mahmutUser
                });
                return;
            }
            // التحقق من بيانات الشركة
            if (credentials.email === '<EMAIL>' && credentials.password === 'Gr123456') {
                const businessUser = {
                    id: 'user-business-001',
                    email: '<EMAIL>',
                    phone: '+963988652401',
                    name: 'Grand Mark Turkey',
                    userType: 'business',
                    isVerified: true,
                    createdAt: new Date('2024-01-10'),
                    lastLogin: new Date(),
                    stats: {
                        totalAds: 25,
                        activeAds: 18,
                        expiredAds: 7,
                        totalViews: 5200,
                        totalContacts: 156,
                        successfulDeals: 32
                    },
                    settings: {
                        language: 'ar',
                        notifications: {
                            email: true,
                            sms: true,
                            push: true,
                            marketing: true
                        },
                        privacy: {
                            showPhone: true,
                            showEmail: true,
                            allowMessages: true,
                            showOnlineStatus: true
                        },
                        preferences: {
                            currency: 'SYP',
                            theme: 'light',
                            autoSave: true
                        }
                    },
                    businessInfo: {
                        companyName: 'Grand Mark Turkey',
                        businessType: 'استيراد وتصدير',
                        registrationNumber: 'GMT123456',
                        taxNumber: 'TAX789012',
                        establishedYear: 2018,
                        employeeCount: '50-100',
                        website: 'https://grandmarkturkey.com',
                        description: 'شركة رائدة في مجال الاستيراد والتصدير مع تركيا',
                        address: {
                            governorate: 'دمشق',
                            city: 'دمشق',
                            area: 'المزة',
                            street: 'شارع التجارة',
                            building: 'مبنى التجارة الدولية'
                        },
                        contactPerson: {
                            name: 'أحمد محمد',
                            position: 'المدير العام',
                            phone: '+963988652401',
                            email: '<EMAIL>'
                        }
                    },
                    subscription: {
                        planId: 'business-professional',
                        planName: 'الخطة المهنية',
                        planType: 'business',
                        startDate: new Date('2024-01-10'),
                        endDate: new Date('2025-01-10'),
                        isActive: true,
                        autoRenew: true,
                        features: [
                            'إعلانات غير محدودة',
                            'دعم أولوية',
                            'تحليلات متقدمة',
                            'شارة التوثيق الذهبية'
                        ]
                    }
                };
                const session = {
                    token: 'mock-token-business',
                    refreshToken: 'mock-refresh-token-business',
                    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
                    user: businessUser
                };
                localStorage.setItem('userSession', JSON.stringify(session));
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('currentUser', JSON.stringify(businessUser));
                dispatch({
                    type: 'LOGIN_SUCCESS',
                    payload: businessUser
                });
                return;
            }
            // التحقق من بيانات المكتب العقاري
            if (credentials.email === '<EMAIL>' && credentials.password === 'Du123456') {
                const realEstateUser = {
                    id: 'user-realestate-001',
                    email: '<EMAIL>',
                    phone: '+963988652401',
                    name: 'مكتب دنيا زاد العقاري',
                    userType: 'real-estate-office',
                    isVerified: true,
                    createdAt: new Date('2024-01-05'),
                    lastLogin: new Date(),
                    stats: {
                        totalAds: 45,
                        activeAds: 32,
                        expiredAds: 13,
                        totalViews: 8500,
                        totalContacts: 245,
                        successfulDeals: 58
                    },
                    settings: {
                        language: 'ar',
                        notifications: {
                            email: true,
                            sms: true,
                            push: true,
                            marketing: true
                        },
                        privacy: {
                            showPhone: true,
                            showEmail: true,
                            allowMessages: true,
                            showOnlineStatus: true
                        },
                        preferences: {
                            currency: 'SYP',
                            theme: 'light',
                            autoSave: true
                        }
                    },
                    realEstateOfficeInfo: {
                        officeName: 'مكتب دنيا زاد العقاري',
                        licenseNumber: 'RE123456',
                        establishedYear: 2020,
                        specialization: [
                            'بيع',
                            'شراء',
                            'إيجار',
                            'استثمار عقاري'
                        ],
                        serviceAreas: [
                            'دمشق',
                            'ريف دمشق',
                            'حلب'
                        ],
                        ownerName: 'محمد دنيا زاد',
                        managerName: 'أحمد دنيا زاد',
                        yearsOfExperience: 8,
                        teamSize: 12,
                        description: 'مكتب عقاري متخصص في جميع أنواع العقارات السكنية والتجارية',
                        address: {
                            governorate: 'دمشق',
                            city: 'دمشق',
                            area: 'المالكي',
                            street: 'شارع العقارات',
                            building: 'مبنى دنيا زاد'
                        }
                    },
                    subscription: {
                        planId: 'real-estate-office',
                        planName: 'خطة المكتب العقاري',
                        planType: 'business',
                        startDate: new Date('2024-01-05'),
                        endDate: new Date('2025-01-05'),
                        isActive: true,
                        autoRenew: true,
                        features: [
                            'إعلانات عقارية غير محدودة',
                            'أدوات تحليل السوق',
                            'دعم متخصص',
                            'شارة التوثيق الفضية'
                        ]
                    }
                };
                const session = {
                    token: 'mock-token-realestate',
                    refreshToken: 'mock-refresh-token-realestate',
                    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
                    user: realEstateUser
                };
                localStorage.setItem('userSession', JSON.stringify(session));
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('currentUser', JSON.stringify(realEstateUser));
                dispatch({
                    type: 'LOGIN_SUCCESS',
                    payload: realEstateUser
                });
                return;
            }
            // محاكاة API call للمستخدمين الآخرين
            await new Promise((resolve)=>setTimeout(resolve, 1000));
            // بيانات مستخدم تجريبية
            const mockUser = {
                id: 'user-' + Date.now(),
                email: credentials.email,
                phone: '+963988123456',
                name: credentials.email.split('@')[0],
                userType: credentials.email.includes('business') ? 'business' : credentials.email.includes('office') ? 'real-estate-office' : 'individual',
                isVerified: true,
                createdAt: new Date(),
                lastLogin: new Date(),
                stats: {
                    totalAds: 5,
                    activeAds: 3,
                    expiredAds: 2,
                    totalViews: 1250,
                    totalContacts: 45,
                    successfulDeals: 8
                },
                settings: {
                    language: 'ar',
                    notifications: {
                        email: true,
                        sms: true,
                        push: true,
                        marketing: false
                    },
                    privacy: {
                        showPhone: true,
                        showEmail: false,
                        allowMessages: true,
                        showOnlineStatus: true
                    },
                    preferences: {
                        currency: 'SYP',
                        theme: 'light',
                        autoSave: true
                    }
                }
            };
            // إضافة معلومات حسب نوع المستخدم
            if (mockUser.userType === 'individual') {
                mockUser.individualInfo = {
                    firstName: 'أحمد',
                    lastName: 'محمد',
                    gender: 'male',
                    address: {
                        governorate: 'دمشق',
                        city: 'دمشق',
                        area: 'المزة'
                    }
                };
            } else if (mockUser.userType === 'business') {
                mockUser.businessInfo = {
                    companyName: 'شركة التجارة المتقدمة',
                    businessType: 'تجارة عامة',
                    registrationNumber: 'REG123456',
                    establishedYear: 2015,
                    employeeCount: '10-50',
                    address: {
                        governorate: 'دمشق',
                        city: 'دمشق',
                        area: 'المزة',
                        street: 'شارع الثورة'
                    },
                    contactPerson: {
                        name: 'محمد أحمد',
                        position: 'مدير عام',
                        phone: '+963988123456',
                        email: credentials.email
                    }
                };
                mockUser.subscription = {
                    planId: 'business-starter',
                    planName: 'باقة البداية',
                    planType: 'business',
                    startDate: new Date(),
                    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                    isActive: true,
                    autoRenew: true,
                    features: [
                        '50 إعلان شهرياً',
                        'دعم فني',
                        'إحصائيات متقدمة'
                    ]
                };
            } else if (mockUser.userType === 'real-estate-office') {
                mockUser.realEstateOfficeInfo = {
                    officeName: 'مكتب العقارات المتميز',
                    licenseNumber: 'LIC789012',
                    licenseIssueDate: new Date('2020-01-01'),
                    licenseExpiryDate: new Date('2025-01-01'),
                    ownerName: 'خالد السوري',
                    specializations: [
                        'شقق سكنية',
                        'فيلات',
                        'محلات تجارية'
                    ],
                    serviceAreas: [
                        'دمشق',
                        'ريف دمشق'
                    ],
                    yearsOfExperience: 10,
                    teamSize: 5,
                    address: {
                        governorate: 'دمشق',
                        city: 'دمشق',
                        area: 'المزة',
                        street: 'شارع المتنبي',
                        building: 'بناء رقم 15',
                        floor: 'الطابق الثاني'
                    },
                    workingHours: {
                        sunday: {
                            open: '09:00',
                            close: '17:00',
                            isOpen: true
                        },
                        monday: {
                            open: '09:00',
                            close: '17:00',
                            isOpen: true
                        },
                        tuesday: {
                            open: '09:00',
                            close: '17:00',
                            isOpen: true
                        },
                        wednesday: {
                            open: '09:00',
                            close: '17:00',
                            isOpen: true
                        },
                        thursday: {
                            open: '09:00',
                            close: '17:00',
                            isOpen: true
                        },
                        friday: {
                            open: '09:00',
                            close: '12:00',
                            isOpen: true
                        },
                        saturday: {
                            open: '09:00',
                            close: '17:00',
                            isOpen: true
                        }
                    }
                };
                mockUser.subscription = {
                    planId: 'real-estate-office',
                    planName: 'باقة المكتب العقاري',
                    planType: 'business',
                    startDate: new Date(),
                    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                    isActive: true,
                    autoRenew: true,
                    features: [
                        '30 إعلان شهرياً',
                        'شارة موثقة',
                        'أولوية في النتائج',
                        'دعم فني مخصص'
                    ]
                };
            }
            // حفظ الجلسة
            const session = {
                token: 'mock-token-' + Date.now(),
                refreshToken: 'mock-refresh-token-' + Date.now(),
                expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
                user: mockUser
            };
            if (credentials.rememberMe) {
                localStorage.setItem('userSession', JSON.stringify(session));
            }
            dispatch({
                type: 'LOGIN_SUCCESS',
                payload: mockUser
            });
        } catch (error) {
            dispatch({
                type: 'LOGIN_FAILURE',
                payload: 'فشل في تسجيل الدخول'
            });
        }
    };
    const register = async (data)=>{
        dispatch({
            type: 'LOGIN_START'
        });
        try {
            // محاكاة API call
            await new Promise((resolve)=>setTimeout(resolve, 1500));
            // التحقق من صحة البيانات
            if (data.password !== data.confirmPassword) {
                throw new Error('كلمات المرور غير متطابقة');
            }
            // إنشاء مستخدم جديد
            const newUser = {
                id: 'user-' + Date.now(),
                email: data.email,
                phone: data.phone,
                name: data.name,
                userType: data.userType,
                isVerified: false,
                createdAt: new Date(),
                lastLogin: new Date(),
                stats: {
                    totalAds: 0,
                    activeAds: 0,
                    expiredAds: 0,
                    totalViews: 0,
                    totalContacts: 0,
                    successfulDeals: 0
                },
                settings: {
                    language: 'ar',
                    notifications: {
                        email: true,
                        sms: true,
                        push: true,
                        marketing: false
                    },
                    privacy: {
                        showPhone: true,
                        showEmail: false,
                        allowMessages: true,
                        showOnlineStatus: true
                    },
                    preferences: {
                        currency: 'SYP',
                        theme: 'light',
                        autoSave: true
                    }
                },
                individualInfo: data.individualInfo,
                businessInfo: data.businessInfo,
                realEstateOfficeInfo: data.realEstateOfficeInfo
            };
            dispatch({
                type: 'LOGIN_SUCCESS',
                payload: newUser
            });
        } catch (error) {
            dispatch({
                type: 'LOGIN_FAILURE',
                payload: error instanceof Error ? error.message : 'فشل في إنشاء الحساب'
            });
        }
    };
    const logout = ()=>{
        // إزالة جميع بيانات المستخدم من localStorage
        localStorage.removeItem('userSession');
        localStorage.removeItem('isLoggedIn');
        localStorage.removeItem('currentUser');
        // إزالة إعدادات المستخدم المخصصة
        localStorage.removeItem('userSettings');
        localStorage.removeItem('userPreferences');
        // تحديث حالة المصادقة
        dispatch({
            type: 'LOGOUT'
        });
        // إعادة توجيه إلى الصفحة الرئيسية فقط
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
    };
    const updateProfile = async (updates)=>{
        try {
            dispatch({
                type: 'UPDATE_PROFILE',
                payload: updates
            });
            // تحديث الجلسة المحفوظة
            const sessionData = localStorage.getItem('userSession');
            if (sessionData) {
                const session = JSON.parse(sessionData);
                session.user = {
                    ...session.user,
                    ...updates
                };
                localStorage.setItem('userSession', JSON.stringify(session));
            }
        } catch (error) {
            dispatch({
                type: 'SET_ERROR',
                payload: 'فشل في تحديث الملف الشخصي'
            });
        }
    };
    const refreshToken = async ()=>{
        // محاكاة تجديد الرمز المميز
        try {
            await new Promise((resolve)=>setTimeout(resolve, 500));
        // تحديث الجلسة
        } catch (error) {
            logout();
        }
    };
    const deleteAccount = async ()=>{
        try {
            // محاكاة حذف الحساب
            await new Promise((resolve)=>setTimeout(resolve, 1000));
            logout();
        } catch (error) {
            dispatch({
                type: 'SET_ERROR',
                payload: 'فشل في حذف الحساب'
            });
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: {
            ...state,
            login,
            register,
            logout,
            updateProfile,
            refreshToken,
            deleteAccount
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 715,
        columnNumber: 5
    }, this);
};
const useAuth = ()=>{
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
}}),
"[project]/src/components/Logo.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/image.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)");
'use client';
;
;
;
;
const Logo = ({ variant = 'transparent', size = 'md', className = '', showText = true, href = '/', textColor = 'primary', isFooter = false })=>{
    const [imageError, setImageError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const sizeClasses = {
        xs: 'h-6 w-auto',
        sm: 'h-8 w-auto',
        md: 'h-10 w-auto',
        lg: 'h-12 w-auto',
        xl: 'h-24 w-auto',
        loading: 'h-32 w-auto'
    };
    const textSizeClasses = {
        xs: 'text-base',
        sm: 'text-lg',
        md: 'text-xl',
        lg: 'text-2xl',
        xl: 'text-3xl',
        loading: 'text-4xl'
    };
    const textColorClasses = {
        primary: 'text-green-600',
        white: 'text-white',
        gray: 'text-gray-300',
        yellow: 'text-yellow-400'
    };
    // مسار الشعار حسب النوع
    const logoSrc = variant === 'white' ? '/images/Adsız tasarım (3)_page-0001-Photoroom.png' : '/images/Adsız tasarım (3)_page-0001-Photoroom.png';
    const LogoContent = ()=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: `flex items-center gap-3 ${className}`,
            children: [
                !imageError ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    src: logoSrc,
                    alt: "من المالك",
                    width: 120,
                    height: 60,
                    className: `${sizeClasses[size]} object-contain`,
                    priority: true,
                    onError: ()=>setImageError(true)
                }, void 0, false, {
                    fileName: "[project]/src/components/Logo.tsx",
                    lineNumber: 60,
                    columnNumber: 9
                }, this) : // Fallback logo
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: `${sizeClasses[size]} bg-primary-600 rounded-lg flex items-center justify-center text-white font-bold`,
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-lg",
                        children: "🤝"
                    }, void 0, false, {
                        fileName: "[project]/src/components/Logo.tsx",
                        lineNumber: 72,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/Logo.tsx",
                    lineNumber: 71,
                    columnNumber: 9
                }, this),
                showText && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "hidden sm:block",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `font-bold ${textSizeClasses[size]}`,
                            style: {
                                fontFamily: 'Cairo, sans-serif',
                                color: textColor === 'white' ? '#fde047' : textColor === 'yellow' ? '#fde047' : '#10b981'
                            },
                            children: "من المالك"
                        }, void 0, false, {
                            fileName: "[project]/src/components/Logo.tsx",
                            lineNumber: 77,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: `text-xs text-gray-600 mt-0.5 ${isFooter ? '-ml-4' : '-ml-8'}`,
                            style: {
                                fontFamily: 'Cairo, sans-serif'
                            },
                            children: "موقع الإعلانات المبوبة"
                        }, void 0, false, {
                            fileName: "[project]/src/components/Logo.tsx",
                            lineNumber: 87,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/Logo.tsx",
                    lineNumber: 76,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/Logo.tsx",
            lineNumber: 58,
            columnNumber: 5
        }, this);
    if (href) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            href: href,
            className: "flex items-center",
            onClick: (e)=>{
                // منع التنقل إذا كان المستخدم في الصفحة الرئيسية بالفعل
                if ("TURBOPACK compile-time falsy", 0) {
                    "TURBOPACK unreachable";
                }
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(LogoContent, {}, void 0, false, {
                fileName: "[project]/src/components/Logo.tsx",
                lineNumber: 112,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/Logo.tsx",
            lineNumber: 102,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(LogoContent, {}, void 0, false, {
        fileName: "[project]/src/components/Logo.tsx",
        lineNumber: 117,
        columnNumber: 10
    }, this);
};
const __TURBOPACK__default__export__ = Logo;
}}),
"[project]/src/components/LoadingScreen.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "NavigationLoader": (()=>NavigationLoader),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Logo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/components/Logo.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
const LoadingScreen = ({ isLoading, onComplete })=>{
    const [progress, setProgress] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [isVisible, setIsVisible] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(isLoading);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isLoading) {
            setIsVisible(true);
            setProgress(0);
            // محاكاة تقدم التحميل السريع
            const interval = setInterval(()=>{
                setProgress((prev)=>{
                    if (prev >= 100) {
                        clearInterval(interval);
                        // تأخير قصير جداً قبل إخفاء الشاشة
                        setTimeout(()=>{
                            setIsVisible(false);
                            onComplete?.();
                        }, 100);
                        return 100;
                    }
                    return prev + Math.random() * 50; // زيادة سرعة التقدم
                });
            }, 50); // تقليل الفترة الزمنية
            return ()=>clearInterval(interval);
        }
    }, [
        isLoading,
        onComplete
    ]);
    if (!isVisible) return null;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 bg-black bg-opacity-30 z-50 flex items-center justify-center backdrop-blur-sm",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex flex-col items-center justify-center",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0 -m-20",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-80 h-80 bg-white/25 rounded-full blur-3xl animate-pulse"
                        }, void 0, false, {
                            fileName: "[project]/src/components/LoadingScreen.tsx",
                            lineNumber: 51,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/LoadingScreen.tsx",
                        lineNumber: 50,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0 -m-16",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-64 h-64 bg-white/35 rounded-full blur-2xl animate-pulse",
                            style: {
                                animationDelay: '0.3s'
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/LoadingScreen.tsx",
                            lineNumber: 54,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/LoadingScreen.tsx",
                        lineNumber: 53,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0 -m-12",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-48 h-48 bg-white/45 rounded-full blur-xl animate-pulse",
                            style: {
                                animationDelay: '0.6s'
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/LoadingScreen.tsx",
                            lineNumber: 57,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/LoadingScreen.tsx",
                        lineNumber: 56,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0 -m-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-32 h-32 bg-white/55 rounded-full blur-lg animate-pulse",
                            style: {
                                animationDelay: '0.9s'
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/LoadingScreen.tsx",
                            lineNumber: 60,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/LoadingScreen.tsx",
                        lineNumber: 59,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0 -m-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "w-24 h-24 bg-white/40 rounded-full blur-md animate-pulse",
                            style: {
                                animationDelay: '1.2s'
                            }
                        }, void 0, false, {
                            fileName: "[project]/src/components/LoadingScreen.tsx",
                            lineNumber: 63,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/LoadingScreen.tsx",
                        lineNumber: 62,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "relative z-10",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "transform transition-all duration-1000 ease-in-out animate-pulse",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "filter drop-shadow-2xl",
                                style: {
                                    filter: 'drop-shadow(0 0 20px rgba(255, 255, 255, 0.8)) drop-shadow(0 0 40px rgba(255, 255, 255, 0.6)) drop-shadow(0 0 60px rgba(255, 255, 255, 0.4))'
                                },
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$Logo$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                    variant: "transparent",
                                    size: "loading",
                                    showText: false
                                }, void 0, false, {
                                    fileName: "[project]/src/components/LoadingScreen.tsx",
                                    lineNumber: 70,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/LoadingScreen.tsx",
                                lineNumber: 69,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/LoadingScreen.tsx",
                            lineNumber: 68,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/LoadingScreen.tsx",
                        lineNumber: 67,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/LoadingScreen.tsx",
                lineNumber: 48,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/LoadingScreen.tsx",
            lineNumber: 46,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/LoadingScreen.tsx",
        lineNumber: 44,
        columnNumber: 5
    }, this);
};
const NavigationLoader = ()=>{
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [previousPath, setPreviousPath] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(pathname);
    const [isMounted, setIsMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setIsMounted(true);
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (isMounted && pathname !== previousPath) {
            setIsLoading(true);
            setPreviousPath(pathname);
        }
    }, [
        pathname,
        previousPath,
        isMounted
    ]);
    const handleLoadingComplete = ()=>{
        setIsLoading(false);
    };
    if (!isMounted) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(LoadingScreen, {
        isLoading: isLoading,
        onComplete: handleLoadingComplete
    }, void 0, false, {
        fileName: "[project]/src/components/LoadingScreen.tsx",
        lineNumber: 107,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = LoadingScreen;
}}),
"[project]/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component, client modules ssr)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__89fe33._.js.map