'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { PhoneIcon, EmailIcon, WhatsAppIcon, LocationIcon } from '@/components/ContactIcons';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    type: 'general'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // محاكاة إرسال النموذج
    await new Promise(resolve => setTimeout(resolve, 2000));

    setIsSubmitting(false);
    setSubmitted(true);
  };

  const contactMethods = [
    {
      icon: '📞',
      title: 'الهاتف',
      value: '+963-11-123-4567',
      description: 'متاح 24/7 للدعم الفني',
      action: 'tel:+963-11-123-4567'
    },
    {
      icon: '✉️',
      title: 'البريد الإلكتروني',
      value: '<EMAIL>',
      description: 'نرد خلال 24 ساعة',
      action: 'mailto:<EMAIL>'
    },
    {
      icon: '💬',
      title: 'واتساب',
      value: '+963 988 652 401',
      description: 'دردشة مباشرة',
      action: 'https://wa.me/963988652401'
    },
    {
      icon: '📍',
      title: 'العنوان',
      value: 'دمشق - المزة - بناء المهندسين',
      description: 'مكتبنا الرئيسي',
      action: '#'
    }
  ];

  const departments = [
    { id: 'general', name: 'استفسار عام' },
    { id: 'technical', name: 'دعم فني' },
    { id: 'billing', name: 'الفوترة والدفع' },
    { id: 'business', name: 'الحسابات التجارية' },
    { id: 'report', name: 'إبلاغ عن مشكلة' },
    { id: 'partnership', name: 'الشراكات' }
  ];

  if (submitted) {
    return (
      <div className="min-h-screen">
        <Header />
        <main className="py-16">
          <div className="container mx-auto px-4 text-center">
            <div className="max-w-md mx-auto bg-white rounded-xl shadow-lg p-8">
              <div className="text-6xl mb-4">✅</div>
              <h2 className="text-2xl font-bold text-gray-800 mb-4">تم إرسال رسالتك بنجاح!</h2>
              <p className="text-gray-600 mb-6">
                شكراً لتواصلك معنا. سنرد عليك خلال 24 ساعة.
              </p>
              <button
                onClick={() => setSubmitted(false)}
                className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
              >
                إرسال رسالة أخرى
              </button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Header />
      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-16">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">تواصل معنا</h1>
            <p className="text-xl md:text-2xl text-primary-100 max-w-3xl mx-auto">
              نحن هنا لمساعدتك في أي استفسار أو مشكلة تواجهها
            </p>
          </div>
        </section>

        {/* Contact Methods */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-gray-800 text-center mb-12">طرق التواصل</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {contactMethods.map((method, index) => (
                <a
                  key={index}
                  href={method.action}
                  className="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow group"
                >
                  <div className="text-4xl mb-4 group-hover:scale-110 transition-transform">
                    {method.icon}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">{method.title}</h3>
                  <p className="text-primary-600 font-medium mb-2">{method.value}</p>
                  <p className="text-sm text-gray-600">{method.description}</p>
                </a>
              ))}
            </div>
          </div>
        </section>

        {/* Contact Form */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-800 mb-4">أرسل لنا رسالة</h2>
                <p className="text-lg text-gray-600">املأ النموذج أدناه وسنتواصل معك قريباً</p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                {/* Contact Form */}
                <div className="bg-gray-50 rounded-xl p-8">
                  <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          الاسم الكامل *
                        </label>
                        <input
                          type="text"
                          required
                          value={formData.name}
                          onChange={(e) => handleInputChange('name', e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                          placeholder="أدخل اسمك الكامل"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          البريد الإلكتروني *
                        </label>
                        <input
                          type="email"
                          required
                          value={formData.email}
                          onChange={(e) => handleInputChange('email', e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          رقم الهاتف
                        </label>
                        <input
                          type="tel"
                          value={formData.phone}
                          onChange={(e) => handleInputChange('phone', e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                          placeholder="+963 9XX XXX XXX"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          نوع الاستفسار *
                        </label>
                        <select
                          required
                          value={formData.type}
                          onChange={(e) => handleInputChange('type', e.target.value)}
                          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        >
                          {departments.map((dept) => (
                            <option key={dept.id} value={dept.id}>{dept.name}</option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        موضوع الرسالة *
                      </label>
                      <input
                        type="text"
                        required
                        value={formData.subject}
                        onChange={(e) => handleInputChange('subject', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="موضوع مختصر للرسالة"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        نص الرسالة *
                      </label>
                      <textarea
                        required
                        rows={6}
                        value={formData.message}
                        onChange={(e) => handleInputChange('message', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        placeholder="اكتب رسالتك هنا..."
                      />
                    </div>

                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full bg-primary-600 text-white py-4 rounded-lg hover:bg-primary-700 transition-colors font-semibold text-lg disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmitting ? (
                        <span className="flex items-center justify-center gap-2">
                          <div className="animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full"></div>
                          جاري الإرسال...
                        </span>
                      ) : (
                        'إرسال الرسالة'
                      )}
                    </button>
                  </form>
                </div>

                {/* Additional Info */}
                <div className="space-y-8">
                  <div className="bg-primary-50 border border-primary-200 rounded-xl p-6">
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">ساعات العمل</h3>
                    <div className="space-y-2 text-gray-700">
                      <div className="flex justify-between">
                        <span>الأحد - الخميس:</span>
                        <span>9:00 ص - 6:00 م</span>
                      </div>
                      <div className="flex justify-between">
                        <span>الجمعة:</span>
                        <span>9:00 ص - 2:00 م</span>
                      </div>
                      <div className="flex justify-between">
                        <span>السبت:</span>
                        <span>مغلق</span>
                      </div>
                      <div className="mt-4 pt-4 border-t border-primary-200">
                        <div className="flex justify-between font-semibold text-primary-700">
                          <span>الدعم الفني:</span>
                          <span>24/7</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6">
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">نصائح للحصول على رد سريع</h3>
                    <ul className="space-y-2 text-gray-700 text-sm">
                      <li>• اكتب موضوعاً واضحاً ومحدداً</li>
                      <li>• أرفق لقطات شاشة إذا كانت المشكلة تقنية</li>
                      <li>• اذكر رقم الإعلان إذا كان الاستفسار متعلقاً بإعلان معين</li>
                      <li>• تأكد من صحة بريدك الإلكتروني</li>
                    </ul>
                  </div>

                  <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">الدعم السريع</h3>
                    <p className="text-gray-700 mb-4">للمساعدة الفورية، يمكنك:</p>
                    <div className="space-y-3">
                      <a
                        href="https://wa.me/963988652401"
                        className="flex items-center gap-3 p-3 bg-green-100 rounded-lg hover:bg-green-200 transition-colors"
                      >
                        <span className="text-2xl">💬</span>
                        <div>
                          <div className="font-medium">واتساب</div>
                          <div className="text-sm text-gray-600">رد فوري</div>
                        </div>
                      </a>
                      <a
                        href="tel:+963-11-123-4567"
                        className="flex items-center gap-3 p-3 bg-blue-100 rounded-lg hover:bg-blue-200 transition-colors"
                      >
                        <span className="text-2xl">📞</span>
                        <div>
                          <div className="font-medium">اتصال مباشر</div>
                          <div className="text-sm text-gray-600">متاح 24/7</div>
                        </div>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Quick Links */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">قد تجد إجابتك هنا</h2>
            <p className="text-gray-600 mb-8">تحقق من الأسئلة الشائعة قبل التواصل معنا</p>
            <div className="flex flex-col md:flex-row gap-4 justify-center">
              <a
                href="/faq"
                className="bg-primary-600 text-white px-8 py-3 rounded-lg hover:bg-primary-700 transition-colors"
              >
                الأسئلة الشائعة
              </a>
              <a
                href="/safety"
                className="border border-primary-600 text-primary-600 px-8 py-3 rounded-lg hover:bg-primary-50 transition-colors"
              >
                نصائح الأمان
              </a>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
