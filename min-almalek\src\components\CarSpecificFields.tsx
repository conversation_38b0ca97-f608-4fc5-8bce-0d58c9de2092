'use client';

import { useState } from 'react';

interface CarData {
  listingType: 'sale' | 'rent';
  rentPeriod?: 'daily' | 'weekly' | 'monthly';
  carType?: 'private' | 'commercial';
  bodyType?: 'sedan' | 'coupe' | 'truck' | 'suv' | 'hatchback' | 'wagon';
  driveType?: 'front' | 'rear' | 'awd' | '4wd';
  engineSize?: string;
  fuelType?: 'gasoline' | 'diesel' | 'electric' | 'hybrid';
  transmission: 'automatic' | 'manual';
  year: number;
  brand: string;
  model: string;
  features: string[];
  insurance?: 'full' | 'basic' | 'none';
  insuranceDetails?: string;
}

interface CarSpecificFieldsProps {
  onDataChange: (data: Partial<CarData>) => void;
  initialData?: Partial<CarData>;
}

export default function CarSpecificFields({ onDataChange, initialData = {} }: CarSpecificFieldsProps) {
  const [carData, setCarData] = useState<Partial<CarData>>(initialData);

  const handleChange = (field: keyof CarData, value: any) => {
    const newData = { ...carData, [field]: value };
    setCarData(newData);
    onDataChange(newData);
  };

  const handleFeatureToggle = (feature: string) => {
    const currentFeatures = carData.features || [];
    const newFeatures = currentFeatures.includes(feature)
      ? currentFeatures.filter(f => f !== feature)
      : [...currentFeatures, feature];

    handleChange('features', newFeatures);
  };



  const listingTypes = [
    { id: 'sale', name: 'للبيع', icon: '💰', description: 'بيع السيارة نهائياً' },
    { id: 'rent', name: 'للإيجار', icon: '🔄', description: 'إيجار السيارة لفترة محددة' }
  ];

  const rentPeriods = [
    { id: 'daily', name: 'إيجار يومي', icon: '📅', description: 'إيجار لمدة يوم أو أكثر' },
    { id: 'weekly', name: 'إيجار أسبوعي', icon: '📆', description: 'إيجار لمدة أسبوع أو أكثر' },
    { id: 'monthly', name: 'إيجار شهري', icon: '🗓️', description: 'إيجار لمدة شهر أو أكثر' }
  ];

  const carTypes = [
    { id: 'private', name: 'خصوصي', icon: '🚗', description: 'سيارة للاستخدام الشخصي' },
    { id: 'commercial', name: 'عمومي', icon: '🚕', description: 'سيارة للاستخدام التجاري' }
  ];

  const bodyTypes = [
    { id: 'sedan', name: 'سيدان', icon: '🚗' },
    { id: 'coupe', name: 'كوبيه', icon: '🏎️' },
    { id: 'truck', name: 'شاحنة', icon: '🚚' },
    { id: 'suv', name: 'SUV', icon: '🚙' },
    { id: 'hatchback', name: 'هاتشباك', icon: '🚘' },
    { id: 'wagon', name: 'ستيشن واجن', icon: '🚐' }
  ];

  const driveTypes = [
    { id: 'front', name: 'دفع أمامي', icon: '⬆️' },
    { id: 'rear', name: 'دفع خلفي', icon: '⬇️' },
    { id: 'awd', name: 'دفع رباعي دائم', icon: '🔄' },
    { id: '4wd', name: 'دفع رباعي', icon: '🚜' }
  ];

  const fuelTypes = [
    { id: 'gasoline', name: 'بنزين', icon: '⛽' },
    { id: 'diesel', name: 'ديزل', icon: '🛢️' },
    { id: 'electric', name: 'كهرباء', icon: '🔋' },
    { id: 'hybrid', name: 'هجين', icon: '🌱' }
  ];

  const transmissionTypes = [
    { id: 'automatic', name: 'أوتوماتيك', icon: '🔄' },
    { id: 'manual', name: 'عادي', icon: '🎛️' }
  ];

  const insuranceTypes = [
    { id: 'full', name: 'تأمين شامل', icon: '🛡️', description: 'تأمين شامل ضد جميع المخاطر' },
    { id: 'basic', name: 'تأمين أساسي', icon: '🔰', description: 'تأمين أساسي ضد الحوادث' },
    { id: 'none', name: 'بدون تأمين', icon: '⚠️', description: 'المستأجر مسؤول عن أي أضرار' }
  ];



  const carFeatures = [
    // مميزات الراحة والأمان
    'مكيف هواء', 'نوافذ كهربائية', 'مرايا كهربائية', 'قفل مركزي', 'إنذار',
    'ABS', 'وسائد هوائية', 'مثبت سرعة', 'نظام ملاحة GPS', 'بلوتوث',
    'كاميرا خلفية', 'حساسات ركن', 'فتحة سقف', 'مقاعد جلد', 'مقاعد كهربائية',
    'تدفئة مقاعد', 'شاشة لمس', 'نظام صوتي متطور', 'إضاءة LED', 'عجلات سبائك',

    // مميزات متقدمة
    'نظام تشغيل بدون مفتاح', 'شحن لاسلكي', 'مساعد ركن تلقائي', 'نظام تحذير النقطة العمياء',
    'نظام مراقبة ضغط الإطارات', 'نظام تحذير مغادرة المسار', 'فرامل طوارئ تلقائية',
    'نظام تحكم تكيفي بالسرعة', 'رؤية ليلية', 'نظام تحذير التصادم',

    // مميزات الترفيه والتكنولوجيا
    'Apple CarPlay', 'Android Auto', 'واي فاي', 'نظام صوتي بريميوم', 'مكبرات صوت متعددة',
    'شاشات خلفية للترفيه', 'نظام تحكم صوتي', 'مساعد ذكي',

    // مميزات السيارات الكلاسيكية
    'مكيف يدوي', 'راديو كلاسيك', 'عجلة قيادة خشبية', 'مقاعد قماش أصلية',
    'محرك مُعدل', 'عادم رياضي', 'تعديلات كلاسيكية', 'حالة أصلية'
  ];

  const currentYear = new Date().getFullYear();
  const startYear = 1970;
  const years = Array.from({ length: currentYear - startYear + 1 }, (_, i) => currentYear - i);

  return (
    <div className="space-y-6">
      {/* نوع الإعلان */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">نوع الإعلان</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {listingTypes.map(type => (
            <button
              key={type.id}
              type="button"
              onClick={() => handleChange('listingType', type.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                carData.listingType === type.id
                  ? 'border-primary-500 bg-primary-50 text-primary-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-3xl mb-2">{type.icon}</div>
              <div className="font-medium text-lg">{type.name}</div>
              <div className="text-xs text-gray-500 mt-1">{type.description}</div>
            </button>
          ))}
        </div>
      </div>

      {/* فترة التأجير - تظهر فقط عند اختيار التأجير */}
      {carData.listingType === 'rent' && (
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4">فترة التأجير</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {rentPeriods.map(period => (
              <button
                key={period.id}
                type="button"
                onClick={() => handleChange('rentPeriod', period.id)}
                className={`p-4 rounded-lg border-2 transition-all ${
                  carData.rentPeriod === period.id
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="text-2xl mb-2">{period.icon}</div>
                <div className="font-medium text-sm">{period.name}</div>
                <div className="text-xs text-gray-500 mt-1">{period.description}</div>
              </button>
            ))}
          </div>

          {/* نصيحة للإيجار */}
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800 flex items-center gap-2">
              <span className="text-lg">💡</span>
              <span>
                <strong>نصيحة:</strong> حدد السعر حسب فترة الإيجار المختارة (يومي/أسبوعي/شهري)
              </span>
            </p>
          </div>
        </div>
      )}

      {/* نوع السيارة - يظهر فقط للبيع */}
      {carData.listingType === 'sale' && (
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4">نوع السيارة</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {carTypes.map(type => (
              <button
                key={type.id}
                type="button"
                onClick={() => handleChange('carType', type.id)}
                className={`p-4 rounded-lg border-2 transition-all ${
                  carData.carType === type.id
                    ? 'border-orange-500 bg-orange-50 text-orange-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="text-3xl mb-2">{type.icon}</div>
                <div className="font-medium text-lg">{type.name}</div>
                <div className="text-xs text-gray-500 mt-1">{type.description}</div>
              </button>
            ))}
          </div>

          {/* نصيحة للبيع */}
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-800 flex items-center gap-2">
              <span className="text-lg">💡</span>
              <span>
                <strong>نصيحة للبيع:</strong> أضف صور واضحة ومتعددة، اذكر تاريخ الصيانة الأخيرة، وكن صادقاً في وصف حالة السيارة لجذب مشترين جديين
              </span>
            </p>
          </div>
        </div>
      )}

      {/* معلومات أساسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* الماركة */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            الماركة *
          </label>
          <select
            value={carData.brand || ''}
            onChange={(e) => handleChange('brand', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">اختر الماركة</option>

            {/* الماركات اليابانية والكورية */}
            <optgroup label="🇯🇵🇰🇷 الماركات اليابانية والكورية">
              <option value="تويوتا">تويوتا</option>
              <option value="هيونداي">هيونداي</option>
              <option value="كيا">كيا</option>
              <option value="نيسان">نيسان</option>
              <option value="هوندا">هوندا</option>
              <option value="مازدا">مازدا</option>
              <option value="سوزوكي">سوزوكي</option>
              <option value="ميتسوبيشي">ميتسوبيشي</option>
              <option value="سوبارو">سوبارو</option>
              <option value="إنفينيتي">إنفينيتي</option>
              <option value="لكزس">لكزس</option>
              <option value="أكورا">أكورا</option>
            </optgroup>

            {/* الماركات الألمانية */}
            <optgroup label="🇩🇪 الماركات الألمانية">
              <option value="مرسيدس">مرسيدس</option>
              <option value="BMW">BMW</option>
              <option value="أودي">أودي</option>
              <option value="فولكس واجن">فولكس واجن</option>
              <option value="بورش">بورش</option>
              <option value="مايباخ">مايباخ</option>
              <option value="مينى كوبر">مينى كوبر</option>
            </optgroup>

            {/* الماركات الأمريكية */}
            <optgroup label="🇺🇸 الماركات الأمريكية">
              <option value="فورد">فورد</option>
              <option value="شيفروليه">شيفروليه</option>
              <option value="كاديلاك">كاديلاك</option>
              <option value="جي إم سي">جي إم سي</option>
              <option value="دودج">دودج</option>
              <option value="كرايسلر">كرايسلر</option>
              <option value="جيب">جيب</option>
              <option value="لينكولن">لينكولن</option>
              <option value="بويك">بويك</option>
              <option value="هامر">هامر</option>
              <option value="تسلا">تسلا</option>
            </optgroup>

            {/* الماركات الأوروبية */}
            <optgroup label="🇪🇺 الماركات الأوروبية">
              <option value="بيجو">بيجو</option>
              <option value="رينو">رينو</option>
              <option value="سيتروين">سيتروين</option>
              <option value="داسيا">داسيا</option>
              <option value="فيات">فيات</option>
              <option value="ألفا روميو">ألفا روميو</option>
              <option value="لانسيا">لانسيا</option>
              <option value="سكودا">سكودا</option>
              <option value="سيات">سيات</option>
              <option value="أوبل">أوبل</option>
            </optgroup>

            {/* الماركات الفاخرة */}
            <optgroup label="👑 الماركات الفاخرة">
              <option value="فيراري">فيراري</option>
              <option value="لامبورغيني">لامبورغيني</option>
              <option value="مازيراتي">مازيراتي</option>
              <option value="لاند روفر">لاند روفر</option>
              <option value="جاكوار">جاكوار</option>
              <option value="رولز رويس">رولز رويس</option>
              <option value="بنتلي">بنتلي</option>
              <option value="أستون مارتن">أستون مارتن</option>
            </optgroup>

            {/* الماركات الأخرى */}
            <optgroup label="🌍 الماركات الأخرى">
              <option value="لادا">لادا</option>
              <option value="شيري">شيري</option>
              <option value="جيلي">جيلي</option>
              <option value="هافال">هافال</option>
              <option value="بي واي دي">بي واي دي</option>
              <option value="جريت وول">جريت وول</option>
              <option value="أخرى">أخرى</option>
            </optgroup>
          </select>
        </div>

        {/* الموديل */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            الموديل *
          </label>
          <input
            type="text"
            value={carData.model || ''}
            onChange={(e) => handleChange('model', e.target.value)}
            placeholder="مثال: كامري، إلنترا، X5"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>

        {/* سنة الصنع */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            سنة الصنع
          </label>
          <select
            value={carData.year || ''}
            onChange={(e) => handleChange('year', parseInt(e.target.value))}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">اختر السنة</option>

            {/* السنوات الحديثة (2020-2024) */}
            <optgroup label="🚗 السنوات الحديثة (2020-2024)">
              {years.filter(year => year >= 2020).map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </optgroup>

            {/* السنوات الحديثة نسبياً (2010-2019) */}
            <optgroup label="🚙 السنوات الحديثة نسبياً (2010-2019)">
              {years.filter(year => year >= 2010 && year < 2020).map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </optgroup>

            {/* السنوات المتوسطة (2000-2009) */}
            <optgroup label="🚕 السنوات المتوسطة (2000-2009)">
              {years.filter(year => year >= 2000 && year < 2010).map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </optgroup>

            {/* السنوات القديمة (1990-1999) */}
            <optgroup label="🚐 السنوات القديمة (1990-1999)">
              {years.filter(year => year >= 1990 && year < 2000).map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </optgroup>

            {/* السنوات الكلاسيكية (1980-1989) */}
            <optgroup label="🏎️ السنوات الكلاسيكية (1980-1989)">
              {years.filter(year => year >= 1980 && year < 1990).map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </optgroup>

            {/* السنوات الكلاسيكية القديمة (1970-1979) */}
            <optgroup label="🚗 السنوات الكلاسيكية القديمة (1970-1979)">
              {years.filter(year => year >= 1970 && year < 1980).map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </optgroup>
          </select>

          {carData.year && carData.year < 1990 && (
            <p className="text-sm text-amber-600 mt-2 flex items-center gap-1">
              <span>🏆</span>
              <span>سيارة كلاسيكية - قيمة تاريخية عالية!</span>
            </p>
          )}
        </div>


      </div>

      {/* نوع الهيكل */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">نوع الهيكل</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {bodyTypes.map(body => (
            <button
              key={body.id}
              type="button"
              onClick={() => handleChange('bodyType', body.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                carData.bodyType === body.id
                  ? 'border-purple-500 bg-purple-50 text-purple-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-2xl mb-2">{body.icon}</div>
              <div className="font-medium text-sm">{body.name}</div>
            </button>
          ))}
        </div>
      </div>

      {/* نوع الدفع */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">نوع الدفع</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {driveTypes.map(drive => (
            <button
              key={drive.id}
              type="button"
              onClick={() => handleChange('driveType', drive.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                carData.driveType === drive.id
                  ? 'border-indigo-500 bg-indigo-50 text-indigo-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-2xl mb-2">{drive.icon}</div>
              <div className="font-medium text-sm">{drive.name}</div>
            </button>
          ))}
        </div>
      </div>

      {/* سعة المحرك */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">سعة المحرك</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <input
            type="text"
            value={carData.engineSize || ''}
            onChange={(e) => handleChange('engineSize', e.target.value)}
            placeholder="مثال: 1.6L، 2.0L، 3.5L، V6، V8"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
          <div className="flex items-center text-sm text-gray-600">
            <span className="text-lg mr-2">🔧</span>
            <span>أدخل سعة المحرك باللتر أو نوع المحرك</span>
          </div>
        </div>
      </div>

      {/* نوع الوقود */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">نوع الوقود</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {fuelTypes.map(fuel => (
            <button
              key={fuel.id}
              type="button"
              onClick={() => handleChange('fuelType', fuel.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                carData.fuelType === fuel.id
                  ? 'border-green-500 bg-green-50 text-green-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-2xl mb-2">{fuel.icon}</div>
              <div className="font-medium text-sm">{fuel.name}</div>
            </button>
          ))}
        </div>
      </div>

      {/* ناقل الحركة */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">ناقل الحركة</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {transmissionTypes.map(transmission => (
            <button
              key={transmission.id}
              type="button"
              onClick={() => handleChange('transmission', transmission.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                carData.transmission === transmission.id
                  ? 'border-primary-500 bg-primary-50 text-primary-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="text-2xl mb-2">{transmission.icon}</div>
              <div className="font-medium text-sm">{transmission.name}</div>
            </button>
          ))}
        </div>
      </div>

      {/* التأمين - يظهر فقط للإيجار */}
      {carData.listingType === 'rent' && (
        <div>
          <h3 className="text-lg font-semibold text-gray-800 mb-4">التأمين</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {insuranceTypes.map(insurance => (
              <button
                key={insurance.id}
                type="button"
                onClick={() => handleChange('insurance', insurance.id)}
                className={`p-4 rounded-lg border-2 transition-all ${
                  carData.insurance === insurance.id
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="text-2xl mb-2">{insurance.icon}</div>
                <div className="font-medium text-sm">{insurance.name}</div>
                <div className="text-xs text-gray-500 mt-1">{insurance.description}</div>
              </button>
            ))}
          </div>

          {/* تفاصيل التأمين */}
          {carData.insurance && carData.insurance !== 'none' && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تفاصيل التأمين (اختياري)
              </label>
              <textarea
                value={carData.insuranceDetails || ''}
                onChange={(e) => handleChange('insuranceDetails', e.target.value)}
                placeholder="اكتب تفاصيل إضافية عن التأمين، مثل قيمة التحمل، الشركة المؤمنة، إلخ..."
                rows={3}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          )}

          {/* تحذير للتأمين */}
          <div className="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <p className="text-sm text-amber-800 flex items-center gap-2">
              <span className="text-lg">⚠️</span>
              <span>
                <strong>مهم:</strong> تأكد من صحة معلومات التأمين وأن جميع الأوراق سارية المفعول
              </span>
            </p>
          </div>
        </div>
      )}



      {/* المميزات الإضافية */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 mb-4">المميزات الإضافية</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          {carFeatures.map(feature => (
            <button
              key={feature}
              type="button"
              onClick={() => handleFeatureToggle(feature)}
              className={`p-3 rounded-lg border-2 transition-all text-sm ${
                carData.features?.includes(feature)
                  ? 'border-green-500 bg-green-50 text-green-700'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center gap-2">
                <span className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                  carData.features?.includes(feature)
                    ? 'border-green-500 bg-green-500 text-white'
                    : 'border-gray-300'
                }`}>
                  {carData.features?.includes(feature) && '✓'}
                </span>
                <span>{feature}</span>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
