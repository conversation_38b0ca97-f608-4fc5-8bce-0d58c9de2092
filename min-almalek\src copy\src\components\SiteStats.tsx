const SiteStats = () => {
  const stats = [
    {
      id: 1,
      title: 'إجمالي الإعلانات',
      value: '125,430',
      icon: '📝',
      color: 'bg-blue-500',
      change: '+12%',
      changeType: 'increase'
    },
    {
      id: 2,
      title: 'المستخدمين النشطين',
      value: '45,280',
      icon: '👥',
      color: 'bg-green-500',
      change: '+8%',
      changeType: 'increase'
    },
    {
      id: 3,
      title: 'الإعلانات المميزة',
      value: '8,920',
      icon: '⭐',
      color: 'bg-yellow-500',
      change: '+15%',
      changeType: 'increase'
    },
    {
      id: 4,
      title: 'المعاملات اليومية',
      value: '2,340',
      icon: '💰',
      color: 'bg-purple-500',
      change: '+5%',
      changeType: 'increase'
    }
  ];

  const categoryStats = [
    { name: 'العقارات', count: 45230, percentage: 36 },
    { name: 'السيارات', count: 28450, percentage: 23 },
    { name: 'الإلكترونيات', count: 22180, percentage: 18 },
    { name: 'الوظائف', count: 15670, percentage: 12 },
    { name: 'الخدمات', count: 8920, percentage: 7 },
    { name: 'أخرى', count: 4980, percentage: 4 }
  ];

  const locationStats = [
    { name: 'دمشق', count: 38450, percentage: 31 },
    { name: 'حلب', count: 28230, percentage: 22 },
    { name: 'حمص', count: 18670, percentage: 15 },
    { name: 'حماة', count: 12340, percentage: 10 },
    { name: 'اللاذقية', count: 15890, percentage: 13 },
    { name: 'أخرى', count: 11850, percentage: 9 }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">إحصائيات الموقع</h2>
          <p className="text-lg text-gray-600">أرقام حقيقية تعكس نشاط منصتنا</p>
        </div>

        {/* الإحصائيات الرئيسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {stats.map((stat) => (
            <div key={stat.id} className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center text-white text-2xl`}>
                  {stat.icon}
                </div>
                <span className={`text-sm font-medium px-2 py-1 rounded-full ${
                  stat.changeType === 'increase' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {stat.change}
                </span>
              </div>
              <div className="text-3xl font-bold text-gray-800 mb-2">{stat.value}</div>
              <div className="text-gray-600">{stat.title}</div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* إحصائيات التصنيفات */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-800 mb-6">الإعلانات حسب التصنيف</h3>
            <div className="space-y-4">
              {categoryStats.map((category, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-4 h-4 bg-primary-500 rounded-full"></div>
                    <span className="font-medium text-gray-800">{category.name}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${category.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 w-16 text-left">
                      {category.count.toLocaleString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* إحصائيات المحافظات */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-800 mb-6">الإعلانات حسب المحافظة</h3>
            <div className="space-y-4">
              {locationStats.map((location, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                    <span className="font-medium text-gray-800">{location.name}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${location.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 w-16 text-left">
                      {location.count.toLocaleString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* إحصائيات إضافية */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl p-6 text-white text-center">
            <div className="text-3xl mb-2">⚡</div>
            <div className="text-2xl font-bold mb-2">99.9%</div>
            <div className="text-primary-100">وقت التشغيل</div>
          </div>

          <div className="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-6 text-white text-center">
            <div className="text-3xl mb-2">🚀</div>
            <div className="text-2xl font-bold mb-2">2.3 ثانية</div>
            <div className="text-green-100">متوسط سرعة التحميل</div>
          </div>

          <div className="bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl p-6 text-white text-center">
            <div className="text-3xl mb-2">📱</div>
            <div className="text-2xl font-bold mb-2">78%</div>
            <div className="text-orange-100">مستخدمي الهاتف المحمول</div>
          </div>
        </div>

        {/* رسم بياني للنمو */}
        <div className="mt-12 bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-xl font-bold text-gray-800 mb-6">نمو الإعلانات الشهري</h3>
          <div className="grid grid-cols-12 gap-2 h-40 items-end">
            {[65, 78, 82, 88, 95, 102, 108, 115, 122, 128, 135, 142].map((height, index) => (
              <div key={index} className="flex flex-col items-center">
                <div 
                  className="bg-primary-500 rounded-t-sm transition-all duration-1000 hover:bg-primary-600 cursor-pointer"
                  style={{ height: `${height}px` }}
                  title={`الشهر ${index + 1}: ${(height * 100).toLocaleString()} إعلان`}
                ></div>
                <span className="text-xs text-gray-500 mt-2">
                  {['ي', 'ف', 'م', 'أ', 'م', 'ي', 'ي', 'أ', 'س', 'أ', 'ن', 'د'][index]}
                </span>
              </div>
            ))}
          </div>
          <div className="mt-4 text-center text-sm text-gray-600">
            نمو مستمر في عدد الإعلانات المنشورة شهرياً
          </div>
        </div>

        {/* شهادات المستخدمين */}
        <div className="mt-12">
          <h3 className="text-2xl font-bold text-gray-800 text-center mb-8">ماذا يقول مستخدمونا</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                name: 'أحمد محمد',
                role: 'مستخدم فردي',
                comment: 'منصة رائعة وسهلة الاستخدام، تمكنت من بيع سيارتي خلال أسبوع واحد!',
                rating: 5
              },
              {
                name: 'شركة العقارات الذهبية',
                role: 'شركة عقارات',
                comment: 'أفضل منصة للإعلانات العقارية في سوريا، عملاؤنا يجدوننا بسهولة.',
                rating: 5
              },
              {
                name: 'سارة أحمد',
                role: 'مستخدمة فردية',
                comment: 'وجدت الوظيفة المناسبة من خلال الموقع، خدمة ممتازة ودعم فني رائع.',
                rating: 5
              }
            ].map((testimonial, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <span key={i} className="text-yellow-400 text-lg">⭐</span>
                  ))}
                </div>
                <p className="text-gray-700 mb-4 italic">"{testimonial.comment}"</p>
                <div>
                  <div className="font-semibold text-gray-800">{testimonial.name}</div>
                  <div className="text-sm text-gray-600">{testimonial.role}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SiteStats;
