'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import { DataService } from '@/lib/data';
import type { Ad } from '@/lib/data';

export default function FavoritesPage() {
  const [favoriteAds, setFavoriteAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    loadFavorites();
  }, []);

  const loadFavorites = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // محاكاة جلب الإعلانات المفضلة
    const allAds = DataService.getAds({ page: 1, limit: 50 }).ads;
    const favorites = allAds.slice(0, 8); // محاكاة 8 إعلانات مفضلة
    
    setFavoriteAds(favorites);
    setLoading(false);
  };

  const removeFavorite = (adId: string) => {
    setFavoriteAds(prev => prev.filter(ad => ad.id !== adId));
  };

  const clearAllFavorites = () => {
    if (confirm('هل أنت متأكد من حذف جميع المفضلة؟')) {
      setFavoriteAds([]);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* العنوان والإحصائيات */}
        <div className="mb-8">
          <div className="text-center mb-6">
            <h1 className="text-4xl font-bold text-gray-800 mb-4 flex items-center justify-center gap-3">
              <svg 
                className="w-10 h-10 text-green-600" 
                fill="currentColor" 
                viewBox="0 0 24 24"
                style={{
                  filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.6))'
                }}
              >
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
              </svg>
              إعلاناتي المفضلة
            </h1>
            <p className="text-lg text-gray-600">
              جميع الإعلانات التي أضفتها إلى المفضلة
            </p>
          </div>

          {/* إحصائيات */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-white rounded-lg p-4 text-center shadow-sm border border-green-200">
              <div className="text-2xl font-bold text-green-600">{favoriteAds.length}</div>
              <div className="text-sm text-gray-600">إعلان مفضل</div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center shadow-sm border border-blue-200">
              <div className="text-2xl font-bold text-blue-600">
                {favoriteAds.filter(ad => ad.category === 'real-estate').length}
              </div>
              <div className="text-sm text-gray-600">عقارات</div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center shadow-sm border border-red-200">
              <div className="text-2xl font-bold text-red-600">
                {favoriteAds.filter(ad => ad.category === 'cars').length}
              </div>
              <div className="text-sm text-gray-600">سيارات</div>
            </div>
          </div>
        </div>

        {/* شريط التحكم */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <svg className="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
              </svg>
              <span className="font-medium text-gray-800">
                المفضلة ({favoriteAds.length} إعلان)
              </span>
            </div>
            
            <div className="flex items-center gap-4">
              {/* أزرار العرض */}
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-lg transition-colors ${
                    viewMode === 'grid' 
                      ? 'bg-primary-600 text-white' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                  title="عرض شبكي"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M3 3h7v7H3V3zm0 11h7v7H3v-7zm11-11h7v7h-7V3zm0 11h7v7h-7v-7z"/>
                  </svg>
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-lg transition-colors ${
                    viewMode === 'list' 
                      ? 'bg-primary-600 text-white' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                  title="عرض قائمة"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M3 13h2v-2H3v2zm0 4h2v-2H3v2zm0-8h2V7H3v2zm4 4h14v-2H7v2zm0 4h14v-2H7v2zM7 7v2h14V7H7z"/>
                  </svg>
                </button>
              </div>

              {favoriteAds.length > 0 && (
                <button
                  onClick={clearAllFavorites}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
                >
                  حذف الكل
                </button>
              )}
            </div>
          </div>
        </div>

        {/* المحتوى */}
        {loading ? (
          <div className={`grid gap-6 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3' 
              : 'grid-cols-1'
          }`}>
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                <div className="h-48 bg-gray-200"></div>
                <div className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : favoriteAds.length > 0 ? (
          <div className={`grid gap-6 ${
            viewMode === 'grid' 
              ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3' 
              : 'grid-cols-1'
          }`}>
            {favoriteAds.map((ad) => (
              <div key={ad.id} className="relative group">
                {/* زر إزالة من المفضلة */}
                <button
                  onClick={() => removeFavorite(ad.id)}
                  className="absolute top-2 left-2 z-10 w-8 h-8 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-red-600"
                  title="إزالة من المفضلة"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
                  </svg>
                </button>

                {/* شارة المفضلة */}
                <div className="absolute top-2 right-2 z-10">
                  <div className="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center">
                    <svg 
                      className="w-4 h-4" 
                      fill="currentColor" 
                      viewBox="0 0 24 24"
                      style={{
                        filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.8))'
                      }}
                    >
                      <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                    </svg>
                  </div>
                </div>

                <AdCard 
                  ad={ad} 
                  viewMode={viewMode}
                />
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">💚</div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد إعلانات مفضلة</h3>
            <p className="text-gray-600 mb-6">لم تقم بإضافة أي إعلانات إلى المفضلة بعد</p>
            <a
              href="/"
              className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors inline-block"
            >
              تصفح الإعلانات
            </a>
          </div>
        )}

        {/* نصائح */}
        {favoriteAds.length > 0 && (
          <div className="mt-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl text-white p-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">💡 نصائح للمفضلة</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-green-100">
                <div className="text-center">
                  <div className="text-3xl mb-2">🔔</div>
                  <h3 className="font-semibold mb-1">تنبيهات الأسعار</h3>
                  <p className="text-sm">احصل على تنبيه عند تغيير أسعار إعلاناتك المفضلة</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl mb-2">📊</div>
                  <h3 className="font-semibold mb-1">مقارنة الأسعار</h3>
                  <p className="text-sm">قارن بين الإعلانات المفضلة لاتخاذ القرار الأفضل</p>
                </div>
                <div className="text-center">
                  <div className="text-3xl mb-2">📱</div>
                  <h3 className="font-semibold mb-1">مشاركة سهلة</h3>
                  <p className="text-sm">شارك إعلاناتك المفضلة مع الأصدقاء والعائلة</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>

      <Footer />
    </div>
  );
}
