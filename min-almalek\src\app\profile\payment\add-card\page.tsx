'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { useNotificationHelpers } from '@/hooks/useNotificationHelpers';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ClientOnlyWrapper from '@/components/ClientOnlyWrapper';

export default function AddCardPage() {
  const { user } = useAuth();
  const { notifySuccess, notifyError } = useNotificationHelpers();
  const [isLoading, setIsLoading] = useState(false);
  
  const [formData, setFormData] = useState({
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    cardholderName: '',
    isDefault: false
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.cardNumber || !formData.expiryMonth || !formData.expiryYear || !formData.cvv || !formData.cardholderName) {
      notifyError('يرجى ملء جميع الحقول');
      return;
    }

    setIsLoading(true);
    try {
      // هنا سيتم إضافة منطق حفظ البطاقة
      await new Promise(resolve => setTimeout(resolve, 2000)); // محاكاة API call
      notifySuccess('تم إضافة البطاقة بنجاح');
      // إعادة توجيه إلى صفحة الملف الشخصي
      window.location.href = '/profile';
    } catch (error) {
      notifyError('حدث خطأ أثناء إضافة البطاقة');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const formatCardNumber = (value: string) => {
    // إزالة جميع المسافات والأحرف غير الرقمية
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    // إضافة مسافة كل 4 أرقام
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const getCardType = (number: string) => {
    const num = number.replace(/\s/g, '');
    if (num.startsWith('4')) return 'Visa';
    if (num.startsWith('5') || num.startsWith('2')) return 'MasterCard';
    return 'Unknown';
  };

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 20 }, (_, i) => currentYear + i);
  const months = Array.from({ length: 12 }, (_, i) => String(i + 1).padStart(2, '0'));

  return (
    <ClientOnlyWrapper>
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
        <Header />
        
        <main className="container mx-auto px-4 py-8">
          {/* شريط التنقل */}
          <div className="mb-6">
            <nav className="flex items-center gap-2 text-sm text-gray-600">
              <Link href="/profile" className="hover:text-green-600 transition-colors">
                الملف الشخصي
              </Link>
              <span>›</span>
              <Link href="/profile" className="hover:text-green-600 transition-colors">
                الإعدادات
              </Link>
              <span>›</span>
              <span className="text-gray-800 font-medium">إضافة بطاقة دفع</span>
            </nav>
          </div>

          <div className="max-w-2xl mx-auto">
            <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
              {/* العنوان */}
              <div className="flex items-center gap-3 mb-6">
                <span className="text-3xl opacity-80 hover:opacity-100 transition-all duration-300"
                      style={{filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))'}}>💳</span>
                <div>
                  <h1 className="text-2xl font-bold text-gray-800">إضافة بطاقة دفع</h1>
                  <p className="text-gray-600">أضف بطاقة ائتمان أو خصم جديدة</p>
                </div>
              </div>

              {/* معاينة البطاقة */}
              <div className="mb-8">
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white relative overflow-hidden">
                  <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
                  <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
                  
                  <div className="relative z-10">
                    <div className="flex justify-between items-start mb-8">
                      <div className="text-lg font-medium">
                        {getCardType(formData.cardNumber) || 'بطاقة دفع'}
                      </div>
                      <div className="w-12 h-8 bg-white/20 rounded"></div>
                    </div>
                    
                    <div className="mb-6">
                      <div className="text-xl font-mono tracking-wider">
                        {formData.cardNumber || '•••• •••• •••• ••••'}
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-end">
                      <div>
                        <div className="text-xs opacity-75 mb-1">اسم حامل البطاقة</div>
                        <div className="font-medium">
                          {formData.cardholderName || 'الاسم الكامل'}
                        </div>
                      </div>
                      <div>
                        <div className="text-xs opacity-75 mb-1">تاريخ الانتهاء</div>
                        <div className="font-mono">
                          {formData.expiryMonth && formData.expiryYear 
                            ? `${formData.expiryMonth}/${formData.expiryYear.slice(-2)}`
                            : 'MM/YY'
                          }
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* النموذج */}
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* رقم البطاقة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    رقم البطاقة
                  </label>
                  <input
                    type="text"
                    value={formData.cardNumber}
                    onChange={(e) => handleInputChange('cardNumber', formatCardNumber(e.target.value))}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent font-mono"
                    placeholder="1234 5678 9012 3456"
                    maxLength={19}
                    required
                  />
                </div>

                {/* تاريخ الانتهاء و CVV */}
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الشهر
                    </label>
                    <select
                      value={formData.expiryMonth}
                      onChange={(e) => handleInputChange('expiryMonth', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      required
                    >
                      <option value="">الشهر</option>
                      {months.map(month => (
                        <option key={month} value={month}>{month}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      السنة
                    </label>
                    <select
                      value={formData.expiryYear}
                      onChange={(e) => handleInputChange('expiryYear', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      required
                    >
                      <option value="">السنة</option>
                      {years.map(year => (
                        <option key={year} value={year}>{year}</option>
                      ))}
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      CVV
                    </label>
                    <input
                      type="text"
                      value={formData.cvv}
                      onChange={(e) => handleInputChange('cvv', e.target.value.replace(/\D/g, '').slice(0, 4))}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent font-mono"
                      placeholder="123"
                      maxLength={4}
                      required
                    />
                  </div>
                </div>

                {/* اسم حامل البطاقة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    اسم حامل البطاقة
                  </label>
                  <input
                    type="text"
                    value={formData.cardholderName}
                    onChange={(e) => handleInputChange('cardholderName', e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="الاسم كما هو مكتوب على البطاقة"
                    required
                  />
                </div>

                {/* جعل البطاقة افتراضية */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isDefault"
                    checked={formData.isDefault}
                    onChange={(e) => handleInputChange('isDefault', e.target.checked)}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <label htmlFor="isDefault" className="mr-2 text-sm text-gray-700">
                    جعل هذه البطاقة الافتراضية للدفع
                  </label>
                </div>

                {/* ملاحظة الأمان */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-start gap-3">
                    <svg className="w-5 h-5 text-green-600 mt-0.5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"/>
                    </svg>
                    <div>
                      <h4 className="font-medium text-green-800 mb-1">معلوماتك آمنة</h4>
                      <p className="text-sm text-green-700">
                        جميع معلومات البطاقة محمية بتشفير SSL ولن يتم مشاركتها مع أطراف ثالثة
                      </p>
                    </div>
                  </div>
                </div>

                {/* أزرار الإجراءات */}
                <div className="flex gap-4 pt-4">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="flex-1 bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    style={{filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))'}}
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                        جاري الحفظ...
                      </>
                    ) : (
                      <>
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"/>
                        </svg>
                        حفظ البطاقة
                      </>
                    )}
                  </button>
                  
                  <Link
                    href="/profile"
                    className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center"
                  >
                    إلغاء
                  </Link>
                </div>
              </form>
            </div>
          </div>
        </main>

        <Footer />
      </div>
    </ClientOnlyWrapper>
  );
}
