import { NextRequest, NextResponse } from 'next/server';
import { AuthService, RegisterData } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const registerData: RegisterData = await request.json();

    // التحقق من صحة البيانات
    const validation = AuthService.validateRegisterData(registerData);
    
    if (!validation.valid) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'بيانات غير صحيحة',
          details: validation.errors 
        },
        { status: 400 }
      );
    }

    // محاولة إنشاء الحساب
    const result = await AuthService.register(registerData);

    if (result.success && result.data) {
      // إنشاء استجابة مع كوكيز الجلسة
      const response = NextResponse.json({
        success: true,
        data: {
          user: result.data.user,
          expiresAt: result.data.expiresAt
        },
        message: 'تم إنشاء الحساب بنجاح'
      });

      // إضافة كوكي الجلسة
      response.cookies.set('auth-token', result.data.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 // أسبوع بالثواني
      });

      return response;
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('خطأ في إنشاء الحساب:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
