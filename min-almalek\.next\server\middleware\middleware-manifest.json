{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_881373._.js", "server/edge/chunks/[root of the server]__d50270._.js", "server/edge/chunks/edge-wrapper_fcd2e9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/settings/:path*{(\\\\.json)}?", "originalSource": "/settings/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/profile/:path*{(\\\\.json)}?", "originalSource": "/profile/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/add-ad/:path*{(\\\\.json)}?", "originalSource": "/add-ad/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/my-ads/:path*{(\\\\.json)}?", "originalSource": "/my-ads/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/favorites/:path*{(\\\\.json)}?", "originalSource": "/favorites/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/messages/:path*{(\\\\.json)}?", "originalSource": "/messages/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/notifications/:path*{(\\\\.json)}?", "originalSource": "/notifications/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/subscription/:path*{(\\\\.json)}?", "originalSource": "/subscription/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/billing/:path*{(\\\\.json)}?", "originalSource": "/billing/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "q1p8Zh2M8fwiZRN6Q6yhhg3RfoFUx/4R2T3BA7G4n/w=", "__NEXT_PREVIEW_MODE_ID": "cbbf012ed3006d5566e03c0be3e29797", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3dbcf81ae34cb451d053fc3f24c36bce8169f01c9e93713c5ead046bdd846032", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9f67d054f8565e4f6ec5b1f68d761e46fd135e594e3a6431e8e2139424f0d8b0"}}}, "instrumentation": null, "functions": {}}