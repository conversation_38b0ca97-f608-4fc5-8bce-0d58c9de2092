{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_881373._.js", "server/edge/chunks/[root of the server]__d50270._.js", "server/edge/chunks/edge-wrapper_fcd2e9.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/admin/:path*{(\\\\.json)}?", "originalSource": "/admin/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "q1p8Zh2M8fwiZRN6Q6yhhg3RfoFUx/4R2T3BA7G4n/w=", "__NEXT_PREVIEW_MODE_ID": "cbbf012ed3006d5566e03c0be3e29797", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3dbcf81ae34cb451d053fc3f24c36bce8169f01c9e93713c5ead046bdd846032", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9f67d054f8565e4f6ec5b1f68d761e46fd135e594e3a6431e8e2139424f0d8b0"}}}, "instrumentation": null, "functions": {}}