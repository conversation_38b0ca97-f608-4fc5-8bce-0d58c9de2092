'use client';

import { useState } from 'react';
import PaymentModal from '@/components/PaymentModal';
import SuccessModal from '@/components/SuccessModal';
import CategoryIcon from '@/components/CategoryIcon';

const PostAdForm = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [hoveredStep, setHoveredStep] = useState<number | null>(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [selectedPaymentPlan, setSelectedPaymentPlan] = useState<{
    name: string;
    price: string;
    currency: string;
  } | null>(null);
  const [formData, setFormData] = useState({
    category: '',
    subcategory: '',
    title: '',
    description: '',
    price: '',
    currency: 'ل.س',
    condition: '',
    location: '',
    area: '',
    contactPhone: '',
    contactEmail: '',
    images: [] as File[],
    features: [] as string[],
    adType: 'free', // free, featured, premium
    userType: 'individual', // individual, business
    acceptTerms: false, // موافقة على الشروط والأحكام
    // مواصفات العقارات
    propertyType: '',
    transactionType: '',
    propertyArea: '',
    rooms: '',
    bathrooms: '',
    floor: '',
    propertyFeatures: [] as string[]
  });

  const categories = [
    {
      id: 'real-estate',
      name: 'العقارات',
      icon: 'real-estate',
      color: '#3b82f6',
      subcategories: ['شقق للبيع', 'شقق للإيجار', 'فيلات للبيع', 'فيلات للإيجار', 'منازل للبيع', 'منازل للإيجار', 'مكاتب للبيع', 'مكاتب للإيجار', 'محلات للبيع', 'محلات للإيجار', 'محلات في مول', 'محلات في سوق تجاري', 'مستودعات', 'أراضي للبيع', 'مزارع للبيع', 'مزارع للإيجار اليومي', 'مزارع للإيجار الأسبوعي', 'شاليهات']
    },
    {
      id: 'cars',
      name: 'السيارات',
      icon: 'cars',
      color: '#ef4444',
      subcategories: ['سيارات للبيع', 'سيارات للإيجار', 'دراجات نارية', 'قطع غيار', 'إكسسوارات']
    },
    {
      id: 'electronics',
      name: 'الإلكترونيات',
      icon: 'electronics',
      color: '#8b5cf6',
      subcategories: ['هواتف ذكية', 'حاسوب ولابتوب', 'تلفزيونات', 'ألعاب فيديو', 'كاميرات']
    },
    {
      id: 'fashion',
      name: 'الأزياء والموضة',
      icon: 'fashion-shirt-mirror',
      color: '#ec4899',
      subcategories: ['ملابس رجالية', 'ملابس نسائية', 'ملابس أطفال', 'أحذية', 'حقائب', 'إكسسوارات']
    },
    {
      id: 'jobs',
      name: 'الوظائف',
      icon: 'jobs',
      color: '#10b981',
      subcategories: ['دوام كامل', 'دوام جزئي', 'عمل حر', 'تدريب', 'وظائف حكومية']
    }
  ];

  const locations = [
    'دمشق', 'حلب', 'حمص', 'حماة', 'اللاذقية', 'طرطوس', 'درعا', 'السويداء', 'القنيطرة', 'دير الزور', 'الرقة', 'الحسكة', 'إدلب', 'ريف دمشق'
  ];

  const conditions = [
    { id: 'new', name: 'جديد' },
    { id: 'excellent', name: 'ممتاز' },
    { id: 'very_good', name: 'جيد جداً' },
    { id: 'good', name: 'جيد' },
    { id: 'needs_renovation', name: 'يحتاج تجديد' }
  ];

  // مواصفات العقارات
  const propertyTypes = [
    { id: 'apartment', name: 'شقة' },
    { id: 'villa', name: 'فيلا' },
    { id: 'house', name: 'منزل' },
    { id: 'office', name: 'مكتب' },
    { id: 'shop', name: 'محل تجاري' },
    { id: 'warehouse', name: 'مستودع' },
    { id: 'land', name: 'أرض' },
    { id: 'farm', name: 'مزرعة' },
    { id: 'chalet', name: 'شاليه' }
  ];

  const transactionTypes = [
    { id: 'sale', name: 'للبيع' },
    { id: 'rent', name: 'للإيجار الشهري' },
    { id: 'rent_daily', name: 'للإيجار اليومي' },
    { id: 'rent_weekly', name: 'للإيجار الأسبوعي' },
    { id: 'rent_seasonal', name: 'للإيجار الموسمي' },
    { id: 'exchange', name: 'للمقايضة' }
  ];

  const roomOptions = [
    { id: '1', name: 'غرفة واحدة' },
    { id: '2', name: 'غرفتان' },
    { id: '3', name: '3 غرف' },
    { id: '4', name: '4 غرف' },
    { id: '5', name: '5 غرف' },
    { id: '6+', name: '6 غرف أو أكثر' }
  ];

  const bathroomOptions = [
    { id: '1', name: 'حمام واحد' },
    { id: '2', name: 'حمامان' },
    { id: '3', name: '3 حمامات' },
    { id: '4+', name: '4 حمامات أو أكثر' }
  ];

  const floorOptions = [
    { id: 'ground', name: 'الطابق الأرضي' },
    { id: '1', name: 'الطابق الأول' },
    { id: '2', name: 'الطابق الثاني' },
    { id: '3', name: 'الطابق الثالث' },
    { id: '4', name: 'الطابق الرابع' },
    { id: '5', name: 'الطابق الخامس' },
    { id: '6+', name: 'الطابق السادس أو أعلى' },
    { id: 'roof', name: 'السطح' }
  ];

  // مميزات مشتركة لجميع العقارات
  const commonFeatures = [
    { id: 'green_title', name: 'طابو أخضر', icon: '📋' },
    { id: 'prime_location', name: 'موقع مميز', icon: '⭐' },
    { id: 'parking', name: 'مرآب سيارات', icon: '🚗' },
    { id: 'security', name: 'حراسة', icon: '🔒' },
    { id: 'air_conditioning', name: 'تكييف', icon: '❄️' },
    { id: 'heating', name: 'تدفئة', icon: '🔥' },
    { id: 'solar_energy', name: 'طاقة شمسية', icon: '☀️' },
    { id: 'sea_view', name: 'إطلالة بحر', icon: '🌊' },
    { id: 'mountain_view', name: 'إطلالة جبل', icon: '⛰️' },
    { id: 'city_view', name: 'إطلالة مدينة', icon: '🏙️' }
  ];

  // مميزات خاصة بالشقق والمنازل والفيلات
  const residentialFeatures = [
    { id: 'elevator', name: 'مصعد', icon: '🛗' },
    { id: 'balcony', name: 'بلكونة', icon: '🏠' },
    { id: 'garden', name: 'حديقة', icon: '🌳' },
    { id: 'furnished', name: 'مفروش', icon: '🛋️' },
    { id: 'swimming_pool', name: 'مسبح', icon: '🏊' },
    { id: 'gym', name: 'نادي رياضي', icon: '💪' },
    { id: 'new_finishing', name: 'إكساء جديد', icon: '✨' },
    { id: 'old_finishing', name: 'إكساء قديم', icon: '🔧' },
    { id: 'tiled', name: 'مكسي', icon: '🔲' },
    { id: 'marble', name: 'رخام', icon: '💎' },
    { id: 'ceramic', name: 'سيراميك', icon: '🔳' },
    { id: 'central_heating', name: 'تدفئة مركزية', icon: '🌡️' },
    { id: 'intercom', name: 'انتركوم', icon: '📞' },
    { id: 'storage_room', name: 'غرفة تخزين', icon: '📦' }
  ];

  // مميزات خاصة بالمكاتب
  const officeFeatures = [
    { id: 'elevator', name: 'مصعد', icon: '🛗' },
    { id: 'reception_area', name: 'منطقة استقبال', icon: '🏢' },
    { id: 'meeting_rooms', name: 'غرف اجتماعات', icon: '👥' },
    { id: 'internet_fiber', name: 'إنترنت فايبر', icon: '🌐' },
    { id: 'phone_lines', name: 'خطوط هاتف', icon: '☎️' },
    { id: 'kitchen_area', name: 'منطقة مطبخ', icon: '🍽️' },
    { id: 'server_room', name: 'غرفة خوادم', icon: '💻' },
    { id: 'conference_room', name: 'قاعة مؤتمرات', icon: '🎤' },
    { id: 'business_district', name: 'منطقة أعمال', icon: '🏢' },
    { id: 'bank_nearby', name: 'قريب من بنوك', icon: '🏦' },
    { id: 'public_transport', name: 'قريب من المواصلات', icon: '🚌' }
  ];

  // مميزات خاصة بالمحلات التجارية
  const shopFeatures = [
    { id: 'commercial_market', name: 'ضمن سوق تجاري', icon: '🏪' },
    { id: 'shopping_mall', name: 'ضمن مول', icon: '🏬' },
    { id: 'street_front', name: 'واجهة شارع رئيسي', icon: '🛣️' },
    { id: 'corner_shop', name: 'محل زاوية', icon: '📐' },
    { id: 'high_traffic', name: 'حركة مرور عالية', icon: '🚶' },
    { id: 'glass_front', name: 'واجهة زجاجية', icon: '🪟' },
    { id: 'storage_back', name: 'مخزن خلفي', icon: '📦' },
    { id: 'tourist_area', name: 'منطقة سياحية', icon: '🗺️' },
    { id: 'residential_area', name: 'منطقة سكنية', icon: '🏘️' },
    { id: 'university_nearby', name: 'قريب من جامعة', icon: '🎓' },
    { id: 'hospital_nearby', name: 'قريب من مستشفى', icon: '🏥' },
    { id: 'restaurant_area', name: 'منطقة مطاعم', icon: '🍽️' },
    { id: 'delivery_access', name: 'مدخل توصيل', icon: '🚚' }
  ];

  // مميزات خاصة بالمزارع والشاليهات
  const farmFeatures = [
    { id: 'bbq_area', name: 'منطقة شواء', icon: '🔥' },
    { id: 'playground', name: 'ملعب أطفال', icon: '🎪' },
    { id: 'fruit_trees', name: 'أشجار مثمرة', icon: '🌳' },
    { id: 'water_well', name: 'بئر ماء', icon: '💧' },
    { id: 'farm_animals', name: 'حيوانات مزرعة', icon: '🐄' },
    { id: 'greenhouse', name: 'بيت زجاجي', icon: '🏡' },
    { id: 'irrigation_system', name: 'نظام ري', icon: '💦' },
    { id: 'guest_house', name: 'بيت ضيافة', icon: '🏠' },
    { id: 'horse_stable', name: 'إسطبل خيول', icon: '🐎' },
    { id: 'farm_equipment', name: 'معدات زراعية', icon: '🚜' },
    { id: 'fishing_pond', name: 'بركة أسماك', icon: '🐟' },
    { id: 'picnic_area', name: 'منطقة نزهة', icon: '🧺' },
    { id: 'camping_area', name: 'منطقة تخييم', icon: '⛺' }
  ];

  // مميزات خاصة بالمستودعات
  const warehouseFeatures = [
    { id: 'loading_dock', name: 'رصيف تحميل', icon: '🚛' },
    { id: 'high_ceiling', name: 'سقف عالي', icon: '📏' },
    { id: 'crane_system', name: 'نظام رافعة', icon: '🏗️' },
    { id: 'office_space', name: 'مساحة مكتبية', icon: '🏢' },
    { id: 'truck_access', name: 'مدخل شاحنات', icon: '🚚' },
    { id: 'industrial_area', name: 'منطقة صناعية', icon: '🏭' },
    { id: 'rail_access', name: 'وصول سكة حديد', icon: '🚂' },
    { id: 'cold_storage', name: 'تبريد', icon: '🧊' },
    { id: 'fire_system', name: 'نظام إطفاء', icon: '🚨' }
  ];

  // دالة لجلب المميزات حسب نوع العقار
  const getPropertyFeatures = () => {
    const propertyType = formData.propertyType;
    let specificFeatures: any[] = [];

    switch (propertyType) {
      case 'apartment':
      case 'villa':
      case 'house':
      case 'chalet':
        specificFeatures = residentialFeatures;
        break;
      case 'office':
        specificFeatures = officeFeatures;
        break;
      case 'shop':
        specificFeatures = shopFeatures;
        break;
      case 'farm':
        specificFeatures = farmFeatures;
        break;
      case 'warehouse':
        specificFeatures = warehouseFeatures;
        break;
      default:
        specificFeatures = residentialFeatures;
    }

    return [...commonFeatures, ...specificFeatures];
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handlePropertyFeatureToggle = (featureId: string) => {
    setFormData(prev => ({
      ...prev,
      propertyFeatures: prev.propertyFeatures.includes(featureId)
        ? prev.propertyFeatures.filter(f => f !== featureId)
        : [...prev.propertyFeatures, featureId]
    }));
  };

  // تحديد حدود الصور حسب نوع الإعلان
  const getImageLimits = () => {
    if (formData.userType === 'business') {
      return { max: 10, description: 'حتى 10 صور للشركات' };
    }

    switch (formData.adType) {
      case 'free':
        return { max: 3, description: 'حتى 3 صور للإعلان المجاني' };
      case 'featured':
        return { max: 6, description: 'حتى 6 صور للإعلان المميز' };
      case 'premium':
        return { max: 10, description: 'حتى 10 صور للإعلان الذهبي' };
      default:
        return { max: 3, description: 'حتى 3 صور للإعلان المجاني' };
    }
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const limits = getImageLimits();
    const currentImageCount = formData.images.length;
    const availableSlots = limits.max - currentImageCount;

    if (availableSlots <= 0) {
      alert(`لقد وصلت للحد الأقصى من الصور (${limits.max} صور)`);
      return;
    }

    const filesToAdd = files.slice(0, availableSlots);
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, ...filesToAdd]
    }));

    if (files.length > availableSlots) {
      alert(`تم إضافة ${filesToAdd.length} صور فقط. للمزيد من الصور، قم بترقية نوع الإعلان.`);
    }
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  const nextStep = () => {
    if (currentStep < 5) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const getStepIcon = (stepNumber: number, isActive: boolean, isHovered: boolean) => {
    const iconClass = `w-6 h-6 transition-all duration-300 ${
      isActive ? 'text-green-600' : 'text-gray-500'
    } group-hover:text-green-600 ${isHovered ? 'drop-shadow-lg filter brightness-110' : ''}`;

    const glowStyle = isHovered ? {
      filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.6)) brightness(1.2)'
    } : {};

    switch (stepNumber) {
      case 1: // نوع الحساب
        return (
          <svg className={iconClass} style={glowStyle} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
          </svg>
        );
      case 2: // التصنيف
        return (
          <svg className={iconClass} style={glowStyle} fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
          </svg>
        );
      case 3: // التفاصيل
        return (
          <svg className={iconClass} style={glowStyle} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
          </svg>
        );
      case 4: // الصور
        return (
          <svg className={iconClass} style={glowStyle} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
          </svg>
        );
      case 5: // النشر
        return (
          <svg className={iconClass} style={glowStyle} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };

  const handleSubmit = () => {
    console.log('إرسال الإعلان:', formData);

    // إظهار مودال النجاح
    setShowSuccessModal(true);
  };

  const selectedCategory = categories.find(cat => cat.id === formData.category);

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* شريط التقدم */}
      <div className="bg-white rounded-xl shadow-lg mb-6">
        <div className="border-b border-gray-200 p-4 md:p-6">
          {/* Mobile Progress */}
          <div className="md:hidden">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm font-medium text-gray-600">
                الخطوة {currentStep} من 5
              </span>
              <span className="text-sm text-gray-500">
                {Math.round((currentStep / 5) * 100)}%
              </span>
            </div>

            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(currentStep / 5) * 100}%` }}
              ></div>
            </div>

            <div className="text-center">
              <div className="text-2xl mb-2 flex justify-center">
                {getStepIcon(currentStep, true, false)}
              </div>
              <div className="text-lg font-semibold text-gray-800">
                {currentStep === 1 && 'نوع الحساب'}
                {currentStep === 2 && 'التصنيف'}
                {currentStep === 3 && 'التفاصيل'}
                {currentStep === 4 && 'الصور'}
                {currentStep === 5 && 'النشر'}
              </div>
            </div>
          </div>

          {/* Desktop Progress */}
          <div className="hidden md:block">
            <div className="flex items-center justify-between">
              {[1, 2, 3, 4, 5].map((step, index) => (
                <div
                  key={step}
                  className={`flex items-center ${index < 4 ? 'flex-1' : ''}`}
                >
                  <div className="flex flex-col items-center">
                    <div
                      className={`w-12 h-12 rounded-full flex items-center justify-center text-lg font-medium mb-2 transition-all duration-300 cursor-pointer group ${
                        currentStep >= step
                          ? 'bg-transparent border-2 border-green-500 text-green-600 shadow-lg'
                          : 'bg-transparent border-2 border-gray-300 text-gray-500'
                      } hover:border-green-500 hover:text-green-600 hover:shadow-lg hover:shadow-green-500/30 hover:scale-105`}
                      onClick={() => setCurrentStep(step)}
                      onMouseEnter={() => setHoveredStep(step)}
                      onMouseLeave={() => setHoveredStep(null)}
                    >
                      {getStepIcon(step, currentStep >= step, hoveredStep === step)}
                    </div>
                    <div className="text-center max-w-24">
                      <div className={`text-xs font-medium leading-tight transition-colors duration-300 ${
                        currentStep >= step ? 'text-green-600' : 'text-gray-500'
                      } hover:text-green-600`}>
                        {step === 1 && 'نوع الحساب'}
                        {step === 2 && 'التصنيف'}
                        {step === 3 && 'التفاصيل'}
                        {step === 4 && 'الصور'}
                        {step === 5 && 'النشر'}
                      </div>
                    </div>
                  </div>
                  {index < 4 && (
                    <div className={`flex-1 h-0.5 mx-2 mt-6 transition-colors duration-300 ${
                      currentStep > step ? 'bg-green-500' : 'bg-gray-200'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* الخطوة 1: نوع المستخدم */}
      {currentStep === 1 && (
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">اختر نوع الحساب</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {/* فرد */}
            <div
              className={`border-2 rounded-xl p-6 cursor-pointer transition-all hover:shadow-lg ${
                formData.userType === 'individual'
                  ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => handleInputChange('userType', 'individual')}
            >
              <div className="text-center">
                <div className="text-4xl mb-4">👤</div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">فرد</h3>
                <div className="space-y-2 text-sm text-gray-600 mb-4">
                  <p>✅ إعلانات مجانية (3 صور)</p>
                  <p>💰 إعلانات مميزة (6 صور - 5,000 ل.س)</p>
                  <p>🏆 إعلانات ذهبية (10 صور - 10,000 ل.س)</p>
                </div>
                <div className="text-green-600 font-semibold">يمكن النشر مجاناً</div>
              </div>
            </div>

            {/* شركة */}
            <div
              className={`border-2 rounded-xl p-6 cursor-pointer transition-all hover:shadow-lg ${
                formData.userType === 'business'
                  ? 'border-purple-500 bg-purple-50 ring-2 ring-purple-200'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => handleInputChange('userType', 'business')}
            >
              <div className="text-center">
                <div className="text-4xl mb-4">🏢</div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">شركة</h3>
                <div className="space-y-2 text-sm text-gray-600 mb-4">
                  <p>📊 اشتراكات شهرية</p>
                  <p>⭐ إعلانات مميزة دائماً</p>
                  <p>🎯 أولوية في النتائج</p>
                  <p>📈 إحصائيات متقدمة</p>
                </div>
                <div className="text-purple-600 font-semibold">يتطلب اشتراك مدفوع</div>
              </div>
            </div>
          </div>

          <div className="flex justify-between">
            <div></div>
            <button
              onClick={() => setCurrentStep(2)}
              disabled={!formData.userType}
              className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              التالي
            </button>
          </div>
        </div>
      )}

      {/* الخطوة 2: التصنيف */}
      {currentStep === 2 && (
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">اختر التصنيف</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => handleInputChange('category', category.id)}
                className={`p-4 border-2 rounded-lg text-right transition-colors ${
                  formData.category === category.id
                    ? 'border-primary-600 bg-primary-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-8 h-8 flex items-center justify-center">
                    <CategoryIcon
                      category={category.icon}
                      className="w-6 h-6"
                      color={category.color}
                    />
                  </div>
                  <h3 className="font-semibold text-lg">{category.name}</h3>
                </div>
                <div className="text-sm text-gray-600">
                  {category.subcategories.slice(0, 3).join(' • ')}
                  {category.subcategories.length > 3 && '...'}
                </div>
              </button>
            ))}
          </div>

          {selectedCategory && (
            <div>
              <h3 className="font-semibold text-lg mb-4">اختر التصنيف الفرعي</h3>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {selectedCategory.subcategories.map((sub) => (
                  <button
                    key={sub}
                    onClick={() => handleInputChange('subcategory', sub)}
                    className={`p-3 border rounded-lg text-sm transition-colors ${
                      formData.subcategory === sub
                        ? 'border-primary-600 bg-primary-50 text-primary-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    {sub}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* الخطوة 3: التفاصيل */}
      {currentStep === 3 && (
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">تفاصيل الإعلان</h2>

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                عنوان الإعلان *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="اكتب عنواناً واضحاً وجذاباً"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                maxLength={100}
              />
              <div className="text-sm text-gray-500 mt-1">
                {formData.title.length}/100 حرف
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الوصف *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="اكتب وصفاً مفصلاً للإعلان..."
                rows={6}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                maxLength={1000}
              />
              <div className="text-sm text-gray-500 mt-1">
                {formData.description.length}/1000 حرف
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  السعر *
                </label>
                <div className="flex">
                  <input
                    type="number"
                    value={formData.price}
                    onChange={(e) => handleInputChange('price', e.target.value)}
                    placeholder="0"
                    className="flex-1 px-4 py-3 border border-gray-300 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                  <select
                    value={formData.currency}
                    onChange={(e) => handleInputChange('currency', e.target.value)}
                    className="px-4 py-3 border border-r-0 border-gray-300 rounded-l-lg bg-gray-50"
                  >
                    <option value="ل.س">ل.س</option>
                    <option value="$">$</option>
                    <option value="€">€</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الحالة
                </label>
                <select
                  value={formData.condition}
                  onChange={(e) => handleInputChange('condition', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">اختر الحالة</option>
                  {conditions.map(condition => (
                    <option key={condition.id} value={condition.id}>
                      {condition.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المحافظة *
                </label>
                <select
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">اختر المحافظة</option>
                  {locations.map(location => (
                    <option key={location} value={location}>
                      {location}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المنطقة أو الشارع
                </label>
                <input
                  type="text"
                  value={formData.area}
                  onChange={(e) => handleInputChange('area', e.target.value)}
                  placeholder="اسم المنطقة أو الشارع"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  رقم الهاتف *
                </label>
                <input
                  type="tel"
                  value={formData.contactPhone}
                  onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                  placeholder="+963 9X XXX XXXX"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  البريد الإلكتروني
                </label>
                <input
                  type="email"
                  value={formData.contactEmail}
                  onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                />
              </div>
            </div>

            {/* مواصفات العقارات */}
            {formData.category === 'real-estate' && (
              <div className="mt-8 p-6 bg-blue-50 rounded-lg border border-blue-200">
                <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                  🏠 مواصفات العقار
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                  {/* نوع العقار */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نوع العقار
                    </label>
                    <select
                      value={formData.propertyType}
                      onChange={(e) => handleInputChange('propertyType', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر النوع</option>
                      {propertyTypes.map(type => (
                        <option key={type.id} value={type.id}>{type.name}</option>
                      ))}
                    </select>
                  </div>

                  {/* نوع المعاملة */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نوع المعاملة
                    </label>
                    <select
                      value={formData.transactionType}
                      onChange={(e) => handleInputChange('transactionType', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر المعاملة</option>
                      {transactionTypes.map(type => (
                        <option key={type.id} value={type.id}>{type.name}</option>
                      ))}
                    </select>
                  </div>

                  {/* مساحة العقار */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      المساحة (م²)
                    </label>
                    <input
                      type="number"
                      value={formData.propertyArea}
                      onChange={(e) => handleInputChange('propertyArea', e.target.value)}
                      placeholder="مساحة العقار"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  {/* عدد الغرف */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      عدد الغرف
                    </label>
                    <select
                      value={formData.rooms}
                      onChange={(e) => handleInputChange('rooms', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر العدد</option>
                      {roomOptions.map(room => (
                        <option key={room.id} value={room.id}>{room.name}</option>
                      ))}
                    </select>
                  </div>

                  {/* عدد الحمامات */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      عدد الحمامات
                    </label>
                    <select
                      value={formData.bathrooms}
                      onChange={(e) => handleInputChange('bathrooms', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر العدد</option>
                      {bathroomOptions.map(bathroom => (
                        <option key={bathroom.id} value={bathroom.id}>{bathroom.name}</option>
                      ))}
                    </select>
                  </div>

                  {/* الطابق */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الطابق
                    </label>
                    <select
                      value={formData.floor}
                      onChange={(e) => handleInputChange('floor', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    >
                      <option value="">اختر الطابق</option>
                      {floorOptions.map(floor => (
                        <option key={floor.id} value={floor.id}>{floor.name}</option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* مميزات العقار */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    مميزات {
                      formData.propertyType === 'apartment' ? 'الشقة' :
                      formData.propertyType === 'villa' ? 'الفيلا' :
                      formData.propertyType === 'house' ? 'المنزل' :
                      formData.propertyType === 'office' ? 'المكتب' :
                      formData.propertyType === 'shop' ? 'المحل التجاري' :
                      formData.propertyType === 'warehouse' ? 'المستودع' :
                      formData.propertyType === 'farm' ? 'المزرعة' :
                      formData.propertyType === 'chalet' ? 'الشاليه' :
                      'العقار'
                    }
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3">
                    {getPropertyFeatures().map((feature: any) => (
                      <button
                        key={feature.id}
                        type="button"
                        onClick={() => handlePropertyFeatureToggle(feature.id)}
                        className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors border text-center ${
                          formData.propertyFeatures.includes(feature.id)
                            ? 'bg-primary-600 text-white border-primary-600'
                            : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
                        }`}
                      >
                        <div className="flex flex-col items-center gap-1">
                          <span className="text-lg">{feature.icon}</span>
                          <span className="text-xs">{feature.name}</span>
                        </div>
                      </button>
                    ))}
                  </div>
                  {formData.propertyFeatures.length > 0 && (
                    <div className="mt-2 text-xs text-gray-500">
                      تم اختيار {formData.propertyFeatures.length} ميزة
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* الخطوة 4: الصور */}
      {currentStep === 4 && (
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">إضافة الصور</h2>

          {/* معلومات حدود الصور */}
          <div className="mb-6">
            {(() => {
              const limits = getImageLimits();
              const currentCount = formData.images.length;
              const isAtLimit = currentCount >= limits.max;

              return (
                <div className={`p-4 rounded-lg border-2 mb-4 ${
                  formData.adType === 'free'
                    ? 'bg-yellow-50 border-yellow-300'
                    : formData.adType === 'featured'
                    ? 'bg-blue-50 border-blue-300'
                    : formData.adType === 'premium'
                    ? 'bg-yellow-50 border-yellow-400'
                    : 'bg-gray-50 border-gray-300'
                }`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">
                        {formData.adType === 'free' ? '🆓' :
                         formData.adType === 'featured' ? '⭐' :
                         formData.adType === 'premium' ? '🏆' : '📷'}
                      </span>
                      <div>
                        <div className="font-semibold text-gray-800">
                          {limits.description}
                        </div>
                        <div className="text-sm text-gray-600">
                          تم استخدام {currentCount} من {limits.max} صور
                        </div>
                      </div>
                    </div>
                    {formData.adType === 'free' && (
                      <div className="text-right">
                        <div className="text-xs text-yellow-700 font-medium">
                          💡 للمزيد من الصور
                        </div>
                        <div className="text-xs text-yellow-600">
                          اختر إعلان مميز أو ذهبي
                        </div>
                      </div>
                    )}
                  </div>

                  {/* شريط التقدم */}
                  <div className="mt-3">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          formData.adType === 'free' ? 'bg-yellow-500' :
                          formData.adType === 'featured' ? 'bg-blue-500' :
                          formData.adType === 'premium' ? 'bg-yellow-600' : 'bg-gray-500'
                        }`}
                        style={{ width: `${(currentCount / limits.max) * 100}%` }}
                      />
                    </div>
                  </div>
                </div>
              );
            })()}
          </div>

          <div className="mb-6">
            <label className="block w-full">
              <div className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
                formData.images.length >= getImageLimits().max
                  ? 'border-gray-200 bg-gray-50 cursor-not-allowed'
                  : 'border-gray-300 hover:border-primary-400'
              }`}>
                <div className="text-4xl mb-4">
                  {formData.images.length >= getImageLimits().max ? '🚫' : '📷'}
                </div>
                <div className="text-lg font-medium text-gray-700 mb-2">
                  {formData.images.length >= getImageLimits().max
                    ? 'وصلت للحد الأقصى من الصور'
                    : 'اضغط لإضافة الصور'
                  }
                </div>
                <div className="text-sm text-gray-500">
                  {getImageLimits().description} (JPG, PNG, WebP)
                </div>
                {formData.images.length >= getImageLimits().max && formData.adType === 'free' && (
                  <div className="mt-3 text-sm text-yellow-600 font-medium">
                    💡 قم بترقية نوع الإعلان للمزيد من الصور
                  </div>
                )}
              </div>
              <input
                type="file"
                multiple
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
                disabled={formData.images.length >= getImageLimits().max}
              />
            </label>
          </div>

          {formData.images.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {formData.images.map((image, index) => (
                <div key={index} className="relative">
                  <img
                    src={URL.createObjectURL(image)}
                    alt={`صورة ${index + 1}`}
                    className="w-full h-32 object-cover rounded-lg"
                  />
                  <button
                    onClick={() => removeImage(index)}
                    className="absolute top-2 left-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-sm hover:bg-red-600"
                  >
                    ×
                  </button>
                  {index === 0 && (
                    <div className="absolute bottom-2 right-2 bg-primary-600 text-white text-xs px-2 py-1 rounded">
                      الصورة الرئيسية
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* الخطوة 5: النشر */}
      {currentStep === 5 && (
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-6">نوع الإعلان</h2>

          {/* خيارات الأفراد */}
          {formData.userType === 'individual' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className={`border-2 rounded-lg p-6 cursor-pointer transition-colors ${
                formData.adType === 'free' ? 'border-green-500 bg-green-50' : 'border-gray-200'
              }`} onClick={() => handleInputChange('adType', 'free')}>
                <div className="text-center">
                  <div className="text-3xl mb-3">🆓</div>
                  <h3 className="font-semibold text-lg mb-2">إعلان مجاني</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    إعلان عادي يظهر في النتائج العامة
                  </p>
                  <div className="bg-yellow-100 rounded-lg p-2 mb-3">
                    <div className="text-xs text-yellow-800 font-medium">📷 حتى 3 صور</div>
                  </div>
                  <div className="text-2xl font-bold text-green-600">مجاني</div>
                  <div className="text-xs text-gray-500">لمدة 30 يوم</div>
                </div>
              </div>

              <div className={`border-2 rounded-lg p-6 cursor-pointer transition-colors ${
                formData.adType === 'featured' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
              }`} onClick={() => {
                handleInputChange('adType', 'featured');
                setSelectedPaymentPlan({
                  name: 'إعلان مميز',
                  price: '5000',
                  currency: 'ل.س'
                });
              }}>
                <div className="text-center">
                  <div className="text-3xl mb-3">⭐</div>
                  <h3 className="font-semibold text-lg mb-2">إعلان مميز</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    يظهر في المقدمة ويحصل على مشاهدات أكثر
                  </p>
                  <div className="bg-blue-100 rounded-lg p-2 mb-3">
                    <div className="text-xs text-blue-800 font-medium">📷 حتى 6 صور</div>
                  </div>
                  <div className="text-2xl font-bold text-blue-600">5,000 ل.س</div>
                  <div className="text-xs text-gray-500">لمدة 30 يوم</div>
                </div>
              </div>

              <div className={`border-2 rounded-lg p-6 cursor-pointer transition-colors ${
                formData.adType === 'premium' ? 'border-yellow-500 bg-yellow-50' : 'border-gray-200'
              }`} onClick={() => {
                handleInputChange('adType', 'premium');
                setSelectedPaymentPlan({
                  name: 'إعلان ذهبي',
                  price: '10000',
                  currency: 'ل.س'
                });
              }}>
                <div className="text-center">
                  <div className="text-3xl mb-3">🏆</div>
                  <h3 className="font-semibold text-lg mb-2">إعلان ذهبي</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    أولوية عالية مع شارة "ذهبي"
                  </p>
                  <div className="bg-yellow-100 rounded-lg p-2 mb-3">
                    <div className="text-xs text-yellow-800 font-medium">📷 حتى 10 صور</div>
                  </div>
                  <div className="text-2xl font-bold text-yellow-600">10,000 ل.س</div>
                  <div className="text-xs text-gray-500">لمدة 45 يوم</div>
                </div>
              </div>
            </div>
          )}

          {/* خيارات الشركات */}
          {formData.userType === 'business' && (
            <div className="space-y-6">
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 border-2 border-purple-300 rounded-xl p-8 shadow-lg">
                <div className="text-center">
                  <div className="text-6xl mb-4 animate-pulse">🏢</div>
                  <h3 className="text-2xl font-bold text-purple-800 mb-4">حساب شركة - اشتراك مطلوب</h3>
                  <div className="bg-yellow-100 border border-yellow-400 rounded-lg p-4 mb-6">
                    <div className="flex items-center justify-center gap-2 text-yellow-800">
                      <span className="text-2xl">⚠️</span>
                      <span className="font-bold">تنبيه مهم</span>
                      <span className="text-2xl">⚠️</span>
                    </div>
                    <p className="text-yellow-800 font-semibold mt-2">
                      للشركات، يجب اختيار اشتراك شهري للحصول على إعلانات مميزة دائماً
                    </p>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                    <div className="bg-white rounded-xl p-6 border-2 border-blue-200 shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                      <div className="text-center">
                        <div className="text-3xl mb-2">🔵</div>
                        <h4 className="font-bold text-blue-800 mb-3 text-lg">الباقة الأساسية</h4>
                        <div className="text-2xl font-bold text-blue-600 mb-2">25,000 ل.س</div>
                        <div className="text-sm text-blue-500 mb-3">/شهر</div>
                        <div className="bg-blue-50 rounded-lg p-3">
                          <p className="text-sm font-semibold text-blue-700">✅ 10 إعلانات مميزة</p>
                          <p className="text-sm font-semibold text-blue-700">✅ شارة توثيق زرقاء</p>
                          <p className="text-sm font-semibold text-blue-700">📷 حتى 10 صور لكل إعلان</p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-white rounded-xl p-6 border-2 border-gray-300 shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                      <div className="text-center">
                        <div className="text-3xl mb-2">🔘</div>
                        <h4 className="font-bold text-gray-800 mb-3 text-lg">الباقة المميزة</h4>
                        <div className="text-2xl font-bold text-gray-600 mb-2">50,000 ل.س</div>
                        <div className="text-sm text-gray-500 mb-3">/شهر</div>
                        <div className="bg-gray-50 rounded-lg p-3">
                          <p className="text-sm font-semibold text-gray-700">✅ 25 إعلان مميز</p>
                          <p className="text-sm font-semibold text-gray-700">✅ شارة توثيق فضية</p>
                          <p className="text-sm font-semibold text-gray-700">📷 حتى 10 صور لكل إعلان</p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-white rounded-xl p-6 border-2 border-yellow-300 shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-105 relative">
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-yellow-500 text-white px-3 py-1 rounded-full text-xs font-bold">الأكثر شعبية</span>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl mb-2">🟡</div>
                        <h4 className="font-bold text-yellow-800 mb-3 text-lg">باقة الأعمال</h4>
                        <div className="text-2xl font-bold text-yellow-600 mb-2">100,000 ل.س</div>
                        <div className="text-sm text-yellow-500 mb-3">/شهر</div>
                        <div className="bg-yellow-50 rounded-lg p-3">
                          <p className="text-sm font-semibold text-yellow-700">✅ إعلانات غير محدودة</p>
                          <p className="text-sm font-semibold text-yellow-700">✅ شارة توثيق ذهبية</p>
                          <p className="text-sm font-semibold text-yellow-700">📷 حتى 10 صور لكل إعلان</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => window.open('/pricing', '_blank')}
                    className="bg-gradient-to-r from-purple-600 to-purple-700 text-white px-8 py-4 rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-300 font-bold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-3 mx-auto"
                  >
                    <span className="text-2xl">💎</span>
                    <span>ترقية الاشتراك</span>
                    <span className="text-2xl">🚀</span>
                  </button>
                  <div className="bg-green-100 border border-green-400 rounded-lg p-4 mt-6">
                    <div className="flex items-center justify-center gap-2 text-green-800 mb-2">
                      <span className="text-xl">✅</span>
                      <span className="font-bold">خطوات بسيطة</span>
                      <span className="text-xl">✅</span>
                    </div>
                    <p className="text-green-800 font-semibold text-center">
                      1️⃣ اختر الباقة المناسبة → 2️⃣ ادفع الاشتراك → 3️⃣ ارجع لنشر إعلانات مميزة
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="bg-gray-50 rounded-lg p-6 mb-6">
            <h3 className="font-semibold text-lg mb-4">ملخص الإعلان</h3>
            <div className="space-y-2 text-sm">
              <div><span className="font-medium">التصنيف:</span> {selectedCategory?.name} - {formData.subcategory}</div>
              <div><span className="font-medium">العنوان:</span> {formData.title}</div>
              <div><span className="font-medium">السعر:</span> {formData.price} {formData.currency}</div>
              <div><span className="font-medium">الموقع:</span> {formData.location} {formData.area && `- ${formData.area}`}</div>
              <div><span className="font-medium">عدد الصور:</span> {formData.images.length}</div>

              {/* مواصفات العقارات في الملخص */}
              {formData.category === 'real-estate' && (
                <>
                  {formData.propertyType && (
                    <div><span className="font-medium">نوع العقار:</span> {propertyTypes.find(t => t.id === formData.propertyType)?.name}</div>
                  )}
                  {formData.transactionType && (
                    <div><span className="font-medium">نوع المعاملة:</span> {transactionTypes.find(t => t.id === formData.transactionType)?.name}</div>
                  )}
                  {formData.propertyArea && (
                    <div><span className="font-medium">المساحة:</span> {formData.propertyArea} م²</div>
                  )}
                  {formData.rooms && (
                    <div><span className="font-medium">عدد الغرف:</span> {roomOptions.find(r => r.id === formData.rooms)?.name}</div>
                  )}
                  {formData.bathrooms && (
                    <div><span className="font-medium">عدد الحمامات:</span> {bathroomOptions.find(b => b.id === formData.bathrooms)?.name}</div>
                  )}
                  {formData.floor && (
                    <div><span className="font-medium">الطابق:</span> {floorOptions.find(f => f.id === formData.floor)?.name}</div>
                  )}
                  {formData.propertyFeatures.length > 0 && (
                    <div><span className="font-medium">المميزات:</span> {formData.propertyFeatures.length} ميزة مختارة</div>
                  )}
                </>
              )}
            </div>
          </div>

          {/* الشروط والأحكام */}
          <div className="bg-red-50 border-2 border-red-200 rounded-xl p-6 mb-6">
            <div className="flex items-center gap-3 mb-4">
              <span className="text-3xl">⚠️</span>
              <h3 className="text-xl font-bold text-red-800">شروط وأحكام النشر</h3>
              <span className="text-3xl">⚠️</span>
            </div>

            <div className="space-y-4 text-sm">
              {/* صحة المعلومات */}
              <div className="bg-white rounded-lg p-4 border border-red-200">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg">✅</span>
                  <span className="font-bold text-red-800">صحة المعلومات والبيانات</span>
                </div>
                <ul className="text-red-700 space-y-1 mr-6">
                  <li>• أتعهد بأن جميع المعلومات المدخلة صحيحة ودقيقة</li>
                  <li>• أتحمل المسؤولية الكاملة عن صحة الأسعار والأوصاف</li>
                  <li>• أتعهد بتحديث المعلومات في حال تغييرها</li>
                  <li>• أرقام الهواتف وبيانات التواصل صحيحة وفعالة</li>
                </ul>
              </div>

              {/* الصور والمحتوى */}
              <div className="bg-white rounded-lg p-4 border border-red-200">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg">📷</span>
                  <span className="font-bold text-red-800">الصور والمحتوى المرئي</span>
                </div>
                <ul className="text-red-700 space-y-1 mr-6">
                  <li>• جميع الصور المرفقة تخصني أو لدي الحق في استخدامها</li>
                  <li>• الصور لا تحتوي على محتوى مخالف للآداب العامة</li>
                  <li>• الصور لا تنتهك حقوق الملكية الفكرية لأي طرف آخر</li>
                  <li>• الصور تمثل المنتج/الخدمة المعلن عنها بصدق</li>
                </ul>
              </div>

              {/* القوانين السورية */}
              <div className="bg-white rounded-lg p-4 border border-red-200">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg">🇸🇾</span>
                  <span className="font-bold text-red-800">الالتزام بالقوانين السورية</span>
                </div>
                <ul className="text-red-700 space-y-1 mr-6">
                  <li>• أتعهد بعدم نشر أي محتوى مخالف لقوانين الجمهورية العربية السورية</li>
                  <li>• أتعهد بعدم نشر محتوى مخالف للآداب والأخلاق العامة</li>
                  <li>• أتعهد بعدم الإعلان عن سلع أو خدمات محظورة قانونياً</li>
                  <li>• أحترم القيم والتقاليد المجتمعية السورية</li>
                </ul>
              </div>

              {/* المسؤولية القانونية */}
              <div className="bg-yellow-100 rounded-lg p-4 border border-yellow-400">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg">⚖️</span>
                  <span className="font-bold text-yellow-800">المسؤولية القانونية</span>
                </div>
                <div className="text-yellow-800 font-semibold">
                  أتحمل المسؤولية القانونية الكاملة عن كل ما أنشره في هذا الإعلان، وأتعهد بتعويض المنصة عن أي أضرار قد تنجم عن مخالفة هذه الشروط.
                </div>
              </div>
            </div>

            {/* صندوق الموافقة */}
            <div className={`mt-6 rounded-lg p-4 border-2 transition-all duration-300 ${
              formData.acceptTerms
                ? 'bg-green-50 border-green-300'
                : 'bg-white border-red-300'
            }`}>
              <label className="flex items-start gap-3 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.acceptTerms || false}
                  onChange={(e) => handleInputChange('acceptTerms', e.target.checked)}
                  className="mt-1 w-5 h-5 text-red-600 border-2 border-red-300 rounded focus:ring-red-500"
                />
                <div className="text-sm">
                  <span className={`font-bold ${formData.acceptTerms ? 'text-green-800' : 'text-red-800'}`}>
                    أوافق على جميع الشروط والأحكام المذكورة أعلاه
                  </span>
                  <div className={`mt-1 ${formData.acceptTerms ? 'text-green-600' : 'text-red-600'}`}>
                    وأتعهد بالالتزام بها وأتحمل المسؤولية الكاملة عن محتوى إعلاني
                  </div>
                </div>
              </label>

              {!formData.acceptTerms && (
                <div className="mt-3 p-3 bg-red-100 border border-red-300 rounded-lg">
                  <div className="flex items-center gap-2 text-red-800">
                    <span className="text-lg">🚫</span>
                    <span className="font-bold text-sm">يجب الموافقة على الشروط لمتابعة النشر</span>
                  </div>
                </div>
              )}

              {formData.acceptTerms && (
                <div className="mt-3 p-3 bg-green-100 border border-green-300 rounded-lg">
                  <div className="flex items-center gap-2 text-green-800">
                    <span className="text-lg">✅</span>
                    <span className="font-bold text-sm">شكراً لموافقتك على الشروط والأحكام</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* أزرار التنقل */}
      <div className="flex justify-between mt-8">
        <button
          onClick={prevStep}
          disabled={currentStep === 1}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          السابق
        </button>

        {currentStep < 5 ? (
          <button
            onClick={nextStep}
            disabled={
              (currentStep === 1 && !formData.userType) ||
              (currentStep === 2 && (!formData.category || !formData.subcategory)) ||
              (currentStep === 3 && (!formData.title || !formData.description || !formData.price || !formData.location || !formData.contactPhone))
            }
            className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            التالي
          </button>
        ) : (
          <>
            {formData.userType === 'individual' ? (
              <button
                onClick={() => {
                  if (!formData.acceptTerms) {
                    alert('يجب الموافقة على الشروط والأحكام أولاً');
                    return;
                  }
                  if (formData.adType === 'free') {
                    handleSubmit();
                  } else {
                    // تعيين معلومات الخطة حسب نوع الإعلان
                    const planInfo = formData.adType === 'featured'
                      ? { name: 'إعلان مميز', price: '5,000', currency: 'ل.س' }
                      : { name: 'إعلان ذهبي', price: '10,000', currency: 'ل.س' };

                    setSelectedPaymentPlan(planInfo);
                    setShowPaymentModal(true);
                  }
                }}
                disabled={!formData.adType || !formData.acceptTerms}
                className="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-semibold disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
              >
                {formData.adType === 'free' ? '✅ نشر مجاني' :
                 formData.adType === 'featured' ? '💳 دفع 5,000 ل.س ونشر' :
                 formData.adType === 'premium' ? '💳 دفع 10,000 ل.س ونشر' : 'نشر الإعلان'}
              </button>
            ) : (
              <button
                onClick={() => window.open('/pricing', '_blank')}
                className="bg-gradient-to-r from-purple-600 to-purple-700 text-white px-10 py-4 rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-300 font-bold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 flex items-center gap-3"
              >
                <span className="text-2xl">💎</span>
                <span>ترقية الاشتراك أولاً</span>
                <span className="text-2xl">⚡</span>
              </button>
            )}
          </>
        )}
      </div>

      {/* Payment Modal */}
      {selectedPaymentPlan && (
        <PaymentModal
          isOpen={showPaymentModal}
          onClose={() => {
            setShowPaymentModal(false);
            setSelectedPaymentPlan(null);
          }}
          planName={selectedPaymentPlan.name}
          planPrice={selectedPaymentPlan.price}
          planCurrency={selectedPaymentPlan.currency}
          onSuccess={() => {
            setShowPaymentModal(false);
            setSelectedPaymentPlan(null);
            setShowSuccessModal(true);
          }}
        />
      )}

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        adData={{
          title: formData.title,
          category: selectedCategory?.name || '',
          subcategory: formData.subcategory,
          price: formData.price,
          currency: formData.currency,
          location: formData.location,
          imageCount: formData.images.length,
          contactPhone: formData.contactPhone,
          adType: formData.adType
        }}
      />
    </div>
  );
};

export default PostAdForm;
