'use client';

import { useEffect, useState } from 'react';
import NotificationBell from './NotificationBell';

const ClientOnlyNotifications = () => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    // عرض placeholder أثناء التحميل
    return (
      <div className="relative p-3 text-gray-600 rounded-full">
        <svg
          className="w-6 h-6"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 19V20H3V19L5 17V11C5 7.9 7 5.2 10 4.3V4C10 2.9 10.9 2 12 2S14 2.9 14 4V4.3C17 5.2 19 7.9 19 11V17L21 19ZM12 22C10.9 22 10 21.1 10 20H14C14 21.1 13.1 22 12 22Z"/>
        </svg>
      </div>
    );
  }

  return <NotificationBell />;
};

export default ClientOnlyNotifications;
