'use client';

import { COMPANY_CONTACT, ContactUtils, InquiryTypes } from '@/lib/contact';
import { WhatsAppIcon, PhoneIcon } from './ContactIcons';
import { useState } from 'react';

interface ContactButtonsProps {
  variant?: 'full' | 'compact' | 'floating' | 'inline';
  showInquiryTypes?: boolean;
  defaultMessage?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const ContactButtons = ({
  variant = 'full',
  showInquiryTypes = false,
  defaultMessage,
  className = '',
  size = 'md'
}: ContactButtonsProps) => {
  const [showInquiries, setShowInquiries] = useState(false);

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-6 py-4 text-lg'
  };

  const iconSizes = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl'
  };

  // مكون الأزرار الأساسية
  const BasicButtons = () => (
    <div className={`flex gap-3 ${variant === 'inline' ? 'flex-row' : 'flex-col sm:flex-row'}`}>
      {/* زر الواتساب */}
      <button
        onClick={() => ContactUtils.openWhatsApp(defaultMessage)}
        className={`
          ${sizeClasses[size]}
          bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700
          text-white rounded-xl font-semibold shadow-lg hover:shadow-xl
          flex items-center justify-center gap-3 transition-all duration-300
          hover:scale-105 transform border border-green-400
          relative overflow-hidden group
        `}
      >
        <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
        <WhatsAppIcon className="w-5 h-5 relative z-10" />
        <span className="relative z-10">واتساب</span>
      </button>

      {/* زر الاتصال */}
      <button
        onClick={() => ContactUtils.makeCall()}
        className={`
          ${sizeClasses[size]}
          bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700
          text-white rounded-xl font-semibold shadow-lg hover:shadow-xl
          flex items-center justify-center gap-3 transition-all duration-300
          hover:scale-105 transform border border-blue-400
          relative overflow-hidden group
        `}
      >
        <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
        <PhoneIcon className="w-5 h-5 relative z-10" />
        <span className="relative z-10">اتصال</span>
      </button>

      {/* عرض رقم الهاتف */}
      {variant === 'full' && (
        <div className="text-center text-gray-600 mt-2">
          <div className="font-semibold text-lg text-primary-600">
            {COMPANY_CONTACT.phone.displayNumber}
          </div>
          <div className="text-sm text-gray-500">
            {COMPANY_CONTACT.info.workingHours}
          </div>
        </div>
      )}
    </div>
  );

  // مكون أنواع الاستفسارات
  const InquiryTypesComponent = () => (
    <div className="mt-4">
      <button
        onClick={() => setShowInquiries(!showInquiries)}
        className="text-primary-600 hover:text-primary-700 font-medium text-sm flex items-center gap-1"
      >
        <span>اختر نوع الاستفسار</span>
        <span className={`transform transition-transform ${showInquiries ? 'rotate-180' : ''}`}>
          ▼
        </span>
      </button>

      {showInquiries && (
        <div className="mt-3 grid grid-cols-2 sm:grid-cols-3 gap-2">
          {InquiryTypes.map((type) => (
            <button
              key={type.id}
              onClick={() => {
                ContactUtils.openWhatsApp(type.message);
                setShowInquiries(false);
              }}
              className="p-2 text-xs bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200
                         hover:border-primary-300 transition-all duration-200 text-center"
            >
              <div className="text-lg mb-1">{type.icon}</div>
              <div className="font-medium text-gray-700">{type.name}</div>
            </button>
          ))}
        </div>
      )}
    </div>
  );

  // التخطيط المضغوط
  if (variant === 'compact') {
    return (
      <div className={`${className}`}>
        <div className="flex gap-2">
          <button
            onClick={() => ContactUtils.openWhatsApp(defaultMessage)}
            className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border border-green-400"
            title="واتساب"
          >
            <WhatsAppIcon className="w-5 h-5" />
          </button>
          <button
            onClick={() => ContactUtils.makeCall()}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 border border-blue-400"
            title="اتصال"
          >
            <PhoneIcon className="w-5 h-5" />
          </button>
        </div>
        <div className="text-xs text-gray-600 mt-1 text-center">
          {COMPANY_CONTACT.phone.displayNumber}
        </div>
      </div>
    );
  }

  // التخطيط العائم
  if (variant === 'floating') {
    return (
      <div className={`fixed bottom-6 right-6 z-50 ${className}`}>
        <div className="flex flex-col gap-3">
          <button
            onClick={() => ContactUtils.openWhatsApp(defaultMessage)}
            className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white p-4 rounded-full shadow-xl
                       hover:shadow-2xl transition-all duration-300 hover:scale-110 border-2 border-green-400 relative overflow-hidden group"
            title="تواصل عبر الواتساب"
          >
            <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
            <WhatsAppIcon className="w-6 h-6 relative z-10" />
          </button>
          <button
            onClick={() => ContactUtils.makeCall()}
            className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white p-4 rounded-full shadow-xl
                       hover:shadow-2xl transition-all duration-300 hover:scale-110 border-2 border-blue-400 relative overflow-hidden group"
            title="اتصال مباشر"
          >
            <div className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
            <PhoneIcon className="w-6 h-6 relative z-10" />
          </button>
        </div>
      </div>
    );
  }

  // التخطيط الكامل (افتراضي)
  return (
    <div className={`${className}`}>
      <BasicButtons />
      {showInquiryTypes && <InquiryTypesComponent />}
    </div>
  );
};

// مكون معلومات الاتصال
export const ContactInfo = ({ variant = 'full' }: { variant?: 'full' | 'minimal' }) => {
  if (variant === 'minimal') {
    return (
      <div className="text-center">
        <div className="font-semibold text-primary-600">
          {COMPANY_CONTACT.phone.displayNumber}
        </div>
        <div className="text-sm text-gray-500">
          {COMPANY_CONTACT.info.workingHours}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 rounded-lg p-4">
      <h3 className="font-semibold text-gray-800 mb-3 flex items-center gap-2">
        <PhoneIcon className="w-5 h-5 text-blue-600" />
        <span>معلومات الاتصال</span>
      </h3>

      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span className="text-gray-600">رقم الهاتف:</span>
          <span className="font-semibold text-primary-600">
            {COMPANY_CONTACT.phone.displayNumber}
          </span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-600">ساعات العمل:</span>
          <span className="font-medium">{COMPANY_CONTACT.info.workingHours}</span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-600">أيام العمل:</span>
          <span className="font-medium">{COMPANY_CONTACT.info.workingDays}</span>
        </div>

        <div className="flex justify-between">
          <span className="text-gray-600">وقت الرد:</span>
          <span className="font-medium">{COMPANY_CONTACT.info.responseTime}</span>
        </div>
      </div>
    </div>
  );
};

export default ContactButtons;
