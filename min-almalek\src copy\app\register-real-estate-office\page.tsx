'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { governorates } from '@/lib/data';

export default function RegisterRealEstateOfficePage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [formData, setFormData] = useState({
    // معلومات المكتب الأساسية
    officeName: '',
    licenseNumber: '',
    establishedYear: '',
    employeeCount: '',
    specialization: [],

    // معلومات التواصل
    phone: '',
    mobile: '',
    whatsapp: '',
    email: '',
    website: '',

    // العنوان
    governorate: '',
    city: '',
    area: '',
    street: '',
    buildingNumber: '',
    floor: '',

    // معلومات المدير
    managerName: '',
    managerPhone: '',
    managerEmail: '',
    managerExperience: '',

    // الوثائق
    licenseDocument: null,
    commercialRegister: null,
    managerID: null,

    // الخدمات
    services: [],
    serviceAreas: [],

    // معلومات إضافية
    description: '',
    workingHours: {
      sunday: { from: '09:00', to: '17:00', closed: false },
      monday: { from: '09:00', to: '17:00', closed: false },
      tuesday: { from: '09:00', to: '17:00', closed: false },
      wednesday: { from: '09:00', to: '17:00', closed: false },
      thursday: { from: '09:00', to: '17:00', closed: false },
      friday: { from: '09:00', to: '13:00', closed: false },
      saturday: { from: '', to: '', closed: true }
    }
  });

  const specializationOptions = [
    'العقارات السكنية',
    'العقارات التجارية',
    'العقارات الصناعية',
    'العقارات الزراعية',
    'الفيلات والقصور',
    'الشقق والمنازل',
    'المحلات التجارية',
    'المكاتب والمباني الإدارية',
    'المستودعات والمخازن',
    'الأراضي والمشاريع'
  ];

  const serviceOptions = [
    'بيع العقارات',
    'إيجار العقارات',
    'تقييم العقارات',
    'إدارة العقارات',
    'الاستشارات العقارية',
    'الاستشارات القانونية',
    'التمويل العقاري',
    'التأمين العقاري',
    'التسويق العقاري',
    'جولات افتراضية'
  ];

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayToggle = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter(item => item !== value)
        : [...prev[field], value]
    }));
  };

  const handleFileUpload = (field: string, file: File) => {
    setFormData(prev => ({
      ...prev,
      [field]: file
    }));
  };

  const nextStep = () => {
    if (currentStep < 5) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);

    try {
      // محاكاة إرسال البيانات إلى الخادم
      const applicationData = {
        ...formData,
        applicationId: `REO-${Date.now()}`,
        submittedAt: new Date().toISOString(),
        status: 'pending_review',
        subscriptionPlan: 'real-estate-office'
      };

      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 2000));

      // حفظ البيانات محلياً (في التطبيق الحقيقي سيتم إرسالها للخادم)
      localStorage.setItem('realEstateOfficeApplication', JSON.stringify(applicationData));

      setSubmitSuccess(true);

      // الانتقال إلى صفحة الدفع بعد 3 ثوان
      setTimeout(() => {
        window.location.href = `/subscription?plan=real-estate-office&application=${applicationData.applicationId}`;
      }, 3000);

    } catch (error) {
      console.error('خطأ في إرسال الطلب:', error);
      alert('حدث خطأ في إرسال الطلب. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const steps = [
    { number: 1, title: 'معلومات المكتب', icon: '🏢' },
    { number: 2, title: 'معلومات التواصل', icon: '📞' },
    { number: 3, title: 'العنوان والموقع', icon: '📍' },
    { number: 4, title: 'الوثائق والتراخيص', icon: '📋' },
    { number: 5, title: 'الخدمات والمراجعة', icon: '✅' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="py-8">
        <div className="container mx-auto px-4 max-w-4xl">
          {/* رأس الصفحة */}
          <div className="text-center mb-8">
            <div className="text-6xl mb-4">🏘️</div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">تسجيل مكتب عقاري</h1>
            <p className="text-gray-600">انضم إلى شبكة المكاتب العقارية الموثقة واحصل على شارة التوثيق الخاصة</p>
          </div>

          {/* شريط التقدم */}
          <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.number} className="flex items-center">
                  <div className={`flex items-center justify-center w-12 h-12 rounded-full border-2 ${
                    currentStep >= step.number
                      ? 'bg-primary-500 border-primary-500 text-white'
                      : 'bg-gray-100 border-gray-300 text-gray-500'
                  }`}>
                    <span className="text-lg">{step.icon}</span>
                  </div>
                  <div className="ml-3 hidden md:block">
                    <div className={`text-sm font-medium ${
                      currentStep >= step.number ? 'text-primary-600' : 'text-gray-500'
                    }`}>
                      الخطوة {step.number}
                    </div>
                    <div className="text-xs text-gray-500">{step.title}</div>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-8 h-0.5 mx-4 ${
                      currentStep > step.number ? 'bg-primary-500' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* محتوى النموذج */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            {submitSuccess ? (
              /* صفحة نجاح الإرسال */
              <div className="text-center py-12">
                <div className="text-6xl mb-6">🎉</div>
                <h2 className="text-3xl font-bold text-green-600 mb-4">تم إرسال الطلب بنجاح!</h2>
                <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                  شكراً لك على تقديم طلب تسجيل المكتب العقاري. سيتم مراجعة طلبك من قبل فريقنا المختص خلال 24-48 ساعة.
                </p>

                <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 mb-8 max-w-2xl mx-auto">
                  <h3 className="text-lg font-semibold text-blue-800 mb-4">الخطوات التالية:</h3>
                  <div className="space-y-3 text-sm text-blue-700">
                    <div className="flex items-center gap-3">
                      <span className="text-lg">📋</span>
                      <span>مراجعة الطلب والوثائق من قبل الإدارة</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-lg">✅</span>
                      <span>التحقق من صحة البيانات والتراخيص</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-lg">💳</span>
                      <span>إتمام عملية الدفع للباقة</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <span className="text-lg">🏘️</span>
                      <span>الحصول على شارة التوثيق وتفعيل الحساب</span>
                    </div>
                  </div>
                </div>

                <div className="bg-emerald-50 border border-emerald-200 rounded-xl p-6 mb-8 max-w-2xl mx-auto">
                  <div className="flex items-center gap-3 mb-4">
                    <span className="text-3xl">🏘️</span>
                    <div>
                      <h3 className="text-lg font-semibold text-emerald-800">باقة المكاتب العقارية</h3>
                      <p className="text-emerald-600">700,000 ل.س شهرياً</p>
                    </div>
                  </div>
                  <p className="text-sm text-emerald-700">
                    سيتم توجيهك إلى صفحة الدفع خلال ثوانٍ لإتمام الاشتراك...
                  </p>
                </div>

                <div className="flex justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                </div>
              </div>
            ) : (
              <>
                {/* الخطوة 1: معلومات المكتب */}
                {currentStep === 1 && (
              <div>
                <h2 className="text-2xl font-bold text-gray-800 mb-6">معلومات المكتب الأساسية</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">اسم المكتب العقاري *</label>
                    <input
                      type="text"
                      value={formData.officeName}
                      onChange={(e) => handleInputChange('officeName', e.target.value)}
                      placeholder="مكتب الأمانة العقاري"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">رقم الترخيص *</label>
                    <input
                      type="text"
                      value={formData.licenseNumber}
                      onChange={(e) => handleInputChange('licenseNumber', e.target.value)}
                      placeholder="RE-2024-001234"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">سنة التأسيس *</label>
                    <select
                      value={formData.establishedYear}
                      onChange={(e) => handleInputChange('establishedYear', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    >
                      <option value="">اختر السنة</option>
                      {Array.from({ length: 30 }, (_, i) => 2024 - i).map(year => (
                        <option key={year} value={year}>{year}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">عدد الموظفين *</label>
                    <select
                      value={formData.employeeCount}
                      onChange={(e) => handleInputChange('employeeCount', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    >
                      <option value="">اختر العدد</option>
                      <option value="1-5">1-5 موظفين</option>
                      <option value="6-10">6-10 موظفين</option>
                      <option value="11-20">11-20 موظف</option>
                      <option value="21-50">21-50 موظف</option>
                      <option value="50+">أكثر من 50 موظف</option>
                    </select>
                  </div>
                </div>

                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">التخصصات (اختر المناسب) *</label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {specializationOptions.map((spec) => (
                      <button
                        key={spec}
                        type="button"
                        onClick={() => handleArrayToggle('specialization', spec)}
                        className={`p-3 rounded-lg border-2 text-sm transition-all ${
                          formData.specialization.includes(spec)
                            ? 'border-primary-500 bg-primary-50 text-primary-700'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        {spec}
                      </button>
                    ))}
                  </div>
                </div>

                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">نبذة عن المكتب</label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="اكتب نبذة مختصرة عن المكتب وخبراته..."
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                  />
                </div>
              </div>
            )}

            {/* الخطوة 2: معلومات التواصل */}
            {currentStep === 2 && (
              <div>
                <h2 className="text-2xl font-bold text-gray-800 mb-6">معلومات التواصل</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف الثابت *</label>
                    <input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="+963 11 123 4567"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">رقم الموبايل *</label>
                    <input
                      type="tel"
                      value={formData.mobile}
                      onChange={(e) => handleInputChange('mobile', e.target.value)}
                      placeholder="+963 988 123 456"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">رقم الواتساب</label>
                    <input
                      type="tel"
                      value={formData.whatsapp}
                      onChange={(e) => handleInputChange('whatsapp', e.target.value)}
                      placeholder="+963 988 123 456"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">الموقع الإلكتروني</label>
                    <input
                      type="url"
                      value={formData.website}
                      onChange={(e) => handleInputChange('website', e.target.value)}
                      placeholder="www.office.com"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                </div>

                <div className="mt-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">معلومات مدير المكتب</h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">اسم المدير *</label>
                      <input
                        type="text"
                        value={formData.managerName}
                        onChange={(e) => handleInputChange('managerName', e.target.value)}
                        placeholder="أحمد محمد"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">هاتف المدير *</label>
                      <input
                        type="tel"
                        value={formData.managerPhone}
                        onChange={(e) => handleInputChange('managerPhone', e.target.value)}
                        placeholder="+963 988 123 456"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">بريد المدير الإلكتروني</label>
                      <input
                        type="email"
                        value={formData.managerEmail}
                        onChange={(e) => handleInputChange('managerEmail', e.target.value)}
                        placeholder="<EMAIL>"
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">سنوات الخبرة *</label>
                      <select
                        value={formData.managerExperience}
                        onChange={(e) => handleInputChange('managerExperience', e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      >
                        <option value="">اختر سنوات الخبرة</option>
                        <option value="1-3">1-3 سنوات</option>
                        <option value="4-7">4-7 سنوات</option>
                        <option value="8-15">8-15 سنة</option>
                        <option value="15+">أكثر من 15 سنة</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* الخطوة 3: العنوان والموقع */}
            {currentStep === 3 && (
              <div>
                <h2 className="text-2xl font-bold text-gray-800 mb-6">العنوان والموقع</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">المحافظة *</label>
                    <select
                      value={formData.governorate}
                      onChange={(e) => handleInputChange('governorate', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    >
                      <option value="">اختر المحافظة</option>
                      {governorates.map(gov => (
                        <option key={gov} value={gov}>{gov}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">المدينة أو الحي *</label>
                    <input
                      type="text"
                      value={formData.city}
                      onChange={(e) => handleInputChange('city', e.target.value)}
                      placeholder="دمشق، المالكي، الحمدانية..."
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">المنطقة أو الشارع *</label>
                    <input
                      type="text"
                      value={formData.area}
                      onChange={(e) => handleInputChange('area', e.target.value)}
                      placeholder="شارع الثورة، شارع بغداد..."
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">اسم الشارع التفصيلي</label>
                    <input
                      type="text"
                      value={formData.street}
                      onChange={(e) => handleInputChange('street', e.target.value)}
                      placeholder="شارع فرعي، زقاق..."
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">رقم المبنى</label>
                    <input
                      type="text"
                      value={formData.buildingNumber}
                      onChange={(e) => handleInputChange('buildingNumber', e.target.value)}
                      placeholder="123"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">الطابق</label>
                    <input
                      type="text"
                      value={formData.floor}
                      onChange={(e) => handleInputChange('floor', e.target.value)}
                      placeholder="الطابق الأول"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  </div>
                </div>

                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">المناطق المخدومة (اختر المناطق التي يخدمها المكتب)</label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                    {governorates.map((area) => (
                      <button
                        key={area}
                        type="button"
                        onClick={() => handleArrayToggle('serviceAreas', area)}
                        className={`p-3 rounded-lg border-2 text-sm transition-all ${
                          formData.serviceAreas.includes(area)
                            ? 'border-primary-500 bg-primary-50 text-primary-700'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        {area}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* الخطوة 4: الوثائق والتراخيص */}
            {currentStep === 4 && (
              <div>
                <h2 className="text-2xl font-bold text-gray-800 mb-6">الوثائق والتراخيص</h2>

                <div className="space-y-6">
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 text-yellow-800">
                      <span className="text-lg">⚠️</span>
                      <span className="font-medium">مطلوب: يرجى رفع جميع الوثائق بصيغة PDF أو صور واضحة</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">ترخيص المكتب العقاري *</label>
                      <input
                        type="file"
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={(e) => e.target.files && handleFileUpload('licenseDocument', e.target.files[0])}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                      <p className="text-xs text-gray-500 mt-1">ترخيص وزارة الإسكان أو الجهة المختصة</p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">السجل التجاري *</label>
                      <input
                        type="file"
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={(e) => e.target.files && handleFileUpload('commercialRegister', e.target.files[0])}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                      <p className="text-xs text-gray-500 mt-1">السجل التجاري ساري المفعول</p>
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">هوية مدير المكتب *</label>
                      <input
                        type="file"
                        accept=".pdf,.jpg,.jpeg,.png"
                        onChange={(e) => e.target.files && handleFileUpload('managerID', e.target.files[0])}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                        required
                      />
                      <p className="text-xs text-gray-500 mt-1">صورة عن الهوية الشخصية أو جواز السفر</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* الخطوة 5: الخدمات والمراجعة */}
            {currentStep === 5 && (
              <div>
                <h2 className="text-2xl font-bold text-gray-800 mb-6">الخدمات والمراجعة النهائية</h2>

                <div className="space-y-8">
                  {/* الخدمات المقدمة */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">الخدمات المقدمة</h3>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {serviceOptions.map((service) => (
                        <button
                          key={service}
                          type="button"
                          onClick={() => handleArrayToggle('services', service)}
                          className={`p-3 rounded-lg border-2 text-sm transition-all ${
                            formData.services.includes(service)
                              ? 'border-primary-500 bg-primary-50 text-primary-700'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          {service}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* ساعات العمل */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">ساعات العمل</h3>
                    <div className="space-y-3">
                      {Object.entries(formData.workingHours).map(([day, hours]) => {
                        const dayNames = {
                          sunday: 'الأحد',
                          monday: 'الاثنين',
                          tuesday: 'الثلاثاء',
                          wednesday: 'الأربعاء',
                          thursday: 'الخميس',
                          friday: 'الجمعة',
                          saturday: 'السبت'
                        };

                        return (
                          <div key={day} className="flex items-center gap-4">
                            <div className="w-20 text-sm font-medium text-gray-700">
                              {dayNames[day]}
                            </div>
                            <div className="flex items-center gap-2">
                              <input
                                type="checkbox"
                                checked={!hours.closed}
                                onChange={(e) => {
                                  const newHours = { ...formData.workingHours };
                                  newHours[day].closed = !e.target.checked;
                                  handleInputChange('workingHours', newHours);
                                }}
                                className="rounded"
                              />
                              <span className="text-sm text-gray-600">مفتوح</span>
                            </div>
                            {!hours.closed && (
                              <>
                                <input
                                  type="time"
                                  value={hours.from}
                                  onChange={(e) => {
                                    const newHours = { ...formData.workingHours };
                                    newHours[day].from = e.target.value;
                                    handleInputChange('workingHours', newHours);
                                  }}
                                  className="px-3 py-2 border border-gray-300 rounded text-sm"
                                />
                                <span className="text-gray-500">إلى</span>
                                <input
                                  type="time"
                                  value={hours.to}
                                  onChange={(e) => {
                                    const newHours = { ...formData.workingHours };
                                    newHours[day].to = e.target.value;
                                    handleInputChange('workingHours', newHours);
                                  }}
                                  className="px-3 py-2 border border-gray-300 rounded text-sm"
                                />
                              </>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* ملخص البيانات */}
                  <div className="bg-gray-50 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">ملخص البيانات</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-gray-700">اسم المكتب:</span>
                        <span className="text-gray-600 mr-2">{formData.officeName || 'غير محدد'}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">رقم الترخيص:</span>
                        <span className="text-gray-600 mr-2">{formData.licenseNumber || 'غير محدد'}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">الهاتف:</span>
                        <span className="text-gray-600 mr-2">{formData.phone || 'غير محدد'}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">البريد الإلكتروني:</span>
                        <span className="text-gray-600 mr-2">{formData.email || 'غير محدد'}</span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">العنوان:</span>
                        <span className="text-gray-600 mr-2">
                          {[formData.governorate, formData.city, formData.area].filter(Boolean).join(', ') || 'غير محدد'}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">مدير المكتب:</span>
                        <span className="text-gray-600 mr-2">{formData.managerName || 'غير محدد'}</span>
                      </div>
                    </div>
                  </div>

                  {/* معلومات الباقة */}
                  <div className="bg-gradient-to-r from-emerald-50 to-emerald-100 border border-emerald-200 rounded-xl p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <span className="text-3xl">🏘️</span>
                      <div>
                        <h3 className="text-lg font-semibold text-emerald-800">باقة المكاتب العقارية</h3>
                        <p className="text-emerald-600">700,000 ل.س شهرياً</p>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-emerald-700">
                      <div>✅ 30 إعلان عقاري مميز شهرياً</div>
                      <div>✅ شارة "مكتب عقاري موثق"</div>
                      <div>✅ صفحة مكتب متكاملة</div>
                      <div>✅ أدوات تقييم العقارات</div>
                      <div>✅ نظام إدارة العملاء CRM</div>
                      <div>✅ دعم فني متخصص</div>
                    </div>
                  </div>

                  {/* شروط وأحكام */}
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                      <input type="checkbox" className="mt-1" required />
                      <div className="text-sm text-blue-800">
                        <p className="font-medium mb-2">أوافق على الشروط والأحكام:</p>
                        <ul className="space-y-1 text-xs">
                          <li>• جميع المعلومات المقدمة صحيحة ودقيقة</li>
                          <li>• الالتزام بقوانين وأنظمة العمل العقاري</li>
                          <li>• دفع رسوم الاشتراك الشهرية في المواعيد المحددة</li>
                          <li>• الحفاظ على سمعة المنصة ومعايير الجودة</li>
                          <li>• تحديث البيانات عند حدوث أي تغيير</li>
                          <li>• الخضوع لعملية المراجعة والتوثيق</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* أزرار التنقل */}
            <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
              <button
                onClick={prevStep}
                disabled={currentStep === 1}
                className={`px-6 py-3 rounded-lg font-medium ${
                  currentStep === 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                السابق
              </button>

              {currentStep < 5 ? (
                <button
                  onClick={nextStep}
                  className="px-6 py-3 bg-primary-600 text-white rounded-lg font-medium hover:bg-primary-700"
                >
                  التالي
                </button>
              ) : (
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className={`px-8 py-3 rounded-lg font-medium transition-all ${
                    isSubmitting
                      ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                      : 'bg-green-600 text-white hover:bg-green-700'
                  }`}
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>جاري الإرسال...</span>
                    </div>
                  ) : (
                    '🚀 إرسال الطلب والانتقال للدفع'
                  )}
                </button>
              )}
            </div>
              </>
            )}
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}