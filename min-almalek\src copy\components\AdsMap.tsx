'use client';

import { useState } from 'react';

interface AdLocation {
  id: number;
  title: string;
  price: string;
  currency: string;
  location: string;
  category: string;
  coordinates: { lat: number; lng: number };
  image?: string;
  featured?: boolean;
}

const AdsMap = () => {
  const [selectedAd, setSelectedAd] = useState<AdLocation | null>(null);
  const [mapView, setMapView] = useState<'map' | 'list'>('map');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // بيانات تجريبية للإعلانات مع المواقع
  const adsWithLocations: AdLocation[] = [
    {
      id: 1,
      title: 'شقة للبيع في دمشق - المالكي',
      price: '85,000,000',
      currency: 'ل.س',
      location: 'دمشق - المالكي',
      category: 'real-estate',
      coordinates: { lat: 33.5138, lng: 36.2765 },
      featured: true
    },
    {
      id: 2,
      title: 'BMW X5 2020 فل أوبشن',
      price: '45,000',
      currency: '$',
      location: 'حلب - الفرقان',
      category: 'cars',
      coordinates: { lat: 36.2021, lng: 37.1343 }
    },
    {
      id: 3,
      title: 'فيلا للإيجار في حمص',
      price: '500,000',
      currency: 'ل.س',
      location: 'حمص - الوعر',
      category: 'real-estate',
      coordinates: { lat: 34.7394, lng: 36.7076 }
    },
    {
      id: 4,
      title: 'iPhone 15 Pro Max جديد',
      price: '1,200',
      currency: '$',
      location: 'دمشق - أبو رمانة',
      category: 'electronics',
      coordinates: { lat: 33.5024, lng: 36.2815 },
      featured: true
    },
    {
      id: 5,
      title: 'محل تجاري للبيع',
      price: '120,000,000',
      currency: 'ل.س',
      location: 'حماة - وسط البلد',
      category: 'real-estate',
      coordinates: { lat: 35.1324, lng: 36.7581 }
    }
  ];

  const categories = [
    { id: 'all', name: 'جميع التصنيفات', color: 'bg-gray-500' },
    { id: 'real-estate', name: 'العقارات', color: 'bg-blue-500' },
    { id: 'cars', name: 'السيارات', color: 'bg-red-500' },
    { id: 'electronics', name: 'الإلكترونيات', color: 'bg-green-500' },
    { id: 'jobs', name: 'الوظائف', color: 'bg-purple-500' }
  ];

  const filteredAds = selectedCategory === 'all'
    ? adsWithLocations
    : adsWithLocations.filter(ad => ad.category === selectedCategory);

  const getCategoryColor = (category: string) => {
    const cat = categories.find(c => c.id === category);
    return cat?.color || 'bg-gray-500';
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold text-gray-800">خريطة الإعلانات</h1>
        <div className="flex gap-3">
          <button
            onClick={() => setMapView('map')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              mapView === 'map'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            🗺️ الخريطة
          </button>
          <button
            onClick={() => setMapView('list')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              mapView === 'list'
                ? 'bg-primary-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            📋 القائمة
          </button>
        </div>
      </div>

      {/* فلتر التصنيفات */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
                selectedCategory === category.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
              }`}
            >
              <div className={`w-3 h-3 rounded-full ${category.color}`}></div>
              {category.name}
              <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
                {category.id === 'all'
                  ? adsWithLocations.length
                  : adsWithLocations.filter(ad => ad.category === category.id).length
                }
              </span>
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* الخريطة أو القائمة */}
        <div className="lg:col-span-2">
          {mapView === 'map' ? (
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              {/* محاكاة الخريطة */}
              <div className="relative h-96 bg-gradient-to-br from-green-100 to-blue-100 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-6xl mb-4">🗺️</div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">خريطة سوريا</h3>
                  <p className="text-gray-600">عرض الإعلانات على الخريطة</p>
                </div>

                {/* نقاط الإعلانات على الخريطة */}
                {filteredAds.map((ad, index) => (
                  <div
                    key={ad.id}
                    className="absolute cursor-pointer transform -translate-x-1/2 -translate-y-1/2"
                    style={{
                      left: `${20 + (index * 15)}%`,
                      top: `${30 + (index * 10)}%`
                    }}
                    onClick={() => setSelectedAd(ad)}
                  >
                    <div className={`w-8 h-8 rounded-full ${getCategoryColor(ad.category)} flex items-center justify-center text-white text-sm font-bold shadow-lg hover:scale-110 transition-transform`}>
                      {ad.featured ? '⭐' : index + 1}
                    </div>
                    {selectedAd?.id === ad.id && (
                      <div className="absolute top-10 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-xl p-3 min-w-48 z-10">
                        <div className="text-sm font-semibold text-gray-800 mb-1">
                          {ad.title}
                        </div>
                        <div className="text-primary-600 font-bold mb-1">
                          {ad.price} {ad.currency}
                        </div>
                        <div className="text-xs text-gray-600">
                          📍 {ad.location}
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            window.location.href = `/ad/${ad.id}`;
                          }}
                          className="mt-2 w-full bg-primary-600 text-white py-1 px-2 rounded text-xs hover:bg-primary-700"
                        >
                          عرض التفاصيل
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* أدوات التحكم في الخريطة */}
              <div className="p-4 bg-gray-50 border-t">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <button className="flex items-center gap-2 px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                      <span>🔍</span>
                      <span className="text-sm">تكبير</span>
                    </button>
                    <button className="flex items-center gap-2 px-3 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50">
                      <span>📍</span>
                      <span className="text-sm">موقعي</span>
                    </button>
                  </div>
                  <div className="text-sm text-gray-600">
                    {filteredAds.length} إعلان في المنطقة
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredAds.map((ad) => (
                <div key={ad.id} className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-start gap-4">
                    <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                      <span className="text-gray-400 text-2xl">🖼️</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="font-semibold text-gray-800 line-clamp-2">
                          {ad.title}
                        </h3>
                        {ad.featured && (
                          <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                            مميز
                          </span>
                        )}
                      </div>
                      <div className="text-xl font-bold text-primary-600 mb-2">
                        {ad.price} {ad.currency}
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span>📍 {ad.location}</span>
                        <span className={`w-3 h-3 rounded-full ${getCategoryColor(ad.category)}`}></span>
                      </div>
                    </div>
                    <div className="flex flex-col gap-2">
                      <a
                        href={`/ad/${ad.id}`}
                        className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors text-sm text-center"
                      >
                        عرض التفاصيل
                      </a>
                      <button
                        onClick={() => setSelectedAd(ad)}
                        className="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                      >
                        عرض على الخريطة
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* الشريط الجانبي */}
        <div className="space-y-6">
          {/* إحصائيات سريعة */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">إحصائيات المنطقة</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">إجمالي الإعلانات</span>
                <span className="font-semibold text-gray-800">{filteredAds.length}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">الإعلانات المميزة</span>
                <span className="font-semibold text-gray-800">
                  {filteredAds.filter(ad => ad.featured).length}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-gray-600">متوسط السعر</span>
                <span className="font-semibold text-gray-800">
                  {Math.round(filteredAds.reduce((sum, ad) => sum + parseInt(ad.price.replace(/,/g, '')), 0) / filteredAds.length).toLocaleString()} ل.س
                </span>
              </div>
            </div>
          </div>

          {/* البحث في المنطقة */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">البحث في المنطقة</h3>
            <div className="space-y-4">
              <input
                type="text"
                placeholder="ابحث في المدينة أو الحي أو الشارع..."
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
              <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                <option value="">نطاق السعر</option>
                <option value="0-1000000">أقل من مليون</option>
                <option value="1000000-5000000">1-5 مليون</option>
                <option value="5000000-10000000">5-10 مليون</option>
                <option value="10000000+">أكثر من 10 مليون</option>
              </select>
              <button className="w-full bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 transition-colors">
                بحث
              </button>
            </div>
          </div>

          {/* المناطق الشائعة */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">المناطق الشائعة</h3>
            <div className="space-y-2">
              {[
                { name: 'دمشق - المالكي', count: 45 },
                { name: 'حلب - الفرقان', count: 32 },
                { name: 'حمص - الوعر', count: 28 },
                { name: 'دمشق - أبو رمانة', count: 24 },
                { name: 'حماة - وسط البلد', count: 18 }
              ].map((area, index) => (
                <button
                  key={index}
                  className="w-full flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors text-right"
                >
                  <span className="text-gray-700">{area.name}</span>
                  <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                    {area.count}
                  </span>
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdsMap;
