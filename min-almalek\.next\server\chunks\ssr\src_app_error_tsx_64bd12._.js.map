{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/app/error.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport Link from 'next/link';\n\nexport default function Error({\n  error,\n  reset,\n}: {\n  error: Error & { digest?: string };\n  reset: () => void;\n}) {\n  useEffect(() => {\n    console.error('خطأ في التطبيق:', error);\n  }, [error]);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n      <div className=\"max-w-md mx-auto text-center px-4\">\n        <div className=\"text-8xl mb-8\">⚠️</div>\n        \n        <h1 className=\"text-3xl font-bold text-gray-800 mb-4\">\n          حدث خطأ غير متوقع\n        </h1>\n        \n        <p className=\"text-gray-600 mb-8 leading-relaxed\">\n          نعتذر، حدث خطأ أثناء تحميل هذه الصفحة. \n          يرجى المحاولة مرة أخرى أو العودة إلى الصفحة الرئيسية.\n        </p>\n        \n        <div className=\"space-y-4\">\n          <button\n            onClick={reset}\n            className=\"block w-full bg-primary-600 text-white py-3 px-6 rounded-lg hover:bg-primary-700 transition-colors font-semibold\"\n          >\n            المحاولة مرة أخرى\n          </button>\n          \n          <Link\n            href=\"/\"\n            className=\"block w-full border border-primary-600 text-primary-600 py-3 px-6 rounded-lg hover:bg-primary-50 transition-colors font-semibold\"\n          >\n            العودة للصفحة الرئيسية\n          </Link>\n        </div>\n        \n        {process.env.NODE_ENV === 'development' && (\n          <details className=\"mt-8 text-left\">\n            <summary className=\"cursor-pointer text-sm text-gray-500 mb-2\">\n              تفاصيل الخطأ (للمطورين)\n            </summary>\n            <pre className=\"bg-gray-100 p-4 rounded-lg text-xs overflow-auto\">\n              {error.message}\n              {error.stack && (\n                <>\n                  {'\\n\\n'}\n                  {error.stack}\n                </>\n              )}\n            </pre>\n          </details>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS,MAAM,EAC5B,KAAK,EACL,KAAK,EAIN;IACC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,KAAK,CAAC,mBAAmB;IACnC,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BAAgB;;;;;;8BAE/B,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAItD,8OAAC;oBAAE,WAAU;8BAAqC;;;;;;8BAKlD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAID,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;gBAKF,oDAAyB,+BACxB,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAQ,WAAU;sCAA4C;;;;;;sCAG/D,8OAAC;4BAAI,WAAU;;gCACZ,MAAM,OAAO;gCACb,MAAM,KAAK,kBACV;;wCACG;wCACA,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9B"}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}