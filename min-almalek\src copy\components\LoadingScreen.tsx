'use client';

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import Logo from './Logo';

interface LoadingScreenProps {
  isLoading: boolean;
  onComplete?: () => void;
}

const LoadingScreen = ({ isLoading, onComplete }: LoadingScreenProps) => {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(isLoading);

  useEffect(() => {
    if (isLoading) {
      setIsVisible(true);
      setProgress(0);

      // محاكاة تقدم التحميل السريع
      const interval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            // تأخير قصير جداً قبل إخفاء الشاشة
            setTimeout(() => {
              setIsVisible(false);
              onComplete?.();
            }, 100);
            return 100;
          }
          return prev + Math.random() * 50; // زيادة سرعة التقدم
        });
      }, 50); // تقليل الفترة الزمنية

      return () => clearInterval(interval);
    }
  }, [isLoading, onComplete]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 z-50 flex items-center justify-center backdrop-blur-sm">
      {/* محتوى شاشة التحميل - الشعار فقط */}
      <div className="flex flex-col items-center justify-center">
        {/* الشعار مع إضاءة قوية شفافة */}
        <div className="relative">
          {/* إضاءة شفافة قوية متعددة الطبقات */}
          <div className="absolute inset-0 -m-20">
            <div className="w-80 h-80 bg-white/25 rounded-full blur-3xl animate-pulse"></div>
          </div>
          <div className="absolute inset-0 -m-16">
            <div className="w-64 h-64 bg-white/35 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '0.3s' }}></div>
          </div>
          <div className="absolute inset-0 -m-12">
            <div className="w-48 h-48 bg-white/45 rounded-full blur-xl animate-pulse" style={{ animationDelay: '0.6s' }}></div>
          </div>
          <div className="absolute inset-0 -m-8">
            <div className="w-32 h-32 bg-white/55 rounded-full blur-lg animate-pulse" style={{ animationDelay: '0.9s' }}></div>
          </div>
          <div className="absolute inset-0 -m-4">
            <div className="w-24 h-24 bg-white/40 rounded-full blur-md animate-pulse" style={{ animationDelay: '1.2s' }}></div>
          </div>

          {/* الشعار مع إضاءة قوية */}
          <div className="relative z-10">
            <div className="transform transition-all duration-1000 ease-in-out animate-pulse">
              <div className="filter drop-shadow-2xl" style={{ filter: 'drop-shadow(0 0 20px rgba(255, 255, 255, 0.8)) drop-shadow(0 0 40px rgba(255, 255, 255, 0.6)) drop-shadow(0 0 60px rgba(255, 255, 255, 0.4))' }}>
                <Logo variant="transparent" size="loading" showText={false} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// مكون لإدارة التنقل مع شاشة التحميل
export const NavigationLoader = () => {
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const [previousPath, setPreviousPath] = useState(pathname);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (isMounted && pathname !== previousPath) {
      setIsLoading(true);
      setPreviousPath(pathname);
    }
  }, [pathname, previousPath, isMounted]);

  const handleLoadingComplete = () => {
    setIsLoading(false);
  };

  if (!isMounted) {
    return null;
  }

  return (
    <LoadingScreen
      isLoading={isLoading}
      onComplete={handleLoadingComplete}
    />
  );
};

export default LoadingScreen;
