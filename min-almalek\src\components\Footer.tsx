import Link from 'next/link';
import Logo from './Logo';
import MyCvLogo from './MyCvLogo';
import ContactButtons, { ContactInfo } from '@/components/ContactButtons';
import { COMPANY_CONTACT } from '@/lib/contact';

const Footer = () => {
  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer */}
      <div className="container mx-auto px-4 py-8 md:py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
          {/* Company Info */}
          <div>
            <div className="mb-6">
              <div className="flex items-center gap-3 mb-2">
                <Logo variant="transparent" size="lg" showText={true} textColor="yellow" href="/" isFooter={true} />
                <div className="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold">
                  سوريا
                </div>
              </div>
            </div>
            <p className="text-gray-400 mb-4">
              موقع من المالك هو أكبر موقع للإعلانات المبوبة في سوريا، يوفر منصة آمنة وسهلة للبيع والشراء.
            </p>



            {/* مواقع التواصل الاجتماعي */}
            <div className="mt-6">
              <h5 className="font-medium mb-3 text-sm text-gray-300">تابعنا على</h5>
              <div className="flex gap-2">
                <a href="#" className="w-8 h-8 bg-gray-800 hover:bg-blue-600 rounded-md flex items-center justify-center transition-all duration-300 group" title="فيسبوك">
                  <svg viewBox="0 0 24 24" className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" fill="currentColor">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>
                <a href="#" className="w-8 h-8 bg-gray-800 hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 rounded-md flex items-center justify-center transition-all duration-300 group" title="انستغرام">
                  <svg viewBox="0 0 24 24" className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" fill="currentColor">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                  </svg>
                </a>
                <a href="#" className="w-8 h-8 bg-gray-800 hover:bg-red-600 rounded-md flex items-center justify-center transition-all duration-300 group" title="يوتيوب">
                  <svg viewBox="0 0 24 24" className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" fill="currentColor">
                    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                  </svg>
                </a>
                <a href="#" className="w-8 h-8 bg-gray-800 hover:bg-gray-900 rounded-md flex items-center justify-center transition-all duration-300 group" title="X (تويتر)">
                  <svg viewBox="0 0 24 24" className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" fill="currentColor">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                  </svg>
                </a>
                <a href="#" className="w-8 h-8 bg-gray-800 hover:bg-black rounded-md flex items-center justify-center transition-all duration-300 group" title="تيك توك">
                  <svg viewBox="0 0 24 24" className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" fill="currentColor">
                    <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                  </svg>
                </a>
                <a href="#" className="w-8 h-8 bg-gray-800 hover:bg-blue-700 rounded-md flex items-center justify-center transition-all duration-300 group" title="لينكد إن">
                  <svg viewBox="0 0 24 24" className="w-4 h-4 text-gray-400 group-hover:text-white transition-colors" fill="currentColor">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6">روابط سريعة</h4>
            <ul className="space-y-3">
              <li><Link href="/about" className="text-gray-400 hover:text-white transition-colors">من نحن</Link></li>
              <li><Link href="/pricing" className="text-gray-400 hover:text-white transition-colors">أسعار الإعلانات</Link></li>
              <li><Link href="/safety" className="text-gray-400 hover:text-white transition-colors">نصائح الأمان</Link></li>
              <li><Link href="/faq" className="text-gray-400 hover:text-white transition-colors">الأسئلة الشائعة</Link></li>
              <li><Link href="/dashboard" className="text-gray-400 hover:text-white transition-colors">لوحة التحكم</Link></li>
              <li><Link href="/contact" className="text-gray-400 hover:text-white transition-colors">اتصل بنا</Link></li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h4 className="text-lg font-semibold mb-6">التصنيفات الرئيسية</h4>
            <ul className="space-y-3">
              <li><Link href="/category/real-estate" className="text-gray-400 hover:text-white transition-colors">العقارات</Link></li>
              <li><Link href="/category/cars" className="text-gray-400 hover:text-white transition-colors">السيارات</Link></li>
              <li><Link href="/category/electronics" className="text-gray-400 hover:text-white transition-colors">الإلكترونيات</Link></li>
              <li><Link href="/jobs" className="text-gray-400 hover:text-white transition-colors">الوظائف</Link></li>
              <li><Link href="/category/services" className="text-gray-400 hover:text-white transition-colors">الخدمات</Link></li>
              <li><Link href="/category/fashion" className="text-gray-400 hover:text-white transition-colors">الأزياء</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-6">معلومات التواصل</h4>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <img
                  src="/images/الفوتر/دمشق، سوريا.png"
                  alt="الموقع"
                  className="w-5 h-5 mt-1 opacity-80"
                  style={{ filter: 'brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(200deg)' }}
                />
                <div>
                  <p className="text-white font-semibold">دمشق، سوريا</p>
                  <p className="text-gray-400 text-sm">العنوان الرئيسي</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <img
                  src="/images/الفوتر/+963 988 652 401.png"
                  alt="الهاتف"
                  className="w-5 h-5 opacity-80"
                  style={{ filter: 'brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(200deg)' }}
                />
                <div>
                  <p className="text-white font-semibold">+963 988 652 401</p>
                  <p className="text-gray-400 text-sm">للاتصال والواتساب</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <img
                  src="/images/الفوتر/<EMAIL>"
                  alt="البريد الإلكتروني"
                  className="w-5 h-5 opacity-80"
                  style={{ filter: 'brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(200deg)' }}
                />
                <p className="text-gray-400"><EMAIL></p>
              </div>
              <div className="flex items-center gap-3">
                <img
                  src="/images/الفوتر/من السبت إلى الخميس.png"
                  alt="أوقات العمل"
                  className="w-5 h-5 opacity-80"
                  style={{ filter: 'brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(200deg)' }}
                />
                <div>
                  <p className="text-gray-400">من السبت إلى الخميس</p>
                  <p className="text-gray-400 text-sm">وقت الرد: خلال 24 ساعة</p>
                </div>
              </div>
            </div>


          </div>
        </div>
      </div>

      {/* Apps Download Section */}
      <div className="border-t border-gray-800 bg-gray-800/50">
        <div className="container mx-auto px-4 py-4 md:py-6">
          {/* Apps and Payment Methods in One Row */}
          <div className="flex flex-wrap items-center justify-center gap-4 md:gap-6">
            {/* تطبيق MyCv */}
            <div className="flex items-center gap-3 bg-gray-900 rounded-lg p-3 min-w-[240px]">
              <MyCvLogo size="sm" variant="square" />
              <div className="flex-1 min-w-0">
                <h6 className="font-medium text-white text-sm">تطبيق MyCv</h6>
                <p className="text-gray-400 text-xs mb-2">منصة السير الذاتية والتوظيف</p>
                <div className="flex gap-1">
                  <a href="#" className="bg-black text-white px-2 py-1 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs">
                    <svg viewBox="0 0 24 24" className="w-3 h-3 text-white" fill="currentColor">
                      <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                    </svg>
                    <span className="hidden sm:inline">AppStore</span>
                  </a>
                  <a href="#" className="bg-black text-white px-2 py-1 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs">
                    <svg viewBox="0 0 24 24" className="w-3 h-3 text-white" fill="currentColor">
                      <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                    </svg>
                    <span className="hidden sm:inline">Android</span>
                  </a>
                </div>
              </div>
            </div>

            {/* تطبيق من المالك */}
            <div className="flex items-center gap-3 bg-gray-900 rounded-lg p-3 min-w-[240px]">
              <Logo variant="transparent" size="sm" showText={false} />
              <div className="flex-1 min-w-0">
                <h6 className="font-medium text-white text-sm">تطبيق من المالك</h6>
                <p className="text-gray-400 text-xs mb-2">منصة الإعلانات المبوبة</p>
                <div className="flex gap-1">
                  <a href="#" className="bg-black text-white px-2 py-1 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs">
                    <svg viewBox="0 0 24 24" className="w-3 h-3 text-white" fill="currentColor">
                      <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                    </svg>
                    <span className="hidden sm:inline">AppStore</span>
                  </a>
                  <a href="#" className="bg-black text-white px-2 py-1 rounded-md hover:bg-gray-800 transition-colors flex items-center gap-1 text-xs">
                    <svg viewBox="0 0 24 24" className="w-3 h-3 text-white" fill="currentColor">
                      <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                    </svg>
                    <span className="hidden sm:inline">Android</span>
                  </a>
                </div>
              </div>
            </div>



            {/* QR Code Section */}
            <div className="flex items-center gap-4 bg-gray-900 rounded-lg p-4 min-w-[200px]">
              <div className="relative flex-shrink-0">
                {/* QR Code Background */}
                <div className="w-20 h-20 bg-white rounded-md p-1 shadow-lg">
                  {/* Real QR Code for Min Almalek website */}
                  <img
                    src="https://api.qrserver.com/v1/create-qr-code/?size=80x80&data=https://min-almalek.com&bgcolor=ffffff&color=000000&margin=1"
                    alt="QR Code for Min Almalek"
                    className="w-full h-full rounded"
                  />

                  {/* Logo overlay in center */}
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-4 h-4 bg-white rounded-sm flex items-center justify-center shadow-sm">
                      <Logo variant="dark" size="xs" showText={false} />
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex-1">
                <h6 className="font-medium text-white text-sm">موقع من المالك</h6>
                <p className="text-gray-400 text-xs mb-1">امسح للوصول السريع</p>
                <p className="text-gray-500 text-xs">min-almalek.com</p>
              </div>
            </div>

            {/* Partner Logos and Payment Methods Section */}
            <div className="flex items-center gap-4 bg-gray-900 rounded-lg p-4 min-w-[200px]">
              <div className="flex-1">
                {/* Partner Logos */}
                <div className="flex items-center justify-center gap-4 mb-3">
                  {/* MADENLİ Group Logo */}
                  <div className="flex items-center justify-center">
                    <img
                      src="/images/madenli group LOGO.jpg"
                      alt="MADENLİ Group"
                      className="h-8 w-auto object-contain rounded-sm"
                    />
                  </div>

                  {/* MyCv Logo */}
                  <div className="flex items-center justify-center">
                    <img
                      src="/images/MyCV Logo.jpg"
                      alt="MyCv"
                      className="h-8 w-auto object-contain rounded-sm"
                    />
                  </div>
                </div>

                {/* Payment Methods */}
                <div className="flex flex-wrap items-center justify-center gap-2">
                  {/* Visa */}
                  <img
                    src="https://upload.wikimedia.org/wikipedia/commons/4/41/Visa_Logo.png"
                    alt="Visa"
                    className="h-4 w-auto object-contain"
                  />

                  {/* MasterCard */}
                  <img
                    src="https://upload.wikimedia.org/wikipedia/commons/0/04/Mastercard-logo.png"
                    alt="MasterCard"
                    className="h-4 w-auto object-contain"
                  />

                  {/* PayPal */}
                  <img
                    src="https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg"
                    alt="PayPal"
                    className="h-4 w-auto object-contain"
                  />

                  {/* Cash App */}
                  <img
                    src="/images/cash-app-logo.svg"
                    alt="Cash App"
                    className="h-4 w-auto object-contain"
                  />

                  {/* Apple Pay */}
                  <div className="h-4 flex items-center gap-1">
                    <img
                      src="https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg"
                      alt="Apple"
                      className="h-4 w-3 object-contain invert"
                    />
                    <span className="text-xs font-semibold text-white">Pay</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="text-gray-400 text-sm">
              © 2017-2025 من المالك. جميع الحقوق محفوظة.
            </div>
            <div className="flex flex-wrap gap-6 text-sm">
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                سياسة الخصوصية
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors">
                شروط الاستخدام
              </Link>
              <Link href="/cookies" className="text-gray-400 hover:text-white transition-colors">
                سياسة الكوكيز
              </Link>
              <Link href="/sitemap" className="text-gray-400 hover:text-white transition-colors">
                خريطة الموقع
              </Link>
            </div>
          </div>
        </div>
      </div>


    </footer>
  );
};

export default Footer;
