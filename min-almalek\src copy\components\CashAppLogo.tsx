import Image from 'next/image';

interface CashAppLogoProps {
  size?: 'xs' | 'sm' | 'md' | 'lg';
  className?: string;
}

const CashAppLogo = ({ size = 'md', className = '' }: CashAppLogoProps) => {
  const sizeClasses = {
    xs: 'w-4 h-4',
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const dimensions = {
    xs: { width: 16, height: 16 },
    sm: { width: 24, height: 24 },
    md: { width: 32, height: 32 },
    lg: { width: 48, height: 48 }
  };

  return (
    <div className={`${sizeClasses[size]} ${className} flex items-center justify-center`}>
      <Image
        src="/images/cash-app-logo.svg"
        alt="Cash App"
        width={dimensions[size].width}
        height={dimensions[size].height}
        className="object-contain"
        priority
      />
    </div>
  );
};

export default CashAppLogo;
