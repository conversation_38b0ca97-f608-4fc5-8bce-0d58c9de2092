'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Ad } from '@/lib/data';
import VerificationBadge from './VerificationBadge';
import SafeTimeDisplay from './SafeTimeDisplay';
import { ContactUtils } from '@/lib/contact';
import { useToast } from '@/components/ToastManager';
import MessageModal from './MessageModal';

interface AdDetailsProps {
  ad: Ad;
}

const AdDetails = ({ ad }: AdDetailsProps) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showContactInfo, setShowContactInfo] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const toast = useToast();

  // دالة لتنسيق الموقع
  const getLocationString = () => {
    return `${ad.location.governorate} - ${ad.location.city}${ad.location.area ? ` - ${ad.location.area}` : ''}`;
  };

  // دوال المشاركة
  const handleFacebookShare = () => {
    const url = encodeURIComponent(window.location.href);
    const text = encodeURIComponent(`${ad.title} - ${ad.price.toLocaleString()} ${ad.currency === 'SYP' ? 'ل.س' : '$'}`);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}&quote=${text}`, '_blank');
  };

  const handleWhatsAppShare = () => {
    const text = encodeURIComponent(`${ad.title}\n${ad.price.toLocaleString()} ${ad.currency === 'SYP' ? 'ل.س' : '$'}\n${window.location.href}`);
    window.open(`https://wa.me/?text=${text}`, '_blank');
  };

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      toast.showSuccess('تم نسخ الرابط بنجاح', 'يمكنك الآن مشاركة الرابط مع الآخرين');
    } catch (error) {
      toast.showError('فشل في نسخ الرابط', 'يرجى المحاولة مرة أخرى');
    }
  };

  // دوال التواصل
  const handleCallNow = () => {
    window.open(`tel:${ad.seller.phone}`, '_self');
  };

  const handleSendMessage = () => {
    setShowMessageModal(true);
  };



  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % ad.images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + ad.images.length) % ad.images.length);
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
  };

  return (
    <div className="container mx-auto px-4 py-8" suppressHydrationWarning={true}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* العمود الرئيسي */}
        <div className="lg:col-span-2">
          {/* معرض الصور */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-6">
            <div className="relative h-96">
              {ad.images.length > 0 ? (
                <>
                  <img
                    src={ad.images[currentImageIndex]}
                    alt={ad.title}
                    className="w-full h-full object-cover"
                  />
                  {ad.images.length > 1 && (
                    <>
                      <button
                        onClick={prevImage}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-black/70"
                      >
                        ←
                      </button>
                      <button
                        onClick={nextImage}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-black/70"
                      >
                        →
                      </button>
                    </>
                  )}

                  {/* مؤشر الصور */}
                  {ad.images.length > 1 && (
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                      {ad.images.map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentImageIndex(index)}
                          className={`w-3 h-3 rounded-full ${
                            index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                          }`}
                        />
                      ))}
                    </div>
                  )}
                </>
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-400 text-6xl">🖼️</span>
                </div>
              )}

              {/* الشارات */}
              <div className="absolute top-4 right-4 flex flex-col gap-2">
                {ad.featured && (
                  <span className="bg-yellow-500 text-white text-sm px-3 py-1 rounded-full font-medium">
                    مميز
                  </span>
                )}
                {ad.status === 'active' && (
                  <span className="bg-green-500 text-white text-sm px-3 py-1 rounded-full font-medium">
                    نشط
                  </span>
                )}
              </div>
            </div>

            {/* صور مصغرة */}
            {ad.images.length > 1 && (
              <div className="p-4 border-t">
                <div className="flex gap-2 overflow-x-auto">
                  {ad.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                        index === currentImageIndex ? 'border-primary-500' : 'border-gray-200'
                      }`}
                    >
                      <img
                        src={image}
                        alt={`صورة ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* تفاصيل الإعلان */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 mb-2">{ad.title}</h1>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <span>📍 {getLocationString()}</span>
                  <span>📂 {ad.category} - {ad.subcategory}</span>
                  <span>👁️ {ad.views} مشاهدة</span>
                  <span>📅 <SafeTimeDisplay dateString={ad.createdAt} /></span>
                </div>
              </div>
              <button
                onClick={toggleFavorite}
                className="text-2xl hover:scale-110 transition-transform"
              >
                {isFavorite ? '❤️' : '🤍'}
              </button>
            </div>

            <div className="flex items-center justify-between mb-6 p-4 bg-gray-50 rounded-lg">
              <div>
                <div className="text-4xl font-bold text-primary-600">
                  {ad.price.toLocaleString()} {ad.currency === 'SYP' ? 'ل.س' : ad.currency === 'USD' ? '$' : '€'}
                </div>
                {ad.condition && (
                  <div className="text-sm text-gray-600 mt-1">
                    الحالة: <span className="font-medium">{ad.condition}</span>
                  </div>
                )}
              </div>
              <div className="flex gap-2">
                <button
                  onClick={handleCallNow}
                  className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-all duration-300 opacity-80 hover:opacity-100"
                  style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}
                >
                  <span className="flex items-center gap-2">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z"/>
                    </svg>
                    اتصل الآن
                  </span>
                </button>
                <button
                  onClick={handleSendMessage}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-all duration-300 opacity-80 hover:opacity-100"
                  style={{filter: 'drop-shadow(0 0 4px rgba(75, 85, 99, 0.3))'}}
                >
                  <span className="flex items-center gap-2">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M6,9V7H18V9H6M14,11V13H6V11H14M16,15V17H6V15H16Z"/>
                    </svg>
                    رسالة
                  </span>
                </button>
              </div>
            </div>

            <div className="prose max-w-none">
              <h3 className="text-xl font-semibold text-gray-800 mb-3">الوصف</h3>
              <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                {ad.description}
              </p>
            </div>
          </div>

          {/* المواصفات */}
          {ad.specifications && Object.keys(ad.specifications).length > 0 && (
            <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
              <h3 className="text-xl font-semibold text-gray-800 mb-4">المواصفات</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(ad.specifications).map(([key, value]) => (
                  <div key={key} className="flex justify-between py-2 border-b border-gray-100">
                    <span className="font-medium text-gray-700">{key}</span>
                    <span className="text-gray-600">{String(value)}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* أزرار المشاركة */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">شارك الإعلان</h3>
            <div className="flex gap-3 flex-wrap">
              <button
                onClick={handleFacebookShare}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 opacity-80 hover:opacity-100"
                style={{filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.3))'}}
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                فيسبوك
              </button>
              <button
                onClick={handleWhatsAppShare}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-all duration-300 opacity-80 hover:opacity-100"
                style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
                </svg>
                واتساب
              </button>
              <button
                onClick={handleCopyLink}
                className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-all duration-300 opacity-80 hover:opacity-100"
                style={{filter: 'drop-shadow(0 0 4px rgba(75, 85, 99, 0.3))'}}
              >
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/>
                </svg>
                نسخ الرابط
              </button>
            </div>
          </div>
        </div>

        {/* الشريط الجانبي */}
        <div className="lg:col-span-1">
          {/* معلومات البائع */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">معلومات البائع</h3>

            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                <span className="text-2xl">
                  {ad.seller.type === 'business' ? '🏢' : '👤'}
                </span>
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-semibold text-gray-800">{ad.seller.name}</span>
                  {ad.seller.verified && (
                    <VerificationBadge
                      badgeId={
                        ad.seller.type === 'business' && ad.seller.name.includes('مكتب') && ad.seller.name.includes('عقاري')
                          ? 'real-estate-office'
                          : ad.seller.type === 'business'
                            ? 'business-verified'
                            : 'verified-basic'
                      }
                      size="sm"
                    />
                  )}
                </div>
                <div className="text-sm text-gray-600">
                  {ad.seller.type === 'business' ? 'شركة' : 'فرد'}
                </div>
              </div>
            </div>

            <div className="space-y-2 text-sm text-gray-600 mb-4">
              <div>📍 {getLocationString()}</div>
              <div>📅 عضو منذ <SafeTimeDisplay dateString={ad.createdAt} /></div>
              <div>⭐ {ad.seller.rating.toFixed(1)} ({ad.seller.totalRatings} تقييم)</div>
            </div>

            {showContactInfo ? (
              <div className="space-y-3">
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">رقم الهاتف</div>
                  <div className="font-semibold text-gray-800">{ad.seller.phone}</div>
                </div>
                {ad.seller.email && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="text-sm text-gray-600 mb-1">البريد الإلكتروني</div>
                    <div className="font-semibold text-gray-800">{ad.seller.email}</div>
                  </div>
                )}

                {/* أزرار التواصل */}
                <div className="flex gap-2 mt-4">
                  <button
                    onClick={handleCallNow}
                    className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-all duration-300 font-semibold opacity-80 hover:opacity-100"
                    style={{filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.3))'}}
                  >
                    <span className="flex items-center justify-center gap-2">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z"/>
                      </svg>
                      اتصل الآن
                    </span>
                  </button>
                  <button
                    onClick={handleSendMessage}
                    className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-all duration-300 font-semibold opacity-80 hover:opacity-100"
                    style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}
                  >
                    <span className="flex items-center justify-center gap-2">
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/>
                      </svg>
                      رسالة
                    </span>
                  </button>
                </div>
              </div>
            ) : (
              <button
                onClick={() => setShowContactInfo(true)}
                className="w-full bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 transition-all duration-300 font-semibold opacity-80 hover:opacity-100"
                style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}
              >
                <span className="flex items-center justify-center gap-2">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
                  </svg>
                  إظهار معلومات الاتصال
                </span>
              </button>
            )}

            <div className="mt-4 pt-4 border-t border-gray-100">
              <Link
                href={`/seller/${ad.seller.name}`}
                className="text-primary-600 hover:text-primary-700 text-sm font-medium"
              >
                عرض جميع إعلانات البائع →
              </Link>
            </div>
          </div>

          {/* خريطة الموقع */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              🗺️ موقع الإعلان
            </h3>
            <div className="relative h-64 bg-gray-100 rounded-lg overflow-hidden">
              <iframe
                src={`https://www.google.com/maps?q=${encodeURIComponent(getLocationString())}&output=embed&z=14`}
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                className="rounded-lg"
                title={`خريطة موقع ${getLocationString()}`}
              />
              <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-2 rounded-lg shadow-md">
                <div className="text-sm font-medium text-gray-800">📍 {getLocationString()}</div>
              </div>
            </div>
            <div className="mt-4 flex items-center justify-between">
              <div className="text-sm text-gray-600">
                انقر على الخريطة للحصول على الاتجاهات
              </div>
              <a
                href={`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(getLocationString())}`}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-all duration-300 text-sm opacity-80 hover:opacity-100"
                style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}
              >
                <span className="flex items-center gap-2">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12,2C8.13,2 5,5.13 5,9C5,14.25 12,22 12,22C12,22 19,14.25 19,9C19,5.13 15.87,2 12,2M12,11.5A2.5,2.5 0 0,1 9.5,9A2.5,2.5 0 0,1 12,6.5A2.5,2.5 0 0,1 14.5,9A2.5,2.5 0 0,1 12,11.5Z"/>
                  </svg>
                  فتح في خرائط جوجل
                </span>
              </a>
            </div>
          </div>

          {/* نصائح الأمان */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
              🛡️ نصائح الأمان
            </h3>
            <ul className="space-y-2 text-sm text-gray-700">
              <li>• تأكد من المنتج قبل الدفع</li>
              <li>• التقي في مكان عام آمن</li>
              <li>• لا تدفع مقدماً قبل المعاينة</li>
              <li>• تأكد من هوية البائع</li>
              <li>• احذر من العروض المشبوهة</li>
            </ul>
          </div>

          {/* إبلاغ عن الإعلان */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">إبلاغ عن مشكلة</h3>
            <p className="text-sm text-gray-600 mb-4">
              إذا كان هناك مشكلة في هذا الإعلان، يرجى إبلاغنا
            </p>
            <button className="w-full border border-red-300 text-red-600 py-2 rounded-lg hover:bg-red-50 transition-colors text-sm">
              🚨 إبلاغ عن الإعلان
            </button>
          </div>
        </div>
      </div>

      {/* نافذة الرسائل */}
      <MessageModal
        isOpen={showMessageModal}
        onClose={() => setShowMessageModal(false)}
        recipientName={ad.seller.name}
        recipientPhone={ad.seller.phone}
        adTitle={ad.title}
        adId={ad.id}
      />
    </div>
  );
};

export default AdDetails;
