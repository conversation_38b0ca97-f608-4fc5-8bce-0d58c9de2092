'use client';

import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function TestNavigationPage() {
  const testPages = [
    { name: 'الصفحة الرئيسية', href: '/' },
    { name: 'إضافة إعلان', href: '/add-ad' },
    { name: 'لوحة التحكم', href: '/dashboard' },
    { name: 'الملف الشخصي', href: '/profile' },
    { name: 'الاشتراكات', href: '/my-subscription' },
    { name: 'اختبار الشعار', href: '/simple-logo-test' },
    { name: 'مقارنة الشعارات', href: '/logo-comparison' },
    { name: 'عرض الحسابات', href: '/demo-accounts' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-6 text-center">
              اختبار شاشة التحميل عند التنقل
            </h1>
            
            <div className="mb-8 text-center">
              <p className="text-gray-600 text-lg mb-4">
                انقر على أي رابط أدناه لرؤية شاشة التحميل مع الشعار
              </p>
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-blue-800 text-sm">
                  💡 <strong>ملاحظة:</strong> ستظهر شاشة التحميل مع الشعار في وسط الصفحة لبضع ثوانٍ عند التنقل بين الصفحات
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {testPages.map((page, index) => (
                <Link
                  key={index}
                  href={page.href}
                  className="block p-6 bg-gradient-to-r from-primary-500 to-green-500 text-white rounded-lg hover:from-primary-600 hover:to-green-600 transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  <div className="text-center">
                    <div className="text-2xl mb-2">🔗</div>
                    <h3 className="font-semibold text-lg">{page.name}</h3>
                    <p className="text-sm opacity-90 mt-1">{page.href}</p>
                  </div>
                </Link>
              ))}
            </div>

            <div className="mt-8 bg-gray-50 rounded-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4">مميزات شاشة التحميل:</h2>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✅</span>
                  <span>عرض الشعار بحجم كبير في وسط الصفحة</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✅</span>
                  <span>انيميشن ناعم ومتدرج</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✅</span>
                  <span>شريط تقدم متحرك</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✅</span>
                  <span>تأثيرات بصرية جذابة</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✅</span>
                  <span>يظهر تلقائياً عند التنقل بين الصفحات</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✅</span>
                  <span>خلفية متدرجة بألوان الموقع</span>
                </li>
              </ul>
            </div>

            <div className="mt-6 text-center">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p className="text-yellow-800 text-sm">
                  <strong>تلميح:</strong> جرب التنقل السريع بين الصفحات لرؤية التأثير بوضوح أكبر
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
