'use client';

import Image from 'next/image';

const SimpleLogoTest = () => {
  return (
    <div className="p-8 space-y-8">
      <h1 className="text-3xl font-bold text-center">اختبار الشعار الجديد</h1>

      {/* اختبار الشعار الأساسي */}
      <div className="bg-white p-8 rounded-lg shadow-lg">
        <h2 className="text-xl font-bold mb-4">الشعار على خلفية بيضاء</h2>
        <div className="flex items-center justify-center">
          <Image
            src="/images/Adsız tasarım (3)_page-0001-Photoroom.png"
            alt="من المالك"
            width={200}
            height={100}
            className="object-contain"
          />
        </div>
      </div>

      {/* اختبار على خلفية ملونة */}
      <div className="bg-gray-800 p-8 rounded-lg shadow-lg">
        <h2 className="text-xl font-bold mb-4 text-white">الشعار على خلفية داكنة</h2>
        <div className="flex items-center justify-center">
          <Image
            src="/images/Adsız tasarım (3)_page-0001-Photoroom.png"
            alt="من المالك"
            width={200}
            height={100}
            className="object-contain"
          />
        </div>
      </div>

      {/* اختبار أحجام مختلفة */}
      <div className="bg-white p-8 rounded-lg shadow-lg">
        <h2 className="text-xl font-bold mb-4">أحجام مختلفة</h2>
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <span className="w-20 text-sm">صغير:</span>
            <Image
              src="/images/Adsız tasarım (3)_page-0001-Photoroom (1).png"
              alt="من المالك"
              width={80}
              height={40}
              className="object-contain"
            />
          </div>
          <div className="flex items-center gap-4">
            <span className="w-20 text-sm">متوسط:</span>
            <Image
              src="/images/Adsız tasarım (3)_page-0001-Photoroom (1).png"
              alt="من المالك"
              width={120}
              height={60}
              className="object-contain"
            />
          </div>
          <div className="flex items-center gap-4">
            <span className="w-20 text-sm">كبير:</span>
            <Image
              src="/images/Adsız tasarım (3)_page-0001-Photoroom (1).png"
              alt="من المالك"
              width={160}
              height={80}
              className="object-contain"
            />
          </div>
        </div>
      </div>

      {/* اختبار مع النص */}
      <div className="bg-white p-8 rounded-lg shadow-lg">
        <h2 className="text-xl font-bold mb-4">الشعار مع النص</h2>
        <div className="flex items-center justify-center gap-4">
          <Image
            src="/images/logo . min almalek.jpg"
            alt="من المالك"
            width={120}
            height={60}
            className="object-contain"
          />
          <span className="text-2xl font-bold text-primary-600">من المالك</span>
        </div>
      </div>

      {/* اختبار في Header مقلد */}
      <div className="bg-white border-b shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Image
                src="/images/logo . min almalek.jpg"
                alt="من المالك"
                width={100}
                height={50}
                className="object-contain"
              />
              <span className="text-xl font-bold text-primary-600 hidden sm:block">من المالك</span>
            </div>
            <div className="text-gray-600">باقي عناصر Header</div>
          </div>
        </div>
      </div>

      {/* معلومات الملف */}
      <div className="bg-blue-50 p-6 rounded-lg">
        <h3 className="text-lg font-bold mb-2">معلومات الملف:</h3>
        <ul className="text-sm space-y-1">
          <li><strong>المسار:</strong> /images/Adsız tasarım (3)_page-0001-Photoroom.png</li>
          <li><strong>النوع:</strong> PNG</li>
          <li><strong>الخلفية:</strong> شفافة</li>
          <li><strong>الاستخدام:</strong> شعار رئيسي للموقع</li>
          <li><strong>الألوان:</strong> أصفر وأخضر (مصافحة)</li>
        </ul>
      </div>
    </div>
  );
};

export default SimpleLogoTest;
