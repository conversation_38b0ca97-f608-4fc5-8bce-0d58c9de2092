'use client';

import { useState, useRef, useEffect } from 'react';
import { useAdvancedNotifications } from './AdvancedNotificationSystem';

export default function NotificationBell() {
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState<string>('all');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { 
    notifications, 
    getUnreadCount, 
    markAsRead, 
    markAllAsRead, 
    removeNotification,
    getNotificationsByCategory 
  } = useAdvancedNotifications();

  const unreadCount = getUnreadCount();

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const filteredNotifications = filter === 'all' 
    ? notifications 
    : getNotificationsByCategory(filter);

  const getTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'الآن';
    if (minutes < 60) return `منذ ${minutes} دقيقة`;
    if (hours < 24) return `منذ ${hours} ساعة`;
    return `منذ ${days} يوم`;
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'warning': return '⚠️';
      case 'welcome': return '🎉';
      case 'payment': return '💳';
      case 'message': return '💬';
      case 'favorite': return '❤️';
      case 'ad_posted': return '📢';
      case 'search_alert': return '🔍';
      case 'logout': return '👋';
      default: return '🔔';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'border-l-red-500 bg-red-50/50';
      case 'high': return 'border-l-orange-500 bg-orange-50/50';
      case 'medium': return 'border-l-blue-500 bg-blue-50/50';
      case 'low': return 'border-l-gray-500 bg-gray-50/50';
      default: return 'border-l-gray-500 bg-gray-50/50';
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <style jsx>{`
        @keyframes bellGlow {
          0% {
            filter: grayscale(1) opacity(0.7) brightness(1) drop-shadow(0 0 4px rgba(251, 191, 36, 0.4));
          }
          100% {
            filter: grayscale(0) opacity(1) brightness(1.5) drop-shadow(0 0 8px rgba(251, 191, 36, 0.8)) hue-rotate(45deg) saturate(1.5);
          }
        }
      `}</style>
      {/* زر الجرس */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-3 text-gray-600 hover:text-gray-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-2 rounded-full hover:bg-gray-100"
        aria-label="الإشعارات"
        style={{
          filter: isOpen
            ? 'drop-shadow(0 0 8px rgba(251, 191, 36, 0.8)) brightness(1.3)'
            : 'none',
          animation: unreadCount > 0 ? 'bellGlow 2s infinite alternate' : 'none'
        }}
      >
        <span
          className="text-xl transition-all duration-300"
          style={{
            filter: isOpen
              ? 'brightness(1.5) drop-shadow(0 0 6px rgba(251, 191, 36, 0.8)) hue-rotate(45deg) saturate(1.5)'
              : 'grayscale(1) opacity(0.7)',
            textShadow: isOpen
              ? '0 0 6px rgba(251, 191, 36, 0.6)'
              : 'none'
          }}
        >
          🔔
        </span>
        
        {/* عداد الإشعارات غير المقروءة */}
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center animate-pulse">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {/* قائمة الإشعارات */}
      {isOpen && (
        <div className="absolute left-0 mt-2 w-80 md:w-96 bg-white/95 backdrop-blur-md rounded-xl shadow-2xl border border-gray-200/50 z-50 max-h-[500px] overflow-hidden">
          {/* الرأس */}
          <div className="p-4 border-b border-gray-200/50">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-lg font-semibold text-gray-800">الإشعارات</h3>
              <div className="flex items-center gap-2">
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                  >
                    تحديد الكل كمقروء
                  </button>
                )}
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* فلاتر */}
            <div className="flex gap-2 overflow-x-auto">
              {[
                { key: 'all', label: 'الكل', count: notifications.length },
                { key: 'system', label: 'النظام', count: getNotificationsByCategory('system').length },
                { key: 'social', label: 'اجتماعي', count: getNotificationsByCategory('social').length },
                { key: 'commerce', label: 'تجاري', count: getNotificationsByCategory('commerce').length },
                { key: 'user_action', label: 'إجراءات', count: getNotificationsByCategory('user_action').length }
              ].map(({ key, label, count }) => (
                <button
                  key={key}
                  onClick={() => setFilter(key)}
                  className={`
                    px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap transition-colors
                    ${filter === key 
                      ? 'bg-blue-100 text-blue-800 border border-blue-200' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }
                  `}
                >
                  {label} {count > 0 && `(${count})`}
                </button>
              ))}
            </div>
          </div>

          {/* قائمة الإشعارات */}
          <div className="max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            {filteredNotifications.length > 0 ? (
              filteredNotifications.slice(0, 15).map((notification) => (
                <div
                  key={notification.id}
                  className={`
                    p-3 border-l-4 hover:bg-gray-50/70 transition-all duration-200 cursor-pointer group
                    ${getPriorityColor(notification.priority)}
                    ${!notification.isRead ? 'bg-blue-50/40 shadow-sm' : 'bg-white/20'}
                    border-b border-gray-100/50 last:border-b-0
                  `}
                  onClick={() => {
                    if (!notification.isRead) {
                      markAsRead(notification.id);
                    }
                    if (notification.actionUrl) {
                      setIsOpen(false);
                      window.location.href = notification.actionUrl;
                    }
                  }}
                >
                  <div className="flex items-start gap-3">
                    {/* الأيقونة */}
                    <div className="flex-shrink-0 mt-0.5">
                      <div className="w-8 h-8 rounded-full bg-gray-100/50 flex items-center justify-center">
                        <span className="text-lg">
                          {notification.icon || getNotificationIcon(notification.type)}
                        </span>
                      </div>
                    </div>

                    {/* المحتوى */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-1">
                        <h4 className={`
                          text-sm leading-tight text-gray-800
                          ${!notification.isRead ? 'font-semibold' : 'font-medium'}
                        `}>
                          {notification.title}
                        </h4>
                        <div className="flex items-center gap-1 ml-2 flex-shrink-0">
                          {!notification.isRead && (
                            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              removeNotification(notification.id);
                            }}
                            className="text-gray-400 hover:text-red-500 opacity-0 group-hover:opacity-100 transition-all duration-200 p-1 rounded hover:bg-red-50"
                            title="حذف الإشعار"
                          >
                            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </div>
                      </div>

                      <p className="text-xs text-gray-600 mb-2 leading-relaxed line-clamp-2">
                        {notification.message}
                      </p>

                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500 font-medium">
                          {getTimeAgo(notification.timestamp)}
                        </span>

                        {notification.actionText && (
                          <span className="text-xs text-green-600 font-medium bg-green-50 px-2 py-1 rounded-full">
                            {notification.actionText} ←
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="p-8 text-center">
                <div className="text-4xl mb-4">🔔</div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">لا توجد إشعارات</h3>
                <p className="text-gray-600">
                  {filter === 'all' 
                    ? 'ستظهر الإشعارات الجديدة هنا' 
                    : `لا توجد إشعارات في فئة ${filter === 'system' ? 'النظام' : filter === 'social' ? 'الاجتماعي' : filter === 'commerce' ? 'التجاري' : 'الإجراءات'}`
                  }
                </p>
              </div>
            )}
          </div>

          {/* الذيل */}
          {filteredNotifications.length > 0 && (
            <div className="p-3 border-t border-gray-200/50 bg-gray-50/50">
              <button
                onClick={() => {
                  window.location.href = '/notifications';
                  setIsOpen(false);
                }}
                className="w-full text-center text-sm text-blue-600 hover:text-blue-800 font-medium"
              >
                عرض جميع الإشعارات
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
