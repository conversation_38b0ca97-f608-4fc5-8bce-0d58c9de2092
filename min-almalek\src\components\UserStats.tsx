'use client';

import { useState } from 'react';

interface UserStatsProps {
  userId: number;
  userName: string;
}

const UserStats = ({ userId, userName }: UserStatsProps) => {
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('month');

  const stats = {
    week: {
      totalAds: 3,
      activeAds: 2,
      views: 145,
      messages: 8,
      favorites: 12,
      clicks: 67
    },
    month: {
      totalAds: 12,
      activeAds: 8,
      views: 1250,
      messages: 45,
      favorites: 89,
      clicks: 456
    },
    year: {
      totalAds: 48,
      activeAds: 8,
      views: 8750,
      messages: 234,
      favorites: 567,
      clicks: 2890
    }
  };

  const currentStats = stats[timeRange];

  const chartData = {
    week: [
      { day: 'الأحد', views: 25, messages: 2 },
      { day: 'الاثنين', views: 18, messages: 1 },
      { day: 'الثلاثاء', views: 32, messages: 3 },
      { day: 'الأربعاء', views: 28, messages: 1 },
      { day: 'الخميس', views: 22, messages: 1 },
      { day: 'الجمعة', views: 12, messages: 0 },
      { day: 'السبت', views: 8, messages: 0 }
    ],
    month: [
      { day: 'الأسبوع 1', views: 280, messages: 12 },
      { day: 'الأسبوع 2', views: 320, messages: 15 },
      { day: 'الأسبوع 3', views: 290, messages: 8 },
      { day: 'الأسبوع 4', views: 360, messages: 10 }
    ],
    year: [
      { day: 'يناير', views: 650, messages: 18 },
      { day: 'فبراير', views: 720, messages: 22 },
      { day: 'مارس', views: 890, messages: 28 },
      { day: 'أبريل', views: 780, messages: 25 },
      { day: 'مايو', views: 920, messages: 32 },
      { day: 'يونيو', views: 1050, messages: 38 },
      { day: 'يوليو', views: 980, messages: 35 },
      { day: 'أغسطس', views: 1120, messages: 42 },
      { day: 'سبتمبر', views: 850, messages: 28 },
      { day: 'أكتوبر', views: 760, messages: 24 },
      { day: 'نوفمبر', views: 680, messages: 18 },
      { day: 'ديسمبر', views: 590, messages: 15 }
    ]
  };

  const categoryPerformance = [
    { category: 'العقارات', ads: 5, views: 680, percentage: 54 },
    { category: 'السيارات', ads: 3, views: 320, percentage: 26 },
    { category: 'الإلكترونيات', ads: 2, views: 180, percentage: 14 },
    { category: 'أخرى', ads: 2, views: 70, percentage: 6 }
  ];

  const maxViews = Math.max(...chartData[timeRange].map(d => d.views));

  const getGrowthPercentage = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return Math.round(((current - previous) / previous) * 100);
  };

  const timeRangeLabels = {
    week: 'هذا الأسبوع',
    month: 'هذا الشهر',
    year: 'هذا العام'
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-800">إحصائياتي</h2>
        <div className="flex gap-2">
          {(['week', 'month', 'year'] as const).map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                timeRange === range
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {timeRangeLabels[range]}
            </button>
          ))}
        </div>
      </div>

      {/* Main Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <span className="text-2xl">📝</span>
            </div>
            <span className="text-green-600 text-sm font-medium">
              +{getGrowthPercentage(currentStats.totalAds, 8)}%
            </span>
          </div>
          <div className="text-3xl font-bold text-gray-800 mb-2">
            {currentStats.totalAds}
          </div>
          <div className="text-gray-600">إجمالي الإعلانات</div>
          <div className="text-sm text-gray-500 mt-1">
            {currentStats.activeAds} نشط
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <svg className="w-6 h-6 text-green-600 opacity-70" fill="currentColor" viewBox="0 0 20 20" style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}>
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
              </svg>
            </div>
            <span className="text-green-600 text-sm font-medium">
              +{getGrowthPercentage(currentStats.views, 980)}%
            </span>
          </div>
          <div className="text-3xl font-bold text-gray-800 mb-2">
            {currentStats.views.toLocaleString()}
          </div>
          <div className="text-gray-600">المشاهدات</div>
          <div className="text-sm text-gray-500 mt-1">
            {Math.round(currentStats.views / currentStats.totalAds)} متوسط لكل إعلان
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <span className="text-2xl">💬</span>
            </div>
            <span className="text-green-600 text-sm font-medium">
              +{getGrowthPercentage(currentStats.messages, 32)}%
            </span>
          </div>
          <div className="text-3xl font-bold text-gray-800 mb-2">
            {currentStats.messages}
          </div>
          <div className="text-gray-600">الرسائل</div>
          <div className="text-sm text-gray-500 mt-1">
            {Math.round((currentStats.messages / currentStats.views) * 100)}% معدل التحويل
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <span className="text-2xl">❤️</span>
            </div>
            <span className="text-green-600 text-sm font-medium">
              +{getGrowthPercentage(currentStats.favorites, 67)}%
            </span>
          </div>
          <div className="text-3xl font-bold text-gray-800 mb-2">
            {currentStats.favorites}
          </div>
          <div className="text-gray-600">المفضلة</div>
          <div className="text-sm text-gray-500 mt-1">
            من {currentStats.views} مشاهدة
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Views Chart */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-6">
            المشاهدات والرسائل - {timeRangeLabels[timeRange]}
          </h3>
          <div className="space-y-4">
            {chartData[timeRange].map((item, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">{item.day}</span>
                  <div className="flex gap-4">
                    <span className="text-blue-600">{item.views} مشاهدة</span>
                    <span className="text-green-600">{item.messages} رسالة</span>
                  </div>
                </div>
                <div className="flex gap-2">
                  <div className="flex-1 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${(item.views / maxViews) * 100}%` }}
                    />
                  </div>
                  <div className="w-16 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${(item.messages / 10) * 100}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Category Performance */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-6">
            الأداء حسب التصنيف
          </h3>
          <div className="space-y-4">
            {categoryPerformance.map((category, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-gray-800">{category.category}</span>
                  <div className="text-sm text-gray-600">
                    {category.ads} إعلان • {category.views} مشاهدة
                  </div>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-primary-500 h-3 rounded-full transition-all duration-500"
                    style={{ width: `${category.percentage}%` }}
                  />
                </div>
                <div className="text-xs text-gray-500">
                  {category.percentage}% من إجمالي المشاهدات
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Insights */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-6">نصائح لتحسين الأداء</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-2">
              <span className="text-2xl">📸</span>
              <h4 className="font-semibold text-gray-800">جودة الصور</h4>
            </div>
            <p className="text-sm text-gray-600">
              أضف صوراً عالية الجودة لزيادة المشاهدات بنسبة 40%
            </p>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-2">
              <span className="text-2xl">⭐</span>
              <h4 className="font-semibold text-gray-800">الإعلانات المميزة</h4>
            </div>
            <p className="text-sm text-gray-600">
              ترقية إعلاناتك لتظهر في المقدمة وتحصل على مشاهدات أكثر
            </p>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center gap-3 mb-2">
              <span className="text-2xl">💰</span>
              <h4 className="font-semibold text-gray-800">السعر التنافسي</h4>
            </div>
            <p className="text-sm text-gray-600">
              راجع أسعار الإعلانات المشابهة لضمان سعر تنافسي
            </p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl p-6 text-white">
        <h3 className="text-lg font-semibold mb-4">إجراءات سريعة</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <a
            href="/jobs"
            className="bg-white/20 hover:bg-white/30 rounded-lg p-4 text-center transition-colors"
          >
            <div className="text-2xl mb-2">👔</div>
            <div className="font-medium">تصفح الوظائف</div>
          </a>
          <a
            href="/dashboard?tab=ads"
            className="bg-white/20 hover:bg-white/30 rounded-lg p-4 text-center transition-colors"
          >
            <div className="text-2xl mb-2">📝</div>
            <div className="font-medium">إدارة إعلاناتي</div>
          </a>
          <a
            href="/pricing"
            className="bg-white/20 hover:bg-white/30 rounded-lg p-4 text-center transition-colors"
          >
            <div className="text-2xl mb-2">⭐</div>
            <div className="font-medium">ترقية الاشتراك</div>
          </a>
        </div>
      </div>
    </div>
  );
};

export default UserStats;
