'use client';

import { useState } from 'react';

interface PetsData {
  category: string;
  subCategory: string;
  animalType: string;
  breed: string;
  age: string;
  gender: string;
  size: string;
  color: string;
  vaccinated: boolean;
  trained: boolean;
  healthStatus: string;
  description: string;
}

interface PetsSpecificFieldsProps {
  data: PetsData;
  onChange: (data: PetsData) => void;
}

const petsCategories = {
  'animals': {
    name: 'الحيوانات',
    subCategories: {
      'dogs': {
        name: 'كلاب',
        breeds: ['جيرمان', 'هاسكي', 'بيتبول', 'روتوايلر', 'شيواوا', 'لابرادور', 'جولدن ريتريفر', 'بولدوغ', 'مالينوا', 'دوبرمان']
      },
      'cats': {
        name: 'قطط',
        breeds: ['شامي', 'شيرازي', 'أنغورا', 'سيامي', 'ماين كون', 'بريتيش شورت هير', 'راغدول', 'بنغال']
      },
      'birds': {
        name: 'طيور زينة',
        breeds: ['كنار', 'ببغاء', 'كوكتيل', 'غريب', 'حمام', 'فيشر', 'زيبرا', 'جاوا']
      },
      'fish': {
        name: 'أسماك زينة',
        breeds: ['مياه عذبة', 'مياه مالحة', 'أحواض صغيرة', 'أحواض كبيرة', 'سمك ذهبي', 'أنجل فيش']
      },
      'rabbits': {
        name: 'أرانب',
        breeds: ['أرانب زينة', 'أرانب عادية', 'أرانب أنغورا']
      },
      'turtles': {
        name: 'سلاحف',
        breeds: ['سلاحف مائية', 'سلاحف برية', 'سلاحف صغيرة']
      },
      'hamsters': {
        name: 'هامستر وخنازير غينيا',
        breeds: ['هامستر ذهبي', 'هامستر قزم', 'خنزير غينيا']
      },
      'reptiles': {
        name: 'زواحف',
        breeds: ['سحالي', 'ثعابين', 'سلاحف نادرة', 'إغوانا']
      }
    }
  },
  'supplies': {
    name: 'مستلزمات الحيوانات',
    subCategories: {
      'food': {
        name: 'طعام الحيوانات',
        items: ['طعام كلاب جاف', 'طعام كلاب معلب', 'طعام قطط جاف', 'طعام قطط معلب', 'طعام طيور', 'طعام أسماك', 'مكملات غذائية', 'فيتامينات']
      },
      'toys': {
        name: 'ألعاب وإكسسوارات',
        items: ['ألعاب كلاب', 'ألعاب قطط', 'أسرة وصناديق نوم', 'أطواق وسلاسل', 'صناديق نقل', 'أواني أكل وشرب']
      },
      'aquarium': {
        name: 'أحواض أسماك وإكسسوارات',
        items: ['أحواض أسماك', 'فلاتر مياه', 'إنارة أحواض', 'ديكور أحواض', 'مضخات هواء', 'سخانات مياه']
      },
      'cages': {
        name: 'أقفاص وبيوت',
        items: ['أقفاص طيور', 'بيوت كلاب', 'بيوت قطط', 'أقفاص أرانب', 'بيوت خشبية', 'بيوت بلاستيكية']
      },
      'grooming': {
        name: 'أدوات العناية',
        items: ['فرشاة شعر', 'مقص تهذيب', 'شامبو حيوانات', 'مناشف', 'مقلمة أظافر', 'فرشاة أسنان']
      }
    }
  },
  'services': {
    name: 'خدمات الحيوانات',
    subCategories: {
      'veterinary': {
        name: 'خدمات بيطرية',
        items: ['عيادات بيطرية', 'أطباء بيطريون متنقلون', 'صيدليات بيطرية', 'عمليات جراحية', 'تطعيمات', 'فحوصات دورية']
      },
      'grooming-services': {
        name: 'حمامات وتجميل',
        items: ['حمام وتنظيف', 'قص شعر', 'تنظيف أسنان', 'تقليم أظافر', 'تجميل شامل']
      },
      'training': {
        name: 'تدريب الحيوانات',
        items: ['تدريب كلاب طاعة', 'تدريب حراسة', 'تدريب استعراض', 'تدريب سلوكي', 'تدريب منزلي']
      },
      'breeding': {
        name: 'تربية وتهجين',
        items: ['خدمات تهجين', 'تزاوج', 'مزارع تربية', 'مراكز تبني', 'استشارات تربية']
      },
      'photography': {
        name: 'تصوير الحيوانات',
        items: ['تصوير فوتوغرافي', 'جلسات تصوير', 'تصوير احترافي']
      }
    }
  },
  'accessories': {
    name: 'إكسسوارات وأدوات',
    subCategories: {
      'clothing': {
        name: 'ملابس الحيوانات',
        items: ['ملابس كلاب', 'ملابس قطط', 'ملابس شتوية', 'ملابس صيفية', 'إكسسوارات']
      },
      'smart-devices': {
        name: 'أجهزة ذكية',
        items: ['أجهزة تتبع GPS', 'كاميرات مراقبة', 'مغذيات أوتوماتيكية', 'ألعاب ذكية']
      },
      'cleaning': {
        name: 'أدوات التنظيف',
        items: ['صناديق فضلات', 'رمل قطط', 'أكياس تنظيف', 'مناديل تنظيف', 'مطهرات']
      }
    }
  },
  'additional-services': {
    name: 'خدمات إضافية',
    subCategories: {
      'hotels': {
        name: 'فنادق ومبيت الحيوانات',
        items: ['فنادق حيوانات', 'مبيت مؤقت', 'رعاية أثناء السفر']
      },
      'transport': {
        name: 'خدمات النقل',
        items: ['نقل داخلي', 'نقل خارجي', 'نقل دولي', 'خدمات الشحن']
      },
      'rescue': {
        name: 'جمعيات الإنقاذ والرعاية',
        items: ['جمعيات إنقاذ', 'مراكز رعاية', 'تبني مجاني', 'إعادة تأهيل']
      },
      'training-courses': {
        name: 'دورات تدريبية',
        items: ['دورات تدريب المالكين', 'دورات رعاية', 'دورات إسعافات أولية']
      },
      'markets': {
        name: 'أسواق وتبادل',
        items: ['أسواق حيوانات', 'تبادل حيوانات', 'معارض', 'مزادات']
      }
    }
  }
};

const PetsSpecificFields = ({ data, onChange }: PetsSpecificFieldsProps) => {
  const [selectedCategory, setSelectedCategory] = useState(data?.category || '');
  const [selectedSubCategory, setSelectedSubCategory] = useState(data?.subCategory || '');

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setSelectedSubCategory('');
    onChange({
      ...data,
      category,
      subCategory: '',
      animalType: '',
      breed: ''
    });
  };

  const handleSubCategoryChange = (subCategory: string) => {
    setSelectedSubCategory(subCategory);
    onChange({
      ...data,
      subCategory,
      animalType: '',
      breed: ''
    });
  };

  const handleChange = (field: keyof PetsData, value: string | boolean) => {
    onChange({
      category: '',
      subCategory: '',
      animalType: '',
      breed: '',
      age: '',
      gender: '',
      size: '',
      color: '',
      vaccinated: false,
      trained: false,
      healthStatus: '',
      description: '',
      ...data,
      [field]: value
    });
  };

  const currentCategory = selectedCategory ? petsCategories[selectedCategory as keyof typeof petsCategories] : null;
  const currentSubCategory = selectedSubCategory && currentCategory ? 
    currentCategory.subCategories[selectedSubCategory as keyof typeof currentCategory.subCategories] : null;

  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-amber-50 to-orange-50 p-6 rounded-xl border border-amber-200">
        <h3 className="text-lg font-semibold text-amber-800 mb-4">
          تفاصيل الحيوانات الأليفة
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* الفئة الرئيسية */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الرئيسية</label>
            <select
              value={selectedCategory}
              onChange={(e) => handleCategoryChange(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
            >
              <option value="">اختر الفئة</option>
              {Object.entries(petsCategories).map(([key, category]) => (
                <option key={key} value={key}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* الفئة الفرعية */}
          {currentCategory && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الفرعية</label>
              <select
                value={selectedSubCategory}
                onChange={(e) => handleSubCategoryChange(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              >
                <option value="">اختر الفئة الفرعية</option>
                {Object.entries(currentCategory.subCategories).map(([key, subCategory]) => (
                  <option key={key} value={key}>
                    {subCategory.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* نوع الحيوان/المنتج */}
          {currentSubCategory && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {selectedCategory === 'animals' ? 'السلالة' : 'النوع'}
              </label>
              <select
                value={data?.breed || data?.animalType || ''}
                onChange={(e) => {
                  if (selectedCategory === 'animals') {
                    handleChange('breed', e.target.value);
                  } else {
                    handleChange('animalType', e.target.value);
                  }
                }}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              >
                <option value="">
                  {selectedCategory === 'animals' ? 'اختر السلالة' : 'اختر النوع'}
                </option>
                {(currentSubCategory as any).breeds?.map((breed: string, index: number) => (
                  <option key={index} value={breed}>
                    {breed}
                  </option>
                )) || (currentSubCategory as any).items?.map((item: string, index: number) => (
                  <option key={index} value={item}>
                    {item}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* العمر - للحيوانات فقط */}
          {selectedCategory === 'animals' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">العمر</label>
              <select
                value={data?.age || ''}
                onChange={(e) => handleChange('age', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              >
                <option value="">اختر العمر</option>
                <option value="أقل من شهر">أقل من شهر</option>
                <option value="1-3 أشهر">1-3 أشهر</option>
                <option value="4-6 أشهر">4-6 أشهر</option>
                <option value="7-12 شهر">7-12 شهر</option>
                <option value="1-2 سنة">1-2 سنة</option>
                <option value="3-5 سنوات">3-5 سنوات</option>
                <option value="أكثر من 5 سنوات">أكثر من 5 سنوات</option>
              </select>
            </div>
          )}

          {/* الجنس - للحيوانات فقط */}
          {selectedCategory === 'animals' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الجنس</label>
              <select
                value={data?.gender || ''}
                onChange={(e) => handleChange('gender', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              >
                <option value="">اختر الجنس</option>
                <option value="ذكر">ذكر</option>
                <option value="أنثى">أنثى</option>
              </select>
            </div>
          )}

          {/* الحجم - للحيوانات فقط */}
          {selectedCategory === 'animals' && (selectedSubCategory === 'dogs' || selectedSubCategory === 'cats') && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الحجم</label>
              <select
                value={data?.size || ''}
                onChange={(e) => handleChange('size', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              >
                <option value="">اختر الحجم</option>
                <option value="صغير">صغير</option>
                <option value="متوسط">متوسط</option>
                <option value="كبير">كبير</option>
                <option value="كبير جداً">كبير جداً</option>
              </select>
            </div>
          )}

          {/* اللون - للحيوانات فقط */}
          {selectedCategory === 'animals' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">اللون</label>
              <input
                type="text"
                value={data?.color || ''}
                onChange={(e) => handleChange('color', e.target.value)}
                placeholder="مثال: أسود، أبيض، بني..."
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              />
            </div>
          )}

          {/* الحالة الصحية - للحيوانات فقط */}
          {selectedCategory === 'animals' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الحالة الصحية</label>
              <select
                value={data?.healthStatus || ''}
                onChange={(e) => handleChange('healthStatus', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
              >
                <option value="">اختر الحالة الصحية</option>
                <option value="ممتازة">ممتازة</option>
                <option value="جيدة">جيدة</option>
                <option value="تحتاج رعاية">تحتاج رعاية</option>
                <option value="تحت العلاج">تحت العلاج</option>
              </select>
            </div>
          )}

          {/* مطعم - للحيوانات فقط */}
          {selectedCategory === 'animals' && (
            <div>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={data?.vaccinated || false}
                  onChange={(e) => handleChange('vaccinated', e.target.checked)}
                  className="w-4 h-4 text-amber-600 border-gray-300 rounded focus:ring-amber-500"
                />
                <span className="text-sm font-medium text-gray-700">مطعم</span>
              </label>
            </div>
          )}

          {/* مدرب - للحيوانات فقط */}
          {selectedCategory === 'animals' && (selectedSubCategory === 'dogs' || selectedSubCategory === 'cats') && (
            <div>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={data?.trained || false}
                  onChange={(e) => handleChange('trained', e.target.checked)}
                  className="w-4 h-4 text-amber-600 border-gray-300 rounded focus:ring-amber-500"
                />
                <span className="text-sm font-medium text-gray-700">مدرب</span>
              </label>
            </div>
          )}

          {/* وصف إضافي */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">وصف إضافي</label>
            <textarea
              value={data?.description || ''}
              onChange={(e) => handleChange('description', e.target.value)}
              placeholder="اكتب تفاصيل إضافية..."
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-amber-500"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PetsSpecificFields;
