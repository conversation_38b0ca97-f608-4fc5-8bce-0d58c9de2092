'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ClientOnlyWrapper from '@/components/ClientOnlyWrapper';
import { useToast } from '@/components/ToastManager';

interface Message {
  id: string;
  from: string;
  subject: string;
  content: string;
  date: Date;
  isRead: boolean;
  isArchived: boolean;
  adTitle?: string;
  adId?: string;
}

export default function MessagesPage() {
  const { user, isAuthenticated } = useAuth();
  const toast = useToast();
  const [activeTab, setActiveTab] = useState('inbox');
  const [selectedMessages, setSelectedMessages] = useState<string[]>([]);
  const [showReplyModal, setShowReplyModal] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);

  // رسائل تجريبية
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      from: 'أحمد محمد',
      subject: 'استفسار عن الشقة في المزة',
      content: 'مرحباً، أريد الاستفسار عن الشقة المعروضة في منطقة المزة. هل ما زالت متاحة؟',
      date: new Date('2024-01-20'),
      isRead: false,
      isArchived: false,
      adTitle: 'شقة للإيجار في المزة',
      adId: 'ad-123'
    },
    {
      id: '2',
      from: 'فاطمة علي',
      subject: 'سؤال عن السيارة',
      content: 'السلام عليكم، أريد معرفة المزيد عن السيارة المعروضة. ما هو السعر النهائي؟',
      date: new Date('2024-01-19'),
      isRead: true,
      isArchived: false,
      adTitle: 'تويوتا كامري 2020',
      adId: 'ad-456'
    },
    {
      id: '3',
      from: 'محمد خالد',
      subject: 'عرض شراء',
      content: 'أريد تقديم عرض شراء للعقار المعروض. هل يمكننا التفاوض على السعر؟',
      date: new Date('2024-01-18'),
      isRead: true,
      isArchived: true,
      adTitle: 'فيلا للبيع في دمشق',
      adId: 'ad-789'
    }
  ]);

  if (!isAuthenticated || !user) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <div className="max-w-md mx-auto bg-white rounded-xl shadow-lg p-8 text-center">
            <div className="text-6xl mb-4">🔒</div>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">تسجيل الدخول مطلوب</h2>
            <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى الرسائل</p>
            <button
              onClick={() => window.location.href = '/'}
              className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              العودة للرئيسية
            </button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  const filteredMessages = messages.filter(message => {
    if (activeTab === 'inbox') return !message.isArchived;
    if (activeTab === 'archived') return message.isArchived;
    return true;
  });

  const handleSelectMessage = (messageId: string) => {
    setSelectedMessages(prev => 
      prev.includes(messageId) 
        ? prev.filter(id => id !== messageId)
        : [...prev, messageId]
    );
  };

  const handleSelectAll = () => {
    if (selectedMessages.length === filteredMessages.length) {
      setSelectedMessages([]);
    } else {
      setSelectedMessages(filteredMessages.map(m => m.id));
    }
  };

  const handleMarkAsRead = () => {
    setMessages(prev => prev.map(message => 
      selectedMessages.includes(message.id) 
        ? { ...message, isRead: true }
        : message
    ));
    setSelectedMessages([]);
  };

  const handleArchive = (messageId?: number) => {
    if (messageId) {
      // Archive single message
      setMessages(prev => prev.map(message =>
        message.id === messageId
          ? { ...message, isArchived: true }
          : message
      ));
      toast.showSuccess('تم أرشفة الرسالة', 'تم نقل الرسالة إلى الأرشيف');
    } else {
      // Archive selected messages
      setMessages(prev => prev.map(message =>
        selectedMessages.includes(message.id)
          ? { ...message, isArchived: true }
          : message
      ));
      setSelectedMessages([]);
      toast.showSuccess(`تم أرشفة ${selectedMessages.length} رسالة`, 'تم نقل الرسائل المحددة إلى الأرشيف');
    }
  };

  const handleDelete = () => {
    if (confirm('هل أنت متأكد من حذف الرسائل المحددة؟')) {
      setMessages(prev => prev.filter(message => !selectedMessages.includes(message.id)));
      setSelectedMessages([]);
    }
  };

  const handleReply = (message: Message) => {
    setSelectedMessage(message);
    setShowReplyModal(true);
    toast.showSuccess('فتح نافذة الرد', 'يمكنك الآن كتابة ردك على الرسالة');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <ClientOnlyWrapper
          fallback={
            <div className="max-w-6xl mx-auto">
              <div className="animate-pulse space-y-6">
                <div className="h-32 bg-gray-200 rounded-xl"></div>
                <div className="h-96 bg-gray-200 rounded-xl"></div>
              </div>
            </div>
          }
        >
          <div className="max-w-6xl mx-auto">
            {/* رأس الصفحة */}
            <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-800 mb-2">الرسائل</h1>
                  <p className="text-gray-600">إدارة رسائلك والردود على الاستفسارات</p>
                </div>
                <div className="text-6xl opacity-50" style={{filter: 'grayscale(1)'}}>📧</div>
              </div>
            </div>

            {/* التبويبات */}
            <div className="bg-white rounded-xl shadow-lg mb-6">
              <div className="flex">
                <button
                  onClick={() => setActiveTab('inbox')}
                  className={`flex-1 px-6 py-4 text-center transition-colors ${
                    activeTab === 'inbox'
                      ? 'bg-primary-600 text-white rounded-r-xl'
                      : 'text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  صندوق الوارد ({messages.filter(m => !m.isArchived).length})
                </button>
                <button
                  onClick={() => setActiveTab('archived')}
                  className={`flex-1 px-6 py-4 text-center transition-colors ${
                    activeTab === 'archived'
                      ? 'bg-primary-600 text-white rounded-l-xl'
                      : 'text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  الأرشيف ({messages.filter(m => m.isArchived).length})
                </button>
              </div>
            </div>

            {/* أدوات التحكم */}
            {filteredMessages.length > 0 && (
              <div className="bg-white rounded-xl shadow-lg p-4 mb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={selectedMessages.length === filteredMessages.length}
                        onChange={handleSelectAll}
                        className="rounded"
                      />
                      <span className="text-sm text-gray-600">تحديد الكل</span>
                    </label>
                    <span className="text-sm text-gray-500">
                      {selectedMessages.length} من {filteredMessages.length} محدد
                    </span>
                  </div>

                  {selectedMessages.length > 0 && (
                    <div className="flex items-center gap-2">
                      <button
                        onClick={handleMarkAsRead}
                        className="px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        تم القراءة
                      </button>
                      <button
                        onClick={handleArchive}
                        className="px-3 py-1 text-sm bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
                      >
                        أرشيف
                      </button>
                      <button
                        onClick={handleDelete}
                        className="px-3 py-1 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                      >
                        حذف
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* قائمة الرسائل */}
            <div className="bg-white rounded-xl shadow-lg">
              {filteredMessages.length === 0 ? (
                <div className="p-8 text-center">
                  <div className="text-6xl mb-4">📭</div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">
                    {activeTab === 'inbox' ? 'لا توجد رسائل جديدة' : 'لا توجد رسائل مؤرشفة'}
                  </h3>
                  <p className="text-gray-600">
                    {activeTab === 'inbox' 
                      ? 'ستظهر هنا الرسائل الواردة من المهتمين بإعلاناتك'
                      : 'ستظهر هنا الرسائل المؤرشفة'
                    }
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {filteredMessages.map((message) => (
                    <div
                      key={message.id}
                      className={`p-4 hover:bg-gray-50 transition-colors ${
                        !message.isRead ? 'bg-blue-50 border-r-4 border-blue-500' : ''
                      }`}
                    >
                      <div className="flex items-center gap-4">
                        <input
                          type="checkbox"
                          checked={selectedMessages.includes(message.id)}
                          onChange={() => handleSelectMessage(message.id)}
                          className="rounded"
                        />
                        
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center gap-2">
                              <h3 className={`font-semibold ${!message.isRead ? 'text-gray-900' : 'text-gray-700'}`}>
                                {message.from}
                              </h3>
                              {!message.isRead && (
                                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                              )}
                            </div>
                            <span className="text-sm text-gray-500">
                              {message.date.toLocaleDateString('en-GB')}
                            </span>
                          </div>
                          
                          <h4 className="font-medium text-gray-800 mb-1">{message.subject}</h4>
                          <p className="text-gray-600 text-sm mb-2 line-clamp-2">{message.content}</p>
                          
                          {message.adTitle && (
                            <div className="text-xs text-blue-600 mb-2">
                              بخصوص: {message.adTitle}
                            </div>
                          )}
                          
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => handleReply(message)}
                              className="flex items-center gap-1 text-sm bg-blue-100 text-blue-700 px-3 py-1 rounded-lg hover:bg-blue-200 transition-colors"
                            >
                              <span className="opacity-50" style={{filter: 'grayscale(1)'}}>↩️</span>
                              رد
                            </button>
                            <button
                              onClick={() => handleArchive(message.id)}
                              className="flex items-center gap-1 text-sm bg-yellow-100 text-yellow-700 px-3 py-1 rounded-lg hover:bg-yellow-200 transition-colors"
                            >
                              <span className="opacity-50" style={{filter: 'grayscale(1)'}}>📁</span>
                              أرشيف
                            </button>
                            <button
                              onClick={() => handleDelete()}
                              className="text-sm text-red-600 hover:text-red-700 transition-colors"
                            >
                              حذف
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </ClientOnlyWrapper>
      </main>

      {/* Reply Modal */}
      {showReplyModal && selectedMessage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-2xl w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-800">رد على الرسالة</h3>
              <button
                onClick={() => setShowReplyModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>

            <div className="mb-4 p-4 bg-gray-50 rounded-lg">
              <div className="text-sm text-gray-600 mb-2">رسالة أصلية من: {selectedMessage.from}</div>
              <div className="text-sm text-gray-800">{selectedMessage.content}</div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الرد</label>
                <textarea
                  rows={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="اكتب ردك هنا..."
                />
              </div>
            </div>

            <div className="flex gap-3 mt-6">
              <button className="flex-1 bg-primary-600 text-white py-2 rounded-lg hover:bg-primary-700 transition-colors">
                إرسال الرد
              </button>
              <button
                onClick={() => setShowReplyModal(false)}
                className="flex-1 border border-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-50 transition-colors"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}

      <Footer />
    </div>
  );
}
