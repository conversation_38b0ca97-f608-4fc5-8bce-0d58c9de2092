'use client';

import Link from 'next/link';
import { useState } from 'react';
import SafeNavigationButton from './SafeNavigationButton';
import MyCvLogo from './MyCvLogo';
import CategoryIcon from './CategoryIcon';



const categories = [
  {
    id: 'real-estate',
    name: 'العقارات',
    icon: 'real-estate',
    description: 'شقق، فيلات، أراضي',
    count: '12,450',
    subcategories: ['للبيع', 'للإيجار', 'تجاري', 'أراضي'],
    color: 'bg-blue-100/60 border border-blue-200/50',
    iconColor: '#3b82f6'
  },
  {
    id: 'cars',
    name: 'السيارات',
    icon: 'cars',
    description: 'سيارات جديدة ومستعملة',
    count: '8,230',
    subcategories: ['جديدة', 'مستعملة', 'دراجات', 'قطع غيار'],
    color: 'bg-red-100/60 border border-red-200/50',
    iconColor: '#ef4444'
  },
  {
    id: 'electronics',
    name: 'الإلكترونيات',
    icon: 'electronics',
    description: 'هواتف، حاسوب، أجهزة',
    count: '15,670',
    subcategories: ['هواتف', 'حاسوب', 'تلفزيون', 'ألعاب'],
    color: 'bg-purple-100/60 border border-purple-200/50',
    iconColor: '#8b5cf6'
  },
  {
    id: 'services',
    name: 'الخدمات',
    icon: 'services',
    description: 'خدمات متنوعة',
    count: '5,420',
    subcategories: ['صيانة', 'تنظيف', 'توصيل', 'تعليم'],
    color: 'bg-orange-100/60 border border-orange-200/50',
    iconColor: '#f97316'
  },
  {
    id: 'fashion',
    name: 'الأزياء والموضة',
    icon: 'fashion',
    description: 'ملابس، أحذية، إكسسوارات',
    count: '7,650',
    subcategories: ['رجالي', 'نسائي', 'أطفال', 'أحذية'],
    color: 'bg-pink-100/60 border border-pink-200/50',
    iconColor: '#ec4899'
  },
  {
    id: 'furniture',
    name: 'الأثاث والمنزل',
    icon: 'furniture',
    description: 'أثاث، ديكور، أدوات منزلية',
    count: '4,320',
    subcategories: ['غرف نوم', 'صالونات', 'مطبخ', 'ديكور'],
    color: 'bg-yellow-100/60 border border-yellow-200/50',
    iconColor: '#eab308'
  },
  {
    id: 'sports',
    name: 'الرياضة والترفيه',
    icon: 'sports',
    description: 'معدات رياضية، ألعاب',
    count: '2,180',
    subcategories: ['كرة قدم', 'لياقة', 'ألعاب', 'رحلات'],
    color: 'bg-indigo-100/60 border border-indigo-200/50',
    iconColor: '#6366f1'
  },
  {
    id: 'books',
    name: 'الكتب والتعليم',
    icon: 'books',
    description: 'كتب، دورات، تعليم',
    count: '1,890',
    subcategories: ['كتب', 'دورات', 'جامعة', 'مدرسة'],
    color: 'bg-teal-100/60 border border-teal-200/50',
    iconColor: '#14b8a6'
  },
  {
    id: 'pets',
    name: 'الحيوانات الأليفة',
    icon: 'pets',
    description: 'حيوانات، مستلزمات',
    count: '980',
    subcategories: ['قطط', 'كلاب', 'طيور', 'مستلزمات'],
    color: 'bg-amber-100/60 border border-amber-200/50',
    iconColor: '#f59e0b'
  },
  {
    id: 'health',
    name: 'الصحة والجمال',
    icon: 'health',
    description: 'منتجات صحية وتجميل',
    count: '1,560',
    subcategories: ['أدوية', 'تجميل', 'عطور', 'صحة'],
    color: 'bg-emerald-100/60 border border-emerald-200/50',
    iconColor: '#10b981'
  },
  {
    id: 'food',
    name: 'الطعام والمشروبات',
    icon: 'food',
    description: 'مطاعم، طعام، مشروبات',
    count: '2,340',
    subcategories: ['مطاعم', 'حلويات', 'مشروبات', 'طبخ'],
    color: 'bg-rose-100/60 border border-rose-200/50',
    iconColor: '#f43f5e'
  },
  {
    id: 'tourism',
    name: 'السياحة والسفر',
    icon: 'tourism',
    description: 'فنادق، رحلات، خدمات سياحية',
    count: '1,890',
    subcategories: ['فنادق', 'رحلات', 'نقل', 'مرشدين'],
    color: 'bg-sky-100/60 border border-sky-200/50',
    iconColor: '#0ea5e9'
  },
  {
    id: 'jobs',
    name: 'الوظائف',
    icon: 'jobs',
    description: 'فرص عمل متنوعة',
    count: '3,890',
    subcategories: ['دوام كامل', 'دوام جزئي', 'عمل حر', 'تدريب'],
    color: 'bg-green-100/60 border border-green-200/50',
    iconColor: '#10b981'
  },
  {
    id: 'offers-discounts',
    name: 'العروض والتخفيضات',
    icon: 'offers-discounts',
    description: 'عروض المطاعم والتخفيضات الخاصة',
    count: '2,150',
    subcategories: ['مطاعم', 'تسوق', 'خدمات', 'ترفيه', 'سفر', 'تعليم'],
    color: 'bg-gradient-to-r from-orange-100/60 to-red-100/60 border border-orange-200/50',
    iconColor: '#ea580c',
    isSpecial: true
  },
  {
    id: 'business-plans',
    name: 'خطط الشركات',
    icon: 'business-plans',
    description: 'باقات النشر والترقية للشركات',
    count: '3 باقات',
    subcategories: ['أساسية', 'مميزة', 'أعمال'],
    color: 'bg-gradient-to-r from-yellow-100/60 to-amber-100/60 border border-yellow-200/50',
    iconColor: '#f59e0b',
    isSpecial: true,
    isCompact: true
  }
];

const CategoryGrid = () => {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [pressedCategory, setPressedCategory] = useState<string | null>(null);

  return (
    <section className="py-12 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">تصفح حسب التصنيف</h2>
          <p className="text-gray-600 text-lg">اختر التصنيف المناسب لك وابدأ البحث أو أضف إعلانك الخاص</p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
          {categories.map((category) => (
            <div
              key={category.id}
              className={`group bg-white rounded-lg md:rounded-xl shadow-md hover:shadow-xl transition-all duration-300 border overflow-hidden relative ${
                category.isSpecial
                  ? 'border-yellow-300 hover:border-yellow-400 ring-2 ring-yellow-100'
                  : 'border-gray-100 hover:border-primary-200'
              }`}
              onMouseEnter={() => setHoveredCategory(category.id)}
              onMouseLeave={() => setHoveredCategory(null)}
              onMouseDown={() => setPressedCategory(category.id)}
              onMouseUp={() => setPressedCategory(null)}
              onTouchStart={() => setPressedCategory(category.id)}
              onTouchEnd={() => setPressedCategory(null)}
            >
              <div className="p-4 md:p-6">
                <div className="flex items-center justify-between mb-3 md:mb-4">
                  <div
                    className={`category-container w-12 h-12 md:w-14 md:h-14 ${category.color} rounded-lg flex items-center justify-center group-hover:scale-110 transition-all duration-300 cursor-pointer backdrop-blur-sm`}
                    style={{
                      // تحسينات إضافية للموبايل
                      minWidth: '48px',
                      minHeight: '48px',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                    }}
                  >
                    {category.id === 'jobs' ? (
                      <MyCvLogo size="sm" variant="square" style={{ color: category.iconColor }} />
                    ) : (
                      <CategoryIcon
                        category={category.icon}
                        className="category-icon w-7 h-7 md:w-8 md:h-8"
                        color={category.iconColor}
                        isHovered={hoveredCategory === category.id}
                        isPressed={pressedCategory === category.id}
                      />
                    )}
                  </div>
                  <span className="text-xs md:text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                    {category.count}
                  </span>
                </div>

                <h3 className="text-base md:text-lg font-semibold text-gray-800 mb-2 group-hover:text-primary-600 transition-colors">
                  {category.name}
                </h3>

                <p className="text-gray-600 text-xs md:text-sm mb-3 md:mb-4">
                  {category.description}
                </p>

                {/* Special content for Jobs category */}
                {category.id === 'jobs' && (
                  <div className="mb-4 p-3 bg-gradient-to-r from-yellow-50 to-amber-50 rounded-lg border border-yellow-200 shadow-sm">
                    <div className="flex items-center gap-2 mb-2">
                      <MyCvLogo size="sm" variant="square" />
                      <span className="text-xs text-amber-800 font-medium">
                        مدعوم من قبل تطبيق MyCv
                      </span>
                    </div>
                    <p className="text-xs text-amber-700">
                      منصة متكاملة للسير الذاتية والتوظيف
                    </p>
                  </div>
                )}

                {/* Special content for Business Plans category */}
                {category.id === 'business-plans' && (
                  <div className="mb-3 space-y-2">
                    {/* Compact Pricing Preview */}
                    <div className="p-2 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-semibold text-green-800">💰 من</span>
                        <div className="flex items-center gap-1">
                          <span className="text-sm font-bold text-green-700">25,000 ل.س</span>
                          <span className="text-xs text-green-600">/شهر</span>
                        </div>
                      </div>
                    </div>

                    {/* Compact Features Preview */}
                    <div className="p-2 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg border border-purple-200">
                      <div className="flex items-center justify-between">
                        <span className="text-xs font-semibold text-purple-800">⭐ مميز</span>
                        <span className="text-xs text-purple-700">إعلانات + إحصائيات</span>
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex flex-wrap gap-1">
                  {category.subcategories.slice(0, 3).map((sub, index) => (
                    <span
                      key={index}
                      className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                    >
                      {sub}
                    </span>
                  ))}
                  {category.subcategories.length > 3 && (
                    <span className="text-xs text-gray-400 px-2 py-1">
                      +{category.subcategories.length - 3}
                    </span>
                  )}
                </div>
              </div>

              {/* أيقونة إضافة إعلان */}
              {hoveredCategory === category.id && category.id !== 'business-plans' && (
                <div className="absolute top-4 left-4">
                  <SafeNavigationButton
                    href={
                      category.id === 'jobs' ? '/jobs' :
                      `/add-ad?category=${category.id}`
                    }
                    className="bg-green-500 hover:bg-green-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-lg transition-all duration-200 shadow-lg hover:shadow-xl"
                    title={category.id === 'jobs' ? 'تصفح الوظائف' : 'إضافة إعلان'}
                  >
                    +
                  </SafeNavigationButton>
                </div>
              )}

              {/* أزرار التصفح والإضافة */}
              <div className={`px-6 py-3 transition-colors ${
                category.isSpecial
                  ? 'bg-gradient-to-r from-yellow-50 to-amber-50 group-hover:from-yellow-100 group-hover:to-amber-100'
                  : 'bg-gray-50 group-hover:bg-primary-50'
              }`}>
                {category.id === 'business-plans' ? (
                  <Link
                    href="/pricing"
                    className={`block text-sm font-medium ${
                      category.isSpecial
                        ? 'text-amber-700 group-hover:text-amber-800'
                        : 'text-gray-600 group-hover:text-primary-600'
                    }`}
                  >
                    اشترك الآن ←
                  </Link>
                ) : (
                  <div className="flex items-center justify-between">
                    <Link
                      href={
                        category.id === 'jobs' ? '/jobs' :
                        `/category/${category.id}`
                      }
                      className={`text-sm font-medium ${
                        category.isSpecial
                          ? 'text-amber-700 group-hover:text-amber-800'
                          : 'text-gray-600 group-hover:text-primary-600'
                      }`}
                    >
                      تصفح الإعلانات ←
                    </Link>
                    <SafeNavigationButton
                      href={
                        category.id === 'jobs' ? '/jobs' :
                        `/add-ad?category=${category.id}`
                      }
                      className="bg-primary-600 hover:bg-primary-700 text-white text-xs px-3 py-1 rounded-full transition-colors"
                    >
                      + إضافة
                    </SafeNavigationButton>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-10">
          <Link
            href="/categories"
            className="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium"
          >
            عرض جميع التصنيفات
            <span className="mr-2">→</span>
          </Link>
        </div>
      </div>

      {/* CSS إضافي لتحسين عرض الأيقونات على الموبايل */}
      <style jsx>{`
        @media (max-width: 768px) {
          :global(.category-icon) {
            filter: brightness(1.2) contrast(1.1) !important;
            opacity: 0.9 !important;
          }
          :global(.category-container) {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
          }
        }
      `}</style>
    </section>
  );
};

export default CategoryGrid;
