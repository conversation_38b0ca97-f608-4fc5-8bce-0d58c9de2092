'use client';

import React from 'react';

interface NewVerificationBadgeProps {
  userType: 'individual' | 'business' | 'real-estate-office';
  planId?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg';
  showTooltip?: boolean;
  className?: string;
}

const NewVerificationBadge: React.FC<NewVerificationBadgeProps> = ({
  userType,
  planId,
  size = 'md',
  showTooltip = true,
  className = ''
}) => {
  const getBadgeInfo = () => {
    switch (userType) {
      case 'individual':
        if (planId === 'individual-free') {
          return {
            image: null, // الباقة المجانية بدون شارة
            label: 'عضو',
            color: 'bg-gray-100 border-gray-300 text-gray-600'
          };
        } else if (planId === 'individual-basic') {
          return {
            image: '/images/شارات باقات الأفراد/شارة الباقة الأساسية للأفراد.png',
            label: 'عضو موثق',
            color: 'bg-blue-100 border-blue-500 text-blue-700'
          };
        } else if (planId === 'individual-premium') {
          return {
            image: '/images/شارات باقات الأفراد/شارة الباقة المميزة للأفراد.png',
            label: 'عضو مميز',
            color: 'bg-yellow-100 border-yellow-500 text-yellow-700'
          };
        } else if (planId === 'individual-vip') {
          return {
            image: '/images/شارات باقات الأفراد/شارة باقة  الأعمال للأفراد.png',
            label: 'عضو أعمال',
            color: 'bg-purple-100 border-purple-500 text-purple-700'
          };
        }
        return {
          image: null,
          label: 'عضو',
          color: 'bg-gray-100 border-gray-300 text-gray-600'
        };

      case 'business':
        if (planId === 'business-starter') {
          return {
            image: '/images/شارات باقات  الشركات والمكاتب العقارية/شارة باقة البداية - التجريبية (المجانية) -LOGO-شركة موثقة.png',
            label: 'شركة موثقة',
            color: 'bg-green-100 border-green-500 text-green-700'
          };
        } else if (planId === 'business-professional') {
          return {
            image: '/images/شارات باقات  الشركات والمكاتب العقارية/شارة الباقة المهنية (الفضية) -LOGO-شركة موثقة.png',
            label: 'شركة موثقة',
            color: 'bg-gray-100 border-gray-500 text-gray-700'
          };
        } else if (planId === 'business-gold') {
          return {
            image: '/images/شارات باقات  الشركات والمكاتب العقارية/شارة الباقة الذهبية -LOGO-شركة موثقة.png',
            label: 'شركة موثقة',
            color: 'bg-yellow-100 border-yellow-500 text-yellow-700'
          };
        }
        return {
          image: '/images/شارات باقات  الشركات والمكاتب العقارية/شارة الباقة المهنية (الفضية) -LOGO-شركة موثقة.png',
          label: 'شركة موثقة',
          color: 'bg-blue-100 border-blue-500 text-blue-700'
        };

      case 'real-estate-office':
        return {
          image: '/images/شارات باقات  الشركات والمكاتب العقارية/LOGO-باقة المكاتب العقارية-مكتب عقاري موثق.png',
          label: 'مكتب عقاري موثق',
          color: 'bg-purple-100 border-purple-500 text-purple-700'
        };

      default:
        return {
          image: '/images/شارة باقة  الأعمال للأفراد.png',
          label: 'عضو موثق',
          color: 'bg-green-100 border-green-500 text-green-700'
        };
    }
  };

  const badgeInfo = getBadgeInfo();
  
  if (!badgeInfo.image && userType === 'individual') {
    return null; // لا نعرض شارة للأفراد
  }

  const sizeClasses = {
    xs: 'h-4 w-6',
    sm: 'h-5 w-8',
    md: 'h-6 w-10',
    lg: 'h-8 w-12'
  };

  const containerSizeClasses = {
    xs: 'px-1.5 py-0.5 text-xs',
    sm: 'px-2 py-1 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base'
  };

  return (
    <div className="relative inline-block">
      <div
        className={`
          inline-flex items-center gap-1.5 rounded-full border-2 font-medium
          ${badgeInfo.color} ${containerSizeClasses[size]} ${className}
        `}
        title={showTooltip ? badgeInfo.label : undefined}
      >
        {badgeInfo.image && (
          <img
            src={badgeInfo.image}
            alt={badgeInfo.label}
            className={`${sizeClasses[size]} object-contain`}
          />
        )}
        <span className="whitespace-nowrap">
          {size === 'xs' ? '✓' : badgeInfo.label}
        </span>
      </div>
    </div>
  );
};

export default NewVerificationBadge;
