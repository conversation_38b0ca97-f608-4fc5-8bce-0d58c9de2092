'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import VerificationBadge from './VerificationBadge';
import { determineUserBadge } from '@/lib/verification';

interface AccountDropdownProps {
  className?: string;
}

const AccountDropdown = ({ className = '' }: AccountDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { user, isAuthenticated, logout } = useAuth();
  const router = useRouter();

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleLogout = () => {
    logout();
    setIsOpen(false);

    // إعادة توجيه فورية للصفحة الرئيسية
    setTimeout(() => {
      router.push('/');
    }, 100);
  };

  if (!isAuthenticated || !user) {
    // عرض أيقونة تسجيل الدخول للمستخدمين غير المسجلين
    return (
      <button
        onClick={() => router.push('/')}
        className="relative p-3 text-gray-600 hover:text-primary-600 transition-all duration-300 group rounded-full focus:outline-none focus:ring-0"
        title="تسجيل الدخول"
      >
        <svg
          className="w-6 h-6 transition-all duration-300 group-hover:text-primary-600 group-hover:scale-110"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
        </svg>
      </button>
    );
  }

  const userBadge = determineUserBadge(user.userType, user.subscription?.planId);

  const getMenuIcon = (iconType: string) => {
    const iconClass = "w-5 h-5 transition-all duration-300";
    const iconStyle = {
      filter: 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))',
      transition: 'all 0.3s ease'
    };

    const handleIconHover = (e: React.MouseEvent, isEnter: boolean) => {
      if (isEnter) {
        e.currentTarget.style.filter = 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))';
        e.currentTarget.style.color = '#22c55e';
      } else {
        e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))';
        e.currentTarget.style.color = '';
      }
    };

    switch (iconType) {
      case 'dashboard':
        return (
          <svg
            className={iconClass}
            fill="currentColor"
            viewBox="0 0 20 20"
            style={iconStyle}
            onMouseEnter={(e) => handleIconHover(e, true)}
            onMouseLeave={(e) => handleIconHover(e, false)}
          >
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
          </svg>
        );
      case 'ads':
        return (
          <svg
            className={iconClass}
            fill="currentColor"
            viewBox="0 0 20 20"
            style={iconStyle}
            onMouseEnter={(e) => handleIconHover(e, true)}
            onMouseLeave={(e) => handleIconHover(e, false)}
          >
            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clipRule="evenodd" />
          </svg>
        );
      case 'subscription':
        return (
          <svg
            className={iconClass}
            fill="currentColor"
            viewBox="0 0 20 20"
            style={iconStyle}
            onMouseEnter={(e) => handleIconHover(e, true)}
            onMouseLeave={(e) => handleIconHover(e, false)}
          >
            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'profile':
        return (
          <svg
            className={iconClass}
            fill="currentColor"
            viewBox="0 0 20 20"
            style={iconStyle}
            onMouseEnter={(e) => handleIconHover(e, true)}
            onMouseLeave={(e) => handleIconHover(e, false)}
          >
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
          </svg>
        );
      case 'messages':
        return (
          <svg
            className={iconClass}
            fill="currentColor"
            viewBox="0 0 20 20"
            style={iconStyle}
            onMouseEnter={(e) => handleIconHover(e, true)}
            onMouseLeave={(e) => handleIconHover(e, false)}
          >
            <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
            <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
          </svg>
        );
      case 'settings':
        return (
          <svg
            className={iconClass}
            fill="currentColor"
            viewBox="0 0 20 20"
            style={iconStyle}
            onMouseEnter={(e) => handleIconHover(e, true)}
            onMouseLeave={(e) => handleIconHover(e, false)}
          >
            <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
          </svg>
        );
      default:
        return <span className="text-lg">📋</span>;
    }
  };

  const menuItems = [
    {
      icon: 'dashboard',
      label: 'لوحة التحكم',
      href: '/dashboard',
      description: 'نظرة عامة على حسابك'
    },
    {
      icon: 'messages',
      label: 'الرسائل',
      href: '/messages',
      description: 'إدارة رسائلك والردود'
    },
    {
      icon: 'subscription',
      label: 'الاشتراكات',
      href: '/my-subscription',
      description: 'إدارة باقاتك والدفع'
    },
    {
      icon: 'profile',
      label: 'الملف الشخصي',
      href: '/profile',
      description: 'تحديث معلوماتك الشخصية'
    },
    {
      icon: 'settings',
      label: 'الإعدادات',
      href: '/settings',
      description: 'إعدادات الحساب والخصوصية'
    },
  ];

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* زر الحساب */}
      <button
        onClick={handleToggle}
        className={`relative p-3 text-gray-600 hover:text-primary-600 transition-all duration-300 group rounded-full focus:outline-none focus:ring-0 ${
          isOpen ? 'shadow-lg shadow-green-500/30 bg-green-50' : ''
        }`}
        title="حسابي"
      >
        <svg
          className="w-6 h-6 transition-all duration-300 group-hover:text-primary-600 group-hover:scale-110"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
        </svg>

        {/* نقطة الحالة */}
        <span className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></span>
      </button>

      {/* قائمة الحساب */}
      {isOpen && (
        <div className="absolute left-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden">
          {/* رأس القائمة - معلومات المستخدم */}
          <div className="p-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <span className="text-xl">
                  {user.userType === 'individual' && '👤'}
                  {user.userType === 'business' && '🏢'}
                  {user.userType === 'real-estate-office' && '🏘️'}
                </span>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold text-white">{user.name}</h3>
                  <VerificationBadge type={userBadge.type} size="xs" />
                </div>
                <p className="text-sm opacity-90">{user.email}</p>
                <p className="text-xs opacity-75">
                  {user.userType === 'individual' && 'مستخدم فردي'}
                  {user.userType === 'business' && 'حساب شركة'}
                  {user.userType === 'real-estate-office' && 'مكتب عقاري'}
                  {' • '}
                  عضو منذ {new Date(user.createdAt).toLocaleDateString('en-GB', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                  }).split('/').reverse().join('/')}
                </p>
              </div>
            </div>
          </div>

          {/* إحصائيات سريعة */}
          <div className="p-4 bg-gray-50 border-b border-gray-200">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-primary-600">{user.stats.activeAds}</div>
                <div className="text-xs text-gray-600">إعلانات نشطة</div>
              </div>
              <div>
                <div className="flex items-center justify-center gap-1">
                  <svg
                    className="w-4 h-4 text-green-600 transition-all duration-300"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    style={{
                      filter: 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.filter = 'drop-shadow(0 0 6px rgba(34, 197, 94, 0.8))';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(34, 197, 94, 0.6))';
                    }}
                  >
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                    <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd"/>
                  </svg>
                  <div className="text-lg font-bold text-green-600">{user.stats.totalViews}</div>
                </div>
                <div className="text-xs text-gray-600">مشاهدات</div>
              </div>
              <div>
                <div className="flex items-center justify-center gap-1">
                  <svg
                    className="w-4 h-4 text-orange-600 transition-all duration-300"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    style={{
                      filter: 'drop-shadow(0 0 0px rgba(249, 115, 22, 0.6))',
                      transition: 'all 0.3s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.filter = 'drop-shadow(0 0 6px rgba(249, 115, 22, 0.8))';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.filter = 'drop-shadow(0 0 0px rgba(249, 115, 22, 0.6))';
                    }}
                  >
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                  </svg>
                  <div className="text-lg font-bold text-orange-600">{user.stats.totalContacts}</div>
                </div>
                <div className="text-xs text-gray-600">استفسارات</div>
              </div>
            </div>
          </div>

          {/* عناصر القائمة */}
          <div className="py-2">
            {menuItems.map((item, index) => (
              <Link
                key={index}
                href={item.href}
                className="flex items-center gap-3 px-4 py-3 hover:bg-gray-50 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <span className="text-primary-600">{getMenuIcon(item.icon)}</span>
                <div className="flex-1">
                  <div className="font-medium text-gray-800">{item.label}</div>
                  <div className="text-xs text-gray-500">{item.description}</div>
                </div>
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </Link>
            ))}
          </div>

          {/* معلومات الاشتراك */}
          {user.subscription && (
            <div className="p-4 bg-primary-50 border-t border-gray-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-primary-800">{user.subscription.planName}</span>
                  <VerificationBadge type={userBadge.type} size="xs" />
                </div>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  user.subscription.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {user.subscription.isActive ? 'نشط' : 'غير نشط'}
                </span>
              </div>
              <div className="text-xs text-primary-600">
                ينتهي في {new Date(user.subscription.endDate).toLocaleDateString('en-GB', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit'
                }).split('/').reverse().join('/')}
              </div>
            </div>
          )}

          {/* أزرار التحكم */}
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <div className="flex gap-2">
              <button
                onClick={() => {
                  router.push('/add-ad');
                  setIsOpen(false);
                }}
                className="flex-1 px-3 py-2 text-sm bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
              >
                إضافة إعلان
              </button>
              <button
                onClick={handleLogout}
                className="flex-1 px-3 py-2 text-sm bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccountDropdown;
