'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import Logo from './Logo';
import VerificationBadge from './VerificationBadge';
import { determineUserBadge } from '@/lib/verification';
import ClientOnlyWrapper from './ClientOnlyWrapper';

const UserDashboard = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedAdId, setSelectedAdId] = useState<number | null>(null);

  // وظائف الإجراءات
  const handleEditAd = (adId: number) => {
    // توجيه لصفحة تعديل الإعلان
    window.location.href = `/edit-ad/${adId}`;
  };

  const handleRenewAd = (adId: number) => {
    // إظهار نافذة تأكيد التجديد
    if (confirm('هل تريد تجديد هذا الإعلان؟ سيتم خصم الرسوم المطلوبة.')) {
      // تنفيذ عملية التجديد
      alert('تم تجديد الإعلان بنجاح!');
      // يمكن إضافة API call هنا
    }
  };

  const handleDeleteAd = (adId: number) => {
    setSelectedAdId(adId);
    setShowDeleteModal(true);
  };

  const confirmDelete = () => {
    if (selectedAdId) {
      // تنفيذ عملية الحذف
      alert('تم حذف الإعلان بنجاح!');
      // يمكن إضافة API call هنا
      setShowDeleteModal(false);
      setSelectedAdId(null);
    }
  };

  if (!isAuthenticated || !user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto bg-white rounded-xl shadow-lg p-8 text-center">
          <div className="text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">تسجيل الدخول مطلوب</h2>
          <p className="text-gray-600 mb-6">يجب تسجيل الدخول للوصول إلى لوحة التحكم</p>
          <button
            onClick={() => window.location.href = '/'}
            className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            العودة للرئيسية
          </button>
        </div>
      </div>
    );
  }

  const userBadge = determineUserBadge(user.userType, user.subscription?.planId);
  const userStats = user.stats;

  const userAds = [
    {
      id: 1,
      title: 'شقة للبيع في دمشق - المالكي',
      price: '85,000,000 ل.س',
      status: 'نشط',
      views: 245,
      messages: 12,
      postedDate: '2024-01-15',
      expiryDate: '2024-02-15',
      featured: true
    },
    {
      id: 2,
      title: 'سيارة BMW للبيع',
      price: '45,000 $',
      status: 'نشط',
      views: 189,
      messages: 8,
      postedDate: '2024-01-10',
      expiryDate: '2024-02-10',
      featured: false
    },
    {
      id: 3,
      title: 'لابتوب Dell Gaming',
      price: '850 $',
      status: 'منتهي',
      views: 87,
      messages: 3,
      postedDate: '2023-12-20',
      expiryDate: '2024-01-20',
      featured: false
    }
  ];

  const messages = [
    {
      id: 1,
      adTitle: 'شقة للبيع في دمشق - المالكي',
      senderName: 'محمد أحمد',
      message: 'مرحباً، هل الشقة ما زالت متاحة؟',
      date: '2024-01-20',
      read: false
    },
    {
      id: 2,
      adTitle: 'سيارة BMW للبيع',
      senderName: 'سارة علي',
      message: 'هل يمكن معاينة السيارة غداً؟',
      date: '2024-01-19',
      read: true
    }
  ];

  const tabs = [
    { id: 'overview', name: 'نظرة عامة', icon: '📊' },
    { id: 'ads', name: 'إعلاناتي', icon: '📝' },
    { id: 'messages', name: 'الرسائل', icon: '💬', href: '/messages' },
    ...(user.userType === 'business' || user.userType === 'real-estate-office' ? [
      { id: 'analytics', name: 'إحصائيات متقدمة', icon: '📈' },
      { id: 'reports', name: 'التقارير الأسبوعية', icon: '📋' },
      { id: 'features', name: 'ميزات إضافية', icon: '⭐' }
    ] : [])
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col lg:flex-row gap-8">
        {/* الشريط الجانبي */}
        <div className="lg:w-1/4">
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            <div className="text-center mb-6">
              <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-3xl">
                  {user.userType === 'individual' && '👤'}
                  {user.userType === 'business' && '🏢'}
                  {user.userType === 'real-estate-office' && '🏘️'}
                </span>
              </div>
              <h2 className="text-xl font-bold text-gray-800">{user.name}</h2>
              <p className="text-gray-600">
                {user.userType === 'individual' && 'مستخدم فردي'}
                {user.userType === 'business' && 'حساب شركة'}
                {user.userType === 'real-estate-office' && 'مكتب عقاري'}
              </p>
              <p className="text-sm text-gray-500">
                عضو منذ {new Date(user.createdAt).toLocaleDateString('en-GB', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit'
                }).split('/').reverse().join('/')}
              </p>
              <div className="mt-2">
                <VerificationBadge type={userBadge.type} size="sm" />
              </div>
            </div>

            <nav className="space-y-2">
              {tabs.map((tab) => {
                if (tab.href) {
                  return (
                    <Link
                      key={tab.id}
                      href={tab.href}
                      className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-right transition-colors ${
                        activeTab === tab.id
                          ? 'bg-primary-600 text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <span
                        className={`transition-all duration-200 ${
                          activeTab === tab.id ? 'opacity-100' : 'opacity-50'
                        }`}
                        style={activeTab === tab.id ? {
                          filter: 'drop-shadow(0 0 8px rgba(255, 255, 255, 0.8)) grayscale(0)',
                          transform: 'scale(1.1)'
                        } : {
                          filter: 'grayscale(1)'
                        }}
                      >
                        {tab.icon}
                      </span>
                      <span>{tab.name}</span>
                      {tab.id === 'messages' && messages.filter(m => !m.read).length > 0 && (
                        <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                          {messages.filter(m => !m.read).length}
                        </span>
                      )}
                    </Link>
                  );
                }
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-right transition-colors ${
                      activeTab === tab.id
                        ? 'bg-primary-600 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <span
                      className={`transition-all duration-200 ${
                        activeTab === tab.id ? 'opacity-100' : 'opacity-50'
                      }`}
                      style={activeTab === tab.id ? {
                        filter: 'drop-shadow(0 0 8px rgba(255, 255, 255, 0.8)) grayscale(0)',
                        transform: 'scale(1.1)'
                      } : {
                        filter: 'grayscale(1)'
                      }}
                    >
                      {tab.icon}
                    </span>
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="font-semibold text-gray-800 mb-4">الاشتراك الحالي</h3>
            <div className="text-center">
              {user.subscription ? (
                <>
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <div className="text-lg font-bold text-primary-600">{user.subscription.planName}</div>
                    <VerificationBadge type={userBadge.type} size="xs" />
                  </div>
                  <p className="text-sm text-gray-600 mb-2">
                    ينتهي في {new Date(user.subscription.endDate).toLocaleDateString('en-GB', {
                      year: 'numeric',
                      month: '2-digit',
                      day: '2-digit'
                    }).split('/').reverse().join('/')}
                  </p>
                  <p className="text-xs text-gray-500 mb-4">
                    {user.subscription.isActive ? '✅ نشط' : '❌ غير نشط'}
                  </p>
                  <Link
                    href="/subscription"
                    className="block bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors text-sm"
                  >
                    إدارة الاشتراك
                  </Link>
                </>
              ) : (
                <>
                  <div className="text-lg font-bold text-gray-600 mb-2">الخطة المجانية</div>
                  <p className="text-sm text-gray-600 mb-4">5 إعلانات مجانية شهرياً</p>
                  <Link
                    href="/subscription"
                    className="block bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors text-sm"
                  >
                    ترقية الاشتراك
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>

        {/* المحتوى الرئيسي */}
        <div className="lg:w-3/4">
          {/* نظرة عامة */}
          {activeTab === 'overview' && (
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-8">لوحة التحكم</h1>

              {/* الإحصائيات */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="bg-white rounded-xl shadow-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-gray-800">{userStats.totalAds}</div>
                      <div className="text-gray-600">إجمالي الإعلانات</div>
                    </div>
                    <div className="text-3xl opacity-70 hover:opacity-100 transition-opacity">
                      <svg className="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 24 24" style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}>
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-gray-800">{userStats.totalViews}</div>
                      <div className="text-gray-600">إجمالي المشاهدات</div>
                    </div>
                    <div className="text-3xl opacity-70 hover:opacity-100 transition-opacity">
                      <svg className="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 24 24" style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}>
                        <path d="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"/>
                      </svg>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-xl shadow-lg p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-2xl font-bold text-gray-800">{userStats.totalMessages}</div>
                      <div className="text-gray-600">الرسائل الواردة</div>
                    </div>
                    <div className="text-3xl opacity-70 hover:opacity-100 transition-opacity">
                      <svg className="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 24 24" style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}>
                        <path d="M20,2H4A2,2 0 0,0 2,4V22L6,18H20A2,2 0 0,0 22,16V4A2,2 0 0,0 20,2M6,9H18V11H6V9M14,14H6V12H14V14M18,8H6V6H18V8Z"/>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* الإعلانات الحديثة */}
              <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-gray-800">آخر الإعلانات</h2>
                  <Link href="#" onClick={() => setActiveTab('ads')} className="text-primary-600 hover:text-primary-700">
                    عرض الكل
                  </Link>
                </div>
                <div className="space-y-4">
                  {userAds.slice(0, 3).map((ad) => (
                    <div key={ad.id} className="flex items-center justify-between p-4 border border-gray-100 rounded-lg">
                      <div>
                        <h3 className="font-semibold text-gray-800">{ad.title}</h3>
                        <div className="text-sm text-gray-600">{ad.price} • {ad.views} مشاهدة</div>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-xs ${
                        ad.status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {ad.status}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* الرسائل الحديثة */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-gray-800">آخر الرسائل</h2>
                  <Link href="/messages" className="text-primary-600 hover:text-primary-700">
                    عرض الكل
                  </Link>
                </div>
                <div className="space-y-4">
                  {messages.slice(0, 3).map((message) => (
                    <div key={message.id} className={`p-4 border border-gray-100 rounded-lg ${!message.read ? 'bg-blue-50' : ''}`}>
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-semibold text-gray-800">{message.senderName}</h4>
                          <p className="text-sm text-gray-600 mb-1">{message.adTitle}</p>
                          <p className="text-gray-700">{message.message}</p>
                        </div>
                        <span className="text-xs text-gray-500">{message.date}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* إعلاناتي */}
          {activeTab === 'ads' && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <h1 className="text-3xl font-bold text-gray-800">إعلاناتي</h1>
                <Link
                  href="/add-ad"
                  className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  إضافة إعلان جديد
                </Link>
              </div>

              <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإعلان</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">السعر</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المشاهدات</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الرسائل</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {userAds.map((ad) => (
                        <tr key={ad.id}>
                          <td className="px-6 py-4">
                            <div>
                              <div className="font-semibold text-gray-800">{ad.title}</div>
                              <div className="text-sm text-gray-600">
                                نُشر في {ad.postedDate} • ينتهي في {ad.expiryDate}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 text-gray-800">{ad.price}</td>
                          <td className="px-6 py-4">
                            <span className={`px-3 py-1 rounded-full text-xs ${
                              ad.status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                            }`}>
                              {ad.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-gray-800">{ad.views}</td>
                          <td className="px-6 py-4 text-gray-800">{ad.messages}</td>
                          <td className="px-6 py-4">
                            <div className="flex gap-2">
                              <button
                                onClick={() => handleEditAd(ad.id)}
                                className="text-blue-600 hover:text-blue-700 text-sm font-medium px-3 py-1 rounded hover:bg-blue-50 transition-colors"
                              >
                                ✏️ تعديل
                              </button>
                              <button
                                onClick={() => handleRenewAd(ad.id)}
                                className="text-green-600 hover:text-green-700 text-sm font-medium px-3 py-1 rounded hover:bg-green-50 transition-colors"
                              >
                                🔄 تجديد
                              </button>
                              <button
                                onClick={() => handleDeleteAd(ad.id)}
                                className="text-red-600 hover:text-red-700 text-sm font-medium px-3 py-1 rounded hover:bg-red-50 transition-colors"
                              >
                                🗑️ حذف
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* الرسائل */}
          {activeTab === 'messages' && (
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-6">الرسائل</h1>

              <div className="bg-white rounded-xl shadow-lg">
                <div className="p-6 border-b border-gray-200">
                  <div className="flex items-center justify-between">
                    <h2 className="text-xl font-semibold text-gray-800">صندوق الوارد</h2>
                    <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm">
                      {messages.filter(m => !m.read).length} رسالة جديدة
                    </span>
                  </div>
                </div>

                <div className="divide-y divide-gray-200">
                  {messages.map((message) => (
                    <div key={message.id} className={`p-6 hover:bg-gray-50 cursor-pointer ${!message.read ? 'bg-blue-50' : ''}`}>
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-gray-800">{message.senderName}</h3>
                            {!message.read && <span className="w-2 h-2 bg-blue-500 rounded-full"></span>}
                          </div>
                          <p className="text-sm text-gray-600 mb-2">بخصوص: {message.adTitle}</p>
                          <p className="text-gray-700">{message.message}</p>
                        </div>
                        <span className="text-sm text-gray-500">{message.date}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* إحصائيات متقدمة للشركات */}
          {activeTab === 'analytics' && (user.userType === 'business' || user.userType === 'real-estate-office') && (
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
                  <span className="text-3xl opacity-50" style={{filter: 'grayscale(1)'}}>📈</span>
                  إحصائيات متقدمة
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                  <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-xl">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-blue-100">إجمالي المشاهدات</p>
                        <p className="text-3xl font-bold">12,543</p>
                      </div>
                      <span className="text-4xl opacity-80">👁️</span>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-6 rounded-xl">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-green-100">معدل التحويل</p>
                        <p className="text-3xl font-bold">8.5%</p>
                      </div>
                      <span className="text-4xl opacity-80">📊</span>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6 rounded-xl">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-purple-100">متوسط وقت البقاء</p>
                        <p className="text-3xl font-bold">3:42</p>
                      </div>
                      <span className="text-4xl opacity-80">⏱️</span>
                    </div>
                  </div>

                  <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white p-6 rounded-xl">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-orange-100">العملاء المحتملون</p>
                        <p className="text-3xl font-bold">156</p>
                      </div>
                      <span className="text-4xl opacity-80">🎯</span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="bg-gray-50 p-6 rounded-xl">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">أداء الإعلانات حسب الفئة</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">عقارات</span>
                        <div className="flex items-center gap-2">
                          <div className="w-32 bg-gray-200 rounded-full h-2">
                            <div className="bg-blue-500 h-2 rounded-full" style={{width: '75%'}}></div>
                          </div>
                          <span className="text-sm font-medium">75%</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">سيارات</span>
                        <div className="flex items-center gap-2">
                          <div className="w-32 bg-gray-200 rounded-full h-2">
                            <div className="bg-green-500 h-2 rounded-full" style={{width: '60%'}}></div>
                          </div>
                          <span className="text-sm font-medium">60%</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">إلكترونيات</span>
                        <div className="flex items-center gap-2">
                          <div className="w-32 bg-gray-200 rounded-full h-2">
                            <div className="bg-purple-500 h-2 rounded-full" style={{width: '45%'}}></div>
                          </div>
                          <span className="text-sm font-medium">45%</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 p-6 rounded-xl">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">المناطق الأكثر نشاطاً</h3>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">دمشق</span>
                        <span className="font-semibold text-blue-600">2,543 مشاهدة</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">حلب</span>
                        <span className="font-semibold text-green-600">1,876 مشاهدة</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-gray-600">حمص</span>
                        <span className="font-semibold text-purple-600">1,234 مشاهدة</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* التقارير الأسبوعية */}
          {activeTab === 'reports' && (user.userType === 'business' || user.userType === 'real-estate-office') && (
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
                  <span className="text-3xl opacity-50" style={{filter: 'grayscale(1)'}}>📋</span>
                  التقارير الأسبوعية
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <span className="opacity-50" style={{filter: 'grayscale(1)'}}>📊</span>
                      تقرير الأسبوع الحالي
                    </h3>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">إعلانات جديدة:</span>
                        <span className="font-semibold">12</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">إجمالي المشاهدات:</span>
                        <span className="font-semibold">3,456</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">رسائل جديدة:</span>
                        <span className="font-semibold">89</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">معدل النقر:</span>
                        <span className="font-semibold text-green-600">7.2%</span>
                      </div>
                    </div>
                    <button className="w-full mt-4 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors">
                      تحميل التقرير المفصل
                    </button>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                      <span className="opacity-50" style={{filter: 'grayscale(1)'}}>📈</span>
                      مقارنة مع الأسبوع السابق
                    </h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">المشاهدات:</span>
                        <span className="font-semibold text-green-600 flex items-center gap-1">
                          +15% <span className="text-sm">↗️</span>
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">الرسائل:</span>
                        <span className="font-semibold text-green-600 flex items-center gap-1">
                          +8% <span className="text-sm">↗️</span>
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">معدل التحويل:</span>
                        <span className="font-semibold text-red-600 flex items-center gap-1">
                          -2% <span className="text-sm">↘️</span>
                        </span>
                      </div>
                    </div>
                    <button className="w-full mt-4 bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition-colors">
                      عرض التحليل المقارن
                    </button>
                  </div>
                </div>

                <div className="mt-6 bg-gray-50 p-6 rounded-xl">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">التقارير السابقة</h3>
                  <div className="space-y-2">
                    {['الأسبوع الماضي', 'منذ أسبوعين', 'منذ ثلاثة أسابيع'].map((week, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg">
                        <span className="text-gray-700">{week}</span>
                        <button className="text-blue-600 hover:text-blue-800 transition-colors">
                          تحميل
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* ميزات إضافية */}
          {activeTab === 'features' && (user.userType === 'business' || user.userType === 'real-estate-office') && (
            <div className="space-y-6">
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
                  <span className="text-3xl opacity-50" style={{filter: 'grayscale(1)'}}>⭐</span>
                  ميزات إضافية للإعلانات
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                    <div className="text-center">
                      <span className="text-4xl mb-4 block opacity-50" style={{filter: 'grayscale(1)'}}>🎯</span>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">إعلانات مميزة</h3>
                      <p className="text-gray-600 text-sm mb-4">اجعل إعلاناتك تظهر في المقدمة</p>
                      <button className="w-full bg-yellow-500 text-white py-2 rounded-lg hover:bg-yellow-600 transition-colors">
                        ترقية الإعلان
                      </button>
                    </div>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                    <div className="text-center">
                      <span className="text-4xl mb-4 block opacity-50" style={{filter: 'grayscale(1)'}}>📊</span>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">تحليلات مفصلة</h3>
                      <p className="text-gray-600 text-sm mb-4">احصل على تقارير مفصلة لكل إعلان</p>
                      <button className="w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        عرض التحليلات
                      </button>
                    </div>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                    <div className="text-center">
                      <span className="text-4xl mb-4 block opacity-50" style={{filter: 'grayscale(1)'}}>🚀</span>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">دفع سريع</h3>
                      <p className="text-gray-600 text-sm mb-4">انشر إعلاناتك بشكل أسرع</p>
                      <button className="w-full bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors">
                        تفعيل الميزة
                      </button>
                    </div>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                    <div className="text-center">
                      <span className="text-4xl mb-4 block opacity-50" style={{filter: 'grayscale(1)'}}>📱</span>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">تطبيق الجوال</h3>
                      <p className="text-gray-600 text-sm mb-4">إدارة إعلاناتك من الهاتف</p>
                      <button className="w-full bg-purple-500 text-white py-2 rounded-lg hover:bg-purple-600 transition-colors">
                        تحميل التطبيق
                      </button>
                    </div>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                    <div className="text-center">
                      <span className="text-4xl mb-4 block opacity-50" style={{filter: 'grayscale(1)'}}>🎨</span>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">تصميم مخصص</h3>
                      <p className="text-gray-600 text-sm mb-4">صفحة شركة مخصصة</p>
                      <button className="w-full bg-indigo-500 text-white py-2 rounded-lg hover:bg-indigo-600 transition-colors">
                        طلب التصميم
                      </button>
                    </div>
                  </div>

                  <div className="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                    <div className="text-center">
                      <span className="text-4xl mb-4 block opacity-50" style={{filter: 'grayscale(1)'}}>📞</span>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">دعم مخصص</h3>
                      <p className="text-gray-600 text-sm mb-4">دعم فني على مدار الساعة</p>
                      <button className="w-full bg-red-500 text-white py-2 rounded-lg hover:bg-red-600 transition-colors">
                        تواصل معنا
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* نافذة تأكيد الحذف */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <div className="text-center">
              <div className="text-6xl mb-4">⚠️</div>
              <h3 className="text-xl font-bold text-gray-800 mb-4">تأكيد الحذف</h3>
              <p className="text-gray-600 mb-6">
                هل أنت متأكد من حذف هذا الإعلان؟ لا يمكن التراجع عن هذا الإجراء.
              </p>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowDeleteModal(false)}
                  className="flex-1 px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  إلغاء
                </button>
                <button
                  onClick={confirmDelete}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  حذف
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserDashboard;
