{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/HydrationProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\ninterface HydrationProviderProps {\n  children: React.ReactNode;\n}\n\nconst HydrationProvider = ({ children }: HydrationProviderProps) => {\n  const [isHydrated, setIsHydrated] = useState(false);\n\n  useEffect(() => {\n    // إزالة عناصر Grammarly قبل الـ hydration\n    const removeGrammarlyElements = () => {\n      // إزالة عناصر Grammarly\n      const grammarlySelectors = [\n        'grammarly-extension',\n        'grammarly-popups',\n        'grammarly-desktop-integration',\n        '[data-grammarly-shadow-root]',\n        '[data-grammarly-part]'\n      ];\n\n      grammarlySelectors.forEach(selector => {\n        const elements = document.querySelectorAll(selector);\n        elements.forEach(el => el.remove());\n      });\n\n      // إزالة attributes من body\n      const bodyAttributes = [\n        'data-new-gr-c-s-check-loaded',\n        'data-gr-ext-installed',\n        'data-new-gr-c-s-loaded',\n        'data-grammarly-shadow-root'\n      ];\n\n      bodyAttributes.forEach(attr => {\n        document.body.removeAttribute(attr);\n        document.documentElement.removeAttribute(attr);\n      });\n    };\n\n    // تشغيل التنظيف فوراً\n    removeGrammarlyElements();\n\n    // تشغيل التنظيف بشكل دوري لمنع إعادة الإدراج\n    const cleanupInterval = setInterval(removeGrammarlyElements, 100);\n\n    // تعيين الـ hydration بعد تأخير قصير\n    const timer = setTimeout(() => {\n      setIsHydrated(true);\n      clearInterval(cleanupInterval);\n    }, 150);\n\n    return () => {\n      clearTimeout(timer);\n      clearInterval(cleanupInterval);\n    };\n  }, []);\n\n  // عرض loading skeleton أثناء انتظار hydration\n  if (!isHydrated) {\n    return (\n      <div className=\"min-h-screen bg-gray-50\" suppressHydrationWarning={true}>\n        <div className=\"animate-pulse\">\n          {/* Header skeleton */}\n          <div className=\"h-16 bg-white border-b border-gray-200 mb-4\"></div>\n\n          {/* Hero section skeleton */}\n          <div className=\"container mx-auto px-4 py-8\">\n            <div className=\"h-32 bg-gray-200 rounded-lg mb-8\"></div>\n\n            {/* Categories skeleton */}\n            <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8\">\n              {Array.from({ length: 6 }).map((_, i) => (\n                <div key={i} className=\"h-24 bg-gray-200 rounded-lg\"></div>\n              ))}\n            </div>\n\n            {/* Content skeleton */}\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n              {Array.from({ length: 6 }).map((_, i) => (\n                <div key={i} className=\"h-48 bg-gray-200 rounded-lg\"></div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div suppressHydrationWarning={true}>\n      {children}\n    </div>\n  );\n};\n\nexport default HydrationProvider;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQA,MAAM,oBAAoB,CAAC,EAAE,QAAQ,EAA0B;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0CAA0C;QAC1C,MAAM,0BAA0B;YAC9B,wBAAwB;YACxB,MAAM,qBAAqB;gBACzB;gBACA;gBACA;gBACA;gBACA;aACD;YAED,mBAAmB,OAAO,CAAC,CAAA;gBACzB,MAAM,WAAW,SAAS,gBAAgB,CAAC;gBAC3C,SAAS,OAAO,CAAC,CAAA,KAAM,GAAG,MAAM;YAClC;YAEA,2BAA2B;YAC3B,MAAM,iBAAiB;gBACrB;gBACA;gBACA;gBACA;aACD;YAED,eAAe,OAAO,CAAC,CAAA;gBACrB,SAAS,IAAI,CAAC,eAAe,CAAC;gBAC9B,SAAS,eAAe,CAAC,eAAe,CAAC;YAC3C;QACF;QAEA,sBAAsB;QACtB;QAEA,6CAA6C;QAC7C,MAAM,kBAAkB,YAAY,yBAAyB;QAE7D,qCAAqC;QACrC,MAAM,QAAQ,WAAW;YACvB,cAAc;YACd,cAAc;QAChB,GAAG;QAEH,OAAO;YACL,aAAa;YACb,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,8CAA8C;IAC9C,IAAI,CAAC,YAAY;QACf,qBACE,8OAAC;YAAI,WAAU;YAA0B,0BAA0B;sBACjE,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;wCAAY,WAAU;uCAAb;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;wCAAY,WAAU;uCAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOxB;IAEA,qBACE,8OAAC;QAAI,0BAA0B;kBAC5B;;;;;;AAGP;uCAEe"}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/LiveChat.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\n\ninterface Message {\n  id: number;\n  text: string;\n  sender: 'user' | 'support';\n  timestamp: string;\n  type?: 'text' | 'image' | 'file';\n}\n\nconst LiveChat = () => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [messages, setMessages] = useState<Message[]>([\n    {\n      id: 1,\n      text: 'مرحباً! كيف يمكنني مساعدتك اليوم؟',\n      sender: 'support',\n      timestamp: new Date().toLocaleTimeString('ar-SY', { hour: '2-digit', minute: '2-digit' })\n    }\n  ]);\n  const [newMessage, setNewMessage] = useState('');\n  const [isTyping, setIsTyping] = useState(false);\n  const [isOnline, setIsOnline] = useState(true);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const quickReplies = [\n    'كيف أنشر إعلان؟',\n    'ما هي أنواع الحسابات؟',\n    'كيف أدفع؟',\n    'مشكلة في تسجيل الدخول',\n    'كيف أحذف حسابي؟',\n    'ما هي الباقات المتاحة؟',\n    'كيف أغير معلوماتي؟',\n    'مشكلة في الموقع'\n  ];\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  const sendMessage = (text: string) => {\n    if (!text.trim()) return;\n\n    const userMessage: Message = {\n      id: Date.now(),\n      text: text.trim(),\n      sender: 'user',\n      timestamp: new Date().toLocaleTimeString('ar-SY', { hour: '2-digit', minute: '2-digit' })\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setNewMessage('');\n    setIsTyping(true);\n\n    // محاكاة رد الدعم الفني\n    setTimeout(() => {\n      setIsTyping(false);\n      const supportMessage: Message = {\n        id: Date.now() + 1,\n        text: getAutoReply(text),\n        sender: 'support',\n        timestamp: new Date().toLocaleTimeString('ar-SY', { hour: '2-digit', minute: '2-digit' })\n      };\n      setMessages(prev => [...prev, supportMessage]);\n    }, 1500);\n  };\n\n  const getAutoReply = (userMessage: string): string => {\n    const message = userMessage.toLowerCase();\n\n    // أسئلة حول نشر الإعلانات\n    if (message.includes('إعلان') || message.includes('نشر') || message.includes('أضيف')) {\n      return '📝 لنشر إعلان:\\n1. اضغط على \"أضف إعلانك\" من القائمة الرئيسية\\n2. اختر التصنيف المناسب (عقارات، سيارات، إلخ)\\n3. املأ جميع البيانات المطلوبة\\n4. ارفع صور واضحة (حتى 10 صور)\\n5. اضغط \"نشر الإعلان\"\\n\\nملاحظة: الإعلانات المجانية تظهر لمدة 30 يوم.';\n    }\n\n    // أسئلة حول أنواع الحسابات\n    else if (message.includes('حساب') || message.includes('نوع') || message.includes('فرق')) {\n      return '👤 أنواع الحسابات:\\n\\n🔹 حساب فردي: للأشخاص العاديين\\n🔹 حساب تجاري: للشركات والأعمال\\n🔹 مكتب عقاري: متخصص للعقارات فقط\\n\\nكل نوع له باقات مختلفة وميزات خاصة. يمكنك ترقية حسابك في أي وقت من الملف الشخصي.';\n    }\n\n    // أسئلة حول الباقات والأسعار\n    else if (message.includes('باقة') || message.includes('سعر') || message.includes('تكلفة') || message.includes('مجاني')) {\n      return '💰 الباقات المتاحة:\\n\\n🆓 مجاني: 5 إعلانات شهرياً\\n🥈 فضي: 15 إعلان + ميزات إضافية\\n🥇 ذهبي: 25 إعلان + أولوية في البحث\\n💎 ماسي: إعلانات لا محدودة + جميع الميزات\\n\\nالأسعار تبدأ من 10$ شهرياً. اطلع على التفاصيل في صفحة الباقات.';\n    }\n\n    // أسئلة حول الدفع\n    else if (message.includes('دفع') || message.includes('فيزا') || message.includes('ماستر') || message.includes('باي بال') || message.includes('فوترة')) {\n      return '💳 طرق الدفع المقبولة:\\n\\n✅ فيزا (Visa)\\n✅ ماستركارد (MasterCard)\\n✅ باي بال (PayPal)\\n✅ كاش آب (Cash App)\\n✅ آبل باي (Apple Pay)\\n\\nجميع المدفوعات آمنة ومشفرة. يمكنك إضافة طريقة دفع من الإعدادات.';\n    }\n\n    // أسئلة حول تسجيل الدخول\n    else if (message.includes('دخول') || message.includes('تسجيل') || message.includes('لوجين')) {\n      return '🔐 مشاكل تسجيل الدخول:\\n\\n1. تأكد من صحة البريد الإلكتروني\\n2. تأكد من كلمة المرور\\n3. استخدم \"نسيت كلمة المرور\" إذا لزم الأمر\\n4. تأكد من تفعيل حسابك عبر البريد\\n5. امسح ذاكرة التخزين المؤقت\\n\\nإذا استمرت المشكلة، اتصل بنا مباشرة.';\n    }\n\n    // أسئلة حول كلمة المرور\n    else if (message.includes('كلمة المرور') || message.includes('باسورد') || message.includes('رقم سري')) {\n      return '🔑 تغيير كلمة المرور:\\n\\n1. اذهب إلى الملف الشخصي\\n2. اضغط على \"إعدادات الحساب\"\\n3. اختر \"تغيير كلمة المرور\"\\n4. أدخل كلمة المرور الحالية\\n5. أدخل كلمة المرور الجديدة\\n6. أكد كلمة المرور الجديدة\\n\\nكلمة المرور يجب أن تحتوي على 8 أحرف على الأقل.';\n    }\n\n    // أسئلة حول حذف الحساب\n    else if (message.includes('حذف') || message.includes('إلغاء') || message.includes('إغلاق')) {\n      return '🗑️ حذف الحساب:\\n\\n⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!\\n\\n1. اذهب إلى إعدادات الحساب\\n2. اضغط على \"حذف الحساب\"\\n3. أدخل كلمة المرور للتأكيد\\n4. اكتب \"حذف\" في المربع\\n5. اضغط \"تأكيد الحذف\"\\n\\nسيتم حذف جميع بياناتك وإعلاناتك نهائياً.';\n    }\n\n    // أسئلة حول تعديل المعلومات\n    else if (message.includes('تعديل') || message.includes('تغيير') || message.includes('تحديث')) {\n      return '✏️ تعديل المعلومات الشخصية:\\n\\n1. اذهب إلى \"الملف الشخصي\"\\n2. اضغط على \"تعديل\"\\n3. غيّر المعلومات المطلوبة\\n4. اضغط \"حفظ التغييرات\"\\n\\nيمكنك تعديل: الاسم، الهاتف، العنوان، الصورة الشخصية، ومعلومات الشركة.';\n    }\n\n    // أسئلة حول الصور\n    else if (message.includes('صور') || message.includes('صورة') || message.includes('رفع')) {\n      return '📸 رفع الصور:\\n\\n✅ الحد الأقصى: 10 صور لكل إعلان\\n✅ الحجم الأقصى: 5 ميجابايت لكل صورة\\n✅ الصيغ المقبولة: JPG, PNG, WEBP\\n✅ الدقة المنصوح بها: 1200x800 بكسل\\n\\nنصائح:\\n• استخدم صور واضحة وعالية الجودة\\n• صوّر من زوايا مختلفة\\n• تجنب الصور المكررة';\n    }\n\n    // أسئلة حول البحث\n    else if (message.includes('بحث') || message.includes('أجد') || message.includes('أبحث')) {\n      return '🔍 البحث في الموقع:\\n\\n1. استخدم شريط البحث في الأعلى\\n2. اختر التصنيف المناسب\\n3. استخدم الفلاتر المتقدمة:\\n   • السعر\\n   • الموقع\\n   • تاريخ النشر\\n   • نوع الإعلان\\n\\n💡 نصيحة: استخدم كلمات مفتاحية محددة للحصول على نتائج أفضل.';\n    }\n\n    // أسئلة حول التواصل\n    else if (message.includes('تواصل') || message.includes('اتصال') || message.includes('رسالة')) {\n      return '📞 التواصل مع المعلنين:\\n\\n✅ الهاتف: اضغط على رقم الهاتف للاتصال\\n✅ واتساب: اضغط على أيقونة واتساب\\n✅ رسائل الموقع: استخدم نظام الرسائل الداخلي\\n\\n⚠️ نصائح الأمان:\\n• تواصل عبر الموقع أولاً\\n• لا تشارك معلومات مالية\\n• قابل في أماكن عامة';\n    }\n\n    // أسئلة حول الأمان\n    else if (message.includes('أمان') || message.includes('احتيال') || message.includes('نصب')) {\n      return '🛡️ نصائح الأمان:\\n\\n❌ لا تدفع مقدماً قبل المعاينة\\n❌ لا تشارك معلومات بنكية\\n❌ احذر من العروض المشبوهة\\n✅ قابل في أماكن عامة\\n✅ تأكد من هوية البائع\\n✅ استخدم طرق دفع آمنة\\n\\n📞 للإبلاغ عن احتيال: +963988652401';\n    }\n\n    // أسئلة حول التطبيق\n    else if (message.includes('تطبيق') || message.includes('موبايل') || message.includes('هاتف')) {\n      return '📱 تطبيق من المالك:\\n\\n🔜 قريباً على:\\n• متجر آبل (App Store)\\n• متجر جوجل (Google Play)\\n\\nحالياً يمكنك استخدام الموقع من المتصفح على الهاتف. التطبيق سيوفر:\\n• إشعارات فورية\\n• سرعة أكبر\\n• سهولة في الاستخدام';\n    }\n\n    // أسئلة حول المشاكل التقنية\n    else if (message.includes('مشكلة') || message.includes('خطأ') || message.includes('لا يعمل')) {\n      return '🔧 حل المشاكل التقنية:\\n\\n1. أعد تحديث الصفحة (F5)\\n2. امسح ذاكرة التخزين المؤقت\\n3. جرب متصفح آخر\\n4. تأكد من اتصال الإنترنت\\n5. أعد تشغيل المتصفح\\n\\nإذا استمرت المشكلة:\\n📞 اتصل بنا: +963988652401\\n📧 أو راسلنا عبر النموذج';\n    }\n\n    // أسئلة عامة\n    else if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {\n      return '👋 أهلاً وسهلاً بك في موقع من المالك!\\n\\nأنا المساعد الذكي، يمكنني مساعدتك في:\\n• نشر الإعلانات\\n• إدارة الحساب\\n• حل المشاكل التقنية\\n• الإجابة على استفساراتك\\n\\nكيف يمكنني مساعدتك اليوم؟ 😊';\n    }\n\n    // رد افتراضي\n    else {\n      return '🤖 شكراً لتواصلك معنا!\\n\\nلم أتمكن من فهم استفسارك بالضبط. يمكنك:\\n\\n1. إعادة صياغة السؤال\\n2. اختيار من المواضيع الشائعة أدناه\\n3. التواصل مع فريق الدعم مباشرة\\n\\n📞 للدعم الفوري: +963988652401\\n\\nسأكون سعيداً لمساعدتك! 😊';\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage(newMessage);\n    }\n  };\n\n  return (\n    <>\n      {/* زر فتح الدردشة */}\n      {!isOpen && (\n        <button\n          onClick={() => setIsOpen(true)}\n          className=\"fixed bottom-6 left-6 w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-50 flex items-center justify-center group hover:scale-110\"\n        >\n          {/* رأس الروبوت مع سماعات */}\n          <div className=\"relative\">\n            {/* الرأس */}\n            <div className=\"w-8 h-8 bg-white/90 rounded-lg flex items-center justify-center relative animate-pulse\">\n              {/* العيون */}\n              <div className=\"flex gap-1\">\n                <div className=\"w-1.5 h-1.5 bg-blue-600 rounded-full animate-ping\"></div>\n                <div className=\"w-1.5 h-1.5 bg-blue-600 rounded-full animate-ping\" style={{ animationDelay: '0.5s' }}></div>\n              </div>\n\n              {/* الفم */}\n              <div className=\"absolute bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-0.5 bg-blue-600 rounded-full\"></div>\n            </div>\n\n            {/* السماعات */}\n            <div className=\"absolute -left-1 top-1 w-2 h-2 bg-gray-300 rounded-full border border-gray-400\"></div>\n            <div className=\"absolute -right-1 top-1 w-2 h-2 bg-gray-300 rounded-full border border-gray-400\"></div>\n\n            {/* موجات الصوت */}\n            <div className=\"absolute -left-2 top-0 w-1 h-1 bg-green-400 rounded-full animate-bounce opacity-70\"></div>\n            <div className=\"absolute -right-2 top-0 w-1 h-1 bg-green-400 rounded-full animate-bounce opacity-70\" style={{ animationDelay: '0.3s' }}></div>\n          </div>\n\n          {/* مؤشر الاتصال */}\n          {isOnline && (\n            <div className=\"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse\"></div>\n          )}\n        </button>\n      )}\n\n      {/* نافذة الدردشة */}\n      {isOpen && (\n        <div className=\"fixed bottom-6 left-6 w-80 h-96 bg-white rounded-xl shadow-2xl z-50 flex flex-col overflow-hidden border border-gray-200\">\n          {/* رأس الدردشة */}\n          <div className=\"bg-primary-600 text-white p-4 flex items-center justify-between\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-8 h-8 bg-white/20 rounded-full flex items-center justify-center relative\">\n                {/* رأس الروبوت الصغير */}\n                <div className=\"w-5 h-5 bg-white/90 rounded flex items-center justify-center relative\">\n                  <div className=\"flex gap-0.5\">\n                    <div className=\"w-1 h-1 bg-blue-600 rounded-full\"></div>\n                    <div className=\"w-1 h-1 bg-blue-600 rounded-full\"></div>\n                  </div>\n                  <div className=\"absolute bottom-0.5 left-1/2 transform -translate-x-1/2 w-1 h-0.5 bg-blue-600 rounded-full\"></div>\n                </div>\n                {/* السماعات الصغيرة */}\n                <div className=\"absolute -left-0.5 top-1 w-1 h-1 bg-gray-300 rounded-full\"></div>\n                <div className=\"absolute -right-0.5 top-1 w-1 h-1 bg-gray-300 rounded-full\"></div>\n              </div>\n              <div>\n                <h3 className=\"font-semibold\">المساعد الذكي</h3>\n                <div className=\"flex items-center gap-2 text-xs\">\n                  <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-400' : 'bg-gray-400'}`}></div>\n                  <span>{isOnline ? 'متصل الآن' : 'غير متصل'}</span>\n                </div>\n              </div>\n            </div>\n            <button\n              onClick={() => setIsOpen(false)}\n              className=\"text-white/80 hover:text-white text-xl\"\n            >\n              ×\n            </button>\n          </div>\n\n          {/* منطقة الرسائل */}\n          <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n            {messages.map((message) => (\n              <div\n                key={message.id}\n                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}\n              >\n                <div\n                  className={`max-w-xs px-4 py-2 rounded-lg ${\n                    message.sender === 'user'\n                      ? 'bg-primary-600 text-white'\n                      : 'bg-gray-100 text-gray-800'\n                  }`}\n                >\n                  <p className=\"text-sm\">{message.text}</p>\n                  <span className={`text-xs ${\n                    message.sender === 'user' ? 'text-primary-200' : 'text-gray-500'\n                  } block mt-1`}>\n                    {message.timestamp}\n                  </span>\n                </div>\n              </div>\n            ))}\n\n            {/* مؤشر الكتابة */}\n            {isTyping && (\n              <div className=\"flex justify-start\">\n                <div className=\"bg-gray-100 px-4 py-2 rounded-lg\">\n                  <div className=\"flex gap-1\">\n                    <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                    <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                    <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                  </div>\n                </div>\n              </div>\n            )}\n            <div ref={messagesEndRef} />\n          </div>\n\n          {/* الردود السريعة */}\n          {messages.length === 1 && (\n            <div className=\"p-3 border-t border-gray-200\">\n              <p className=\"text-xs text-gray-600 mb-2\">مواضيع شائعة:</p>\n              <div className=\"flex flex-wrap gap-1\">\n                {quickReplies.slice(0, 3).map((reply, index) => (\n                  <button\n                    key={index}\n                    onClick={() => sendMessage(reply)}\n                    className=\"text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full hover:bg-gray-200 transition-colors\"\n                  >\n                    {reply}\n                  </button>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* منطقة الإدخال */}\n          <div className=\"p-4 border-t border-gray-200\">\n            <div className=\"flex gap-2\">\n              <input\n                type=\"text\"\n                value={newMessage}\n                onChange={(e) => setNewMessage(e.target.value)}\n                onKeyPress={handleKeyPress}\n                placeholder=\"اكتب رسالتك...\"\n                className=\"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm\"\n              />\n              <button\n                onClick={() => sendMessage(newMessage)}\n                disabled={!newMessage.trim()}\n                className=\"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                <span className=\"text-sm\">📤</span>\n              </button>\n            </div>\n            <p className=\"text-xs text-gray-500 mt-2 text-center\">\n              عادة ما نرد خلال دقائق قليلة\n            </p>\n          </div>\n        </div>\n      )}\n    </>\n  );\n};\n\nexport default LiveChat;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,WAAW;IACf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAClD;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,WAAW,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBAAE,MAAM;gBAAW,QAAQ;YAAU;QACzF;KACD;IACD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,KAAK,IAAI,IAAI;QAElB,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG;YACZ,MAAM,KAAK,IAAI;YACf,QAAQ;YACR,WAAW,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBAAE,MAAM;gBAAW,QAAQ;YAAU;QACzF;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,cAAc;QACd,YAAY;QAEZ,wBAAwB;QACxB,WAAW;YACT,YAAY;YACZ,MAAM,iBAA0B;gBAC9B,IAAI,KAAK,GAAG,KAAK;gBACjB,MAAM,aAAa;gBACnB,QAAQ;gBACR,WAAW,IAAI,OAAO,kBAAkB,CAAC,SAAS;oBAAE,MAAM;oBAAW,QAAQ;gBAAU;YACzF;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAe;QAC/C,GAAG;IACL;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,UAAU,YAAY,WAAW;QAEvC,0BAA0B;QAC1B,IAAI,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,SAAS;YACpF,OAAO;QACT,OAGK,IAAI,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,QAAQ;YACvF,OAAO;QACT,OAGK,IAAI,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,UAAU;YACtH,OAAO;QACT,OAGK,IAAI,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,cAAc,QAAQ,QAAQ,CAAC,UAAU;YACrJ,OAAO;QACT,OAGK,IAAI,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,UAAU;YAC3F,OAAO;QACT,OAGK,IAAI,QAAQ,QAAQ,CAAC,kBAAkB,QAAQ,QAAQ,CAAC,aAAa,QAAQ,QAAQ,CAAC,YAAY;YACrG,OAAO;QACT,OAGK,IAAI,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,UAAU;YAC1F,OAAO;QACT,OAGK,IAAI,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,UAAU;YAC5F,OAAO;QACT,OAGK,IAAI,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,QAAQ;YACvF,OAAO;QACT,OAGK,IAAI,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,SAAS;YACvF,OAAO;QACT,OAGK,IAAI,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,UAAU;YAC5F,OAAO;QACT,OAGK,IAAI,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,aAAa,QAAQ,QAAQ,CAAC,QAAQ;YAC1F,OAAO;QACT,OAGK,IAAI,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,aAAa,QAAQ,QAAQ,CAAC,SAAS;YAC5F,OAAO;QACT,OAGK,IAAI,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,YAAY;YAC5F,OAAO;QACT,OAGK,IAAI,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,aAAa,QAAQ,QAAQ,CAAC,SAAS;YAC5F,OAAO;QACT,OAGK;YACH,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB,YAAY;QACd;IACF;IAEA,qBACE;;YAEG,CAAC,wBACA,8OAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;gDAAoD,OAAO;oDAAE,gBAAgB;gDAAO;;;;;;;;;;;;kDAIrG,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAIjB,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CAGf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;gCAAsF,OAAO;oCAAE,gBAAgB;gCAAO;;;;;;;;;;;;oBAItI,0BACC,8OAAC;wBAAI,WAAU;;;;;;;;;;;;YAMpB,wBACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;kEAEjB,8OAAC;wDAAI,WAAU;;;;;;;;;;;;0DAGjB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAgB;;;;;;0DAC9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EAAE,WAAW,iBAAiB,eAAe;;;;;;kEACnF,8OAAC;kEAAM,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0CAItC,8OAAC;gCACC,SAAS,IAAM,UAAU;gCACzB,WAAU;0CACX;;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;oCAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,KAAK,SAAS,gBAAgB,iBAAiB;8CAEhF,cAAA,8OAAC;wCACC,WAAW,CAAC,8BAA8B,EACxC,QAAQ,MAAM,KAAK,SACf,8BACA,6BACJ;;0DAEF,8OAAC;gDAAE,WAAU;0DAAW,QAAQ,IAAI;;;;;;0DACpC,8OAAC;gDAAK,WAAW,CAAC,QAAQ,EACxB,QAAQ,MAAM,KAAK,SAAS,qBAAqB,gBAClD,WAAW,CAAC;0DACV,QAAQ,SAAS;;;;;;;;;;;;mCAdjB,QAAQ,EAAE;;;;;4BAqBlB,0BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;gDAAkD,OAAO;oDAAE,gBAAgB;gDAAO;;;;;;0DACjG,8OAAC;gDAAI,WAAU;gDAAkD,OAAO;oDAAE,gBAAgB;gDAAO;;;;;;;;;;;;;;;;;;;;;;0CAKzG,8OAAC;gCAAI,KAAK;;;;;;;;;;;;oBAIX,SAAS,MAAM,KAAK,mBACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAC1C,8OAAC;gCAAI,WAAU;0CACZ,aAAa,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACpC,8OAAC;wCAEC,SAAS,IAAM,YAAY;wCAC3B,WAAU;kDAET;uCAJI;;;;;;;;;;;;;;;;kCAYf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,YAAY;wCACZ,aAAY;wCACZ,WAAU;;;;;;kDAEZ,8OAAC;wCACC,SAAS,IAAM,YAAY;wCAC3B,UAAU,CAAC,WAAW,IAAI;wCAC1B,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAG9B,8OAAC;gCAAE,WAAU;0CAAyC;;;;;;;;;;;;;;;;;;;;AAQlE;uCAEe"}}, {"offset": {"line": 708, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 714, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/ClientSideProtection.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\nexport default function ClientSideProtection() {\n  useEffect(() => {\n    // منع إضافات المتصفح من التدخل\n    const preventExtensions = () => {\n      // منع Grammarly\n      (window as any).grammarly = false;\n      (window as any).grammarlyExtension = false;\n      \n      // إزالة خصائص الإضافات\n      const elements = document.querySelectorAll('*');\n      elements.forEach(el => {\n        // إزالة خصائص Grammarly\n        if (el.hasAttribute('data-new-gr-c-s-check-loaded')) {\n          el.removeAttribute('data-new-gr-c-s-check-loaded');\n        }\n        if (el.hasAttribute('data-gr-ext-installed')) {\n          el.removeAttribute('data-gr-ext-installed');\n        }\n        \n        // منع تحرير النصوص\n        if ((el as HTMLElement).contentEditable === 'true' && \n            !el.matches('input, textarea, [data-editable=\"true\"]')) {\n          (el as HTMLElement).contentEditable = 'false';\n        }\n        \n        // إزالة خاصية user-modify\n        const htmlEl = el as HTMLElement;\n        if (htmlEl.style) {\n          htmlEl.style.webkitUserModify = 'read-only';\n          (htmlEl.style as any).userModify = 'read-only';\n        }\n      });\n    };\n\n    // تشغيل فوري\n    preventExtensions();\n    \n    // مراقبة مستمرة للتغييرات\n    const observer = new MutationObserver(preventExtensions);\n    \n    observer.observe(document.body, {\n      attributes: true,\n      subtree: true,\n      childList: true,\n      attributeFilter: ['data-new-gr-c-s-check-loaded', 'data-gr-ext-installed', 'contenteditable']\n    });\n\n    // تنظيف عند إلغاء التحميل\n    return () => {\n      observer.disconnect();\n    };\n  }, []);\n\n  return null; // هذا المكون لا يعرض أي شيء\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,MAAM,oBAAoB;YACxB,gBAAgB;YACf,OAAe,SAAS,GAAG;YAC3B,OAAe,kBAAkB,GAAG;YAErC,uBAAuB;YACvB,MAAM,WAAW,SAAS,gBAAgB,CAAC;YAC3C,SAAS,OAAO,CAAC,CAAA;gBACf,wBAAwB;gBACxB,IAAI,GAAG,YAAY,CAAC,iCAAiC;oBACnD,GAAG,eAAe,CAAC;gBACrB;gBACA,IAAI,GAAG,YAAY,CAAC,0BAA0B;oBAC5C,GAAG,eAAe,CAAC;gBACrB;gBAEA,mBAAmB;gBACnB,IAAI,AAAC,GAAmB,eAAe,KAAK,UACxC,CAAC,GAAG,OAAO,CAAC,4CAA4C;oBACzD,GAAmB,eAAe,GAAG;gBACxC;gBAEA,0BAA0B;gBAC1B,MAAM,SAAS;gBACf,IAAI,OAAO,KAAK,EAAE;oBAChB,OAAO,KAAK,CAAC,gBAAgB,GAAG;oBAC/B,OAAO,KAAK,CAAS,UAAU,GAAG;gBACrC;YACF;QACF;QAEA,aAAa;QACb;QAEA,0BAA0B;QAC1B,MAAM,WAAW,IAAI,iBAAiB;QAEtC,SAAS,OAAO,CAAC,SAAS,IAAI,EAAE;YAC9B,YAAY;YACZ,SAAS;YACT,WAAW;YACX,iBAAiB;gBAAC;gBAAgC;gBAAyB;aAAkB;QAC/F;QAEA,0BAA0B;QAC1B,OAAO;YACL,SAAS,UAAU;QACrB;IACF,GAAG,EAAE;IAEL,OAAO,MAAM,4BAA4B;AAC3C"}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 776, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/ScrollIndicators.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface ScrollIndicatorsProps {\n  className?: string;\n}\n\nexport default function ScrollIndicators({ className = '' }: ScrollIndicatorsProps) {\n  // حالات التمرير الذكي\n  const [showScrollToTop, setShowScrollToTop] = useState(false);\n  const [showScrollToBottom, setShowScrollToBottom] = useState(false);\n  const [lastScrollY, setLastScrollY] = useState(0);\n  const [isScrolling, setIsScrolling] = useState(false);\n  const [isMobileDevice, setIsMobileDevice] = useState(false);\n  const [isTabletDevice, setIsTabletDevice] = useState(false);\n  const [scrollProgress, setScrollProgress] = useState(0);\n  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(null);\n  const [isAtTop, setIsAtTop] = useState(true);\n  const [isAtBottom, setIsAtBottom] = useState(false);\n  const [showScrollIndicator, setShowScrollIndicator] = useState(false);\n  const [scrollVelocity, setScrollVelocity] = useState(0);\n  const [isClient, setIsClient] = useState(false);\n\n  // إصلاح مشكلة Hydration\n  useEffect(() => {\n    setIsClient(true);\n    // تحديد نوع الجهاز\n    if (typeof window !== 'undefined') {\n      const updateDeviceType = () => {\n        const width = window.innerWidth;\n        setIsMobileDevice(width < 768);\n        setIsTabletDevice(width >= 768 && width < 1024);\n      };\n\n      updateDeviceType();\n\n      // مراقبة تغيير حجم الشاشة\n      const handleResize = () => {\n        updateDeviceType();\n      };\n\n      window.addEventListener('resize', handleResize);\n      return () => window.removeEventListener('resize', handleResize);\n    }\n  }, []);\n\n  // مراقبة التمرير لإظهار/إخفاء أزرار التنقل\n  useEffect(() => {\n    if (typeof window === 'undefined' || !isClient) return;\n\n    const handleScroll = () => {\n      const currentScrollY = window.scrollY;\n      const windowHeight = window.innerHeight;\n      const documentHeight = document.documentElement.scrollHeight;\n\n      // تحديد اتجاه التمرير وحساب السرعة\n      const direction = currentScrollY > lastScrollY ? 'down' : currentScrollY < lastScrollY ? 'up' : null;\n      const velocity = Math.abs(currentScrollY - lastScrollY);\n\n      setScrollDirection(direction);\n      setScrollVelocity(velocity);\n\n      // تحديث موضع التمرير الأخير\n      setLastScrollY(currentScrollY);\n\n      // حساب نسبة التقدم في التمرير\n      const maxScroll = documentHeight - windowHeight;\n      const progress = maxScroll > 0 ? (currentScrollY / maxScroll) * 100 : 0;\n      setScrollProgress(Math.min(100, Math.max(0, progress)));\n\n      // تحديد ما إذا كنا في أعلى أو أسفل الصفحة\n      const atTop = currentScrollY <= 50;\n      const atBottom = currentScrollY + windowHeight >= documentHeight - 50;\n\n      setIsAtTop(atTop);\n      setIsAtBottom(atBottom);\n\n      // إظهار مؤشر التمرير عند الوصول للحدود\n      if ((atTop && direction === 'up') || (atBottom && direction === 'down')) {\n        setShowScrollIndicator(true);\n\n        // إضافة اهتزاز للموبايل عند الوصول للحدود\n        if (navigator.vibrate && window.innerWidth < 768) {\n          navigator.vibrate([50, 25, 50]); // نمط اهتزاز قصير ومصغر\n        }\n\n        setTimeout(() => setShowScrollIndicator(false), 2000);\n      }\n\n      // إظهار زر العودة للأعلى إذا تم التمرير أكثر من 200px\n      setShowScrollToTop(currentScrollY > 200);\n\n      // إظهار زر التمرير للأسفل إذا لم نصل للنهاية\n      const isNearBottom = currentScrollY + windowHeight >= documentHeight - 100;\n      setShowScrollToBottom(!isNearBottom && currentScrollY < documentHeight - windowHeight - 150);\n\n      // تحديد حالة التمرير\n      setIsScrolling(true);\n\n      // إزالة حالة التمرير بعد توقف المستخدم\n      clearTimeout((window as any).scrollTimeout);\n      (window as any).scrollTimeout = setTimeout(() => {\n        setIsScrolling(false);\n        setScrollDirection(null);\n      }, 100);\n    };\n\n    // إضافة مستمع التمرير مع throttling\n    let ticking = false;\n    const throttledHandleScroll = () => {\n      if (!ticking) {\n        requestAnimationFrame(() => {\n          handleScroll();\n          ticking = false;\n        });\n        ticking = true;\n      }\n    };\n\n    window.addEventListener('scroll', throttledHandleScroll, { passive: true });\n\n    // تنظيف المستمع عند إلغاء التحميل\n    return () => {\n      window.removeEventListener('scroll', throttledHandleScroll);\n      clearTimeout((window as any).scrollTimeout);\n    };\n  }, [isClient, lastScrollY]);\n\n  // دالة للتمرير إلى الأعلى\n  const scrollToTop = () => {\n    if (typeof window === 'undefined' || !isClient) return;\n\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n\n  // دالة للتمرير إلى الأسفل\n  const scrollToBottom = () => {\n    if (typeof window === 'undefined' || !isClient) return;\n\n    window.scrollTo({\n      top: document.documentElement.scrollHeight,\n      behavior: 'smooth'\n    });\n  };\n\n  // دالة للتمرير إلى الخطوة التالية\n  const scrollToNextSection = () => {\n    if (typeof window === 'undefined' || !isClient) return;\n\n    const currentScrollY = window.scrollY;\n    const windowHeight = window.innerHeight;\n    const nextScrollPosition = currentScrollY + windowHeight * 0.7; // تمرير 70% من ارتفاع الشاشة\n\n    window.scrollTo({\n      top: Math.min(nextScrollPosition, document.documentElement.scrollHeight - windowHeight),\n      behavior: 'smooth'\n    });\n  };\n\n  // دالة للتمرير إلى الخطوة السابقة\n  const scrollToPrevSection = () => {\n    if (typeof window === 'undefined' || !isClient) return;\n\n    const currentScrollY = window.scrollY;\n    const windowHeight = window.innerHeight;\n    const prevScrollPosition = currentScrollY - windowHeight * 0.7; // تمرير 70% من ارتفاع الشاشة للخلف\n\n    window.scrollTo({\n      top: Math.max(prevScrollPosition, 0),\n      behavior: 'smooth'\n    });\n  };\n\n  if (!isClient) return null;\n\n  return (\n    <>\n      {/* شريط التقدم في التمرير */}\n      <div className=\"fixed top-0 left-0 w-full h-0.5 bg-gray-200/30 z-50\">\n        <div\n          className=\"h-full bg-gradient-to-r from-primary-500/70 to-primary-600/70 transition-all duration-300 ease-out shadow-sm\"\n          style={{ width: `${scrollProgress}%` }}\n        />\n      </div>\n\n      {/* أزرار التنقل الذكية */}\n      <>\n        {/* زر العودة للأعلى */}\n        {showScrollToTop && (\n          <button\n            onClick={scrollToTop}\n            className={`fixed bottom-12 right-3 z-50 bg-gradient-to-r from-primary-600/60 to-primary-700/60 hover:from-primary-700/80 hover:to-primary-800/80 text-white p-2 rounded-full shadow-md transition-all duration-300 transform ${\n              isScrolling ? 'scale-105 rotate-1' : 'scale-100'\n            } hover:scale-105 hover:-translate-y-0.5 focus:outline-none focus:ring-1 focus:ring-primary-300 group backdrop-blur-sm`}\n            style={{\n              opacity: showScrollToTop ? 0.7 : 0,\n              visibility: showScrollToTop ? 'visible' : 'hidden'\n            }}\n            aria-label=\"العودة للأعلى\"\n          >\n            <svg className=\"w-3.5 h-3.5 group-hover:animate-bounce\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2.5} d=\"M5 10l7-7m0 0l7 7m-7-7v18\" />\n            </svg>\n            <div className=\"absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse\"></div>\n          </button>\n        )}\n\n        {/* زر التمرير للأسفل */}\n        {showScrollToBottom && (\n          <button\n            onClick={scrollToBottom}\n            className={`fixed bottom-20 right-3 z-50 bg-gradient-to-r from-gray-600/60 to-gray-700/60 hover:from-gray-700/80 hover:to-gray-800/80 text-white p-2 rounded-full shadow-md transition-all duration-300 transform ${\n              isScrolling ? 'scale-105 -rotate-1' : 'scale-100'\n            } hover:scale-105 hover:translate-y-0.5 focus:outline-none focus:ring-1 focus:ring-gray-300 group backdrop-blur-sm`}\n            style={{\n              opacity: showScrollToBottom ? 0.7 : 0,\n              visibility: showScrollToBottom ? 'visible' : 'hidden'\n            }}\n            aria-label=\"التمرير للأسفل\"\n          >\n            <svg className=\"w-3.5 h-3.5 group-hover:animate-bounce\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2.5} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n            </svg>\n            <div className=\"absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse\"></div>\n          </button>\n        )}\n\n        {/* أزرار التنقل السريع للموبايل والتابلت */}\n        {(isMobileDevice || isTabletDevice) && (showScrollToTop || showScrollToBottom) && (\n          <div className=\"fixed bottom-3 left-3 z-50 flex flex-col gap-1.5\" style={{ opacity: 0.6 }}>\n            {/* زر التمرير للأعلى قليلاً */}\n            <button\n              onClick={scrollToPrevSection}\n              className=\"bg-gradient-to-r from-blue-500/70 to-blue-600/70 hover:from-blue-600/90 hover:to-blue-700/90 text-white p-1.5 rounded-full shadow-sm transition-all duration-300 hover:scale-105 hover:-translate-y-0.5 focus:outline-none focus:ring-1 focus:ring-blue-300 group backdrop-blur-sm\"\n              aria-label=\"التمرير للأعلى قليلاً\"\n            >\n              <svg className=\"w-2.5 h-2.5 group-hover:animate-pulse\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2.5} d=\"M7 11l5-5m0 0l5 5m-5-5v12\" />\n              </svg>\n            </button>\n\n            {/* زر التمرير للأسفل قليلاً */}\n            <button\n              onClick={scrollToNextSection}\n              className=\"bg-gradient-to-r from-green-500/70 to-green-600/70 hover:from-green-600/90 hover:to-green-700/90 text-white p-1.5 rounded-full shadow-sm transition-all duration-300 hover:scale-105 hover:translate-y-0.5 focus:outline-none focus:ring-1 focus:ring-green-300 group backdrop-blur-sm\"\n              aria-label=\"التمرير للأسفل قليلاً\"\n            >\n              <svg className=\"w-2.5 h-2.5 group-hover:animate-pulse\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2.5} d=\"M17 13l-5 5m0 0l-5-5m5 5V6\" />\n              </svg>\n            </button>\n          </div>\n        )}\n      </>\n\n      {/* مؤشرات التمرير عند الوصول للحدود */}\n      {showScrollIndicator && (\n        <>\n          {/* مؤشر الوصول لأعلى الصفحة */}\n          {isAtTop && (\n            <div className=\"fixed top-12 left-1/2 transform -translate-x-1/2 z-50\">\n              <div className=\"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-3 py-1.5 rounded-full shadow-md animate-bounce text-xs\">\n                <div className=\"flex items-center gap-1.5\">\n                  <svg className=\"w-2.5 h-2.5 animate-pulse\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={3} d=\"M5 15l7-7 7 7\" />\n                  </svg>\n                  <span className=\"font-medium\">أعلى الصفحة</span>\n                  <svg className=\"w-2.5 h-2.5 animate-pulse\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={3} d=\"M5 15l7-7 7 7\" />\n                  </svg>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* مؤشر الوصول لأسفل الصفحة */}\n          {isAtBottom && (\n            <div className=\"fixed bottom-12 left-1/2 transform -translate-x-1/2 z-50\">\n              <div className=\"bg-gradient-to-r from-green-500 to-emerald-600 text-white px-3 py-1.5 rounded-full shadow-md animate-bounce text-xs\">\n                <div className=\"flex items-center gap-1.5\">\n                  <svg className=\"w-2.5 h-2.5 animate-pulse\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={3} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                  <span className=\"font-medium\">آخر الصفحة</span>\n                  <svg className=\"w-2.5 h-2.5 animate-pulse\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={3} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </div>\n              </div>\n            </div>\n          )}\n        </>\n      )}\n\n      {/* مؤشر اتجاه التمرير الجانبي المصغر */}\n      {scrollDirection && isScrolling && !isMobileDevice && (\n        <div className={`fixed right-1.5 top-1/2 transform -translate-y-1/2 z-40 transition-all duration-300 ${\n          scrollDirection === 'down' ? 'animate-pulse' : 'animate-bounce'\n        }`} style={{ opacity: 0.5 }}>\n          <div className={`w-5 h-5 rounded-full flex items-center justify-center shadow-sm backdrop-blur-sm ${\n            scrollDirection === 'down'\n              ? 'bg-gradient-to-b from-orange-400/70 to-red-500/70 text-white'\n              : 'bg-gradient-to-t from-blue-400/70 to-purple-500/70 text-white'\n          }`}>\n            <svg className=\"w-2.5 h-2.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              {scrollDirection === 'down' ? (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={3} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n              ) : (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={3} d=\"M5 10l7-7m0 0l7 7m-7-7v18\" />\n              )}\n            </svg>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAQe,SAAS,iBAAiB,EAAE,YAAY,EAAE,EAAyB;IAChF,sBAAsB;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC7E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;QACZ,mBAAmB;QACnB,uCAAmC;;QAgBnC;IACF,GAAG,EAAE;IAEL,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAgD;;QAEhD,MAAM;QAyDN,oCAAoC;QACpC,IAAI;QACJ,MAAM;IAiBR,GAAG;QAAC;QAAU;KAAY;IAE1B,0BAA0B;IAC1B,MAAM,cAAc;QAClB,wCAAgD;;IAMlD;IAEA,0BAA0B;IAC1B,MAAM,iBAAiB;QACrB,wCAAgD;;IAMlD;IAEA,kCAAkC;IAClC,MAAM,sBAAsB;QAC1B,wCAAgD;;QAEhD,MAAM;QACN,MAAM;QACN,MAAM,gCAA0D,6BAA6B;IAM/F;IAEA,kCAAkC;IAClC,MAAM,sBAAsB;QAC1B,wCAAgD;;QAEhD,MAAM;QACN,MAAM;QACN,MAAM,gCAA0D,mCAAmC;IAMrG;IAEA,IAAI,CAAC,UAAU,OAAO;IAEtB,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,OAAO,GAAG,eAAe,CAAC,CAAC;oBAAC;;;;;;;;;;;0BAKzC;;oBAEG,iCACC,8OAAC;wBACC,SAAS;wBACT,WAAW,CAAC,kNAAkN,EAC5N,cAAc,uBAAuB,YACtC,qHAAqH,CAAC;wBACvH,OAAO;4BACL,SAAS,kBAAkB,MAAM;4BACjC,YAAY,kBAAkB,YAAY;wBAC5C;wBACA,cAAW;;0CAEX,8OAAC;gCAAI,WAAU;gCAAyC,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAChG,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAK,GAAE;;;;;;;;;;;0CAEzE,8OAAC;gCAAI,WAAU;;;;;;;;;;;;oBAKlB,oCACC,8OAAC;wBACC,SAAS;wBACT,WAAW,CAAC,sMAAsM,EAChN,cAAc,wBAAwB,YACvC,iHAAiH,CAAC;wBACnH,OAAO;4BACL,SAAS,qBAAqB,MAAM;4BACpC,YAAY,qBAAqB,YAAY;wBAC/C;wBACA,cAAW;;0CAEX,8OAAC;gCAAI,WAAU;gCAAyC,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAChG,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAK,GAAE;;;;;;;;;;;0CAEzE,8OAAC;gCAAI,WAAU;;;;;;;;;;;;oBAKlB,CAAC,kBAAkB,cAAc,KAAK,CAAC,mBAAmB,kBAAkB,mBAC3E,8OAAC;wBAAI,WAAU;wBAAmD,OAAO;4BAAE,SAAS;wBAAI;;0CAEtF,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCAAI,WAAU;oCAAwC,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC/F,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAK3E,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,8OAAC;oCAAI,WAAU;oCAAwC,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC/F,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;YAQhF,qCACC;;oBAEG,yBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAA4B,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACnF,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,8OAAC;wCAAK,WAAU;kDAAc;;;;;;kDAC9B,8OAAC;wCAAI,WAAU;wCAA4B,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACnF,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQ9E,4BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;wCAA4B,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACnF,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;kDAEvE,8OAAC;wCAAK,WAAU;kDAAc;;;;;;kDAC9B,8OAAC;wCAAI,WAAU;wCAA4B,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACnF,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUlF,mBAAmB,eAAe,CAAC,gCAClC,8OAAC;gBAAI,WAAW,CAAC,oFAAoF,EACnG,oBAAoB,SAAS,kBAAkB,kBAC/C;gBAAE,OAAO;oBAAE,SAAS;gBAAI;0BACxB,cAAA,8OAAC;oBAAI,WAAW,CAAC,iFAAiF,EAChG,oBAAoB,SAChB,iEACA,iEACJ;8BACA,cAAA,8OAAC;wBAAI,WAAU;wBAAc,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACpE,oBAAoB,uBACnB,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;iDAErE,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;AAQrF"}}, {"offset": {"line": 1218, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1248, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/FloatingActionButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\n\ninterface AssistiveTouchProps {\n  onFilterClick?: () => void;\n}\n\nexport default function AssistiveTouch({ onFilterClick }: AssistiveTouchProps) {\n  const [isOpen, setIsOpen] = useState(false);\n  const [position, setPosition] = useState({ x: 20, y: 100 });\n  const [isDragging, setIsDragging] = useState(false);\n  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });\n  const buttonRef = useRef<HTMLDivElement>(null);\n  const router = useRouter();\n  const pathname = usePathname();\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (buttonRef.current && !buttonRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleStart = (clientX: number, clientY: number) => {\n    if (isOpen) return;\n    setIsDragging(true);\n    setDragStart({\n      x: clientX - position.x,\n      y: clientY - position.y\n    });\n  };\n\n  const handleMouseDown = (e: React.MouseEvent) => handleStart(e.clientX, e.clientY);\n  const handleTouchStart = (e: React.TouchEvent) => {\n    const touch = e.touches[0];\n    handleStart(touch.clientX, touch.clientY);\n  };\n\n  const handleMove = (clientX: number, clientY: number) => {\n    if (!isDragging) return;\n    const newX = clientX - dragStart.x;\n    const newY = clientY - dragStart.y;\n\n    const radius = 110; // نصف قطر الدائرة\n    const buttonSize = 64; // حجم الزر الرئيسي\n    const maxX = window.innerWidth - radius - buttonSize / 2;\n    const minX = radius - buttonSize / 2;\n    const maxY = window.innerHeight - radius - buttonSize / 2;\n    const minY = radius - buttonSize / 2;\n\n    setPosition({\n      x: Math.max(minX, Math.min(newX, maxX)),\n      y: Math.max(minY, Math.min(newY, maxY))\n    });\n  };\n\n  const handleMouseMove = (e: MouseEvent) => handleMove(e.clientX, e.clientY);\n  const handleTouchMove = (e: TouchEvent) => {\n    e.preventDefault();\n    const touch = e.touches[0];\n    handleMove(touch.clientX, touch.clientY);\n  };\n\n  const handleEnd = () => setIsDragging(false);\n\n  useEffect(() => {\n    if (isDragging) {\n      document.addEventListener('mousemove', handleMouseMove);\n      document.addEventListener('mouseup', handleEnd);\n      document.addEventListener('touchmove', handleTouchMove, { passive: false });\n      document.addEventListener('touchend', handleEnd);\n    }\n    return () => {\n      document.removeEventListener('mousemove', handleMouseMove);\n      document.removeEventListener('mouseup', handleEnd);\n      document.removeEventListener('touchmove', handleTouchMove);\n      document.removeEventListener('touchend', handleEnd);\n    };\n  }, [isDragging, dragStart]);\n\n  const handleGoBack = () => { window.history.back(); setIsOpen(false); };\n  const handleGoHome = () => { router.push('/'); setIsOpen(false); };\n  const handleMyAds = () => { router.push('/dashboard'); setIsOpen(false); };\n  const handleJobs = () => { router.push('/jobs/all'); setIsOpen(false); };\n\n  const handleClick = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    if (!isDragging) setIsOpen(!isOpen);\n  };\n\n  const menuItems = [\n    { icon: '/images/AssistiveTouch/الصفحة الرئيسية.png', label: 'الرئيسية', action: handleGoHome },\n    { icon: '/images/AssistiveTouch/رجوع.png', label: 'رجوع', action: handleGoBack },\n    { icon: '/images/AssistiveTouch/إعلاناتي.png', label: 'إعلاناتي', action: handleMyAds },\n    { icon: '/images/AssistiveTouch/أيقونة الوظائف.png', label: 'الوظائف', action: handleJobs }\n  ];\n\n  const containerSize = 320; // w-80 = 320px\n  const radius = 110;\n  const buttonSize = 64;\n\n  return (\n    <div\n      ref={buttonRef}\n      className=\"fixed z-50 transition-all duration-300 block\"\n      style={{\n        left: `${position.x}px`,\n        top: `${position.y}px`,\n        cursor: isDragging ? 'grabbing' : 'grab',\n        display: 'block',\n        visibility: 'visible'\n      }}\n    >\n      {isOpen && (\n        <div className=\"relative w-80 h-80\">\n          {/* الخلفية الدائرية */}\n          <div\n            className=\"absolute inset-0 rounded-full backdrop-blur-lg\"\n            style={{\n              background:\n                'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 50%, rgba(0,0,0,0.1) 100%)',\n              border: '1px solid rgba(255, 255, 255, 0.2)',\n              boxShadow: '0 8px 32px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.1)'\n            }}\n          />\n\n          {/* شعار الموقع في الوسط */}\n          <div className=\"absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16\">\n            <img\n              src=\"/images/AssistiveTouch/شعار موقع من المالك.png\"\n              alt=\"شعار الموقع\"\n              className=\"w-full h-full object-contain\"\n            />\n          </div>\n\n          {/* الأزرار الأربعة */}\n          {menuItems.map((item, index) => {\n            let x = containerSize / 2 - buttonSize / 2;\n            let y = containerSize / 2 - buttonSize / 2;\n\n            switch (item.label) {\n              case 'الرئيسية': x += radius; break; // يمين\n              case 'رجوع': x -= radius; break;    // يسار\n              case 'الوظائف': y += radius; break; // تحت\n              case 'إعلاناتي': y -= radius; break; // فوق\n            }\n\n            return (\n              <div\n                key={index}\n                className=\"absolute flex flex-col items-center opacity-0\"\n                style={{\n                  left: `${x}px`,\n                  top: `${y}px`,\n                  animation: `fadeInScale 0.4s ease-out ${index * 150}ms forwards`\n                }}\n              >\n                <button\n                  onClick={item.action}\n                  className=\"w-16 h-16 rounded-full flex items-center justify-center hover:scale-110 transition-all duration-200 p-2\"\n                  style={{\n                    background: 'linear-gradient(135deg, rgba(255,255,255,0.25) 0%, rgba(255,255,255,0.1) 100%)',\n                    border: '1px solid rgba(255,255,255,0.3)',\n                    boxShadow: '0 4px 16px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.2)',\n                    backdropFilter: 'blur(10px)'\n                  }}\n                >\n                  <img src={item.icon} alt={item.label} className=\"w-full h-full object-contain\" />\n                </button>\n                <span className=\"text-white text-xs font-medium text-center mt-1 drop-shadow-lg\">{item.label}</span>\n              </div>\n            );\n          })}\n        </div>\n      )}\n\n      {/* الزر الرئيسي */}\n      <div\n        onMouseDown={handleMouseDown}\n        onTouchStart={handleTouchStart}\n        onClick={handleClick}\n        className={`w-16 h-16 bg-black bg-opacity-70 backdrop-blur-md rounded-2xl flex items-center justify-center transition-all duration-300 hover:scale-105 active:scale-95 ${\n          isOpen ? 'scale-110 bg-opacity-80' : 'scale-100'\n        }`}\n        style={{\n          border: '2px solid rgba(255, 255, 255, 0.3)',\n          boxShadow: isOpen\n            ? '0 12px 40px rgba(0, 0, 0, 0.5), 0 0 20px rgba(255, 255, 255, 0.1)'\n            : '0 8px 32px rgba(0, 0, 0, 0.3)',\n          userSelect: 'none',\n          touchAction: 'none'\n        }}\n      >\n        <img\n          src=\"/images/AssistiveTouch/AssistiveTouch.png\"\n          alt=\"Assistive Touch\"\n          className=\"w-10 h-10\"\n          style={{ filter: isOpen ? 'brightness(1.2)' : 'brightness(1)' }}\n        />\n      </div>\n\n      <style jsx>{`\n        @keyframes fadeInScale {\n          0% { opacity: 0; transform: scale(0.5); }\n          100% { opacity: 1; transform: scale(1); }\n        }\n      `}</style>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;;AASe,SAAS,eAAe,EAAE,aAAa,EAAuB;IAC3E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAI,GAAG;IAAI;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACxD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC1E,UAAU;YACZ;QACF;QACA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,cAAc,CAAC,SAAiB;QACpC,IAAI,QAAQ;QACZ,cAAc;QACd,aAAa;YACX,GAAG,UAAU,SAAS,CAAC;YACvB,GAAG,UAAU,SAAS,CAAC;QACzB;IACF;IAEA,MAAM,kBAAkB,CAAC,IAAwB,YAAY,EAAE,OAAO,EAAE,EAAE,OAAO;IACjF,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,EAAE,OAAO,CAAC,EAAE;QAC1B,YAAY,MAAM,OAAO,EAAE,MAAM,OAAO;IAC1C;IAEA,MAAM,aAAa,CAAC,SAAiB;QACnC,IAAI,CAAC,YAAY;QACjB,MAAM,OAAO,UAAU,UAAU,CAAC;QAClC,MAAM,OAAO,UAAU,UAAU,CAAC;QAElC,MAAM,SAAS,KAAK,kBAAkB;QACtC,MAAM,aAAa,IAAI,mBAAmB;QAC1C,MAAM,OAAO,OAAO,UAAU,GAAG,SAAS,aAAa;QACvD,MAAM,OAAO,SAAS,aAAa;QACnC,MAAM,OAAO,OAAO,WAAW,GAAG,SAAS,aAAa;QACxD,MAAM,OAAO,SAAS,aAAa;QAEnC,YAAY;YACV,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM;YACjC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM;QACnC;IACF;IAEA,MAAM,kBAAkB,CAAC,IAAkB,WAAW,EAAE,OAAO,EAAE,EAAE,OAAO;IAC1E,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,MAAM,QAAQ,EAAE,OAAO,CAAC,EAAE;QAC1B,WAAW,MAAM,OAAO,EAAE,MAAM,OAAO;IACzC;IAEA,MAAM,YAAY,IAAM,cAAc;IAEtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY;YACd,SAAS,gBAAgB,CAAC,aAAa;YACvC,SAAS,gBAAgB,CAAC,WAAW;YACrC,SAAS,gBAAgB,CAAC,aAAa,iBAAiB;gBAAE,SAAS;YAAM;YACzE,SAAS,gBAAgB,CAAC,YAAY;QACxC;QACA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,mBAAmB,CAAC,aAAa;YAC1C,SAAS,mBAAmB,CAAC,YAAY;QAC3C;IACF,GAAG;QAAC;QAAY;KAAU;IAE1B,MAAM,eAAe;QAAQ,OAAO,OAAO,CAAC,IAAI;QAAI,UAAU;IAAQ;IACtE,MAAM,eAAe;QAAQ,OAAO,IAAI,CAAC;QAAM,UAAU;IAAQ;IACjE,MAAM,cAAc;QAAQ,OAAO,IAAI,CAAC;QAAe,UAAU;IAAQ;IACzE,MAAM,aAAa;QAAQ,OAAO,IAAI,CAAC;QAAc,UAAU;IAAQ;IAEvE,MAAM,cAAc,CAAC;QACnB,EAAE,eAAe;QACjB,IAAI,CAAC,YAAY,UAAU,CAAC;IAC9B;IAEA,MAAM,YAAY;QAChB;YAAE,MAAM;YAA8C,OAAO;YAAY,QAAQ;QAAa;QAC9F;YAAE,MAAM;YAAmC,OAAO;YAAQ,QAAQ;QAAa;QAC/E;YAAE,MAAM;YAAuC,OAAO;YAAY,QAAQ;QAAY;QACtF;YAAE,MAAM;YAA6C,OAAO;YAAW,QAAQ;QAAW;KAC3F;IAED,MAAM,gBAAgB,KAAK,eAAe;IAC1C,MAAM,SAAS;IACf,MAAM,aAAa;IAEnB,qBACE,8OAAC;QACC,KAAK;QAEL,OAAO;YACL,MAAM,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC;YACvB,KAAK,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC;YACtB,QAAQ,aAAa,aAAa;YAClC,SAAS;YACT,YAAY;QACd;kDAPU;;YAST,wBACC,8OAAC;0DAAc;;kCAEb,8OAAC;wBAEC,OAAO;4BACL,YACE;4BACF,QAAQ;4BACR,WAAW;wBACb;kEANU;;;;;;kCAUZ,8OAAC;kEAAc;kCACb,cAAA,8OAAC;4BACC,KAAI;4BACJ,KAAI;sEACM;;;;;;;;;;;oBAKb,UAAU,GAAG,CAAC,CAAC,MAAM;wBACpB,IAAI,IAAI,gBAAgB,IAAI,aAAa;wBACzC,IAAI,IAAI,gBAAgB,IAAI,aAAa;wBAEzC,OAAQ,KAAK,KAAK;4BAChB,KAAK;gCAAY,KAAK;gCAAQ,OAAO,OAAO;4BAC5C,KAAK;gCAAQ,KAAK;gCAAQ,OAAU,OAAO;4BAC3C,KAAK;gCAAW,KAAK;gCAAQ,OAAO,MAAM;4BAC1C,KAAK;gCAAY,KAAK;gCAAQ,OAAO,MAAM;wBAC7C;wBAEA,qBACE,8OAAC;4BAGC,OAAO;gCACL,MAAM,GAAG,EAAE,EAAE,CAAC;gCACd,KAAK,GAAG,EAAE,EAAE,CAAC;gCACb,WAAW,CAAC,0BAA0B,EAAE,QAAQ,IAAI,WAAW,CAAC;4BAClE;sEALU;;8CAOV,8OAAC;oCACC,SAAS,KAAK,MAAM;oCAEpB,OAAO;wCACL,YAAY;wCACZ,QAAQ;wCACR,WAAW;wCACX,gBAAgB;oCAClB;8EANU;8CAQV,cAAA,8OAAC;wCAAI,KAAK,KAAK,IAAI;wCAAE,KAAK,KAAK,KAAK;kFAAY;;;;;;;;;;;8CAElD,8OAAC;8EAAe;8CAAkE,KAAK,KAAK;;;;;;;2BApBvF;;;;;oBAuBX;;;;;;;0BAKJ,8OAAC;gBACC,aAAa;gBACb,cAAc;gBACd,SAAS;gBAIT,OAAO;oBACL,QAAQ;oBACR,WAAW,SACP,sEACA;oBACJ,YAAY;oBACZ,aAAa;gBACf;0DAVW,CAAC,2JAA2J,EACrK,SAAS,4BAA4B,aACrC;0BAUF,cAAA,8OAAC;oBACC,KAAI;oBACJ,KAAI;oBAEJ,OAAO;wBAAE,QAAQ,SAAS,oBAAoB;oBAAgB;8DADpD;;;;;;;;;;;;;;;;;;;;;AAapB"}}, {"offset": {"line": 1532, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1538, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/NotificationSystem.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, createContext, useContext, ReactNode } from 'react';\n\nexport interface Notification {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  title: string;\n  message: string;\n  timestamp: Date;\n  read: boolean;\n  actionUrl?: string;\n  actionText?: string;\n  icon?: string;\n  category?: 'ad' | 'payment' | 'system' | 'user' | 'general';\n}\n\ninterface NotificationContextType {\n  notifications: Notification[];\n  unreadCount: number;\n  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;\n  markAsRead: (id: string) => void;\n  markAllAsRead: () => void;\n  removeNotification: (id: string) => void;\n  clearAll: () => void;\n}\n\nconst NotificationContext = createContext<NotificationContextType | undefined>(undefined);\n\nexport const useNotifications = () => {\n  const context = useContext(NotificationContext);\n  if (!context) {\n    throw new Error('useNotifications must be used within a NotificationProvider');\n  }\n  return context;\n};\n\ninterface NotificationProviderProps {\n  children: ReactNode;\n}\n\nexport const NotificationProvider = ({ children }: NotificationProviderProps) => {\n  const [notifications, setNotifications] = useState<Notification[]>([]);\n  const [isClient, setIsClient] = useState(false);\n\n  // تحديد أننا في العميل\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  // تحميل الإشعارات من localStorage عند بدء التطبيق\n  useEffect(() => {\n    if (!isClient) return;\n\n    const savedNotifications = localStorage.getItem('notifications');\n    if (savedNotifications) {\n      try {\n        const parsed = JSON.parse(savedNotifications);\n        // التأكد من أن parsed هو مصفوفة\n        if (Array.isArray(parsed)) {\n          setNotifications(parsed.map((n: any) => ({\n            ...n,\n            timestamp: new Date(n.timestamp)\n          })));\n        } else {\n          // إذا لم يكن مصفوفة، قم بمسح البيانات المعطوبة\n          localStorage.removeItem('notifications');\n          setNotifications([]);\n        }\n      } catch (error) {\n        console.error('Error loading notifications:', error);\n        // مسح البيانات المعطوبة\n        localStorage.removeItem('notifications');\n        setNotifications([]);\n      }\n    }\n  }, [isClient]);\n\n  // حفظ الإشعارات في localStorage عند تغييرها\n  useEffect(() => {\n    if (!isClient) return;\n    localStorage.setItem('notifications', JSON.stringify(notifications));\n  }, [notifications, isClient]);\n\n  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {\n    const newNotification: Notification = {\n      ...notification,\n      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n      timestamp: new Date(),\n      read: false,\n    };\n\n    setNotifications(prev => [newNotification, ...prev]);\n\n    // إزالة الإشعارات القديمة (أكثر من 50 إشعار)\n    setNotifications(prev => prev.slice(0, 50));\n  };\n\n  const markAsRead = (id: string) => {\n    setNotifications(prev =>\n      prev.map(notification =>\n        notification.id === id ? { ...notification, read: true } : notification\n      )\n    );\n  };\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(notification => ({ ...notification, read: true }))\n    );\n  };\n\n  const removeNotification = (id: string) => {\n    setNotifications(prev => prev.filter(notification => notification.id !== id));\n  };\n\n  const clearAll = () => {\n    setNotifications([]);\n  };\n\n  const unreadCount = notifications.filter(n => !n.read).length;\n\n  return (\n    <NotificationContext.Provider\n      value={{\n        notifications,\n        unreadCount,\n        addNotification,\n        markAsRead,\n        markAllAsRead,\n        removeNotification,\n        clearAll,\n      }}\n    >\n      {children}\n    </NotificationContext.Provider>\n  );\n};\n\n// مكون عرض الإشعار الفردي\ninterface NotificationItemProps {\n  notification: Notification;\n  onMarkAsRead: (id: string) => void;\n  onRemove: (id: string) => void;\n}\n\nexport const NotificationItem = ({ notification, onMarkAsRead, onRemove }: NotificationItemProps) => {\n  const getTypeStyles = () => {\n    switch (notification.type) {\n      case 'success':\n        return 'bg-gradient-to-br from-green-50/80 via-green-100/60 to-green-50/80 backdrop-blur-sm border border-green-200/50 text-green-800 shadow-lg';\n      case 'error':\n        return 'bg-gradient-to-br from-red-50/80 via-red-100/60 to-red-50/80 backdrop-blur-sm border border-red-200/50 text-red-800 shadow-lg';\n      case 'warning':\n        return 'bg-gradient-to-br from-yellow-50/80 via-yellow-100/60 to-yellow-50/80 backdrop-blur-sm border border-yellow-200/50 text-yellow-800 shadow-lg';\n      case 'info':\n        return 'bg-gradient-to-br from-blue-50/80 via-blue-100/60 to-blue-50/80 backdrop-blur-sm border border-blue-200/50 text-blue-800 shadow-lg';\n      default:\n        return 'bg-gradient-to-br from-gray-50/80 via-gray-100/60 to-gray-50/80 backdrop-blur-sm border border-gray-200/50 text-gray-800 shadow-lg';\n    }\n  };\n\n  const getTypeIcon = () => {\n    if (notification.icon) return notification.icon;\n\n    switch (notification.type) {\n      case 'success':\n        return '✅';\n      case 'error':\n        return '❌';\n      case 'warning':\n        return '⚠️';\n      case 'info':\n        return 'ℹ️';\n      default:\n        return '📢';\n    }\n  };\n\n  const getIconGlow = () => {\n    switch (notification.type) {\n      case 'success':\n        return 'drop-shadow-[0_0_8px_rgba(34,197,94,0.6)]';\n      case 'error':\n        return 'drop-shadow-[0_0_8px_rgba(239,68,68,0.6)]';\n      case 'warning':\n        return 'drop-shadow-[0_0_8px_rgba(245,158,11,0.6)]';\n      case 'info':\n        return 'drop-shadow-[0_0_8px_rgba(59,130,246,0.6)]';\n      default:\n        return 'drop-shadow-[0_0_8px_rgba(107,114,128,0.6)]';\n    }\n  };\n\n  const formatTime = (date: Date) => {\n    const now = new Date();\n    const diff = now.getTime() - date.getTime();\n    const minutes = Math.floor(diff / 60000);\n    const hours = Math.floor(diff / 3600000);\n    const days = Math.floor(diff / 86400000);\n\n    if (minutes < 1) return 'الآن';\n    if (minutes < 60) return `منذ ${minutes} دقيقة`;\n    if (hours < 24) return `منذ ${hours} ساعة`;\n    if (days < 7) return `منذ ${days} يوم`;\n    return date.toLocaleDateString('ar-SA');\n  };\n\n  return (\n    <div\n      className={`p-4 rounded-xl transition-all duration-300 hover:scale-[1.02] ${getTypeStyles()} ${\n        !notification.read ? 'ring-2 ring-primary-300/50 shadow-xl' : 'shadow-md'\n      }`}\n    >\n      <div className=\"flex items-start gap-3\">\n        <div className={`text-2xl flex-shrink-0 ${getIconGlow()} opacity-90`}>{getTypeIcon()}</div>\n\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-start justify-between gap-2\">\n            <h4 className=\"font-semibold text-sm\">{notification.title}</h4>\n            <div className=\"flex items-center gap-2 flex-shrink-0\">\n              <span className=\"text-xs opacity-70\">{formatTime(notification.timestamp)}</span>\n              <button\n                onClick={() => onRemove(notification.id)}\n                className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n                title=\"حذف الإشعار\"\n              >\n                ×\n              </button>\n            </div>\n          </div>\n\n          <p className=\"text-sm mt-1 opacity-90\">{notification.message}</p>\n\n          <div className=\"flex items-center gap-3 mt-3\">\n            {!notification.read && (\n              <button\n                onClick={() => onMarkAsRead(notification.id)}\n                className=\"text-xs bg-white/70 backdrop-blur-sm hover:bg-white/90 px-3 py-1.5 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md border border-white/30\"\n              >\n                تم القراءة\n              </button>\n            )}\n\n            {notification.actionUrl && notification.actionText && (\n              <a\n                href={notification.actionUrl}\n                className=\"text-xs bg-white/70 backdrop-blur-sm hover:bg-white/90 px-3 py-1.5 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md border border-white/30\"\n              >\n                {notification.actionText}\n              </a>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAEA;AAFA;;;AA2BA,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAuC;AAExE,MAAM,mBAAmB;IAC9B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,MAAM,uBAAuB,CAAC,EAAE,QAAQ,EAA6B;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QAEf,MAAM,qBAAqB,aAAa,OAAO,CAAC;QAChD,IAAI,oBAAoB;YACtB,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,gCAAgC;gBAChC,IAAI,MAAM,OAAO,CAAC,SAAS;oBACzB,iBAAiB,OAAO,GAAG,CAAC,CAAC,IAAW,CAAC;4BACvC,GAAG,CAAC;4BACJ,WAAW,IAAI,KAAK,EAAE,SAAS;wBACjC,CAAC;gBACH,OAAO;oBACL,+CAA+C;oBAC/C,aAAa,UAAU,CAAC;oBACxB,iBAAiB,EAAE;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,wBAAwB;gBACxB,aAAa,UAAU,CAAC;gBACxB,iBAAiB,EAAE;YACrB;QACF;IACF,GAAG;QAAC;KAAS;IAEb,4CAA4C;IAC5C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QACf,aAAa,OAAO,CAAC,iBAAiB,KAAK,SAAS,CAAC;IACvD,GAAG;QAAC;QAAe;KAAS;IAE5B,MAAM,kBAAkB,CAAC;QACvB,MAAM,kBAAgC;YACpC,GAAG,YAAY;YACf,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACjE,WAAW,IAAI;YACf,MAAM;QACR;QAEA,iBAAiB,CAAA,OAAQ;gBAAC;mBAAoB;aAAK;QAEnD,6CAA6C;QAC7C,iBAAiB,CAAA,OAAQ,KAAK,KAAK,CAAC,GAAG;IACzC;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eACP,aAAa,EAAE,KAAK,KAAK;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,IAAI;IAGjE;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,eAAgB,CAAC;oBAAE,GAAG,YAAY;oBAAE,MAAM;gBAAK,CAAC;IAE7D;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,eAAgB,aAAa,EAAE,KAAK;IAC3E;IAEA,MAAM,WAAW;QACf,iBAAiB,EAAE;IACrB;IAEA,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;IAE7D,qBACE,8OAAC,oBAAoB,QAAQ;QAC3B,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AASO,MAAM,mBAAmB,CAAC,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EAAyB;IAC9F,MAAM,gBAAgB;QACpB,OAAQ,aAAa,IAAI;YACvB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,aAAa,IAAI,EAAE,OAAO,aAAa,IAAI;QAE/C,OAAQ,aAAa,IAAI;YACvB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc;QAClB,OAAQ,aAAa,IAAI;YACvB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,OAAO,KAAK,KAAK,OAAO;QACzC,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO;QAClC,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO;QAChC,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO;QAE/B,IAAI,UAAU,GAAG,OAAO;QACxB,IAAI,UAAU,IAAI,OAAO,CAAC,IAAI,EAAE,QAAQ,MAAM,CAAC;QAC/C,IAAI,QAAQ,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,KAAK,CAAC;QAC1C,IAAI,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,KAAK,IAAI,CAAC;QACtC,OAAO,KAAK,kBAAkB,CAAC;IACjC;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,8DAA8D,EAAE,gBAAgB,CAAC,EAC3F,CAAC,aAAa,IAAI,GAAG,yCAAyC,aAC9D;kBAEF,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAW,CAAC,uBAAuB,EAAE,cAAc,WAAW,CAAC;8BAAG;;;;;;8BAEvE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyB,aAAa,KAAK;;;;;;8CACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB,WAAW,aAAa,SAAS;;;;;;sDACvE,8OAAC;4CACC,SAAS,IAAM,SAAS,aAAa,EAAE;4CACvC,WAAU;4CACV,OAAM;sDACP;;;;;;;;;;;;;;;;;;sCAML,8OAAC;4BAAE,WAAU;sCAA2B,aAAa,OAAO;;;;;;sCAE5D,8OAAC;4BAAI,WAAU;;gCACZ,CAAC,aAAa,IAAI,kBACjB,8OAAC;oCACC,SAAS,IAAM,aAAa,aAAa,EAAE;oCAC3C,WAAU;8CACX;;;;;;gCAKF,aAAa,SAAS,IAAI,aAAa,UAAU,kBAChD,8OAAC;oCACC,MAAM,aAAa,SAAS;oCAC5B,WAAU;8CAET,aAAa,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxC"}}, {"offset": {"line": 1818, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1824, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/AdvancedNotificationSystem.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, createContext, useContext, ReactNode } from 'react';\n\nexport interface AdvancedNotification {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info' | 'welcome' | 'payment' | 'message' | 'favorite' | 'ad_posted' | 'search_alert' | 'logout';\n  title: string;\n  message: string;\n  timestamp: Date;\n  isRead: boolean;\n  priority: 'low' | 'medium' | 'high' | 'urgent';\n  category: 'system' | 'user_action' | 'social' | 'commerce' | 'security';\n  actionUrl?: string;\n  actionText?: string;\n  autoHide?: boolean;\n  duration?: number;\n  userId?: string;\n  relatedData?: any;\n  icon?: string;\n}\n\ninterface AdvancedNotificationContextType {\n  notifications: AdvancedNotification[];\n  addNotification: (notification: Omit<AdvancedNotification, 'id' | 'timestamp' | 'isRead'>) => string;\n  removeNotification: (id: string) => void;\n  markAsRead: (id: string) => void;\n  markAllAsRead: () => void;\n  clearAll: () => void;\n  getUnreadCount: () => number;\n  getNotificationsByCategory: (category: string) => AdvancedNotification[];\n}\n\nconst AdvancedNotificationContext = createContext<AdvancedNotificationContextType | undefined>(undefined);\n\nexport const useAdvancedNotifications = () => {\n  const context = useContext(AdvancedNotificationContext);\n  if (!context) {\n    throw new Error('useAdvancedNotifications must be used within an AdvancedNotificationProvider');\n  }\n  return context;\n};\n\ninterface AdvancedNotificationProviderProps {\n  children: ReactNode;\n}\n\nexport function AdvancedNotificationProvider({ children }: AdvancedNotificationProviderProps) {\n  const [notifications, setNotifications] = useState<AdvancedNotification[]>([]);\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  // تحميل الإشعارات المحفوظة\n  useEffect(() => {\n    if (!isClient) return;\n\n    const savedNotifications = localStorage.getItem('advanced_notifications');\n    if (savedNotifications) {\n      try {\n        const parsed = JSON.parse(savedNotifications);\n        // التأكد من أن parsed هو مصفوفة\n        if (Array.isArray(parsed)) {\n          setNotifications(parsed.map((n: any) => ({\n            ...n,\n            timestamp: new Date(n.timestamp)\n          })));\n        } else {\n          // إذا لم يكن مصفوفة، قم بمسح البيانات المعطوبة\n          localStorage.removeItem('advanced_notifications');\n          setNotifications([]);\n        }\n      } catch (error) {\n        console.error('Error loading notifications:', error);\n        // مسح البيانات المعطوبة\n        localStorage.removeItem('advanced_notifications');\n        setNotifications([]);\n      }\n    }\n  }, [isClient]);\n\n  // حفظ الإشعارات\n  useEffect(() => {\n    if (!isClient) return;\n    localStorage.setItem('advanced_notifications', JSON.stringify(notifications));\n  }, [notifications, isClient]);\n\n  const addNotification = (notificationData: Omit<AdvancedNotification, 'id' | 'timestamp' | 'isRead'>): string => {\n    const newNotification: AdvancedNotification = {\n      ...notificationData,\n      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n      timestamp: new Date(),\n      isRead: false\n    };\n\n    setNotifications(prev => [newNotification, ...prev.slice(0, 99)]); // الاحتفاظ بآخر 100 إشعار\n\n    // إزالة تلقائية للإشعارات المؤقتة\n    if (notificationData.autoHide !== false) {\n      const duration = notificationData.duration || getDurationByType(notificationData.type);\n      setTimeout(() => {\n        removeNotification(newNotification.id);\n      }, duration);\n    }\n\n    // إشعار صوتي للإشعارات المهمة\n    if (notificationData.priority === 'high' || notificationData.priority === 'urgent') {\n      playNotificationSound(notificationData.type);\n    }\n\n    return newNotification.id;\n  };\n\n  const removeNotification = (id: string) => {\n    setNotifications(prev => prev.filter(n => n.id !== id));\n  };\n\n  const markAsRead = (id: string) => {\n    setNotifications(prev =>\n      prev.map(n => n.id === id ? { ...n, isRead: true } : n)\n    );\n  };\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(n => ({ ...n, isRead: true }))\n    );\n  };\n\n  const clearAll = () => {\n    setNotifications([]);\n  };\n\n  const getUnreadCount = () => {\n    return notifications.filter(n => !n.isRead).length;\n  };\n\n  const getNotificationsByCategory = (category: string) => {\n    return notifications.filter(n => n.category === category);\n  };\n\n  const getDurationByType = (type: string): number => {\n    switch (type) {\n      case 'success': return 4000;\n      case 'error': return 8000;\n      case 'warning': return 6000;\n      case 'welcome': return 10000;\n      case 'payment': return 6000;\n      case 'logout': return 3000;\n      default: return 5000;\n    }\n  };\n\n  const playNotificationSound = (type: string) => {\n    if (!isClient) return;\n    \n    try {\n      const audio = new Audio();\n      switch (type) {\n        case 'success':\n        case 'payment':\n          audio.src = '/sounds/success.mp3';\n          break;\n        case 'error':\n          audio.src = '/sounds/error.mp3';\n          break;\n        case 'warning':\n          audio.src = '/sounds/warning.mp3';\n          break;\n        case 'message':\n          audio.src = '/sounds/message.mp3';\n          break;\n        default:\n          audio.src = '/sounds/notification.mp3';\n      }\n      audio.volume = 0.3;\n      audio.play().catch(() => {\n        // تجاهل الأخطاء الصوتية\n      });\n    } catch (error) {\n      // تجاهل الأخطاء الصوتية\n    }\n  };\n\n  const value: AdvancedNotificationContextType = {\n    notifications,\n    addNotification,\n    removeNotification,\n    markAsRead,\n    markAllAsRead,\n    clearAll,\n    getUnreadCount,\n    getNotificationsByCategory\n  };\n\n  return (\n    <AdvancedNotificationContext.Provider value={value}>\n      {children}\n    </AdvancedNotificationContext.Provider>\n  );\n}\n\n// دوال مساعدة لإنشاء إشعارات محددة\nexport const createWelcomeNotification = (userName: string) => ({\n  type: 'welcome' as const,\n  title: `مرحباً بك ${userName}! 🎉`,\n  message: 'نحن سعداء لانضمامك إلى منصة من المالك. استكشف الميزات الجديدة وابدأ رحلتك معنا!',\n  priority: 'high' as const,\n  category: 'system' as const,\n  actionUrl: '/profile/setup',\n  actionText: 'إكمال الملف الشخصي',\n  autoHide: false,\n  icon: '🎉'\n});\n\nexport const createPaymentSuccessNotification = (amount: string, service: string) => ({\n  type: 'payment' as const,\n  title: 'تم الدفع بنجاح! ✅',\n  message: `تم دفع ${amount} ل.س لخدمة ${service} بنجاح. ستتلقى إيصالاً عبر البريد الإلكتروني.`,\n  priority: 'high' as const,\n  category: 'commerce' as const,\n  actionUrl: '/payments/history',\n  actionText: 'عرض تاريخ المدفوعات',\n  icon: '💳'\n});\n\nexport const createAdPostedNotification = (adTitle: string) => ({\n  type: 'ad_posted' as const,\n  title: 'تم نشر إعلانك! 🚀',\n  message: `تم نشر إعلان \"${adTitle}\" بنجاح. سيظهر للمستخدمين خلال دقائق قليلة.`,\n  priority: 'medium' as const,\n  category: 'user_action' as const,\n  actionUrl: '/my-ads',\n  actionText: 'عرض إعلاناتي',\n  icon: '📢'\n});\n\nexport const createMessageNotification = (senderName: string, preview: string) => ({\n  type: 'message' as const,\n  title: `رسالة جديدة من ${senderName} 💬`,\n  message: preview.length > 50 ? preview.substring(0, 50) + '...' : preview,\n  priority: 'medium' as const,\n  category: 'social' as const,\n  actionUrl: '/messages',\n  actionText: 'عرض الرسائل',\n  icon: '💬'\n});\n\nexport const createFavoriteNotification = (itemTitle: string, type: 'product' | 'seller') => ({\n  type: 'favorite' as const,\n  title: type === 'product' ? 'تمت الإضافة للمفضلة! ❤️' : 'تمت متابعة البائع! 👤',\n  message: type === 'product' \n    ? `تم إضافة \"${itemTitle}\" إلى قائمة المفضلة`\n    : `تم إضافة \"${itemTitle}\" إلى قائمة البائعين المتابعين`,\n  priority: 'low' as const,\n  category: 'user_action' as const,\n  actionUrl: type === 'product' ? '/favorites' : '/following',\n  icon: type === 'product' ? '❤️' : '👤'\n});\n\nexport const createSearchAlertNotification = (searchTerm: string, newItemsCount: number) => ({\n  type: 'search_alert' as const,\n  title: 'عناصر جديدة تطابق بحثك! 🔍',\n  message: `تم العثور على ${newItemsCount} عنصر جديد يطابق بحثك عن \"${searchTerm}\"`,\n  priority: 'medium' as const,\n  category: 'system' as const,\n  actionUrl: `/search?q=${encodeURIComponent(searchTerm)}`,\n  actionText: 'عرض النتائج',\n  icon: '🔍'\n});\n\nexport const createLogoutNotification = () => ({\n  type: 'logout' as const,\n  title: 'تم تسجيل الخروج بنجاح 👋',\n  message: 'شكراً لاستخدام منصة من المالك. نراك قريباً!',\n  priority: 'medium' as const,\n  category: 'security' as const,\n  icon: '👋'\n});\n\nexport const createErrorNotification = (title: string, message: string) => ({\n  type: 'error' as const,\n  title,\n  message,\n  priority: 'high' as const,\n  category: 'system' as const,\n  icon: '❌'\n});\n\nexport const createSuccessNotification = (title: string, message: string) => ({\n  type: 'success' as const,\n  title,\n  message,\n  priority: 'medium' as const,\n  category: 'system' as const,\n  icon: '✅'\n});\n\nexport const createWarningNotification = (title: string, message: string) => ({\n  type: 'warning' as const,\n  title,\n  message,\n  priority: 'medium' as const,\n  category: 'system' as const,\n  icon: '⚠️'\n});\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAEA;AAFA;;;AAiCA,MAAM,4CAA8B,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+C;AAExF,MAAM,2BAA2B;IACtC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,6BAA6B,EAAE,QAAQ,EAAqC;IAC1F,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IAC7E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QAEf,MAAM,qBAAqB,aAAa,OAAO,CAAC;QAChD,IAAI,oBAAoB;YACtB,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,gCAAgC;gBAChC,IAAI,MAAM,OAAO,CAAC,SAAS;oBACzB,iBAAiB,OAAO,GAAG,CAAC,CAAC,IAAW,CAAC;4BACvC,GAAG,CAAC;4BACJ,WAAW,IAAI,KAAK,EAAE,SAAS;wBACjC,CAAC;gBACH,OAAO;oBACL,+CAA+C;oBAC/C,aAAa,UAAU,CAAC;oBACxB,iBAAiB,EAAE;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,wBAAwB;gBACxB,aAAa,UAAU,CAAC;gBACxB,iBAAiB,EAAE;YACrB;QACF;IACF,GAAG;QAAC;KAAS;IAEb,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QACf,aAAa,OAAO,CAAC,0BAA0B,KAAK,SAAS,CAAC;IAChE,GAAG;QAAC;QAAe;KAAS;IAE5B,MAAM,kBAAkB,CAAC;QACvB,MAAM,kBAAwC;YAC5C,GAAG,gBAAgB;YACnB,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACjE,WAAW,IAAI;YACf,QAAQ;QACV;QAEA,iBAAiB,CAAA,OAAQ;gBAAC;mBAAoB,KAAK,KAAK,CAAC,GAAG;aAAI,GAAG,0BAA0B;QAE7F,kCAAkC;QAClC,IAAI,iBAAiB,QAAQ,KAAK,OAAO;YACvC,MAAM,WAAW,iBAAiB,QAAQ,IAAI,kBAAkB,iBAAiB,IAAI;YACrF,WAAW;gBACT,mBAAmB,gBAAgB,EAAE;YACvC,GAAG;QACL;QAEA,8BAA8B;QAC9B,IAAI,iBAAiB,QAAQ,KAAK,UAAU,iBAAiB,QAAQ,KAAK,UAAU;YAClF,sBAAsB,iBAAiB,IAAI;QAC7C;QAEA,OAAO,gBAAgB,EAAE;IAC3B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrD;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK;oBAAE,GAAG,CAAC;oBAAE,QAAQ;gBAAK,IAAI;IAEzD;IAEA,MAAM,gBAAgB;QACpB,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC;oBAAE,GAAG,CAAC;oBAAE,QAAQ;gBAAK,CAAC;IAEzC;IAEA,MAAM,WAAW;QACf,iBAAiB,EAAE;IACrB;IAEA,MAAM,iBAAiB;QACrB,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM,EAAE,MAAM;IACpD;IAEA,MAAM,6BAA6B,CAAC;QAClC,OAAO,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IAClD;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,IAAI,CAAC,UAAU;QAEf,IAAI;YACF,MAAM,QAAQ,IAAI;YAClB,OAAQ;gBACN,KAAK;gBACL,KAAK;oBACH,MAAM,GAAG,GAAG;oBACZ;gBACF,KAAK;oBACH,MAAM,GAAG,GAAG;oBACZ;gBACF,KAAK;oBACH,MAAM,GAAG,GAAG;oBACZ;gBACF,KAAK;oBACH,MAAM,GAAG,GAAG;oBACZ;gBACF;oBACE,MAAM,GAAG,GAAG;YAChB;YACA,MAAM,MAAM,GAAG;YACf,MAAM,IAAI,GAAG,KAAK,CAAC;YACjB,wBAAwB;YAC1B;QACF,EAAE,OAAO,OAAO;QACd,wBAAwB;QAC1B;IACF;IAEA,MAAM,QAAyC;QAC7C;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,4BAA4B,QAAQ;QAAC,OAAO;kBAC1C;;;;;;AAGP;AAGO,MAAM,4BAA4B,CAAC,WAAqB,CAAC;QAC9D,MAAM;QACN,OAAO,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC;QAClC,SAAS;QACT,UAAU;QACV,UAAU;QACV,WAAW;QACX,YAAY;QACZ,UAAU;QACV,MAAM;IACR,CAAC;AAEM,MAAM,mCAAmC,CAAC,QAAgB,UAAoB,CAAC;QACpF,MAAM;QACN,OAAO;QACP,SAAS,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE,QAAQ,6CAA6C,CAAC;QAC7F,UAAU;QACV,UAAU;QACV,WAAW;QACX,YAAY;QACZ,MAAM;IACR,CAAC;AAEM,MAAM,6BAA6B,CAAC,UAAoB,CAAC;QAC9D,MAAM;QACN,OAAO;QACP,SAAS,CAAC,cAAc,EAAE,QAAQ,2CAA2C,CAAC;QAC9E,UAAU;QACV,UAAU;QACV,WAAW;QACX,YAAY;QACZ,MAAM;IACR,CAAC;AAEM,MAAM,4BAA4B,CAAC,YAAoB,UAAoB,CAAC;QACjF,MAAM;QACN,OAAO,CAAC,eAAe,EAAE,WAAW,GAAG,CAAC;QACxC,SAAS,QAAQ,MAAM,GAAG,KAAK,QAAQ,SAAS,CAAC,GAAG,MAAM,QAAQ;QAClE,UAAU;QACV,UAAU;QACV,WAAW;QACX,YAAY;QACZ,MAAM;IACR,CAAC;AAEM,MAAM,6BAA6B,CAAC,WAAmB,OAA+B,CAAC;QAC5F,MAAM;QACN,OAAO,SAAS,YAAY,4BAA4B;QACxD,SAAS,SAAS,YACd,CAAC,UAAU,EAAE,UAAU,mBAAmB,CAAC,GAC3C,CAAC,UAAU,EAAE,UAAU,8BAA8B,CAAC;QAC1D,UAAU;QACV,UAAU;QACV,WAAW,SAAS,YAAY,eAAe;QAC/C,MAAM,SAAS,YAAY,OAAO;IACpC,CAAC;AAEM,MAAM,gCAAgC,CAAC,YAAoB,gBAA0B,CAAC;QAC3F,MAAM;QACN,OAAO;QACP,SAAS,CAAC,cAAc,EAAE,cAAc,0BAA0B,EAAE,WAAW,CAAC,CAAC;QACjF,UAAU;QACV,UAAU;QACV,WAAW,CAAC,UAAU,EAAE,mBAAmB,aAAa;QACxD,YAAY;QACZ,MAAM;IACR,CAAC;AAEM,MAAM,2BAA2B,IAAM,CAAC;QAC7C,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;QACV,MAAM;IACR,CAAC;AAEM,MAAM,0BAA0B,CAAC,OAAe,UAAoB,CAAC;QAC1E,MAAM;QACN;QACA;QACA,UAAU;QACV,UAAU;QACV,MAAM;IACR,CAAC;AAEM,MAAM,4BAA4B,CAAC,OAAe,UAAoB,CAAC;QAC5E,MAAM;QACN;QACA;QACA,UAAU;QACV,UAAU;QACV,MAAM;IACR,CAAC;AAEM,MAAM,4BAA4B,CAAC,OAAe,UAAoB,CAAC;QAC5E,MAAM;QACN;QACA;QACA,UAAU;QACV,UAAU;QACV,MAAM;IACR,CAAC"}}, {"offset": {"line": 2099, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2105, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/ToastNotification.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { AdvancedNotification } from './AdvancedNotificationSystem';\n\ninterface ToastNotificationProps {\n  notification: AdvancedNotification;\n  onClose: () => void;\n  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';\n}\n\nexport default function ToastNotification({ \n  notification, \n  onClose, \n  position = 'top-right' \n}: ToastNotificationProps) {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isLeaving, setIsLeaving] = useState(false);\n\n  useEffect(() => {\n    // إظهار التوست مع تأخير بسيط للحصول على تأثير الانزلاق\n    const showTimer = setTimeout(() => setIsVisible(true), 100);\n    \n    // إخفاء التوست تلقائياً إذا كان مؤقتاً\n    let hideTimer: NodeJS.Timeout;\n    if (notification.autoHide !== false) {\n      const duration = notification.duration || getDurationByType(notification.type);\n      hideTimer = setTimeout(() => {\n        handleClose();\n      }, duration);\n    }\n\n    return () => {\n      clearTimeout(showTimer);\n      if (hideTimer) clearTimeout(hideTimer);\n    };\n  }, [notification]);\n\n  const handleClose = () => {\n    setIsLeaving(true);\n    setTimeout(() => {\n      onClose();\n    }, 300); // مدة الانيميشن\n  };\n\n  const getDurationByType = (type: string): number => {\n    switch (type) {\n      case 'success': return 4000;\n      case 'error': return 8000;\n      case 'warning': return 6000;\n      case 'welcome': return 10000;\n      case 'payment': return 6000;\n      case 'logout': return 3000;\n      default: return 5000;\n    }\n  };\n\n  const getToastStyles = () => {\n    const baseStyles = `\n      fixed z-50 max-w-sm w-full transition-all duration-300 ease-in-out transform\n      ${isVisible && !isLeaving ? 'translate-x-0 opacity-100' : ''}\n      ${!isVisible || isLeaving ? getHiddenTransform() : ''}\n    `;\n\n    const positionStyles = {\n      'top-right': 'top-4 right-4',\n      'top-left': 'top-4 left-4',\n      'bottom-right': 'bottom-4 right-4',\n      'bottom-left': 'bottom-4 left-4',\n      'top-center': 'top-4 left-1/2 transform -translate-x-1/2',\n      'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2'\n    };\n\n    return `${baseStyles} ${positionStyles[position]}`;\n  };\n\n  const getHiddenTransform = () => {\n    switch (position) {\n      case 'top-right':\n      case 'bottom-right':\n        return 'translate-x-full opacity-0';\n      case 'top-left':\n      case 'bottom-left':\n        return '-translate-x-full opacity-0';\n      case 'top-center':\n        return '-translate-y-full opacity-0';\n      case 'bottom-center':\n        return 'translate-y-full opacity-0';\n      default:\n        return 'translate-x-full opacity-0';\n    }\n  };\n\n  const getTypeStyles = () => {\n    switch (notification.type) {\n      case 'success':\n      case 'payment':\n      case 'ad_posted':\n      case 'welcome':\n        return {\n          container: 'bg-green-50/95 backdrop-blur-md border border-green-200/50 shadow-lg shadow-green-500/20',\n          icon: 'text-green-600',\n          title: 'text-green-800',\n          message: 'text-green-700',\n          button: 'text-green-600 hover:text-green-800 hover:bg-green-100',\n          glow: 'shadow-green-400/30',\n          animation: 'animate-pulse'\n        };\n      \n      case 'error':\n        return {\n          container: 'bg-red-50/95 backdrop-blur-md border border-red-200/50 shadow-lg shadow-red-500/20',\n          icon: 'text-red-600',\n          title: 'text-red-800',\n          message: 'text-red-700',\n          button: 'text-red-600 hover:text-red-800 hover:bg-red-100',\n          glow: 'shadow-red-400/30',\n          animation: 'animate-bounce'\n        };\n      \n      case 'warning':\n        return {\n          container: 'bg-orange-50/95 backdrop-blur-md border border-orange-200/50 shadow-lg shadow-orange-500/20',\n          icon: 'text-orange-600',\n          title: 'text-orange-800',\n          message: 'text-orange-700',\n          button: 'text-orange-600 hover:text-orange-800 hover:bg-orange-100',\n          glow: 'shadow-orange-400/30',\n          animation: 'animate-pulse'\n        };\n      \n      case 'info':\n      case 'message':\n      case 'search_alert':\n        return {\n          container: 'bg-blue-50/95 backdrop-blur-md border border-blue-200/50 shadow-lg shadow-blue-500/20',\n          icon: 'text-blue-600',\n          title: 'text-blue-800',\n          message: 'text-blue-700',\n          button: 'text-blue-600 hover:text-blue-800 hover:bg-blue-100',\n          glow: 'shadow-blue-400/30',\n          animation: 'animate-pulse'\n        };\n      \n      case 'favorite':\n        return {\n          container: 'bg-pink-50/95 backdrop-blur-md border border-pink-200/50 shadow-lg shadow-pink-500/20',\n          icon: 'text-pink-600',\n          title: 'text-pink-800',\n          message: 'text-pink-700',\n          button: 'text-pink-600 hover:text-pink-800 hover:bg-pink-100',\n          glow: 'shadow-pink-400/30',\n          animation: 'animate-pulse'\n        };\n      \n      case 'logout':\n        return {\n          container: 'bg-gray-50/95 backdrop-blur-md border border-gray-200/50 shadow-lg shadow-gray-500/20',\n          icon: 'text-gray-600',\n          title: 'text-gray-800',\n          message: 'text-gray-700',\n          button: 'text-gray-600 hover:text-gray-800 hover:bg-gray-100',\n          glow: 'shadow-gray-400/30',\n          animation: 'animate-pulse'\n        };\n      \n      default:\n        return {\n          container: 'bg-white/95 backdrop-blur-md border border-gray-200/50 shadow-lg',\n          icon: 'text-gray-600',\n          title: 'text-gray-800',\n          message: 'text-gray-700',\n          button: 'text-gray-600 hover:text-gray-800 hover:bg-gray-100',\n          glow: 'shadow-gray-400/30',\n          animation: ''\n        };\n    }\n  };\n\n  const styles = getTypeStyles();\n\n  const getPriorityIndicator = () => {\n    if (notification.priority === 'urgent') {\n      return (\n        <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-ping\"></div>\n      );\n    }\n    if (notification.priority === 'high') {\n      return (\n        <div className=\"absolute -top-1 -right-1 w-2 h-2 bg-yellow-500 rounded-full animate-pulse\"></div>\n      );\n    }\n    return null;\n  };\n\n  return (\n    <div className={getToastStyles()}>\n      <div className={`\n        relative rounded-xl p-4 ${styles.container} ${styles.glow}\n        hover:shadow-xl transition-all duration-300\n        border-l-4 ${notification.type === 'success' || notification.type === 'payment' || notification.type === 'welcome' ? 'border-l-green-500' : \n                     notification.type === 'error' ? 'border-l-red-500' :\n                     notification.type === 'warning' ? 'border-l-orange-500' : 'border-l-blue-500'}\n      `}>\n        {getPriorityIndicator()}\n        \n        <div className=\"flex items-start gap-3\">\n          {/* الأيقونة */}\n          <div className={`flex-shrink-0 ${styles.icon} ${styles.animation}`}>\n            <span className=\"text-2xl\">\n              {notification.icon || getDefaultIcon(notification.type)}\n            </span>\n          </div>\n\n          {/* المحتوى */}\n          <div className=\"flex-1 min-w-0\">\n            <h4 className={`font-semibold text-sm ${styles.title} mb-1`}>\n              {notification.title}\n            </h4>\n            <p className={`text-sm ${styles.message} leading-relaxed`}>\n              {notification.message}\n            </p>\n            \n            {/* زر الإجراء */}\n            {notification.actionUrl && notification.actionText && (\n              <div className=\"mt-3\">\n                <a\n                  href={notification.actionUrl}\n                  className={`inline-flex items-center text-xs font-medium ${styles.button} \n                    px-3 py-1 rounded-lg transition-colors duration-200`}\n                  onClick={handleClose}\n                >\n                  {notification.actionText}\n                  <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                  </svg>\n                </a>\n              </div>\n            )}\n          </div>\n\n          {/* زر الإغلاق */}\n          <button\n            onClick={handleClose}\n            className={`flex-shrink-0 ${styles.button} p-1 rounded-lg transition-colors duration-200`}\n            aria-label=\"إغلاق الإشعار\"\n          >\n            <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* شريط التقدم للإشعارات المؤقتة */}\n        {notification.autoHide !== false && (\n          <div className=\"absolute bottom-0 left-0 right-0 h-1 bg-gray-200/30 rounded-b-xl overflow-hidden\">\n            <div \n              className={`h-full transition-all ease-linear ${\n                notification.type === 'success' || notification.type === 'payment' || notification.type === 'welcome' ? 'bg-green-500' :\n                notification.type === 'error' ? 'bg-red-500' :\n                notification.type === 'warning' ? 'bg-orange-500' : 'bg-blue-500'\n              }`}\n              style={{\n                width: '100%',\n                animation: `shrink ${getDurationByType(notification.type)}ms linear forwards`\n              }}\n            />\n          </div>\n        )}\n      </div>\n\n      <style jsx>{`\n        @keyframes shrink {\n          from { width: 100%; }\n          to { width: 0%; }\n        }\n      `}</style>\n    </div>\n  );\n}\n\nfunction getDefaultIcon(type: string): string {\n  switch (type) {\n    case 'success': return '✅';\n    case 'error': return '❌';\n    case 'warning': return '⚠️';\n    case 'info': return 'ℹ️';\n    case 'welcome': return '🎉';\n    case 'payment': return '💳';\n    case 'message': return '💬';\n    case 'favorite': return '❤️';\n    case 'ad_posted': return '📢';\n    case 'search_alert': return '🔍';\n    case 'logout': return '👋';\n    default: return '🔔';\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;;AAWe,SAAS,kBAAkB,EACxC,YAAY,EACZ,OAAO,EACP,WAAW,WAAW,EACC;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uDAAuD;QACvD,MAAM,YAAY,WAAW,IAAM,aAAa,OAAO;QAEvD,uCAAuC;QACvC,IAAI;QACJ,IAAI,aAAa,QAAQ,KAAK,OAAO;YACnC,MAAM,WAAW,aAAa,QAAQ,IAAI,kBAAkB,aAAa,IAAI;YAC7E,YAAY,WAAW;gBACrB;YACF,GAAG;QACL;QAEA,OAAO;YACL,aAAa;YACb,IAAI,WAAW,aAAa;QAC9B;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,cAAc;QAClB,aAAa;QACb,WAAW;YACT;QACF,GAAG,MAAM,gBAAgB;IAC3B;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,aAAa,CAAC;;MAElB,EAAE,aAAa,CAAC,YAAY,8BAA8B,GAAG;MAC7D,EAAE,CAAC,aAAa,YAAY,uBAAuB,GAAG;IACxD,CAAC;QAED,MAAM,iBAAiB;YACrB,aAAa;YACb,YAAY;YACZ,gBAAgB;YAChB,eAAe;YACf,cAAc;YACd,iBAAiB;QACnB;QAEA,OAAO,GAAG,WAAW,CAAC,EAAE,cAAc,CAAC,SAAS,EAAE;IACpD;IAEA,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ,aAAa,IAAI;YACvB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,MAAM;oBACN,WAAW;gBACb;YAEF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,MAAM;oBACN,WAAW;gBACb;YAEF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,MAAM;oBACN,WAAW;gBACb;YAEF,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,MAAM;oBACN,WAAW;gBACb;YAEF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,MAAM;oBACN,WAAW;gBACb;YAEF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,MAAM;oBACN,WAAW;gBACb;YAEF;gBACE,OAAO;oBACL,WAAW;oBACX,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,QAAQ;oBACR,MAAM;oBACN,WAAW;gBACb;QACJ;IACF;IAEA,MAAM,SAAS;IAEf,MAAM,uBAAuB;QAC3B,IAAI,aAAa,QAAQ,KAAK,UAAU;YACtC,qBACE,8OAAC;gBAAI,WAAU;;;;;;QAEnB;QACA,IAAI,aAAa,QAAQ,KAAK,QAAQ;YACpC,qBACE,8OAAC;gBAAI,WAAU;;;;;;QAEnB;QACA,OAAO;IACT;IAEA,qBACE,8OAAC;mDAAe;;0BACd,8OAAC;0DAAe,CAAC;gCACS,EAAE,OAAO,SAAS,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;;mBAE/C,EAAE,aAAa,IAAI,KAAK,aAAa,aAAa,IAAI,KAAK,aAAa,aAAa,IAAI,KAAK,YAAY,uBACxG,aAAa,IAAI,KAAK,UAAU,qBAChC,aAAa,IAAI,KAAK,YAAY,wBAAwB,oBAAoB;MAC7F,CAAC;;oBACE;kCAED,8OAAC;kEAAc;;0CAEb,8OAAC;0EAAe,CAAC,cAAc,EAAE,OAAO,IAAI,CAAC,CAAC,EAAE,OAAO,SAAS,EAAE;0CAChE,cAAA,8OAAC;8EAAe;8CACb,aAAa,IAAI,IAAI,eAAe,aAAa,IAAI;;;;;;;;;;;0CAK1D,8OAAC;0EAAc;;kDACb,8OAAC;kFAAc,CAAC,sBAAsB,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC;kDACxD,aAAa,KAAK;;;;;;kDAErB,8OAAC;kFAAa,CAAC,QAAQ,EAAE,OAAO,OAAO,CAAC,gBAAgB,CAAC;kDACtD,aAAa,OAAO;;;;;;oCAItB,aAAa,SAAS,IAAI,aAAa,UAAU,kBAChD,8OAAC;kFAAc;kDACb,cAAA,8OAAC;4CACC,MAAM,aAAa,SAAS;4CAG5B,SAAS;sFAFE,CAAC,6CAA6C,EAAE,OAAO,MAAM,CAAC;uEACpB,CAAC;;gDAGrD,aAAa,UAAU;8DACxB,8OAAC;oDAA6B,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8FAAzD;8DACb,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQ/E,8OAAC;gCACC,SAAS;gCAET,cAAW;0EADA,CAAC,cAAc,EAAE,OAAO,MAAM,CAAC,8CAA8C,CAAC;0CAGzF,cAAA,8OAAC;oCAAwB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8EAApD;8CACb,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;oBAM1E,aAAa,QAAQ,KAAK,uBACzB,8OAAC;kEAAc;kCACb,cAAA,8OAAC;4BAMC,OAAO;gCACL,OAAO;gCACP,WAAW,CAAC,OAAO,EAAE,kBAAkB,aAAa,IAAI,EAAE,kBAAkB,CAAC;4BAC/E;sEARW,CAAC,kCAAkC,EAC5C,aAAa,IAAI,KAAK,aAAa,aAAa,IAAI,KAAK,aAAa,aAAa,IAAI,KAAK,YAAY,iBACxG,aAAa,IAAI,KAAK,UAAU,eAChC,aAAa,IAAI,KAAK,YAAY,kBAAkB,eACpD;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBhB;AAEA,SAAS,eAAe,IAAY;IAClC,OAAQ;QACN,KAAK;YAAW,OAAO;QACvB,KAAK;YAAS,OAAO;QACrB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAQ,OAAO;QACpB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAY,OAAO;QACxB,KAAK;YAAa,OAAO;QACzB,KAAK;YAAgB,OAAO;QAC5B,KAAK;YAAU,OAAO;QACtB;YAAS,OAAO;IAClB;AACF"}}, {"offset": {"line": 2487, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2493, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/ToastManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, createContext, useContext, ReactNode } from 'react';\nimport ToastNotification from './ToastNotification';\nimport { AdvancedNotification } from './AdvancedNotificationSystem';\n\ninterface ToastContextType {\n  showToast: (notification: Omit<AdvancedNotification, 'id' | 'timestamp' | 'isRead'>) => void;\n  showSuccess: (title: string, message: string, actionUrl?: string, actionText?: string) => void;\n  showError: (title: string, message: string) => void;\n  showWarning: (title: string, message: string) => void;\n  showInfo: (title: string, message: string) => void;\n  showWelcome: (userName: string) => void;\n  showPaymentSuccess: (amount: string, service: string) => void;\n  showAdPosted: (adTitle: string) => void;\n  showMessage: (senderName: string, preview: string) => void;\n  showFavorite: (itemTitle: string, type: 'product' | 'seller') => void;\n  showLogout: () => void;\n  clearAllToasts: () => void;\n}\n\nconst ToastContext = createContext<ToastContextType | undefined>(undefined);\n\nexport const useToast = () => {\n  const context = useContext(ToastContext);\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider');\n  }\n  return context;\n};\n\ninterface ToastProviderProps {\n  children: ReactNode;\n  maxToasts?: number;\n  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';\n}\n\nexport function ToastProvider({ \n  children, \n  maxToasts = 5, \n  position = 'top-right' \n}: ToastProviderProps) {\n  const [toasts, setToasts] = useState<AdvancedNotification[]>([]);\n\n  const showToast = (notificationData: Omit<AdvancedNotification, 'id' | 'timestamp' | 'isRead'>) => {\n    const newToast: AdvancedNotification = {\n      ...notificationData,\n      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n      timestamp: new Date(),\n      isRead: false\n    };\n\n    setToasts(prev => {\n      const updated = [newToast, ...prev];\n      // الاحتفاظ بالحد الأقصى من التوست\n      return updated.slice(0, maxToasts);\n    });\n  };\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id));\n  };\n\n  const clearAllToasts = () => {\n    setToasts([]);\n  };\n\n  // دوال مساعدة لأنواع مختلفة من التوست\n  const showSuccess = (title: string, message: string, actionUrl?: string, actionText?: string) => {\n    showToast({\n      type: 'success',\n      title,\n      message,\n      priority: 'medium',\n      category: 'system',\n      actionUrl,\n      actionText,\n      icon: '✅'\n    });\n  };\n\n  const showError = (title: string, message: string) => {\n    showToast({\n      type: 'error',\n      title,\n      message,\n      priority: 'high',\n      category: 'system',\n      autoHide: false, // الأخطاء لا تختفي تلقائياً\n      icon: '❌'\n    });\n  };\n\n  const showWarning = (title: string, message: string) => {\n    showToast({\n      type: 'warning',\n      title,\n      message,\n      priority: 'medium',\n      category: 'system',\n      icon: '⚠️'\n    });\n  };\n\n  const showInfo = (title: string, message: string) => {\n    showToast({\n      type: 'info',\n      title,\n      message,\n      priority: 'low',\n      category: 'system',\n      icon: 'ℹ️'\n    });\n  };\n\n  const showWelcome = (userName: string) => {\n    showToast({\n      type: 'welcome',\n      title: `مرحباً بك ${userName}! 🎉`,\n      message: 'نحن سعداء لانضمامك إلى منصة من المالك. استكشف الميزات الجديدة وابدأ رحلتك معنا!',\n      priority: 'high',\n      category: 'system',\n      actionUrl: '/profile/setup',\n      actionText: 'إكمال الملف الشخصي',\n      autoHide: false,\n      icon: '🎉'\n    });\n  };\n\n  const showPaymentSuccess = (amount: string, service: string) => {\n    showToast({\n      type: 'payment',\n      title: 'تم الدفع بنجاح! ✅',\n      message: `تم دفع ${amount} ل.س لخدمة ${service} بنجاح. ستتلقى إيصالاً عبر البريد الإلكتروني.`,\n      priority: 'high',\n      category: 'commerce',\n      actionUrl: '/payments/history',\n      actionText: 'عرض تاريخ المدفوعات',\n      icon: '💳'\n    });\n  };\n\n  const showAdPosted = (adTitle: string) => {\n    showToast({\n      type: 'ad_posted',\n      title: 'تم نشر إعلانك! 🚀',\n      message: `تم نشر إعلان \"${adTitle}\" بنجاح. سيظهر للمستخدمين خلال دقائق قليلة.`,\n      priority: 'medium',\n      category: 'user_action',\n      actionUrl: '/my-ads',\n      actionText: 'عرض إعلاناتي',\n      icon: '📢'\n    });\n  };\n\n  const showMessage = (senderName: string, preview: string) => {\n    showToast({\n      type: 'message',\n      title: `رسالة جديدة من ${senderName} 💬`,\n      message: preview.length > 50 ? preview.substring(0, 50) + '...' : preview,\n      priority: 'medium',\n      category: 'social',\n      actionUrl: '/messages',\n      actionText: 'عرض الرسائل',\n      icon: '💬'\n    });\n  };\n\n  const showFavorite = (itemTitle: string, type: 'product' | 'seller') => {\n    showToast({\n      type: 'favorite',\n      title: type === 'product' ? 'تمت الإضافة للمفضلة! ❤️' : 'تمت متابعة البائع! 👤',\n      message: type === 'product' \n        ? `تم إضافة \"${itemTitle}\" إلى قائمة المفضلة`\n        : `تم إضافة \"${itemTitle}\" إلى قائمة البائعين المتابعين`,\n      priority: 'low',\n      category: 'user_action',\n      actionUrl: type === 'product' ? '/favorites' : '/following',\n      actionText: type === 'product' ? 'عرض المفضلة' : 'عرض المتابعين',\n      icon: type === 'product' ? '❤️' : '👤'\n    });\n  };\n\n  const showLogout = () => {\n    showToast({\n      type: 'logout',\n      title: 'تم تسجيل الخروج بنجاح 👋',\n      message: 'شكراً لاستخدام منصة من المالك. نراك قريباً!',\n      priority: 'medium',\n      category: 'security',\n      duration: 3000,\n      icon: '👋'\n    });\n  };\n\n  const value: ToastContextType = {\n    showToast,\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo,\n    showWelcome,\n    showPaymentSuccess,\n    showAdPosted,\n    showMessage,\n    showFavorite,\n    showLogout,\n    clearAllToasts\n  };\n\n  return (\n    <ToastContext.Provider value={value}>\n      {children}\n      \n      {/* عرض التوست */}\n      <div className=\"fixed inset-0 pointer-events-none z-50\">\n        <div className={`\n          absolute flex flex-col gap-2\n          ${position === 'top-right' ? 'top-4 right-4' : ''}\n          ${position === 'top-left' ? 'top-4 left-4' : ''}\n          ${position === 'bottom-right' ? 'bottom-4 right-4' : ''}\n          ${position === 'bottom-left' ? 'bottom-4 left-4' : ''}\n          ${position === 'top-center' ? 'top-4 left-1/2 transform -translate-x-1/2' : ''}\n          ${position === 'bottom-center' ? 'bottom-4 left-1/2 transform -translate-x-1/2' : ''}\n        `}>\n          {toasts.map((toast, index) => (\n            <div\n              key={toast.id}\n              className=\"pointer-events-auto\"\n              style={{\n                zIndex: 1000 - index, // التوست الأحدث في المقدمة\n                marginTop: position.includes('top') ? `${index * 8}px` : '0',\n                marginBottom: position.includes('bottom') ? `${index * 8}px` : '0'\n              }}\n            >\n              <ToastNotification\n                notification={toast}\n                onClose={() => removeToast(toast.id)}\n                position={position}\n              />\n            </div>\n          ))}\n        </div>\n      </div>\n    </ToastContext.Provider>\n  );\n}\n\n// Hook مخصص لاستخدام التوست بسهولة\nexport const useQuickToast = () => {\n  const { showSuccess, showError, showWarning, showInfo } = useToast();\n  \n  return {\n    success: (message: string, title = 'نجح العملية') => showSuccess(title, message),\n    error: (message: string, title = 'حدث خطأ') => showError(title, message),\n    warning: (message: string, title = 'تحذير') => showWarning(title, message),\n    info: (message: string, title = 'معلومة') => showInfo(title, message)\n  };\n};\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAQO,SAAS,cAAc,EAC5B,QAAQ,EACR,YAAY,CAAC,EACb,WAAW,WAAW,EACH;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IAE/D,MAAM,YAAY,CAAC;QACjB,MAAM,WAAiC;YACrC,GAAG,gBAAgB;YACnB,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACjE,WAAW,IAAI;YACf,QAAQ;QACV;QAEA,UAAU,CAAA;YACR,MAAM,UAAU;gBAAC;mBAAa;aAAK;YACnC,kCAAkC;YAClC,OAAO,QAAQ,KAAK,CAAC,GAAG;QAC1B;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,iBAAiB;QACrB,UAAU,EAAE;IACd;IAEA,sCAAsC;IACtC,MAAM,cAAc,CAAC,OAAe,SAAiB,WAAoB;QACvE,UAAU;YACR,MAAM;YACN;YACA;YACA,UAAU;YACV,UAAU;YACV;YACA;YACA,MAAM;QACR;IACF;IAEA,MAAM,YAAY,CAAC,OAAe;QAChC,UAAU;YACR,MAAM;YACN;YACA;YACA,UAAU;YACV,UAAU;YACV,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,cAAc,CAAC,OAAe;QAClC,UAAU;YACR,MAAM;YACN;YACA;YACA,UAAU;YACV,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,WAAW,CAAC,OAAe;QAC/B,UAAU;YACR,MAAM;YACN;YACA;YACA,UAAU;YACV,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU;YACR,MAAM;YACN,OAAO,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC;YAClC,SAAS;YACT,UAAU;YACV,UAAU;YACV,WAAW;YACX,YAAY;YACZ,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,UAAU;YACR,MAAM;YACN,OAAO;YACP,SAAS,CAAC,OAAO,EAAE,OAAO,WAAW,EAAE,QAAQ,6CAA6C,CAAC;YAC7F,UAAU;YACV,UAAU;YACV,WAAW;YACX,YAAY;YACZ,MAAM;QACR;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,UAAU;YACR,MAAM;YACN,OAAO;YACP,SAAS,CAAC,cAAc,EAAE,QAAQ,2CAA2C,CAAC;YAC9E,UAAU;YACV,UAAU;YACV,WAAW;YACX,YAAY;YACZ,MAAM;QACR;IACF;IAEA,MAAM,cAAc,CAAC,YAAoB;QACvC,UAAU;YACR,MAAM;YACN,OAAO,CAAC,eAAe,EAAE,WAAW,GAAG,CAAC;YACxC,SAAS,QAAQ,MAAM,GAAG,KAAK,QAAQ,SAAS,CAAC,GAAG,MAAM,QAAQ;YAClE,UAAU;YACV,UAAU;YACV,WAAW;YACX,YAAY;YACZ,MAAM;QACR;IACF;IAEA,MAAM,eAAe,CAAC,WAAmB;QACvC,UAAU;YACR,MAAM;YACN,OAAO,SAAS,YAAY,4BAA4B;YACxD,SAAS,SAAS,YACd,CAAC,UAAU,EAAE,UAAU,mBAAmB,CAAC,GAC3C,CAAC,UAAU,EAAE,UAAU,8BAA8B,CAAC;YAC1D,UAAU;YACV,UAAU;YACV,WAAW,SAAS,YAAY,eAAe;YAC/C,YAAY,SAAS,YAAY,gBAAgB;YACjD,MAAM,SAAS,YAAY,OAAO;QACpC;IACF;IAEA,MAAM,aAAa;QACjB,UAAU;YACR,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,QAA0B;QAC9B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;;YAC3B;0BAGD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAW,CAAC;;UAEf,EAAE,aAAa,cAAc,kBAAkB,GAAG;UAClD,EAAE,aAAa,aAAa,iBAAiB,GAAG;UAChD,EAAE,aAAa,iBAAiB,qBAAqB,GAAG;UACxD,EAAE,aAAa,gBAAgB,oBAAoB,GAAG;UACtD,EAAE,aAAa,eAAe,8CAA8C,GAAG;UAC/E,EAAE,aAAa,kBAAkB,iDAAiD,GAAG;QACvF,CAAC;8BACE,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;4BAEC,WAAU;4BACV,OAAO;gCACL,QAAQ,OAAO;gCACf,WAAW,SAAS,QAAQ,CAAC,SAAS,GAAG,QAAQ,EAAE,EAAE,CAAC,GAAG;gCACzD,cAAc,SAAS,QAAQ,CAAC,YAAY,GAAG,QAAQ,EAAE,EAAE,CAAC,GAAG;4BACjE;sCAEA,cAAA,8OAAC,uIAAA,CAAA,UAAiB;gCAChB,cAAc;gCACd,SAAS,IAAM,YAAY,MAAM,EAAE;gCACnC,UAAU;;;;;;2BAXP,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;AAmB3B;AAGO,MAAM,gBAAgB;IAC3B,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG;IAE1D,OAAO;QACL,SAAS,CAAC,SAAiB,QAAQ,aAAa,GAAK,YAAY,OAAO;QACxE,OAAO,CAAC,SAAiB,QAAQ,SAAS,GAAK,UAAU,OAAO;QAChE,SAAS,CAAC,SAAiB,QAAQ,OAAO,GAAK,YAAY,OAAO;QAClE,MAAM,CAAC,SAAiB,QAAQ,QAAQ,GAAK,SAAS,OAAO;IAC/D;AACF"}}, {"offset": {"line": 2730, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2736, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/NotificationModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, createContext, useContext, ReactNode } from 'react';\n\ninterface ModalNotification {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info' | 'welcome' | 'confirmation' | 'logout';\n  title: string;\n  message: string;\n  icon?: string;\n  primaryAction?: {\n    text: string;\n    action: () => void;\n    style?: 'primary' | 'success' | 'danger' | 'warning';\n  };\n  secondaryAction?: {\n    text: string;\n    action: () => void;\n  };\n  autoClose?: boolean;\n  duration?: number;\n  backdrop?: boolean;\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n}\n\ninterface ModalContextType {\n  showModal: (modal: Omit<ModalNotification, 'id'>) => void;\n  showConfirmation: (\n    title: string, \n    message: string, \n    onConfirm: () => void, \n    onCancel?: () => void\n  ) => void;\n  showWelcomeModal: (userName: string, onComplete: () => void) => void;\n  showLogoutConfirmation: (onConfirm: () => void) => void;\n  closeModal: (id?: string) => void;\n  closeAllModals: () => void;\n}\n\nconst ModalContext = createContext<ModalContextType | undefined>(undefined);\n\nexport const useModal = () => {\n  const context = useContext(ModalContext);\n  if (!context) {\n    throw new Error('useModal must be used within a ModalProvider');\n  }\n  return context;\n};\n\ninterface ModalProviderProps {\n  children: ReactNode;\n}\n\nexport function ModalProvider({ children }: ModalProviderProps) {\n  const [modals, setModals] = useState<ModalNotification[]>([]);\n\n  const showModal = (modalData: Omit<ModalNotification, 'id'>) => {\n    const newModal: ModalNotification = {\n      ...modalData,\n      id: Date.now().toString() + Math.random().toString(36).substr(2, 9)\n    };\n\n    setModals(prev => [...prev, newModal]);\n\n    // إغلاق تلقائي إذا كان مطلوباً\n    if (modalData.autoClose && modalData.duration) {\n      setTimeout(() => {\n        closeModal(newModal.id);\n      }, modalData.duration);\n    }\n  };\n\n  const closeModal = (id?: string) => {\n    if (id) {\n      setModals(prev => prev.filter(modal => modal.id !== id));\n    } else {\n      // إغلاق آخر modal\n      setModals(prev => prev.slice(0, -1));\n    }\n  };\n\n  const closeAllModals = () => {\n    setModals([]);\n  };\n\n  const showConfirmation = (\n    title: string, \n    message: string, \n    onConfirm: () => void, \n    onCancel?: () => void\n  ) => {\n    showModal({\n      type: 'confirmation',\n      title,\n      message,\n      icon: '❓',\n      primaryAction: {\n        text: 'تأكيد',\n        action: () => {\n          onConfirm();\n          closeModal();\n        },\n        style: 'primary'\n      },\n      secondaryAction: {\n        text: 'إلغاء',\n        action: () => {\n          onCancel?.();\n          closeModal();\n        }\n      },\n      backdrop: true,\n      size: 'md'\n    });\n  };\n\n  const showWelcomeModal = (userName: string, onComplete: () => void) => {\n    showModal({\n      type: 'welcome',\n      title: `مرحباً بك ${userName}! 🎉`,\n      message: 'نحن سعداء لانضمامك إلى منصة من المالك. دعنا نساعدك في إعداد حسابك للحصول على أفضل تجربة.',\n      icon: '🎉',\n      primaryAction: {\n        text: 'ابدأ الإعداد',\n        action: () => {\n          onComplete();\n          closeModal();\n        },\n        style: 'success'\n      },\n      secondaryAction: {\n        text: 'لاحقاً',\n        action: () => closeModal()\n      },\n      backdrop: true,\n      size: 'lg'\n    });\n  };\n\n  const showLogoutConfirmation = (onConfirm: () => void) => {\n    showModal({\n      type: 'logout',\n      title: 'تأكيد تسجيل الخروج',\n      message: 'هل أنت متأكد من أنك تريد تسجيل الخروج من حسابك؟',\n      icon: '👋',\n      primaryAction: {\n        text: 'تسجيل الخروج',\n        action: () => {\n          onConfirm();\n          closeModal();\n        },\n        style: 'danger'\n      },\n      secondaryAction: {\n        text: 'البقاء متصلاً',\n        action: () => closeModal()\n      },\n      backdrop: true,\n      size: 'md'\n    });\n  };\n\n  const value: ModalContextType = {\n    showModal,\n    showConfirmation,\n    showWelcomeModal,\n    showLogoutConfirmation,\n    closeModal,\n    closeAllModals\n  };\n\n  // إغلاق المودال بالضغط على Escape\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape' && modals.length > 0) {\n        closeModal();\n      }\n    };\n\n    document.addEventListener('keydown', handleEscape);\n    return () => document.removeEventListener('keydown', handleEscape);\n  }, [modals.length]);\n\n  return (\n    <ModalContext.Provider value={value}>\n      {children}\n      \n      {/* عرض المودالات */}\n      {modals.map((modal, index) => (\n        <ModalComponent\n          key={modal.id}\n          modal={modal}\n          onClose={() => closeModal(modal.id)}\n          zIndex={1000 + index}\n        />\n      ))}\n    </ModalContext.Provider>\n  );\n}\n\ninterface ModalComponentProps {\n  modal: ModalNotification;\n  onClose: () => void;\n  zIndex: number;\n}\n\nfunction ModalComponent({ modal, onClose, zIndex }: ModalComponentProps) {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    // إظهار المودال مع تأخير بسيط للحصول على تأثير الانزلاق\n    const timer = setTimeout(() => setIsVisible(true), 50);\n    return () => clearTimeout(timer);\n  }, []);\n\n  const handleClose = () => {\n    setIsVisible(false);\n    setTimeout(onClose, 200); // انتظار انتهاء الانيميشن\n  };\n\n  const handleBackdropClick = (e: React.MouseEvent) => {\n    if (e.target === e.currentTarget && modal.backdrop) {\n      handleClose();\n    }\n  };\n\n  const getTypeStyles = () => {\n    switch (modal.type) {\n      case 'success':\n      case 'welcome':\n        return {\n          container: 'bg-green-50/95 backdrop-blur-md border-green-200',\n          header: 'text-green-800',\n          icon: 'text-green-600',\n          message: 'text-green-700'\n        };\n      \n      case 'error':\n        return {\n          container: 'bg-red-50/95 backdrop-blur-md border-red-200',\n          header: 'text-red-800',\n          icon: 'text-red-600',\n          message: 'text-red-700'\n        };\n      \n      case 'warning':\n        return {\n          container: 'bg-orange-50/95 backdrop-blur-md border-orange-200',\n          header: 'text-orange-800',\n          icon: 'text-orange-600',\n          message: 'text-orange-700'\n        };\n      \n      case 'info':\n      case 'confirmation':\n        return {\n          container: 'bg-blue-50/95 backdrop-blur-md border-blue-200',\n          header: 'text-blue-800',\n          icon: 'text-blue-600',\n          message: 'text-blue-700'\n        };\n      \n      case 'logout':\n        return {\n          container: 'bg-gray-50/95 backdrop-blur-md border-gray-200',\n          header: 'text-gray-800',\n          icon: 'text-gray-600',\n          message: 'text-gray-700'\n        };\n      \n      default:\n        return {\n          container: 'bg-white/95 backdrop-blur-md border-gray-200',\n          header: 'text-gray-800',\n          icon: 'text-gray-600',\n          message: 'text-gray-700'\n        };\n    }\n  };\n\n  const getSizeClasses = () => {\n    switch (modal.size) {\n      case 'sm': return 'max-w-sm';\n      case 'md': return 'max-w-md';\n      case 'lg': return 'max-w-lg';\n      case 'xl': return 'max-w-xl';\n      default: return 'max-w-md';\n    }\n  };\n\n  const getButtonStyle = (style?: string) => {\n    switch (style) {\n      case 'primary':\n        return 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500';\n      case 'success':\n        return 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500';\n      case 'danger':\n        return 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500';\n      case 'warning':\n        return 'bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500';\n      default:\n        return 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500';\n    }\n  };\n\n  const styles = getTypeStyles();\n\n  return (\n    <div\n      className={`\n        fixed inset-0 flex items-center justify-center p-4 transition-all duration-200\n        ${isVisible ? 'opacity-100' : 'opacity-0'}\n      `}\n      style={{ zIndex }}\n      onClick={handleBackdropClick}\n    >\n      {/* الخلفية المظلمة */}\n      <div className=\"absolute inset-0 bg-black/50 backdrop-blur-sm\" />\n      \n      {/* المودال */}\n      <div\n        className={`\n          relative w-full ${getSizeClasses()} transform transition-all duration-200\n          ${isVisible ? 'scale-100 translate-y-0' : 'scale-95 translate-y-4'}\n        `}\n      >\n        <div className={`\n          rounded-2xl border shadow-2xl ${styles.container}\n          ${modal.type === 'success' || modal.type === 'welcome' ? 'shadow-green-500/20' :\n            modal.type === 'error' ? 'shadow-red-500/20' :\n            modal.type === 'warning' ? 'shadow-orange-500/20' : 'shadow-blue-500/20'}\n        `}>\n          {/* الرأس */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200/50\">\n            <div className=\"flex items-center gap-3\">\n              {modal.icon && (\n                <span className={`text-3xl ${styles.icon}`}>\n                  {modal.icon}\n                </span>\n              )}\n              <h3 className={`text-xl font-bold ${styles.header}`}>\n                {modal.title}\n              </h3>\n            </div>\n            <button\n              onClick={handleClose}\n              className=\"text-gray-400 hover:text-gray-600 transition-colors\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n\n          {/* المحتوى */}\n          <div className=\"p-6\">\n            <p className={`text-lg leading-relaxed ${styles.message}`}>\n              {modal.message}\n            </p>\n          </div>\n\n          {/* الأزرار */}\n          {(modal.primaryAction || modal.secondaryAction) && (\n            <div className=\"flex items-center justify-end gap-3 p-6 border-t border-gray-200/50\">\n              {modal.secondaryAction && (\n                <button\n                  onClick={modal.secondaryAction.action}\n                  className=\"px-6 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2\"\n                >\n                  {modal.secondaryAction.text}\n                </button>\n              )}\n              {modal.primaryAction && (\n                <button\n                  onClick={modal.primaryAction.action}\n                  className={`\n                    px-6 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2\n                    ${getButtonStyle(modal.primaryAction.style)}\n                  `}\n                >\n                  {modal.primaryAction.text}\n                </button>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAuCA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,MAAM,WAAW;IACtB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAE5D,MAAM,YAAY,CAAC;QACjB,MAAM,WAA8B;YAClC,GAAG,SAAS;YACZ,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QACnE;QAEA,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;QAErC,+BAA+B;QAC/B,IAAI,UAAU,SAAS,IAAI,UAAU,QAAQ,EAAE;YAC7C,WAAW;gBACT,WAAW,SAAS,EAAE;YACxB,GAAG,UAAU,QAAQ;QACvB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,IAAI;YACN,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACtD,OAAO;YACL,kBAAkB;YAClB,UAAU,CAAA,OAAQ,KAAK,KAAK,CAAC,GAAG,CAAC;QACnC;IACF;IAEA,MAAM,iBAAiB;QACrB,UAAU,EAAE;IACd;IAEA,MAAM,mBAAmB,CACvB,OACA,SACA,WACA;QAEA,UAAU;YACR,MAAM;YACN;YACA;YACA,MAAM;YACN,eAAe;gBACb,MAAM;gBACN,QAAQ;oBACN;oBACA;gBACF;gBACA,OAAO;YACT;YACA,iBAAiB;gBACf,MAAM;gBACN,QAAQ;oBACN;oBACA;gBACF;YACF;YACA,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,mBAAmB,CAAC,UAAkB;QAC1C,UAAU;YACR,MAAM;YACN,OAAO,CAAC,UAAU,EAAE,SAAS,IAAI,CAAC;YAClC,SAAS;YACT,MAAM;YACN,eAAe;gBACb,MAAM;gBACN,QAAQ;oBACN;oBACA;gBACF;gBACA,OAAO;YACT;YACA,iBAAiB;gBACf,MAAM;gBACN,QAAQ,IAAM;YAChB;YACA,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,UAAU;YACR,MAAM;YACN,OAAO;YACP,SAAS;YACT,MAAM;YACN,eAAe;gBACb,MAAM;gBACN,QAAQ;oBACN;oBACA;gBACF;gBACA,OAAO;YACT;YACA,iBAAiB;gBACf,MAAM;gBACN,QAAQ,IAAM;YAChB;YACA,UAAU;YACV,MAAM;QACR;IACF;IAEA,MAAM,QAA0B;QAC9B;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,YAAY,OAAO,MAAM,GAAG,GAAG;gBAC3C;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC,OAAO,MAAM;KAAC;IAElB,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;;YAC3B;YAGA,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;oBAEC,OAAO;oBACP,SAAS,IAAM,WAAW,MAAM,EAAE;oBAClC,QAAQ,OAAO;mBAHV,MAAM,EAAE;;;;;;;;;;;AAQvB;AAQA,SAAS,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAuB;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wDAAwD;QACxD,MAAM,QAAQ,WAAW,IAAM,aAAa,OAAO;QACnD,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,aAAa;QACb,WAAW,SAAS,MAAM,0BAA0B;IACtD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,IAAI,MAAM,QAAQ,EAAE;YAClD;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ,MAAM,IAAI;YAChB,KAAK;YACL,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,QAAQ;oBACR,MAAM;oBACN,SAAS;gBACX;YAEF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,QAAQ;oBACR,MAAM;oBACN,SAAS;gBACX;YAEF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,QAAQ;oBACR,MAAM;oBACN,SAAS;gBACX;YAEF,KAAK;YACL,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,QAAQ;oBACR,MAAM;oBACN,SAAS;gBACX;YAEF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,QAAQ;oBACR,MAAM;oBACN,SAAS;gBACX;YAEF;gBACE,OAAO;oBACL,WAAW;oBACX,QAAQ;oBACR,MAAM;oBACN,SAAS;gBACX;QACJ;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ,MAAM,IAAI;YAChB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB,KAAK;gBAAM,OAAO;YAClB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,SAAS;IAEf,qBACE,8OAAC;QACC,WAAW,CAAC;;QAEV,EAAE,YAAY,gBAAgB,YAAY;MAC5C,CAAC;QACD,OAAO;YAAE;QAAO;QAChB,SAAS;;0BAGT,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBACC,WAAW,CAAC;0BACM,EAAE,iBAAiB;UACnC,EAAE,YAAY,4BAA4B,yBAAyB;QACrE,CAAC;0BAED,cAAA,8OAAC;oBAAI,WAAW,CAAC;wCACe,EAAE,OAAO,SAAS,CAAC;UACjD,EAAE,MAAM,IAAI,KAAK,aAAa,MAAM,IAAI,KAAK,YAAY,wBACvD,MAAM,IAAI,KAAK,UAAU,sBACzB,MAAM,IAAI,KAAK,YAAY,yBAAyB,qBAAqB;QAC7E,CAAC;;sCAEC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,MAAM,IAAI,kBACT,8OAAC;4CAAK,WAAW,CAAC,SAAS,EAAE,OAAO,IAAI,EAAE;sDACvC,MAAM,IAAI;;;;;;sDAGf,8OAAC;4CAAG,WAAW,CAAC,kBAAkB,EAAE,OAAO,MAAM,EAAE;sDAChD,MAAM,KAAK;;;;;;;;;;;;8CAGhB,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;sCAM3E,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAW,CAAC,wBAAwB,EAAE,OAAO,OAAO,EAAE;0CACtD,MAAM,OAAO;;;;;;;;;;;wBAKjB,CAAC,MAAM,aAAa,IAAI,MAAM,eAAe,mBAC5C,8OAAC;4BAAI,WAAU;;gCACZ,MAAM,eAAe,kBACpB,8OAAC;oCACC,SAAS,MAAM,eAAe,CAAC,MAAM;oCACrC,WAAU;8CAET,MAAM,eAAe,CAAC,IAAI;;;;;;gCAG9B,MAAM,aAAa,kBAClB,8OAAC;oCACC,SAAS,MAAM,aAAa,CAAC,MAAM;oCACnC,WAAW,CAAC;;oBAEV,EAAE,eAAe,MAAM,aAAa,CAAC,KAAK,EAAE;kBAC9C,CAAC;8CAEA,MAAM,aAAa,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C"}}, {"offset": {"line": 3137, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3143, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';\nimport { User, AuthState, LoginCredentials, RegisterData, UserSession } from '@/types/user';\n\ninterface AuthContextType extends AuthState {\n  login: (credentials: LoginCredentials) => Promise<void>;\n  register: (data: RegisterData) => Promise<void>;\n  logout: () => void;\n  updateProfile: (updates: Partial<User>) => Promise<void>;\n  refreshToken: () => Promise<void>;\n  deleteAccount: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\ntype AuthAction =\n  | { type: 'LOGIN_START' }\n  | { type: 'LOGIN_SUCCESS'; payload: User }\n  | { type: 'LOGIN_FAILURE'; payload: string }\n  | { type: 'LOGOUT' }\n  | { type: 'UPDATE_PROFILE'; payload: Partial<User> }\n  | { type: 'SET_LOADING'; payload: boolean }\n  | { type: 'SET_ERROR'; payload: string | null };\n\nconst authReducer = (state: AuthState, action: AuthAction): AuthState => {\n  switch (action.type) {\n    case 'LOGIN_START':\n      return { ...state, isLoading: true, error: null };\n    case 'LOGIN_SUCCESS':\n      return {\n        ...state,\n        user: action.payload,\n        isAuthenticated: true,\n        isLoading: false,\n        error: null,\n      };\n    case 'LOGIN_FAILURE':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: action.payload,\n      };\n    case 'LOGOUT':\n      return {\n        ...state,\n        user: null,\n        isAuthenticated: false,\n        isLoading: false,\n        error: null,\n      };\n    case 'UPDATE_PROFILE':\n      return {\n        ...state,\n        user: state.user ? { ...state.user, ...action.payload } : null,\n      };\n    case 'SET_LOADING':\n      return { ...state, isLoading: action.payload };\n    case 'SET_ERROR':\n      return { ...state, error: action.payload };\n    default:\n      return state;\n  }\n};\n\nconst initialState: AuthState = {\n  user: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null,\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider = ({ children }: AuthProviderProps) => {\n  const [state, dispatch] = useReducer(authReducer, initialState);\n\n  // تحميل بيانات المستخدم من localStorage عند بدء التطبيق\n  useEffect(() => {\n    const loadUserFromStorage = () => {\n      try {\n        if (typeof window === 'undefined') return;\n\n        // التحقق من النظام الجديد أولاً\n        const isLoggedIn = localStorage.getItem('isLoggedIn');\n        const currentUser = localStorage.getItem('currentUser');\n\n        if (isLoggedIn === 'true' && currentUser) {\n          const userData = JSON.parse(currentUser);\n\n          // تحويل البيانات إلى تنسيق User\n          const user: User = {\n            id: userData.id || 'user-mahmut-001',\n            email: userData.email || '<EMAIL>',\n            phone: '+963988652401',\n            name: userData.name || 'Mahmut Madenli',\n            userType: userData.userType || 'individual',\n            isVerified: true,\n            createdAt: new Date(userData.createdAt || '2024-01-15'),\n            lastLogin: new Date(),\n            stats: {\n              totalAds: 12,\n              activeAds: 8,\n              expiredAds: 4,\n              totalViews: 2450,\n              totalContacts: 89,\n              successfulDeals: 15,\n            },\n            settings: {\n              language: 'ar',\n              notifications: {\n                email: true,\n                sms: true,\n                push: true,\n                marketing: false,\n              },\n              privacy: {\n                showPhone: true,\n                showEmail: false,\n                allowMessages: true,\n                showOnlineStatus: true,\n              },\n              preferences: {\n                currency: 'SYP',\n                theme: 'light',\n                autoSave: true,\n              },\n            },\n            individualInfo: {\n              firstName: userData.individualInfo?.firstName || 'Mahmut',\n              lastName: userData.individualInfo?.lastName || 'Madenli',\n              gender: 'male',\n              address: {\n                governorate: 'دمشق',\n                city: 'دمشق',\n                area: 'المزة',\n              },\n            },\n            subscription: {\n              planId: userData.subscriptionPlan || 'premium',\n              planName: 'الباقة المميزة',\n              planType: 'individual',\n              startDate: new Date('2024-01-15'),\n              endDate: new Date('2024-12-31'),\n              isActive: true,\n              autoRenew: true,\n              features: ['15 إعلان شهرياً', 'إعلانات مميزة', 'دعم فني'],\n            },\n          };\n\n          dispatch({ type: 'LOGIN_SUCCESS', payload: user });\n          return;\n        }\n\n        // النظام القديم\n        const sessionData = localStorage.getItem('userSession');\n        if (sessionData) {\n          const session: UserSession = JSON.parse(sessionData);\n\n          // التحقق من انتهاء صلاحية الجلسة\n          if (new Date(session.expiresAt) > new Date()) {\n            dispatch({ type: 'LOGIN_SUCCESS', payload: session.user });\n          } else {\n            localStorage.removeItem('userSession');\n          }\n        }\n      } catch (error) {\n        console.error('Error loading user session:', error);\n        if (typeof window !== 'undefined') {\n          localStorage.removeItem('userSession');\n          localStorage.removeItem('isLoggedIn');\n          localStorage.removeItem('currentUser');\n        }\n      }\n    };\n\n    loadUserFromStorage();\n  }, []);\n\n  const login = async (credentials: LoginCredentials) => {\n    dispatch({ type: 'LOGIN_START' });\n\n    try {\n      // التحقق من بيانات Mahmut Madenli\n      if (credentials.email === '<EMAIL>' && credentials.password === 'Ma123456') {\n        const mahmutUser: User = {\n          id: 'user-mahmut-001',\n          email: '<EMAIL>',\n          phone: '+963988652401',\n          name: 'Mahmut Madenli',\n          userType: 'individual',\n          isVerified: true,\n          createdAt: new Date('2024-01-15'),\n          lastLogin: new Date(),\n          stats: {\n            totalAds: 12,\n            activeAds: 8,\n            expiredAds: 4,\n            totalViews: 2450,\n            totalContacts: 89,\n            successfulDeals: 15,\n          },\n          settings: {\n            language: 'ar',\n            notifications: {\n              email: true,\n              sms: true,\n              push: true,\n              marketing: false,\n            },\n            privacy: {\n              showPhone: true,\n              showEmail: false,\n              allowMessages: true,\n              showOnlineStatus: true,\n            },\n            preferences: {\n              currency: 'SYP',\n              theme: 'light',\n              autoSave: true,\n            },\n          },\n          individualInfo: {\n            firstName: 'Mahmut',\n            lastName: 'Madenli',\n            gender: 'male',\n            address: {\n              governorate: 'دمشق',\n              city: 'دمشق',\n              area: 'المزة',\n            },\n          },\n          subscription: {\n            planId: 'premium',\n            planName: 'الباقة المميزة',\n            planType: 'individual',\n            startDate: new Date('2024-01-15'),\n            endDate: new Date('2024-12-31'),\n            isActive: true,\n            autoRenew: true,\n            features: ['15 إعلان شهرياً', 'إعلانات مميزة', 'دعم فني'],\n          },\n        };\n\n        // حفظ في النظام الجديد\n        localStorage.setItem('isLoggedIn', 'true');\n        localStorage.setItem('currentUser', JSON.stringify({\n          id: mahmutUser.id,\n          name: mahmutUser.name,\n          email: mahmutUser.email,\n          userType: mahmutUser.userType,\n          individualInfo: mahmutUser.individualInfo,\n          membershipId: 'MIN-2024-001247',\n          createdAt: mahmutUser.createdAt,\n          subscriptionPlan: 'premium',\n          verificationBadge: 'silver'\n        }));\n\n        dispatch({ type: 'LOGIN_SUCCESS', payload: mahmutUser });\n        return;\n      }\n\n      // التحقق من بيانات الشركة\n      if (credentials.email === '<EMAIL>' && credentials.password === 'Gr123456') {\n        const businessUser: User = {\n          id: 'user-business-001',\n          email: '<EMAIL>',\n          phone: '+963988652401',\n          name: 'Grand Mark Turkey',\n          userType: 'business',\n          isVerified: true,\n          createdAt: new Date('2024-01-10'),\n          lastLogin: new Date(),\n          stats: {\n            totalAds: 25,\n            activeAds: 18,\n            expiredAds: 7,\n            totalViews: 5200,\n            totalContacts: 156,\n            successfulDeals: 32,\n          },\n          settings: {\n            language: 'ar',\n            notifications: {\n              email: true,\n              sms: true,\n              push: true,\n              marketing: true,\n            },\n            privacy: {\n              showPhone: true,\n              showEmail: true,\n              allowMessages: true,\n              showOnlineStatus: true,\n            },\n            preferences: {\n              currency: 'SYP',\n              theme: 'light',\n              autoSave: true,\n            },\n          },\n          businessInfo: {\n            companyName: 'Grand Mark Turkey',\n            businessType: 'استيراد وتصدير',\n            registrationNumber: 'GMT123456',\n            taxNumber: 'TAX789012',\n            establishedYear: 2018,\n            employeeCount: '50-100',\n            website: 'https://grandmarkturkey.com',\n            description: 'شركة رائدة في مجال الاستيراد والتصدير مع تركيا',\n            address: {\n              governorate: 'دمشق',\n              city: 'دمشق',\n              area: 'المزة',\n              street: 'شارع التجارة',\n              building: 'مبنى التجارة الدولية',\n            },\n            contactPerson: {\n              name: 'أحمد محمد',\n              position: 'المدير العام',\n              phone: '+963988652401',\n              email: '<EMAIL>',\n            },\n          },\n          subscription: {\n            planId: 'business-professional',\n            planName: 'الخطة المهنية',\n            planType: 'business',\n            startDate: new Date('2024-01-10'),\n            endDate: new Date('2025-01-10'),\n            isActive: true,\n            autoRenew: true,\n            features: ['إعلانات غير محدودة', 'دعم أولوية', 'تحليلات متقدمة', 'شارة التوثيق الذهبية'],\n          },\n        };\n\n        const session: UserSession = {\n          token: 'mock-token-business',\n          refreshToken: 'mock-refresh-token-business',\n          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),\n          user: businessUser,\n        };\n\n        localStorage.setItem('userSession', JSON.stringify(session));\n        localStorage.setItem('isLoggedIn', 'true');\n        localStorage.setItem('currentUser', JSON.stringify(businessUser));\n\n        dispatch({ type: 'LOGIN_SUCCESS', payload: businessUser });\n        return;\n      }\n\n      // التحقق من بيانات المكتب العقاري\n      if (credentials.email === '<EMAIL>' && credentials.password === 'Du123456') {\n        const realEstateUser: User = {\n          id: 'user-realestate-001',\n          email: '<EMAIL>',\n          phone: '+963988652401',\n          name: 'مكتب دنيا زاد العقاري',\n          userType: 'real-estate-office',\n          isVerified: true,\n          createdAt: new Date('2024-01-05'),\n          lastLogin: new Date(),\n          stats: {\n            totalAds: 45,\n            activeAds: 32,\n            expiredAds: 13,\n            totalViews: 8500,\n            totalContacts: 245,\n            successfulDeals: 58,\n          },\n          settings: {\n            language: 'ar',\n            notifications: {\n              email: true,\n              sms: true,\n              push: true,\n              marketing: true,\n            },\n            privacy: {\n              showPhone: true,\n              showEmail: true,\n              allowMessages: true,\n              showOnlineStatus: true,\n            },\n            preferences: {\n              currency: 'SYP',\n              theme: 'light',\n              autoSave: true,\n            },\n          },\n          realEstateOfficeInfo: {\n            officeName: 'مكتب دنيا زاد العقاري',\n            licenseNumber: 'RE123456',\n            establishedYear: 2020,\n            specialization: ['بيع', 'شراء', 'إيجار', 'استثمار عقاري'],\n            serviceAreas: ['دمشق', 'ريف دمشق', 'حلب'],\n            ownerName: 'محمد دنيا زاد',\n            managerName: 'أحمد دنيا زاد',\n            yearsOfExperience: 8,\n            teamSize: 12,\n            description: 'مكتب عقاري متخصص في جميع أنواع العقارات السكنية والتجارية',\n            address: {\n              governorate: 'دمشق',\n              city: 'دمشق',\n              area: 'المالكي',\n              street: 'شارع العقارات',\n              building: 'مبنى دنيا زاد',\n            },\n          },\n          subscription: {\n            planId: 'real-estate-office',\n            planName: 'خطة المكتب العقاري',\n            planType: 'business',\n            startDate: new Date('2024-01-05'),\n            endDate: new Date('2025-01-05'),\n            isActive: true,\n            autoRenew: true,\n            features: ['إعلانات عقارية غير محدودة', 'أدوات تحليل السوق', 'دعم متخصص', 'شارة التوثيق الفضية'],\n          },\n        };\n\n        const session: UserSession = {\n          token: 'mock-token-realestate',\n          refreshToken: 'mock-refresh-token-realestate',\n          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),\n          user: realEstateUser,\n        };\n\n        localStorage.setItem('userSession', JSON.stringify(session));\n        localStorage.setItem('isLoggedIn', 'true');\n        localStorage.setItem('currentUser', JSON.stringify(realEstateUser));\n\n        dispatch({ type: 'LOGIN_SUCCESS', payload: realEstateUser });\n        return;\n      }\n\n      // محاكاة API call للمستخدمين الآخرين\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // بيانات مستخدم تجريبية\n      const mockUser: User = {\n        id: 'user-' + Date.now(),\n        email: credentials.email,\n        phone: '+963988123456',\n        name: credentials.email.split('@')[0],\n        userType: credentials.email.includes('business') ? 'business' :\n                  credentials.email.includes('office') ? 'real-estate-office' : 'individual',\n        isVerified: true,\n        createdAt: new Date(),\n        lastLogin: new Date(),\n        stats: {\n          totalAds: 5,\n          activeAds: 3,\n          expiredAds: 2,\n          totalViews: 1250,\n          totalContacts: 45,\n          successfulDeals: 8,\n        },\n        settings: {\n          language: 'ar',\n          notifications: {\n            email: true,\n            sms: true,\n            push: true,\n            marketing: false,\n          },\n          privacy: {\n            showPhone: true,\n            showEmail: false,\n            allowMessages: true,\n            showOnlineStatus: true,\n          },\n          preferences: {\n            currency: 'SYP',\n            theme: 'light',\n            autoSave: true,\n          },\n        },\n      };\n\n      // إضافة معلومات حسب نوع المستخدم\n      if (mockUser.userType === 'individual') {\n        mockUser.individualInfo = {\n          firstName: 'أحمد',\n          lastName: 'محمد',\n          gender: 'male',\n          address: {\n            governorate: 'دمشق',\n            city: 'دمشق',\n            area: 'المزة',\n          },\n        };\n      } else if (mockUser.userType === 'business') {\n        mockUser.businessInfo = {\n          companyName: 'شركة التجارة المتقدمة',\n          businessType: 'تجارة عامة',\n          registrationNumber: 'REG123456',\n          establishedYear: 2015,\n          employeeCount: '10-50',\n          address: {\n            governorate: 'دمشق',\n            city: 'دمشق',\n            area: 'المزة',\n            street: 'شارع الثورة',\n          },\n          contactPerson: {\n            name: 'محمد أحمد',\n            position: 'مدير عام',\n            phone: '+963988123456',\n            email: credentials.email,\n          },\n        };\n        mockUser.subscription = {\n          planId: 'business-starter',\n          planName: 'باقة البداية',\n          planType: 'business',\n          startDate: new Date(),\n          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),\n          isActive: true,\n          autoRenew: true,\n          features: ['50 إعلان شهرياً', 'دعم فني', 'إحصائيات متقدمة'],\n        };\n      } else if (mockUser.userType === 'real-estate-office') {\n        mockUser.realEstateOfficeInfo = {\n          officeName: 'مكتب العقارات المتميز',\n          licenseNumber: 'LIC789012',\n          licenseIssueDate: new Date('2020-01-01'),\n          licenseExpiryDate: new Date('2025-01-01'),\n          ownerName: 'خالد السوري',\n          specializations: ['شقق سكنية', 'فيلات', 'محلات تجارية'],\n          serviceAreas: ['دمشق', 'ريف دمشق'],\n          yearsOfExperience: 10,\n          teamSize: 5,\n          address: {\n            governorate: 'دمشق',\n            city: 'دمشق',\n            area: 'المزة',\n            street: 'شارع المتنبي',\n            building: 'بناء رقم 15',\n            floor: 'الطابق الثاني',\n          },\n          workingHours: {\n            sunday: { open: '09:00', close: '17:00', isOpen: true },\n            monday: { open: '09:00', close: '17:00', isOpen: true },\n            tuesday: { open: '09:00', close: '17:00', isOpen: true },\n            wednesday: { open: '09:00', close: '17:00', isOpen: true },\n            thursday: { open: '09:00', close: '17:00', isOpen: true },\n            friday: { open: '09:00', close: '12:00', isOpen: true },\n            saturday: { open: '09:00', close: '17:00', isOpen: true },\n          },\n        };\n        mockUser.subscription = {\n          planId: 'real-estate-office',\n          planName: 'باقة المكتب العقاري',\n          planType: 'business',\n          startDate: new Date(),\n          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),\n          isActive: true,\n          autoRenew: true,\n          features: ['30 إعلان شهرياً', 'شارة موثقة', 'أولوية في النتائج', 'دعم فني مخصص'],\n        };\n      }\n\n      // حفظ الجلسة\n      const session: UserSession = {\n        token: 'mock-token-' + Date.now(),\n        refreshToken: 'mock-refresh-token-' + Date.now(),\n        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 ساعة\n        user: mockUser,\n      };\n\n      if (credentials.rememberMe) {\n        localStorage.setItem('userSession', JSON.stringify(session));\n      }\n\n      dispatch({ type: 'LOGIN_SUCCESS', payload: mockUser });\n    } catch (error) {\n      dispatch({ type: 'LOGIN_FAILURE', payload: 'فشل في تسجيل الدخول' });\n    }\n  };\n\n  const register = async (data: RegisterData) => {\n    dispatch({ type: 'LOGIN_START' });\n\n    try {\n      // محاكاة API call\n      await new Promise(resolve => setTimeout(resolve, 1500));\n\n      // التحقق من صحة البيانات\n      if (data.password !== data.confirmPassword) {\n        throw new Error('كلمات المرور غير متطابقة');\n      }\n\n      // إنشاء مستخدم جديد\n      const newUser: User = {\n        id: 'user-' + Date.now(),\n        email: data.email,\n        phone: data.phone,\n        name: data.name,\n        userType: data.userType,\n        isVerified: false,\n        createdAt: new Date(),\n        lastLogin: new Date(),\n        stats: {\n          totalAds: 0,\n          activeAds: 0,\n          expiredAds: 0,\n          totalViews: 0,\n          totalContacts: 0,\n          successfulDeals: 0,\n        },\n        settings: {\n          language: 'ar',\n          notifications: {\n            email: true,\n            sms: true,\n            push: true,\n            marketing: false,\n          },\n          privacy: {\n            showPhone: true,\n            showEmail: false,\n            allowMessages: true,\n            showOnlineStatus: true,\n          },\n          preferences: {\n            currency: 'SYP',\n            theme: 'light',\n            autoSave: true,\n          },\n        },\n        individualInfo: data.individualInfo,\n        businessInfo: data.businessInfo,\n        realEstateOfficeInfo: data.realEstateOfficeInfo,\n      };\n\n      dispatch({ type: 'LOGIN_SUCCESS', payload: newUser });\n    } catch (error) {\n      dispatch({ type: 'LOGIN_FAILURE', payload: error instanceof Error ? error.message : 'فشل في إنشاء الحساب' });\n    }\n  };\n\n  const logout = () => {\n    localStorage.removeItem('userSession');\n    localStorage.removeItem('isLoggedIn');\n    localStorage.removeItem('currentUser');\n    dispatch({ type: 'LOGOUT' });\n  };\n\n  const updateProfile = async (updates: Partial<User>) => {\n    try {\n      dispatch({ type: 'UPDATE_PROFILE', payload: updates });\n\n      // تحديث الجلسة المحفوظة\n      const sessionData = localStorage.getItem('userSession');\n      if (sessionData) {\n        const session: UserSession = JSON.parse(sessionData);\n        session.user = { ...session.user, ...updates };\n        localStorage.setItem('userSession', JSON.stringify(session));\n      }\n    } catch (error) {\n      dispatch({ type: 'SET_ERROR', payload: 'فشل في تحديث الملف الشخصي' });\n    }\n  };\n\n  const refreshToken = async () => {\n    // محاكاة تجديد الرمز المميز\n    try {\n      await new Promise(resolve => setTimeout(resolve, 500));\n      // تحديث الجلسة\n    } catch (error) {\n      logout();\n    }\n  };\n\n  const deleteAccount = async () => {\n    try {\n      // محاكاة حذف الحساب\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      logout();\n    } catch (error) {\n      dispatch({ type: 'SET_ERROR', payload: 'فشل في حذف الحساب' });\n    }\n  };\n\n  return (\n    <AuthContext.Provider\n      value={{\n        ...state,\n        login,\n        register,\n        logout,\n        updateProfile,\n        refreshToken,\n        deleteAccount,\n      }}\n    >\n      {children}\n    </AuthContext.Provider>\n  );\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAcA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAW/D,MAAM,cAAc,CAAC,OAAkB;IACrC,OAAQ,OAAO,IAAI;QACjB,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,WAAW;gBAAM,OAAO;YAAK;QAClD,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM,OAAO,OAAO;gBACpB,iBAAiB;gBACjB,WAAW;gBACX,OAAO;YACT;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM;gBACN,iBAAiB;gBACjB,WAAW;gBACX,OAAO,OAAO,OAAO;YACvB;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM;gBACN,iBAAiB;gBACjB,WAAW;gBACX,OAAO;YACT;QACF,KAAK;YACH,OAAO;gBACL,GAAG,KAAK;gBACR,MAAM,MAAM,IAAI,GAAG;oBAAE,GAAG,MAAM,IAAI;oBAAE,GAAG,OAAO,OAAO;gBAAC,IAAI;YAC5D;QACF,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,WAAW,OAAO,OAAO;YAAC;QAC/C,KAAK;YACH,OAAO;gBAAE,GAAG,KAAK;gBAAE,OAAO,OAAO,OAAO;YAAC;QAC3C;YACE,OAAO;IACX;AACF;AAEA,MAAM,eAA0B;IAC9B,MAAM;IACN,iBAAiB;IACjB,WAAW;IACX,OAAO;AACT;AAMO,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAqB;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,aAAa;IAElD,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,IAAI;gBACF,wCAAmC;;gBAEnC,gCAAgC;gBAChC,MAAM;gBACN,MAAM;gBAqEN,gBAAgB;gBAChB,MAAM;YAWR,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,uCAAmC;;gBAInC;YACF;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,QAAQ,OAAO;QACnB,SAAS;YAAE,MAAM;QAAc;QAE/B,IAAI;YACF,kCAAkC;YAClC,IAAI,YAAY,KAAK,KAAK,6BAA6B,YAAY,QAAQ,KAAK,YAAY;gBAC1F,MAAM,aAAmB;oBACvB,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,MAAM;oBACN,UAAU;oBACV,YAAY;oBACZ,WAAW,IAAI,KAAK;oBACpB,WAAW,IAAI;oBACf,OAAO;wBACL,UAAU;wBACV,WAAW;wBACX,YAAY;wBACZ,YAAY;wBACZ,eAAe;wBACf,iBAAiB;oBACnB;oBACA,UAAU;wBACR,UAAU;wBACV,eAAe;4BACb,OAAO;4BACP,KAAK;4BACL,MAAM;4BACN,WAAW;wBACb;wBACA,SAAS;4BACP,WAAW;4BACX,WAAW;4BACX,eAAe;4BACf,kBAAkB;wBACpB;wBACA,aAAa;4BACX,UAAU;4BACV,OAAO;4BACP,UAAU;wBACZ;oBACF;oBACA,gBAAgB;wBACd,WAAW;wBACX,UAAU;wBACV,QAAQ;wBACR,SAAS;4BACP,aAAa;4BACb,MAAM;4BACN,MAAM;wBACR;oBACF;oBACA,cAAc;wBACZ,QAAQ;wBACR,UAAU;wBACV,UAAU;wBACV,WAAW,IAAI,KAAK;wBACpB,SAAS,IAAI,KAAK;wBAClB,UAAU;wBACV,WAAW;wBACX,UAAU;4BAAC;4BAAmB;4BAAiB;yBAAU;oBAC3D;gBACF;gBAEA,uBAAuB;gBACvB,aAAa,OAAO,CAAC,cAAc;gBACnC,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;oBACjD,IAAI,WAAW,EAAE;oBACjB,MAAM,WAAW,IAAI;oBACrB,OAAO,WAAW,KAAK;oBACvB,UAAU,WAAW,QAAQ;oBAC7B,gBAAgB,WAAW,cAAc;oBACzC,cAAc;oBACd,WAAW,WAAW,SAAS;oBAC/B,kBAAkB;oBAClB,mBAAmB;gBACrB;gBAEA,SAAS;oBAAE,MAAM;oBAAiB,SAAS;gBAAW;gBACtD;YACF;YAEA,0BAA0B;YAC1B,IAAI,YAAY,KAAK,KAAK,+BAA+B,YAAY,QAAQ,KAAK,YAAY;gBAC5F,MAAM,eAAqB;oBACzB,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,MAAM;oBACN,UAAU;oBACV,YAAY;oBACZ,WAAW,IAAI,KAAK;oBACpB,WAAW,IAAI;oBACf,OAAO;wBACL,UAAU;wBACV,WAAW;wBACX,YAAY;wBACZ,YAAY;wBACZ,eAAe;wBACf,iBAAiB;oBACnB;oBACA,UAAU;wBACR,UAAU;wBACV,eAAe;4BACb,OAAO;4BACP,KAAK;4BACL,MAAM;4BACN,WAAW;wBACb;wBACA,SAAS;4BACP,WAAW;4BACX,WAAW;4BACX,eAAe;4BACf,kBAAkB;wBACpB;wBACA,aAAa;4BACX,UAAU;4BACV,OAAO;4BACP,UAAU;wBACZ;oBACF;oBACA,cAAc;wBACZ,aAAa;wBACb,cAAc;wBACd,oBAAoB;wBACpB,WAAW;wBACX,iBAAiB;wBACjB,eAAe;wBACf,SAAS;wBACT,aAAa;wBACb,SAAS;4BACP,aAAa;4BACb,MAAM;4BACN,MAAM;4BACN,QAAQ;4BACR,UAAU;wBACZ;wBACA,eAAe;4BACb,MAAM;4BACN,UAAU;4BACV,OAAO;4BACP,OAAO;wBACT;oBACF;oBACA,cAAc;wBACZ,QAAQ;wBACR,UAAU;wBACV,UAAU;wBACV,WAAW,IAAI,KAAK;wBACpB,SAAS,IAAI,KAAK;wBAClB,UAAU;wBACV,WAAW;wBACX,UAAU;4BAAC;4BAAsB;4BAAc;4BAAkB;yBAAuB;oBAC1F;gBACF;gBAEA,MAAM,UAAuB;oBAC3B,OAAO;oBACP,cAAc;oBACd,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK;oBAChD,MAAM;gBACR;gBAEA,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;gBACnD,aAAa,OAAO,CAAC,cAAc;gBACnC,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;gBAEnD,SAAS;oBAAE,MAAM;oBAAiB,SAAS;gBAAa;gBACxD;YACF;YAEA,kCAAkC;YAClC,IAAI,YAAY,KAAK,KAAK,0BAA0B,YAAY,QAAQ,KAAK,YAAY;gBACvF,MAAM,iBAAuB;oBAC3B,IAAI;oBACJ,OAAO;oBACP,OAAO;oBACP,MAAM;oBACN,UAAU;oBACV,YAAY;oBACZ,WAAW,IAAI,KAAK;oBACpB,WAAW,IAAI;oBACf,OAAO;wBACL,UAAU;wBACV,WAAW;wBACX,YAAY;wBACZ,YAAY;wBACZ,eAAe;wBACf,iBAAiB;oBACnB;oBACA,UAAU;wBACR,UAAU;wBACV,eAAe;4BACb,OAAO;4BACP,KAAK;4BACL,MAAM;4BACN,WAAW;wBACb;wBACA,SAAS;4BACP,WAAW;4BACX,WAAW;4BACX,eAAe;4BACf,kBAAkB;wBACpB;wBACA,aAAa;4BACX,UAAU;4BACV,OAAO;4BACP,UAAU;wBACZ;oBACF;oBACA,sBAAsB;wBACpB,YAAY;wBACZ,eAAe;wBACf,iBAAiB;wBACjB,gBAAgB;4BAAC;4BAAO;4BAAQ;4BAAS;yBAAgB;wBACzD,cAAc;4BAAC;4BAAQ;4BAAY;yBAAM;wBACzC,WAAW;wBACX,aAAa;wBACb,mBAAmB;wBACnB,UAAU;wBACV,aAAa;wBACb,SAAS;4BACP,aAAa;4BACb,MAAM;4BACN,MAAM;4BACN,QAAQ;4BACR,UAAU;wBACZ;oBACF;oBACA,cAAc;wBACZ,QAAQ;wBACR,UAAU;wBACV,UAAU;wBACV,WAAW,IAAI,KAAK;wBACpB,SAAS,IAAI,KAAK;wBAClB,UAAU;wBACV,WAAW;wBACX,UAAU;4BAAC;4BAA6B;4BAAqB;4BAAa;yBAAsB;oBAClG;gBACF;gBAEA,MAAM,UAAuB;oBAC3B,OAAO;oBACP,cAAc;oBACd,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK;oBAChD,MAAM;gBACR;gBAEA,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;gBACnD,aAAa,OAAO,CAAC,cAAc;gBACnC,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;gBAEnD,SAAS;oBAAE,MAAM;oBAAiB,SAAS;gBAAe;gBAC1D;YACF;YAEA,qCAAqC;YACrC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,wBAAwB;YACxB,MAAM,WAAiB;gBACrB,IAAI,UAAU,KAAK,GAAG;gBACtB,OAAO,YAAY,KAAK;gBACxB,OAAO;gBACP,MAAM,YAAY,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBACrC,UAAU,YAAY,KAAK,CAAC,QAAQ,CAAC,cAAc,aACzC,YAAY,KAAK,CAAC,QAAQ,CAAC,YAAY,uBAAuB;gBACxE,YAAY;gBACZ,WAAW,IAAI;gBACf,WAAW,IAAI;gBACf,OAAO;oBACL,UAAU;oBACV,WAAW;oBACX,YAAY;oBACZ,YAAY;oBACZ,eAAe;oBACf,iBAAiB;gBACnB;gBACA,UAAU;oBACR,UAAU;oBACV,eAAe;wBACb,OAAO;wBACP,KAAK;wBACL,MAAM;wBACN,WAAW;oBACb;oBACA,SAAS;wBACP,WAAW;wBACX,WAAW;wBACX,eAAe;wBACf,kBAAkB;oBACpB;oBACA,aAAa;wBACX,UAAU;wBACV,OAAO;wBACP,UAAU;oBACZ;gBACF;YACF;YAEA,iCAAiC;YACjC,IAAI,SAAS,QAAQ,KAAK,cAAc;gBACtC,SAAS,cAAc,GAAG;oBACxB,WAAW;oBACX,UAAU;oBACV,QAAQ;oBACR,SAAS;wBACP,aAAa;wBACb,MAAM;wBACN,MAAM;oBACR;gBACF;YACF,OAAO,IAAI,SAAS,QAAQ,KAAK,YAAY;gBAC3C,SAAS,YAAY,GAAG;oBACtB,aAAa;oBACb,cAAc;oBACd,oBAAoB;oBACpB,iBAAiB;oBACjB,eAAe;oBACf,SAAS;wBACP,aAAa;wBACb,MAAM;wBACN,MAAM;wBACN,QAAQ;oBACV;oBACA,eAAe;wBACb,MAAM;wBACN,UAAU;wBACV,OAAO;wBACP,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBACA,SAAS,YAAY,GAAG;oBACtB,QAAQ;oBACR,UAAU;oBACV,UAAU;oBACV,WAAW,IAAI;oBACf,SAAS,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;oBACnD,UAAU;oBACV,WAAW;oBACX,UAAU;wBAAC;wBAAmB;wBAAW;qBAAkB;gBAC7D;YACF,OAAO,IAAI,SAAS,QAAQ,KAAK,sBAAsB;gBACrD,SAAS,oBAAoB,GAAG;oBAC9B,YAAY;oBACZ,eAAe;oBACf,kBAAkB,IAAI,KAAK;oBAC3B,mBAAmB,IAAI,KAAK;oBAC5B,WAAW;oBACX,iBAAiB;wBAAC;wBAAa;wBAAS;qBAAe;oBACvD,cAAc;wBAAC;wBAAQ;qBAAW;oBAClC,mBAAmB;oBACnB,UAAU;oBACV,SAAS;wBACP,aAAa;wBACb,MAAM;wBACN,MAAM;wBACN,QAAQ;wBACR,UAAU;wBACV,OAAO;oBACT;oBACA,cAAc;wBACZ,QAAQ;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACtD,QAAQ;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACtD,SAAS;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACvD,WAAW;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACzD,UAAU;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACxD,QAAQ;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;wBACtD,UAAU;4BAAE,MAAM;4BAAS,OAAO;4BAAS,QAAQ;wBAAK;oBAC1D;gBACF;gBACA,SAAS,YAAY,GAAG;oBACtB,QAAQ;oBACR,UAAU;oBACV,UAAU;oBACV,WAAW,IAAI;oBACf,SAAS,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;oBACnD,UAAU;oBACV,WAAW;oBACX,UAAU;wBAAC;wBAAmB;wBAAc;wBAAqB;qBAAe;gBAClF;YACF;YAEA,aAAa;YACb,MAAM,UAAuB;gBAC3B,OAAO,gBAAgB,KAAK,GAAG;gBAC/B,cAAc,wBAAwB,KAAK,GAAG;gBAC9C,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK;gBAChD,MAAM;YACR;YAEA,IAAI,YAAY,UAAU,EAAE;gBAC1B,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;YACrD;YAEA,SAAS;gBAAE,MAAM;gBAAiB,SAAS;YAAS;QACtD,EAAE,OAAO,OAAO;YACd,SAAS;gBAAE,MAAM;gBAAiB,SAAS;YAAsB;QACnE;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,SAAS;YAAE,MAAM;QAAc;QAE/B,IAAI;YACF,kBAAkB;YAClB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,yBAAyB;YACzB,IAAI,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;gBAC1C,MAAM,IAAI,MAAM;YAClB;YAEA,oBAAoB;YACpB,MAAM,UAAgB;gBACpB,IAAI,UAAU,KAAK,GAAG;gBACtB,OAAO,KAAK,KAAK;gBACjB,OAAO,KAAK,KAAK;gBACjB,MAAM,KAAK,IAAI;gBACf,UAAU,KAAK,QAAQ;gBACvB,YAAY;gBACZ,WAAW,IAAI;gBACf,WAAW,IAAI;gBACf,OAAO;oBACL,UAAU;oBACV,WAAW;oBACX,YAAY;oBACZ,YAAY;oBACZ,eAAe;oBACf,iBAAiB;gBACnB;gBACA,UAAU;oBACR,UAAU;oBACV,eAAe;wBACb,OAAO;wBACP,KAAK;wBACL,MAAM;wBACN,WAAW;oBACb;oBACA,SAAS;wBACP,WAAW;wBACX,WAAW;wBACX,eAAe;wBACf,kBAAkB;oBACpB;oBACA,aAAa;wBACX,UAAU;wBACV,OAAO;wBACP,UAAU;oBACZ;gBACF;gBACA,gBAAgB,KAAK,cAAc;gBACnC,cAAc,KAAK,YAAY;gBAC/B,sBAAsB,KAAK,oBAAoB;YACjD;YAEA,SAAS;gBAAE,MAAM;gBAAiB,SAAS;YAAQ;QACrD,EAAE,OAAO,OAAO;YACd,SAAS;gBAAE,MAAM;gBAAiB,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAAsB;QAC5G;IACF;IAEA,MAAM,SAAS;QACb,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,SAAS;YAAE,MAAM;QAAS;IAC5B;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,SAAS;gBAAE,MAAM;gBAAkB,SAAS;YAAQ;YAEpD,wBAAwB;YACxB,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,IAAI,aAAa;gBACf,MAAM,UAAuB,KAAK,KAAK,CAAC;gBACxC,QAAQ,IAAI,GAAG;oBAAE,GAAG,QAAQ,IAAI;oBAAE,GAAG,OAAO;gBAAC;gBAC7C,aAAa,OAAO,CAAC,eAAe,KAAK,SAAS,CAAC;YACrD;QACF,EAAE,OAAO,OAAO;YACd,SAAS;gBAAE,MAAM;gBAAa,SAAS;YAA4B;QACrE;IACF;IAEA,MAAM,eAAe;QACnB,4BAA4B;QAC5B,IAAI;YACF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACjD,eAAe;QACjB,EAAE,OAAO,OAAO;YACd;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD;QACF,EAAE,OAAO,OAAO;YACd,SAAS;gBAAE,MAAM;gBAAa,SAAS;YAAoB;QAC7D;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QACnB,OAAO;YACL,GAAG,KAAK;YACR;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 3842, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3848, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/Logo.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\n\ninterface LogoProps {\n  variant?: 'white' | 'transparent';\n  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'loading';\n  className?: string;\n  showText?: boolean;\n  href?: string;\n  textColor?: 'primary' | 'white' | 'gray';\n  isFooter?: boolean;\n}\n\nconst Logo = ({\n  variant = 'transparent',\n  size = 'md',\n  className = '',\n  showText = true,\n  href = '/',\n  textColor = 'primary',\n  isFooter = false\n}: LogoProps) => {\n  const [imageError, setImageError] = useState(false);\n  const sizeClasses = {\n    xs: 'h-6 w-auto',\n    sm: 'h-8 w-auto',\n    md: 'h-10 w-auto',\n    lg: 'h-12 w-auto',\n    xl: 'h-24 w-auto',\n    loading: 'h-32 w-auto'\n  };\n\n  const textSizeClasses = {\n    xs: 'text-base',\n    sm: 'text-lg',\n    md: 'text-xl',\n    lg: 'text-2xl',\n    xl: 'text-3xl',\n    loading: 'text-4xl'\n  };\n\n  const textColorClasses = {\n    primary: 'text-green-600',\n    white: 'text-white',\n    gray: 'text-gray-300',\n    yellow: 'text-yellow-400'\n  };\n\n  // مسار الشعار حسب النوع\n  const logoSrc = variant === 'white'\n    ? '/images/Adsız tasarım (3)_page-0001-Photoroom.png'\n    : '/images/Adsız tasarım (3)_page-0001-Photoroom.png';\n\n  const LogoContent = () => (\n    <div className={`flex items-center gap-3 ${className}`}>\n      {!imageError ? (\n        <Image\n          src={logoSrc}\n          alt=\"من المالك\"\n          width={120}\n          height={60}\n          className={`${sizeClasses[size]} object-contain`}\n          priority\n          onError={() => setImageError(true)}\n        />\n      ) : (\n        // Fallback logo\n        <div className={`${sizeClasses[size]} bg-primary-600 rounded-lg flex items-center justify-center text-white font-bold`}>\n          <span className=\"text-lg\">🤝</span>\n        </div>\n      )}\n      {showText && (\n        <div className=\"hidden sm:block\">\n          <div\n            className={`font-bold ${textSizeClasses[size]}`}\n            style={{\n              fontFamily: 'Cairo, sans-serif',\n              color: textColor === 'white' ? '#fde047' :\n                     textColor === 'yellow' ? '#fde047' : '#10b981'\n            }}\n          >\n            من المالك\n          </div>\n          <div\n            className={`text-xs text-gray-600 mt-0.5 ${isFooter ? '-ml-4' : '-ml-8'}`}\n            style={{\n              fontFamily: 'Cairo, sans-serif'\n            }}\n          >\n         موقع الإعلانات المبوبة\n          </div>\n        </div>\n      )}\n    </div>\n  );\n\n  if (href) {\n    return (\n      <Link\n        href={href}\n        className=\"flex items-center\"\n        onClick={(e) => {\n          // منع التنقل إذا كان المستخدم في الصفحة الرئيسية بالفعل\n          if (typeof window !== 'undefined' && window.location.pathname === '/' && href === '/') {\n            e.preventDefault();\n          }\n        }}\n      >\n        <LogoContent />\n      </Link>\n    );\n  }\n\n  return <LogoContent />;\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAgBA,MAAM,OAAO,CAAC,EACZ,UAAU,aAAa,EACvB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,WAAW,IAAI,EACf,OAAO,GAAG,EACV,YAAY,SAAS,EACrB,WAAW,KAAK,EACN;IACV,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,SAAS;IACX;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,SAAS;IACX;IAEA,MAAM,mBAAmB;QACvB,SAAS;QACT,OAAO;QACP,MAAM;QACN,QAAQ;IACV;IAEA,wBAAwB;IACxB,MAAM,UAAU,YAAY,UACxB,sDACA;IAEJ,MAAM,cAAc,kBAClB,8OAAC;YAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;gBACnD,CAAC,2BACA,8OAAC,6HAAA,CAAA,UAAK;oBACJ,KAAK;oBACL,KAAI;oBACJ,OAAO;oBACP,QAAQ;oBACR,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,eAAe,CAAC;oBAChD,QAAQ;oBACR,SAAS,IAAM,cAAc;;;;;2BAG/B,gBAAgB;8BAChB,8OAAC;oBAAI,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,gFAAgF,CAAC;8BACpH,cAAA,8OAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;gBAG7B,0BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAW,CAAC,UAAU,EAAE,eAAe,CAAC,KAAK,EAAE;4BAC/C,OAAO;gCACL,YAAY;gCACZ,OAAO,cAAc,UAAU,YACxB,cAAc,WAAW,YAAY;4BAC9C;sCACD;;;;;;sCAGD,8OAAC;4BACC,WAAW,CAAC,6BAA6B,EAAE,WAAW,UAAU,SAAS;4BACzE,OAAO;gCACL,YAAY;4BACd;sCACD;;;;;;;;;;;;;;;;;;IAQT,IAAI,MAAM;QACR,qBACE,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAM;YACN,WAAU;YACV,SAAS,CAAC;gBACR,wDAAwD;gBACxD,uCAAuF;;gBAEvF;YACF;sBAEA,cAAA,8OAAC;;;;;;;;;;IAGP;IAEA,qBAAO,8OAAC;;;;;AACV;uCAEe"}}, {"offset": {"line": 3983, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3989, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/%D9%85%D8%B4%D8%B1%D9%88%D8%B9/MinAlmalek/min-almalek/src/components/LoadingScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { usePathname } from 'next/navigation';\nimport Logo from './Logo';\n\ninterface LoadingScreenProps {\n  isLoading: boolean;\n  onComplete?: () => void;\n}\n\nconst LoadingScreen = ({ isLoading, onComplete }: LoadingScreenProps) => {\n  const [progress, setProgress] = useState(0);\n  const [isVisible, setIsVisible] = useState(isLoading);\n\n  useEffect(() => {\n    if (isLoading) {\n      setIsVisible(true);\n      setProgress(0);\n\n      // محاكاة تقدم التحميل السريع\n      const interval = setInterval(() => {\n        setProgress(prev => {\n          if (prev >= 100) {\n            clearInterval(interval);\n            // تأخير قصير جداً قبل إخفاء الشاشة\n            setTimeout(() => {\n              setIsVisible(false);\n              onComplete?.();\n            }, 100);\n            return 100;\n          }\n          return prev + Math.random() * 50; // زيادة سرعة التقدم\n        });\n      }, 50); // تقليل الفترة الزمنية\n\n      return () => clearInterval(interval);\n    }\n  }, [isLoading, onComplete]);\n\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-30 z-50 flex items-center justify-center backdrop-blur-sm\">\n      {/* محتوى شاشة التحميل - الشعار فقط */}\n      <div className=\"flex flex-col items-center justify-center\">\n        {/* الشعار مع إضاءة قوية شفافة */}\n        <div className=\"relative\">\n          {/* إضاءة شفافة قوية متعددة الطبقات */}\n          <div className=\"absolute inset-0 -m-20\">\n            <div className=\"w-80 h-80 bg-white/25 rounded-full blur-3xl animate-pulse\"></div>\n          </div>\n          <div className=\"absolute inset-0 -m-16\">\n            <div className=\"w-64 h-64 bg-white/35 rounded-full blur-2xl animate-pulse\" style={{ animationDelay: '0.3s' }}></div>\n          </div>\n          <div className=\"absolute inset-0 -m-12\">\n            <div className=\"w-48 h-48 bg-white/45 rounded-full blur-xl animate-pulse\" style={{ animationDelay: '0.6s' }}></div>\n          </div>\n          <div className=\"absolute inset-0 -m-8\">\n            <div className=\"w-32 h-32 bg-white/55 rounded-full blur-lg animate-pulse\" style={{ animationDelay: '0.9s' }}></div>\n          </div>\n          <div className=\"absolute inset-0 -m-4\">\n            <div className=\"w-24 h-24 bg-white/40 rounded-full blur-md animate-pulse\" style={{ animationDelay: '1.2s' }}></div>\n          </div>\n\n          {/* الشعار مع إضاءة قوية */}\n          <div className=\"relative z-10\">\n            <div className=\"transform transition-all duration-1000 ease-in-out animate-pulse\">\n              <div className=\"filter drop-shadow-2xl\" style={{ filter: 'drop-shadow(0 0 20px rgba(255, 255, 255, 0.8)) drop-shadow(0 0 40px rgba(255, 255, 255, 0.6)) drop-shadow(0 0 60px rgba(255, 255, 255, 0.4))' }}>\n                <Logo variant=\"transparent\" size=\"loading\" showText={false} />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// مكون لإدارة التنقل مع شاشة التحميل\nexport const NavigationLoader = () => {\n  const pathname = usePathname();\n  const [isLoading, setIsLoading] = useState(false);\n  const [previousPath, setPreviousPath] = useState(pathname);\n  const [isMounted, setIsMounted] = useState(false);\n\n  useEffect(() => {\n    setIsMounted(true);\n  }, []);\n\n  useEffect(() => {\n    if (isMounted && pathname !== previousPath) {\n      setIsLoading(true);\n      setPreviousPath(pathname);\n    }\n  }, [pathname, previousPath, isMounted]);\n\n  const handleLoadingComplete = () => {\n    setIsLoading(false);\n  };\n\n  if (!isMounted) {\n    return null;\n  }\n\n  return (\n    <LoadingScreen\n      isLoading={isLoading}\n      onComplete={handleLoadingComplete}\n    />\n  );\n};\n\nexport default LoadingScreen;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAWA,MAAM,gBAAgB,CAAC,EAAE,SAAS,EAAE,UAAU,EAAsB;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,aAAa;YACb,YAAY;YAEZ,6BAA6B;YAC7B,MAAM,WAAW,YAAY;gBAC3B,YAAY,CAAA;oBACV,IAAI,QAAQ,KAAK;wBACf,cAAc;wBACd,mCAAmC;wBACnC,WAAW;4BACT,aAAa;4BACb;wBACF,GAAG;wBACH,OAAO;oBACT;oBACA,OAAO,OAAO,KAAK,MAAM,KAAK,IAAI,oBAAoB;gBACxD;YACF,GAAG,KAAK,uBAAuB;YAE/B,OAAO,IAAM,cAAc;QAC7B;IACF,GAAG;QAAC;QAAW;KAAW;IAE1B,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;sBAEb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAA4D,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;;;;;;kCAE7G,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAA2D,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;;;;;;kCAE5G,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAA2D,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;;;;;;kCAE5G,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAA2D,OAAO;gCAAE,gBAAgB;4BAAO;;;;;;;;;;;kCAI5G,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAyB,OAAO;oCAAE,QAAQ;gCAA+I;0CACtM,cAAA,8OAAC,0HAAA,CAAA,UAAI;oCAAC,SAAQ;oCAAc,MAAK;oCAAU,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrE;AAGO,MAAM,mBAAmB;IAC9B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;IACf,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,aAAa,cAAc;YAC1C,aAAa;YACb,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAU;QAAc;KAAU;IAEtC,MAAM,wBAAwB;QAC5B,aAAa;IACf;IAEA,IAAI,CAAC,WAAW;QACd,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW;QACX,YAAY;;;;;;AAGlB;uCAEe"}}, {"offset": {"line": 4204, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}