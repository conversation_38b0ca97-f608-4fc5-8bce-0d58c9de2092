'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import { DataService } from '@/lib/data';
import type { Ad } from '@/lib/data';

export default function LiveUpdatesPage() {
  const [featuredAds, setFeaturedAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [isLive, setIsLive] = useState(true);

  useEffect(() => {
    loadFeaturedAds();
    
    // تحديث كل 30 ثانية
    const interval = setInterval(() => {
      if (isLive) {
        loadFeaturedAds();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [isLive]);

  const loadFeaturedAds = async () => {
    setLoading(true);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const result = DataService.getAds({
      page: 1,
      limit: 12,
      sortBy: 'newest',
      featured: true // فقط الإعلانات المميزة
    });
    
    setFeaturedAds(result.ads.filter(ad => ad.featured));
    setLastUpdate(new Date());
    setLoading(false);
  };

  const formatLastUpdate = (date: Date) => {
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const stats = {
    totalLiveAds: 1247,
    featuredAds: 892,
    newToday: 156,
    activeUsers: 2840
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* العنوان والحالة المباشرة */}
        <div className="mb-8">
          <div className="text-center mb-6">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className={`w-3 h-3 rounded-full ${isLive ? 'bg-red-500 animate-pulse' : 'bg-gray-400'}`}></div>
              <h1 className="text-4xl font-bold text-gray-800">
                📡 مشاهدة مباشرة
              </h1>
              <div className={`w-3 h-3 rounded-full ${isLive ? 'bg-red-500 animate-pulse' : 'bg-gray-400'}`}></div>
            </div>
            <p className="text-lg text-gray-600 mb-4">
              تابع أحدث الإعلانات المميزة لحظة بلحظة
            </p>
            <div className="flex items-center justify-center gap-4 text-sm text-gray-500">
              <span>آخر تحديث: {formatLastUpdate(lastUpdate)}</span>
              <button
                onClick={() => setIsLive(!isLive)}
                className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                  isLive 
                    ? 'bg-red-100 text-red-700 hover:bg-red-200' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {isLive ? '🔴 مباشر' : '⏸️ متوقف'}
              </button>
            </div>
          </div>

          {/* إحصائيات مباشرة */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
            <div className="bg-white rounded-lg p-4 text-center shadow-sm border border-red-200">
              <div className="text-2xl font-bold text-red-600">{stats.totalLiveAds.toLocaleString()}</div>
              <div className="text-sm text-gray-600">إعلان مباشر</div>
              <div className="w-2 h-2 bg-red-500 rounded-full mx-auto mt-1 animate-pulse"></div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center shadow-sm border border-yellow-200">
              <div className="text-2xl font-bold text-yellow-600">{stats.featuredAds.toLocaleString()}</div>
              <div className="text-sm text-gray-600">إعلان مميز</div>
              <div className="w-2 h-2 bg-yellow-500 rounded-full mx-auto mt-1 animate-pulse"></div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center shadow-sm border border-green-200">
              <div className="text-2xl font-bold text-green-600">{stats.newToday.toLocaleString()}</div>
              <div className="text-sm text-gray-600">جديد اليوم</div>
              <div className="w-2 h-2 bg-green-500 rounded-full mx-auto mt-1 animate-pulse"></div>
            </div>
            <div className="bg-white rounded-lg p-4 text-center shadow-sm border border-blue-200">
              <div className="text-2xl font-bold text-blue-600">{stats.activeUsers.toLocaleString()}</div>
              <div className="text-sm text-gray-600">مستخدم نشط</div>
              <div className="w-2 h-2 bg-blue-500 rounded-full mx-auto mt-1 animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* شريط التحكم */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6 border-l-4 border-l-red-500">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
              <span className="font-medium text-gray-800">
                عرض الإعلانات المميزة المباشرة
              </span>
              <span className="text-sm text-gray-500">
                ({featuredAds.length} إعلان)
              </span>
            </div>
            
            <div className="flex items-center gap-4">
              <button
                onClick={loadFeaturedAds}
                disabled={loading}
                className="px-4 py-2 bg-primary-600 text-white rounded-lg text-sm font-medium hover:bg-primary-700 transition-colors disabled:opacity-50"
              >
                {loading ? '🔄 جاري التحديث...' : '🔄 تحديث الآن'}
              </button>
              
              <div className="text-sm text-gray-500">
                تحديث تلقائي كل 30 ثانية
              </div>
            </div>
          </div>
        </div>

        {/* الإعلانات المميزة المباشرة */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                <div className="h-48 bg-gray-200"></div>
                <div className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : featuredAds.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {featuredAds.map((ad, index) => (
                <div
                  key={ad.id}
                  className="relative"
                  style={{
                    animationDelay: `${index * 0.1}s`
                  }}
                >
                  {/* شارة المباشر */}
                  <div className="absolute top-2 right-2 z-10 bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center gap-1">
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    مباشر
                  </div>
                  
                  {/* شارة مميز */}
                  {ad.featured && (
                    <div className="absolute top-2 left-2 z-10 bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                      ⭐ مميز
                    </div>
                  )}
                  
                  <AdCard 
                    ad={ad} 
                    viewMode="grid"
                  />
                </div>
              ))}
            </div>

            {/* معلومات إضافية */}
            <div className="mt-8 bg-gradient-to-r from-red-500 to-pink-600 rounded-xl text-white p-6">
              <div className="text-center">
                <h3 className="text-xl font-bold mb-2">📡 البث المباشر</h3>
                <p className="text-red-100 mb-4">
                  تتم مراقبة الإعلانات وتحديثها تلقائياً كل 30 ثانية لضمان عرض أحدث المحتوى
                </p>
                <div className="flex items-center justify-center gap-6 text-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    <span>تحديث مباشر</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>⭐</span>
                    <span>إعلانات مميزة فقط</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>🔄</span>
                    <span>تحديث كل 30 ثانية</span>
                  </div>
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📡</div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد إعلانات مباشرة</h3>
            <p className="text-gray-600 mb-6">لا توجد إعلانات مميزة متاحة للعرض المباشر حالياً</p>
            <button
              onClick={loadFeaturedAds}
              className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
            >
              🔄 إعادة تحميل
            </button>
          </div>
        )}

        {/* دعوة للاشتراك */}
        <div className="mt-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl text-white p-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-4">⭐ اجعل إعلانك مميزاً</h2>
            <p className="text-purple-100 mb-6">
              اشترك في إحدى باقاتنا المميزة لتظهر إعلاناتك في البث المباشر وتحصل على مشاهدات أكثر
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/subscription"
                className="bg-white text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-purple-50 transition-colors"
              >
                عرض الباقات المميزة
              </a>
              <a
                href="/add-ad"
                className="bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors border border-purple-400"
              >
                أضف إعلانك الآن
              </a>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
