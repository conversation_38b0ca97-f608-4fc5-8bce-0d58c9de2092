import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/data';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adId = parseInt(params.id);
    
    if (isNaN(adId)) {
      return NextResponse.json(
        { success: false, error: 'معرف الإعلان غير صحيح' },
        { status: 400 }
      );
    }

    const ad = DataService.getAdById(adId);
    
    if (!ad) {
      return NextResponse.json(
        { success: false, error: 'الإعلان غير موجود' },
        { status: 404 }
      );
    }

    // زيادة عدد المشاهدات (في التطبيق الحقيقي ستحفظ في قاعدة البيانات)
    ad.views += 1;

    // الحصول على إعلانات مشابهة
    const similarAds = DataService.getAdsByCategory(ad.category)
      .filter(similarAd => similarAd.id !== ad.id)
      .slice(0, 4);

    return NextResponse.json({
      success: true,
      data: {
        ad,
        similarAds
      }
    });

  } catch (error) {
    console.error('خطأ في جلب الإعلان:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في جلب الإعلان' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adId = parseInt(params.id);
    const updateData = await request.json();
    
    if (isNaN(adId)) {
      return NextResponse.json(
        { success: false, error: 'معرف الإعلان غير صحيح' },
        { status: 400 }
      );
    }

    const ad = DataService.getAdById(adId);
    
    if (!ad) {
      return NextResponse.json(
        { success: false, error: 'الإعلان غير موجود' },
        { status: 404 }
      );
    }

    // في التطبيق الحقيقي، ستحديث البيانات في قاعدة البيانات
    const updatedAd = {
      ...ad,
      ...updateData,
      updatedAt: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      data: updatedAd,
      message: 'تم تحديث الإعلان بنجاح'
    });

  } catch (error) {
    console.error('خطأ في تحديث الإعلان:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في تحديث الإعلان' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const adId = parseInt(params.id);
    
    if (isNaN(adId)) {
      return NextResponse.json(
        { success: false, error: 'معرف الإعلان غير صحيح' },
        { status: 400 }
      );
    }

    const ad = DataService.getAdById(adId);
    
    if (!ad) {
      return NextResponse.json(
        { success: false, error: 'الإعلان غير موجود' },
        { status: 404 }
      );
    }

    // في التطبيق الحقيقي، ستحذف الإعلان من قاعدة البيانات
    
    return NextResponse.json({
      success: true,
      message: 'تم حذف الإعلان بنجاح'
    });

  } catch (error) {
    console.error('خطأ في حذف الإعلان:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في حذف الإعلان' },
      { status: 500 }
    );
  }
}
