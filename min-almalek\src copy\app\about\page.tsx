import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      <Header />
      <main>
        {/* Hero Section */}
        <section className="bg-gradient-to-br from-primary-600 to-primary-800 text-white py-16">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">من نحن</h1>
            <p className="text-xl md:text-2xl text-primary-100 max-w-3xl mx-auto">
              نحن أكبر منصة للإعلانات المبوبة في سوريا، نربط بين المشترين والبائعين بطريقة آمنة وسهلة
            </p>
          </div>
        </section>

        {/* قصتنا */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold text-gray-800 mb-6">قصتنا</h2>
                <p className="text-gray-700 leading-relaxed mb-6">
                  بدأت فكرة "من المالك" في عام 2020 عندما لاحظنا الحاجة الماسة لمنصة موثوقة وسهلة الاستخدام 
                  للإعلانات المبوبة في سوريا. كان هدفنا إنشاء مساحة آمنة حيث يمكن للأشخاص بيع وشراء كل ما يحتاجونه 
                  بثقة وراحة بال.
                </p>
                <p className="text-gray-700 leading-relaxed mb-6">
                  منذ ذلك الحين، نمت منصتنا لتصبح الوجهة الأولى للإعلانات المبوبة في سوريا، حيث نخدم آلاف المستخدمين 
                  يومياً ونساعدهم في العثور على ما يبحثون عنه أو بيع ما لا يحتاجونه.
                </p>
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-600 mb-2">50K+</div>
                    <div className="text-gray-600">مستخدم نشط</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-primary-600 mb-2">100K+</div>
                    <div className="text-gray-600">إعلان منشور</div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-200 rounded-xl h-96 flex items-center justify-center">
                <span className="text-gray-400 text-6xl">🏢</span>
              </div>
            </div>
          </div>
        </section>

        {/* رؤيتنا ورسالتنا */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="text-4xl mb-4">🎯</div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">رؤيتنا</h3>
                <p className="text-gray-700 leading-relaxed">
                  أن نكون المنصة الرائدة والأكثر ثقة للإعلانات المبوبة في المنطقة، حيث نوفر تجربة استثنائية 
                  للمستخدمين ونساهم في تنمية الاقتصاد المحلي من خلال تسهيل عمليات البيع والشراء.
                </p>
              </div>
              
              <div className="bg-white rounded-xl shadow-lg p-8">
                <div className="text-4xl mb-4">🚀</div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">رسالتنا</h3>
                <p className="text-gray-700 leading-relaxed">
                  نسعى لتوفير منصة آمنة وسهلة الاستخدام تربط بين المشترين والبائعين، مع التركيز على الجودة 
                  والأمان وخدمة العملاء المتميزة، لنساعد الجميع في العثور على ما يحتاجونه بأفضل الأسعار.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* قيمنا */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-gray-800 text-center mb-12">قيمنا</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🛡️</span>
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">الأمان والثقة</h3>
                <p className="text-gray-600">
                  نضع أمان مستخدمينا في المقدمة ونعمل على توفير بيئة آمنة للتعاملات
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">⚡</span>
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">السرعة والكفاءة</h3>
                <p className="text-gray-600">
                  نوفر تجربة سريعة وسهلة للعثور على الإعلانات ونشرها
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🤝</span>
                </div>
                <h3 className="text-xl font-bold text-gray-800 mb-3">خدمة العملاء</h3>
                <p className="text-gray-600">
                  نقدم دعماً فنياً متميزاً ونساعد مستخدمينا في كل خطوة
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* فريق العمل */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-gray-800 text-center mb-12">فريق العمل</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                { name: 'أحمد محمد', role: 'المدير التنفيذي', image: '👨‍💼' },
                { name: 'سارة أحمد', role: 'مديرة التطوير', image: '👩‍💻' },
                { name: 'محمد علي', role: 'مدير التسويق', image: '👨‍💼' }
              ].map((member, index) => (
                <div key={index} className="bg-white rounded-xl shadow-lg p-6 text-center">
                  <div className="text-6xl mb-4">{member.image}</div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">{member.name}</h3>
                  <p className="text-gray-600">{member.role}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* الإحصائيات */}
        <section className="py-16 bg-primary-600 text-white">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">إنجازاتنا بالأرقام</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold mb-2">50,000+</div>
                <div className="text-primary-100">مستخدم مسجل</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">100,000+</div>
                <div className="text-primary-100">إعلان منشور</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">14</div>
                <div className="text-primary-100">محافظة مغطاة</div>
              </div>
              <div>
                <div className="text-4xl font-bold mb-2">24/7</div>
                <div className="text-primary-100">دعم فني</div>
              </div>
            </div>
          </div>
        </section>

        {/* اتصل بنا */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold text-gray-800 mb-6">تواصل معنا</h2>
            <p className="text-xl text-gray-600 mb-8">
              هل لديك أسئلة أو اقتراحات؟ نحن هنا للمساعدة
            </p>
            <div className="flex flex-col md:flex-row gap-4 justify-center">
              <a
                href="mailto:<EMAIL>"
                className="bg-primary-600 text-white px-8 py-3 rounded-lg hover:bg-primary-700 transition-colors"
              >
                راسلنا عبر البريد الإلكتروني
              </a>
              <a
                href="tel:+963-11-123-4567"
                className="border border-primary-600 text-primary-600 px-8 py-3 rounded-lg hover:bg-primary-50 transition-colors"
              >
                اتصل بنا
              </a>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
