'use client';

import React from 'react';

interface CategoryIconProps {
  category: string;
  className?: string;
  color?: string;
  isHovered?: boolean;
  isPressed?: boolean;
}

const CategoryIcon: React.FC<CategoryIconProps> = ({
  category,
  className = "w-6 h-6",
  color = "currentColor",
  isHovered = false,
  isPressed = false
}) => {

  // تحديد ألوان الإضاءة لكل فئة
  const glowColors: { [key: string]: string } = {
    'real-estate': '#3b82f6', // أزرق
    'cars': '#ef4444', // أحمر
    'electronics': '#8b5cf6', // بنفسجي
    'jobs': '#10b981', // أخضر
    'offers-discounts': '#ea580c', // برتقالي محمر
    'business-plans': '#f59e0b', // أصفر
    'services': '#f97316', // برتقالي
    'fashion': '#ec4899', // وردي
    'furniture': '#eab308', // أصفر
    'sports': '#6366f1', // نيلي
    'books': '#14b8a6', // تركوازي
    'pets': '#f59e0b', // كهرماني
    'health': '#10b981', // أخضر زمردي
    'food': '#f43f5e', // وردي أحمر
    'tourism': '#0ea5e9' // أزرق سماوي
  };

  const currentGlowColor = glowColors[category] || '#10b981';

  // تحديد الشفافية والتأثيرات - تحسين للموبايل
  const opacity = isPressed ? '1' : isHovered ? '0.9' : '0.8';
  const glowIntensity = isPressed ? '15px' : isHovered ? '8px' : '3px';
  const glowSpread = isPressed ? '5px' : isHovered ? '2px' : '1px';

  const iconStyle = {
    opacity,
    filter: `drop-shadow(0 0 ${glowIntensity} ${currentGlowColor}) drop-shadow(0 0 ${glowSpread} ${currentGlowColor}40) brightness(1.1) contrast(1.1)`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    transform: isPressed ? 'scale(0.9)' : isHovered ? 'scale(1.1)' : 'scale(1)',
    WebkitFilter: `drop-shadow(0 0 ${glowIntensity} ${currentGlowColor}) drop-shadow(0 0 ${glowSpread} ${currentGlowColor}40) brightness(1.1) contrast(1.1)`,
    // تحسينات إضافية للموبايل
    imageRendering: 'crisp-edges',
    WebkitImageRendering: 'crisp-edges'
  };
  const iconMap: { [key: string]: JSX.Element } = {
    'real-estate': (
      <svg className={className} fill={color} viewBox="0 0 24 24" style={iconStyle}>
        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" fillOpacity="0.4"/>
        <path d="M12 7.69l4 3.09V18h-2v-4H10v4H8v-7.22l4-3.09z" fillOpacity="0.3"/>
      </svg>
    ),
    'cars': (
      <svg className={className} fill={color} viewBox="0 0 24 24" style={iconStyle}>
        <path d="M18.92 6.01C18.72 5.42 18.16 5 17.5 5h-11c-.66 0-1.22.42-1.42 1.01L3 12v8c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-1h12v1c0 .55.45 1 1 1h1c.55 0 1-.45 1-1v-8l-2.08-5.99zM6.5 16c-.83 0-1.5-.67-1.5-1.5S5.67 13 6.5 13s1.5.67 1.5 1.5S7.33 16 6.5 16zm11 0c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zM5 11l1.5-4.5h11L19 11H5z" fillOpacity="0.4"/>
      </svg>
    ),
    'electronics': (
      <svg className={className} fill={color} viewBox="0 0 24 24" style={iconStyle}>
        <path d="M17 1.01L7 1c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM17 19H7V5h10v14z" fillOpacity="0.4"/>
        <circle cx="12" cy="17" r="1" fillOpacity="0.5"/>
        <rect x="9" y="7" width="6" height="8" rx="1" fillOpacity="0.3"/>
      </svg>
    ),
    'jobs': (
      <svg className={className} fill={color} viewBox="0 0 24 24" style={iconStyle}>
        <path d="M20 6h-2.5l-1.5-1.5h-5L9.5 6H7c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h13c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-8 3h4v2h-4V9z" fillOpacity="0.4"/>
        <path d="M10 4V2h4v2h-4z" fillOpacity="0.5"/>
      </svg>
    ),
    'offers-discounts': (
      <img
        src="/images/الخدمات/الخدمات.png"
        alt="العروض والتخفيضات"
        className={className}
        style={iconStyle}
      />
    ),
    'business-plans': (
      <svg className={className} fill={color} viewBox="0 0 24 24" style={iconStyle}>
        <path d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 21 12 17.77 5.82 21 7 13.87 2 9l6.91-.74L12 2z" fillOpacity="0.4"/>
        <circle cx="12" cy="12" r="3" fillOpacity="0.5"/>
      </svg>
    ),
    'services': (
      <svg className={className} fill={color} viewBox="0 0 24 24" style={iconStyle}>
        <path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z" fillOpacity="0.4"/>
      </svg>
    ),
    'fashion': (
      <img
        src="/images/clothes/transparent-Photoroom (4).v2.png"
        alt="الأزياء والموضة"
        className={className}
        style={iconStyle}
      />
    ),
    'fashion-shirt-mirror': (
      <div className="flex items-center justify-center gap-0.5" style={iconStyle}>
        {/* قميص */}
        <svg className="w-3 h-3" fill={color} viewBox="0 0 24 24">
          <path d="M16,4H15V2H9V4H8A1,1 0 0,0 7,5V11L9,13V22H15V13L17,11V5A1,1 0 0,0 16,4Z" fillOpacity="0.8"/>
        </svg>
        {/* مرآة */}
        <svg className="w-3 h-3" fill={color} viewBox="0 0 24 24">
          <path d="M12,2A7,7 0 0,1 19,9V15A7,7 0 0,1 12,22A7,7 0 0,1 5,15V9A7,7 0 0,1 12,2M12,4A5,5 0 0,0 7,9V15A5,5 0 0,0 12,20A5,5 0 0,0 17,15V9A5,5 0 0,0 12,4M12,6A3,3 0 0,1 15,9V15A3,3 0 0,1 12,18A3,3 0 0,1 9,15V9A3,3 0 0,1 12,6Z" fillOpacity="0.6"/>
        </svg>
      </div>
    ),
    'furniture': (
      <svg className={className} fill={color} viewBox="0 0 24 24" style={iconStyle}>
        <path d="M7 13c1.66 0 3-1.34 3-3S8.66 7 7 7s-3 1.34-3 3 1.34 3 3 3zm12-6h-8v7H3V6H1v15h2v-3h18v3h2v-9c0-2.21-1.79-4-4-4z" fillOpacity="0.4"/>
        <rect x="5" y="15" width="14" height="2" fillOpacity="0.5"/>
      </svg>
    ),
    'sports': (
      <svg className={className} fill={color} viewBox="0 0 24 24" style={iconStyle}>
        <circle cx="12" cy="12" r="10" fillOpacity="0.4"/>
        <path d="M8 8c2.5 2.5 5.5 2.5 8 0" fill="none" stroke="white" strokeWidth="1" opacity="0.5"/>
        <path d="M8 16c2.5-2.5 5.5-2.5 8 0" fill="none" stroke="white" strokeWidth="1" opacity="0.5"/>
        <path d="M8 8c0 2.5 0 5.5 0 8" fill="none" stroke="white" strokeWidth="1" opacity="0.5"/>
        <path d="M16 8c0 2.5 0 5.5 0 8" fill="none" stroke="white" strokeWidth="1" opacity="0.5"/>
        <path d="M12 6v12" fill="none" stroke="white" strokeWidth="1" opacity="0.5"/>
        <path d="M6 12h12" fill="none" stroke="white" strokeWidth="1" opacity="0.5"/>
      </svg>
    ),
    'books': (
      <svg className={className} fill={color} viewBox="0 0 24 24" style={iconStyle}>
        <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z" fillOpacity="0.3"/>
        <rect x="8" y="14" width="8" height="1" fillOpacity="0.4"/>
        <rect x="8" y="16" width="6" height="1" fillOpacity="0.4"/>
      </svg>
    ),
    'pets': (
      <svg className={className} fill={color} viewBox="0 0 24 24" style={iconStyle}>
        <circle cx="4.5" cy="9.5" r="2.5" fillOpacity="0.4"/>
        <circle cx="9" cy="5.5" r="2.5" fillOpacity="0.4"/>
        <circle cx="15" cy="5.5" r="2.5" fillOpacity="0.4"/>
        <circle cx="19.5" cy="9.5" r="2.5" fillOpacity="0.4"/>
        <path d="M17.34 14.86c-.87-1.02-1.6-1.89-2.48-2.91-.46-.54-1.05-1.08-1.75-1.32-.11-.04-.22-.07-.33-.09-.25-.04-.52-.04-.77-.01-.11.02-.22.05-.33.09-.7.24-1.29.78-1.75 1.32-.87 1.02-1.6 1.89-2.48 2.91-1.31 1.31-2.92 2.76-2.62 4.79.29 1.02 1.02 2.03 2.33 2.32.73.15 3.06-.44 5.54-.44h.18c2.48 0 4.81.59 5.54.44 1.31-.29 2.04-1.31 2.33-2.32.3-2.03-1.31-3.48-2.62-4.79z" fillOpacity="0.3"/>
      </svg>
    ),
    'health': (
      <img
        src="/images/Beautiful and Health/transparent-Photoroom-Photoroom (1).png"
        alt="الصحة والجمال"
        className={className}
        style={iconStyle}
      />
    ),
    'food': (
      <svg className={className} fill={color} viewBox="0 0 24 24" style={iconStyle}>
        <path d="M11 9H9V2H7v7H5V2H3v7c0 2.12 1.66 3.84 3.75 3.97V22h2.5v-9.03C11.34 12.84 13 11.12 13 9V2h-2v7z" fillOpacity="0.4"/>
        <path d="M16 6v8h2.5v8H21V2c-2.76 0-5 2.24-5 4z" fillOpacity="0.4"/>
      </svg>
    ),
    'tourism': (
      <img
        src="/images/السياحة/Tourism.png"
        alt="السياحة والسفر"
        className={className}
        style={iconStyle}
      />
    )
  };

  return iconMap[category] || (
    <svg className={className} fill={color} viewBox="0 0 24 24" style={iconStyle}>
      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fillOpacity="0.4"/>
    </svg>
  );
};

export default CategoryIcon;
