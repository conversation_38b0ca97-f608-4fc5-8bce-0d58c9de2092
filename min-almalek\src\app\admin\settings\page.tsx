'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

interface Settings {
  features: {
    enableRegistration: boolean;
    enableComments: boolean;
    enableRatings: boolean;
    enableNotifications: boolean;
    enableChat: boolean;
  };
  limits: {
    maxImagesPerAd: number;
    maxAdDuration: number;
    maxFreeAdsPerMonth: number;
  };
  pricing: {
    featuredAdPrice: number;
    premiumAdPrice: number;
    businessPlanPrice: number;
    realEstateOfficePrice: number;
  };
  seo: {
    metaTitle: string;
    metaDescription: string;
    keywords: string;
  };
}

export default function SettingsPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [mounted, setMounted] = useState(false);
  const [settings, setSettings] = useState<Settings>({
    features: {
      enableRegistration: true,
      enableComments: true,
      enableRatings: true,
      enableNotifications: true,
      enableChat: true,
    },
    limits: {
      maxImagesPerAd: 10,
      maxAdDuration: 30,
      maxFreeAdsPerMonth: 5,
    },
    pricing: {
      featuredAdPrice: 5000,
      premiumAdPrice: 10000,
      businessPlanPrice: 50000,
      realEstateOfficePrice: 700000,
    },
    seo: {
      metaTitle: 'من المالك - منصة الإعلانات المبوبة في سوريا',
      metaDescription: 'منصة من المالك للإعلانات المبوبة في سوريا. بيع وشراء العقارات، السيارات، الوظائف وأكثر.',
      keywords: 'إعلانات مبوبة، سوريا، عقارات، سيارات، وظائف، من المالك',
    },
  });

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setMounted(true);
    // Check if user is admin
    if (!user || user.userType !== 'admin') {
      router.push('/');
      return;
    }
  }, [user, router]);

  const handleSave = async () => {
    setLoading(true);
    try {
      // Save settings to localStorage (in real app, this would be API call)
      localStorage.setItem('adminSettings', JSON.stringify({
        ...settings,
        lastUpdated: new Date().toISOString()
      }));

      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('✅ تم حفظ الإعدادات بنجاح');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('❌ حدث خطأ أثناء حفظ الإعدادات');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SY', {
      style: 'currency',
      currency: 'SYP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  if (!mounted || !user || user.userType !== 'admin') {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">الإعدادات المركزية للموقع</h1>
          <p className="text-gray-600">إدارة وتكوين جميع إعدادات المنصة</p>
        </div>

        <div className="space-y-6">
          {/* Site Configuration */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                  <span className="text-white text-2xl">⚙️</span>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-white">إعدادات النظام</h1>
                  <p className="text-primary-100">إدارة إعدادات الموقع والمميزات</p>
                </div>
              </div>

              <div className="flex flex-wrap items-center gap-3">
                <button className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors flex items-center gap-2">
                  <span>🔄</span>
                  <span>تحديث البيانات</span>
                </button>
                <button className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors flex items-center gap-2">
                  <span>📊</span>
                  <span>تقرير النظام</span>
                </button>
                <button className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors flex items-center gap-2">
                  <span>💾</span>
                  <span>نسخ احتياطي</span>
                </button>
              </div>
            </div>

            {/* مؤشرات الحالة السريعة */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-white/10 rounded-lg p-3 text-center">
                <div className="text-white text-lg font-bold">99.9%</div>
                <div className="text-primary-100 text-sm">وقت التشغيل</div>
              </div>
              <div className="bg-white/10 rounded-lg p-3 text-center">
                <div className="text-white text-lg font-bold">2.1GB</div>
                <div className="text-primary-100 text-sm">استخدام التخزين</div>
              </div>
              <div className="bg-white/10 rounded-lg p-3 text-center">
                <div className="text-white text-lg font-bold">156ms</div>
                <div className="text-primary-100 text-sm">سرعة الاستجابة</div>
              </div>
              <div className="bg-white/10 rounded-lg p-3 text-center">
                <div className="text-green-300 text-lg font-bold">✓ نشط</div>
                <div className="text-primary-100 text-sm">حالة النظام</div>
              </div>
            </div>

            {/* شريط التبويب الأفقي للإعدادات - مدمج في العنوان */}
            <div className="bg-white/10 rounded-lg p-4">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
                <button className="bg-white/20 border border-white/30 rounded-lg p-3 text-center hover:bg-white/30 transition-colors">
                  <div className="text-white text-2xl mb-1">🎯</div>
                  <div className="text-xs font-medium text-white">المميزات العامة</div>
                </button>

                <button className="bg-white/10 border border-white/20 rounded-lg p-3 text-center hover:bg-white/20 transition-colors">
                  <div className="text-white text-2xl mb-1">💰</div>
                  <div className="text-xs font-medium text-white/80">الأسعار والباقات</div>
                </button>

                <button className="bg-white/10 border border-white/20 rounded-lg p-3 text-center hover:bg-white/20 transition-colors">
                  <div className="text-white text-2xl mb-1">🔍</div>
                  <div className="text-xs font-medium text-white/80">SEO والتحسين</div>
                </button>

                <button className="bg-white/10 border border-white/20 rounded-lg p-3 text-center hover:bg-white/20 transition-colors">
                  <div className="text-white text-2xl mb-1">🔒</div>
                  <div className="text-xs font-medium text-white/80">الأمان</div>
                </button>

                <button className="bg-white/10 border border-white/20 rounded-lg p-3 text-center hover:bg-white/20 transition-colors">
                  <div className="text-white text-2xl mb-1">📧</div>
                  <div className="text-xs font-medium text-white/80">الإشعارات</div>
                </button>
              </div>
            </div>
          </div>

          {/* شريط الأدوات السريع */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex flex-wrap items-center justify-between gap-4">
              <div className="flex items-center gap-3">
                <span className="text-sm text-gray-600 font-medium">إعدادات سريعة:</span>
                <div className="flex items-center gap-2">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input type="checkbox" className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" defaultChecked />
                    <span className="text-sm text-gray-700">وضع الصيانة</span>
                  </label>
                </div>
                <div className="flex items-center gap-2">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input type="checkbox" className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" defaultChecked />
                    <span className="text-sm text-gray-700">التسجيل المفتوح</span>
                  </label>
                </div>
                <div className="flex items-center gap-2">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input type="checkbox" className="rounded border-gray-300 text-primary-600 focus:ring-primary-500" />
                    <span className="text-sm text-gray-700">وضع التطوير</span>
                  </label>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <button className="bg-blue-100 text-blue-700 px-4 py-2 rounded-lg text-sm hover:bg-blue-200 transition-colors flex items-center gap-2">
                  <span>📥</span>
                  <span>استيراد إعدادات</span>
                </button>
                <button className="bg-green-100 text-green-700 px-4 py-2 rounded-lg text-sm hover:bg-green-200 transition-colors flex items-center gap-2">
                  <span>📤</span>
                  <span>تصدير إعدادات</span>
                </button>
                <button className="bg-orange-100 text-orange-700 px-4 py-2 rounded-lg text-sm hover:bg-orange-200 transition-colors flex items-center gap-2">
                  <span>🔄</span>
                  <span>إعادة تعيين</span>
                </button>
              </div>
            </div>
          </div>

          {/* بطاقات إعدادات إضافية */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* بطاقة الحدود والقيود */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                  <span className="text-orange-600 text-xl">⚡</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">الحدود والقيود</h3>
                  <p className="text-sm text-gray-500">إدارة حدود النظام</p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الحد الأقصى للصور لكل إعلان
                  </label>
                  <input
                    type="number"
                    value={settings.limits.maxImagesPerAd}
                    onChange={(e) => setSettings({
                      ...settings,
                      limits: { ...settings.limits, maxImagesPerAd: parseInt(e.target.value) }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    مدة الإعلان (بالأيام)
                  </label>
                  <input
                    type="number"
                    value={settings.limits.maxAdDuration}
                    onChange={(e) => setSettings({
                      ...settings,
                      limits: { ...settings.limits, maxAdDuration: parseInt(e.target.value) }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الإعلانات المجانية شهرياً
                  </label>
                  <input
                    type="number"
                    value={settings.limits.maxFreeAdsPerMonth}
                    onChange={(e) => setSettings({
                      ...settings,
                      limits: { ...settings.limits, maxFreeAdsPerMonth: parseInt(e.target.value) }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  />
                </div>
              </div>
            </div>

            {/* بطاقة إعدادات SEO */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 text-xl">🔍</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">إعدادات SEO</h3>
                  <p className="text-sm text-gray-500">تحسين محركات البحث</p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    عنوان الموقع
                  </label>
                  <input
                    type="text"
                    value={settings.seo.metaTitle}
                    onChange={(e) => setSettings({
                      ...settings,
                      seo: { ...settings.seo, metaTitle: e.target.value }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    وصف الموقع
                  </label>
                  <textarea
                    value={settings.seo.metaDescription}
                    onChange={(e) => setSettings({
                      ...settings,
                      seo: { ...settings.seo, metaDescription: e.target.value }
                    })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الكلمات المفتاحية
                  </label>
                  <input
                    type="text"
                    value={settings.seo.keywords}
                    onChange={(e) => setSettings({
                      ...settings,
                      seo: { ...settings.seo, keywords: e.target.value }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  />
                </div>
              </div>
            </div>

            {/* بطاقة إعدادات النظام */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <span className="text-purple-600 text-xl">⚙️</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">إعدادات النظام</h3>
                  <p className="text-sm text-gray-500">تكوين عام للنظام</p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">وضع الصيانة</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">التسجيل المفتوح</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" defaultChecked />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">النسخ الاحتياطي التلقائي</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" defaultChecked />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                  </label>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">تسجيل الأنشطة</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" defaultChecked />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* إعدادات المميزات */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <div className="flex items-center gap-3">
                <span className="text-green-600 text-2xl">🔧</span>
                <div>
                  <h3 className="font-medium text-green-900">إعدادات المميزات</h3>
                  <p className="text-sm text-green-700">تحكم في المميزات المتاحة للمستخدمين</p>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.entries(settings.features).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {key === 'enableRegistration' ? 'تفعيل التسجيل' :
                         key === 'enableComments' ? 'تفعيل التعليقات' :
                         key === 'enableRatings' ? 'تفعيل التقييمات' :
                         key === 'enableNotifications' ? 'تفعيل الإشعارات' :
                         key === 'enableChat' ? 'تفعيل الدردشة' : key}
                      </h4>
                      <p className="text-sm text-gray-600">
                        {key === 'enableRegistration' ? 'السماح للمستخدمين بإنشاء حسابات جديدة' :
                         key === 'enableComments' ? 'السماح بالتعليق على الإعلانات' :
                         key === 'enableRatings' ? 'السماح بتقييم البائعين والمشترين' :
                         key === 'enableNotifications' ? 'إرسال إشعارات للمستخدمين' :
                         key === 'enableChat' ? 'تفعيل نظام الدردشة المباشرة' : ''}
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={value}
                        onChange={(e) => setSettings({
                          ...settings,
                          features: { ...settings.features, [key]: e.target.checked }
                        })}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* إعدادات الأسعار */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <div className="flex items-center gap-3">
                <span className="text-yellow-600 text-2xl">💰</span>
                <div>
                  <h3 className="font-medium text-yellow-900">إعدادات الأسعار</h3>
                  <p className="text-sm text-yellow-700">تحديد أسعار الخدمات والاشتراكات</p>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    سعر الإعلان المميز (ليرة سورية)
                  </label>
                  <input
                    type="number"
                    value={settings.pricing.featuredAdPrice}
                    onChange={(e) => setSettings({
                      ...settings,
                      pricing: { ...settings.pricing, featuredAdPrice: parseInt(e.target.value) }
                    })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    السعر الحالي: {formatCurrency(settings.pricing.featuredAdPrice)}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    سعر الإعلان الذهبي (ليرة سورية)
                  </label>
                  <input
                    type="number"
                    value={settings.pricing.premiumAdPrice}
                    onChange={(e) => setSettings({
                      ...settings,
                      pricing: { ...settings.pricing, premiumAdPrice: parseInt(e.target.value) }
                    })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    السعر الحالي: {formatCurrency(settings.pricing.premiumAdPrice)}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    اشتراك الشركات الشهري (ليرة سورية)
                  </label>
                  <input
                    type="number"
                    value={settings.pricing.businessPlanPrice}
                    onChange={(e) => setSettings({
                      ...settings,
                      pricing: { ...settings.pricing, businessPlanPrice: parseInt(e.target.value) }
                    })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    السعر الحالي: {formatCurrency(settings.pricing.businessPlanPrice)}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    اشتراك المكاتب العقارية (ليرة سورية)
                  </label>
                  <input
                    type="number"
                    value={settings.pricing.realEstateOfficePrice}
                    onChange={(e) => setSettings({
                      ...settings,
                      pricing: { ...settings.pricing, realEstateOfficePrice: parseInt(e.target.value) }
                    })}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    السعر الحالي: {formatCurrency(settings.pricing.realEstateOfficePrice)}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* زر الحفظ */}
          <div className="flex justify-end">
            <button
              onClick={handleSave}
              disabled={loading}
              className="bg-primary-600 text-white px-6 py-2 rounded-lg hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? 'جاري الحفظ...' : 'حفظ الإعدادات'}
            </button>
          </div>

          {/* إحصائيات النظام */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-center gap-3">
                <span className="text-blue-600 text-2xl">📊</span>
                <div>
                  <h3 className="font-medium text-blue-900">إحصائيات النظام</h3>
                  <p className="text-sm text-blue-700">نظرة عامة على أداء الموقع</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm">إجمالي الإعلانات</p>
                    <p className="text-2xl font-bold">1,247</p>
                    <p className="text-blue-200 text-xs">↗ +12% من الشهر الماضي</p>
                  </div>
                  <div className="text-3xl">📋</div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm">إعلانات نشطة</p>
                    <p className="text-2xl font-bold">1,156</p>
                    <p className="text-green-200 text-xs">منشورة حالياً</p>
                  </div>
                  <div className="text-3xl">✅</div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-yellow-100 text-sm">في الانتظار</p>
                    <p className="text-2xl font-bold">23</p>
                    <p className="text-yellow-200 text-xs">تحتاج مراجعة</p>
                  </div>
                  <div className="text-3xl">⏳</div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm">إجمالي المستخدمين</p>
                    <p className="text-2xl font-bold">3,456</p>
                    <p className="text-purple-200 text-xs">↗ +8% من الشهر الماضي</p>
                  </div>
                  <div className="text-3xl">👥</div>
                </div>
              </div>
            </div>
          </div>

          {/* قسم إعدادات إضافية */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* إعدادات البريد الإلكتروني */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                  <span className="text-red-600 text-xl">📧</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">إعدادات البريد الإلكتروني</h3>
                  <p className="text-sm text-gray-500">تكوين خادم البريد الإلكتروني</p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    خادم SMTP
                  </label>
                  <input
                    type="text"
                    defaultValue="smtp.gmail.com"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    منفذ SMTP
                  </label>
                  <input
                    type="number"
                    defaultValue="587"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    البريد الإلكتروني للإرسال
                  </label>
                  <input
                    type="email"
                    defaultValue="<EMAIL>"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">تفعيل SSL</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" defaultChecked />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                  </label>
                </div>
              </div>
            </div>

            {/* إعدادات التخزين */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <span className="text-indigo-600 text-xl">💾</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">إعدادات التخزين</h3>
                  <p className="text-sm text-gray-500">إدارة مساحة التخزين</p>
                </div>
              </div>

              <div className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">المساحة المستخدمة</span>
                    <span className="text-sm text-gray-600">2.1 GB / 10 GB</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: '21%' }}></div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    الحد الأقصى لحجم الملف (MB)
                  </label>
                  <input
                    type="number"
                    defaultValue="5"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    أنواع الملفات المسموحة
                  </label>
                  <input
                    type="text"
                    defaultValue="jpg, jpeg, png, gif, pdf"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent text-sm"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">ضغط الصور تلقائياً</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" className="sr-only peer" defaultChecked />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* قسم الأدوات المتقدمة */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
              <div className="flex items-center gap-3">
                <span className="text-gray-600 text-2xl">🛠️</span>
                <div>
                  <h3 className="font-medium text-gray-900">الأدوات المتقدمة</h3>
                  <p className="text-sm text-gray-600">أدوات إدارية متقدمة للنظام</p>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 text-lg">🔄</span>
                </div>
                <div className="text-left">
                  <p className="font-medium text-gray-900">مزامنة البيانات</p>
                  <p className="text-sm text-gray-600">تحديث قاعدة البيانات</p>
                </div>
              </button>

              <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <span className="text-green-600 text-lg">🧹</span>
                </div>
                <div className="text-left">
                  <p className="font-medium text-gray-900">تنظيف الملفات</p>
                  <p className="text-sm text-gray-600">حذف الملفات المؤقتة</p>
                </div>
              </button>

              <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                  <span className="text-purple-600 text-lg">📊</span>
                </div>
                <div className="text-left">
                  <p className="font-medium text-gray-900">تحليل الأداء</p>
                  <p className="text-sm text-gray-600">فحص سرعة الموقع</p>
                </div>
              </button>

              <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                  <span className="text-red-600 text-lg">🔒</span>
                </div>
                <div className="text-left">
                  <p className="font-medium text-gray-900">فحص الأمان</p>
                  <p className="text-sm text-gray-600">تدقيق الثغرات الأمنية</p>
                </div>
              </button>

              <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
                  <span className="text-yellow-600 text-lg">⚡</span>
                </div>
                <div className="text-left">
                  <p className="font-medium text-gray-900">تحسين الأداء</p>
                  <p className="text-sm text-gray-600">ضغط وتحسين الملفات</p>
                </div>
              </button>

              <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <span className="text-indigo-600 text-lg">📋</span>
                </div>
                <div className="text-left">
                  <p className="font-medium text-gray-900">تقرير شامل</p>
                  <p className="text-sm text-gray-600">تصدير تقرير النظام</p>
                </div>
              </button>
            </div>
          </div>

          {/* قسم سجل الأنشطة */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900">سجل الأنشطة الأخيرة</h3>
              <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
                عرض الكل
              </button>
            </div>

            <div className="space-y-4">
              {[
                { action: 'تم تحديث إعدادات الأسعار', user: 'المدير العام', time: 'منذ 5 دقائق', type: 'update' },
                { action: 'تم إضافة مستخدم جديد', user: 'النظام', time: 'منذ 15 دقيقة', type: 'create' },
                { action: 'تم حذف إعلان مخالف', user: 'مدير المحتوى', time: 'منذ 30 دقيقة', type: 'delete' },
                { action: 'تم تفعيل ميزة الدردشة', user: 'المدير العام', time: 'منذ ساعة', type: 'update' },
                { action: 'تم إنشاء نسخة احتياطية', user: 'النظام', time: 'منذ ساعتين', type: 'backup' }
              ].map((activity, index) => (
                <div key={index} className="flex items-center gap-4 p-3 bg-gray-50 rounded-lg">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm ${
                    activity.type === 'update' ? 'bg-blue-100 text-blue-600' :
                    activity.type === 'create' ? 'bg-green-100 text-green-600' :
                    activity.type === 'delete' ? 'bg-red-100 text-red-600' :
                    'bg-purple-100 text-purple-600'
                  }`}>
                    {activity.type === 'update' ? '✏️' :
                     activity.type === 'create' ? '➕' :
                     activity.type === 'delete' ? '🗑️' : '💾'}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                    <p className="text-xs text-gray-600">بواسطة {activity.user} • {activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}