'use client';

import { useState, useRef } from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface ImageUploadProps {
  maxImages: number;
  onImagesChange: (images: File[]) => void;
  userType: 'individual' | 'business';
  subscriptionTier: 'free' | 'basic' | 'premium' | 'gold' | 'business';
}

export default function ImageUpload({
  maxImages,
  onImagesChange,
  userType,
  subscriptionTier
}: ImageUploadProps) {
  const [images, setImages] = useState<File[]>([]);
  const [previews, setPreviews] = useState<string[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // تحديد عدد الصور المسموح حسب الاشتراك
  const getImageLimits = () => {
    if (userType === 'business') {
      switch (subscriptionTier) {
        case 'business':
          return { max: 20, message: 'حتى 20 صورة للشركات' };
        default:
          return { max: 10, message: 'حتى 10 صور للشركات' };
      }
    } else {
      switch (subscriptionTier) {
        case 'free':
          return { max: 3, message: 'حتى 3 صور للأفراد (مجاني)' };
        case 'basic':
          return { max: 5, message: 'حتى 5 صور للأفراد (أساسي)' };
        case 'premium':
          return { max: 8, message: 'حتى 8 صور للأفراد (بريميوم)' };
        case 'gold':
          return { max: 15, message: 'حتى 15 صورة للأفراد (ذهبي)' };
        default:
          return { max: 3, message: 'حتى 3 صور للأفراد' };
      }
    }
  };

  const limits = getImageLimits();
  const actualMaxImages = Math.min(maxImages, limits.max);

  const handleFiles = (files: FileList) => {
    const fileArray = Array.from(files);
    const validFiles = fileArray.filter(file => {
      const isImage = file.type.startsWith('image/');
      const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB
      return isImage && isValidSize;
    });

    const remainingSlots = actualMaxImages - images.length;
    const filesToAdd = validFiles.slice(0, remainingSlots);

    if (filesToAdd.length > 0) {
      const newImages = [...images, ...filesToAdd];
      setImages(newImages);
      onImagesChange(newImages);

      // إنشاء معاينات
      filesToAdd.forEach(file => {
        const reader = new FileReader();
        reader.onload = (e) => {
          setPreviews(prev => [...prev, e.target?.result as string]);
        };
        reader.readAsDataURL(file);
      });
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFiles(e.target.files);
    }
  };

  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    const newPreviews = previews.filter((_, i) => i !== index);
    setImages(newImages);
    setPreviews(newPreviews);
    onImagesChange(newImages);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-4">
      {/* معلومات الحد الأقصى */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-800">إضافة الصور</h3>
        <div className="text-sm text-gray-600">
          <span className={`font-medium ${images.length >= actualMaxImages ? 'text-red-600' : 'text-primary-600'}`}>
            {images.length}/{actualMaxImages}
          </span>
          <span className="mr-2">{limits.message}</span>
        </div>
      </div>

      {/* منطقة رفع الصور */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive
            ? 'border-primary-500 bg-primary-50'
            : images.length >= actualMaxImages
            ? 'border-gray-300 bg-gray-50'
            : 'border-gray-300 hover:border-primary-400 hover:bg-gray-50'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleFileInput}
          className="hidden"
          disabled={images.length >= actualMaxImages}
        />

        {images.length >= actualMaxImages ? (
          <div className="text-center">
            <span className="text-2xl mb-2 block">📸</span>
            <p className="text-gray-700 font-medium">تم الوصول للحد الأقصى من الصور</p>
            <p className="text-sm text-gray-500 mt-1">يمكنك حذف صورة لإضافة أخرى</p>

            {/* اقتراح ترقية الاشتراك */}
            {(subscriptionTier === 'free' || subscriptionTier === 'basic' || (userType === 'individual' && subscriptionTier !== 'gold')) && (
              <div className="mt-4 p-3 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
                <p className="text-sm text-blue-800 mb-2">
                  💡 هل تريد رفع المزيد من الصور؟
                </p>
                <Link
                  href="/pricing"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white text-xs px-4 py-2 rounded-full transition-all duration-300 inline-flex items-center gap-1 shadow-lg hover:shadow-xl transform hover:scale-105"
                >
                  <span>💎</span>
                  <span>ترقية الاشتراك</span>
                </Link>
              </div>
            )}
          </div>
        ) : (
          <div className="cursor-pointer" onClick={openFileDialog}>
            <span className="text-4xl mb-2 block">📷</span>
            <p className="text-lg font-medium text-gray-700 mb-1">
              اسحب الصور هنا أو اضغط للاختيار
            </p>
            <p className="text-sm text-gray-500">
              PNG, JPG, GIF حتى 5MB لكل صورة
            </p>
          </div>
        )}
      </div>

      {/* معاينة الصور */}
      {previews.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {previews.map((preview, index) => (
            <div key={index} className="relative group">
              <div className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                <Image
                  src={preview}
                  alt={`معاينة ${index + 1}`}
                  width={200}
                  height={200}
                  className="w-full h-full object-cover"
                />
              </div>
              <button
                onClick={() => removeImage(index)}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600 transition-colors opacity-0 group-hover:opacity-100"
              >
                ×
              </button>
              <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                {index + 1}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* تحذير عند الاقتراب من الحد الأقصى */}
      {images.length >= actualMaxImages - 1 && images.length < actualMaxImages && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-yellow-600">⚠️</span>
            <h4 className="font-medium text-yellow-800">
              تبقى {actualMaxImages - images.length} صورة فقط!
            </h4>
          </div>
          <p className="text-sm text-yellow-700">
            أنت على وشك الوصول للحد الأقصى من الصور في خطتك الحالية.
          </p>
          {(subscriptionTier === 'free' || subscriptionTier === 'basic') && (
            <Link
              href="/pricing"
              className="mt-2 inline-flex items-center gap-1 text-xs text-yellow-800 hover:text-yellow-900 underline"
            >
              <span>💎</span>
              <span>ترقية للحصول على المزيد</span>
            </Link>
          )}
        </div>
      )}

      {/* نصائح للصور */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-800 mb-2">💡 نصائح للحصول على أفضل النتائج:</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• استخدم صور عالية الجودة وواضحة</li>
          <li>• اجعل الصورة الأولى هي الصورة الرئيسية</li>
          <li>• تأكد من إضاءة جيدة في الصور</li>
          <li>• تجنب الصور المكررة أو المشابهة</li>
        </ul>
      </div>
    </div>
  );
}
