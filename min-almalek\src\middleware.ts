import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// الصفحات المحمية التي تتطلب تسجيل دخول
const protectedPaths = [
  '/settings',
  '/profile',
  '/dashboard',
  '/add-ad',
  '/my-ads',
  '/favorites',
  '/messages',
  '/notifications',
  '/subscription',
  '/billing'
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  console.log('🔍 Middleware: فحص المسار:', pathname);

  // التحقق من الصفحات المحمية للمستخدمين العاديين
  const isProtectedPath = protectedPaths.some(path => pathname.startsWith(path));

  if (isProtectedPath) {
    console.log('🛡️ Middleware: صفحة محمية تم اكتشافها');

    // التحقق من وجود token في الكوكيز
    const authToken = request.cookies.get('auth-token')?.value;

    console.log('🍪 Middleware: البحث عن token:', authToken ? 'موجود' : 'غير موجود');

    // إذا لم يكن هناك token، إعادة توجيه للصفحة الرئيسية
    if (!authToken) {
      console.log('❌ Middleware: غير مصرح، إعادة توجيه للصفحة الرئيسية');
      const url = request.nextUrl.clone();
      url.pathname = '/';
      url.searchParams.set('auth_required', 'true');
      url.searchParams.set('message', 'يجب تسجيل الدخول للوصول لهذه الصفحة');
      return NextResponse.redirect(url);
    }
  }

  // التحقق من الصفحات الإدارية
  if (pathname.startsWith('/admin')) {
    console.log('🛡️ Middleware: صفحة إدارية تم اكتشافها');

    // السماح بصفحة تسجيل الدخول الإداري
    if (request.nextUrl.pathname === '/admin/login') {
      console.log('✅ Middleware: السماح بصفحة تسجيل الدخول');
      return NextResponse.next();
    }

    // التحقق من وجود جلسة إدارية في الكوكيز
    const adminSession = request.cookies.get('admin-session');
    console.log('🍪 Middleware: البحث عن جلسة في الكوكيز:', adminSession ? 'موجودة' : 'غير موجودة');

    // مؤقت<|im_start|>: السماح بالدخول بدون تحقق للاختبار
    console.log('⚠️ Middleware: السماح المؤقت بالدخول للاختبار');
    return NextResponse.next();

    /*
    if (!adminSession) {
      console.log('❌ Middleware: لا توجد جلسة، إعادة توجيه لتسجيل الدخول');
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }

    // في التطبيق الحقيقي، ستتحقق من صحة التوكن هنا
    try {
      const sessionData = JSON.parse(adminSession.value);
      if (!sessionData.admin || !sessionData.token) {
        console.log('❌ Middleware: جلسة غير صالحة، إعادة توجيه');
        return NextResponse.redirect(new URL('/admin/login', request.url));
      }
      console.log('✅ Middleware: جلسة صالحة، السماح بالمرور');
    } catch (error) {
      console.log('❌ Middleware: خطأ في تحليل الجلسة، إعادة توجيه');
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }
    */
  }

  console.log('✅ Middleware: السماح بالمرور');
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/admin/:path*',
    '/settings/:path*',
    '/profile/:path*',
    '/dashboard/:path*',
    '/add-ad/:path*',
    '/my-ads/:path*',
    '/favorites/:path*',
    '/messages/:path*',
    '/notifications/:path*',
    '/subscription/:path*',
    '/billing/:path*'
  ]
};
