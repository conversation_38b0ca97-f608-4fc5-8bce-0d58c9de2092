import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  console.log('🔍 Middleware: فحص المسار:', request.nextUrl.pathname);

  // التحقق من الصفحات الإدارية
  if (request.nextUrl.pathname.startsWith('/admin')) {
    console.log('🛡️ Middleware: صفحة إدارية تم اكتشافها');

    // السماح بصفحة تسجيل الدخول الإداري
    if (request.nextUrl.pathname === '/admin/login') {
      console.log('✅ Middleware: السماح بصفحة تسجيل الدخول');
      return NextResponse.next();
    }

    // التحقق من وجود جلسة إدارية في الكوكيز
    const adminSession = request.cookies.get('admin-session');
    console.log('🍪 Middleware: البحث عن جلسة في الكوكيز:', adminSession ? 'موجودة' : 'غير موجودة');

    // مؤقت<|im_start|>: السماح بالدخول بدون تحقق للاختبار
    console.log('⚠️ Middleware: السماح المؤقت بالدخول للاختبار');
    return NextResponse.next();

    /*
    if (!adminSession) {
      console.log('❌ Middleware: لا توجد جلسة، إعادة توجيه لتسجيل الدخول');
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }

    // في التطبيق الحقيقي، ستتحقق من صحة التوكن هنا
    try {
      const sessionData = JSON.parse(adminSession.value);
      if (!sessionData.admin || !sessionData.token) {
        console.log('❌ Middleware: جلسة غير صالحة، إعادة توجيه');
        return NextResponse.redirect(new URL('/admin/login', request.url));
      }
      console.log('✅ Middleware: جلسة صالحة، السماح بالمرور');
    } catch (error) {
      console.log('❌ Middleware: خطأ في تحليل الجلسة، إعادة توجيه');
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }
    */
  }

  console.log('✅ Middleware: السماح بالمرور');
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/admin/:path*'
  ]
};
