const SafetyTips = () => {
  const buyerTips = [
    {
      icon: '🔍',
      title: 'تحقق من المنتج',
      description: 'افحص المنتج بعناية قبل الشراء وتأكد من مطابقته للوصف'
    },
    {
      icon: '🏪',
      title: 'التقي في مكان عام',
      description: 'اختر مكاناً عاماً وآمناً للقاء البائع مثل مول أو مقهى مشهور'
    },
    {
      icon: '💰',
      title: 'لا تدفع مقدماً',
      description: 'تجنب الدفع قبل معاينة المنتج أو استلامه بشكل كامل'
    },
    {
      icon: '📱',
      title: 'تواصل عبر الموقع',
      description: 'استخدم نظام الرسائل في الموقع للحفاظ على سجل المحادثات'
    },
    {
      icon: '🆔',
      title: 'تحقق من الهوية',
      description: 'اطلب رؤية هوية البائع وتأكد من صحة المعلومات'
    },
    {
      icon: '🚨',
      title: 'احذر من العروض المشبوهة',
      description: 'إذا كان السعر منخفضاً جداً، فقد يكون هناك مشكلة'
    }
  ];

  const sellerTips = [
    {
      icon: '📸',
      title: 'صور واضحة وحقيقية',
      description: 'استخدم صوراً واضحة وحديثة للمنتج من زوايا مختلفة'
    },
    {
      icon: '📝',
      title: 'وصف دقيق',
      description: 'اكتب وصفاً مفصلاً وصادقاً للمنتج مع ذكر أي عيوب'
    },
    {
      icon: '💵',
      title: 'سعر عادل',
      description: 'ضع سعراً عادلاً ومناسباً لحالة وقيمة المنتج'
    },
    {
      icon: '📞',
      title: 'معلومات اتصال صحيحة',
      description: 'تأكد من صحة رقم هاتفك وبريدك الإلكتروني'
    },
    {
      icon: '🤝',
      title: 'كن مهذباً ومتعاوناً',
      description: 'تعامل مع المشترين بأدب واستجب لاستفساراتهم بسرعة'
    },
    {
      icon: '⏰',
      title: 'حدد مواعيد واضحة',
      description: 'اتفق على موعد ومكان محددين للقاء وكن ملتزماً بهما'
    }
  ];

  const warningSignals = [
    {
      icon: '🚩',
      title: 'طلب الدفع المسبق',
      description: 'البائع يطلب تحويل أموال قبل رؤية المنتج'
    },
    {
      icon: '🚩',
      title: 'أسعار منخفضة جداً',
      description: 'السعر أقل بكثير من السعر المعتاد في السوق'
    },
    {
      icon: '🚩',
      title: 'رفض اللقاء',
      description: 'البائع يرفض اللقاء أو معاينة المنتج'
    },
    {
      icon: '🚩',
      title: 'ضغط للشراء السريع',
      description: 'البائع يضغط عليك لاتخاذ قرار سريع'
    },
    {
      icon: '🚩',
      title: 'معلومات غير مكتملة',
      description: 'نقص في المعلومات أو تجنب الإجابة على الأسئلة'
    },
    {
      icon: '🚩',
      title: 'طلب معلومات شخصية',
      description: 'طلب معلومات بنكية أو شخصية غير ضرورية'
    }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">نصائح الأمان</h2>
          <p className="text-lg text-gray-600">دليلك للتعامل الآمن في منصة من المالك</p>
        </div>

        {/* نصائح للمشترين */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">
            🛒 نصائح للمشترين
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {buyerTips.map((tip, index) => (
              <div key={index} className="bg-blue-50 border border-blue-200 rounded-xl p-6 hover:shadow-lg transition-shadow">
                <div className="text-4xl mb-4">{tip.icon}</div>
                <h4 className="text-lg font-semibold text-gray-800 mb-3">{tip.title}</h4>
                <p className="text-gray-700">{tip.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* نصائح للبائعين */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">
            💼 نصائح للبائعين
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sellerTips.map((tip, index) => (
              <div key={index} className="bg-green-50 border border-green-200 rounded-xl p-6 hover:shadow-lg transition-shadow">
                <div className="text-4xl mb-4">{tip.icon}</div>
                <h4 className="text-lg font-semibold text-gray-800 mb-3">{tip.title}</h4>
                <p className="text-gray-700">{tip.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* علامات التحذير */}
        <div className="mb-16">
          <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">
            ⚠️ علامات التحذير
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {warningSignals.map((signal, index) => (
              <div key={index} className="bg-red-50 border border-red-200 rounded-xl p-6 hover:shadow-lg transition-shadow">
                <div className="text-4xl mb-4">{signal.icon}</div>
                <h4 className="text-lg font-semibold text-gray-800 mb-3">{signal.title}</h4>
                <p className="text-gray-700">{signal.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* خطوات التعامل الآمن */}
        <div className="bg-gradient-to-br from-primary-50 to-blue-50 rounded-2xl p-8 mb-16">
          <h3 className="text-2xl font-bold text-gray-800 mb-8 text-center">
            خطوات التعامل الآمن
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[
              { step: '1', title: 'البحث والتواصل', desc: 'ابحث عن المنتج وتواصل مع البائع' },
              { step: '2', title: 'التحقق والاستفسار', desc: 'اطرح الأسئلة وتحقق من التفاصيل' },
              { step: '3', title: 'ترتيب اللقاء', desc: 'حدد موعد ومكان آمن للقاء' },
              { step: '4', title: 'المعاينة والشراء', desc: 'افحص المنتج ثم ادفع الثمن' }
            ].map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 bg-primary-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                  {step.step}
                </div>
                <h4 className="font-semibold text-gray-800 mb-2">{step.title}</h4>
                <p className="text-sm text-gray-600">{step.desc}</p>
              </div>
            ))}
          </div>
        </div>

        {/* طرق الدفع الآمنة */}
        <div className="bg-white border border-gray-200 rounded-xl p-8 mb-16">
          <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            💳 طرق الدفع الآمنة
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h4 className="text-lg font-semibold text-green-600 mb-4 flex items-center gap-2">
                ✅ طرق دفع آمنة
              </h4>
              <ul className="space-y-2 text-gray-700">
                <li>• الدفع نقداً عند الاستلام</li>
                <li>• التحويل البنكي بعد المعاينة</li>
                <li>• الدفع عبر تطبيقات البنوك المحلية</li>
                <li>• استخدام خدمات الضمان المصرفي</li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold text-red-600 mb-4 flex items-center gap-2">
                ❌ تجنب هذه الطرق
              </h4>
              <ul className="space-y-2 text-gray-700">
                <li>• التحويل المسبق قبل المعاينة</li>
                <li>• إرسال الأموال عبر شركات التحويل</li>
                <li>• الدفع عبر مواقع غير موثوقة</li>
                <li>• إعطاء معلومات بطاقتك الائتمانية</li>
              </ul>
            </div>
          </div>
        </div>

        {/* معلومات الاتصال للطوارئ */}
        <div className="bg-gray-900 text-white rounded-xl p-8 text-center">
          <h3 className="text-2xl font-bold mb-4">هل تحتاج مساعدة؟</h3>
          <p className="text-gray-300 mb-6">
            إذا واجهت أي مشكلة أو شككت في صحة إعلان، لا تتردد في التواصل معنا
          </p>
          <div className="flex flex-col md:flex-row gap-4 justify-center">
            <a
              href="tel:+963-11-123-4567"
              className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
            >
              📞 اتصل بنا: +963-11-123-4567
            </a>
            <a
              href="mailto:<EMAIL>"
              className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors"
            >
              🚨 إبلاغ عن مشكلة
            </a>
            <a
              href="/help"
              className="bg-gray-700 text-white px-6 py-3 rounded-lg hover:bg-gray-600 transition-colors"
            >
              ❓ مركز المساعدة
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SafetyTips;
