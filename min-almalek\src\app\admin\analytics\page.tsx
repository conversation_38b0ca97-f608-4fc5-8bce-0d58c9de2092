'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';

interface AnalyticsData {
  overview: {
    totalUsers: number;
    totalAds: number;
    totalViews: number;
    totalRevenue: number;
    activeUsers: number;
    pendingAds: number;
  };
  userGrowth: Array<{
    month: string;
    users: number;
    ads: number;
  }>;
  categoryStats: Array<{
    category: string;
    count: number;
    percentage: number;
  }>;
  locationStats: Array<{
    governorate: string;
    users: number;
    ads: number;
  }>;
  revenueStats: Array<{
    month: string;
    revenue: number;
    subscriptions: number;
  }>;
}

export default function AnalyticsPage() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    overview: {
      totalUsers: 1247,
      totalAds: 3856,
      totalViews: 125430,
      totalRevenue: 45600000,
      activeUsers: 892,
      pendingAds: 23
    },
    userGrowth: [
      { month: 'يناير', users: 150, ads: 420 },
      { month: 'فبراير', users: 180, ads: 520 },
      { month: 'مارس', users: 220, ads: 680 },
      { month: 'أبريل', users: 280, ads: 850 },
      { month: 'مايو', users: 320, ads: 950 },
      { month: 'يونيو', users: 380, ads: 1200 }
    ],
    categoryStats: [
      { category: 'عقارات', count: 1542, percentage: 40 },
      { category: 'سيارات', count: 1156, percentage: 30 },
      { category: 'وظائف', count: 771, percentage: 20 },
      { category: 'إلكترونيات', count: 387, percentage: 10 }
    ],
    locationStats: [
      { governorate: 'دمشق', users: 456, ads: 1234 },
      { governorate: 'حلب', users: 342, ads: 987 },
      { governorate: 'حمص', users: 189, ads: 543 },
      { governorate: 'اللاذقية', users: 156, ads: 432 },
      { governorate: 'طرطوس', users: 104, ads: 321 }
    ],
    revenueStats: [
      { month: 'يناير', revenue: 5200000, subscriptions: 12 },
      { month: 'فبراير', revenue: 6800000, subscriptions: 18 },
      { month: 'مارس', revenue: 8400000, subscriptions: 24 },
      { month: 'أبريل', revenue: 9200000, subscriptions: 28 },
      { month: 'مايو', revenue: 7600000, subscriptions: 22 },
      { month: 'يونيو', revenue: 8200000, subscriptions: 26 }
    ]
  });

  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');
  const [loading, setLoading] = useState(false);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('ar-SY', {
      style: 'currency',
      currency: 'SYP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('ar-SY').format(num);
  };

  const getGrowthPercentage = (current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous * 100).toFixed(1);
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* العنوان */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900" style={{ fontFamily: 'Cairo, sans-serif' }}>
              الإحصائيات والتقارير
            </h1>
            <p className="text-gray-600 mt-1">
              تحليل شامل لأداء الموقع والمستخدمين
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value as any)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="week">آخر أسبوع</option>
              <option value="month">آخر شهر</option>
              <option value="year">آخر سنة</option>
            </select>
            
            <button
              onClick={() => setLoading(true)}
              className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
            >
              تحديث البيانات
            </button>
          </div>
        </div>

        {/* نظرة عامة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المستخدمين</p>
                <p className="text-2xl font-bold text-blue-600">
                  {formatNumber(analyticsData.overview.totalUsers)}
                </p>
                <p className="text-xs text-green-600 mt-1">
                  +{getGrowthPercentage(analyticsData.overview.totalUsers, 1100)}% من الشهر الماضي
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 text-xl">👥</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الإعلانات</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatNumber(analyticsData.overview.totalAds)}
                </p>
                <p className="text-xs text-green-600 mt-1">
                  +{getGrowthPercentage(analyticsData.overview.totalAds, 3200)}% من الشهر الماضي
                </p>
              </div>
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 text-xl">📝</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المشاهدات</p>
                <p className="text-2xl font-bold text-purple-600">
                  {formatNumber(analyticsData.overview.totalViews)}
                </p>
                <p className="text-xs text-green-600 mt-1">
                  +{getGrowthPercentage(analyticsData.overview.totalViews, 98000)}% من الشهر الماضي
                </p>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-purple-600 text-xl">👁️</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي الإيرادات</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {formatCurrency(analyticsData.overview.totalRevenue)}
                </p>
                <p className="text-xs text-green-600 mt-1">
                  +{getGrowthPercentage(analyticsData.overview.totalRevenue, 38000000)}% من الشهر الماضي
                </p>
              </div>
              <div className="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                <span className="text-yellow-600 text-xl">💰</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">المستخدمين النشطين</p>
                <p className="text-2xl font-bold text-indigo-600">
                  {formatNumber(analyticsData.overview.activeUsers)}
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  {((analyticsData.overview.activeUsers / analyticsData.overview.totalUsers) * 100).toFixed(1)}% من الإجمالي
                </p>
              </div>
              <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                <span className="text-indigo-600 text-xl">⚡</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إعلانات في الانتظار</p>
                <p className="text-2xl font-bold text-orange-600">
                  {formatNumber(analyticsData.overview.pendingAds)}
                </p>
                <p className="text-xs text-orange-600 mt-1">
                  يحتاج مراجعة
                </p>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <span className="text-orange-600 text-xl">⏳</span>
              </div>
            </div>
          </div>
        </div>

        {/* الرسوم البيانية */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* نمو المستخدمين والإعلانات */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">نمو المستخدمين والإعلانات</h3>
            <div className="space-y-4">
              {analyticsData.userGrowth.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-gray-700">{item.month}</span>
                      <div className="flex gap-4">
                        <span className="text-sm text-blue-600">{item.users} مستخدم</span>
                        <span className="text-sm text-green-600">{item.ads} إعلان</span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full" 
                          style={{ width: `${(item.users / 400) * 100}%` }}
                        ></div>
                      </div>
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-600 h-2 rounded-full" 
                          style={{ width: `${(item.ads / 1200) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* إحصائيات الفئات */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">توزيع الإعلانات حسب الفئة</h3>
            <div className="space-y-4">
              {analyticsData.categoryStats.map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-gray-700">{item.category}</span>
                      <span className="text-sm text-gray-600">{formatNumber(item.count)} ({item.percentage}%)</span>
                    </div>
                    <div className="bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          index === 0 ? 'bg-blue-600' :
                          index === 1 ? 'bg-green-600' :
                          index === 2 ? 'bg-yellow-600' : 'bg-purple-600'
                        }`}
                        style={{ width: `${item.percentage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* إحصائيات المواقع */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">التوزيع الجغرافي</h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-right text-sm font-medium text-gray-700">المحافظة</th>
                  <th className="px-6 py-3 text-right text-sm font-medium text-gray-700">المستخدمين</th>
                  <th className="px-6 py-3 text-right text-sm font-medium text-gray-700">الإعلانات</th>
                  <th className="px-6 py-3 text-right text-sm font-medium text-gray-700">متوسط الإعلانات/مستخدم</th>
                  <th className="px-6 py-3 text-right text-sm font-medium text-gray-700">النسبة</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {analyticsData.locationStats.map((location, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 text-sm font-medium text-gray-900">
                      {location.governorate}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {formatNumber(location.users)}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {formatNumber(location.ads)}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      {(location.ads / location.users).toFixed(1)}
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        <div className="flex-1 bg-gray-200 rounded-full h-2 mr-3">
                          <div 
                            className="bg-primary-600 h-2 rounded-full" 
                            style={{ width: `${(location.users / 456) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600">
                          {((location.users / analyticsData.overview.totalUsers) * 100).toFixed(1)}%
                        </span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* إحصائيات الإيرادات */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">تطور الإيرادات والاشتراكات</h3>
          <div className="space-y-4">
            {analyticsData.revenueStats.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex-1">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">{item.month}</span>
                    <div className="flex gap-6">
                      <span className="text-sm text-green-600 font-medium">
                        {formatCurrency(item.revenue)}
                      </span>
                      <span className="text-sm text-blue-600">
                        {item.subscriptions} اشتراك
                      </span>
                    </div>
                  </div>
                  <div className="bg-gray-200 rounded-full h-3">
                    <div 
                      className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full" 
                      style={{ width: `${(item.revenue / 10000000) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* أزرار التصدير */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">تصدير التقارير</h3>
          <div className="flex flex-wrap gap-4">
            <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2">
              <span>📊</span>
              تصدير Excel
            </button>
            <button className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2">
              <span>📄</span>
              تصدير PDF
            </button>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2">
              <span>📈</span>
              تقرير مفصل
            </button>
            <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2">
              <span>📧</span>
              إرسال بالبريد
            </button>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
