'use client';

interface RealEstateData {
  listingType?: 'sale' | 'rent';
  rentPeriod?: 'daily' | 'weekly' | 'monthly' | 'yearly';
  propertyType?: string;
  area?: number;
  rooms?: number;
  bathrooms?: number;
  floor?: string;
  condition?: string;
  features?: string[];
}

interface RealEstateSummaryProps {
  realEstateData: RealEstateData;
}

export default function RealEstateSummary({ realEstateData }: RealEstateSummaryProps) {
  // مميزات مشتركة لجميع العقارات
  const commonFeatures = [
    { id: 'green_title', name: 'طابو أخضر', icon: '📋' },
    { id: 'prime_location', name: 'موقع مميز', icon: '⭐' },
    { id: 'parking', name: 'مرآب سيارات', icon: '🚗' },
    { id: 'security', name: 'حراسة', icon: '🔒' },
    { id: 'air_conditioning', name: 'تكييف', icon: '❄️' },
    { id: 'heating', name: 'تدفئة', icon: '🔥' },
    { id: 'solar_energy', name: 'طاقة شمسية', icon: '☀️' },
    { id: 'sea_view', name: 'إطلالة بحر', icon: '🌊' },
    { id: 'mountain_view', name: 'إطلالة جبل', icon: '⛰️' },
    { id: 'city_view', name: 'إطلالة مدينة', icon: '🏙️' },
    { id: 'deluxe_finishing', name: 'كسوة ديلوكس', icon: '✨' },
    { id: 'unfinished', name: 'غير مكسي', icon: '🏗️' }
  ];

  // مميزات خاصة بالشقق والمنازل والفيلات
  const residentialFeatures = [
    { id: 'elevator', name: 'مصعد', icon: '🛗' },
    { id: 'balcony', name: 'بلكونة', icon: '🏠' },
    { id: 'garden', name: 'حديقة', icon: '🌳' },
    { id: 'furnished', name: 'مفروش', icon: '🛋️' },
    { id: 'swimming_pool', name: 'مسبح', icon: '🏊' },
    { id: 'gym', name: 'نادي رياضي', icon: '💪' },
    { id: 'new_finishing', name: 'إكساء جديد', icon: '✨' },
    { id: 'old_finishing', name: 'إكساء قديم', icon: '🔧' },
    { id: 'tiled', name: 'مكسي', icon: '🔲' },
    { id: 'marble', name: 'رخام', icon: '💎' },
    { id: 'ceramic', name: 'سيراميك', icon: '🔳' },
    { id: 'central_heating', name: 'تدفئة مركزية', icon: '🌡️' },
    { id: 'intercom', name: 'انتركوم', icon: '📞' },
    { id: 'storage_room', name: 'غرفة تخزين', icon: '📦' }
  ];

  // مميزات خاصة بالشاليهات
  const chaletFeatures = [
    { id: 'sea_access', name: 'وصول للبحر', icon: '🏖️' },
    { id: 'private_beach', name: 'شاطئ خاص', icon: '🏝️' },
    { id: 'bbq_area', name: 'منطقة شواء', icon: '🔥' },
    { id: 'outdoor_seating', name: 'جلسة خارجية', icon: '🪑' },
    { id: 'terrace', name: 'تراس', icon: '🌅' },
    { id: 'jacuzzi', name: 'جاكوزي', icon: '🛁' },
    { id: 'boat_dock', name: 'رصيف قوارب', icon: '⛵' },
    { id: 'beach_volleyball', name: 'ملعب كرة شاطئية', icon: '🏐' },
    { id: 'outdoor_shower', name: 'دش خارجي', icon: '🚿' },
    { id: 'fire_pit', name: 'موقد نار', icon: '🔥' },
    { id: 'gazebo', name: 'كشك', icon: '🏛️' },
    { id: 'outdoor_kitchen', name: 'مطبخ خارجي', icon: '🍳' }
  ];

  // مميزات خاصة بالمكاتب
  const officeFeatures = [
    { id: 'elevator', name: 'مصعد', icon: '🛗' },
    { id: 'reception_area', name: 'منطقة استقبال', icon: '🏢' },
    { id: 'meeting_rooms', name: 'غرف اجتماعات', icon: '👥' },
    { id: 'internet_fiber', name: 'إنترنت فايبر', icon: '🌐' },
    { id: 'phone_lines', name: 'خطوط هاتف', icon: '☎️' },
    { id: 'kitchen_area', name: 'منطقة مطبخ', icon: '🍽️' },
    { id: 'server_room', name: 'غرفة خوادم', icon: '💻' },
    { id: 'conference_room', name: 'قاعة مؤتمرات', icon: '🎤' },
    { id: 'business_district', name: 'منطقة أعمال', icon: '🏢' },
    { id: 'bank_nearby', name: 'قريب من بنوك', icon: '🏦' },
    { id: 'public_transport', name: 'قريب من المواصلات', icon: '🚌' }
  ];

  // مميزات خاصة بالمحلات التجارية
  const shopFeatures = [
    { id: 'commercial_market', name: 'ضمن سوق تجاري', icon: '🏪' },
    { id: 'shopping_mall', name: 'ضمن مول', icon: '🏬' },
    { id: 'street_front', name: 'واجهة شارع رئيسي', icon: '🛣️' },
    { id: 'corner_shop', name: 'محل زاوية', icon: '📐' },
    { id: 'high_traffic', name: 'حركة مرور عالية', icon: '🚶' },
    { id: 'glass_front', name: 'واجهة زجاجية', icon: '🪟' },
    { id: 'storage_back', name: 'مخزن خلفي', icon: '📦' },
    { id: 'tourist_area', name: 'منطقة سياحية', icon: '🗺️' },
    { id: 'residential_area', name: 'منطقة سكنية', icon: '🏘️' },
    { id: 'university_nearby', name: 'قريب من جامعة', icon: '🎓' },
    { id: 'hospital_nearby', name: 'قريب من مستشفى', icon: '🏥' },
    { id: 'restaurant_area', name: 'منطقة مطاعم', icon: '🍽️' },
    { id: 'delivery_access', name: 'مدخل توصيل', icon: '🚚' }
  ];

  // مميزات خاصة بالمزارع
  const farmFeatures = [
    { id: 'bbq_area', name: 'منطقة شواء', icon: '🔥' },
    { id: 'playground', name: 'ملعب أطفال', icon: '🎪' },
    { id: 'fruit_trees', name: 'أشجار مثمرة', icon: '🌳' },
    { id: 'water_well', name: 'بئر ماء', icon: '💧' },
    { id: 'farm_animals', name: 'حيوانات مزرعة', icon: '🐄' },
    { id: 'greenhouse', name: 'بيت زجاجي', icon: '🏡' },
    { id: 'irrigation_system', name: 'نظام ري', icon: '💦' },
    { id: 'guest_house', name: 'بيت ضيافة', icon: '🏠' },
    { id: 'horse_stable', name: 'إسطبل خيول', icon: '🐎' },
    { id: 'farm_equipment', name: 'معدات زراعية', icon: '🚜' },
    { id: 'fishing_pond', name: 'بركة أسماك', icon: '🐟' },
    { id: 'picnic_area', name: 'منطقة نزهة', icon: '🧺' },
    { id: 'camping_area', name: 'منطقة تخييم', icon: '⛺' }
  ];

  // مميزات خاصة بالمستودعات
  const warehouseFeatures = [
    { id: 'loading_dock', name: 'رصيف تحميل', icon: '🚛' },
    { id: 'high_ceiling', name: 'سقف عالي', icon: '📏' },
    { id: 'crane_system', name: 'نظام رافعة', icon: '🏗️' },
    { id: 'office_space', name: 'مساحة مكتبية', icon: '🏢' },
    { id: 'truck_access', name: 'مدخل شاحنات', icon: '🚚' },
    { id: 'industrial_area', name: 'منطقة صناعية', icon: '🏭' },
    { id: 'rail_access', name: 'وصول سكة حديد', icon: '🚂' },
    { id: 'cold_storage', name: 'تبريد', icon: '🧊' },
    { id: 'fire_system', name: 'نظام إطفاء', icon: '🚨' }
  ];

  // مميزات خاصة بالأراضي
  const landFeatures = [
    { id: 'flat_land', name: 'أرض مستوية', icon: '📏' },
    { id: 'sloped_land', name: 'أرض منحدرة', icon: '⛰️' },
    { id: 'corner_land', name: 'أرض زاوية', icon: '📐' },
    { id: 'main_road', name: 'على شارع رئيسي', icon: '🛣️' },
    { id: 'residential_zone', name: 'منطقة سكنية', icon: '🏘️' },
    { id: 'commercial_zone', name: 'منطقة تجارية', icon: '🏪' },
    { id: 'industrial_zone', name: 'منطقة صناعية', icon: '🏭' },
    { id: 'agricultural_zone', name: 'منطقة زراعية', icon: '🌾' },
    { id: 'water_access', name: 'وصول للمياه', icon: '💧' },
    { id: 'electricity_access', name: 'وصول للكهرباء', icon: '⚡' },
    { id: 'sewage_access', name: 'وصول للصرف الصحي', icon: '🚰' }
  ];

  // دالة للحصول على جميع المميزات
  const getAllFeatures = () => {
    return [
      ...commonFeatures,
      ...residentialFeatures,
      ...chaletFeatures,
      ...officeFeatures,
      ...shopFeatures,
      ...farmFeatures,
      ...warehouseFeatures,
      ...landFeatures
    ];
  };

  // دالة للحصول على اسم الميزة بالعربية
  const getFeatureName = (featureId: string) => {
    const feature = getAllFeatures().find(f => f.id === featureId);
    return feature ? feature.name : featureId;
  };

  // دالة للحصول على أيقونة الميزة
  const getFeatureIcon = (featureId: string) => {
    const feature = getAllFeatures().find(f => f.id === featureId);
    return feature ? feature.icon : '✓';
  };
  const getListingTypeText = (listingType?: string) => {
    switch (listingType) {
      case 'sale': return { text: 'للبيع', icon: '💰', color: 'text-green-600' };
      case 'rent': return { text: 'للإيجار', icon: '🏠', color: 'text-blue-600' };
      default: return { text: 'غير محدد', icon: '❓', color: 'text-gray-400' };
    }
  };

  const getRentPeriodText = (rentPeriod?: string) => {
    switch (rentPeriod) {
      case 'daily': return { text: 'إيجار يومي', icon: '📅' };
      case 'weekly': return { text: 'إيجار أسبوعي', icon: '📆' };
      case 'monthly': return { text: 'إيجار شهري', icon: '🗓️' };
      case 'yearly': return { text: 'إيجار سنوي', icon: '📋' };
      default: return { text: 'غير محدد', icon: '❓' };
    }
  };

  const getPropertyTypeText = (propertyType?: string) => {
    switch (propertyType) {
      case 'apartment': return { text: 'شقة', icon: '🏢' };
      case 'villa': return { text: 'فيلا', icon: '🏘️' };
      case 'house': return { text: 'منزل', icon: '🏠' };
      case 'office': return { text: 'مكتب', icon: '🏢' };
      case 'shop': return { text: 'محل تجاري', icon: '🏪' };
      case 'warehouse': return { text: 'مستودع', icon: '🏭' };
      case 'land': return { text: 'أرض', icon: '🌍' };
      case 'farm': return { text: 'مزرعة', icon: '🚜' };
      case 'chalet': return { text: 'شاليه', icon: '🏖️' };
      default: return { text: 'غير محدد', icon: '❓' };
    }
  };

  const getConditionText = (condition?: string) => {
    switch (condition) {
      case 'new': return { text: 'جديد', icon: '✨', color: 'text-green-600' };
      case 'excellent': return { text: 'ممتاز', icon: '⭐', color: 'text-blue-600' };
      case 'very_good': return { text: 'جيد جداً', icon: '👍', color: 'text-indigo-600' };
      case 'good': return { text: 'جيد', icon: '👌', color: 'text-yellow-600' };
      case 'needs_renovation': return { text: 'يحتاج تجديد', icon: '🔨', color: 'text-orange-600' };
      default: return { text: 'غير محدد', icon: '❓', color: 'text-gray-400' };
    }
  };

  const getFloorText = (floor?: string) => {
    switch (floor) {
      case 'ground': return 'أرضي';
      case 'first': return 'أول';
      case 'second': return 'ثاني';
      case 'third': return 'ثالث';
      case 'fourth': return 'رابع';
      case 'fifth': return 'خامس';
      case 'roof': return 'أخير (روف)';
      default: return 'غير محدد';
    }
  };

  const listingType = getListingTypeText(realEstateData.listingType);
  const rentPeriod = getRentPeriodText(realEstateData.rentPeriod);
  const propertyType = getPropertyTypeText(realEstateData.propertyType);
  const condition = getConditionText(realEstateData.condition);

  return (
    <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border border-green-200">
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
        <span className="text-2xl">🏠</span>
        ملخص تفاصيل العقار
      </h3>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {/* نوع الإعلان */}
        <div className="flex items-center gap-2">
          <span className="text-lg">{listingType.icon}</span>
          <div>
            <p className="text-sm text-gray-600">نوع الإعلان</p>
            <p className={`font-medium ${listingType.color}`}>{listingType.text}</p>
          </div>
        </div>

        {/* فترة الإيجار - تظهر فقط للإيجار */}
        {realEstateData.listingType === 'rent' && realEstateData.rentPeriod && (
          <div className="flex items-center gap-2">
            <span className="text-lg">{rentPeriod.icon}</span>
            <div>
              <p className="text-sm text-gray-600">فترة الإيجار</p>
              <p className="font-medium text-blue-600">{rentPeriod.text}</p>
            </div>
          </div>
        )}

        {/* نوع العقار */}
        {realEstateData.propertyType && (
          <div className="flex items-center gap-2">
            <span className="text-lg">{propertyType.icon}</span>
            <div>
              <p className="text-sm text-gray-600">نوع العقار</p>
              <p className="font-medium">{propertyType.text}</p>
            </div>
          </div>
        )}

        {/* المساحة */}
        {realEstateData.area && (
          <div className="flex items-center gap-2">
            <span className="text-lg">📐</span>
            <div>
              <p className="text-sm text-gray-600">المساحة</p>
              <p className="font-medium">{realEstateData.area} م²</p>
            </div>
          </div>
        )}

        {/* عدد الغرف */}
        {realEstateData.rooms && (
          <div className="flex items-center gap-2">
            <span className="text-lg">🛏️</span>
            <div>
              <p className="text-sm text-gray-600">عدد الغرف</p>
              <p className="font-medium">{realEstateData.rooms} {realEstateData.rooms === 1 ? 'غرفة' : 'غرف'}</p>
            </div>
          </div>
        )}

        {/* عدد الحمامات */}
        {realEstateData.bathrooms && (
          <div className="flex items-center gap-2">
            <span className="text-lg">🚿</span>
            <div>
              <p className="text-sm text-gray-600">عدد الحمامات</p>
              <p className="font-medium">{realEstateData.bathrooms} {realEstateData.bathrooms === 1 ? 'حمام' : 'حمامات'}</p>
            </div>
          </div>
        )}

        {/* الطابق */}
        {realEstateData.floor && (
          <div className="flex items-center gap-2">
            <span className="text-lg">🏢</span>
            <div>
              <p className="text-sm text-gray-600">الطابق</p>
              <p className="font-medium">{getFloorText(realEstateData.floor)}</p>
            </div>
          </div>
        )}

        {/* حالة العقار */}
        {realEstateData.condition && (
          <div className="flex items-center gap-2">
            <span className="text-lg">{condition.icon}</span>
            <div>
              <p className="text-sm text-gray-600">حالة العقار</p>
              <p className={`font-medium ${condition.color}`}>{condition.text}</p>
            </div>
          </div>
        )}
      </div>

      {/* المميزات */}
      {realEstateData.features && realEstateData.features.length > 0 && (
        <div className="mt-6">
          <h4 className="font-medium text-gray-800 mb-3 flex items-center gap-2">
            <span className="text-lg">⭐</span>
            المميزات الإضافية
          </h4>
          <div className="flex flex-wrap gap-2">
            {realEstateData.features.map((feature, index) => (
              <span
                key={index}
                className="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full border border-green-200 flex items-center gap-1"
              >
                <span>{getFeatureIcon(feature)}</span>
                <span>{getFeatureName(feature)}</span>
              </span>
            ))}
          </div>
        </div>
      )}

      {/* رسالة تشجيعية */}
      <div className="mt-6 p-4 bg-white rounded-lg border border-green-200">
        <p className="text-sm text-green-800 flex items-center gap-2">
          <span className="text-lg">💡</span>
          <span>
            {realEstateData.listingType === 'rent'
              ? 'تم إضافة تفاصيل شاملة للعقار! هذا سيساعد المستأجرين في اتخاذ قرار أفضل.'
              : 'تم إضافة تفاصيل شاملة للعقار! هذا سيساعد المشترين في اتخاذ قرار أفضل.'
            }
          </span>
        </p>

        {realEstateData.listingType === 'rent' && (
          <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-800 flex items-center gap-2">
              <span className="text-lg">🏠</span>
              <span>
                <strong>إيجار العقارات:</strong> تأكد من ذكر شروط الإيجار والتأمين في الوصف
              </span>
            </p>
          </div>
        )}

        {realEstateData.listingType === 'sale' && (
          <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-800 flex items-center gap-2">
              <span className="text-lg">💰</span>
              <span>
                <strong>نصيحة للبيع:</strong> أضف صور واضحة ومتعددة، اذكر تفاصيل الموقع والمرافق القريبة
              </span>
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
