// نظام إدارة المفضلة والمتابعة
export interface Favorite {
  id: number;
  userId: number;
  adId: number;
  createdAt: string;
}

export interface SavedSearch {
  id: number;
  userId: number;
  name: string;
  filters: any;
  alertsEnabled: boolean;
  createdAt: string;
  lastChecked?: string;
}

export interface WatchList {
  id: number;
  userId: number;
  sellerId: number;
  createdAt: string;
}

// بيانات تجريبية
const sampleFavorites: Favorite[] = [
  {
    id: 1,
    userId: 1,
    adId: 1,
    createdAt: '2024-01-15T10:30:00Z'
  },
  {
    id: 2,
    userId: 1,
    adId: 3,
    createdAt: '2024-01-18T14:20:00Z'
  },
  {
    id: 3,
    userId: 2,
    adId: 1,
    createdAt: '2024-01-19T09:15:00Z'
  }
];

const sampleSavedSearches: SavedSearch[] = [
  {
    id: 1,
    userId: 1,
    name: 'شقق في دمشق أقل من 100 مليون',
    filters: {
      category: 'real-estate',
      governorate: 'damascus',
      priceMax: 100000000,
      currency: 'SYP'
    },
    alertsEnabled: true,
    createdAt: '2024-01-10T12:00:00Z',
    lastChecked: '2024-01-20T08:00:00Z'
  },
  {
    id: 2,
    userId: 1,
    name: 'سيارات BMW',
    filters: {
      category: 'cars',
      keyword: 'BMW'
    },
    alertsEnabled: false,
    createdAt: '2024-01-12T15:30:00Z'
  }
];

const sampleWatchList: WatchList[] = [
  {
    id: 1,
    userId: 1,
    sellerId: 2,
    createdAt: '2024-01-16T11:45:00Z'
  }
];

export class FavoritesService {
  // إضافة إعلان للمفضلة
  static addToFavorites(userId: number, adId: number): { success: boolean; error?: string } {
    try {
      // التحقق من عدم وجود الإعلان في المفضلة مسبقاً
      const existing = sampleFavorites.find(fav => 
        fav.userId === userId && fav.adId === adId
      );

      if (existing) {
        return { success: false, error: 'الإعلان موجود في المفضلة مسبقاً' };
      }

      // إضافة للمفضلة
      const newFavorite: Favorite = {
        id: Date.now(),
        userId,
        adId,
        createdAt: new Date().toISOString()
      };

      sampleFavorites.push(newFavorite);
      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في إضافة الإعلان للمفضلة' };
    }
  }

  // إزالة إعلان من المفضلة
  static removeFromFavorites(userId: number, adId: number): { success: boolean; error?: string } {
    try {
      const index = sampleFavorites.findIndex(fav => 
        fav.userId === userId && fav.adId === adId
      );

      if (index === -1) {
        return { success: false, error: 'الإعلان غير موجود في المفضلة' };
      }

      sampleFavorites.splice(index, 1);
      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في إزالة الإعلان من المفضلة' };
    }
  }

  // الحصول على مفضلة المستخدم
  static getUserFavorites(userId: number): number[] {
    return sampleFavorites
      .filter(fav => fav.userId === userId)
      .map(fav => fav.adId);
  }

  // التحقق من وجود إعلان في المفضلة
  static isFavorite(userId: number, adId: number): boolean {
    return sampleFavorites.some(fav => 
      fav.userId === userId && fav.adId === adId
    );
  }

  // حفظ بحث جديد
  static saveSearch(
    userId: number, 
    name: string, 
    filters: any, 
    alertsEnabled: boolean = false
  ): { success: boolean; data?: SavedSearch; error?: string } {
    try {
      // التحقق من عدم وجود بحث بنفس الاسم
      const existing = sampleSavedSearches.find(search => 
        search.userId === userId && search.name === name
      );

      if (existing) {
        return { success: false, error: 'يوجد بحث محفوظ بنفس الاسم' };
      }

      const newSearch: SavedSearch = {
        id: Date.now(),
        userId,
        name,
        filters,
        alertsEnabled,
        createdAt: new Date().toISOString()
      };

      sampleSavedSearches.push(newSearch);
      return { success: true, data: newSearch };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في حفظ البحث' };
    }
  }

  // الحصول على البحثات المحفوظة
  static getUserSavedSearches(userId: number): SavedSearch[] {
    return sampleSavedSearches.filter(search => search.userId === userId);
  }

  // حذف بحث محفوظ
  static deleteSavedSearch(userId: number, searchId: number): { success: boolean; error?: string } {
    try {
      const index = sampleSavedSearches.findIndex(search => 
        search.id === searchId && search.userId === userId
      );

      if (index === -1) {
        return { success: false, error: 'البحث غير موجود' };
      }

      sampleSavedSearches.splice(index, 1);
      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في حذف البحث' };
    }
  }

  // تحديث إعدادات التنبيهات للبحث
  static updateSearchAlerts(
    userId: number, 
    searchId: number, 
    alertsEnabled: boolean
  ): { success: boolean; error?: string } {
    try {
      const search = sampleSavedSearches.find(s => 
        s.id === searchId && s.userId === userId
      );

      if (!search) {
        return { success: false, error: 'البحث غير موجود' };
      }

      search.alertsEnabled = alertsEnabled;
      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في تحديث إعدادات التنبيهات' };
    }
  }

  // إضافة بائع لقائمة المتابعة
  static followSeller(userId: number, sellerId: number): { success: boolean; error?: string } {
    try {
      // التحقق من عدم متابعة البائع مسبقاً
      const existing = sampleWatchList.find(watch => 
        watch.userId === userId && watch.sellerId === sellerId
      );

      if (existing) {
        return { success: false, error: 'تتم متابعة هذا البائع مسبقاً' };
      }

      const newWatch: WatchList = {
        id: Date.now(),
        userId,
        sellerId,
        createdAt: new Date().toISOString()
      };

      sampleWatchList.push(newWatch);
      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في متابعة البائع' };
    }
  }

  // إلغاء متابعة بائع
  static unfollowSeller(userId: number, sellerId: number): { success: boolean; error?: string } {
    try {
      const index = sampleWatchList.findIndex(watch => 
        watch.userId === userId && watch.sellerId === sellerId
      );

      if (index === -1) {
        return { success: false, error: 'لا تتم متابعة هذا البائع' };
      }

      sampleWatchList.splice(index, 1);
      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في إلغاء متابعة البائع' };
    }
  }

  // الحصول على البائعين المتابعين
  static getFollowedSellers(userId: number): number[] {
    return sampleWatchList
      .filter(watch => watch.userId === userId)
      .map(watch => watch.sellerId);
  }

  // التحقق من متابعة بائع
  static isFollowingSeller(userId: number, sellerId: number): boolean {
    return sampleWatchList.some(watch => 
      watch.userId === userId && watch.sellerId === sellerId
    );
  }

  // الحصول على إحصائيات المفضلة
  static getFavoritesStats(userId: number) {
    const favorites = this.getUserFavorites(userId);
    const savedSearches = this.getUserSavedSearches(userId);
    const followedSellers = this.getFollowedSellers(userId);

    return {
      totalFavorites: favorites.length,
      totalSavedSearches: savedSearches.length,
      totalFollowedSellers: followedSellers.length,
      activeAlerts: savedSearches.filter(s => s.alertsEnabled).length
    };
  }

  // تنظيف المفضلة (إزالة الإعلانات المنتهية الصلاحية)
  static cleanupFavorites(userId: number): { success: boolean; removed: number } {
    try {
      // في التطبيق الحقيقي، ستتحقق من صلاحية الإعلانات
      const initialCount = sampleFavorites.filter(fav => fav.userId === userId).length;
      
      // محاكاة إزالة إعلانات منتهية الصلاحية
      const toRemove = sampleFavorites.filter(fav => 
        fav.userId === userId && 
        new Date(fav.createdAt).getTime() < Date.now() - (90 * 24 * 60 * 60 * 1000) // أقدم من 90 يوم
      );

      toRemove.forEach(fav => {
        const index = sampleFavorites.indexOf(fav);
        if (index > -1) {
          sampleFavorites.splice(index, 1);
        }
      });

      const finalCount = sampleFavorites.filter(fav => fav.userId === userId).length;
      
      return { 
        success: true, 
        removed: initialCount - finalCount 
      };
    } catch (error) {
      return { success: false, removed: 0 };
    }
  }

  // تصدير المفضلة
  static exportFavorites(userId: number): { success: boolean; data?: any; error?: string } {
    try {
      const favorites = this.getUserFavorites(userId);
      const savedSearches = this.getUserSavedSearches(userId);
      const followedSellers = this.getFollowedSellers(userId);

      const exportData = {
        exportDate: new Date().toISOString(),
        userId,
        favorites,
        savedSearches,
        followedSellers,
        stats: this.getFavoritesStats(userId)
      };

      return { success: true, data: exportData };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في تصدير البيانات' };
    }
  }
}
