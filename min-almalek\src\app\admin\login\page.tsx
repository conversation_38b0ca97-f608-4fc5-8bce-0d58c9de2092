'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { AuthService } from '@/lib/auth';

export default function AdminLoginPage() {
  const [credentials, setCredentials] = useState({
    username: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    console.log('محاولة تسجيل الدخول بالبيانات:', credentials);

    try {
      const result = await AuthService.adminLogin(credentials);
      console.log('نتيجة تسجيل الدخول:', result);

      if (result.success && result.data) {
        // حفظ الجلسة الإدارية
        localStorage.setItem('admin-session', JSON.stringify(result.data));
        console.log('تم حفظ الجلسة، الانتقال للوحة التحكم...');
        router.push('/admin/dashboard');
      } else {
        console.log('فشل تسجيل الدخول:', result.error);
        setError(result.error || 'حدث خطأ في تسجيل الدخول');
      }
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      setError('حدث خطأ في الاتصال');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* الشعار */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center gap-3 mb-4">
            <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-primary-800" style={{ fontFamily: 'Cairo, sans-serif' }}>
                لوحة التحكم الإدارية
              </h1>
              <p className="text-primary-600 text-sm">من المالك</p>
            </div>
          </div>
        </div>

        {/* نموذج تسجيل الدخول */}
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <h2 className="text-xl font-bold text-gray-800 mb-6 text-center" style={{ fontFamily: 'Cairo, sans-serif' }}>
            تسجيل دخول المدير
          </h2>

          {error && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اسم المستخدم
              </label>
              <input
                type="text"
                value={credentials.username}
                onChange={(e) => setCredentials({ ...credentials, username: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="أدخل اسم المستخدم"
                required
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                كلمة المرور
              </label>
              <input
                type="password"
                value={credentials.password}
                onChange={(e) => setCredentials({ ...credentials, password: e.target.value })}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="أدخل كلمة المرور"
                required
                disabled={loading}
              />
            </div>

            <button
              type="submit"
              disabled={loading}
              className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
            </button>
          </form>

          {/* معلومات تجريبية */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-700 mb-2">بيانات تجريبية:</h3>
            <div className="text-xs text-gray-600 space-y-1">
              <p><strong>اسم المستخدم:</strong> admin</p>
              <p><strong>كلمة المرور:</strong> MinAlmalek@2024!</p>
            </div>

            {/* زر دخول مباشر للاختبار */}
            <button
              onClick={() => {
                try {
                  console.log('🚀 بدء الدخول المباشر...');

                  // دخول مباشر للاختبار
                  const mockSession = {
                    admin: {
                      id: 'admin-1',
                      username: 'admin',
                      name: 'مدير الموقع',
                      role: 'super-admin',
                      permissions: ['all']
                    },
                    token: 'test-token-' + Date.now(),
                    expiresAt: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString()
                  };

                  console.log('💾 حفظ الجلسة في localStorage:', mockSession);
                  localStorage.setItem('admin-session', JSON.stringify(mockSession));

                  console.log('✅ تم حفظ الجلسة، التحقق من الحفظ...');
                  const saved = localStorage.getItem('admin-session');
                  console.log('📖 البيانات المحفوظة:', saved);

                  console.log('🔄 محاولة التوجيه إلى /admin/dashboard...');
                  router.push('/admin/dashboard');

                  console.log('⏰ انتظار 2 ثانية ثم محاولة التوجيه مرة أخرى...');
                  setTimeout(() => {
                    console.log('🔄 محاولة التوجيه الثانية...');
                    window.location.href = '/admin/dashboard';
                  }, 2000);

                } catch (error) {
                  console.error('❌ خطأ في الدخول المباشر:', error);
                  alert('حدث خطأ: ' + error.message);
                }
              }}
              className="mt-3 w-full bg-blue-600 text-white py-2 px-4 rounded-lg text-sm hover:bg-blue-700 transition-colors"
            >
              🚀 دخول مباشر للاختبار
            </button>

            {/* رابط مباشر */}
            <a
              href="/admin/dashboard"
              className="mt-2 block w-full bg-green-600 text-white py-2 px-4 rounded-lg text-sm hover:bg-green-700 transition-colors text-center"
            >
              🔗 رابط مباشر للوحة التحكم
            </a>
          </div>
        </div>

        {/* تحذير أمني */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            🔒 هذه منطقة محمية للإدارة فقط
          </p>
        </div>
      </div>
    </div>
  );
}
