'use client';

import { useState, useEffect, createContext, useContext, ReactNode } from 'react';
import ToastNotification from './ToastNotification';
import { AdvancedNotification } from './AdvancedNotificationSystem';

interface ToastContextType {
  showToast: (notification: Omit<AdvancedNotification, 'id' | 'timestamp' | 'isRead'>) => void;
  showSuccess: (title: string, message: string, actionUrl?: string, actionText?: string) => void;
  showError: (title: string, message: string) => void;
  showWarning: (title: string, message: string) => void;
  showInfo: (title: string, message: string) => void;
  showWelcome: (userName: string) => void;
  showPaymentSuccess: (amount: string, service: string) => void;
  showAdPosted: (adTitle: string) => void;
  showMessage: (senderName: string, preview: string) => void;
  showFavorite: (itemTitle: string, type: 'product' | 'seller') => void;
  showLogout: () => void;
  clearAllToasts: () => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

interface ToastProviderProps {
  children: ReactNode;
  maxToasts?: number;
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center';
}

export function ToastProvider({ 
  children, 
  maxToasts = 5, 
  position = 'top-right' 
}: ToastProviderProps) {
  const [toasts, setToasts] = useState<AdvancedNotification[]>([]);

  const showToast = (notificationData: Omit<AdvancedNotification, 'id' | 'timestamp' | 'isRead'>) => {
    const newToast: AdvancedNotification = {
      ...notificationData,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      isRead: false
    };

    setToasts(prev => {
      const updated = [newToast, ...prev];
      // الاحتفاظ بالحد الأقصى من التوست
      return updated.slice(0, maxToasts);
    });
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const clearAllToasts = () => {
    setToasts([]);
  };

  // دوال مساعدة لأنواع مختلفة من التوست
  const showSuccess = (title: string, message: string, actionUrl?: string, actionText?: string) => {
    showToast({
      type: 'success',
      title,
      message,
      priority: 'medium',
      category: 'system',
      actionUrl,
      actionText,
      icon: '✅'
    });
  };

  const showError = (title: string, message: string) => {
    showToast({
      type: 'error',
      title,
      message,
      priority: 'high',
      category: 'system',
      autoHide: false, // الأخطاء لا تختفي تلقائياً
      icon: '❌'
    });
  };

  const showWarning = (title: string, message: string) => {
    showToast({
      type: 'warning',
      title,
      message,
      priority: 'medium',
      category: 'system',
      icon: '⚠️'
    });
  };

  const showInfo = (title: string, message: string) => {
    showToast({
      type: 'info',
      title,
      message,
      priority: 'low',
      category: 'system',
      icon: 'ℹ️'
    });
  };

  const showWelcome = (userName: string) => {
    showToast({
      type: 'welcome',
      title: `مرحباً بك ${userName}! 🎉`,
      message: 'نحن سعداء لانضمامك إلى منصة من المالك. استكشف الميزات الجديدة وابدأ رحلتك معنا!',
      priority: 'high',
      category: 'system',
      actionUrl: '/profile/setup',
      actionText: 'إكمال الملف الشخصي',
      autoHide: false,
      icon: '🎉'
    });
  };

  const showPaymentSuccess = (amount: string, service: string) => {
    showToast({
      type: 'payment',
      title: 'تم الدفع بنجاح! ✅',
      message: `تم دفع ${amount} ل.س لخدمة ${service} بنجاح. ستتلقى إيصالاً عبر البريد الإلكتروني.`,
      priority: 'high',
      category: 'commerce',
      actionUrl: '/payments/history',
      actionText: 'عرض تاريخ المدفوعات',
      icon: '💳'
    });
  };

  const showAdPosted = (adTitle: string) => {
    showToast({
      type: 'ad_posted',
      title: 'تم نشر إعلانك! 🚀',
      message: `تم نشر إعلان "${adTitle}" بنجاح. سيظهر للمستخدمين خلال دقائق قليلة.`,
      priority: 'medium',
      category: 'user_action',
      actionUrl: '/my-ads',
      actionText: 'عرض إعلاناتي',
      icon: '📢'
    });
  };

  const showMessage = (senderName: string, preview: string) => {
    showToast({
      type: 'message',
      title: `رسالة جديدة من ${senderName} 💬`,
      message: preview.length > 50 ? preview.substring(0, 50) + '...' : preview,
      priority: 'medium',
      category: 'social',
      actionUrl: '/messages',
      actionText: 'عرض الرسائل',
      icon: '💬'
    });
  };

  const showFavorite = (itemTitle: string, type: 'product' | 'seller') => {
    showToast({
      type: 'favorite',
      title: type === 'product' ? 'تمت الإضافة للمفضلة! ❤️' : 'تمت متابعة البائع! 👤',
      message: type === 'product' 
        ? `تم إضافة "${itemTitle}" إلى قائمة المفضلة`
        : `تم إضافة "${itemTitle}" إلى قائمة البائعين المتابعين`,
      priority: 'low',
      category: 'user_action',
      actionUrl: type === 'product' ? '/favorites' : '/following',
      actionText: type === 'product' ? 'عرض المفضلة' : 'عرض المتابعين',
      icon: type === 'product' ? '❤️' : '👤'
    });
  };

  const showLogout = () => {
    showToast({
      type: 'logout',
      title: 'تم تسجيل الخروج بنجاح 👋',
      message: 'شكراً لاستخدام منصة من المالك. نراك قريباً!',
      priority: 'medium',
      category: 'security',
      duration: 3000,
      icon: '👋'
    });
  };

  const value: ToastContextType = {
    showToast,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showWelcome,
    showPaymentSuccess,
    showAdPosted,
    showMessage,
    showFavorite,
    showLogout,
    clearAllToasts
  };

  return (
    <ToastContext.Provider value={value}>
      {children}
      
      {/* عرض التوست */}
      <div className="fixed inset-0 pointer-events-none z-50">
        <div className={`
          absolute flex flex-col gap-2
          ${position === 'top-right' ? 'top-4 right-4' : ''}
          ${position === 'top-left' ? 'top-4 left-4' : ''}
          ${position === 'bottom-right' ? 'bottom-4 right-4' : ''}
          ${position === 'bottom-left' ? 'bottom-4 left-4' : ''}
          ${position === 'top-center' ? 'top-4 left-1/2 transform -translate-x-1/2' : ''}
          ${position === 'bottom-center' ? 'bottom-4 left-1/2 transform -translate-x-1/2' : ''}
        `}>
          {toasts.map((toast, index) => (
            <div
              key={toast.id}
              className="pointer-events-auto"
              style={{
                zIndex: 1000 - index, // التوست الأحدث في المقدمة
                marginTop: position.includes('top') ? `${index * 8}px` : '0',
                marginBottom: position.includes('bottom') ? `${index * 8}px` : '0'
              }}
            >
              <ToastNotification
                notification={toast}
                onClose={() => removeToast(toast.id)}
                position={position}
              />
            </div>
          ))}
        </div>
      </div>
    </ToastContext.Provider>
  );
}

// Hook مخصص لاستخدام التوست بسهولة
export const useQuickToast = () => {
  const { showSuccess, showError, showWarning, showInfo } = useToast();
  
  return {
    success: (message: string, title = 'نجح العملية') => showSuccess(title, message),
    error: (message: string, title = 'حدث خطأ') => showError(title, message),
    warning: (message: string, title = 'تحذير') => showWarning(title, message),
    info: (message: string, title = 'معلومة') => showInfo(title, message)
  };
};
