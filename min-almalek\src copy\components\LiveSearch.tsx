'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';

interface SearchResult {
  id: number;
  title: string;
  price: string;
  currency: string;
  location: string;
  category: string;
  image?: string;
  featured?: boolean;
}

const LiveSearch = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);

  // بيانات تجريبية للبحث
  const sampleResults: SearchResult[] = [
    {
      id: 1,
      title: 'شقة للبيع في دمشق - المالكي',
      price: '85,000,000',
      currency: 'ل.س',
      location: 'دمشق - المالكي',
      category: 'عقارات',
      featured: true
    },
    {
      id: 2,
      title: 'BMW X5 2020 فل أوبشن',
      price: '45,000',
      currency: '$',
      location: 'حلب - الفرقان',
      category: 'سيارات',
      featured: false
    },
    {
      id: 3,
      title: 'iPhone 15 Pro Max جديد',
      price: '1,200',
      currency: '$',
      location: 'دمشق - أبو رمانة',
      category: 'إلكترونيات',
      featured: true
    },
    {
      id: 4,
      title: 'مطلوب مطور ويب',
      price: '800,000',
      currency: 'ل.س',
      location: 'دمشق - المزة',
      category: 'وظائف',
      featured: false
    },
    {
      id: 5,
      title: 'فيلا للإيجار في حمص',
      price: '500,000',
      currency: 'ل.س',
      location: 'حمص - الوعر',
      category: 'عقارات',
      featured: false
    }
  ];

  const popularSearches = [
    'شقة للبيع دمشق',
    'سيارة مستعملة',
    'iPhone 15',
    'وظيفة مطور',
    'فيلا للإيجار',
    'لابتوب Dell',
    'محل تجاري',
    'دراجة نارية'
  ];

  useEffect(() => {
    if (query.length > 2) {
      setIsLoading(true);
      // محاكاة استدعاء API
      const timer = setTimeout(() => {
        const filteredResults = sampleResults.filter(item =>
          item.title.toLowerCase().includes(query.toLowerCase()) ||
          item.category.toLowerCase().includes(query.toLowerCase()) ||
          item.location.toLowerCase().includes(query.toLowerCase())
        );
        setResults(filteredResults);
        setIsLoading(false);
        setShowResults(true);
      }, 300);

      return () => clearTimeout(timer);
    } else {
      setResults([]);
      setShowResults(false);
      setIsLoading(false);
    }
  }, [query]);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      // توجيه إلى صفحة النتائج
      window.location.href = `/search?q=${encodeURIComponent(query)}`;
    }
  };

  const handlePopularSearch = (searchTerm: string) => {
    setQuery(searchTerm);
  };

  return (
    <div className="relative w-full max-w-2xl mx-auto">
      <form onSubmit={handleSearchSubmit} className="relative">
        <div className="relative">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="ابحث عن أي شيء... (عقارات، سيارات، وظائف، إلخ)"
            className="w-full px-4 py-4 pr-12 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-lg"
            onFocus={() => setShowResults(query.length > 2)}
            onBlur={() => setTimeout(() => setShowResults(false), 200)}
          />
          <button
            type="submit"
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-primary-600 transition-colors"
          >
            {isLoading ? (
              <div className="animate-spin w-6 h-6 border-2 border-primary-600 border-t-transparent rounded-full"></div>
            ) : (
              <span className="text-2xl">🔍</span>
            )}
          </button>
        </div>

        {/* نتائج البحث المباشر */}
        {showResults && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-xl z-50 max-h-96 overflow-y-auto">
            {results.length > 0 ? (
              <div>
                <div className="p-4 border-b border-gray-100">
                  <h3 className="text-sm font-semibold text-gray-700">
                    نتائج البحث ({results.length})
                  </h3>
                </div>
                <div className="divide-y divide-gray-100">
                  {results.map((result) => (
                    <Link
                      key={result.id}
                      href={`/ad/${result.id}`}
                      className="block p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                          <span className="text-gray-400 text-xl">🖼️</span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium text-gray-800 truncate">
                              {result.title}
                            </h4>
                            {result.featured && (
                              <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                مميز
                              </span>
                            )}
                          </div>
                          <div className="flex items-center justify-between">
                            <div className="text-sm text-gray-600">
                              📍 {result.location} • 📂 {result.category}
                            </div>
                            <div className="font-semibold text-primary-600">
                              {result.price} {result.currency}
                            </div>
                          </div>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
                <div className="p-4 border-t border-gray-100">
                  <button
                    onClick={handleSearchSubmit}
                    className="w-full text-center text-primary-600 hover:text-primary-700 font-medium"
                  >
                    عرض جميع النتائج ({results.length}) ←
                  </button>
                </div>
              </div>
            ) : query.length > 2 ? (
              <div className="p-8 text-center">
                <div className="text-4xl mb-4">🔍</div>
                <h3 className="text-lg font-semibold text-gray-800 mb-2">
                  لا توجد نتائج
                </h3>
                <p className="text-gray-600 mb-4">
                  جرب كلمات مختلفة أو تحقق من الإملاء
                </p>
                <div className="text-sm text-gray-500">
                  البحث عن: "{query}"
                </div>
              </div>
            ) : (
              <div className="p-6">
                <h3 className="text-sm font-semibold text-gray-700 mb-4">
                  عمليات البحث الشائعة
                </h3>
                <div className="flex flex-wrap gap-2">
                  {popularSearches.map((search, index) => (
                    <button
                      key={index}
                      onClick={() => handlePopularSearch(search)}
                      className="text-sm bg-gray-100 text-gray-700 px-3 py-2 rounded-full hover:bg-primary-100 hover:text-primary-700 transition-colors"
                    >
                      {search}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </form>

      {/* اقتراحات سريعة */}
      {!showResults && query.length === 0 && (
        <div className="mt-4">
          <div className="flex flex-wrap gap-2 justify-center">
            <span className="text-sm text-gray-600">بحث سريع:</span>
            {popularSearches.slice(0, 4).map((search, index) => (
              <button
                key={index}
                onClick={() => handlePopularSearch(search)}
                className="text-sm bg-gray-100 text-gray-700 px-3 py-1 rounded-full hover:bg-primary-100 hover:text-primary-700 transition-colors"
              >
                {search}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LiveSearch;
