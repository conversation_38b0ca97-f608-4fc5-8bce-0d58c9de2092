'use client';

import { useState, useEffect } from 'react';

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
}

interface ToastProps {
  message: ToastMessage;
  onRemove: (id: string) => void;
}

const Toast = ({ message, onRemove }: ToastProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isLeaving, setIsLeaving] = useState(false);

  useEffect(() => {
    // إظهار التوست
    setTimeout(() => setIsVisible(true), 100);

    // إخفاء التوست تلقائياً
    const timer = setTimeout(() => {
      handleClose();
    }, message.duration || 5000);

    return () => clearTimeout(timer);
  }, [message.duration]);

  const handleClose = () => {
    setIsLeaving(true);
    setTimeout(() => {
      onRemove(message.id);
    }, 300);
  };

  const getIcon = () => {
    switch (message.type) {
      case 'success':
        return (
          <svg className="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        );
      case 'error':
        return (
          <svg className="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        );
      case 'warning':
        return (
          <svg className="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        );
      case 'info':
        return (
          <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return null;
    }
  };

  const getBackgroundClass = () => {
    switch (message.type) {
      case 'success':
        return 'bg-gradient-to-br from-green-500/80 via-green-600/70 to-green-700/80 border-green-300/40 shadow-green-500/25';
      case 'error':
        return 'bg-gradient-to-br from-red-500/80 via-red-600/70 to-red-700/80 border-red-300/40 shadow-red-500/25';
      case 'warning':
        return 'bg-gradient-to-br from-yellow-500/80 via-yellow-600/70 to-yellow-700/80 border-yellow-300/40 shadow-yellow-500/25';
      case 'info':
        return 'bg-gradient-to-br from-blue-500/80 via-blue-600/70 to-blue-700/80 border-blue-300/40 shadow-blue-500/25';
      default:
        return 'bg-gradient-to-br from-green-500/80 via-green-600/70 to-green-700/80 border-green-300/40 shadow-green-500/25';
    }
  };

  return (
    <div
      className={`
        fixed top-4 right-4 z-50 max-w-sm w-full
        transform transition-all duration-300 ease-in-out
        ${isVisible && !isLeaving ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
        ${isLeaving ? 'scale-95' : 'scale-100'}
      `}
    >
      <div
        className={`
          ${getBackgroundClass()}
          backdrop-blur-lg border rounded-xl shadow-2xl p-4 text-white
          relative overflow-hidden
        `}
        style={{
          backdropFilter: 'blur(10px)',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)'
        }}
      >
        {/* تأثير الخلفية الزجاجية */}
        <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
        
        {/* المحتوى */}
        <div className="relative z-10 flex items-start gap-3">
          <div className="flex-shrink-0 mt-0.5">
            {getIcon()}
          </div>
          
          <div className="flex-1 min-w-0">
            <h4 className="text-sm font-semibold text-white mb-1">
              {message.title}
            </h4>
            <p className="text-sm text-white/90 leading-relaxed">
              {message.message}
            </p>
          </div>
          
          <button
            onClick={handleClose}
            className="flex-shrink-0 text-white/70 hover:text-white transition-colors p-1 rounded-full hover:bg-white/10"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* شريط التقدم */}
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-white/20 overflow-hidden">
          <div 
            className="h-full bg-white/40 transition-all ease-linear"
            style={{
              animation: `shrink ${message.duration || 5000}ms linear forwards`
            }}
          />
        </div>
      </div>

      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </div>
  );
};

// مكون إدارة التوست
interface ToastContainerProps {
  messages: ToastMessage[];
  onRemove: (id: string) => void;
}

export const ToastContainer = ({ messages, onRemove }: ToastContainerProps) => {
  return (
    <div className="fixed top-0 right-0 z-50 p-4 space-y-3">
      {messages.map((message, index) => (
        <div
          key={message.id}
          style={{
            transform: `translateY(${index * 10}px)`,
            zIndex: 50 - index
          }}
        >
          <Toast message={message} onRemove={onRemove} />
        </div>
      ))}
    </div>
  );
};

// Hook لاستخدام التوست
export const useToast = () => {
  const [messages, setMessages] = useState<ToastMessage[]>([]);

  const showToast = (toast: Omit<ToastMessage, 'id'>) => {
    const id = Date.now().toString();
    const newMessage: ToastMessage = { ...toast, id };
    
    setMessages(prev => [...prev, newMessage]);
  };

  const removeToast = (id: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== id));
  };

  const showSuccess = (title: string, message: string) => {
    showToast({ type: 'success', title, message });
  };

  const showError = (title: string, message: string) => {
    showToast({ type: 'error', title, message });
  };

  const showWarning = (title: string, message: string) => {
    showToast({ type: 'warning', title, message });
  };

  const showInfo = (title: string, message: string) => {
    showToast({ type: 'info', title, message });
  };

  return {
    messages,
    removeToast,
    showToast,
    showSuccess,
    showError,
    showWarning,
    showInfo
  };
};

export default Toast;
