'use client';

import { useState } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ContactButtons from '@/components/ContactButtons';
import { Resume } from '@/lib/jobs';

// بيانات تجريبية للسيرة الذاتية
const sampleResume: Resume = {
  id: '1',
  userId: 'user1',
  personalInfo: {
    firstName: 'أحمد',
    lastName: 'محمد',
    title: 'مطور ويب متقدم',
    summary: 'مطور ويب متخصص في React.js و Node.js مع خبرة 5 سنوات في تطوير التطبيقات الحديثة والمتجاوبة.',
    nationality: 'سوري',
    maritalStatus: 'متزوج'
  },
  contactInfo: {
    phone: '+*********** 401',
    email: '<EMAIL>',
    linkedin: 'https://linkedin.com/in/ahmed-mohamed',
    github: 'https://github.com/ahmed-mohamed',
    portfolio: 'https://ahmed-portfolio.com',
    address: 'شارع الثورة، المزة',
    city: 'دمشق'
  },
  experiences: [
    {
      id: '1',
      jobTitle: 'مطور ويب أول',
      company: 'شركة التقنيات المتقدمة',
      location: 'دمشق',
      startDate: '2021-03',
      current: true,
      description: 'تطوير وصيانة تطبيقات الويب باستخدام React.js و Node.js. قيادة فريق من 3 مطورين وتحسين أداء التطبيقات بنسبة 40%.',
      achievements: ['تحسين الأداء بنسبة 40%', 'قيادة فريق من 3 مطورين', 'تطوير 5 مشاريع كبيرة']
    },
    {
      id: '2',
      jobTitle: 'مطور ويب',
      company: 'شركة الحلول الرقمية',
      location: 'دمشق',
      startDate: '2019-06',
      endDate: '2021-02',
      current: false,
      description: 'تطوير واجهات المستخدم التفاعلية وتطبيقات الويب المتجاوبة.',
      achievements: ['تطوير 10+ مواقع ويب', 'تحسين تجربة المستخدم']
    }
  ],
  education: [
    {
      id: '1',
      degree: 'بكالوريوس',
      institution: 'جامعة دمشق',
      field: 'هندسة الحاسوب',
      location: 'دمشق',
      startDate: '2015-09',
      endDate: '2019-06',
      current: false,
      gpa: '3.8/4.0'
    }
  ],
  skills: [
    { id: '1', name: 'React.js', level: 'خبير', category: 'تقني', verified: true },
    { id: '2', name: 'Node.js', level: 'متقدم', category: 'تقني', verified: true },
    { id: '3', name: 'TypeScript', level: 'متقدم', category: 'تقني', verified: false },
    { id: '4', name: 'إدارة المشاريع', level: 'متوسط', category: 'إداري', verified: false }
  ],
  languages: [
    { id: '1', name: 'العربية', level: 'أصلي' },
    { id: '2', name: 'الإنجليزية', level: 'متقدم', certification: 'IELTS 7.0' },
    { id: '3', name: 'الفرنسية', level: 'متوسط' }
  ],
  courses: [
    {
      id: '1',
      name: 'React Advanced Patterns',
      provider: 'Udemy',
      completionDate: '2023-08',
      certificateUrl: 'https://certificate-url.com',
      skills: ['React.js', 'Advanced Patterns']
    },
    {
      id: '2',
      name: 'AWS Cloud Practitioner',
      provider: 'Amazon',
      completionDate: '2023-05',
      skills: ['AWS', 'Cloud Computing']
    }
  ],
  createdAt: '2024-01-15',
  updatedAt: '2024-02-15',
  isPublic: true,
  views: 156
};

export default function MyResumePage() {
  const [resume] = useState<Resume>(sampleResume);
  const [activeTab, setActiveTab] = useState('preview');

  const tabs = [
    { id: 'preview', name: 'معاينة', icon: '👁️' },
    { id: 'edit', name: 'تعديل', icon: '✏️' },
    { id: 'settings', name: 'الإعدادات', icon: '⚙️' },
    { id: 'analytics', name: 'الإحصائيات', icon: '📊' }
  ];

  const renderPreview = () => (
    <div className="bg-white rounded-xl shadow-lg p-8">
      {/* Header */}
      <div className="border-b border-gray-200 pb-6 mb-6">
        <div className="flex items-start justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">
              {resume.personalInfo.firstName} {resume.personalInfo.lastName}
            </h1>
            <h2 className="text-xl text-primary-600 mb-4">{resume.personalInfo.title}</h2>
            <p className="text-gray-600 max-w-2xl">{resume.personalInfo.summary}</p>
          </div>
          <div className="text-right text-sm text-gray-600">
            <div className="mb-1">📞 {resume.contactInfo.phone}</div>
            <div className="mb-1">📧 {resume.contactInfo.email}</div>
            <div>📍 {resume.contactInfo.city}</div>
          </div>
        </div>
      </div>

      {/* Experience */}
      <div className="mb-8">
        <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
          💼 الخبرات العملية
        </h3>
        <div className="space-y-6">
          {resume.experiences.map(exp => (
            <div key={exp.id} className="border-r-4 border-primary-200 pr-4">
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h4 className="font-semibold text-gray-800">{exp.jobTitle}</h4>
                  <p className="text-primary-600">{exp.company}</p>
                </div>
                <div className="text-sm text-gray-500">
                  {exp.startDate} - {exp.current ? 'الآن' : exp.endDate}
                </div>
              </div>
              <p className="text-gray-600 text-sm">{exp.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Education */}
      <div className="mb-8">
        <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
          🎓 التعليم
        </h3>
        <div className="space-y-4">
          {resume.education.map(edu => (
            <div key={edu.id} className="border-r-4 border-green-200 pr-4">
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h4 className="font-semibold text-gray-800">{edu.degree} في {edu.field}</h4>
                  <p className="text-green-600">{edu.institution}</p>
                </div>
                <div className="text-sm text-gray-500">
                  {edu.startDate} - {edu.endDate}
                </div>
              </div>
              {edu.gpa && <p className="text-gray-600 text-sm">المعدل: {edu.gpa}</p>}
            </div>
          ))}
        </div>
      </div>

      {/* Skills */}
      <div className="mb-8">
        <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
          ⚡ المهارات
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {resume.skills.map(skill => (
            <div key={skill.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <span className="font-medium text-gray-800">{skill.name}</span>
                {skill.verified && <span className="text-green-500">✓</span>}
              </div>
              <span className={`text-xs px-2 py-1 rounded-full ${
                skill.level === 'خبير' ? 'bg-red-100 text-red-600' :
                skill.level === 'متقدم' ? 'bg-orange-100 text-orange-600' :
                skill.level === 'متوسط' ? 'bg-yellow-100 text-yellow-600' :
                'bg-green-100 text-green-600'
              }`}>
                {skill.level}
              </span>
            </div>
          ))}
        </div>
      </div>

      {/* Languages */}
      <div className="mb-8">
        <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
          🌍 اللغات
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {resume.languages.map(lang => (
            <div key={lang.id} className="p-3 bg-blue-50 rounded-lg">
              <div className="font-medium text-gray-800">{lang.name}</div>
              <div className="text-sm text-blue-600">{lang.level}</div>
              {lang.certification && (
                <div className="text-xs text-gray-500">{lang.certification}</div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Courses */}
      <div>
        <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
          📜 الدورات والشهادات
        </h3>
        <div className="space-y-3">
          {resume.courses.map(course => (
            <div key={course.id} className="p-4 border border-gray-200 rounded-lg">
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-medium text-gray-800">{course.name}</h4>
                  <p className="text-sm text-gray-600">{course.provider}</p>
                </div>
                <div className="text-sm text-gray-500">{course.completionDate}</div>
              </div>
              {course.certificateUrl && (
                <a
                  href={course.certificateUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary-600 text-sm hover:underline"
                >
                  عرض الشهادة →
                </a>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderSettings = () => (
    <div className="bg-white rounded-xl shadow-lg p-8">
      <h3 className="text-xl font-bold text-gray-800 mb-6">إعدادات السيرة الذاتية</h3>

      <div className="space-y-6">
        {/* Privacy Settings */}
        <div className="border-b border-gray-200 pb-6">
          <h4 className="font-semibold text-gray-800 mb-4">إعدادات الخصوصية</h4>
          <div className="space-y-4">
            <label className="flex items-center justify-between">
              <span className="text-gray-700">السيرة الذاتية مرئية للشركات</span>
              <input
                type="checkbox"
                checked={resume.isPublic}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
            </label>
            <label className="flex items-center justify-between">
              <span className="text-gray-700">السماح بالتواصل المباشر</span>
              <input
                type="checkbox"
                defaultChecked={true}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
            </label>
            <label className="flex items-center justify-between">
              <span className="text-gray-700">إشعارات الوظائف المناسبة</span>
              <input
                type="checkbox"
                defaultChecked={true}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
            </label>
          </div>
        </div>

        {/* Download Options */}
        <div className="border-b border-gray-200 pb-6">
          <h4 className="font-semibold text-gray-800 mb-4">تحميل السيرة الذاتية</h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="text-2xl mb-2">📄</div>
              <div className="font-medium">PDF</div>
              <div className="text-sm text-gray-500">للطباعة والإرسال</div>
            </button>
            <button className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="text-2xl mb-2">📝</div>
              <div className="font-medium">Word</div>
              <div className="text-sm text-gray-500">للتعديل</div>
            </button>
            <button className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="text-2xl mb-2">🔗</div>
              <div className="font-medium">رابط</div>
              <div className="text-sm text-gray-500">للمشاركة</div>
            </button>
          </div>
        </div>

        {/* Danger Zone */}
        <div>
          <h4 className="font-semibold text-red-600 mb-4">منطقة الخطر</h4>
          <div className="space-y-3">
            <button className="w-full p-3 border border-red-300 text-red-600 rounded-lg hover:bg-red-50 transition-colors">
              إخفاء السيرة الذاتية مؤقتاً
            </button>
            <button className="w-full p-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
              حذف السيرة الذاتية نهائياً
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAnalytics = () => (
    <div className="bg-white rounded-xl shadow-lg p-8">
      <h3 className="text-xl font-bold text-gray-800 mb-6">إحصائيات السيرة الذاتية</h3>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="text-center p-6 bg-blue-50 rounded-lg">
          <div className="text-3xl font-bold text-blue-600 mb-2">{resume.views}</div>
          <div className="text-gray-600">مشاهدة</div>
        </div>
        <div className="text-center p-6 bg-green-50 rounded-lg">
          <div className="text-3xl font-bold text-green-600 mb-2">23</div>
          <div className="text-gray-600">شركة اهتمت</div>
        </div>
        <div className="text-center p-6 bg-purple-50 rounded-lg">
          <div className="text-3xl font-bold text-purple-600 mb-2">8</div>
          <div className="text-gray-600">دعوة للمقابلة</div>
        </div>
      </div>

      <div className="space-y-6">
        <div>
          <h4 className="font-semibold text-gray-800 mb-4">الشركات التي شاهدت سيرتك</h4>
          <div className="space-y-3">
            {['شركة التقنيات المتقدمة', 'مجموعة الحلول الرقمية', 'شركة الابتكار التقني'].map((company, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <span className="font-medium">{company}</span>
                <span className="text-sm text-gray-500">منذ {index + 1} يوم</span>
              </div>
            ))}
          </div>
        </div>

        <div>
          <h4 className="font-semibold text-gray-800 mb-4">الوظائف المقترحة</h4>
          <div className="space-y-3">
            {['مطور React.js متقدم', 'مطور Full Stack', 'قائد فريق تطوير'].map((job, index) => (
              <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <span className="font-medium">{job}</span>
                <button className="text-primary-600 text-sm hover:underline">
                  عرض التفاصيل
                </button>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800 mb-2">سيرتي الذاتية</h1>
            <p className="text-gray-600">إدارة وعرض سيرتك الذاتية الاحترافية</p>
          </div>

          <div className="flex gap-3">
            <Link
              href="/resume/create"
              className="bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors flex items-center gap-2"
            >
              ✏️ تعديل السيرة
            </Link>
            <button
              onClick={() => window.open(`/resume/preview/${resume.id}`, '_blank')}
              className="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors border border-primary-200 flex items-center gap-2"
            >
              👁️ معاينة كاملة
            </button>
            <button className="bg-white text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors border border-primary-200 flex items-center gap-2">
              📤 مشاركة
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-xl shadow-md mb-8">
          <div className="flex border-b border-gray-200">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 px-6 py-4 text-center font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'text-primary-600 border-b-2 border-primary-600'
                    : 'text-gray-600 hover:text-gray-800'
                }`}
              >
                {tab.icon} {tab.name}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        {activeTab === 'preview' && renderPreview()}
        {activeTab === 'edit' && (
          <div className="bg-white rounded-xl shadow-lg p-8 text-center">
            <div className="text-6xl mb-4">🚧</div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">قريباً</h3>
            <p className="text-gray-600 mb-4">ميزة التعديل المباشر قيد التطوير</p>
            <Link
              href="/resume/create"
              className="bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors"
            >
              تعديل في الصفحة المخصصة
            </Link>
          </div>
        )}
        {activeTab === 'settings' && renderSettings()}
        {activeTab === 'analytics' && renderAnalytics()}
      </main>

      <Footer />
    </div>
  );
}
