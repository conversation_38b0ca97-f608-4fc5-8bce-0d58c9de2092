'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

interface RealEstateApplication {
  applicationId: string;
  officeName: string;
  licenseNumber: string;
  managerName: string;
  phone: string;
  email: string;
  governorate: string;
  city: string;
  submittedAt: string;
  status: 'pending_review' | 'approved' | 'rejected' | 'payment_pending';
  documents: {
    licenseDocument: File | null;
    commercialRegister: File | null;
    managerID: File | null;
  };
  paymentStatus: 'pending' | 'completed' | 'failed';
}

export default function RealEstateApplicationsPage() {
  const [applications, setApplications] = useState<RealEstateApplication[]>([]);
  const [selectedApplication, setSelectedApplication] = useState<RealEstateApplication | null>(null);
  const [filterStatus, setFilterStatus] = useState<string>('all');

  useEffect(() => {
    // محاكاة جلب البيانات من الخادم
    const mockApplications: RealEstateApplication[] = [
      {
        applicationId: 'REO-1703123456789',
        officeName: 'مكتب الأمانة العقاري',
        licenseNumber: 'RE-2024-001234',
        managerName: 'أحمد محمد',
        phone: '+963 988 123 456',
        email: '<EMAIL>',
        governorate: 'دمشق',
        city: 'المالكي',
        submittedAt: '2024-01-15T10:30:00Z',
        status: 'pending_review',
        documents: {
          licenseDocument: null,
          commercialRegister: null,
          managerID: null
        },
        paymentStatus: 'pending'
      },
      {
        applicationId: 'REO-1703123456790',
        officeName: 'مكتب النجاح العقاري',
        licenseNumber: 'RE-2024-001235',
        managerName: 'فاطمة علي',
        phone: '+963 988 123 457',
        email: '<EMAIL>',
        governorate: 'حلب',
        city: 'الفرقان',
        submittedAt: '2024-01-14T14:20:00Z',
        status: 'approved',
        documents: {
          licenseDocument: null,
          commercialRegister: null,
          managerID: null
        },
        paymentStatus: 'completed'
      }
    ];

    setApplications(mockApplications);
  }, []);

  const handleStatusChange = (applicationId: string, newStatus: RealEstateApplication['status']) => {
    setApplications(prev => 
      prev.map(app => 
        app.applicationId === applicationId 
          ? { ...app, status: newStatus }
          : app
      )
    );
  };

  const filteredApplications = applications.filter(app => 
    filterStatus === 'all' || app.status === filterStatus
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending_review': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'approved': return 'bg-green-100 text-green-800 border-green-200';
      case 'rejected': return 'bg-red-100 text-red-800 border-red-200';
      case 'payment_pending': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending_review': return 'قيد المراجعة';
      case 'approved': return 'موافق عليه';
      case 'rejected': return 'مرفوض';
      case 'payment_pending': return 'في انتظار الدفع';
      default: return status;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="py-8">
        <div className="container mx-auto px-4">
          {/* رأس الصفحة */}
          <div className="mb-8">
            <div className="flex items-center gap-3 mb-4">
              <span className="text-4xl">🏘️</span>
              <div>
                <h1 className="text-3xl font-bold text-gray-800">إدارة طلبات المكاتب العقارية</h1>
                <p className="text-gray-600">مراجعة وإدارة طلبات تسجيل المكاتب العقارية</p>
              </div>
            </div>

            {/* إحصائيات سريعة */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">📋</span>
                  <div>
                    <div className="text-2xl font-bold text-gray-800">{applications.length}</div>
                    <div className="text-gray-600 text-sm">إجمالي الطلبات</div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">⏳</span>
                  <div>
                    <div className="text-2xl font-bold text-yellow-600">
                      {applications.filter(app => app.status === 'pending_review').length}
                    </div>
                    <div className="text-gray-600 text-sm">قيد المراجعة</div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">✅</span>
                  <div>
                    <div className="text-2xl font-bold text-green-600">
                      {applications.filter(app => app.status === 'approved').length}
                    </div>
                    <div className="text-gray-600 text-sm">موافق عليها</div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center gap-3">
                  <span className="text-2xl">❌</span>
                  <div>
                    <div className="text-2xl font-bold text-red-600">
                      {applications.filter(app => app.status === 'rejected').length}
                    </div>
                    <div className="text-gray-600 text-sm">مرفوضة</div>
                  </div>
                </div>
              </div>
            </div>

            {/* فلاتر */}
            <div className="bg-white rounded-xl p-6 shadow-lg">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">تصفية الطلبات</h3>
              <div className="flex flex-wrap gap-3">
                {[
                  { value: 'all', label: 'جميع الطلبات', count: applications.length },
                  { value: 'pending_review', label: 'قيد المراجعة', count: applications.filter(app => app.status === 'pending_review').length },
                  { value: 'approved', label: 'موافق عليها', count: applications.filter(app => app.status === 'approved').length },
                  { value: 'rejected', label: 'مرفوضة', count: applications.filter(app => app.status === 'rejected').length },
                  { value: 'payment_pending', label: 'في انتظار الدفع', count: applications.filter(app => app.status === 'payment_pending').length }
                ].map((filter) => (
                  <button
                    key={filter.value}
                    onClick={() => setFilterStatus(filter.value)}
                    className={`px-4 py-2 rounded-lg border-2 transition-all ${
                      filterStatus === filter.value
                        ? 'border-primary-500 bg-primary-50 text-primary-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    {filter.label} ({filter.count})
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* قائمة الطلبات */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-800">
                الطلبات ({filteredApplications.length})
              </h2>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      رقم الطلب
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      اسم المكتب
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المدير
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الموقع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      تاريخ التقديم
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredApplications.map((application) => (
                    <tr key={application.applicationId} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {application.applicationId}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{application.officeName}</div>
                          <div className="text-sm text-gray-500">ترخيص: {application.licenseNumber}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{application.managerName}</div>
                          <div className="text-sm text-gray-500">{application.phone}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {application.governorate} - {application.city}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(application.submittedAt).toLocaleDateString('ar-SA')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full border ${getStatusColor(application.status)}`}>
                          {getStatusText(application.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          <button
                            onClick={() => setSelectedApplication(application)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            عرض
                          </button>
                          {application.status === 'pending_review' && (
                            <>
                              <button
                                onClick={() => handleStatusChange(application.applicationId, 'approved')}
                                className="text-green-600 hover:text-green-900"
                              >
                                موافقة
                              </button>
                              <button
                                onClick={() => handleStatusChange(application.applicationId, 'rejected')}
                                className="text-red-600 hover:text-red-900"
                              >
                                رفض
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* نافذة تفاصيل الطلب */}
          {selectedApplication && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
              <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
                <div className="p-6 border-b border-gray-200">
                  <div className="flex justify-between items-center">
                    <h3 className="text-xl font-semibold text-gray-800">
                      تفاصيل الطلب: {selectedApplication.applicationId}
                    </h3>
                    <button
                      onClick={() => setSelectedApplication(null)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      ✕
                    </button>
                  </div>
                </div>

                <div className="p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold text-gray-800 mb-3">معلومات المكتب</h4>
                      <div className="space-y-2 text-sm">
                        <div><span className="font-medium">اسم المكتب:</span> {selectedApplication.officeName}</div>
                        <div><span className="font-medium">رقم الترخيص:</span> {selectedApplication.licenseNumber}</div>
                        <div><span className="font-medium">المدير:</span> {selectedApplication.managerName}</div>
                        <div><span className="font-medium">الهاتف:</span> {selectedApplication.phone}</div>
                        <div><span className="font-medium">البريد:</span> {selectedApplication.email}</div>
                        <div><span className="font-medium">الموقع:</span> {selectedApplication.governorate} - {selectedApplication.city}</div>
                      </div>
                    </div>

                    <div>
                      <h4 className="font-semibold text-gray-800 mb-3">حالة الطلب</h4>
                      <div className="space-y-3">
                        <div className={`p-3 rounded-lg border ${getStatusColor(selectedApplication.status)}`}>
                          <div className="font-medium">{getStatusText(selectedApplication.status)}</div>
                          <div className="text-xs mt-1">
                            تاريخ التقديم: {new Date(selectedApplication.submittedAt).toLocaleString('ar-SA')}
                          </div>
                        </div>

                        {selectedApplication.status === 'pending_review' && (
                          <div className="flex gap-3">
                            <button
                              onClick={() => {
                                handleStatusChange(selectedApplication.applicationId, 'approved');
                                setSelectedApplication(null);
                              }}
                              className="flex-1 bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700"
                            >
                              ✅ موافقة على الطلب
                            </button>
                            <button
                              onClick={() => {
                                handleStatusChange(selectedApplication.applicationId, 'rejected');
                                setSelectedApplication(null);
                              }}
                              className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700"
                            >
                              ❌ رفض الطلب
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
}
