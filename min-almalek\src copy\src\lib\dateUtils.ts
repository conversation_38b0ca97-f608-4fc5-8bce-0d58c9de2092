/**
 * دوال مساعدة لتنسيق التواريخ
 */

/**
 * تنسيق التاريخ بالتقويم الميلادي
 * @param date التاريخ المراد تنسيقه
 * @param format نوع التنسيق (short, long, full)
 * @returns التاريخ منسق بالعربية والتقويم الميلادي
 */
export const formatDate = (date: Date | string, format: 'short' | 'long' | 'full' = 'short'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return 'تاريخ غير صحيح';
  }

  switch (format) {
    case 'short':
      // تنسيق مختصر: 15/01/2024
      return dateObj.toLocaleDateString('en-GB', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      }).split('/').reverse().join('/');
      
    case 'long':
      // تنسيق طويل: 15 يناير 2024
      return dateObj.toLocaleDateString('ar-EG', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory' // التقويم الميلادي
      });
      
    case 'full':
      // تنسيق كامل: الاثنين، 15 يناير 2024
      return dateObj.toLocaleDateString('ar-EG', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        calendar: 'gregory' // التقويم الميلادي
      });
      
    default:
      return formatDate(date, 'short');
  }
};

/**
 * تنسيق التاريخ والوقت
 * @param date التاريخ المراد تنسيقه
 * @returns التاريخ والوقت منسق
 */
export const formatDateTime = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  if (isNaN(dateObj.getTime())) {
    return 'تاريخ غير صحيح';
  }

  const dateStr = formatDate(dateObj, 'short');
  const timeStr = dateObj.toLocaleTimeString('ar-EG', {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
  
  return `${dateStr} في ${timeStr}`;
};

/**
 * حساب الفترة الزمنية منذ تاريخ معين
 * @param date التاريخ المراد حساب الفترة منه
 * @returns النص الذي يوضح الفترة الزمنية
 */
export const getTimeAgo = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInMs = now.getTime() - dateObj.getTime();
  
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  const diffInWeeks = Math.floor(diffInDays / 7);
  const diffInMonths = Math.floor(diffInDays / 30);
  const diffInYears = Math.floor(diffInDays / 365);

  if (diffInMinutes < 1) return 'الآن';
  if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
  if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
  if (diffInDays < 7) return `منذ ${diffInDays} يوم`;
  if (diffInWeeks < 4) return `منذ ${diffInWeeks} أسبوع`;
  if (diffInMonths < 12) return `منذ ${diffInMonths} شهر`;
  return `منذ ${diffInYears} سنة`;
};

/**
 * تنسيق تاريخ انتهاء الاشتراك
 * @param endDate تاريخ انتهاء الاشتراك
 * @returns النص المنسق مع تحديد حالة الاشتراك
 */
export const formatSubscriptionEndDate = (endDate: Date | string): { text: string; status: 'active' | 'expiring' | 'expired' } => {
  const dateObj = typeof endDate === 'string' ? new Date(endDate) : endDate;
  const now = new Date();
  const diffInDays = Math.ceil((dateObj.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
  
  const formattedDate = formatDate(dateObj, 'short');
  
  if (diffInDays < 0) {
    return {
      text: `انتهى في ${formattedDate}`,
      status: 'expired'
    };
  } else if (diffInDays <= 7) {
    return {
      text: `ينتهي في ${formattedDate} (${diffInDays} أيام متبقية)`,
      status: 'expiring'
    };
  } else {
    return {
      text: `ينتهي في ${formattedDate}`,
      status: 'active'
    };
  }
};

/**
 * تنسيق تاريخ العضوية
 * @param joinDate تاريخ الانضمام
 * @returns النص المنسق للعضوية
 */
export const formatMemberSince = (joinDate: Date | string): string => {
  const dateObj = typeof joinDate === 'string' ? new Date(joinDate) : joinDate;
  return `عضو منذ ${formatDate(dateObj, 'long')}`;
};

/**
 * التحقق من صحة التاريخ
 * @param date التاريخ المراد التحقق منه
 * @returns true إذا كان التاريخ صحيح
 */
export const isValidDate = (date: Date | string): boolean => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return !isNaN(dateObj.getTime());
};

/**
 * تحويل التاريخ إلى بداية اليوم
 * @param date التاريخ
 * @returns التاريخ في بداية اليوم
 */
export const startOfDay = (date: Date | string): Date => {
  const dateObj = typeof date === 'string' ? new Date(date) : new Date(date);
  dateObj.setHours(0, 0, 0, 0);
  return dateObj;
};

/**
 * تحويل التاريخ إلى نهاية اليوم
 * @param date التاريخ
 * @returns التاريخ في نهاية اليوم
 */
export const endOfDay = (date: Date | string): Date => {
  const dateObj = typeof date === 'string' ? new Date(date) : new Date(date);
  dateObj.setHours(23, 59, 59, 999);
  return dateObj;
};

/**
 * إضافة أيام إلى تاريخ
 * @param date التاريخ الأساسي
 * @param days عدد الأيام المراد إضافتها
 * @returns التاريخ الجديد
 */
export const addDays = (date: Date | string, days: number): Date => {
  const dateObj = typeof date === 'string' ? new Date(date) : new Date(date);
  dateObj.setDate(dateObj.getDate() + days);
  return dateObj;
};

/**
 * إضافة شهور إلى تاريخ
 * @param date التاريخ الأساسي
 * @param months عدد الشهور المراد إضافتها
 * @returns التاريخ الجديد
 */
export const addMonths = (date: Date | string, months: number): Date => {
  const dateObj = typeof date === 'string' ? new Date(date) : new Date(date);
  dateObj.setMonth(dateObj.getMonth() + months);
  return dateObj;
};
