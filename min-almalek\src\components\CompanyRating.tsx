'use client';

import { useState, useEffect } from 'react';

interface Review {
  id: string;
  userId: string;
  userName: string;
  rating: number;
  title: string;
  comment: string;
  pros: string[];
  cons: string[];
  jobTitle: string;
  workPeriod: string;
  isCurrentEmployee: boolean;
  isVerified: boolean;
  date: string;
  helpful: number;
}

interface CompanyStats {
  overallRating: number;
  totalReviews: number;
  workLifeBalance: number;
  salary: number;
  culture: number;
  management: number;
  careerGrowth: number;
  recommendationRate: number;
}

interface CompanyRatingProps {
  companyId: string;
  companyName: string;
  canReview?: boolean;
}

export default function CompanyRating({ companyId, companyName, canReview = false }: CompanyRatingProps) {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [stats, setStats] = useState<CompanyStats | null>(null);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('newest');

  // بيانات وهمية
  useEffect(() => {
    const sampleStats: CompanyStats = {
      overallRating: 4.2,
      totalReviews: 127,
      workLifeBalance: 4.1,
      salary: 3.8,
      culture: 4.3,
      management: 3.9,
      careerGrowth: 4.0,
      recommendationRate: 78
    };

    const sampleReviews: Review[] = [
      {
        id: '1',
        userId: 'user1',
        userName: 'أحمد محمد',
        rating: 5,
        title: 'بيئة عمل ممتازة وفريق متعاون',
        comment: 'أعمل في هذه الشركة منذ سنتين وأنا راضٍ جداً عن بيئة العمل والزملاء. الإدارة متفهمة والمشاريع متنوعة ومثيرة.',
        pros: ['بيئة عمل إيجابية', 'مشاريع متنوعة', 'فريق متعاون', 'مرونة في أوقات العمل'],
        cons: ['الراتب يمكن أن يكون أفضل', 'أحياناً ضغط العمل عالي'],
        jobTitle: 'مطور ويب',
        workPeriod: 'سنتان',
        isCurrentEmployee: true,
        isVerified: true,
        date: '2024-01-20',
        helpful: 15
      },
      {
        id: '2',
        userId: 'user2',
        userName: 'فاطمة أحمد',
        rating: 4,
        title: 'شركة جيدة للمبتدئين',
        comment: 'بدأت مسيرتي المهنية هنا وتعلمت الكثير. الشركة توفر فرص تدريب جيدة ولكن الترقيات بطيئة نوعاً ما.',
        pros: ['فرص تعلم جيدة', 'تدريب مستمر', 'بيئة ودودة للمبتدئين'],
        cons: ['الترقيات بطيئة', 'الراتب أقل من المتوقع', 'ساعات عمل طويلة أحياناً'],
        jobTitle: 'مصممة جرافيك',
        workPeriod: 'سنة ونصف',
        isCurrentEmployee: false,
        isVerified: true,
        date: '2024-01-15',
        helpful: 8
      },
      {
        id: '3',
        userId: 'user3',
        userName: 'محمد علي',
        rating: 3,
        title: 'تجربة متوسطة',
        comment: 'الشركة لديها إمكانيات جيدة لكن تحتاج لتحسين في إدارة المشاريع والتواصل الداخلي.',
        pros: ['مشاريع مثيرة', 'تقنيات حديثة'],
        cons: ['سوء في التواصل', 'إدارة مشاريع ضعيفة', 'عدم وضوح في المهام'],
        jobTitle: 'مطور تطبيقات',
        workPeriod: 'سنة',
        isCurrentEmployee: false,
        isVerified: false,
        date: '2024-01-10',
        helpful: 3
      }
    ];

    setStats(sampleStats);
    setReviews(sampleReviews);
  }, [companyId]);

  const renderStars = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6'
    };

    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={`${sizeClasses[size]} ${
              star <= rating ? 'text-yellow-400' : 'text-gray-300'
            }`}
          >
            ⭐
          </span>
        ))}
        <span className="text-sm text-gray-600 mr-2">{rating.toFixed(1)}</span>
      </div>
    );
  };

  const filteredReviews = reviews.filter(review => {
    if (filter === 'verified') return review.isVerified;
    if (filter === 'current') return review.isCurrentEmployee;
    if (filter === 'former') return !review.isCurrentEmployee;
    return true;
  });

  const sortedReviews = [...filteredReviews].sort((a, b) => {
    if (sortBy === 'newest') return new Date(b.date).getTime() - new Date(a.date).getTime();
    if (sortBy === 'oldest') return new Date(a.date).getTime() - new Date(b.date).getTime();
    if (sortBy === 'highest') return b.rating - a.rating;
    if (sortBy === 'lowest') return a.rating - b.rating;
    if (sortBy === 'helpful') return b.helpful - a.helpful;
    return 0;
  });

  if (!stats) return <div>جاري التحميل...</div>;

  return (
    <div className="space-y-6">
      {/* إحصائيات الشركة */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-800">
            تقييمات {companyName}
          </h2>
          {canReview && (
            <button
              onClick={() => setShowReviewForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              اكتب تقييم
            </button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* التقييم العام */}
          <div className="text-center">
            <div className="text-4xl font-bold text-blue-600 mb-2">
              {stats.overallRating.toFixed(1)}
            </div>
            {renderStars(stats.overallRating, 'lg')}
            <div className="text-gray-600 mt-2">
              {stats.totalReviews} تقييم
            </div>
            <div className="text-sm text-gray-500">
              {stats.recommendationRate}% ينصحون بالعمل هنا
            </div>
          </div>

          {/* تفاصيل التقييمات */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700">توازن العمل والحياة</span>
              <div className="flex items-center gap-2">
                {renderStars(stats.workLifeBalance, 'sm')}
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700">الراتب والمزايا</span>
              <div className="flex items-center gap-2">
                {renderStars(stats.salary, 'sm')}
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700">ثقافة الشركة</span>
              <div className="flex items-center gap-2">
                {renderStars(stats.culture, 'sm')}
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700">الإدارة</span>
              <div className="flex items-center gap-2">
                {renderStars(stats.management, 'sm')}
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-700">النمو المهني</span>
              <div className="flex items-center gap-2">
                {renderStars(stats.careerGrowth, 'sm')}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* فلاتر التقييمات */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex flex-wrap items-center gap-4 mb-4">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">فلترة:</span>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">جميع التقييمات</option>
              <option value="verified">موثقة فقط</option>
              <option value="current">موظفين حاليين</option>
              <option value="former">موظفين سابقين</option>
            </select>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">ترتيب:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="newest">الأحدث</option>
              <option value="oldest">الأقدم</option>
              <option value="highest">الأعلى تقييماً</option>
              <option value="lowest">الأقل تقييماً</option>
              <option value="helpful">الأكثر فائدة</option>
            </select>
          </div>
        </div>

        {/* قائمة التقييمات */}
        <div className="space-y-4">
          {sortedReviews.length > 0 ? (
            sortedReviews.map((review) => (
              <div key={review.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 font-semibold">
                        {review.userName.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-semibold text-gray-800">{review.userName}</span>
                        {review.isVerified && (
                          <span className="text-green-600 text-sm">✓ موثق</span>
                        )}
                      </div>
                      <div className="text-sm text-gray-600">
                        {review.jobTitle} • {review.workPeriod} • 
                        {review.isCurrentEmployee ? ' موظف حالي' : ' موظف سابق'}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    {renderStars(review.rating, 'sm')}
                    <div className="text-xs text-gray-500 mt-1">
                      {new Date(review.date).toLocaleDateString('ar-SY')}
                    </div>
                  </div>
                </div>

                <h3 className="font-semibold text-gray-800 mb-2">{review.title}</h3>
                <p className="text-gray-700 mb-4">{review.comment}</p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  {review.pros.length > 0 && (
                    <div>
                      <h4 className="font-medium text-green-700 mb-2">✅ الإيجابيات:</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {review.pros.map((pro, index) => (
                          <li key={index}>• {pro}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {review.cons.length > 0 && (
                    <div>
                      <h4 className="font-medium text-red-700 mb-2">❌ السلبيات:</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {review.cons.map((con, index) => (
                          <li key={index}>• {con}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                  <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    👍 مفيد ({review.helpful})
                  </button>
                  <button className="text-gray-600 hover:text-gray-800 text-sm">
                    الإبلاغ عن مشكلة
                  </button>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-8">
              <div className="text-6xl mb-4">⭐</div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد تقييمات</h3>
              <p className="text-gray-600">كن أول من يقيم هذه الشركة</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
