'use client';

import Link from 'next/link';

interface SubscriptionLimitsProps {
  userType: 'individual' | 'business';
  subscriptionTier: 'free' | 'basic' | 'premium' | 'gold' | 'business';
}

export default function SubscriptionLimits({ userType, subscriptionTier }: SubscriptionLimitsProps) {
  const getSubscriptionDetails = () => {
    if (userType === 'business') {
      switch (subscriptionTier) {
        case 'business':
          return {
            name: 'خطة الأعمال',
            imageLimit: 20,
            adLimit: 'غير محدود',
            features: [
              'حتى 20 صورة لكل إعلان',
              'إعلانات غير محدودة',
              'أولوية في النتائج',
              'إحصائيات مفصلة',
              'دعم فني مخصص'
            ],
            color: 'bg-purple-500',
            textColor: 'text-purple-700',
            bgColor: 'bg-purple-50',
            borderColor: 'border-purple-200'
          };
        default:
          return {
            name: 'خطة الشركات الأساسية',
            imageLimit: 10,
            adLimit: '50 إعلان/شهر',
            features: [
              'حتى 10 صور لكل إعلان',
              '50 إعلان شهرياً',
              'عرض في النتائج العادية',
              'إحصائيات أساسية'
            ],
            color: 'bg-blue-500',
            textColor: 'text-blue-700',
            bgColor: 'bg-blue-50',
            borderColor: 'border-blue-200'
          };
      }
    } else {
      switch (subscriptionTier) {
        case 'free':
          return {
            name: 'الخطة المجانية',
            imageLimit: 3,
            adLimit: '3 إعلانات/شهر',
            features: [
              'حتى 3 صور لكل إعلان',
              '3 إعلانات شهرياً',
              'عرض في النتائج العادية',
              'مدة الإعلان: 30 يوم'
            ],
            color: 'bg-gray-500',
            textColor: 'text-gray-700',
            bgColor: 'bg-gray-50',
            borderColor: 'border-gray-200'
          };
        case 'basic':
          return {
            name: 'الباقة الأساسية',
            imageLimit: 5,
            adLimit: '5 إعلانات/شهر',
            features: [
              'حتى 5 صور لكل إعلان',
              '5 إعلانات شهرياً',
              'عرض في النتائج العادية',
              'مدة الإعلان: 30 يوم',
              'دعم فني أساسي'
            ],
            color: 'bg-green-500',
            textColor: 'text-green-700',
            bgColor: 'bg-green-50',
            borderColor: 'border-green-200'
          };
        case 'premium':
          return {
            name: 'الباقة المميزة',
            imageLimit: 10,
            adLimit: '15 إعلان/شهر',
            features: [
              'حتى 10 صور لكل إعلان',
              '15 إعلان شهرياً',
              'إعلانات مميزة',
              'أولوية في النتائج',
              'دعم فني متقدم',
              'شارة "معلن مميز"',
              'مدة الإعلان: 30 يوم'
            ],
            color: 'bg-blue-500',
            textColor: 'text-blue-700',
            bgColor: 'bg-blue-50',
            borderColor: 'border-blue-200'
          };
        case 'gold':
          return {
            name: 'باقة الأعمال',
            imageLimit: 15,
            adLimit: '30 إعلان/شهر',
            features: [
              'حتى 15 صورة لكل إعلان',
              '30 إعلان شهرياً',
              'إعلانات عاجلة',
              'أولوية قصوى',
              'دعم فني 24/7',
              'صفحة معلن مخصصة',
              'مدة الإعلان: 30 يوم'
            ],
            color: 'bg-yellow-500',
            textColor: 'text-yellow-700',
            bgColor: 'bg-yellow-50',
            borderColor: 'border-yellow-200'
          };
        default:
          return {
            name: 'خطة غير محددة',
            imageLimit: 3,
            adLimit: '3 إعلانات/شهر',
            features: ['خطة أساسية'],
            color: 'bg-gray-500',
            textColor: 'text-gray-700',
            bgColor: 'bg-gray-50',
            borderColor: 'border-gray-200'
          };
      }
    }
  };

  const details = getSubscriptionDetails();

  return (
    <div className={`${details.bgColor} ${details.borderColor} border rounded-lg p-4`}>
      <div className="flex items-center gap-3 mb-3">
        <div className={`w-3 h-3 ${details.color} rounded-full`}></div>
        <h3 className={`font-semibold ${details.textColor}`}>
          {details.name}
        </h3>
        <span className="text-xs bg-white px-2 py-1 rounded-full border">
          {userType === 'business' ? '🏢 شركة' : '👤 فرد'}
        </span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div className="flex items-center gap-2">
          <span className="text-2xl">📸</span>
          <div>
            <p className="text-sm font-medium text-gray-800">عدد الصور</p>
            <p className={`text-lg font-bold ${details.textColor}`}>
              {details.imageLimit} صورة
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-2xl">📋</span>
          <div>
            <p className="text-sm font-medium text-gray-800">عدد الإعلانات</p>
            <p className={`text-lg font-bold ${details.textColor}`}>
              {details.adLimit}
            </p>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="text-sm font-semibold text-gray-800">المميزات المتاحة:</h4>
        <ul className="space-y-1">
          {details.features.map((feature, index) => (
            <li key={index} className="flex items-center gap-2 text-sm text-gray-700">
              <span className="text-green-500">✓</span>
              {feature}
            </li>
          ))}
        </ul>
      </div>

      {(subscriptionTier === 'free' || subscriptionTier === 'basic' || (userType === 'individual' && subscriptionTier !== 'gold')) && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              {subscriptionTier === 'free'
                ? 'هل تريد المزيد من المميزات؟'
                : subscriptionTier === 'basic'
                ? 'ترقية للباقة المميزة؟'
                : 'ترقية لخطة أفضل؟'
              }
            </p>
            <Link
              href="/pricing"
              className="bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white text-xs px-4 py-2 rounded-full transition-all duration-300 inline-flex items-center gap-1 shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <span>💎</span>
              <span>ترقية الاشتراك</span>
            </Link>
          </div>
        </div>
      )}
    </div>
  );
}
