'use client';

import { useState, useRef, useEffect } from 'react';

interface Message {
  id: number;
  text: string;
  sender: 'user' | 'support';
  timestamp: string;
  type?: 'text' | 'image' | 'file';
}

const LiveChat = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      text: 'مرحباً! كيف يمكنني مساعدتك اليوم؟',
      sender: 'support',
      timestamp: new Date().toLocaleTimeString('ar-SY', { hour: '2-digit', minute: '2-digit' })
    }
  ]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const quickReplies = [
    'كيف أنشر إعلان؟',
    'ما هي أنواع الحسابات؟',
    'كيف أدفع؟',
    'مشكلة في تسجيل الدخول',
    'كيف أحذف حسابي؟',
    'ما هي الباقات المتاحة؟',
    'كيف أغير معلوماتي؟',
    'مشكلة في الموقع'
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = (text: string) => {
    if (!text.trim()) return;

    const userMessage: Message = {
      id: Date.now(),
      text: text.trim(),
      sender: 'user',
      timestamp: new Date().toLocaleTimeString('ar-SY', { hour: '2-digit', minute: '2-digit' })
    };

    setMessages(prev => [...prev, userMessage]);
    setNewMessage('');
    setIsTyping(true);

    // محاكاة رد الدعم الفني
    setTimeout(() => {
      setIsTyping(false);
      const supportMessage: Message = {
        id: Date.now() + 1,
        text: getAutoReply(text),
        sender: 'support',
        timestamp: new Date().toLocaleTimeString('ar-SY', { hour: '2-digit', minute: '2-digit' })
      };
      setMessages(prev => [...prev, supportMessage]);
    }, 1500);
  };

  const getAutoReply = (userMessage: string): string => {
    const message = userMessage.toLowerCase();

    // أسئلة حول نشر الإعلانات
    if (message.includes('إعلان') || message.includes('نشر') || message.includes('أضيف')) {
      return '📝 لنشر إعلان:\n1. اضغط على "أضف إعلانك" من القائمة الرئيسية\n2. اختر التصنيف المناسب (عقارات، سيارات، إلخ)\n3. املأ جميع البيانات المطلوبة\n4. ارفع صور واضحة (حتى 10 صور)\n5. اضغط "نشر الإعلان"\n\nملاحظة: الإعلانات المجانية تظهر لمدة 30 يوم.';
    }

    // أسئلة حول أنواع الحسابات
    else if (message.includes('حساب') || message.includes('نوع') || message.includes('فرق')) {
      return '👤 أنواع الحسابات:\n\n🔹 حساب فردي: للأشخاص العاديين\n🔹 حساب تجاري: للشركات والأعمال\n🔹 مكتب عقاري: متخصص للعقارات فقط\n\nكل نوع له باقات مختلفة وميزات خاصة. يمكنك ترقية حسابك في أي وقت من الملف الشخصي.';
    }

    // أسئلة حول الباقات والأسعار
    else if (message.includes('باقة') || message.includes('سعر') || message.includes('تكلفة') || message.includes('مجاني')) {
      return '💰 الباقات المتاحة:\n\n🆓 مجاني: 5 إعلانات شهرياً\n🥈 فضي: 15 إعلان + ميزات إضافية\n🥇 ذهبي: 25 إعلان + أولوية في البحث\n💎 ماسي: إعلانات لا محدودة + جميع الميزات\n\nالأسعار تبدأ من 10$ شهرياً. اطلع على التفاصيل في صفحة الباقات.';
    }

    // أسئلة حول الدفع
    else if (message.includes('دفع') || message.includes('فيزا') || message.includes('ماستر') || message.includes('باي بال') || message.includes('فوترة')) {
      return '💳 طرق الدفع المقبولة:\n\n✅ فيزا (Visa)\n✅ ماستركارد (MasterCard)\n✅ باي بال (PayPal)\n✅ كاش آب (Cash App)\n✅ آبل باي (Apple Pay)\n\nجميع المدفوعات آمنة ومشفرة. يمكنك إضافة طريقة دفع من الإعدادات.';
    }

    // أسئلة حول تسجيل الدخول
    else if (message.includes('دخول') || message.includes('تسجيل') || message.includes('لوجين')) {
      return '🔐 مشاكل تسجيل الدخول:\n\n1. تأكد من صحة البريد الإلكتروني\n2. تأكد من كلمة المرور\n3. استخدم "نسيت كلمة المرور" إذا لزم الأمر\n4. تأكد من تفعيل حسابك عبر البريد\n5. امسح ذاكرة التخزين المؤقت\n\nإذا استمرت المشكلة، اتصل بنا مباشرة.';
    }

    // أسئلة حول كلمة المرور
    else if (message.includes('كلمة المرور') || message.includes('باسورد') || message.includes('رقم سري')) {
      return '🔑 تغيير كلمة المرور:\n\n1. اذهب إلى الملف الشخصي\n2. اضغط على "إعدادات الحساب"\n3. اختر "تغيير كلمة المرور"\n4. أدخل كلمة المرور الحالية\n5. أدخل كلمة المرور الجديدة\n6. أكد كلمة المرور الجديدة\n\nكلمة المرور يجب أن تحتوي على 8 أحرف على الأقل.';
    }

    // أسئلة حول حذف الحساب
    else if (message.includes('حذف') || message.includes('إلغاء') || message.includes('إغلاق')) {
      return '🗑️ حذف الحساب:\n\n⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!\n\n1. اذهب إلى إعدادات الحساب\n2. اضغط على "حذف الحساب"\n3. أدخل كلمة المرور للتأكيد\n4. اكتب "حذف" في المربع\n5. اضغط "تأكيد الحذف"\n\nسيتم حذف جميع بياناتك وإعلاناتك نهائياً.';
    }

    // أسئلة حول تعديل المعلومات
    else if (message.includes('تعديل') || message.includes('تغيير') || message.includes('تحديث')) {
      return '✏️ تعديل المعلومات الشخصية:\n\n1. اذهب إلى "الملف الشخصي"\n2. اضغط على "تعديل"\n3. غيّر المعلومات المطلوبة\n4. اضغط "حفظ التغييرات"\n\nيمكنك تعديل: الاسم، الهاتف، العنوان، الصورة الشخصية، ومعلومات الشركة.';
    }

    // أسئلة حول الصور
    else if (message.includes('صور') || message.includes('صورة') || message.includes('رفع')) {
      return '📸 رفع الصور:\n\n✅ الحد الأقصى: 10 صور لكل إعلان\n✅ الحجم الأقصى: 5 ميجابايت لكل صورة\n✅ الصيغ المقبولة: JPG, PNG, WEBP\n✅ الدقة المنصوح بها: 1200x800 بكسل\n\nنصائح:\n• استخدم صور واضحة وعالية الجودة\n• صوّر من زوايا مختلفة\n• تجنب الصور المكررة';
    }

    // أسئلة حول البحث
    else if (message.includes('بحث') || message.includes('أجد') || message.includes('أبحث')) {
      return '🔍 البحث في الموقع:\n\n1. استخدم شريط البحث في الأعلى\n2. اختر التصنيف المناسب\n3. استخدم الفلاتر المتقدمة:\n   • السعر\n   • الموقع\n   • تاريخ النشر\n   • نوع الإعلان\n\n💡 نصيحة: استخدم كلمات مفتاحية محددة للحصول على نتائج أفضل.';
    }

    // أسئلة حول التواصل
    else if (message.includes('تواصل') || message.includes('اتصال') || message.includes('رسالة')) {
      return '📞 التواصل مع المعلنين:\n\n✅ الهاتف: اضغط على رقم الهاتف للاتصال\n✅ واتساب: اضغط على أيقونة واتساب\n✅ رسائل الموقع: استخدم نظام الرسائل الداخلي\n\n⚠️ نصائح الأمان:\n• تواصل عبر الموقع أولاً\n• لا تشارك معلومات مالية\n• قابل في أماكن عامة';
    }

    // أسئلة حول الأمان
    else if (message.includes('أمان') || message.includes('احتيال') || message.includes('نصب')) {
      return '🛡️ نصائح الأمان:\n\n❌ لا تدفع مقدماً قبل المعاينة\n❌ لا تشارك معلومات بنكية\n❌ احذر من العروض المشبوهة\n✅ قابل في أماكن عامة\n✅ تأكد من هوية البائع\n✅ استخدم طرق دفع آمنة\n\n📞 للإبلاغ عن احتيال: +963988652401';
    }

    // أسئلة حول التطبيق
    else if (message.includes('تطبيق') || message.includes('موبايل') || message.includes('هاتف')) {
      return '📱 تطبيق من المالك:\n\n🔜 قريباً على:\n• متجر آبل (App Store)\n• متجر جوجل (Google Play)\n\nحالياً يمكنك استخدام الموقع من المتصفح على الهاتف. التطبيق سيوفر:\n• إشعارات فورية\n• سرعة أكبر\n• سهولة في الاستخدام';
    }

    // أسئلة حول المشاكل التقنية
    else if (message.includes('مشكلة') || message.includes('خطأ') || message.includes('لا يعمل')) {
      return '🔧 حل المشاكل التقنية:\n\n1. أعد تحديث الصفحة (F5)\n2. امسح ذاكرة التخزين المؤقت\n3. جرب متصفح آخر\n4. تأكد من اتصال الإنترنت\n5. أعد تشغيل المتصفح\n\nإذا استمرت المشكلة:\n📞 اتصل بنا: +963988652401\n📧 أو راسلنا عبر النموذج';
    }

    // أسئلة عامة
    else if (message.includes('مرحبا') || message.includes('السلام') || message.includes('أهلا')) {
      return '👋 أهلاً وسهلاً بك في موقع من المالك!\n\nأنا المساعد الذكي، يمكنني مساعدتك في:\n• نشر الإعلانات\n• إدارة الحساب\n• حل المشاكل التقنية\n• الإجابة على استفساراتك\n\nكيف يمكنني مساعدتك اليوم؟ 😊';
    }

    // رد افتراضي
    else {
      return '🤖 شكراً لتواصلك معنا!\n\nلم أتمكن من فهم استفسارك بالضبط. يمكنك:\n\n1. إعادة صياغة السؤال\n2. اختيار من المواضيع الشائعة أدناه\n3. التواصل مع فريق الدعم مباشرة\n\n📞 للدعم الفوري: +963988652401\n\nسأكون سعيداً لمساعدتك! 😊';
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage(newMessage);
    }
  };

  return (
    <>
      {/* زر فتح الدردشة */}
      {!isOpen && (
        <button
          onClick={() => setIsOpen(true)}
          className="fixed bottom-6 left-6 w-16 h-16 bg-gradient-to-br from-blue-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-50 flex items-center justify-center group hover:scale-110"
        >
          {/* رأس الروبوت مع سماعات */}
          <div className="relative">
            {/* الرأس */}
            <div className="w-8 h-8 bg-white/90 rounded-lg flex items-center justify-center relative animate-pulse">
              {/* العيون */}
              <div className="flex gap-1">
                <div className="w-1.5 h-1.5 bg-blue-600 rounded-full animate-ping"></div>
                <div className="w-1.5 h-1.5 bg-blue-600 rounded-full animate-ping" style={{ animationDelay: '0.5s' }}></div>
              </div>

              {/* الفم */}
              <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-0.5 bg-blue-600 rounded-full"></div>
            </div>

            {/* السماعات */}
            <div className="absolute -left-1 top-1 w-2 h-2 bg-gray-300 rounded-full border border-gray-400"></div>
            <div className="absolute -right-1 top-1 w-2 h-2 bg-gray-300 rounded-full border border-gray-400"></div>

            {/* موجات الصوت */}
            <div className="absolute -left-2 top-0 w-1 h-1 bg-green-400 rounded-full animate-bounce opacity-70"></div>
            <div className="absolute -right-2 top-0 w-1 h-1 bg-green-400 rounded-full animate-bounce opacity-70" style={{ animationDelay: '0.3s' }}></div>
          </div>

          {/* مؤشر الاتصال */}
          {isOnline && (
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white animate-pulse"></div>
          )}
        </button>
      )}

      {/* نافذة الدردشة */}
      {isOpen && (
        <div className="fixed bottom-6 left-6 w-80 h-96 bg-white rounded-xl shadow-2xl z-50 flex flex-col overflow-hidden border border-gray-200">
          {/* رأس الدردشة */}
          <div className="bg-primary-600 text-white p-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center relative">
                {/* رأس الروبوت الصغير */}
                <div className="w-5 h-5 bg-white/90 rounded flex items-center justify-center relative">
                  <div className="flex gap-0.5">
                    <div className="w-1 h-1 bg-blue-600 rounded-full"></div>
                    <div className="w-1 h-1 bg-blue-600 rounded-full"></div>
                  </div>
                  <div className="absolute bottom-0.5 left-1/2 transform -translate-x-1/2 w-1 h-0.5 bg-blue-600 rounded-full"></div>
                </div>
                {/* السماعات الصغيرة */}
                <div className="absolute -left-0.5 top-1 w-1 h-1 bg-gray-300 rounded-full"></div>
                <div className="absolute -right-0.5 top-1 w-1 h-1 bg-gray-300 rounded-full"></div>
              </div>
              <div>
                <h3 className="font-semibold">المساعد الذكي</h3>
                <div className="flex items-center gap-2 text-xs">
                  <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                  <span>{isOnline ? 'متصل الآن' : 'غير متصل'}</span>
                </div>
              </div>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="text-white/80 hover:text-white text-xl"
            >
              ×
            </button>
          </div>

          {/* منطقة الرسائل */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs px-4 py-2 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  <p className="text-sm">{message.text}</p>
                  <span className={`text-xs ${
                    message.sender === 'user' ? 'text-primary-200' : 'text-gray-500'
                  } block mt-1`}>
                    {message.timestamp}
                  </span>
                </div>
              </div>
            ))}

            {/* مؤشر الكتابة */}
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 px-4 py-2 rounded-lg">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* الردود السريعة */}
          {messages.length === 1 && (
            <div className="p-3 border-t border-gray-200">
              <p className="text-xs text-gray-600 mb-2">مواضيع شائعة:</p>
              <div className="flex flex-wrap gap-1">
                {quickReplies.slice(0, 3).map((reply, index) => (
                  <button
                    key={index}
                    onClick={() => sendMessage(reply)}
                    className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full hover:bg-gray-200 transition-colors"
                  >
                    {reply}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* منطقة الإدخال */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex gap-2">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="اكتب رسالتك..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
              />
              <button
                onClick={() => sendMessage(newMessage)}
                disabled={!newMessage.trim()}
                className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span className="text-sm">📤</span>
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-2 text-center">
              عادة ما نرد خلال دقائق قليلة
            </p>
          </div>
        </div>
      )}
    </>
  );
};

export default LiveChat;
