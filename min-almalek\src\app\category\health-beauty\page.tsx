'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import FilterModal from '@/components/FilterModal';

const HealthBeautyPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedSubCategory, setSelectedSubCategory] = useState('');
  const [selectedGender, setSelectedGender] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [priceRange, setPriceRange] = useState([0, 1000000]);
  const [sortBy, setSortBy] = useState('newest');
  const [searchQuery, setSearchQuery] = useState('');
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);

  // فلاتر للـ FilterModal
  const [filters, setFilters] = useState({
    mainCategory: '',
    subCategory: '',
    gender: '',
    serviceType: '',
    brand: '',
    skinType: '',
    ageGroup: '',
    priceFrom: '',
    priceTo: '',
    location: ''
  });

  // بيانات وهمية للإعلانات
  const mockAds = [
    {
      id: '1',
      title: 'صالون تجميل نسائي - خدمات شاملة',
      price: 50000,
      currency: 'SYP',
      location: 'دمشق - المزة',
      images: ['/images/health/salon1.jpg'],
      category: 'health-beauty',
      subCategory: 'personal-care',
      serviceType: 'مكياج عرائس',
      targetGender: 'نساء',
      views: 245,
      isFavorite: false,
      isPromoted: true,
      postedAt: '2024-01-15',
      description: 'صالون تجميل متخصص في خدمات العرائس والمكياج الاحترافي'
    },
    {
      id: '2',
      title: 'جلسات ليزر إزالة الشعر',
      price: 75000,
      currency: 'SYP',
      location: 'دمشق - أبو رمانة',
      images: ['/images/health/laser1.jpg'],
      category: 'health-beauty',
      subCategory: 'personal-care',
      serviceType: 'إزالة بالليزر',
      targetGender: 'الجنسين',
      views: 189,
      isFavorite: false,
      isPromoted: false,
      postedAt: '2024-01-14',
      description: 'جلسات ليزر احترافية لإزالة الشعر بأحدث التقنيات'
    },
    {
      id: '3',
      title: 'مدرب شخصي - تدريب منزلي',
      price: 25000,
      currency: 'SYP',
      location: 'دمشق - الشعلان',
      images: ['/images/health/trainer1.jpg'],
      category: 'health-beauty',
      subCategory: 'fitness-aesthetic',
      serviceType: 'مدربين شخصيين',
      targetGender: 'الجنسين',
      views: 156,
      isFavorite: true,
      isPromoted: false,
      postedAt: '2024-01-13',
      description: 'مدرب شخصي معتمد لتدريب اللياقة البدنية في المنزل'
    }
  ];

  const categories = [
    { id: 'personal-care', name: 'العناية الشخصية' },
    { id: 'fitness-aesthetic', name: 'اللياقة والتجميل الطبي' },
    { id: 'medical-health', name: 'الطب والصحة' },
    { id: 'body-care', name: 'العناية بالجسم والاسترخاء' },
    { id: 'products', name: 'منتجات الصحة والجمال' }
  ];

  const subCategories = {
    'personal-care': [
      { id: 'hair', name: 'الشعر' },
      { id: 'makeup', name: 'المكياج' },
      { id: 'skincare', name: 'البشرة' },
      { id: 'nails', name: 'الأظافر' },
      { id: 'hair-removal', name: 'إزالة الشعر' }
    ],
    'fitness-aesthetic': [
      { id: 'fitness', name: 'اللياقة' },
      { id: 'medical-aesthetic', name: 'التجميل الطبي' }
    ],
    'medical-health': [
      { id: 'clinics', name: 'العيادات' },
      { id: 'home-medical', name: 'الخدمات الطبية المنزلية' }
    ],
    'body-care': [
      { id: 'massage', name: 'المساج والاسترخاء' },
      { id: 'slimming', name: 'التنحيف وتشكيل الجسم' }
    ],
    'products': [
      { id: 'cosmetics', name: 'مستحضرات التجميل' },
      { id: 'devices', name: 'أجهزة العناية' },
      { id: 'supplements', name: 'المكملات والأعشاب' }
    ]
  };

  const locations = [
    'دمشق - المزة',
    'دمشق - أبو رمانة',
    'دمشق - الشعلان',
    'دمشق - المالكي',
    'دمشق - الصالحية',
    'حلب - الفرقان',
    'حلب - الجميلية',
    'حمص - الوعر',
    'حمص - الخالدية'
  ];

  // دوال للفلاتر
  const handleFilterChange = (key: string, value: string | string[]) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      mainCategory: '',
      subCategory: '',
      gender: '',
      serviceType: '',
      brand: '',
      skinType: '',
      ageGroup: '',
      priceFrom: '',
      priceTo: '',
      location: ''
    });
  };

  const filteredAds = mockAds.filter(ad => {
    const matchesSearch = ad.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         ad.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = !selectedCategory || ad.subCategory === selectedCategory;
    const matchesSubCategory = !selectedSubCategory || ad.serviceType?.includes(selectedSubCategory);
    const matchesGender = !selectedGender || ad.targetGender === selectedGender || ad.targetGender === 'الجنسين';
    const matchesLocation = !selectedLocation || ad.location.includes(selectedLocation);
    const matchesPrice = ad.price >= priceRange[0] && ad.price <= priceRange[1];

    return matchesSearch && matchesCategory && matchesSubCategory && matchesGender && matchesLocation && matchesPrice;
  });

  const sortedAds = [...filteredAds].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price;
      case 'price-high':
        return b.price - a.price;
      case 'popular':
        return b.views - a.views;
      case 'newest':
      default:
        return new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime();
    }
  });

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-white">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        {/* العنوان الرئيسي */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full flex items-center justify-center">
              <span className="text-2xl">💄</span>
            </div>
            <h1 className="text-4xl font-bold text-gray-800">الصحة والجمال</h1>
          </div>
          <p className="text-gray-600 text-lg">اكتشف أفضل خدمات الصحة والجمال في منطقتك</p>
        </div>

        {/* زر فتح الفلاتر على الموبايل */}
        <div className="lg:hidden mb-6">
          <button
            onClick={() => setIsFilterModalOpen(true)}
            className="w-full bg-white rounded-xl shadow-lg p-4 flex items-center justify-center gap-3 border border-gray-200 hover:bg-gray-50 transition-colors"
          >
            <img
              src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
              alt="فلاتر"
              className="w-6 h-6 opacity-80"
              style={{
                filter: 'drop-shadow(0 0 4px rgba(236, 72, 153, 0.6))'
              }}
            />
            <span className="text-lg font-semibold text-gray-800">البحث والفلاتر</span>
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* الفلاتر الجانبية */}
          <div className="lg:col-span-1 hidden lg:block">
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-lg border border-pink-200/50 sticky top-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <span className="text-pink-500">🔍</span>
                البحث والفلاتر
              </h3>

              {/* البحث */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="ابحث عن خدمة..."
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
                />
              </div>

              {/* الفئة الرئيسية */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الرئيسية</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => {
                    setSelectedCategory(e.target.value);
                    setSelectedSubCategory('');
                  }}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
                >
                  <option value="">جميع الفئات</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* الفئة الفرعية */}
              {selectedCategory && subCategories[selectedCategory as keyof typeof subCategories] && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الفرعية</label>
                  <select
                    value={selectedSubCategory}
                    onChange={(e) => setSelectedSubCategory(e.target.value)}
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
                  >
                    <option value="">جميع الفئات الفرعية</option>
                    {subCategories[selectedCategory as keyof typeof subCategories].map(subCategory => (
                      <option key={subCategory.id} value={subCategory.name}>
                        {subCategory.name}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* الجنس المستهدف */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الجنس المستهدف</label>
                <select
                  value={selectedGender}
                  onChange={(e) => setSelectedGender(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
                >
                  <option value="">الجميع</option>
                  <option value="رجال">رجال</option>
                  <option value="نساء">نساء</option>
                  <option value="الجنسين">الجنسين</option>
                </select>
              </div>

              {/* الموقع */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                <select
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
                >
                  <option value="">جميع المواقع</option>
                  {locations.map(location => (
                    <option key={location} value={location}>
                      {location}
                    </option>
                  ))}
                </select>
              </div>

              {/* نطاق السعر */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نطاق السعر: {priceRange[0].toLocaleString()} - {priceRange[1].toLocaleString()} ل.س
                </label>
                <div className="space-y-2">
                  <input
                    type="range"
                    min="0"
                    max="1000000"
                    step="10000"
                    value={priceRange[0]}
                    onChange={(e) => setPriceRange([parseInt(e.target.value), priceRange[1]])}
                    className="w-full"
                  />
                  <input
                    type="range"
                    min="0"
                    max="1000000"
                    step="10000"
                    value={priceRange[1]}
                    onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                    className="w-full"
                  />
                </div>
              </div>

              {/* إعادة تعيين الفلاتر */}
              <button
                onClick={() => {
                  setSelectedCategory('');
                  setSelectedSubCategory('');
                  setSelectedGender('');
                  setSelectedLocation('');
                  setPriceRange([0, 1000000]);
                  setSearchQuery('');
                }}
                className="w-full bg-gradient-to-r from-pink-500 to-purple-500 text-white py-2 px-4 rounded-lg hover:from-pink-600 hover:to-purple-600 transition-all duration-300"
              >
                إعادة تعيين الفلاتر
              </button>
            </div>
          </div>

          {/* النتائج */}
          <div className="lg:col-span-3">
            {/* شريط الترتيب */}
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 mb-6 shadow-lg border border-pink-200/50">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="text-gray-600">
                  تم العثور على <span className="font-semibold text-pink-600">{sortedAds.length}</span> إعلان
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium text-gray-700">ترتيب حسب:</label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="popular">الأكثر مشاهدة</option>
                    <option value="price-low">السعر: من الأقل للأعلى</option>
                    <option value="price-high">السعر: من الأعلى للأقل</option>
                  </select>
                </div>
              </div>
            </div>

            {/* الإعلانات */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {sortedAds.map(ad => (
                <AdCard key={ad.id} ad={ad} />
              ))}
            </div>

            {/* رسالة عدم وجود نتائج */}
            {sortedAds.length === 0 && (
              <div className="text-center py-12">
                <div className="w-24 h-24 bg-gradient-to-r from-pink-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-4xl">🔍</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد نتائج</h3>
                <p className="text-gray-600">جرب تعديل الفلاتر للعثور على المزيد من النتائج</p>
              </div>
            )}
          </div>
        </div>

        {/* Modal الفلاتر للموبايل */}
        <FilterModal
          isOpen={isFilterModalOpen}
          onClose={() => setIsFilterModalOpen(false)}
          filters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={clearFilters}
          category="health-beauty"
        />
      </div>

      <Footer />
    </div>
  );
};

export default HealthBeautyPage;
