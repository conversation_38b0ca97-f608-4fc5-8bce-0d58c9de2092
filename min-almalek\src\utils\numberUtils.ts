/**
 * تحويل الأرقام العربية إلى أرقام إنجليزية
 */
export const toEnglishNumbers = (str: string | number): string => {
  if (typeof str === 'number') {
    return str.toString();
  }
  
  const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
  const englishNumbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
  
  let result = str;
  for (let i = 0; i < arabicNumbers.length; i++) {
    result = result.replace(new RegExp(arabicNumbers[i], 'g'), englishNumbers[i]);
  }
  
  return result;
};

/**
 * تنسيق الأرقام مع فواصل الآلاف (بالأرقام الإنجليزية)
 */
export const formatNumber = (num: number): string => {
  return num.toLocaleString('en-US');
};

/**
 * تنسيق السعر مع العملة (بالأرقام الإنجليزية)
 */
export const formatPrice = (price: number, currency: string = 'ل.س'): string => {
  return `${formatNumber(price)} ${currency}`;
};

/**
 * تنسيق التاريخ بالأرقام الإنجليزية
 */
export const formatDate = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return toEnglishNumbers(dateObj.toLocaleDateString('ar-SA'));
};
