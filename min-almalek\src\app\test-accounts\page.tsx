'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

export default function TestAccountsPage() {
  const { login } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState<string | null>(null);

  const handleLogin = async (email: string, password: string, accountType: string) => {
    setIsLoading(accountType);
    try {
      console.log('محاولة تسجيل الدخول:', { email, password });

      // تسجيل الدخول المباشر
      await login({ email, password });

      // انتظار قصير للتأكد من حفظ البيانات
      await new Promise(resolve => setTimeout(resolve, 500));

      // التوجه للملف الشخصي
      router.push('/profile');
    } catch (error) {
      console.error('Login failed:', error);

      // محاولة تسجيل الدخول المباشر بدون AuthContext
      if (email === '<EMAIL>' && password === 'Gr123456') {
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('currentUser', JSON.stringify({
          id: 'user-business-001',
          email: '<EMAIL>',
          name: 'Grand Mark Turkey',
          userType: 'business',
          membershipId: 'GMT-2024-001',
          subscriptionPlan: 'business-professional',
          verificationBadge: 'gold'
        }));
        window.location.href = '/profile';
        return;
      }

      if (email === '<EMAIL>' && password === 'Du123456') {
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('currentUser', JSON.stringify({
          id: 'user-realestate-001',
          email: '<EMAIL>',
          name: 'مكتب دنيا زاد العقاري',
          userType: 'real-estate-office',
          membershipId: 'DZ-2024-001',
          subscriptionPlan: 'real-estate-office',
          verificationBadge: 'silver'
        }));
        window.location.href = '/profile';
        return;
      }

      alert('فشل في تسجيل الدخول: ' + (error.message || 'خطأ غير معروف'));
    } finally {
      setIsLoading(null);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">الحسابات التجريبية</h1>
          <p className="text-gray-600">اختر نوع الحساب للدخول المباشر وتجربة المميزات</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* حساب الشركة */}
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
            <div className="text-center mb-6">
              <div className="text-6xl mb-4 opacity-80" 
                   style={{filter: 'drop-shadow(0 0 12px rgba(251, 191, 36, 0.4))'}}>
                🏢
              </div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">حساب الشركة</h2>
              <p className="text-gray-600">Grand Mark Turkey</p>
            </div>

            <div className="space-y-4 mb-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg opacity-80" style={{filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.3))'}}>📧</span>
                  <span className="font-medium text-gray-700">البريد الإلكتروني:</span>
                </div>
                <p className="text-gray-800 font-mono"><EMAIL></p>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg opacity-80" style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}>🔑</span>
                  <span className="font-medium text-gray-700">كلمة المرور:</span>
                </div>
                <p className="text-gray-800 font-mono">Gr123456</p>
              </div>

              <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 p-4 rounded-lg border border-yellow-200">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg opacity-80" style={{filter: 'drop-shadow(0 0 4px rgba(251, 191, 36, 0.3))'}}>⭐</span>
                  <span className="font-medium text-yellow-700">نوع الاشتراك:</span>
                </div>
                <p className="text-yellow-800 font-semibold">الخطة المهنية (ذهبي)</p>
              </div>
            </div>

            <button
              onClick={() => handleLogin('<EMAIL>', 'Gr123456', 'business')}
              disabled={isLoading === 'business'}
              className="w-full bg-gradient-to-r from-yellow-500 to-yellow-600 text-white py-4 rounded-lg font-semibold hover:from-yellow-600 hover:to-yellow-700 transition-all duration-300 disabled:opacity-50 flex items-center justify-center gap-2"
            >
              {isLoading === 'business' ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  جاري تسجيل الدخول...
                </>
              ) : (
                <>
                  <span className="opacity-80" style={{filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.3))'}}>🚀</span>
                  دخول مباشر للشركة
                </>
              )}
            </button>
          </div>

          {/* حساب المكتب العقاري */}
          <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-8">
            <div className="text-center mb-6">
              <div className="text-6xl mb-4 opacity-80" 
                   style={{filter: 'drop-shadow(0 0 12px rgba(34, 197, 94, 0.4))'}}>
                🏘️
              </div>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">المكتب العقاري</h2>
              <p className="text-gray-600">مكتب دنيا زاد العقاري</p>
            </div>

            <div className="space-y-4 mb-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg opacity-80" style={{filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.3))'}}>📧</span>
                  <span className="font-medium text-gray-700">البريد الإلكتروني:</span>
                </div>
                <p className="text-gray-800 font-mono"><EMAIL></p>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg opacity-80" style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}>🔑</span>
                  <span className="font-medium text-gray-700">كلمة المرور:</span>
                </div>
                <p className="text-gray-800 font-mono">Du123456</p>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg border border-green-200">
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-lg opacity-80" style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}>🏆</span>
                  <span className="font-medium text-green-700">نوع الاشتراك:</span>
                </div>
                <p className="text-green-800 font-semibold">مكتب عقاري معتمد</p>
              </div>
            </div>

            <button
              onClick={() => handleLogin('<EMAIL>', 'Du123456', 'realestate')}
              disabled={isLoading === 'realestate'}
              className="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-4 rounded-lg font-semibold hover:from-green-600 hover:to-green-700 transition-all duration-300 disabled:opacity-50 flex items-center justify-center gap-2"
            >
              {isLoading === 'realestate' ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  جاري تسجيل الدخول...
                </>
              ) : (
                <>
                  <span className="opacity-80" style={{filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.3))'}}>🏠</span>
                  دخول مباشر للمكتب العقاري
                </>
              )}
            </button>
          </div>
        </div>

        {/* روابط سريعة */}
        <div className="mt-12 text-center">
          <h3 className="text-xl font-bold text-gray-800 mb-6">روابط سريعة للاختبار</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="/profile"
              className="bg-blue-500 text-white py-3 px-6 rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center gap-2"
            >
              <span className="opacity-80">👤</span>
              الملف الشخصي
            </a>
            <a
              href="/company/dashboard"
              className="bg-yellow-500 text-white py-3 px-6 rounded-lg hover:bg-yellow-600 transition-colors flex items-center justify-center gap-2"
            >
              <span className="opacity-80">🏢</span>
              لوحة تحكم الشركة
            </a>
            <a
              href="/real-estate-office"
              className="bg-green-500 text-white py-3 px-6 rounded-lg hover:bg-green-600 transition-colors flex items-center justify-center gap-2"
            >
              <span className="opacity-80">🏘️</span>
              صفحة المكتب العقاري
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
