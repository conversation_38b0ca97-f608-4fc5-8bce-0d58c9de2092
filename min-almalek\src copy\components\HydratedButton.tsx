'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface HydratedButtonProps {
  href?: string;
  onClick?: (e: React.MouseEvent) => void;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
  title?: string;
  [key: string]: any;
}

const HydratedButton = ({ 
  href, 
  onClick, 
  children, 
  className, 
  disabled = false,
  type = 'button',
  title,
  ...props 
}: HydratedButtonProps) => {
  const router = useRouter();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleClick = (e: React.MouseEvent) => {
    if (disabled) return;
    
    if (onClick) {
      onClick(e);
    }
    
    if (href && isMounted) {
      e.preventDefault();
      router.push(href);
    }
  };

  // عرض زر عادي قبل الـ hydration
  if (!isMounted) {
    return (
      <button
        type={type}
        className={className}
        disabled={disabled}
        title={title}
        {...props}
      >
        {children}
      </button>
    );
  }

  return (
    <button
      type={type}
      onClick={handleClick}
      className={className}
      disabled={disabled}
      title={title}
      {...props}
    >
      {children}
    </button>
  );
};

export default HydratedButton;
