'use client';

import { ReactNode } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-hot-toast';

interface ProtectedActionButtonProps {
  children: ReactNode;
  onClick: () => void | Promise<void>;
  requireAuth?: boolean;
  authMessage?: string;
  className?: string;
  disabled?: boolean;
  showAuthModal?: () => void;
  type?: 'button' | 'submit' | 'reset';
  title?: string;
}

export default function ProtectedActionButton({
  children,
  onClick,
  requireAuth = true,
  authMessage = 'يجب تسجيل الدخول لاستخدام هذه الميزة',
  className = '',
  disabled = false,
  showAuthModal,
  type = 'button',
  title
}: ProtectedActionButtonProps) {
  const { isAuthenticated } = useAuth();

  const handleClick = async () => {
    if (requireAuth && !isAuthenticated) {
      toast.error(authMessage);
      
      if (showAuthModal) {
        showAuthModal();
      }
      
      return;
    }

    if (disabled) return;

    try {
      await onClick();
    } catch (error) {
      console.error('خطأ في تنفيذ الإجراء:', error);
      toast.error('حدث خطأ أثناء تنفيذ العملية');
    }
  };

  return (
    <button
      type={type}
      onClick={handleClick}
      className={className}
      disabled={disabled}
      title={title}
    >
      {children}
    </button>
  );
}

// مكونات مخصصة للإجراءات المختلفة

interface MessageButtonProps {
  children: ReactNode;
  onSendMessage: () => void | Promise<void>;
  className?: string;
  disabled?: boolean;
  recipientName?: string;
}

export function MessageButton({
  children,
  onSendMessage,
  className = '',
  disabled = false,
  recipientName = ''
}: MessageButtonProps) {
  return (
    <ProtectedActionButton
      onClick={onSendMessage}
      requireAuth={true}
      authMessage={`يجب تسجيل الدخول لإرسال رسالة${recipientName ? ` إلى ${recipientName}` : ''} 📨`}
      className={className}
      disabled={disabled}
      title="إرسال رسالة"
    >
      {children}
    </ProtectedActionButton>
  );
}

interface FavoriteButtonProps {
  children: ReactNode;
  onToggleFavorite: () => void | Promise<void>;
  className?: string;
  disabled?: boolean;
  isFavorite?: boolean;
}

export function FavoriteButton({
  children,
  onToggleFavorite,
  className = '',
  disabled = false,
  isFavorite = false
}: FavoriteButtonProps) {
  return (
    <ProtectedActionButton
      onClick={onToggleFavorite}
      requireAuth={true}
      authMessage={`يجب تسجيل الدخول ${isFavorite ? 'لإزالة من' : 'لإضافة إلى'} المفضلة ❤️`}
      className={className}
      disabled={disabled}
      title={isFavorite ? 'إزالة من المفضلة' : 'إضافة للمفضلة'}
    >
      {children}
    </ProtectedActionButton>
  );
}

interface FollowButtonProps {
  children: ReactNode;
  onToggleFollow: () => void | Promise<void>;
  className?: string;
  disabled?: boolean;
  isFollowing?: boolean;
  sellerName?: string;
}

export function FollowButton({
  children,
  onToggleFollow,
  className = '',
  disabled = false,
  isFollowing = false,
  sellerName = ''
}: FollowButtonProps) {
  return (
    <ProtectedActionButton
      onClick={onToggleFollow}
      requireAuth={true}
      authMessage={`يجب تسجيل الدخول ${isFollowing ? 'لإلغاء متابعة' : 'لمتابعة'} ${sellerName || 'البائع'} 👥`}
      className={className}
      disabled={disabled}
      title={isFollowing ? 'إلغاء المتابعة' : 'متابعة'}
    >
      {children}
    </ProtectedActionButton>
  );
}

interface RatingButtonProps {
  children: ReactNode;
  onSubmitRating: () => void | Promise<void>;
  className?: string;
  disabled?: boolean;
}

export function RatingButton({
  children,
  onSubmitRating,
  className = '',
  disabled = false
}: RatingButtonProps) {
  return (
    <ProtectedActionButton
      onClick={onSubmitRating}
      requireAuth={true}
      authMessage="يجب تسجيل الدخول لإضافة تقييم ⭐"
      className={className}
      disabled={disabled}
      title="إضافة تقييم"
    >
      {children}
    </ProtectedActionButton>
  );
}

interface CommentButtonProps {
  children: ReactNode;
  onSubmitComment: () => void | Promise<void>;
  className?: string;
  disabled?: boolean;
}

export function CommentButton({
  children,
  onSubmitComment,
  className = '',
  disabled = false
}: CommentButtonProps) {
  return (
    <ProtectedActionButton
      onClick={onSubmitComment}
      requireAuth={true}
      authMessage="يجب تسجيل الدخول لإضافة تعليق 💬"
      className={className}
      disabled={disabled}
      title="إضافة تعليق"
    >
      {children}
    </ProtectedActionButton>
  );
}

interface SaveSearchButtonProps {
  children: ReactNode;
  onSaveSearch: () => void | Promise<void>;
  className?: string;
  disabled?: boolean;
}

export function SaveSearchButton({
  children,
  onSaveSearch,
  className = '',
  disabled = false
}: SaveSearchButtonProps) {
  return (
    <ProtectedActionButton
      onClick={onSaveSearch}
      requireAuth={true}
      authMessage="يجب تسجيل الدخول لحفظ عمليات البحث 🔍"
      className={className}
      disabled={disabled}
      title="حفظ البحث"
    >
      {children}
    </ProtectedActionButton>
  );
}

interface AddAdButtonProps {
  children: ReactNode;
  onAddAd: () => void | Promise<void>;
  className?: string;
  disabled?: boolean;
}

export function AddAdButton({
  children,
  onAddAd,
  className = '',
  disabled = false
}: AddAdButtonProps) {
  return (
    <ProtectedActionButton
      onClick={onAddAd}
      requireAuth={true}
      authMessage="يجب تسجيل الدخول لإضافة إعلان جديد 📝"
      className={className}
      disabled={disabled}
      title="إضافة إعلان"
    >
      {children}
    </ProtectedActionButton>
  );
}
