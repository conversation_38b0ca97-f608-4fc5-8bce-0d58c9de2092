import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';
import { MessageService } from '@/lib/messages';

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    // التحقق من صحة الجلسة
    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const conversationId = searchParams.get('conversationId');

    if (conversationId) {
      // الحصول على رسائل محادثة معينة
      const messages = MessageService.getConversationMessages(
        parseInt(conversationId),
        sessionResult.data.id
      );

      return NextResponse.json({
        success: true,
        data: messages
      });
    } else {
      // الحصول على جميع المحادثات
      const conversations = MessageService.getUserConversations(sessionResult.data.id);

      return NextResponse.json({
        success: true,
        data: conversations
      });
    }

  } catch (error) {
    console.error('خطأ في جلب الرسائل:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    // التحقق من صحة الجلسة
    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { receiverId, content, type, adId, offer } = await request.json();

    // التحقق من البيانات المطلوبة
    if (!receiverId || !content) {
      return NextResponse.json(
        { success: false, error: 'بيانات غير مكتملة' },
        { status: 400 }
      );
    }

    // إرسال الرسالة
    const result = await MessageService.sendMessage(
      sessionResult.data.id,
      receiverId,
      content,
      type || 'text',
      adId,
      offer
    );

    if (result.success) {
      return NextResponse.json({
        success: true,
        data: result.data,
        message: 'تم إرسال الرسالة بنجاح'
      });
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('خطأ في إرسال الرسالة:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
