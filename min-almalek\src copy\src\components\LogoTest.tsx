'use client';

import { useState } from 'react';
import Image from 'next/image';

const LogoTest = () => {
  const [imageError, setImageError] = useState(false);
  const [currentImage, setCurrentImage] = useState('/images/logo . min almalek.jpg');

  const testImages = [
    '/images/logo . min almalek.jpg',
    '/images/Adsız tasarım (3)_page-0001-Photoroom.png',
    '/images/Adsız tasarım (3)_page-0001.jpg',
  ];

  return (
    <div className="p-8 bg-gray-100">
      <h2 className="text-2xl font-bold mb-6">اختبار الشعار</h2>

      <div className="space-y-6">
        {/* اختبار الصور المختلفة */}
        <div>
          <h3 className="text-lg font-semibold mb-4">الصور المتاحة:</h3>
          <div className="flex gap-4">
            {testImages.map((imageSrc, index) => (
              <button
                key={index}
                onClick={() => {
                  setCurrentImage(imageSrc);
                  setImageError(false);
                }}
                className={`px-4 py-2 rounded-lg ${
                  currentImage === imageSrc
                    ? 'bg-primary-600 text-white'
                    : 'bg-gray-200 text-gray-700'
                }`}
              >
                صورة {index + 1}
              </button>
            ))}
          </div>
        </div>

        {/* عرض الشعار الحالي */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-4">الشعار الحالي:</h3>
          <div className="flex items-center gap-4">
            {!imageError ? (
              <Image
                src={currentImage}
                alt="من المالك"
                width={120}
                height={60}
                className="h-12 w-auto object-contain"
                onError={() => setImageError(true)}
              />
            ) : (
              <div className="h-12 w-16 bg-primary-600 rounded-lg flex items-center justify-center text-white font-bold">
                <span className="text-lg">🏠</span>
              </div>
            )}
            <span className="text-lg font-bold text-primary-600">من المالك</span>
          </div>

          {imageError && (
            <p className="text-red-600 text-sm mt-2">
              فشل في تحميل الصورة: {currentImage}
            </p>
          )}
        </div>

        {/* معلومات الملفات */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-4">معلومات الملفات:</h3>
          <div className="space-y-2 text-sm">
            <p><strong>المسار الحالي:</strong> {currentImage}</p>
            <p><strong>حالة التحميل:</strong> {imageError ? 'فشل' : 'نجح'}</p>
          </div>
        </div>

        {/* اختبار أحجام مختلفة */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold mb-4">أحجام مختلفة:</h3>
          <div className="space-y-4">
            {['h-6', 'h-8', 'h-10', 'h-12', 'h-16'].map((sizeClass, index) => (
              <div key={index} className="flex items-center gap-4">
                <span className="w-16 text-sm">{sizeClass}:</span>
                {!imageError ? (
                  <Image
                    src={currentImage}
                    alt="من المالك"
                    width={120}
                    height={60}
                    className={`${sizeClass} w-auto object-contain`}
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className={`${sizeClass} w-16 bg-primary-600 rounded-lg flex items-center justify-center text-white font-bold`}>
                    <span>🏠</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LogoTest;
