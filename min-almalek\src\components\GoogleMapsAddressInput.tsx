'use client';

import { useState, useEffect } from 'react';

interface GoogleMapsAddressInputProps {
  governorate: string;
  city: string;
  area?: string;
  onLocationSelect?: (location: { lat: number; lng: number; address: string }) => void;
  className?: string;
}

const GoogleMapsAddressInput = ({ 
  governorate, 
  city, 
  area, 
  onLocationSelect,
  className = ""
}: GoogleMapsAddressInputProps) => {
  const [showMap, setShowMap] = useState(false);
  const [mapUrl, setMapUrl] = useState('');

  // تحديث رابط الخريطة عند تغيير العنوان
  useEffect(() => {
    if (governorate && city) {
      const fullAddress = `${city}, ${governorate}, Syria${area ? `, ${area}` : ''}`;
      const encodedAddress = encodeURIComponent(fullAddress);
      const url = `https://www.google.com/maps?q=${encodedAddress}&output=embed&z=14`;
      setMapUrl(url);
      setShowMap(true);
    } else {
      setShowMap(false);
    }
  }, [governorate, city, area]);

  const getFullAddress = () => {
    const parts = [city, governorate, 'سوريا'];
    if (area) parts.splice(1, 0, area);
    return parts.filter(Boolean).join(', ');
  };

  const handleOpenInGoogleMaps = () => {
    const fullAddress = getFullAddress();
    const encodedAddress = encodeURIComponent(fullAddress);
    window.open(`https://www.google.com/maps/search/${encodedAddress}`, '_blank');
  };

  if (!showMap) {
    return null;
  }

  return (
    <div className={`bg-white rounded-xl shadow-lg p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-800 flex items-center gap-2">
          🗺️ موقع الإعلان
        </h3>
        <button
          onClick={handleOpenInGoogleMaps}
          className="text-sm text-primary-600 hover:text-primary-700 font-medium flex items-center gap-1"
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
          </svg>
          فتح في خرائط جوجل
        </button>
      </div>
      
      <div className="relative h-64 bg-gray-100 rounded-lg overflow-hidden">
        <iframe
          src={mapUrl}
          width="100%"
          height="100%"
          style={{ border: 0 }}
          allowFullScreen
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
          className="rounded-lg"
          title={`خريطة موقع ${getFullAddress()}`}
        />
        <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-2 rounded-lg shadow-md">
          <div className="text-sm font-medium text-gray-800">📍 {getFullAddress()}</div>
        </div>
      </div>
      
      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
        <div className="flex items-start gap-2">
          <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
          <div className="text-sm text-blue-800">
            <div className="font-medium mb-1">نصيحة:</div>
            <div>تأكد من دقة الموقع المعروض على الخريطة. يمكنك النقر على "فتح في خرائط جوجل" للتحقق من الموقع بدقة أكبر.</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GoogleMapsAddressInput;
