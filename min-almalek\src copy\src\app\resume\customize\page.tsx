'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ContactButtons from '@/components/ContactButtons';

interface TemplateCustomization {
  colorScheme: string[];
  fonts: string[];
  layouts: string[];
  sections: string[];
  languages: string[];
}

interface CustomizationOptions {
  selectedColor: string;
  selectedFont: string;
  selectedLayout: string;
  selectedSections: string[];
  selectedLanguage: string;
  includePhoto: boolean;
  includeReferences: boolean;
  includeHobbies: boolean;
  includeCertifications: boolean;
}

// بيانات القوالب (مبسطة)
const templateCustomizations: Record<string, TemplateCustomization> = {
  'executive-pro': {
    colorScheme: ['أزرق داكن', 'رمادي أنثراسيت', 'أسود كلاسيكي', 'كحلي ملكي'],
    fonts: ['Playfair Display', 'Merriweather', 'Source Sans Pro', 'Roboto'],
    layouts: ['عمود واحد', 'عمودين', 'تخطيط هجين'],
    sections: ['ملخص تنفيذي', 'الإنجازات الرئيسية', 'القيادة والإدارة', 'الجوائز والتقديرات'],
    languages: ['العربية', 'الإنجليزية', 'الفرنسية']
  },
  'tech-innovator': {
    colorScheme: ['أزرق تقني', 'أخضر برمجي', 'بنفسجي حديث', 'برتقالي ديناميكي'],
    fonts: ['Fira Code', 'Source Code Pro', 'Inter', 'JetBrains Mono'],
    layouts: ['شبكة تقنية', 'تخطيط كود', 'تصميم API'],
    sections: ['المشاريع التقنية', 'المساهمات مفتوحة المصدر', 'الشهادات التقنية', 'المدونة التقنية'],
    languages: ['العربية', 'الإنجليزية']
  }
};

const colorMappings: Record<string, string> = {
  'أزرق داكن': 'bg-blue-800',
  'رمادي أنثراسيت': 'bg-gray-800',
  'أسود كلاسيكي': 'bg-black',
  'كحلي ملكي': 'bg-blue-900',
  'أزرق تقني': 'bg-blue-600',
  'أخضر برمجي': 'bg-green-600',
  'بنفسجي حديث': 'bg-purple-600',
  'برتقالي ديناميكي': 'bg-orange-500'
};

export default function CustomizePage() {
  const searchParams = useSearchParams();
  const templateId = searchParams.get('template') || 'executive-pro';

  const [customization, setCustomization] = useState<CustomizationOptions>({
    selectedColor: '',
    selectedFont: '',
    selectedLayout: '',
    selectedSections: [],
    selectedLanguage: 'العربية',
    includePhoto: true,
    includeReferences: false,
    includeHobbies: false,
    includeCertifications: true
  });

  const templateConfig = templateCustomizations[templateId];

  useEffect(() => {
    if (templateConfig) {
      setCustomization(prev => ({
        ...prev,
        selectedColor: templateConfig.colorScheme[0],
        selectedFont: templateConfig.fonts[0],
        selectedLayout: templateConfig.layouts[0],
        selectedSections: templateConfig.sections.slice(0, 2)
      }));
    }
  }, [templateId, templateConfig]);

  const handleSectionToggle = (section: string) => {
    setCustomization(prev => ({
      ...prev,
      selectedSections: prev.selectedSections.includes(section)
        ? prev.selectedSections.filter(s => s !== section)
        : [...prev.selectedSections, section]
    }));
  };

  const handleCreateResume = () => {
    const params = new URLSearchParams({
      template: templateId,
      color: customization.selectedColor,
      font: customization.selectedFont,
      layout: customization.selectedLayout,
      language: customization.selectedLanguage,
      sections: customization.selectedSections.join(','),
      photo: customization.includePhoto.toString(),
      references: customization.includeReferences.toString(),
      hobbies: customization.includeHobbies.toString(),
      certifications: customization.includeCertifications.toString()
    });

    window.location.href = `/resume/create?${params.toString()}`;
  };

  if (!templateConfig) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">قالب غير موجود</h1>
          <Link href="/resume/templates" className="text-primary-600 hover:underline">
            العودة إلى القوالب
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            ⚙️ تخصيص القالب
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            خصص القالب حسب احتياجاتك وتفضيلاتك الشخصية
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* Customization Panel */}
          <div className="lg:col-span-2 space-y-6 lg:space-y-8">
            {/* Color Scheme */}
            <div className="bg-white rounded-xl shadow-md p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                🎨 نظام الألوان
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {templateConfig.colorScheme.map(color => (
                  <button
                    key={color}
                    onClick={() => setCustomization(prev => ({ ...prev, selectedColor: color }))}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      customization.selectedColor === color
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className={`w-full h-8 ${colorMappings[color]} rounded mb-2`}></div>
                    <div className="text-sm font-medium text-gray-700">{color}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Fonts */}
            <div className="bg-white rounded-xl shadow-md p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                📝 نوع الخط
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {templateConfig.fonts.map(font => (
                  <button
                    key={font}
                    onClick={() => setCustomization(prev => ({ ...prev, selectedFont: font }))}
                    className={`p-4 rounded-lg border-2 text-left transition-all ${
                      customization.selectedFont === font
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="text-lg font-medium mb-1" style={{ fontFamily: font }}>
                      Sample Text عينة نص
                    </div>
                    <div className="text-sm text-gray-600">{font}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Layout */}
            <div className="bg-white rounded-xl shadow-md p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                📐 تخطيط الصفحة
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {templateConfig.layouts.map(layout => (
                  <button
                    key={layout}
                    onClick={() => setCustomization(prev => ({ ...prev, selectedLayout: layout }))}
                    className={`p-4 rounded-lg border-2 transition-all ${
                      customization.selectedLayout === layout
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="w-full h-16 bg-gray-100 rounded mb-2 flex items-center justify-center">
                      <div className="text-2xl">📄</div>
                    </div>
                    <div className="text-sm font-medium text-gray-700">{layout}</div>
                  </button>
                ))}
              </div>
            </div>

            {/* Special Sections */}
            <div className="bg-white rounded-xl shadow-md p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                📋 الأقسام الخاصة
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {templateConfig.sections.map(section => (
                  <label key={section} className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50">
                    <input
                      type="checkbox"
                      checked={customization.selectedSections.includes(section)}
                      onChange={() => handleSectionToggle(section)}
                      className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    />
                    <span className="font-medium text-gray-700">{section}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Additional Options */}
            <div className="bg-white rounded-xl shadow-md p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                ⚡ خيارات إضافية
              </h3>
              <div className="space-y-4">
                <label className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
                  <span className="font-medium text-gray-700">تضمين الصورة الشخصية</span>
                  <input
                    type="checkbox"
                    checked={customization.includePhoto}
                    onChange={(e) => setCustomization(prev => ({ ...prev, includePhoto: e.target.checked }))}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </label>

                <label className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
                  <span className="font-medium text-gray-700">تضمين المراجع</span>
                  <input
                    type="checkbox"
                    checked={customization.includeReferences}
                    onChange={(e) => setCustomization(prev => ({ ...prev, includeReferences: e.target.checked }))}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </label>

                <label className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
                  <span className="font-medium text-gray-700">تضمين الهوايات</span>
                  <input
                    type="checkbox"
                    checked={customization.includeHobbies}
                    onChange={(e) => setCustomization(prev => ({ ...prev, includeHobbies: e.target.checked }))}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </label>

                <label className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
                  <span className="font-medium text-gray-700">تضمين الشهادات</span>
                  <input
                    type="checkbox"
                    checked={customization.includeCertifications}
                    onChange={(e) => setCustomization(prev => ({ ...prev, includeCertifications: e.target.checked }))}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                </label>
              </div>
            </div>

            {/* Language */}
            <div className="bg-white rounded-xl shadow-md p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                🌍 لغة السيرة الذاتية
              </h3>
              <select
                value={customization.selectedLanguage}
                onChange={(e) => setCustomization(prev => ({ ...prev, selectedLanguage: e.target.value }))}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                {templateConfig.languages.map(language => (
                  <option key={language} value={language}>{language}</option>
                ))}
              </select>
            </div>
          </div>

          {/* Preview Panel */}
          <div className="lg:col-span-1 order-first lg:order-last">
            <div className="bg-white rounded-xl shadow-md p-4 lg:p-6 lg:sticky lg:top-8">
              <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                👁️ معاينة مباشرة
              </h3>

              {/* Mini Preview */}
              <div className="border-2 border-gray-200 rounded-lg p-4 mb-6">
                <div className={`w-full h-32 ${colorMappings[customization.selectedColor]} rounded mb-4 flex items-center justify-center text-white`}>
                  <div className="text-center">
                    <div className="text-lg font-bold">اسمك هنا</div>
                    <div className="text-sm opacity-90">المسمى الوظيفي</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="h-2 bg-gray-200 rounded"></div>
                  <div className="h-2 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-2 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>

              {/* Customization Summary */}
              <div className="space-y-3 mb-6 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">اللون:</span>
                  <span className="font-medium">{customization.selectedColor}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">الخط:</span>
                  <span className="font-medium">{customization.selectedFont}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">التخطيط:</span>
                  <span className="font-medium">{customization.selectedLayout}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">اللغة:</span>
                  <span className="font-medium">{customization.selectedLanguage}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">الأقسام الخاصة:</span>
                  <span className="font-medium">{customization.selectedSections.length}</span>
                </div>
              </div>

              {/* Actions */}
              <div className="space-y-3">
                <button
                  onClick={handleCreateResume}
                  className="w-full bg-primary-600 text-white py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors"
                >
                  إنشاء السيرة الذاتية
                </button>

                <Link
                  href="/resume/templates"
                  className="w-full block text-center py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  العودة للقوالب
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
      <ContactButtons variant="floating" />
    </div>
  );
}
