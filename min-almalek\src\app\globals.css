@tailwind base;
@tailwind components;
@tailwind utilities;

/* إخفاء عناصر Grammarly لتجنب مشاكل hydration */
grammarly-extension,
grammarly-popups,
grammarly-desktop-integration,
[data-grammarly-shadow-root],
[data-grammarly-part],
[data-new-gr-c-s-check-loaded],
[data-gr-ext-installed],
[data-new-gr-c-s-loaded] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  width: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

/* منع تحرير النصوص */
* {
  -webkit-user-modify: read-only !important;
  user-select: text;
}

/* منع تحرير النصوص في العناصر المحددة */
h1, h2, h3, h4, h5, h6, p, span, div, a, button, label {
  -webkit-user-modify: read-only !important;
  -moz-user-modify: read-only !important;
  user-modify: read-only !important;
  contenteditable: false !important;
}

/* السماح بالتحرير فقط في حقول الإدخال */
input, textarea, [contenteditable="true"] {
  -webkit-user-modify: read-write !important;
  -moz-user-modify: read-write !important;
  user-modify: read-write !important;
}

/* إصلاح مشاكل hydration العامة */
html {
  scroll-behavior: smooth;
  margin: 0;
  padding: 0;
  height: 100%;
}

/* إزالة المساحات البيضاء من جميع العناصر */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* إعادة تعيين CSS للمتصفح */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* إزالة المساحات الافتراضية */
html,
body,
div,
span,
applet,
object,
iframe,
h1,
h2,
h3,
h4,
h5,
h6,
p,
blockquote,
pre,
a,
abbr,
acronym,
address,
big,
cite,
code,
del,
dfn,
em,
img,
ins,
kbd,
q,
s,
samp,
small,
strike,
strong,
sub,
sup,
tt,
var,
b,
u,
i,
center,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
embed,
figure,
figcaption,
footer,
header,
hgroup,
menu,
nav,
output,
ruby,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

#__next {
  min-height: 100vh;
  margin: 0;
  padding: 0;
}

main {
  margin: 0;
  padding: 0;
}

/* إزالة المساحات البيضاء من العناصر الأساسية */
header {
  margin: 0;
  padding: 0;
}

footer {
  margin: 0;
  padding: 0;
}

/* إزالة المساحات البيضاء من الحاويات */
.container {
  margin-left: auto;
  margin-right: auto;
}

/* إصلاح تخطيط الصفحة */
html, body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
}

/* إزالة المساحات البيضاء الزائدة */
#__next {
  margin: 0;
  padding: 0;
}

/* إصلاح مشكلة المساحات البيضاء */
body > div:first-child {
  margin: 0;
  padding: 0;
}

/* إزالة أي مساحات من الحاويات الرئيسية */
.min-h-screen {
  margin: 0;
  padding: 0;
}



/* منع التمرير الأفقي */
.no-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* منع التحرير في جميع العناصر */
body * {
  -webkit-user-modify: read-only !important;
  -moz-user-modify: read-only !important;
  user-modify: read-only !important;
}

/* منع contentEditable */
[contenteditable]:not(input):not(textarea):not([data-editable="true"]) {
  -webkit-user-modify: read-only !important;
  -moz-user-modify: read-only !important;
  user-modify: read-only !important;
  pointer-events: auto !important;
}

/* إعادة تفعيل التحرير للحقول المطلوبة */
input,
textarea,
[data-editable="true"],
[role="textbox"],
.editable {
  -webkit-user-modify: read-write !important;
  -moz-user-modify: read-write !important;
  user-modify: read-write !important;
}

/* منع تحرير النصوص في الأيقونات والعناوين */
.category-icon,
.category-title,
h1, h2, h3, h4, h5, h6,
.text-content,
.nav-item,
.menu-item {
  -webkit-user-modify: read-only !important;
  -moz-user-modify: read-only !important;
  user-modify: read-only !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

body {
  font-family: var(--font-cairo), 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  direction: rtl;
  text-align: right;
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #14b8a6;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #0d9488;
}

/* Line clamp utility */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Smooth transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

/* Focus styles */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #14b8a6;
  outline-offset: 2px;
}

/* Animation for new items */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out;
}
