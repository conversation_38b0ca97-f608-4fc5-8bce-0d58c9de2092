'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import { useNotificationHelpers } from '@/hooks/useNotificationHelpers';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ClientOnlyWrapper from '@/components/ClientOnlyWrapper';

export default function PasswordSettingsPage() {
  const { user } = useAuth();
  const { notifySuccess, notifyError } = useNotificationHelpers();
  const [isLoading, setIsLoading] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.newPassword !== formData.confirmPassword) {
      notifyError('كلمات المرور الجديدة غير متطابقة');
      return;
    }

    if (formData.newPassword.length < 8) {
      notifyError('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
      return;
    }

    setIsLoading(true);
    try {
      // هنا سيتم إضافة منطق تغيير كلمة المرور
      await new Promise(resolve => setTimeout(resolve, 2000)); // محاكاة API call
      notifySuccess('تم تغيير كلمة المرور بنجاح');
      setFormData({ currentPassword: '', newPassword: '', confirmPassword: '' });
    } catch (error) {
      notifyError('حدث خطأ أثناء تغيير كلمة المرور');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <ClientOnlyWrapper>
      <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-blue-50">
        <Header />
        
        <main className="container mx-auto px-4 py-8">
          {/* شريط التنقل */}
          <div className="mb-6">
            <nav className="flex items-center gap-2 text-sm text-gray-600">
              <Link href="/profile" className="hover:text-green-600 transition-colors">
                الملف الشخصي
              </Link>
              <span>›</span>
              <Link href="/profile" className="hover:text-green-600 transition-colors">
                الإعدادات
              </Link>
              <span>›</span>
              <span className="text-gray-800 font-medium">تغيير كلمة المرور</span>
            </nav>
          </div>

          <div className="max-w-2xl mx-auto">
            <div className="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 p-6">
              {/* العنوان */}
              <div className="flex items-center gap-3 mb-6">
                <span className="text-3xl opacity-80 hover:opacity-100 transition-all duration-300"
                      style={{filter: 'drop-shadow(0 0 8px rgba(239, 68, 68, 0.3))'}}>🔒</span>
                <div>
                  <h1 className="text-2xl font-bold text-gray-800">تغيير كلمة المرور</h1>
                  <p className="text-gray-600">قم بتحديث كلمة المرور لحسابك</p>
                </div>
              </div>

              {/* النموذج */}
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* كلمة المرور الحالية */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    كلمة المرور الحالية
                  </label>
                  <div className="relative">
                    <input
                      type={showCurrentPassword ? 'text' : 'password'}
                      value={formData.currentPassword}
                      onChange={(e) => handleInputChange('currentPassword', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="أدخل كلمة المرور الحالية"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      {showCurrentPassword ? '🙈' : '👁️'}
                    </button>
                  </div>
                </div>

                {/* كلمة المرور الجديدة */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    كلمة المرور الجديدة
                  </label>
                  <div className="relative">
                    <input
                      type={showNewPassword ? 'text' : 'password'}
                      value={formData.newPassword}
                      onChange={(e) => handleInputChange('newPassword', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="أدخل كلمة المرور الجديدة"
                      required
                      minLength={8}
                    />
                    <button
                      type="button"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      {showNewPassword ? '🙈' : '👁️'}
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    يجب أن تحتوي على 8 أحرف على الأقل
                  </p>
                </div>

                {/* تأكيد كلمة المرور */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    تأكيد كلمة المرور الجديدة
                  </label>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                      placeholder="أعد إدخال كلمة المرور الجديدة"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                    >
                      {showConfirmPassword ? '🙈' : '👁️'}
                    </button>
                  </div>
                </div>

                {/* نصائح الأمان */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-800 mb-2">نصائح لكلمة مرور قوية:</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• استخدم 8 أحرف على الأقل</li>
                    <li>• امزج بين الأحرف الكبيرة والصغيرة</li>
                    <li>• أضف أرقام ورموز خاصة</li>
                    <li>• تجنب المعلومات الشخصية</li>
                  </ul>
                </div>

                {/* أزرار الإجراءات */}
                <div className="flex gap-4 pt-4">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="flex-1 bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    style={{filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.3))'}}
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                        جاري التحديث...
                      </>
                    ) : (
                      <>
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"/>
                        </svg>
                        تحديث كلمة المرور
                      </>
                    )}
                  </button>
                  
                  <Link
                    href="/profile"
                    className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center"
                  >
                    إلغاء
                  </Link>
                </div>
              </form>
            </div>
          </div>
        </main>

        <Footer />
      </div>
    </ClientOnlyWrapper>
  );
}
