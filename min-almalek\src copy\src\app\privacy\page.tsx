'use client';

import Header from '@/components/Header';
import Footer from '@/components/Footer';

const privacyData = [
  {
    id: 1,
    title: 'المعلومات التي نجمعها',
    icon: '📊',
    content: [
      'المعلومات الشخصية: الاسم، البريد الإلكتروني، رقم الهاتف',
      'معلومات الحساب: كلمة المرور، تفضيلات الحساب',
      'معلومات الإعلانات: النصوص، الصور، تفاصيل السلع',
      'معلومات الاستخدام: صفحات الزيارة، وقت التصفح، الإجراءات',
      'معلومات الجهاز: نوع المتصفح، نظام التشغيل، عنوان IP'
    ]
  },
  {
    id: 2,
    title: 'كيف نستخدم معلوماتك',
    icon: '🎯',
    content: [
      'تقديم وتحسين خدماتنا',
      'التواصل معك بخصوص حسابك وإعلاناتك',
      'إرسال إشعارات مهمة وتحديثات الخدمة',
      'منع الاحتيال وضمان أمان المنصة',
      'تحليل استخدام الموقع لتحسين التجربة',
      'الامتثال للمتطلبات القانونية'
    ]
  },
  {
    id: 3,
    title: 'مشاركة المعلومات',
    icon: '🤝',
    content: [
      'لا نبيع معلوماتك الشخصية لأطراف ثالثة',
      'قد نشارك معلومات عامة مع المستخدمين الآخرين (الاسم، رقم الهاتف في الإعلانات)',
      'نشارك المعلومات مع مقدمي الخدمات الموثوقين',
      'قد نكشف المعلومات للامتثال للقوانين',
      'في حالة بيع الشركة، قد تنتقل البيانات للمالك الجديد'
    ]
  },
  {
    id: 4,
    title: 'أمان البيانات',
    icon: '🔒',
    content: [
      'نستخدم تشفير SSL لحماية البيانات المنقولة',
      'نحفظ البيانات في خوادم آمنة ومحمية',
      'نحدد الوصول للبيانات للموظفين المخولين فقط',
      'نراجع إجراءات الأمان بانتظام',
      'نحتفظ بنسخ احتياطية آمنة من البيانات'
    ]
  },
  {
    id: 5,
    title: 'ملفات تعريف الارتباط (Cookies)',
    icon: '🍪',
    content: [
      'نستخدم الكوكيز لتحسين تجربة التصفح',
      'كوكيز ضرورية لعمل الموقع الأساسي',
      'كوكيز تحليلية لفهم سلوك المستخدمين',
      'كوكيز تفضيلات لحفظ إعداداتك',
      'يمكنك التحكم في الكوكيز من إعدادات المتصفح'
    ]
  },
  {
    id: 6,
    title: 'حقوقك',
    icon: '⚖️',
    content: [
      'الحق في الوصول لبياناتك الشخصية',
      'الحق في تصحيح البيانات غير الصحيحة',
      'الحق في حذف بياناتك (الحق في النسيان)',
      'الحق في تقييد معالجة بياناتك',
      'الحق في نقل بياناتك',
      'الحق في الاعتراض على معالجة بياناتك'
    ]
  },
  {
    id: 7,
    title: 'الاحتفاظ بالبيانات',
    icon: '📅',
    content: [
      'نحتفظ بالبيانات طالما كان حسابك نشطاً',
      'بعد حذف الحساب، نحذف البيانات خلال 30 يوماً',
      'قد نحتفظ ببعض البيانات لأغراض قانونية',
      'بيانات الإعلانات المحذوفة تُحذف خلال 7 أيام',
      'سجلات الأمان تُحفظ لمدة سنة واحدة'
    ]
  },
  {
    id: 8,
    title: 'خصوصية الأطفال',
    icon: '👶',
    content: [
      'خدماتنا مخصصة للأشخاص 18 سنة فما فوق',
      'لا نجمع معلومات من الأطفال تحت 18 سنة عمداً',
      'إذا علمنا بوجود بيانات طفل، سنحذفها فوراً',
      'ننصح الآباء بمراقبة استخدام أطفالهم للإنترنت'
    ]
  }
];

const dataTypes = [
  {
    type: 'بيانات الهوية',
    examples: ['الاسم الكامل', 'تاريخ الميلاد', 'الجنس'],
    usage: 'لإنشاء وإدارة حسابك',
    retention: 'طوال فترة الحساب'
  },
  {
    type: 'بيانات الاتصال',
    examples: ['البريد الإلكتروني', 'رقم الهاتف', 'العنوان'],
    usage: 'للتواصل وتأكيد الهوية',
    retention: 'طوال فترة الحساب'
  },
  {
    type: 'بيانات الاستخدام',
    examples: ['سجل التصفح', 'الإعلانات المشاهدة', 'وقت الاستخدام'],
    usage: 'لتحسين الخدمة والتحليل',
    retention: '12 شهر'
  },
  {
    type: 'بيانات تقنية',
    examples: ['عنوان IP', 'نوع المتصفح', 'نظام التشغيل'],
    usage: 'للأمان وحل المشاكل التقنية',
    retention: '6 أشهر'
  }
];

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* العنوان الرئيسي */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">سياسة الخصوصية</h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            نحن ملتزمون بحماية خصوصيتك وبياناتك الشخصية. هذه السياسة توضح كيف نجمع ونستخدم ونحمي معلوماتك
          </p>
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg max-w-2xl mx-auto">
            <p className="text-blue-800 text-sm">
              <strong>آخر تحديث:</strong> 1 يناير 2024 | <strong>نافذة منذ:</strong> 1 يناير 2020
            </p>
          </div>
        </div>

        {/* ملخص سريع */}
        <div className="max-w-4xl mx-auto mb-12">
          <div className="bg-gradient-to-r from-primary-600 to-primary-800 text-white rounded-xl p-8">
            <h2 className="text-2xl font-bold mb-4">ملخص سريع</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-3xl mb-2">🛡️</div>
                <h3 className="font-semibold mb-1">نحمي بياناتك</h3>
                <p className="text-primary-100 text-sm">نستخدم أحدث تقنيات الأمان</p>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-2">🚫</div>
                <h3 className="font-semibold mb-1">لا نبيع معلوماتك</h3>
                <p className="text-primary-100 text-sm">خصوصيتك ليست للبيع</p>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-2">⚖️</div>
                <h3 className="font-semibold mb-1">لك حقوق كاملة</h3>
                <p className="text-primary-100 text-sm">تحكم في بياناتك بالكامل</p>
              </div>
            </div>
          </div>
        </div>

        {/* جدول أنواع البيانات */}
        <div className="max-w-6xl mx-auto mb-12">
          <h2 className="text-3xl font-bold text-gray-800 text-center mb-8">أنواع البيانات التي نجمعها</h2>
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-right text-sm font-semibold text-gray-700">نوع البيانات</th>
                    <th className="px-6 py-4 text-right text-sm font-semibold text-gray-700">أمثلة</th>
                    <th className="px-6 py-4 text-right text-sm font-semibold text-gray-700">الغرض من الاستخدام</th>
                    <th className="px-6 py-4 text-right text-sm font-semibold text-gray-700">مدة الاحتفاظ</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {dataTypes.map((data, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 font-medium text-gray-800">{data.type}</td>
                      <td className="px-6 py-4 text-gray-600 text-sm">
                        {data.examples.join('، ')}
                      </td>
                      <td className="px-6 py-4 text-gray-600 text-sm">{data.usage}</td>
                      <td className="px-6 py-4 text-gray-600 text-sm">{data.retention}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* التفاصيل الكاملة */}
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-gray-800 text-center mb-8">التفاصيل الكاملة</h2>
          <div className="space-y-6">
            {privacyData.map((section) => (
              <div key={section.id} className="bg-white rounded-xl shadow-lg p-8">
                <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center gap-3">
                  <span className="text-3xl">{section.icon}</span>
                  {section.title}
                </h3>
                <ul className="space-y-3">
                  {section.content.map((item, index) => (
                    <li key={index} className="flex items-start gap-3">
                      <span className="text-primary-600 mt-2 text-sm">●</span>
                      <span className="text-gray-700 leading-relaxed">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          {/* إدارة البيانات */}
          <div className="bg-green-50 border border-green-200 rounded-xl p-8 mt-8">
            <h2 className="text-2xl font-bold text-green-800 mb-4 flex items-center gap-3">
              <span className="text-3xl">⚙️</span>
              إدارة بياناتك
            </h2>
            <p className="text-green-700 mb-4">
              يمكنك إدارة بياناتك الشخصية من خلال:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <p className="text-green-700">• إعدادات الحساب في لوحة التحكم</p>
                <p className="text-green-700">• طلب نسخة من بياناتك</p>
                <p className="text-green-700">• تعديل أو حذف المعلومات</p>
              </div>
              <div className="space-y-2">
                <p className="text-green-700">• إلغاء الاشتراك في الإشعارات</p>
                <p className="text-green-700">• حذف الحساب نهائياً</p>
                <p className="text-green-700">• التواصل مع فريق الدعم</p>
              </div>
            </div>
          </div>

          {/* معلومات الاتصال */}
          <div className="bg-primary-50 border border-primary-200 rounded-xl p-8 mt-8">
            <h2 className="text-2xl font-bold text-primary-800 mb-4">مسؤول حماية البيانات</h2>
            <p className="text-primary-700 mb-4">
              للاستفسارات حول الخصوصية أو لممارسة حقوقك:
            </p>
            <div className="space-y-2 text-primary-700">
              <p>📧 البريد الإلكتروني: <EMAIL></p>
              <p>📞 الهاتف: +963-11-123-4567 (تحويلة 3)</p>
              <p>📍 العنوان: دمشق - المزة - شارع الثورة - مبنى التجارة</p>
              <p>⏰ ساعات العمل: الأحد - الخميس، 9:00 ص - 6:00 م</p>
            </div>
          </div>

          {/* أزرار التنقل */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mt-12">
            <a
              href="/terms"
              className="bg-primary-600 text-white px-8 py-3 rounded-lg hover:bg-primary-700 transition-colors font-semibold text-center"
            >
              الشروط والأحكام
            </a>
            <a
              href="/contact"
              className="border border-primary-600 text-primary-600 px-8 py-3 rounded-lg hover:bg-primary-50 transition-colors font-semibold text-center"
            >
              تواصل معنا
            </a>
            <a
              href="/dashboard/settings"
              className="border border-gray-300 text-gray-700 px-8 py-3 rounded-lg hover:bg-gray-50 transition-colors font-semibold text-center"
            >
              إعدادات الحساب
            </a>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
