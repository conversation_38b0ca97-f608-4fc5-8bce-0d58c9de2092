'use client';

import { useState, useEffect } from 'react';

interface ScrollIndicatorsProps {
  className?: string;
}

export default function ScrollIndicators({ className = '' }: ScrollIndicatorsProps) {
  // حالات التمرير الذكي
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const [isMobileDevice, setIsMobileDevice] = useState(false);
  const [isTabletDevice, setIsTabletDevice] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);
  const [scrollDirection, setScrollDirection] = useState<'up' | 'down' | null>(null);
  const [isAtTop, setIsAtTop] = useState(true);
  const [isAtBottom, setIsAtBottom] = useState(false);
  const [showScrollIndicator, setShowScrollIndicator] = useState(false);
  const [scrollVelocity, setScrollVelocity] = useState(0);
  const [isClient, setIsClient] = useState(false);

  // إصلاح مشكلة Hydration
  useEffect(() => {
    setIsClient(true);
    // تحديد نوع الجهاز
    if (typeof window !== 'undefined') {
      const updateDeviceType = () => {
        const width = window.innerWidth;
        setIsMobileDevice(width < 768);
        setIsTabletDevice(width >= 768 && width < 1024);
      };

      updateDeviceType();

      // مراقبة تغيير حجم الشاشة
      const handleResize = () => {
        updateDeviceType();
      };

      window.addEventListener('resize', handleResize);
      return () => window.removeEventListener('resize', handleResize);
    }
  }, []);

  // مراقبة التمرير لإظهار/إخفاء أزرار التنقل
  useEffect(() => {
    if (typeof window === 'undefined' || !isClient) return;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      const windowHeight = window.innerHeight;
      const documentHeight = document.documentElement.scrollHeight;

      // تحديد اتجاه التمرير وحساب السرعة
      const direction = currentScrollY > lastScrollY ? 'down' : currentScrollY < lastScrollY ? 'up' : null;
      const velocity = Math.abs(currentScrollY - lastScrollY);

      setScrollDirection(direction);
      setScrollVelocity(velocity);

      // تحديث موضع التمرير الأخير
      setLastScrollY(currentScrollY);

      // حساب نسبة التقدم في التمرير
      const maxScroll = documentHeight - windowHeight;
      const progress = maxScroll > 0 ? (currentScrollY / maxScroll) * 100 : 0;
      setScrollProgress(Math.min(100, Math.max(0, progress)));

      // تحديد ما إذا كنا في أعلى أو أسفل الصفحة
      const atTop = currentScrollY <= 50;
      const atBottom = currentScrollY + windowHeight >= documentHeight - 50;

      setIsAtTop(atTop);
      setIsAtBottom(atBottom);

      // إظهار مؤشر التمرير عند الوصول للحدود
      if ((atTop && direction === 'up') || (atBottom && direction === 'down')) {
        setShowScrollIndicator(true);

        // إضافة اهتزاز للموبايل عند الوصول للحدود
        if (navigator.vibrate && window.innerWidth < 768) {
          navigator.vibrate([50, 25, 50]); // نمط اهتزاز قصير ومصغر
        }

        setTimeout(() => setShowScrollIndicator(false), 2000);
      }

      // إظهار زر العودة للأعلى إذا تم التمرير أكثر من 200px
      setShowScrollToTop(currentScrollY > 200);

      // إظهار زر التمرير للأسفل إذا لم نصل للنهاية
      const isNearBottom = currentScrollY + windowHeight >= documentHeight - 100;
      setShowScrollToBottom(!isNearBottom && currentScrollY < documentHeight - windowHeight - 150);

      // تحديد حالة التمرير
      setIsScrolling(true);

      // إزالة حالة التمرير بعد توقف المستخدم
      clearTimeout((window as any).scrollTimeout);
      (window as any).scrollTimeout = setTimeout(() => {
        setIsScrolling(false);
        setScrollDirection(null);
      }, 100);
    };

    // إضافة مستمع التمرير مع throttling
    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledHandleScroll, { passive: true });

    // تنظيف المستمع عند إلغاء التحميل
    return () => {
      window.removeEventListener('scroll', throttledHandleScroll);
      clearTimeout((window as any).scrollTimeout);
    };
  }, [isClient, lastScrollY]);

  // دالة للتمرير إلى الأعلى
  const scrollToTop = () => {
    if (typeof window === 'undefined' || !isClient) return;

    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // دالة للتمرير إلى الأسفل
  const scrollToBottom = () => {
    if (typeof window === 'undefined' || !isClient) return;

    window.scrollTo({
      top: document.documentElement.scrollHeight,
      behavior: 'smooth'
    });
  };

  // دالة للتمرير إلى الخطوة التالية
  const scrollToNextSection = () => {
    if (typeof window === 'undefined' || !isClient) return;

    const currentScrollY = window.scrollY;
    const windowHeight = window.innerHeight;
    const nextScrollPosition = currentScrollY + windowHeight * 0.7; // تمرير 70% من ارتفاع الشاشة

    window.scrollTo({
      top: Math.min(nextScrollPosition, document.documentElement.scrollHeight - windowHeight),
      behavior: 'smooth'
    });
  };

  // دالة للتمرير إلى الخطوة السابقة
  const scrollToPrevSection = () => {
    if (typeof window === 'undefined' || !isClient) return;

    const currentScrollY = window.scrollY;
    const windowHeight = window.innerHeight;
    const prevScrollPosition = currentScrollY - windowHeight * 0.7; // تمرير 70% من ارتفاع الشاشة للخلف

    window.scrollTo({
      top: Math.max(prevScrollPosition, 0),
      behavior: 'smooth'
    });
  };

  if (!isClient) return null;

  return (
    <>
      {/* شريط التقدم في التمرير */}
      <div className="fixed top-0 left-0 w-full h-0.5 bg-gray-200/30 z-50">
        <div
          className="h-full bg-gradient-to-r from-primary-500/70 to-primary-600/70 transition-all duration-300 ease-out shadow-sm"
          style={{ width: `${scrollProgress}%` }}
        />
      </div>

      {/* أزرار التنقل الذكية */}
      <>
        {/* زر العودة للأعلى */}
        {showScrollToTop && (
          <button
            onClick={scrollToTop}
            className={`fixed bottom-12 right-3 z-50 bg-gradient-to-r from-primary-600/60 to-primary-700/60 hover:from-primary-700/80 hover:to-primary-800/80 text-white p-2 rounded-full shadow-md transition-all duration-300 transform ${
              isScrolling ? 'scale-105 rotate-1' : 'scale-100'
            } hover:scale-105 hover:-translate-y-0.5 focus:outline-none focus:ring-1 focus:ring-primary-300 group backdrop-blur-sm`}
            style={{
              opacity: showScrollToTop ? 0.7 : 0,
              visibility: showScrollToTop ? 'visible' : 'hidden'
            }}
            aria-label="العودة للأعلى"
          >
            <svg className="w-3.5 h-3.5 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 10l7-7m0 0l7 7m-7-7v18" />
            </svg>
            <div className="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></div>
          </button>
        )}

        {/* زر التمرير للأسفل */}
        {showScrollToBottom && (
          <button
            onClick={scrollToBottom}
            className={`fixed bottom-20 right-3 z-50 bg-gradient-to-r from-gray-600/60 to-gray-700/60 hover:from-gray-700/80 hover:to-gray-800/80 text-white p-2 rounded-full shadow-md transition-all duration-300 transform ${
              isScrolling ? 'scale-105 -rotate-1' : 'scale-100'
            } hover:scale-105 hover:translate-y-0.5 focus:outline-none focus:ring-1 focus:ring-gray-300 group backdrop-blur-sm`}
            style={{
              opacity: showScrollToBottom ? 0.7 : 0,
              visibility: showScrollToBottom ? 'visible' : 'hidden'
            }}
            aria-label="التمرير للأسفل"
          >
            <svg className="w-3.5 h-3.5 group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
            <div className="absolute -top-0.5 -right-0.5 w-1.5 h-1.5 bg-blue-400 rounded-full animate-pulse"></div>
          </button>
        )}

        {/* أزرار التنقل السريع للموبايل والتابلت */}
        {(isMobileDevice || isTabletDevice) && (showScrollToTop || showScrollToBottom) && (
          <div className="fixed bottom-3 left-3 z-50 flex flex-col gap-1.5" style={{ opacity: 0.6 }}>
            {/* زر التمرير للأعلى قليلاً */}
            <button
              onClick={scrollToPrevSection}
              className="bg-gradient-to-r from-blue-500/70 to-blue-600/70 hover:from-blue-600/90 hover:to-blue-700/90 text-white p-1.5 rounded-full shadow-sm transition-all duration-300 hover:scale-105 hover:-translate-y-0.5 focus:outline-none focus:ring-1 focus:ring-blue-300 group backdrop-blur-sm"
              aria-label="التمرير للأعلى قليلاً"
            >
              <svg className="w-2.5 h-2.5 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M7 11l5-5m0 0l5 5m-5-5v12" />
              </svg>
            </button>

            {/* زر التمرير للأسفل قليلاً */}
            <button
              onClick={scrollToNextSection}
              className="bg-gradient-to-r from-green-500/70 to-green-600/70 hover:from-green-600/90 hover:to-green-700/90 text-white p-1.5 rounded-full shadow-sm transition-all duration-300 hover:scale-105 hover:translate-y-0.5 focus:outline-none focus:ring-1 focus:ring-green-300 group backdrop-blur-sm"
              aria-label="التمرير للأسفل قليلاً"
            >
              <svg className="w-2.5 h-2.5 group-hover:animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M17 13l-5 5m0 0l-5-5m5 5V6" />
              </svg>
            </button>
          </div>
        )}
      </>

      {/* مؤشرات التمرير عند الوصول للحدود */}
      {showScrollIndicator && (
        <>
          {/* مؤشر الوصول لأعلى الصفحة */}
          {isAtTop && (
            <div className="fixed top-12 left-1/2 transform -translate-x-1/2 z-50">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-3 py-1.5 rounded-full shadow-md animate-bounce text-xs">
                <div className="flex items-center gap-1.5">
                  <svg className="w-2.5 h-2.5 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 15l7-7 7 7" />
                  </svg>
                  <span className="font-medium">أعلى الصفحة</span>
                  <svg className="w-2.5 h-2.5 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 15l7-7 7 7" />
                  </svg>
                </div>
              </div>
            </div>
          )}

          {/* مؤشر الوصول لأسفل الصفحة */}
          {isAtBottom && (
            <div className="fixed bottom-12 left-1/2 transform -translate-x-1/2 z-50">
              <div className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-3 py-1.5 rounded-full shadow-md animate-bounce text-xs">
                <div className="flex items-center gap-1.5">
                  <svg className="w-2.5 h-2.5 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M19 9l-7 7-7-7" />
                  </svg>
                  <span className="font-medium">آخر الصفحة</span>
                  <svg className="w-2.5 h-2.5 animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {/* مؤشر اتجاه التمرير الجانبي المصغر */}
      {scrollDirection && isScrolling && !isMobileDevice && (
        <div className={`fixed right-1.5 top-1/2 transform -translate-y-1/2 z-40 transition-all duration-300 ${
          scrollDirection === 'down' ? 'animate-pulse' : 'animate-bounce'
        }`} style={{ opacity: 0.5 }}>
          <div className={`w-5 h-5 rounded-full flex items-center justify-center shadow-sm backdrop-blur-sm ${
            scrollDirection === 'down'
              ? 'bg-gradient-to-b from-orange-400/70 to-red-500/70 text-white'
              : 'bg-gradient-to-t from-blue-400/70 to-purple-500/70 text-white'
          }`}>
            <svg className="w-2.5 h-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {scrollDirection === 'down' ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 10l7-7m0 0l7 7m-7-7v18" />
              )}
            </svg>
          </div>
        </div>
      )}
    </>
  );
}
