'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import FilterModal from '@/components/FilterModal';
import CategoryIcon from '@/components/CategoryIcon';
import { Ad, DataService } from '@/lib/data';

export default function RealEstatePage() {
  const [ads, setAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);

  // فلاتر البحث
  const [filters, setFilters] = useState({
    listingType: '',
    propertyType: '',
    priceFrom: '',
    priceTo: '',
    areaFrom: '',
    areaTo: '',
    rooms: '',
    bathrooms: '',
    floor: '',
    condition: '',
    location: '',
    features: [] as string[]
  });

  // قوائم الخيارات
  const listingTypes = [
    { value: 'sale', label: 'للبيع' },
    { value: 'rent', label: 'للإيجار' },
    { value: 'exchange', label: 'للمقايضة' }
  ];

  const propertyTypes = [
    { value: 'apartment', label: 'شقة' },
    { value: 'house', label: 'منزل' },
    { value: 'office', label: 'مكتب' },
    { value: 'shop', label: 'محل تجاري' },
    { value: 'commercial-complex', label: 'مجمع تجاري' },
    { value: 'warehouse', label: 'مستودع' },
    { value: 'factory', label: 'مصنع' },
    { value: 'laboratory', label: 'معمل' },
    { value: 'industrial-facility', label: 'منشأة صناعية' },
    { value: 'building', label: 'بناء كامل' },
    { value: 'farm', label: 'مزرعة' },
    { value: 'land', label: 'أرض' }
  ];

  const roomOptions = [
    { value: '1', label: '1 غرفة' },
    { value: '1+1', label: '1+1 غرفة (صالون)' },
    { value: '2', label: '2 غرفة' },
    { value: '2+1', label: '2+1 غرفة (صالون)' },
    { value: '3', label: '3 غرف' },
    { value: '3+1', label: '3+1 غرف (صالون)' },
    { value: '4', label: '4 غرف' },
    { value: '4+1', label: '4+1 غرف (صالون)' },
    { value: '5', label: '5 غرف' },
    { value: '5+1', label: '5+1 غرف (صالون)' },
    { value: '6', label: '6 غرف' },
    { value: '6+1', label: '6+1 غرف (صالون)' },
    { value: '7', label: '7 غرف' },
    { value: '7+1', label: '7+1 غرف (صالون)' },
    { value: '8', label: '8 غرف' },
    { value: '8+1', label: '8+1 غرف (صالون)' },
    { value: '9+', label: 'أكثر من 8 غرف' }
  ];

  const bathroomOptions = [
    { value: '1', label: '1 حمام' },
    { value: '2', label: '2 حمام' },
    { value: '3', label: '3 حمامات' },
    { value: '4', label: '4 حمامات' },
    { value: '5', label: '5 حمامات أو أكثر' }
  ];

  const floorOptions = [
    { value: 'basement', label: 'قبو' },
    { value: 'ground', label: 'الأرضي' },
    { value: 'first', label: 'الأول' },
    { value: 'second', label: 'الثاني' },
    { value: 'third', label: 'الثالث' },
    { value: 'fourth', label: 'الرابع' },
    { value: 'fifth', label: 'الخامس' },
    { value: 'sixth', label: 'السادس' },
    { value: 'seventh', label: 'السابع' },
    { value: 'eighth', label: 'الثامن' },
    { value: 'roof', label: 'السطح' },
    { value: 'high', label: 'أكثر من 8 طوابق' }
  ];

  const conditionOptions = [
    { value: 'new', label: 'جديد' },
    { value: 'renovated', label: 'معفش' },
    { value: 'deluxe', label: 'كسوة ديلوكس' },
    { value: 'regular', label: 'كسوة عادية' },
    { value: 'old-finishing', label: 'كسوة قديمة' },
    { value: 'unfinished', label: 'غير مكسي' },
    { value: 'furnished', label: 'مفروش' },
    { value: 'unfurnished', label: 'غير مفروش' },
    { value: 'damaged', label: 'مدمر' }
  ];

  const locations = [
    'دمشق',
    'حلب',
    'حمص',
    'حماة',
    'اللاذقية',
    'طرطوس',
    'درعا',
    'السويداء',
    'القنيطرة',
    'دير الزور',
    'الرقة',
    'الحسكة',
    'إدلب',
    'ريف دمشق'
  ];

  const commonFeatures = [
    { id: 'greenDeed', label: 'سند أخضر' },
    { id: 'agriculturalDeed', label: 'سند زراعي' },
    { id: 'withinPlan', label: 'ضمن المخطط التنظيمي' },
    { id: 'primeLocation', label: 'موقع مميز' },
    { id: 'generator', label: 'مولد كهرباء' },
    { id: 'solar', label: 'طاقة شمسية' },
    { id: 'internet', label: 'إنترنت' },
    { id: 'satellite', label: 'ستلايت' },
    { id: 'elevator', label: 'مصعد' },
    { id: 'parking', label: 'موقف سيارة' },
    { id: 'balcony', label: 'شرفة' },
    { id: 'garden', label: 'حديقة' },
    { id: 'pool', label: 'مسبح' },
    { id: 'security', label: 'حراسة' },
    { id: 'airConditioning', label: 'تكييف' },
    { id: 'heating', label: 'تدفئة' }
  ];

  useEffect(() => {
    loadAds();
  }, [sortBy, currentPage, filters]);

  const loadAds = async () => {
    setLoading(true);

    // محاكاة جلب البيانات
    await new Promise(resolve => setTimeout(resolve, 1000));

    const result = DataService.getAds({
      category: 'real-estate',
      page: currentPage,
      limit: 12,
      sortBy: sortBy as any
    });

    setAds(result.ads);
    setTotalPages(result.totalPages);
    setLoading(false);
  };

  const handleSearch = async (newFilters?: any) => {
    if (newFilters) {
      setFilters(newFilters);
    }
    setCurrentPage(1);
    setLoading(true);

    // محاكاة جلب البيانات مع الفلاتر
    await new Promise(resolve => setTimeout(resolve, 1000));

    // تطبيق الفلاتر على البيانات
    const searchParams = {
      category: 'real-estate',
      listingType: filters.listingType,
      propertyType: filters.propertyType,
      priceFrom: filters.priceFrom,
      priceTo: filters.priceTo,
      areaFrom: filters.areaFrom,
      areaTo: filters.areaTo,
      rooms: filters.rooms,
      bathrooms: filters.bathrooms,
      floor: filters.floor,
      condition: filters.condition,
      location: filters.location,
      features: filters.features,
      sortBy: sortBy
    };

    const result = DataService.getAds({
      category: 'real-estate',
      page: currentPage,
      limit: 12,
      sortBy: sortBy as any
    });

    setAds(result.ads);
    setTotalPages(result.totalPages);
    setLoading(false);
  };

  const handleFilterChange = (key: string, value: string | string[]) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      listingType: '',
      propertyType: '',
      priceFrom: '',
      priceTo: '',
      areaFrom: '',
      areaTo: '',
      rooms: '',
      bathrooms: '',
      floor: '',
      condition: '',
      location: '',
      features: []
    });
  };

  const stats = {
    totalAds: 1247,
    avgPrice: '45,000,000',
    newToday: 23,
    featuredAds: 156
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* العنوان والإحصائيات */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 flex items-center justify-center">
              <CategoryIcon
                category="real-estate"
                className="w-10 h-10"
                color="#3b82f6"
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">العقارات</h1>
              <p className="text-gray-600">اكتشف أفضل العقارات للبيع والإيجار في سوريا</p>
            </div>
          </div>


        </div>



        {/* زر فتح الفلاتر على الموبايل */}
        <div className="lg:hidden mb-6">
          <button
            onClick={() => setIsFilterModalOpen(true)}
            className="w-full bg-white rounded-xl shadow-lg p-4 flex items-center justify-center gap-3 border border-gray-200 hover:bg-gray-50 transition-colors"
          >
            <img
              src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
              alt="فلاتر"
              className="w-6 h-6 opacity-80"
              style={{
                filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.6))'
              }}
            />
            <span className="text-lg font-semibold text-gray-800">البحث والفلاتر</span>
          </button>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* فلاتر العقارات */}
          <div className="lg:w-1/4 hidden lg:block">
            <div className="bg-white rounded-xl shadow-lg p-6 sticky top-8 border border-gray-200">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                <img
                  src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="فلاتر"
                  className="w-6 h-6 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.6))'
                  }}
                />
                البحث والفلاتر
              </h2>

              {/* Search */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                <div className="relative">
                  <input
                    type="text"
                    placeholder="ابحث عن عقار..."
                    className="w-full px-3 py-2 pr-10 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                  />
                  <img
                    src="/images/jobs/Jobs -Personals/icons/search_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                    alt="بحث"
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 opacity-60"
                  />
                </div>
                <button
                  onClick={() => handleSearch(filters)}
                  className="mt-2 w-full py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 font-medium flex items-center justify-center gap-2"
                  type="button"
                >
                  <img
                    src="/images/jobs/Jobs -Personals/icons/search_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
                    alt="تطبيق"
                    className="w-5 h-5 opacity-80"
                    style={{
                      filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.6))'
                    }}
                  />
                  تطبيق البحث
                </button>
              </div>

              {/* نوع الإعلان */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع الإعلان</label>
                <select
                  value={filters.listingType}
                  onChange={(e) => handleFilterChange('listingType', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  <option value="">جميع الأنواع</option>
                  {listingTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>

              {/* نوع العقار */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع العقار</label>
                <select
                  value={filters.propertyType}
                  onChange={(e) => handleFilterChange('propertyType', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  <option value="">جميع الأنواع</option>
                  {propertyTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>

              {/* نطاق السعر */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر</label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    value={filters.priceFrom}
                    onChange={(e) => handleFilterChange('priceFrom', e.target.value)}
                    placeholder="السعر من"
                    className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                  />
                  <input
                    type="number"
                    value={filters.priceTo}
                    onChange={(e) => handleFilterChange('priceTo', e.target.value)}
                    placeholder="السعر إلى"
                    className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                  />
                </div>
              </div>

              {/* نطاق المساحة */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">المساحة (م²)</label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    value={filters.areaFrom}
                    onChange={(e) => handleFilterChange('areaFrom', e.target.value)}
                    placeholder="من م²"
                    className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                  />
                  <input
                    type="number"
                    value={filters.areaTo}
                    onChange={(e) => handleFilterChange('areaTo', e.target.value)}
                    placeholder="إلى م²"
                    className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                  />
                </div>
              </div>

              {/* عدد الغرف */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">عدد الغرف</label>
                <select
                  value={filters.rooms}
                  onChange={(e) => handleFilterChange('rooms', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  <option value="">جميع الأعداد</option>
                  {roomOptions.map(room => (
                    <option key={room.value} value={room.value}>{room.label}</option>
                  ))}
                </select>
              </div>

              {/* عدد الحمامات */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">عدد الحمامات</label>
                <select
                  value={filters.bathrooms}
                  onChange={(e) => handleFilterChange('bathrooms', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  <option value="">جميع الأعداد</option>
                  {bathroomOptions.map(bathroom => (
                    <option key={bathroom.value} value={bathroom.value}>{bathroom.label}</option>
                  ))}
                </select>
              </div>

              {/* الطابق */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الطابق</label>
                <select
                  value={filters.floor}
                  onChange={(e) => handleFilterChange('floor', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  <option value="">جميع الطوابق</option>
                  {floorOptions.map(floor => (
                    <option key={floor.value} value={floor.value}>{floor.label}</option>
                  ))}
                </select>
              </div>

              {/* حالة العقار */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">حالة العقار</label>
                <select
                  value={filters.condition}
                  onChange={(e) => handleFilterChange('condition', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  <option value="">جميع الحالات</option>
                  {conditionOptions.map(condition => (
                    <option key={condition.value} value={condition.value}>{condition.label}</option>
                  ))}
                </select>
              </div>

              {/* الموقع */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                <select
                  value={filters.location}
                  onChange={(e) => handleFilterChange('location', e.target.value)}
                  className="w-full px-3 py-2 bg-white/50 backdrop-blur-sm border border-white/40 rounded-lg focus:ring-2 focus:ring-primary-500 focus:bg-white/70"
                >
                  <option value="">جميع المحافظات</option>
                  {locations.map(location => (
                    <option key={location} value={location}>{location}</option>
                  ))}
                </select>
              </div>

              {/* زر مسح الفلاتر */}
              <button
                onClick={clearFilters}
                className="w-full py-2 bg-gray-200/80 backdrop-blur-sm text-gray-700 rounded-lg hover:bg-gray-300/80 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 border border-gray-300/40"
              >
                مسح جميع الفلاتر
              </button>
            </div>
          </div>

          {/* النتائج */}
          <div className="lg:w-3/4">
            {/* شريط التحكم */}
            <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="text-gray-600">
                  عرض {ads.length} من أصل {stats.totalAds} إعلان
                </div>

                <div className="flex items-center gap-4">
                  {/* طريقة العرض */}
                  <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'grid'
                          ? 'bg-primary-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      🔲 شبكة
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'list'
                          ? 'bg-primary-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      📋 قائمة
                    </button>
                  </div>

                  {/* الترتيب */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="oldest">الأقدم</option>
                    <option value="price_low">السعر: من الأقل للأعلى</option>
                    <option value="price_high">السعر: من الأعلى للأقل</option>
                    <option value="most_viewed">الأكثر مشاهدة</option>
                  </select>
                </div>
              </div>
            </div>

            {/* الإعلانات */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                    <div className="h-48 bg-gray-200"></div>
                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : ads.length > 0 ? (
              <>
                <div className={
                  viewMode === 'grid'
                    ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                    : 'space-y-4'
                }>
                  {ads.map((ad) => (
                    <AdCard
                      key={ad.id}
                      ad={ad}
                      viewMode={viewMode}
                    />
                  ))}
                </div>

                {/* الصفحات */}
                {totalPages > 1 && (
                  <div className="flex justify-center mt-8">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                        className="px-3 py-2 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        السابق
                      </button>

                      {[...Array(totalPages)].map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentPage(index + 1)}
                          className={`px-3 py-2 border rounded-lg text-sm ${
                            currentPage === index + 1
                              ? 'bg-primary-600 text-white border-primary-600'
                              : 'border-gray-200 hover:bg-gray-50'
                          }`}
                        >
                          {index + 1}
                        </button>
                      ))}

                      <button
                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                        disabled={currentPage === totalPages}
                        className="px-3 py-2 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        التالي
                      </button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                  <CategoryIcon
                    category="real-estate"
                    className="w-14 h-14"
                    color="#3b82f6"
                  />
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد عقارات</h3>
                <p className="text-gray-600 mb-6">لم نجد أي عقارات تطابق معايير البحث</p>
                <button
                  onClick={() => window.location.reload()}
                  className="bg-primary-600 text-white px-6 py-3 rounded-lg hover:bg-primary-700 transition-colors"
                >
                  إعادة تحميل
                </button>
              </div>
            )}
          </div>
        </div>

        {/* نصائح للمشترين */}
        <div className="mt-12 bg-gradient-to-r from-blue-100 to-blue-200 rounded-lg text-blue-800 p-6">
          <h2 className="text-lg font-bold mb-4 text-center">نصائح لشراء العقار المناسب</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl mb-2">🔍</div>
              <h3 className="font-semibold mb-1 text-sm">ابحث بعناية</h3>
              <p className="text-blue-600 text-xs">قارن الأسعار والمواقع قبل اتخاذ القرار</p>
            </div>
            <div className="text-center">
              <div className="text-2xl mb-2">📋</div>
              <h3 className="font-semibold mb-1 text-sm">تحقق من الأوراق</h3>
              <p className="text-blue-600 text-xs">تأكد من سلامة جميع الوثائق القانونية</p>
            </div>
            <div className="text-center">
              <div className="w-6 h-6 mx-auto mb-2 flex items-center justify-center">
                <CategoryIcon
                  category="real-estate"
                  className="w-5 h-5"
                  color="#1e40af"
                />
              </div>
              <h3 className="font-semibold mb-1 text-sm">عاين شخصياً</h3>
              <p className="text-blue-600 text-xs">لا تشتري عقار دون معاينة شخصية</p>
            </div>
          </div>
        </div>
      </main>

      <Footer />

      {/* Modal للفلاتر على الموبايل */}
      <FilterModal
        isOpen={isFilterModalOpen}
        onClose={() => setIsFilterModalOpen(false)}
        filters={filters}
        onFilterChange={handleFilterChange}
        onClearFilters={clearFilters}
        category="real-estate"
        listingTypes={listingTypes}
        propertyTypes={propertyTypes}
        roomOptions={roomOptions}
        bathroomOptions={bathroomOptions}
        floorOptions={floorOptions}
        conditionOptions={conditionOptions}
        features={commonFeatures}
        locations={locations}
      />
    </div>
  );
}
