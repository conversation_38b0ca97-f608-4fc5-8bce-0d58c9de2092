'use client';

import { useState, useEffect } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';

interface User {
  id: number;
  name: string;
  email: string;
  phone: string;
  type: 'individual' | 'business' | 'real-estate-office';
  verified: boolean;
  subscription: {
    plan: string;
    expiresAt: string;
    adsLimit: number;
    adsUsed: number;
  };
  location: {
    governorate: string;
    city: string;
  };
  rating: number;
  totalRatings: number;
  createdAt: string;
  lastActive: string;
  totalAds: number;
  activeAds: number;
}

export default function UsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      // محاكاة تحميل المستخدمين
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockUsers: User[] = [
        {
          id: 1,
          name: 'أحمد محمد',
          email: '<EMAIL>',
          phone: '+963944123456',
          type: 'individual',
          verified: true,
          subscription: {
            plan: 'premium',
            expiresAt: '2024-12-31T23:59:59Z',
            adsLimit: 15,
            adsUsed: 8
          },
          location: {
            governorate: 'damascus',
            city: 'damascus'
          },
          rating: 4.8,
          totalRatings: 23,
          createdAt: '2023-06-15T10:30:00Z',
          lastActive: '2024-01-20T14:22:00Z',
          totalAds: 12,
          activeAds: 8
        },
        {
          id: 2,
          name: 'شركة العقارات الذهبية',
          email: '<EMAIL>',
          phone: '+963933456789',
          type: 'business',
          verified: true,
          subscription: {
            plan: 'business',
            expiresAt: '2024-06-30T23:59:59Z',
            adsLimit: 50,
            adsUsed: 32
          },
          location: {
            governorate: 'aleppo',
            city: 'aleppo'
          },
          rating: 4.5,
          totalRatings: 67,
          createdAt: '2023-03-10T09:15:00Z',
          lastActive: '2024-01-20T16:45:00Z',
          totalAds: 89,
          activeAds: 32
        },
        {
          id: 3,
          name: 'مكتب الأمل العقاري',
          email: '<EMAIL>',
          phone: '+963955789123',
          type: 'real-estate-office',
          verified: true,
          subscription: {
            plan: 'real-estate-office',
            expiresAt: '2024-09-15T23:59:59Z',
            adsLimit: 30,
            adsUsed: 18
          },
          location: {
            governorate: 'homs',
            city: 'homs'
          },
          rating: 4.2,
          totalRatings: 45,
          createdAt: '2023-08-20T11:00:00Z',
          lastActive: '2024-01-19T20:30:00Z',
          totalAds: 56,
          activeAds: 18
        }
      ];

      setUsers(mockUsers);
    } catch (error) {
      console.error('خطأ في تحميل المستخدمين:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.phone.includes(searchTerm);
    
    const matchesType = filterType === 'all' || user.type === filterType;
    
    return matchesSearch && matchesType;
  });

  const getUserTypeLabel = (type: string) => {
    switch (type) {
      case 'individual': return 'فرد';
      case 'business': return 'شركة';
      case 'real-estate-office': return 'مكتب عقاري';
      default: return type;
    }
  };

  const getPlanLabel = (plan: string) => {
    switch (plan) {
      case 'free': return 'مجاني';
      case 'premium': return 'مميز';
      case 'business': return 'تجاري';
      case 'real-estate-office': return 'مكتب عقاري';
      default: return plan;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SY', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleToggleVerification = (userId: number) => {
    setUsers(prev => prev.map(user => 
      user.id === userId ? { ...user, verified: !user.verified } : user
    ));
  };

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* العنوان والفلاتر */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4" style={{ fontFamily: 'Cairo, sans-serif' }}>
            إدارة المستخدمين
          </h1>
          
          <div className="flex flex-col sm:flex-row gap-4">
            {/* البحث */}
            <div className="flex-1">
              <input
                type="text"
                placeholder="البحث بالاسم، البريد الإلكتروني، أو رقم الهاتف..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
            
            {/* فلتر النوع */}
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="all">جميع الأنواع</option>
              <option value="individual">أفراد</option>
              <option value="business">شركات</option>
              <option value="real-estate-office">مكاتب عقارية</option>
            </select>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">إجمالي المستخدمين</p>
                <p className="text-2xl font-bold text-blue-600">{users.length}</p>
              </div>
              <span className="text-2xl">👥</span>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">مستخدمين موثقين</p>
                <p className="text-2xl font-bold text-green-600">{users.filter(u => u.verified).length}</p>
              </div>
              <span className="text-2xl">✅</span>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">شركات</p>
                <p className="text-2xl font-bold text-purple-600">{users.filter(u => u.type === 'business').length}</p>
              </div>
              <span className="text-2xl">🏢</span>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">مكاتب عقارية</p>
                <p className="text-2xl font-bold text-orange-600">{users.filter(u => u.type === 'real-estate-office').length}</p>
              </div>
              <span className="text-2xl">🏠</span>
            </div>
          </div>
        </div>

        {/* جدول المستخدمين */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المستخدم
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    النوع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الاشتراك
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإعلانات
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    آخر نشاط
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                            <span className="text-primary-600 font-medium text-sm">
                              {user.name.charAt(0)}
                            </span>
                          </div>
                        </div>
                        <div className="mr-4">
                          <div className="flex items-center gap-2">
                            <div className="text-sm font-medium text-gray-900">{user.name}</div>
                            {user.verified && <span className="text-green-500">✓</span>}
                          </div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                          <div className="text-sm text-gray-500">{user.phone}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        user.type === 'individual' ? 'bg-blue-100 text-blue-800' :
                        user.type === 'business' ? 'bg-purple-100 text-purple-800' :
                        'bg-orange-100 text-orange-800'
                      }`}>
                        {getUserTypeLabel(user.type)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>{getPlanLabel(user.subscription.plan)}</div>
                      <div className="text-xs text-gray-500">
                        {user.subscription.adsUsed}/{user.subscription.adsLimit} إعلان
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div>المجموع: {user.totalAds}</div>
                      <div className="text-xs text-gray-500">النشط: {user.activeAds}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(user.lastActive)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex gap-2">
                        <button
                          onClick={() => setSelectedUser(user)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          عرض
                        </button>
                        <button
                          onClick={() => handleToggleVerification(user.id)}
                          className={`${user.verified ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'}`}
                        >
                          {user.verified ? 'إلغاء التوثيق' : 'توثيق'}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* نافذة تفاصيل المستخدم */}
        {selectedUser && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-xl font-bold text-gray-900">تفاصيل المستخدم</h3>
                  <button
                    onClick={() => setSelectedUser(null)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    ✕
                  </button>
                </div>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">الاسم:</h4>
                      <p className="text-gray-700">{selectedUser.name}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">النوع:</h4>
                      <p className="text-gray-700">{getUserTypeLabel(selectedUser.type)}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">البريد الإلكتروني:</h4>
                      <p className="text-gray-700">{selectedUser.email}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">رقم الهاتف:</h4>
                      <p className="text-gray-700">{selectedUser.phone}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">التقييم:</h4>
                      <p className="text-gray-700">{selectedUser.rating} ⭐ ({selectedUser.totalRatings} تقييم)</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-1">الموقع:</h4>
                      <p className="text-gray-700">{selectedUser.location.governorate} - {selectedUser.location.city}</p>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-900 mb-1">تاريخ التسجيل:</h4>
                    <p className="text-gray-700">{formatDate(selectedUser.createdAt)}</p>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 mb-2">إحصائيات الاشتراك:</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">الخطة: </span>
                        <span className="font-medium">{getPlanLabel(selectedUser.subscription.plan)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">انتهاء الاشتراك: </span>
                        <span className="font-medium">{formatDate(selectedUser.subscription.expiresAt)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">الإعلانات المستخدمة: </span>
                        <span className="font-medium">{selectedUser.subscription.adsUsed}/{selectedUser.subscription.adsLimit}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">إجمالي الإعلانات: </span>
                        <span className="font-medium">{selectedUser.totalAds}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex gap-3 mt-6">
                  <button
                    onClick={() => handleToggleVerification(selectedUser.id)}
                    className={`px-4 py-2 rounded-lg transition-colors ${
                      selectedUser.verified
                        ? 'bg-red-600 text-white hover:bg-red-700'
                        : 'bg-green-600 text-white hover:bg-green-700'
                    }`}
                  >
                    {selectedUser.verified ? 'إلغاء التوثيق' : 'توثيق المستخدم'}
                  </button>
                  <button
                    onClick={() => setSelectedUser(null)}
                    className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                  >
                    إغلاق
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}
