'use client';

import { useState, useRef, useEffect } from 'react';

interface Message {
  id: number;
  text: string;
  sender: 'user' | 'support';
  timestamp: string;
  type?: 'text' | 'image' | 'file';
}

const LiveChat = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      text: 'مرحباً! كيف يمكنني مساعدتك اليوم؟',
      sender: 'support',
      timestamp: new Date().toLocaleTimeString('ar-SY', { hour: '2-digit', minute: '2-digit' })
    }
  ]);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isOnline, setIsOnline] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const quickReplies = [
    'مشكلة في تسجيل الدخول',
    'كيف أنشر إعلان؟',
    'مشكلة في الدفع',
    'تغيير كلمة المرور',
    'حذف الحساب'
  ];

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const sendMessage = (text: string) => {
    if (!text.trim()) return;

    const userMessage: Message = {
      id: Date.now(),
      text: text.trim(),
      sender: 'user',
      timestamp: new Date().toLocaleTimeString('ar-SY', { hour: '2-digit', minute: '2-digit' })
    };

    setMessages(prev => [...prev, userMessage]);
    setNewMessage('');
    setIsTyping(true);

    // محاكاة رد الدعم الفني
    setTimeout(() => {
      setIsTyping(false);
      const supportMessage: Message = {
        id: Date.now() + 1,
        text: getAutoReply(text),
        sender: 'support',
        timestamp: new Date().toLocaleTimeString('ar-SY', { hour: '2-digit', minute: '2-digit' })
      };
      setMessages(prev => [...prev, supportMessage]);
    }, 1500);
  };

  const getAutoReply = (userMessage: string): string => {
    const message = userMessage.toLowerCase();
    
    if (message.includes('تسجيل') || message.includes('دخول')) {
      return 'يمكنك تسجيل الدخول من خلال النقر على "تسجيل الدخول" في أعلى الصفحة. إذا نسيت كلمة المرور، يمكنك إعادة تعيينها.';
    } else if (message.includes('إعلان') || message.includes('نشر')) {
      return 'لنشر إعلان جديد، انقر على "أضف إعلانك" واتبع الخطوات. يمكنك نشر 3 إعلانات مجانية شهرياً.';
    } else if (message.includes('دفع') || message.includes('فوترة')) {
      return 'نقبل الدفع عبر البطاقات البنكية والتحويل البنكي. إذا واجهت مشكلة في الدفع، يرجى التواصل مع فريق المحاسبة.';
    } else if (message.includes('كلمة المرور') || message.includes('باسورد')) {
      return 'يمكنك تغيير كلمة المرور من لوحة التحكم > إعدادات الحساب > تغيير كلمة المرور.';
    } else {
      return 'شكراً لتواصلك معنا. سيقوم أحد أعضاء فريق الدعم بالرد عليك قريباً. هل يمكنك توضيح مشكلتك أكثر؟';
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage(newMessage);
    }
  };

  return (
    <>
      {/* زر فتح الدردشة */}
      {!isOpen && (
        <button
          onClick={() => setIsOpen(true)}
          className="fixed bottom-6 left-6 w-16 h-16 bg-primary-600 text-white rounded-full shadow-lg hover:bg-primary-700 transition-all duration-300 z-50 flex items-center justify-center"
        >
          <span className="text-2xl">💬</span>
          {isOnline && (
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
          )}
        </button>
      )}

      {/* نافذة الدردشة */}
      {isOpen && (
        <div className="fixed bottom-6 left-6 w-80 h-96 bg-white rounded-xl shadow-2xl z-50 flex flex-col overflow-hidden border border-gray-200">
          {/* رأس الدردشة */}
          <div className="bg-primary-600 text-white p-4 flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                <span className="text-sm">🎧</span>
              </div>
              <div>
                <h3 className="font-semibold">الدعم الفني</h3>
                <div className="flex items-center gap-2 text-xs">
                  <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-400' : 'bg-gray-400'}`}></div>
                  <span>{isOnline ? 'متصل الآن' : 'غير متصل'}</span>
                </div>
              </div>
            </div>
            <button
              onClick={() => setIsOpen(false)}
              className="text-white/80 hover:text-white text-xl"
            >
              ×
            </button>
          </div>

          {/* منطقة الرسائل */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs px-4 py-2 rounded-lg ${
                    message.sender === 'user'
                      ? 'bg-primary-600 text-white'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  <p className="text-sm">{message.text}</p>
                  <span className={`text-xs ${
                    message.sender === 'user' ? 'text-primary-200' : 'text-gray-500'
                  } block mt-1`}>
                    {message.timestamp}
                  </span>
                </div>
              </div>
            ))}

            {/* مؤشر الكتابة */}
            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-gray-100 px-4 py-2 rounded-lg">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* الردود السريعة */}
          {messages.length === 1 && (
            <div className="p-3 border-t border-gray-200">
              <p className="text-xs text-gray-600 mb-2">مواضيع شائعة:</p>
              <div className="flex flex-wrap gap-1">
                {quickReplies.slice(0, 3).map((reply, index) => (
                  <button
                    key={index}
                    onClick={() => sendMessage(reply)}
                    className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded-full hover:bg-gray-200 transition-colors"
                  >
                    {reply}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* منطقة الإدخال */}
          <div className="p-4 border-t border-gray-200">
            <div className="flex gap-2">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="اكتب رسالتك..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"
              />
              <button
                onClick={() => sendMessage(newMessage)}
                disabled={!newMessage.trim()}
                className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <span className="text-sm">📤</span>
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-2 text-center">
              عادة ما نرد خلال دقائق قليلة
            </p>
          </div>
        </div>
      )}
    </>
  );
};

export default LiveChat;
