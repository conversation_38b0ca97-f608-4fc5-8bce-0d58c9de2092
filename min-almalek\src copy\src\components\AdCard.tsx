import Link from 'next/link';
import { useState } from 'react';
import { Ad, locations } from '@/lib/data';
import VerificationBadge from './VerificationBadge';
import SafeTimeDisplay from './SafeTimeDisplay';

interface AdCardProps {
  ad: Ad;
  viewMode?: 'grid' | 'list';
}

const AdCard = ({ ad, viewMode = 'grid' }: AdCardProps) => {
  const [isFavorite, setIsFavorite] = useState(false);
  const [imageError, setImageError] = useState(false);

  const formatPrice = (price: number, currency: string) => {
    const currencySymbol = currency === 'SYP' ? 'ل.س' : currency === 'USD' ? '$' : '€';
    return `${price.toLocaleString()} ${currencySymbol}`;
  };

  const getLocationName = () => {
    const location = locations[ad.location.governorate as keyof typeof locations];
    return location ? location.name : ad.location.governorate;
  };



  const toggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsFavorite(!isFavorite);
  };

  const handleImageError = () => {
    setImageError(true);
  };

  if (viewMode === 'list') {
    return (
      <Link
        href={`/ad/${ad.id}`}
        className="bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 p-4 border border-gray-100 hover:border-primary-200 group block"
        suppressHydrationWarning={true}
      >
        <div className="flex gap-4">
          {/* صورة الإعلان */}
          <div className="relative w-32 h-24 flex-shrink-0 overflow-hidden rounded-lg">
            {!imageError && ad.images && ad.images.length > 0 ? (
              <img
                src={ad.images[0]}
                alt={ad.title}
                className="w-full h-full object-cover"
                onError={handleImageError}
              />
            ) : (
              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                <span className="text-gray-400 text-2xl">🖼️</span>
              </div>
            )}

            {/* شارات */}
            <div className="absolute top-2 right-2 flex flex-col gap-1">
              {ad.featured && (
                <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                  مميز
                </span>
              )}
              {ad.status === 'active' && (
                <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                  نشط
                </span>
              )}
            </div>
          </div>

          {/* محتوى الإعلان */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <h3 className="font-semibold text-gray-800 group-hover:text-primary-600 transition-colors line-clamp-2">
                {ad.title}
              </h3>
              <button
                onClick={toggleFavorite}
                className="text-gray-400 hover:text-red-500 transition-colors ml-2"
              >
                {isFavorite ? '❤️' : '🤍'}
              </button>
            </div>

            <div className="flex items-center justify-between mb-2">
              <div className="text-xl font-bold text-primary-600">
                {formatPrice(ad.price, ad.currency)}
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                  {ad.category}
                </span>
                {ad.condition && (
                  <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                    {ad.condition === 'new' ? 'جديد' :
                     ad.condition === 'used' ? 'مستعمل' :
                     ad.condition === 'renovated' ? 'معفش' :
                     'مجدد'}
                  </span>
                )}
              </div>
            </div>

            <div className="flex items-center text-gray-600 text-sm mb-2">
              <span className="mr-1">📍</span>
              {getLocationName()}
            </div>

            {/* المواصفات */}
            {ad.specifications && Object.keys(ad.specifications).length > 0 && (
              <div className="flex flex-wrap gap-1 mb-2">
                {Object.entries(ad.specifications).slice(0, 3).map(([key, value], index) => (
                  <span
                    key={index}
                    className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                  >
                    {key}: {value}
                  </span>
                ))}
                {Object.keys(ad.specifications).length > 3 && (
                  <span className="text-xs text-gray-400 px-2 py-1">
                    +{Object.keys(ad.specifications).length - 3}
                  </span>
                )}
              </div>
            )}

            <div className="flex items-center justify-between text-sm text-gray-500">
              <SafeTimeDisplay dateString={ad.createdAt} />
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <span>👁️</span>
                  <span>{ad.views}</span>
                </div>
                {ad.seller && (
                  <div className="flex items-center gap-2">
                    <span>{ad.seller.type === 'business' ? '🏢' : '👤'}</span>
                    <span className="text-xs">{ad.seller.name}</span>
                    {ad.seller.verified && (
                      <VerificationBadge
                        badgeId={
                          ad.seller.type === 'business' && ad.seller.name.includes('مكتب') && ad.seller.name.includes('عقاري')
                            ? 'real-estate-office'
                            : ad.seller.type === 'business'
                              ? 'business-verified'
                              : 'verified-basic'
                        }
                        size="xs"
                      />
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </Link>
    );
  }

  // Grid Layout
  return (
    <div className="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group border border-gray-100 relative" suppressHydrationWarning={true}>
      {/* صورة الإعلان */}
      <div className="relative h-48 overflow-hidden">
        {!imageError && ad.images && ad.images.length > 0 ? (
          <img
            src={ad.images[0]}
            alt={ad.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            onError={handleImageError}
          />
        ) : (
          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
            <span className="text-gray-400 text-4xl">🖼️</span>
          </div>
        )}

        {/* شارات */}
        <div className="absolute top-3 right-3 flex flex-col gap-2">
          {ad.featured && (
            <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">
              مميز
            </span>
          )}
          {ad.status === 'active' && (
            <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">
              نشط
            </span>
          )}
        </div>

        {/* زر المفضلة */}
        <button
          onClick={toggleFavorite}
          className="absolute top-3 left-3 w-8 h-8 bg-white/80 rounded-full flex items-center justify-center hover:bg-white transition-colors"
        >
          <span className="text-gray-600 hover:text-red-500">
            {isFavorite ? '❤️' : '🤍'}
          </span>
        </button>

        {/* شارة التصنيف */}
        <div className="absolute bottom-3 right-3">
          <span className="bg-black/70 text-white text-xs px-2 py-1 rounded-full">
            {ad.category}
          </span>
        </div>
      </div>

      {/* محتوى الإعلان */}
      <div className="p-5">
        <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors">
          {ad.title}
        </h3>

        <div className="flex items-center justify-between mb-3">
          <div className="text-2xl font-bold text-primary-600">
            {formatPrice(ad.price, ad.currency)}
          </div>
          {ad.condition && (
            <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
              {ad.condition === 'new' ? 'جديد' :
               ad.condition === 'used' ? 'مستعمل' :
               ad.condition === 'renovated' ? 'معفش' :
               'مجدد'}
            </span>
          )}
        </div>

        <div className="flex items-center text-gray-600 text-sm mb-3">
          <span className="mr-1">📍</span>
          {getLocationName()}
        </div>

        {/* المواصفات */}
        {ad.specifications && Object.keys(ad.specifications).length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {Object.entries(ad.specifications).slice(0, 3).map(([key, value], index) => (
              <span
                key={index}
                className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
              >
                {key}: {value}
              </span>
            ))}
            {Object.keys(ad.specifications).length > 3 && (
              <span className="text-xs text-gray-400 px-2 py-1">
                +{Object.keys(ad.specifications).length - 3}
              </span>
            )}
          </div>
        )}

        {/* معلومات إضافية */}
        <div className="flex items-center justify-between text-sm text-gray-500 pt-3 border-t border-gray-100">
          <SafeTimeDisplay dateString={ad.createdAt} />
          <div className="flex items-center gap-1">
            <span>👁️</span>
            <span>{ad.views}</span>
          </div>
        </div>

        {/* معلومات البائع */}
        {ad.seller && (
          <div className="flex items-center justify-between mt-2 pt-2 border-t border-gray-100">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>{ad.seller.type === 'business' ? '🏢' : '👤'}</span>
              <span>{ad.seller.name}</span>
              {ad.seller.verified && (
                <VerificationBadge
                  badgeId={
                    ad.seller.type === 'business' && ad.seller.name.includes('مكتب') && ad.seller.name.includes('عقاري')
                      ? 'real-estate-office'
                      : ad.seller.type === 'business'
                        ? 'business-verified'
                        : 'verified-basic'
                  }
                  size="xs"
                />
              )}
            </div>
          </div>
        )}
      </div>

      {/* رابط الإعلان */}
      <Link
        href={`/ad/${ad.id}`}
        className="absolute inset-0 z-10"
      >
        <span className="sr-only">عرض الإعلان</span>
      </Link>
    </div>
  );
};

export default AdCard;
