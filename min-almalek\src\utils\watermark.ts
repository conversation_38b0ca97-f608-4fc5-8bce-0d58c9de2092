/**
 * Watermark utility for adding transparent white logo to uploaded images
 */

export interface WatermarkOptions {
  logoPath?: string;
  opacity?: number;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'center';
  size?: number; // percentage of image width
  margin?: number; // margin from edges in pixels
}

const DEFAULT_OPTIONS: Required<WatermarkOptions> = {
  logoPath: '/images/MinAlmalek Logo White.png',
  opacity: 0.3,
  position: 'bottom-right',
  size: 15, // 15% of image width
  margin: 20
};

/**
 * Add watermark to an image file
 */
export const addWatermarkToImage = async (
  imageFile: File,
  options: WatermarkOptions = {}
): Promise<File> => {
  const opts = { ...DEFAULT_OPTIONS, ...options };

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    const img = new Image();
    const logo = new Image();
    
    // Load the main image
    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      
      // Draw the main image
      ctx.drawImage(img, 0, 0);
      
      // Load and draw the watermark logo
      logo.onload = () => {
        const logoWidth = (img.width * opts.size) / 100;
        const logoHeight = (logo.height * logoWidth) / logo.width;
        
        let x: number, y: number;
        
        // Calculate position
        switch (opts.position) {
          case 'top-left':
            x = opts.margin;
            y = opts.margin;
            break;
          case 'top-right':
            x = img.width - logoWidth - opts.margin;
            y = opts.margin;
            break;
          case 'bottom-left':
            x = opts.margin;
            y = img.height - logoHeight - opts.margin;
            break;
          case 'bottom-right':
            x = img.width - logoWidth - opts.margin;
            y = img.height - logoHeight - opts.margin;
            break;
          case 'center':
            x = (img.width - logoWidth) / 2;
            y = (img.height - logoHeight) / 2;
            break;
          default:
            x = img.width - logoWidth - opts.margin;
            y = img.height - logoHeight - opts.margin;
        }
        
        // Set opacity and draw logo
        ctx.globalAlpha = opts.opacity;
        ctx.drawImage(logo, x, y, logoWidth, logoHeight);
        ctx.globalAlpha = 1.0;
        
        // Convert canvas to blob and create new file
        canvas.toBlob((blob) => {
          if (blob) {
            const watermarkedFile = new File([blob], imageFile.name, {
              type: imageFile.type,
              lastModified: Date.now()
            });
            resolve(watermarkedFile);
          } else {
            reject(new Error('Failed to create watermarked image'));
          }
        }, imageFile.type, 0.9);
      };
      
      logo.onerror = () => {
        reject(new Error('Failed to load watermark logo'));
      };
      
      logo.src = opts.logoPath;
    };
    
    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };
    
    // Create object URL for the image file
    const imageUrl = URL.createObjectURL(imageFile);
    img.src = imageUrl;
  });
};

/**
 * Add watermark to multiple images
 */
export const addWatermarkToImages = async (
  imageFiles: File[],
  options: WatermarkOptions = {}
): Promise<File[]> => {
  const watermarkedImages: File[] = [];
  
  for (const file of imageFiles) {
    try {
      const watermarkedFile = await addWatermarkToImage(file, options);
      watermarkedImages.push(watermarkedFile);
    } catch (error) {
      console.error('Failed to add watermark to image:', file.name, error);
      // If watermarking fails, use original image
      watermarkedImages.push(file);
    }
  }
  
  return watermarkedImages;
};

/**
 * Check if an image needs watermarking (based on file type)
 */
export const shouldWatermarkImage = (file: File): boolean => {
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  return supportedTypes.includes(file.type.toLowerCase());
};

/**
 * Get watermark preview for display purposes
 */
export const getWatermarkPreview = async (
  imageFile: File,
  options: WatermarkOptions = {}
): Promise<string> => {
  try {
    const watermarkedFile = await addWatermarkToImage(imageFile, options);
    return URL.createObjectURL(watermarkedFile);
  } catch (error) {
    console.error('Failed to create watermark preview:', error);
    return URL.createObjectURL(imageFile);
  }
};
