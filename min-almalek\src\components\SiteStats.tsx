import StarRating from './StarRating';

const SiteStats = () => {
  const stats = [
    {
      id: 1,
      title: 'إجمالي الإعلانات',
      value: '125,430',
      icon: '📝',
      color: 'bg-blue-100/60 border border-blue-200/50',
      iconColor: '#3b82f6',
      change: '+12%',
      changeType: 'increase'
    },
    {
      id: 2,
      title: 'المستخدمين النشطين',
      value: '45,280',
      icon: '👥',
      color: 'bg-green-100/60 border border-green-200/50',
      iconColor: '#10b981',
      change: '+8%',
      changeType: 'increase'
    },
    {
      id: 3,
      title: 'الإعلانات المميزة',
      value: '8,920',
      icon: '💎',
      color: 'bg-yellow-100/60 border border-yellow-200/50',
      iconColor: '#f59e0b',
      change: '+15%',
      changeType: 'increase'
    },
    {
      id: 4,
      title: 'المعاملات اليومية',
      value: '2,340',
      icon: '💰',
      color: 'bg-purple-100/60 border border-purple-200/50',
      iconColor: '#8b5cf6',
      change: '+5%',
      changeType: 'increase'
    }
  ];

  const categoryStats = [
    { name: 'العقارات', count: 45230, percentage: 36 },
    { name: 'السيارات', count: 28450, percentage: 23 },
    { name: 'الإلكترونيات', count: 22180, percentage: 18 },
    { name: 'الوظائف', count: 15670, percentage: 12 },
    { name: 'الخدمات', count: 8920, percentage: 7 },
    { name: 'أخرى', count: 4980, percentage: 4 }
  ];

  const locationStats = [
    { name: 'دمشق', count: 38450, percentage: 31 },
    { name: 'حلب', count: 28230, percentage: 22 },
    { name: 'حمص', count: 18670, percentage: 15 },
    { name: 'حماة', count: 12340, percentage: 10 },
    { name: 'اللاذقية', count: 15890, percentage: 13 },
    { name: 'أخرى', count: 11850, percentage: 9 }
  ];

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">إحصائيات الموقع</h2>
          <p className="text-lg text-gray-600">أرقام حقيقية تعكس نشاط منصتنا</p>
        </div>

        {/* الإحصائيات الرئيسية */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-4 mb-8 md:mb-12">
          {stats.map((stat) => (
            <div key={stat.id} className="bg-white rounded-lg shadow-md p-3 md:p-4 hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-between mb-2 md:mb-3">
                <div className={`w-7 h-7 md:w-8 md:h-8 ${stat.color} rounded-lg flex items-center justify-center text-sm backdrop-blur-sm`}>
                  <span style={{ color: stat.iconColor, fontSize: '0.875rem' }}>{stat.icon}</span>
                </div>
                <span className={`text-xs font-medium px-1.5 py-0.5 md:px-2 md:py-1 rounded-full ${
                  stat.changeType === 'increase'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {stat.change}
                </span>
              </div>
              <div className="text-lg md:text-2xl font-bold text-gray-800 mb-1">{stat.value}</div>
              <div className="text-gray-600 text-xs md:text-sm leading-tight">{stat.title}</div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* إحصائيات التصنيفات */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-800 mb-6">الإعلانات حسب التصنيف</h3>
            <div className="space-y-4">
              {categoryStats.map((category, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-4 h-4 bg-primary-500 rounded-full"></div>
                    <span className="font-medium text-gray-800">{category.name}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${category.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 w-16 text-left">
                      {category.count.toLocaleString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* إحصائيات المحافظات */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-800 mb-6">الإعلانات حسب المحافظة</h3>
            <div className="space-y-4">
              {locationStats.map((location, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
                    <span className="font-medium text-gray-800">{location.name}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${location.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600 w-16 text-left">
                      {location.count.toLocaleString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>





        {/* شهادات المستخدمين */}
        <div className="mt-12">
          <h3 className="text-2xl font-bold text-gray-800 text-center mb-8">ماذا يقول مستخدمونا</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[
              {
                name: 'أحمد محمد',
                role: 'مستخدم فردي',
                comment: 'منصة رائعة وسهلة الاستخدام، تمكنت من بيع سيارتي خلال أسبوع واحد!',
                rating: 5
              },
              {
                name: 'شركة العقارات الذهبية',
                role: 'شركة عقارات',
                comment: 'أفضل منصة للإعلانات العقارية في سوريا، عملاؤنا يجدوننا بسهولة.',
                rating: 5
              },
              {
                name: 'سارة أحمد',
                role: 'مستخدمة فردية',
                comment: 'وجدت الوظيفة المناسبة من خلال الموقع، خدمة ممتازة ودعم فني رائع.',
                rating: 5
              }
            ].map((testimonial, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center mb-4">
                  <StarRating rating={testimonial.rating} size="sm" showValue={false} />
                </div>
                <p className="text-gray-700 mb-4 italic">"{testimonial.comment}"</p>
                <div>
                  <div className="font-semibold text-gray-800">{testimonial.name}</div>
                  <div className="text-sm text-gray-600">{testimonial.role}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SiteStats;
