import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import { AdBadge } from '@/components/VerificationBadge';
import { determineUserBadge } from '@/lib/verification';
import StarRating from './StarRating';
import MessageModal from './MessageModal';

const featuredAds = [
  {
    id: 1,
    title: 'شقة للبيع في دمشق - المالكي',
    price: '85,000,000',
    currency: 'ل.س',
    location: 'دمشق - المالكي',
    category: 'عقارات',
    image: '/api/placeholder/300/200',
    featured: true,
    urgent: false,
    specs: ['3 غرف', '2 حمام', '150 م²'],
    postedDate: 'منذ ساعتين',
    views: 245,
    isFavorite: false,
    seller: {
      name: 'أحمد محمد',
      subscription: 'premium',
      adsCount: 15,
      rating: 4.5,
      membershipMonths: 8,
      isBusinessVerified: false
    }
  },
  {
    id: 2,
    title: 'BMW X5 2020 فل أوبشن',
    price: '45,000',
    currency: '$',
    location: 'حلب - الفرقان',
    category: 'سيارات',
    image: '/api/placeholder/300/200',
    featured: true,
    urgent: true,
    specs: ['2020', 'أوتوماتيك', '50,000 كم'],
    postedDate: 'منذ 3 ساعات',
    views: 189,
    isFavorite: false,
    seller: {
      name: 'معرض الفخامة للسيارات',
      subscription: 'business-professional',
      adsCount: 120,
      rating: 4.8,
      membershipMonths: 18,
      isBusinessVerified: true
    }
  },
  {
    id: 3,
    title: 'iPhone 15 Pro Max جديد بالكرتونة',
    price: '1,200',
    currency: '$',
    location: 'دمشق - أبو رمانة',
    category: 'إلكترونيات',
    image: '/api/placeholder/300/200',
    featured: true,
    urgent: false,
    specs: ['256 GB', 'تيتانيوم', 'ضمان سنة'],
    postedDate: 'منذ 5 ساعات',
    views: 156,
    isFavorite: false,
    seller: {
      name: 'فاطمة أحمد',
      subscription: 'basic',
      adsCount: 8,
      rating: 4.2,
      membershipMonths: 3,
      isBusinessVerified: false
    }
  },
  {
    id: 4,
    title: 'مطلوب مطور ويب - دوام كامل',
    price: '800,000',
    currency: 'ل.س',
    location: 'دمشق - المزة',
    category: 'وظائف',
    image: '/api/placeholder/300/200',
    featured: true,
    urgent: false,
    specs: ['React', 'Node.js', 'خبرة 3 سنوات'],
    postedDate: 'منذ يوم',
    views: 98,
    isFavorite: false,
    seller: {
      name: 'شركة التقنيات المتقدمة',
      subscription: 'business-starter',
      adsCount: 45,
      rating: 4.6,
      membershipMonths: 12,
      isBusinessVerified: true
    }
  },
  {
    id: 5,
    title: 'فيلا للإيجار في حمص',
    price: '500,000',
    currency: 'ل.س',
    location: 'حمص - الوعر',
    category: 'عقارات',
    image: '/api/placeholder/300/200',
    featured: true,
    urgent: false,
    specs: ['5 غرف', 'حديقة', '300 م²'],
    postedDate: 'منذ يومين',
    views: 134,
    isFavorite: false,
    seller: {
      name: 'خالد السوري',
      subscription: 'business',
      adsCount: 65,
      rating: 4.7,
      membershipMonths: 15,
      isBusinessVerified: false
    }
  },
  {
    id: 6,
    title: 'لابتوب Dell Gaming جديد',
    price: '850',
    currency: '$',
    location: 'اللاذقية - الزراعة',
    category: 'إلكترونيات',
    image: '/api/placeholder/300/200',
    featured: true,
    urgent: true,
    specs: ['RTX 3060', '16GB RAM', 'SSD 512GB'],
    postedDate: 'منذ 3 أيام',
    views: 87,
    isFavorite: false,
    seller: {
      name: 'محمد علي',
      subscription: 'premium',
      adsCount: 25,
      rating: 4.6,
      membershipMonths: 10,
      isBusinessVerified: false
    }
  },

];

const FeaturedAds = () => {
  const [messageModal, setMessageModal] = useState<{
    isOpen: boolean;
    adId: number;
    recipientName: string;
    recipientPhone: string;
    adTitle: string;
  }>({
    isOpen: false,
    adId: 0,
    recipientName: '',
    recipientPhone: '',
    adTitle: ''
  });

  const openMessageModal = (ad: any) => {
    setMessageModal({
      isOpen: true,
      adId: ad.id,
      recipientName: ad.seller.name,
      recipientPhone: '+963988652401', // Default phone or ad.seller.phone
      adTitle: ad.title
    });
  };

  const closeMessageModal = () => {
    setMessageModal(prev => ({ ...prev, isOpen: false }));
  };

  return (
    <section className="py-12 bg-gradient-to-br from-primary-50 to-blue-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-3xl font-bold text-gray-800 mb-2">الإعلانات المميزة</h2>
            <p className="text-gray-600">أفضل العروض والإعلانات المختارة بعناية</p>
          </div>
          <Link
            href="/featured-ads"
            className="text-primary-600 hover:text-primary-700 font-medium flex items-center gap-2"
          >
            عرض الكل
            <span>←</span>
          </Link>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredAds.map((ad) => (
            <div
              key={ad.id}
              className="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group border border-gray-100"
            >
              {/* Image Container */}
              <div className="relative h-48 overflow-hidden">
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-400 text-4xl">🖼️</span>
                </div>

                {/* Badges */}
                <div className="absolute top-3 right-3 flex flex-col gap-2">
                  {ad.featured && (
                    <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                      مميز
                    </span>
                  )}
                  {ad.urgent && (
                    <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                      عاجل
                    </span>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="absolute top-3 left-3 flex flex-col gap-2">
                  {/* Favorite Button */}
                  <button
                    className="w-8 h-8 bg-white/90 rounded-full flex items-center justify-center transition-all duration-300 hover:bg-white hover:scale-110 group/fav"
                    style={{filter: 'drop-shadow(0 0 4px rgba(0, 0, 0, 0.1))'}}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      // Toggle favorite logic here
                    }}
                  >
                    <svg
                      className="w-4 h-4 text-gray-600 group-hover/fav:text-green-500 transition-all duration-300"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      style={{filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.3))'}}
                    >
                      <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                    </svg>
                  </button>

                  {/* Message Button */}
                  <button
                    className="w-8 h-8 bg-white/90 rounded-full flex items-center justify-center transition-all duration-300 hover:bg-white hover:scale-110 group/msg"
                    style={{filter: 'drop-shadow(0 0 4px rgba(0, 0, 0, 0.1))'}}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      openMessageModal(ad);
                    }}
                  >
                    <svg
                      className="w-4 h-4 text-gray-600 group-hover/msg:text-blue-500 transition-all duration-300"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                      style={{filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.3))'}}
                    >
                      <path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                    </svg>
                  </button>
                </div>

                {/* Category Badge */}
                <div className="absolute bottom-3 right-3">
                  <span className="bg-black/70 text-white text-xs px-2 py-1 rounded-full">
                    {ad.category}
                  </span>
                </div>
              </div>

              {/* Content */}
              <div className="p-5">
                <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors">
                  {ad.title}
                </h3>

                <div className="flex items-center justify-between mb-3">
                  <div className="text-2xl font-bold text-primary-600">
                    {ad.price.toLocaleString()} {ad.currency}
                  </div>
                </div>

                <div className="flex items-center text-gray-600 text-sm mb-3">
                  <span className="mr-1">📍</span>
                  {ad.location}
                </div>

                {/* معلومات البائع مع شارة التوثيق */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex flex-col gap-1">
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-gray-600">بواسطة:</span>
                      <span className="text-sm font-medium text-gray-800">{ad.seller.name}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <StarRating rating={ad.seller.rating} size="xs" showValue={true} />
                    </div>
                  </div>
                  <AdBadge
                    userBadges={determineUserBadge(
                      ad.seller.subscription,
                      ad.seller.adsCount,
                      ad.seller.rating,
                      ad.seller.membershipMonths,
                      ad.seller.isBusinessVerified
                    )}
                    size="xs"
                  />
                </div>

                {/* Specs */}
                <div className="flex flex-wrap gap-1 mb-4">
                  {ad.specs.map((spec, index) => (
                    <span
                      key={index}
                      className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                    >
                      {spec}
                    </span>
                  ))}
                </div>

                {/* Footer */}
                <div className="flex items-center justify-between text-sm text-gray-500 pt-3 border-t border-gray-100">
                  <span>{ad.postedDate}</span>
                  <div className="flex items-center gap-1">
                    <span>👁️</span>
                    <span>{ad.views}</span>
                  </div>
                </div>
              </div>


            </div>
          ))}
        </div>

        <div className="text-center mt-10">
          <Link
            href="/featured"
            className="inline-flex items-center px-8 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors font-medium text-lg"
          >
            عرض جميع الإعلانات المميزة
            <span className="mr-2">→</span>
          </Link>
        </div>
      </div>

      {/* Message Modal */}
      <MessageModal
        isOpen={messageModal.isOpen}
        onClose={closeMessageModal}
        recipientName={messageModal.recipientName}
        recipientPhone={messageModal.recipientPhone}
        adTitle={messageModal.adTitle}
        adId={messageModal.adId}
      />
    </section>
  );
};

export default FeaturedAds;
