'use client';

import { useEffect, useState } from 'react';

interface SafeTimeDisplayProps {
  dateString: string;
  className?: string;
}

const SafeTimeDisplay = ({ dateString, className = '' }: SafeTimeDisplayProps) => {
  const [timeAgo, setTimeAgo] = useState<string>('');
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    
    const calculateTimeAgo = () => {
      const date = new Date(dateString);
      const now = new Date();
      const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

      if (diffInHours < 1) return 'منذ أقل من ساعة';
      if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;

      const diffInDays = Math.floor(diffInHours / 24);
      if (diffInDays < 7) return `منذ ${diffInDays} يوم`;

      const diffInWeeks = Math.floor(diffInDays / 7);
      if (diffInWeeks < 4) return `منذ ${diffInWeeks} أسبوع`;

      const diffInMonths = Math.floor(diffInDays / 30);
      return `منذ ${diffInMonths} شهر`;
    };

    setTimeAgo(calculateTimeAgo());
  }, [dateString]);

  // عرض placeholder أثناء hydration
  if (!isClient) {
    return (
      <span className={className} suppressHydrationWarning={true}>
        منذ وقت قريب
      </span>
    );
  }

  return (
    <span className={className}>
      {timeAgo}
    </span>
  );
};

export default SafeTimeDisplay;
