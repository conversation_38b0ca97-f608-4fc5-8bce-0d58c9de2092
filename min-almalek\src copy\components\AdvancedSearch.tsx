'use client';

import { useState, useEffect } from 'react';
import { DataService, SearchFilters, categories, locations, Ad } from '@/lib/data';

const AdvancedSearch = () => {
  const [filters, setFilters] = useState<SearchFilters>({
    keyword: '',
    category: '',
    governorate: '',
    priceMin: undefined,
    priceMax: undefined,
    currency: undefined,
    condition: undefined,
    sellerType: undefined,
    verified: false,
    featured: false,
    hasImages: false,
    datePosted: undefined,
    sortBy: 'newest'
  });

  const [searchResults, setSearchResults] = useState<Ad[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);

  // تحديث النتائج عند تغيير الفلاتر
  useEffect(() => {
    if (showResults) {
      performSearch();
    }
  }, [filters, showResults]);

  const performSearch = () => {
    setIsSearching(true);

    // محاكاة تأخير البحث
    setTimeout(() => {
      const results = DataService.searchAds(filters);
      setSearchResults(results);
      setIsSearching(false);
    }, 500);
  };

  const handleSearch = () => {
    setShowResults(true);
    performSearch();
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      keyword: '',
      category: '',
      governorate: '',
      priceMin: undefined,
      priceMax: undefined,
      currency: undefined,
      condition: undefined,
      sellerType: undefined,
      verified: false,
      featured: false,
      hasImages: false,
      datePosted: undefined,
      sortBy: 'newest'
    });
    setShowResults(false);
    setSearchResults([]);
  };

  const conditions = [
    { id: 'new', name: 'جديد' },
    { id: 'renovated', name: 'معفش' },
    { id: 'used', name: 'مستعمل' },
    { id: 'refurbished', name: 'مجدد' },
    { id: 'used-excellent', name: 'مستعمل - ممتاز' },
    { id: 'used-good', name: 'مستعمل - جيد' },
    { id: 'used-fair', name: 'مستعمل - مقبول' }
  ];

  const dateOptions = [
    { id: 'today', name: 'اليوم' },
    { id: 'week', name: 'آخر أسبوع' },
    { id: 'month', name: 'آخر شهر' },
    { id: 'all', name: 'كل الأوقات' }
  ];

  const sortOptions = [
    { id: 'newest', name: 'الأحدث أولاً' },
    { id: 'oldest', name: 'الأقدم أولاً' },
    { id: 'price-low', name: 'السعر: من الأقل للأعلى' },
    { id: 'price-high', name: 'السعر: من الأعلى للأقل' },
    { id: 'most-viewed', name: 'الأكثر مشاهدة' }
  ];

  // فئات الأسعار بالليرة السورية
  const priceRangesSYP = [
    { id: 'under-100k', name: 'أقل من 100 ألف', min: 0, max: 100000 },
    { id: '100k-500k', name: '100 - 500 ألف', min: 100000, max: 500000 },
    { id: '500k-1m', name: '500 ألف - 1 مليون', min: 500000, max: 1000000 },
    { id: '1m-2m', name: '1 - 2 مليون', min: 1000000, max: 2000000 },
    { id: '2m-5m', name: '2 - 5 مليون', min: 2000000, max: 5000000 },
    { id: '5m-10m', name: '5 - 10 مليون', min: 5000000, max: 10000000 },
    { id: '10m-20m', name: '10 - 20 مليون', min: 10000000, max: 20000000 },
    { id: '20m-50m', name: '20 - 50 مليون', min: 20000000, max: 50000000 },
    { id: 'over-50m', name: 'أكثر من 50 مليون', min: 50000000, max: null }
  ];

  // فئات الأسعار بالدولار
  const priceRangesUSD = [
    { id: 'under-100', name: 'أقل من 100 دولار', min: 0, max: 100 },
    { id: '100-300', name: '100 - 300 دولار', min: 100, max: 300 },
    { id: '300-500', name: '300 - 500 دولار', min: 300, max: 500 },
    { id: '500-1k', name: '500 - 1000 دولار', min: 500, max: 1000 },
    { id: '1k-2k', name: '1000 - 2000 دولار', min: 1000, max: 2000 },
    { id: '2k-5k', name: '2000 - 5000 دولار', min: 2000, max: 5000 },
    { id: '5k-10k', name: '5000 - 10000 دولار', min: 5000, max: 10000 },
    { id: '10k-20k', name: '10000 - 20000 دولار', min: 10000, max: 20000 },
    { id: 'over-20k', name: 'أكثر من 20000 دولار', min: 20000, max: null }
  ];

  // فئات الأسعار باليورو
  const priceRangesEUR = [
    { id: 'under-100', name: 'أقل من 100 يورو', min: 0, max: 100 },
    { id: '100-300', name: '100 - 300 يورو', min: 100, max: 300 },
    { id: '300-500', name: '300 - 500 يورو', min: 300, max: 500 },
    { id: '500-1k', name: '500 - 1000 يورو', min: 500, max: 1000 },
    { id: '1k-2k', name: '1000 - 2000 يورو', min: 1000, max: 2000 },
    { id: '2k-5k', name: '2000 - 5000 يورو', min: 2000, max: 5000 },
    { id: '5k-10k', name: '5000 - 10000 يورو', min: 5000, max: 10000 },
    { id: '10k-20k', name: '10000 - 20000 يورو', min: 10000, max: 20000 },
    { id: 'over-20k', name: 'أكثر من 20000 يورو', min: 20000, max: null }
  ];

  const currencies = [
    { id: 'SYP', name: 'ليرة سورية', symbol: 'ل.س' },
    { id: 'USD', name: 'دولار أمريكي', symbol: '$' },
    { id: 'EUR', name: 'يورو', symbol: '€' }
  ];

  const getCurrentPriceRanges = () => {
    switch (filters.currency) {
      case 'USD':
        return priceRangesUSD;
      case 'EUR':
        return priceRangesEUR;
      default:
        return priceRangesSYP;
    }
  };

  const handlePriceRangeSelect = (range: any) => {
    setFilters(prev => ({
      ...prev,
      priceMin: range.min,
      priceMax: range.max
    }));
  };



  return (
    <div className="space-y-6">
      {/* البحث بالكلمات المفتاحية */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          البحث بالكلمات المفتاحية
        </label>
        <input
          type="text"
          value={filters.keyword || ''}
          onChange={(e) => handleFilterChange('keyword', e.target.value)}
          placeholder="ابحث عن..."
          className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* التصنيف */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            التصنيف
          </label>
          <select
            value={filters.category}
            onChange={(e) => handleFilterChange('category', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">جميع التصنيفات</option>
            {Object.entries(categories).map(([key, cat]) => (
              <option key={key} value={key}>{cat.name}</option>
            ))}
          </select>
        </div>

        {/* الموقع */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            المحافظة
          </label>
          <select
            value={filters.governorate}
            onChange={(e) => handleFilterChange('governorate', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">جميع المحافظات</option>
            {Object.entries(locations).map(([key, loc]) => (
              <option key={key} value={key}>{loc.name}</option>
            ))}
          </select>
        </div>

        {/* الحالة */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            الحالة
          </label>
          <select
            value={filters.condition}
            onChange={(e) => handleFilterChange('condition', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">جميع الحالات</option>
            {conditions.map(cond => (
              <option key={cond.id} value={cond.id}>{cond.name}</option>
            ))}
          </select>
        </div>

        {/* العملة */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            العملة
          </label>
          <select
            value={filters.currency || 'SYP'}
            onChange={(e) => handleFilterChange('currency', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            {currencies.map(currency => (
              <option key={currency.id} value={currency.id}>
                {currency.name} ({currency.symbol})
              </option>
            ))}
          </select>
        </div>

        {/* السعر الأدنى */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            السعر الأدنى
          </label>
          <input
            type="number"
            value={filters.priceMin || ''}
            onChange={(e) => handleFilterChange('priceMin', e.target.value ? parseInt(e.target.value) : undefined)}
            placeholder="0"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>

        {/* السعر الأعلى */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            السعر الأعلى
          </label>
          <input
            type="number"
            value={filters.priceMax || ''}
            onChange={(e) => handleFilterChange('priceMax', e.target.value ? parseInt(e.target.value) : undefined)}
            placeholder="بلا حدود"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>

        {/* نوع البائع */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            نوع البائع
          </label>
          <select
            value={filters.sellerType || ''}
            onChange={(e) => handleFilterChange('sellerType', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">جميع البائعين</option>
            <option value="individual">أفراد</option>
            <option value="business">شركات</option>
            <option value="real-estate-office">مكاتب عقارية</option>
          </select>
        </div>

        {/* تاريخ النشر */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            تاريخ النشر
          </label>
          <select
            value={filters.datePosted}
            onChange={(e) => handleFilterChange('datePosted', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">كل الأوقات</option>
            {dateOptions.map(date => (
              <option key={date.id} value={date.id}>{date.name}</option>
            ))}
          </select>
        </div>
      </div>

      {/* فئات الأسعار السريعة */}
      <div className="mt-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          فئات الأسعار السريعة ({
            filters.currency === 'USD' ? 'دولار' :
            filters.currency === 'EUR' ? 'يورو' :
            'ليرة سورية'
          })
        </label>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
          {getCurrentPriceRanges().map(range => (
            <button
              key={range.id}
              onClick={() => handlePriceRangeSelect(range)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors border ${
                filters.priceMin === range.min && filters.priceMax === range.max
                  ? 'bg-primary-600 text-white border-primary-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
              }`}
            >
              {range.name}
            </button>
          ))}
          {/* زر مسح فئة السعر */}
          <button
            onClick={() => {
              handleFilterChange('priceMin', undefined);
              handleFilterChange('priceMax', undefined);
            }}
            className="px-3 py-2 rounded-lg text-sm font-medium transition-colors border border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400"
          >
            مسح السعر
          </button>
        </div>
        <div className="mt-2 text-xs text-gray-500">
          💡 انقر على فئة سعر لتطبيقها تلقائياً، أو أدخل مبلغ مخصص في الحقول أعلاه
        </div>
      </div>

      {/* ترتيب النتائج */}
      <div className="mt-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          ترتيب النتائج
        </label>
        <div className="flex flex-wrap gap-2">
          {sortOptions.map(option => (
            <button
              key={option.id}
              onClick={() => handleFilterChange('sortBy', option.id)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                filters.sortBy === option.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {option.name}
            </button>
          ))}
        </div>
      </div>

      {/* أزرار البحث */}
      <div className="flex gap-4 mt-8">
        <button
          onClick={handleSearch}
          className="flex-1 bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 transition-colors font-semibold"
        >
          🔍 بحث متقدم
        </button>
        <button
          onClick={clearFilters}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          مسح الكل
        </button>
      </div>

      {/* عدد النتائج المتوقعة */}
      <div className="mt-4 text-center text-sm text-gray-600">
        النتائج المتوقعة: <span className="font-semibold text-primary-600">1,234 إعلان</span>
      </div>
    </div>
  );
};

export default AdvancedSearch;
