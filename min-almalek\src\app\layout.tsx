import type { Metada<PERSON> } from "next";
import { Cairo } from "next/font/google";
import "./globals.css";
import "@/styles/loading.css";
import HydrationProvider from "@/components/HydrationProvider";
import LiveChat from '@/components/LiveChat';
import ClientSideProtection from '@/components/ClientSideProtection';
import ScrollIndicators from '@/components/ScrollIndicators';
import AssistiveTouch from '@/components/FloatingActionButton';
import { NotificationProvider } from '@/components/NotificationSystem';
import { AdvancedNotificationProvider } from '@/components/AdvancedNotificationSystem';
import { ToastProvider } from '@/components/ToastManager';
import { ModalProvider } from '@/components/NotificationModal';
import { AuthProvider } from '@/contexts/AuthContext';
import { NavigationLoader } from '@/components/LoadingScreen';
import { Toaster } from 'react-hot-toast';

const cairo = Cairo({
  variable: "--font-cairo",
  subsets: ["arabic", "latin"],
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "من المالك - موقع الإعلانات المبوبة في سوريا",
  description: "موقع من المالك للإعلانات المبوبة في سوريا - عقارات، سيارات، إلكترونيات، وظائف وأكثر",
  keywords: "إعلانات مبوبة، سوريا، عقارات، سيارات، وظائف، من المالك",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl" suppressHydrationWarning={true}>
      <head>
        <meta name="grammarly-disable-editor" content="true" />
        <meta name="grammarly-disable-indicator" content="true" />
        <meta name="grammarly-disable" content="true" />
        <meta name="grammarly-extension-installed" content="false" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="format-detection" content="date=no" />
        <meta name="format-detection" content="address=no" />
        <meta name="format-detection" content="email=no" />

        <script
          dangerouslySetInnerHTML={{
            __html: `
              // منع Grammarly من التدخل في الصفحة
              if (typeof window !== 'undefined') {
                // منع إضافة attributes
                const originalSetAttribute = Element.prototype.setAttribute;
                Element.prototype.setAttribute = function(name, value) {
                  if (name.includes('grammarly') || name.includes('data-new-gr') || name.includes('data-gr-ext')) {
                    return;
                  }
                  return originalSetAttribute.call(this, name, value);
                };

                // إزالة عناصر Grammarly بشكل دوري
                const removeGrammarly = () => {
                  const selectors = [
                    'grammarly-extension',
                    'grammarly-popups',
                    'grammarly-desktop-integration',
                    '[data-grammarly-shadow-root]',
                    '[data-grammarly-part]'
                  ];

                  selectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => el.remove());
                  });

                  // إزالة attributes
                  ['data-new-gr-c-s-check-loaded', 'data-gr-ext-installed', 'data-new-gr-c-s-loaded'].forEach(attr => {
                    document.body.removeAttribute(attr);
                    document.documentElement.removeAttribute(attr);
                  });
                };

                // تشغيل التنظيف كل 100ms
                setInterval(removeGrammarly, 100);

                // تشغيل التنظيف عند تحميل الصفحة
                document.addEventListener('DOMContentLoaded', removeGrammarly);
                window.addEventListener('load', removeGrammarly);
              }
            `,
          }}
        />
      </head>
      <body
        className={`${cairo.variable} font-cairo antialiased`}
      >
        <HydrationProvider>
          <AuthProvider>
            <NotificationProvider>
              <AdvancedNotificationProvider>
                <ModalProvider>
                  <ToastProvider position="top-right" maxToasts={5}>
                    <NavigationLoader />
                    {children}
                  </ToastProvider>
                </ModalProvider>
              </AdvancedNotificationProvider>
              <Toaster
                position="top-center"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: '#363636',
                    color: '#fff',
                    fontFamily: 'Cairo, sans-serif',
                    direction: 'rtl',
                    textAlign: 'right'
                  },
                  success: {
                    style: {
                      background: '#10b981',
                    },
                  },
                  error: {
                    style: {
                      background: '#ef4444',
                    },
                  },
                }}
              />
            </NotificationProvider>
          </AuthProvider>
        </HydrationProvider>
        <ScrollIndicators />
        <ClientSideProtection />
        <LiveChat />
        <AssistiveTouch />
      </body>
    </html>
  );
}
