'use client';

import { useState } from 'react';
import Link from 'next/link';

const syrianGovernorates = [
  {
    id: 'damascus',
    name: 'دمشق',
    arabicName: 'دمشق',
    adsCount: 15420
  },
  {
    id: 'damascus-countryside',
    name: 'ريف دمشق',
    arabicName: 'ريف دمشق',
    adsCount: 8930
  },
  {
    id: 'aleppo',
    name: 'حلب',
    arabicName: 'حلب',
    adsCount: 12650
  },
  {
    id: 'homs',
    name: 'حمص',
    arabicName: 'حمص',
    adsCount: 6780
  },
  {
    id: 'hama',
    name: 'حماة',
    arabicName: 'حماة',
    adsCount: 4520
  },
  {
    id: 'lattakia',
    name: 'اللاذقية',
    arabicName: 'اللاذقية',
    adsCount: 5680
  },
  {
    id: 'tartous',
    name: 'طرطوس',
    arabicName: 'طرطوس',
    adsCount: 3240
  },
  {
    id: 'idlib',
    name: 'إدل<PERSON>',
    arabicName: 'إدلب',
    adsCount: 2150
  },
  {
    id: 'daraa',
    name: 'درعا',
    arabicName: 'درعا',
    adsCount: 2890
  },
  {
    id: 'sweida',
    name: 'السويداء',
    arabicName: 'السويداء',
    adsCount: 1980
  },
  {
    id: 'quneitra',
    name: 'القنيطرة',
    arabicName: 'القنيطرة',
    adsCount: 890
  },
  {
    id: 'deir-ez-zor',
    name: 'دير الزور',
    arabicName: 'دير الزور',
    adsCount: 3450
  },
  {
    id: 'raqqa',
    name: 'الرقة',
    arabicName: 'الرقة',
    adsCount: 2760
  },
  {
    id: 'hasaka',
    name: 'الحسكة',
    arabicName: 'الحسكة',
    adsCount: 3120
  }
];

const LocationFilter = () => {
  const [pressedGovernorate, setPressedGovernorate] = useState<string | null>(null);

  return (
    <section className="py-12 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-10">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">تصفح حسب المحافظة</h2>
          <p className="text-gray-600 text-lg">اختر محافظتك للعثور على الإعلانات القريبة منك</p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-4">
          {syrianGovernorates.map((governorate) => (
            <Link
              key={governorate.id}
              href={`/location/${governorate.id}`}
              className={`bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-primary-300 overflow-hidden group cursor-pointer transform hover:scale-105 ${
                pressedGovernorate === governorate.id
                  ? 'shadow-[0_0_20px_rgba(59,130,246,0.6)] border-primary-400 scale-105'
                  : ''
              }`}
              onMouseDown={() => setPressedGovernorate(governorate.id)}
              onMouseUp={() => setPressedGovernorate(null)}
              onMouseLeave={() => setPressedGovernorate(null)}
              onTouchStart={() => setPressedGovernorate(governorate.id)}
              onTouchEnd={() => setPressedGovernorate(null)}
            >
              <div className="p-4 text-center">
                <h3 className="text-lg font-semibold text-gray-800 group-hover:text-primary-600 transition-colors mb-2">
                  {governorate.arabicName}
                </h3>
                <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
                  {governorate.adsCount.toLocaleString()}
                </span>
              </div>
            </Link>
          ))}
        </div>



        {/* Quick Stats */}
        <div className="mt-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl p-8 text-white">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 text-center">
            <div>
              <div className="text-3xl font-bold mb-2">
                {syrianGovernorates.reduce((sum, gov) => sum + gov.adsCount, 0).toLocaleString()}
              </div>
              <div className="text-primary-100">إجمالي الإعلانات</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">{syrianGovernorates.length}</div>
              <div className="text-primary-100">محافظة مغطاة</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">50,000+</div>
              <div className="text-primary-100">مستخدم نشط</div>
            </div>
            <div>
              <div className="text-3xl font-bold mb-2">24/7</div>
              <div className="text-primary-100">خدمة مستمرة</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LocationFilter;
