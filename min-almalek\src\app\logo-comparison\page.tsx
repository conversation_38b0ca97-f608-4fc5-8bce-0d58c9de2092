'use client';

import Image from 'next/image';
import Logo from '@/components/Logo';
import LogoSVG from '@/components/LogoSVG';

export default function LogoComparisonPage() {
  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">مقارنة الشعارات</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* الشعار الأصلي (الصورة) */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-bold mb-6 text-center">الشعار الأصلي (صورة)</h2>
            
            <div className="space-y-6">
              {/* عرض مباشر للصورة */}
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-4">عرض مباشر للصورة:</h3>
                <Image
                  src="/images/logo%20.%20min%20almalek.jpg"
                  alt="من المالك"
                  width={200}
                  height={100}
                  className="mx-auto object-contain"
                />
              </div>
              
              {/* باستخدام مكون Logo */}
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-4">باستخدام مكون Logo:</h3>
                <div className="flex justify-center">
                  <Logo variant="transparent" size="lg" showText={false} />
                </div>
              </div>
              
              {/* مع النص */}
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-4">مع النص:</h3>
                <div className="flex justify-center">
                  <Logo variant="transparent" size="md" showText={true} />
                </div>
              </div>
            </div>
          </div>

          {/* الشعار البديل (SVG) */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-bold mb-6 text-center">الشعار البديل (SVG)</h2>
            
            <div className="space-y-6">
              {/* شعار SVG */}
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-4">شعار SVG بالألوان الصحيحة:</h3>
                <div className="flex justify-center">
                  <LogoSVG size="lg" showText={false} />
                </div>
              </div>
              
              {/* مع النص */}
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-4">مع النص:</h3>
                <div className="flex justify-center">
                  <LogoSVG size="md" showText={true} />
                </div>
              </div>
              
              {/* أحجام مختلفة */}
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-4">أحجام مختلفة:</h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-center gap-4">
                    <span className="w-16 text-sm">صغير:</span>
                    <LogoSVG size="sm" showText={false} />
                  </div>
                  <div className="flex items-center justify-center gap-4">
                    <span className="w-16 text-sm">متوسط:</span>
                    <LogoSVG size="md" showText={false} />
                  </div>
                  <div className="flex items-center justify-center gap-4">
                    <span className="w-16 text-sm">كبير:</span>
                    <LogoSVG size="lg" showText={false} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* اختبار على خلفيات مختلفة */}
        <div className="mt-8 space-y-6">
          <h2 className="text-2xl font-bold text-center">اختبار على خلفيات مختلفة</h2>
          
          {/* خلفية بيضاء */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4">خلفية بيضاء:</h3>
            <div className="flex items-center justify-around">
              <div className="text-center">
                <p className="text-sm mb-2">الشعار الأصلي</p>
                <Logo variant="transparent" size="md" showText={true} />
              </div>
              <div className="text-center">
                <p className="text-sm mb-2">الشعار البديل</p>
                <LogoSVG size="md" showText={true} />
              </div>
            </div>
          </div>

          {/* خلفية داكنة */}
          <div className="bg-gray-800 p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4 text-white">خلفية داكنة:</h3>
            <div className="flex items-center justify-around">
              <div className="text-center">
                <p className="text-sm mb-2 text-white">الشعار الأصلي</p>
                <Logo variant="white" size="md" showText={true} />
              </div>
              <div className="text-center">
                <p className="text-sm mb-2 text-white">الشعار البديل</p>
                <LogoSVG size="md" showText={true} />
              </div>
            </div>
          </div>

          {/* خلفية ملونة */}
          <div className="bg-primary-600 p-6 rounded-lg shadow">
            <h3 className="text-lg font-semibold mb-4 text-white">خلفية ملونة:</h3>
            <div className="flex items-center justify-around">
              <div className="text-center">
                <p className="text-sm mb-2 text-white">الشعار الأصلي</p>
                <Logo variant="white" size="md" showText={true} />
              </div>
              <div className="text-center">
                <p className="text-sm mb-2 text-white">الشعار البديل</p>
                <LogoSVG size="md" showText={true} />
              </div>
            </div>
          </div>
        </div>

        {/* معلومات تقنية */}
        <div className="mt-8 bg-blue-50 p-6 rounded-lg">
          <h3 className="text-lg font-bold mb-4">معلومات تقنية:</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h4 className="font-semibold">الشعار الأصلي:</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>نوع الملف: JPG</li>
                <li>المسار: /images/logo%20.%20min%20almalek.jpg</li>
                <li>يعتمد على ملف صورة خارجي</li>
                <li>قد يتأثر بجودة الصورة</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold">الشعار البديل:</h4>
              <ul className="list-disc list-inside space-y-1">
                <li>نوع الملف: SVG مدمج</li>
                <li>ألوان محددة برمجياً</li>
                <li>قابل للتخصيص بسهولة</li>
                <li>جودة عالية في جميع الأحجام</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
