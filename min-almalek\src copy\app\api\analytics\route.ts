import { NextRequest, NextResponse } from 'next/server';
import { AuthService } from '@/lib/auth';
import { AnalyticsService } from '@/lib/analytics';

export async function GET(request: NextRequest) {
  try {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'غير مصرح' },
        { status: 401 }
      );
    }

    // التحقق من صحة الجلسة
    const sessionResult = await AuthService.verifySession(token);

    if (!sessionResult.success || !sessionResult.data) {
      return NextResponse.json(
        { success: false, error: 'جلسة غير صالحة' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'user';
    const period = (searchParams.get('period') as 'week' | 'month' | 'year') || 'month';
    const format = (searchParams.get('format') as 'json' | 'csv') || 'json';
    const export_data = searchParams.get('export') === 'true';

    switch (type) {
      case 'user':
        const userAnalytics = AnalyticsService.getUserAnalytics(
          sessionResult.data.id,
          period as 'month' | 'year'
        );

        if (export_data) {
          const exportResult = AnalyticsService.exportReport('user', userAnalytics, format);
          
          const response = new NextResponse(exportResult.content);
          response.headers.set('Content-Type', exportResult.mimeType);
          response.headers.set('Content-Disposition', `attachment; filename="${exportResult.filename}"`);
          
          return response;
        }

        return NextResponse.json({
          success: true,
          data: userAnalytics
        });

      case 'site':
        // التحقق من صلاحيات الإدارة (في التطبيق الحقيقي)
        if (sessionResult.data.type !== 'business') {
          return NextResponse.json(
            { success: false, error: 'غير مصرح لعرض إحصائيات الموقع' },
            { status: 403 }
          );
        }

        const siteAnalytics = AnalyticsService.getSiteAnalytics(period);

        if (export_data) {
          const exportResult = AnalyticsService.exportReport('site', siteAnalytics, format);
          
          const response = new NextResponse(exportResult.content);
          response.headers.set('Content-Type', exportResult.mimeType);
          response.headers.set('Content-Disposition', `attachment; filename="${exportResult.filename}"`);
          
          return response;
        }

        return NextResponse.json({
          success: true,
          data: siteAnalytics
        });

      case 'ad':
        const adId = searchParams.get('adId');
        
        if (!adId) {
          return NextResponse.json(
            { success: false, error: 'معرف الإعلان مطلوب' },
            { status: 400 }
          );
        }

        const adAnalytics = AnalyticsService.getAdAnalytics(parseInt(adId));

        if (!adAnalytics) {
          return NextResponse.json(
            { success: false, error: 'الإعلان غير موجود' },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          data: adAnalytics
        });

      case 'keywords':
        const keywordAnalytics = AnalyticsService.getKeywordAnalytics();

        return NextResponse.json({
          success: true,
          data: keywordAnalytics
        });

      case 'behavior':
        // التحقق من صلاحيات الإدارة
        if (sessionResult.data.type !== 'business') {
          return NextResponse.json(
            { success: false, error: 'غير مصرح لعرض تحليل السلوك' },
            { status: 403 }
          );
        }

        const behaviorAnalytics = AnalyticsService.getUserBehaviorAnalytics();

        return NextResponse.json({
          success: true,
          data: behaviorAnalytics
        });

      case 'financial':
        // التحقق من صلاحيات الإدارة
        if (sessionResult.data.type !== 'business') {
          return NextResponse.json(
            { success: false, error: 'غير مصرح لعرض التحليل المالي' },
            { status: 403 }
          );
        }

        const financialAnalytics = AnalyticsService.getFinancialAnalytics(period as 'month' | 'year');

        return NextResponse.json({
          success: true,
          data: financialAnalytics
        });

      case 'custom':
        const dateFrom = searchParams.get('dateFrom');
        const dateTo = searchParams.get('dateTo');
        const categories = searchParams.get('categories')?.split(',');
        const locations = searchParams.get('locations')?.split(',');
        const userTypes = searchParams.get('userTypes')?.split(',');

        if (!dateFrom || !dateTo) {
          return NextResponse.json(
            { success: false, error: 'تواريخ البداية والنهاية مطلوبة' },
            { status: 400 }
          );
        }

        const customReport = AnalyticsService.getCustomReport({
          dateFrom,
          dateTo,
          categories,
          locations,
          userTypes
        });

        return NextResponse.json({
          success: true,
          data: customReport
        });

      default:
        return NextResponse.json(
          { success: false, error: 'نوع التحليل غير صحيح' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('خطأ في جلب التحليلات:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
