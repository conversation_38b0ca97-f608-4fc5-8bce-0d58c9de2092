(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["chunks/[root of the server]__d50270._.js", {

"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[project]/src/middleware.ts [middleware] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "config": (()=>config),
    "middleware": (()=>middleware)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$api$2f$server$2e$js__$5b$middleware$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_import__("[project]/node_modules/next/dist/esm/api/server.js [middleware] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/esm/server/web/spec-extension/response.js [middleware] (ecmascript)");
;
// الصفحات المحمية التي تتطلب تسجيل دخول
const protectedPaths = [
    '/settings',
    '/profile',
    '/dashboard',
    '/add-ad',
    '/my-ads',
    '/favorites',
    '/messages',
    '/notifications',
    '/subscription',
    '/billing'
];
function middleware(request) {
    const { pathname } = request.nextUrl;
    console.log('🔍 Middleware: فحص المسار:', pathname);
    // التحقق من الصفحات المحمية للمستخدمين العاديين
    const isProtectedPath = protectedPaths.some((path)=>pathname.startsWith(path));
    if (isProtectedPath) {
        console.log('🛡️ Middleware: صفحة محمية تم اكتشافها');
        // التحقق من وجود token في الكوكيز
        const authToken = request.cookies.get('auth-token')?.value;
        console.log('🍪 Middleware: البحث عن token:', authToken ? 'موجود' : 'غير موجود');
        // إذا لم يكن هناك token، إعادة توجيه للصفحة الرئيسية
        if (!authToken) {
            console.log('❌ Middleware: غير مصرح، إعادة توجيه للصفحة الرئيسية');
            const url = request.nextUrl.clone();
            url.pathname = '/';
            url.searchParams.set('auth_required', 'true');
            url.searchParams.set('message', 'يجب تسجيل الدخول للوصول لهذه الصفحة');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$5d$__$28$ecmascript$29$__["NextResponse"].redirect(url);
        }
    }
    // التحقق من الصفحات الإدارية
    if (pathname.startsWith('/admin')) {
        console.log('🛡️ Middleware: صفحة إدارية تم اكتشافها');
        // السماح بصفحة تسجيل الدخول الإداري
        if (request.nextUrl.pathname === '/admin/login') {
            console.log('✅ Middleware: السماح بصفحة تسجيل الدخول');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$5d$__$28$ecmascript$29$__["NextResponse"].next();
        }
        // التحقق من وجود جلسة إدارية في الكوكيز
        const adminSession = request.cookies.get('admin-session');
        console.log('🍪 Middleware: البحث عن جلسة في الكوكيز:', adminSession ? 'موجودة' : 'غير موجودة');
        // مؤقت<|im_start|>: السماح بالدخول بدون تحقق للاختبار
        console.log('⚠️ Middleware: السماح المؤقت بالدخول للاختبار');
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$5d$__$28$ecmascript$29$__["NextResponse"].next();
    /*
    if (!adminSession) {
      console.log('❌ Middleware: لا توجد جلسة، إعادة توجيه لتسجيل الدخول');
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }

    // في التطبيق الحقيقي، ستتحقق من صحة التوكن هنا
    try {
      const sessionData = JSON.parse(adminSession.value);
      if (!sessionData.admin || !sessionData.token) {
        console.log('❌ Middleware: جلسة غير صالحة، إعادة توجيه');
        return NextResponse.redirect(new URL('/admin/login', request.url));
      }
      console.log('✅ Middleware: جلسة صالحة، السماح بالمرور');
    } catch (error) {
      console.log('❌ Middleware: خطأ في تحليل الجلسة، إعادة توجيه');
      return NextResponse.redirect(new URL('/admin/login', request.url));
    }
    */ }
    console.log('✅ Middleware: السماح بالمرور');
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$esm$2f$server$2f$web$2f$spec$2d$extension$2f$response$2e$js__$5b$middleware$5d$__$28$ecmascript$29$__["NextResponse"].next();
}
const config = {
    matcher: [
        '/admin/:path*',
        '/settings/:path*',
        '/profile/:path*',
        '/dashboard/:path*',
        '/add-ad/:path*',
        '/my-ads/:path*',
        '/favorites/:path*',
        '/messages/:path*',
        '/notifications/:path*',
        '/subscription/:path*',
        '/billing/:path*'
    ]
};
}}),
}]);

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__d50270._.js.map