'use client';

import { useState, useEffect } from 'react';

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  adData: {
    title: string;
    category: string;
    subcategory: string;
    price: string;
    currency: string;
    location: string;
    imageCount: number;
    contactPhone: string;
    adType: string;
  };
}

const SuccessModal = ({ isOpen, onClose, adData }: SuccessModalProps) => {
  const [showConfetti, setShowConfetti] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setShowConfetti(true);
      // إخفاء الكونفيتي بعد 3 ثوان
      setTimeout(() => setShowConfetti(false), 3000);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const getAdTypeInfo = () => {
    switch (adData.adType) {
      case 'free':
        return { icon: '🆓', name: 'مجاني', color: 'text-green-600', bgColor: 'bg-green-50' };
      case 'featured':
        return { icon: '⭐', name: 'مميز', color: 'text-blue-600', bgColor: 'bg-blue-50' };
      case 'premium':
        return { icon: '🏆', name: 'ذهبي', color: 'text-yellow-600', bgColor: 'bg-yellow-50' };
      default:
        return { icon: '📢', name: 'عادي', color: 'text-gray-600', bgColor: 'bg-gray-50' };
    }
  };

  const adTypeInfo = getAdTypeInfo();

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4 overflow-y-auto">
      {/* Confetti Animation */}
      {showConfetti && (
        <div className="fixed inset-0 pointer-events-none z-40">
          {[...Array(30)].map((_, i) => (
            <div
              key={i}
              className="absolute animate-bounce"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 2}s`,
                animationDuration: `${2 + Math.random() * 2}s`
              }}
            >
              {['🎉', '🎊', '✨', '🌟', '💫'][Math.floor(Math.random() * 5)]}
            </div>
          ))}
        </div>
      )}

      <div className="bg-white rounded-2xl shadow-2xl max-w-lg w-full mx-4 my-8 overflow-hidden transform transition-all duration-300 scale-100 max-h-[90vh] overflow-y-auto">
        {/* Header with gradient */}
        <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-6 text-white text-center relative overflow-hidden">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="text-5xl mb-3 animate-bounce">🎉</div>
            <h2 className="text-xl font-bold mb-2">تم نشر إعلانك بنجاح!</h2>
            <p className="text-green-100 text-sm">إعلانك الآن متاح للجميع</p>
          </div>

          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10"></div>
          <div className="absolute bottom-0 left-0 w-16 h-16 bg-white/10 rounded-full translate-y-8 -translate-x-8"></div>
        </div>

        {/* Content */}
        <div className="p-4 space-y-3">
          {/* Ad Type Badge */}
          <div className={`${adTypeInfo.bgColor} rounded-lg p-2 text-center`}>
            <div className="flex items-center justify-center gap-2">
              <span className="text-xl">{adTypeInfo.icon}</span>
              <span className={`font-bold text-sm ${adTypeInfo.color}`}>إعلان {adTypeInfo.name}</span>
            </div>
          </div>

          {/* Ad Details - Compact */}
          <div className="bg-gray-50 rounded-lg p-3">
            <h3 className="font-bold text-gray-800 text-center mb-2 text-sm">📋 تفاصيل الإعلان</h3>

            <div className="grid grid-cols-1 gap-1 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-600">📝 العنوان:</span>
                <span className="text-gray-800 font-medium truncate max-w-[200px]">{adData.title}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">💰 السعر:</span>
                <span className="text-gray-800 font-bold">{adData.price} {adData.currency}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">📍 الموقع:</span>
                <span className="text-gray-800">{adData.location}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-gray-600">📷 الصور:</span>
                <span className="text-gray-800">{adData.imageCount} صورة</span>
              </div>
            </div>
          </div>

          {/* Next Steps - Compact */}
          <div className="bg-blue-50 rounded-lg p-3">
            <h4 className="font-bold text-blue-800 mb-2 text-sm flex items-center gap-1">
              <span>⏰</span>
              الخطوات التالية
            </h4>
            <ul className="text-xs text-blue-700 space-y-1">
              <li>• سيظهر إعلانك خلال دقائق قليلة</li>
              <li>• تأكد من توفر هاتفك <span className="font-bold">{adData.contactPhone}</span></li>
              <li>• يمكنك إدارة الإعلان من حسابك</li>
            </ul>
          </div>

          {/* Thank you message - Compact */}
          <div className="text-center p-3 bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg">
            <div className="text-xl mb-1">🙏</div>
            <p className="text-purple-800 font-semibold text-sm">شكراً لاستخدامك منصة من المالك</p>
            <p className="text-purple-600 text-xs">نتمنى لك تجربة ناجحة!</p>
          </div>
        </div>

        {/* Footer Buttons - Compact */}
        <div className="p-4 bg-gray-50 flex gap-2">
          <button
            onClick={() => window.location.href = '/my-ads'}
            className="flex-1 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors font-medium text-xs"
          >
            📊 إعلاناتي
          </button>
          <button
            onClick={() => window.location.href = '/'}
            className="flex-1 bg-green-600 text-white py-2 px-3 rounded-lg hover:bg-green-700 transition-colors font-medium text-xs"
          >
            🏠 الرئيسية
          </button>
          <button
            onClick={onClose}
            className="flex-1 bg-gray-600 text-white py-2 px-3 rounded-lg hover:bg-gray-700 transition-colors font-medium text-xs"
          >
            ✕ إغلاق
          </button>
        </div>
      </div>
    </div>
  );
};

export default SuccessModal;
