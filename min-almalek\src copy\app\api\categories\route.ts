import { NextResponse } from 'next/server';
import { categories } from '@/lib/data';

export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      data: categories
    });
  } catch (error) {
    console.error('خطأ في جلب التصنيفات:', error);
    return NextResponse.json(
      { success: false, error: 'حدث خطأ في جلب التصنيفات' },
      { status: 500 }
    );
  }
}
