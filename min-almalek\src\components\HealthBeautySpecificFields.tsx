'use client';

import { useState } from 'react';

interface HealthBeautyData {
  category: string;
  subCategory: string;
  serviceType: string;
  targetGender: string;
  location: string;
  duration: string;
  experience: string;
  certification: string;
  homeService: boolean;
  description: string;
}

interface HealthBeautySpecificFieldsProps {
  data: HealthBeautyData;
  onChange: (data: HealthBeautyData) => void;
}

const healthBeautyCategories = {
  'personal-care': {
    name: 'العناية الشخصية',
    subCategories: {
      'hair': {
        name: 'الشعر',
        services: [
          'قص شعر رجالي / نسائي',
          'صبغات شعر (ألوان طبيعية – ألوان جريئة)',
          'جلسات بروتين / كيراتين / كولاجين للشعر',
          'فرد الشعر',
          'علاج تساقط الشعر',
          'تركيب وصلات شعر (Extensions)'
        ]
      },
      'makeup': {
        name: 'المكياج',
        services: [
          'مكياج يومي',
          'مكياج عرائس',
          'مكياج تصوير',
          'مكياج دائم (Microblading – تاتو حواجب، شفايف)'
        ]
      },
      'skincare': {
        name: 'البشرة',
        services: [
          'تنظيف بشرة عادي / عميق',
          'جلسات تقشير (كيميائي – كريستالي)',
          'جلسات إبر نضارة',
          'علاج حب الشباب',
          'إزالة البقع والتصبغات'
        ]
      },
      'nails': {
        name: 'الأظافر',
        services: [
          'مناكير وباديكير',
          'تركيب أظافر (أكريليك – جل)',
          'نقش ورسم على الأظافر',
          'علاج الأظافر المتشققة'
        ]
      },
      'hair-removal': {
        name: 'إزالة الشعر',
        services: [
          'إزالة بالشمع',
          'إزالة بالليزر',
          'إزالة بالخيط',
          'جلسات دائمة (IPL – ليزر ديود)'
        ]
      }
    }
  },
  'fitness-aesthetic': {
    name: 'اللياقة والتجميل الطبي',
    subCategories: {
      'fitness': {
        name: 'اللياقة',
        services: [
          'اشتراكات نوادي رياضية (رجال / نساء)',
          'مدربين شخصيين (Personal Trainer)',
          'حصص يوغا',
          'حصص بيلاتس',
          'حصص زومبا ورقص رياضي',
          'تدريب كمال أجسام',
          'برامج غذائية مع رياضة'
        ]
      },
      'medical-aesthetic': {
        name: 'التجميل الطبي',
        services: [
          'حقن بوتوكس',
          'حقن فيلر',
          'جلسات بلازما شعر وبشرة',
          'جلسات ميزوثيرابي',
          'تقشير كيميائي طبي',
          'عمليات تجميل (أنف – شد وجه – شفط دهون)',
          'زراعة الشعر',
          'إزالة الوشوم (Tattoos) بالليزر',
          'تبييض الأسنان بالليزر'
        ]
      }
    }
  },
  'medical-health': {
    name: 'الطب والصحة',
    subCategories: {
      'clinics': {
        name: 'العيادات',
        services: [
          'طب عام',
          'عيادات جلدية',
          'عيادات أسنان (حشوات، تقويم، زراعة، تبييض)',
          'عيادات عيون (فحوصات – عمليات ليزر)',
          'عيادات نسائية وتوليد',
          'أطباء تغذية',
          'عيادات أطفال'
        ]
      },
      'home-medical': {
        name: 'الخدمات الطبية المنزلية',
        services: [
          'تمريض منزلي',
          'حقن وأدوية بالمنزل',
          'رعاية مسنين',
          'جلسات فيزيائية (علاج طبيعي)',
          'متابعة مرضى مزمنين (سكري، ضغط)'
        ]
      }
    }
  },
  'body-care': {
    name: 'العناية بالجسم والاسترخاء',
    subCategories: {
      'massage': {
        name: 'المساج والاسترخاء',
        services: [
          'مساج استرخاء',
          'مساج علاجي (للظهر – العمود الفقري – الإصابات)',
          'مساج رياضي',
          'مساج بالحجارة الساخنة',
          'جلسات حمام تركي',
          'جلسات ساونا',
          'جلسات بخار أعشاب'
        ]
      },
      'slimming': {
        name: 'التنحيف وتشكيل الجسم',
        services: [
          'جلسات تنحيف بالحرارة',
          'جلسات تفتيت الدهون (Cavitation – Radio Frequency)'
        ]
      }
    }
  },
  'products': {
    name: 'منتجات الصحة والجمال',
    subCategories: {
      'cosmetics': {
        name: 'مستحضرات التجميل',
        services: [
          'مستحضرات تجميل (مكياج كامل)',
          'منتجات عناية بالشعر (شامبو – سيروم – زيوت)',
          'منتجات عناية بالبشرة (كريمات – سيرومات – واقي شمس)'
        ]
      },
      'devices': {
        name: 'أجهزة العناية',
        services: [
          'أجهزة عناية منزلية (ماكينات ليزر – أجهزة بخار للوجه)'
        ]
      },
      'supplements': {
        name: 'المكملات والأعشاب',
        services: [
          'مكملات غذائية (بروتين – كرياتين – فيتامينات)',
          'أعشاب طبيعية (للتنحيف – لتقوية الشعر – للبشرة)'
        ]
      }
    }
  }
};

const HealthBeautySpecificFields = ({ data, onChange }: HealthBeautySpecificFieldsProps) => {
  const [selectedCategory, setSelectedCategory] = useState(data?.category || '');
  const [selectedSubCategory, setSelectedSubCategory] = useState(data?.subCategory || '');

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    setSelectedSubCategory('');
    onChange({
      ...data,
      category,
      subCategory: '',
      serviceType: ''
    });
  };

  const handleSubCategoryChange = (subCategory: string) => {
    setSelectedSubCategory(subCategory);
    onChange({
      ...data,
      subCategory,
      serviceType: ''
    });
  };

  const handleChange = (field: keyof HealthBeautyData, value: string | boolean) => {
    onChange({
      category: '',
      subCategory: '',
      serviceType: '',
      targetGender: '',
      location: '',
      duration: '',
      experience: '',
      certification: '',
      homeService: false,
      description: '',
      ...data,
      [field]: value
    });
  };

  const currentCategory = selectedCategory ? healthBeautyCategories[selectedCategory as keyof typeof healthBeautyCategories] : null;
  const currentSubCategory = selectedSubCategory && currentCategory ? 
    currentCategory.subCategories[selectedSubCategory as keyof typeof currentCategory.subCategories] : null;

  return (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-pink-50 to-purple-50 p-6 rounded-xl border border-pink-200">
        <h3 className="text-lg font-semibold text-pink-800 mb-4">
          تفاصيل الصحة والجمال
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* الفئة الرئيسية */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الرئيسية</label>
            <select
              value={selectedCategory}
              onChange={(e) => handleCategoryChange(e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              <option value="">اختر الفئة</option>
              {Object.entries(healthBeautyCategories).map(([key, category]) => (
                <option key={key} value={key}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          {/* الفئة الفرعية */}
          {currentCategory && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الفئة الفرعية</label>
              <select
                value={selectedSubCategory}
                onChange={(e) => handleSubCategoryChange(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
              >
                <option value="">اختر الفئة الفرعية</option>
                {Object.entries(currentCategory.subCategories).map(([key, subCategory]) => (
                  <option key={key} value={key}>
                    {subCategory.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* نوع الخدمة */}
          {currentSubCategory && (
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">نوع الخدمة</label>
              <select
                value={data?.serviceType || ''}
                onChange={(e) => handleChange('serviceType', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
              >
                <option value="">اختر نوع الخدمة</option>
                {currentSubCategory.services.map((service, index) => (
                  <option key={index} value={service}>
                    {service}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* الجنس المستهدف */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الجنس المستهدف</label>
            <select
              value={data?.targetGender || ''}
              onChange={(e) => handleChange('targetGender', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              <option value="">اختر الجنس المستهدف</option>
              <option value="رجال">رجال</option>
              <option value="نساء">نساء</option>
              <option value="الجنسين">الجنسين</option>
            </select>
          </div>

          {/* مدة الجلسة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">مدة الجلسة</label>
            <select
              value={data?.duration || ''}
              onChange={(e) => handleChange('duration', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              <option value="">اختر المدة</option>
              <option value="30 دقيقة">30 دقيقة</option>
              <option value="45 دقيقة">45 دقيقة</option>
              <option value="60 دقيقة">60 دقيقة</option>
              <option value="90 دقيقة">90 دقيقة</option>
              <option value="120 دقيقة">120 دقيقة</option>
              <option value="أكثر من ساعتين">أكثر من ساعتين</option>
            </select>
          </div>

          {/* سنوات الخبرة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">سنوات الخبرة</label>
            <select
              value={data?.experience || ''}
              onChange={(e) => handleChange('experience', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
            >
              <option value="">اختر سنوات الخبرة</option>
              <option value="أقل من سنة">أقل من سنة</option>
              <option value="1-2 سنة">1-2 سنة</option>
              <option value="3-5 سنوات">3-5 سنوات</option>
              <option value="6-10 سنوات">6-10 سنوات</option>
              <option value="أكثر من 10 سنوات">أكثر من 10 سنوات</option>
            </select>
          </div>

          {/* الشهادات */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">الشهادات والمؤهلات</label>
            <input
              type="text"
              value={data?.certification || ''}
              onChange={(e) => handleChange('certification', e.target.value)}
              placeholder="مثال: شهادة في التجميل، دبلوم تمريض..."
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
            />
          </div>

          {/* خدمة منزلية */}
          <div className="md:col-span-2">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={data?.homeService || false}
                onChange={(e) => handleChange('homeService', e.target.checked)}
                className="w-4 h-4 text-pink-600 border-gray-300 rounded focus:ring-pink-500"
              />
              <span className="text-sm font-medium text-gray-700">خدمة منزلية متاحة</span>
            </label>
          </div>

          {/* وصف إضافي */}
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">وصف إضافي</label>
            <textarea
              value={data?.description || ''}
              onChange={(e) => handleChange('description', e.target.value)}
              placeholder="اكتب تفاصيل إضافية عن الخدمة..."
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default HealthBeautySpecificFields;
