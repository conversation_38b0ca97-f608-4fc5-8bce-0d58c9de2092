// ملف ثوابت الأسعار الموحدة - يجب استخدامه في جميع أنحاء الموقع

// الأسعار الموحدة للاشتراكات
export const UNIFIED_PRICING = {
  // خطط الأفراد
  INDIVIDUAL: {
    FREE: {
      id: 'individual-free',
      name: 'الخطة المجانية',
      price: 0,
      currency: 'ل.س',
      adsLimit: 3,
      duration: 30, // أيام
      features: [
        '3 إعلانات مجانية شهرياً',
        'صور حتى 3 لكل إعلان',
        'مدة الإعلان 30 يوم',
        'دعم فني أساسي',
        'إحصائيات بسيطة'
      ]
    },
    BASIC: {
      id: 'individual-basic',
      name: 'الباقة الأساسية',
      price: 25000,
      currency: 'ل.س',
      adsLimit: 5,
      duration: 30,
      features: [
        '5 إعلانات شهرياً',
        'عرض في النتائج العامة',
        'دعم فني أساسي',
        'إحصائيات بسيطة',
        'صور حتى 5 لكل إعلان',
        'مدة الإعلان 30 يوم'
      ]
    },
    PREMIUM: {
      id: 'individual-premium',
      name: 'الباقة المميزة',
      price: 50000,
      currency: 'ل.س',
      adsLimit: 15,
      duration: 30,
      popular: true,
      features: [
        '15 إعلان شهرياً',
        'إعلانات مميزة',
        'أولوية في النتائج',
        'دعم فني متقدم',
        'إحصائيات مفصلة',
        'شارة "معلن مميز"',
        'صور حتى 10 لكل إعلان',
        'مدة الإعلان 30 يوم'
      ]
    },
    BUSINESS: {
      id: 'individual-business',
      name: 'باقة الأعمال',
      price: 100000,
      currency: 'ل.س',
      adsLimit: 30,
      duration: 30,
      features: [
        '30 إعلان شهرياً',
        'إعلانات عاجلة',
        'أولوية قصوى',
        'دعم فني 24/7',
        'إحصائيات متقدمة',
        'صفحة معلن مخصصة',
        'إدارة متعددة المستخدمين',
        'صور حتى 15 لكل إعلان',
        'مدة الإعلان 30 يوم'
      ]
    }
  },

  // خطط الشركات
  BUSINESS: {
    STARTER: {
      id: 'business-starter',
      name: 'خطة البداية',
      price: 500000,
      currency: 'ل.س',
      adsLimit: 15,
      duration: 30,
      features: [
        '15 إعلان مميز شهرياً',
        'جميع الإعلانات عدا الوظائف',
        'صور حتى 15 لكل إعلان',
        'مدة الإعلان 30 يوم',
        'شارة "شركة موثقة"',
        'صفحة شركة أساسية',
        'إحصائيات مفصلة',
        'دعم فني للشركات',
        'أولوية في النتائج'
      ]
    },
    REAL_ESTATE: {
      id: 'real-estate-office',
      name: 'باقة المكاتب العقارية',
      price: 700000,
      currency: 'ل.س',
      adsLimit: 30,
      duration: 30,
      popular: true,
      features: [
        '30 إعلان عقاري مميز شهرياً',
        'إعلانات العقارات فقط',
        'صور حتى 20 لكل إعلان',
        'مدة الإعلان 30 يوم',
        'شارة "مكتب عقاري موثق" 🏘️',
        'صفحة مكتب عقاري متكاملة',
        'أدوات تقييم العقارات',
        'إحصائيات عقارية متقدمة',
        'تقارير السوق العقاري',
        'دعم فني متخصص للعقارات',
        'أولوية عالية في نتائج البحث'
      ]
    },
    PROFESSIONAL: {
      id: 'business-professional',
      name: 'الخطة المهنية',
      price: 1000000,
      currency: 'ل.س',
      adsLimit: 50,
      duration: 30,
      features: [
        '50 إعلان مميز شهرياً',
        'جميع الإعلانات عدا الوظائف',
        'صور حتى 20 لكل إعلان',
        'مدة الإعلان 30 يوم',
        'أولوية عالية في النتائج',
        'شارة "شركة مميزة"',
        'صفحة شركة متقدمة',
        'إحصائيات متقدمة',
        'دعم فني مخصص',
        'إمكانية الإعلانات العاجلة',
        'تقارير أسبوعية',
        'إدارة متعددة المستخدمين'
      ]
    }
  }
};

// دالة للحصول على جميع خطط الأفراد
export const getIndividualPlans = () => {
  return Object.values(UNIFIED_PRICING.INDIVIDUAL);
};

// دالة للحصول على جميع خطط الشركات
export const getBusinessPlans = () => {
  return Object.values(UNIFIED_PRICING.BUSINESS);
};

// دالة للحصول على جميع الخطط
export const getAllPlans = () => {
  return [...getIndividualPlans(), ...getBusinessPlans()];
};

// دالة للحصول على الخطط الشائعة
export const getPopularPlans = () => {
  return getAllPlans().filter(plan => plan.popular);
};

// دالة لتنسيق السعر
export const formatUnifiedPrice = (price: number | string, currency: string = 'ل.س'): string => {
  if (typeof price === 'string') {
    return price;
  }
  if (price === 0) {
    return 'مجاني';
  }
  return `${price.toLocaleString()} ${currency}`;
};

// دالة للحصول على خطة بالمعرف
export const getPlanById = (id: string) => {
  return getAllPlans().find(plan => plan.id === id);
};

// معلومات وسائل الدفع الموحدة
export const PAYMENT_METHODS = [
  {
    id: 'visa',
    name: 'Visa',
    nameAr: 'فيزا كارد',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/4/41/Visa_Logo.png',
    description: 'ادفع بأمان باستخدام بطاقة الفيزا',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200'
  },
  {
    id: 'mastercard',
    name: 'MasterCard',
    nameAr: 'ماستر كارد',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/0/04/Mastercard-logo.png',
    description: 'ادفع بأمان باستخدام بطاقة الماستر كارد',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200'
  },
  {
    id: 'paypal',
    name: 'PayPal',
    nameAr: 'باي بال',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg',
    description: 'ادفع بسهولة عبر حسابك في PayPal',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200'
  },
  {
    id: 'cashapp',
    name: 'Cash App',
    nameAr: 'تطبيق كاش',
    logo: null,
    description: 'ادفع بسرعة عبر تطبيق Cash App',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200'
  },
  {
    id: 'applepay',
    name: 'Apple Pay',
    nameAr: 'آبل باي',
    logo: 'https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg',
    description: 'ادفع بأمان عبر Apple Pay',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200'
  }
];

// معلومات الضمانات الموحدة
export const GUARANTEES = [
  'ضمان استرداد المال خلال 7 أيام',
  'دعم فني مجاني',
  'إمكانية تغيير الخطة في أي وقت',
  'بيانات آمنة ومحمية',
  'تشفير SSL للمعاملات'
];

// معلومات الأمان الموحدة
export const SECURITY_FEATURES = {
  security: 'دفع آمن 100%',
  support: 'دعم فني 24/7',
  flexibility: 'إمكانية الإلغاء في أي وقت',
  encryption: 'تشفير SSL للمعاملات',
  refund: 'ضمان استرداد المال'
};

// رسائل التسويق الموحدة
export const MARKETING_MESSAGES = {
  hero: 'اختر الباقة المناسبة لك واستمتع بمميزات حصرية',
  security: 'جميع المعاملات محمية بتشفير SSL',
  support: 'دعم فني متاح 24/7 لمساعدتك',
  flexibility: 'يمكنك تغيير أو إلغاء اشتراكك في أي وقت'
};

// تصدير كل شيء كوحدة واحدة
export default {
  UNIFIED_PRICING,
  PAYMENT_METHODS,
  GUARANTEES,
  SECURITY_FEATURES,
  MARKETING_MESSAGES,
  getIndividualPlans,
  getBusinessPlans,
  getAllPlans,
  getPopularPlans,
  formatUnifiedPrice,
  getPlanById
};
