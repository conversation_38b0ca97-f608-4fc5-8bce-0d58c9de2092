'use client';

import { useEffect } from 'react';
import Header from '@/components/Header';
import HeroSection from '@/components/HeroSection';
import CategoryGrid from '@/components/CategoryGrid';
import FeaturedAds from '@/components/FeaturedAds';
import LocationFilter from '@/components/LocationFilter';
import LatestAds from '@/components/LatestAds';
import SiteStats from '@/components/SiteStats';
import SiteReview from '@/components/SiteReview';
import Footer from '@/components/Footer';
import { useToast, ToastContainer } from '@/components/Toast';

export default function Home() {
  const { messages, removeToast, showSuccess } = useToast();

  // التحقق من رسائل النجاح المحفوظة
  useEffect(() => {
    const showLoginSuccess = localStorage.getItem('showLoginSuccess');
    if (showLoginSuccess === 'true') {
      showSuccess('تم تسجيل الدخول بنجاح', '🌟 أهلاً وسهلاً بك في منصة من المالك! نتمنى لك تجربة مميزة واستكشاف جميع الميزات الاحترافية المتاحة.');
      localStorage.removeItem('showLoginSuccess');
    }
  }, [showSuccess]);
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main>
        <HeroSection />
        <CategoryGrid />
        <FeaturedAds />
        <LocationFilter />
        <LatestAds />
        <SiteStats />
        <SiteReview />
      </main>
      <Footer />

      {/* نظام التوست */}
      <ToastContainer messages={messages} onRemove={removeToast} />
    </div>
  );
}
