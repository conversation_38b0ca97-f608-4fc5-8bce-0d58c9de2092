'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import AdCard from '@/components/AdCard';
import FilterModal from '@/components/FilterModal';
import CategoryIcon from '@/components/CategoryIcon';
import { Ad, DataService } from '@/lib/data';

export default function TourismPage() {
  const [ads, setAds] = useState<Ad[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortBy, setSortBy] = useState('newest');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false);

  // فلاتر البحث
  const [filters, setFilters] = useState({
    serviceType: '',
    accommodationType: '',
    transportType: '',
    tripType: '',
    priceFrom: '',
    priceTo: '',
    location: '',
    rating: '',
    duration: '',
    capacity: '',
    languages: [] as string[]
  });

  // قوائم الخيارات
  const serviceTypes = [
    { value: 'accommodation', label: 'الإقامة' },
    { value: 'transport', label: 'النقل والمواصلات' },
    { value: 'trips', label: 'الرحلات السياحية' },
    { value: 'services', label: 'الخدمات السياحية' },
    { value: 'food', label: 'الطعام والأنشطة' },
    { value: 'shopping', label: 'التسوق' },
    { value: 'events', label: 'الفعاليات والأنشطة' },
    { value: 'specialized', label: 'السياحة المتخصصة' }
  ];

  const accommodationTypes = [
    { value: 'hotel-5star', label: 'فنادق 5 نجوم' },
    { value: 'hotel-4star', label: 'فنادق 4 نجوم' },
    { value: 'hotel-3star', label: 'فنادق 3 نجوم' },
    { value: 'boutique-hotel', label: 'بوتيك هوتيل' },
    { value: 'hotel-apartments', label: 'شقق فندقية' },
    { value: 'chalets', label: 'شاليهات' },
    { value: 'rural-houses', label: 'بيوت ريفية' },
    { value: 'mountain-cabins', label: 'أكواخ جبلية' },
    { value: 'private-villas', label: 'فلل خاصة' },
    { value: 'beach-resorts', label: 'منتجعات شاطئية' },
    { value: 'guesthouses', label: 'بيوت ضيافة' },
    { value: 'camping', label: 'كامبنج' },
    { value: 'boats-yachts', label: 'قوارب ويخوت' }
  ];

  const transportTypes = [
    { value: 'car-rental', label: 'تأجير سيارات' },
    { value: 'vip-cars', label: 'سيارات فاخرة VIP' },
    { value: 'motorcycles', label: 'دراجات نارية' },
    { value: 'electric-bikes', label: 'دراجات كهربائية' },
    { value: 'scooters', label: 'سكوترات' },
    { value: 'tourist-buses', label: 'باصات سياحية' },
    { value: 'minibus', label: 'ميني باص' },
    { value: 'tourist-van', label: 'فان سياحي' },
    { value: 'flight-tickets', label: 'تذاكر طيران' },
    { value: 'train-tickets', label: 'تذاكر قطار' },
    { value: 'airport-services', label: 'خدمات المطار' },
    { value: 'marine-tours', label: 'جولات بحرية' }
  ];

  const tripTypes = [
    { value: 'domestic', label: 'رحلات داخلية' },
    { value: 'international', label: 'رحلات خارجية' },
    { value: 'day-trips', label: 'رحلات يومية' },
    { value: 'mountain', label: 'رحلات جبلية' },
    { value: 'safari', label: 'سفاري' },
    { value: 'camping', label: 'تخييم' },
    { value: 'marine', label: 'رحلات بحرية' },
    { value: 'diving', label: 'غوص وسنوركل' },
    { value: 'fishing', label: 'صيد' },
    { value: 'adventure', label: 'رحلات مغامرات' },
    { value: 'climbing', label: 'تسلق' },
    { value: 'parachute', label: 'باراشوت' },
    { value: 'cultural', label: 'رحلات ثقافية' },
    { value: 'historical', label: 'رحلات تاريخية' },
    { value: 'religious', label: 'رحلات دينية' }
  ];

  const ratingOptions = [
    { value: '5', label: '5 نجوم' },
    { value: '4', label: '4 نجوم فأكثر' },
    { value: '3', label: '3 نجوم فأكثر' },
    { value: '2', label: '2 نجوم فأكثر' },
    { value: '1', label: 'نجمة واحدة فأكثر' }
  ];

  const durationOptions = [
    { value: 'hourly', label: 'بالساعة' },
    { value: 'daily', label: 'يومي' },
    { value: 'weekly', label: 'أسبوعي' },
    { value: 'monthly', label: 'شهري' }
  ];

  const languageOptions = [
    { value: 'arabic', label: 'العربية' },
    { value: 'english', label: 'الإنجليزية' },
    { value: 'french', label: 'الفرنسية' },
    { value: 'german', label: 'الألمانية' },
    { value: 'russian', label: 'الروسية' },
    { value: 'turkish', label: 'التركية' }
  ];

  useEffect(() => {
    loadAds();
  }, [currentPage, sortBy, filters]);

  const loadAds = async () => {
    setLoading(true);

    // محاكاة استدعاء API مع الفلاتر
    const result = await DataService.getAds({
      ...filters,
      category: 'tourism',
      page: currentPage,
      limit: 12,
      sortBy: sortBy as any
    });

    setAds(result.ads);
    setTotalPages(result.totalPages);
    setLoading(false);
  };

  const handleFilterChange = (key: string, value: string | string[]) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const clearFilters = () => {
    setFilters({
      serviceType: '',
      accommodationType: '',
      transportType: '',
      tripType: '',
      priceFrom: '',
      priceTo: '',
      location: '',
      rating: '',
      duration: '',
      capacity: '',
      languages: []
    });
  };

  const stats = {
    totalAds: 1890,
    hotels: 456,
    trips: 623,
    transport: 234
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* العنوان والإحصائيات */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-12 flex items-center justify-center">
              <CategoryIcon
                category="tourism"
                className="w-10 h-10"
                color="#0ea5e9"
              />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-800">السياحة والسفر</h1>
              <p className="text-gray-600">اكتشف أفضل الخدمات السياحية والرحلات في سوريا</p>
            </div>
          </div>

          {/* الإحصائيات */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-white rounded-xl p-4 shadow-lg border border-gray-200">
              <div className="text-2xl font-bold text-sky-600">{stats.totalAds.toLocaleString()}</div>
              <div className="text-sm text-gray-600">إجمالي الإعلانات</div>
            </div>
            <div className="bg-white rounded-xl p-4 shadow-lg border border-gray-200">
              <div className="text-2xl font-bold text-sky-600">{stats.hotels.toLocaleString()}</div>
              <div className="text-sm text-gray-600">فنادق ومنتجعات</div>
            </div>
            <div className="bg-white rounded-xl p-4 shadow-lg border border-gray-200">
              <div className="text-2xl font-bold text-sky-600">{stats.trips.toLocaleString()}</div>
              <div className="text-sm text-gray-600">رحلات سياحية</div>
            </div>
            <div className="bg-white rounded-xl p-4 shadow-lg border border-gray-200">
              <div className="text-2xl font-bold text-sky-600">{stats.transport.toLocaleString()}</div>
              <div className="text-sm text-gray-600">خدمات النقل</div>
            </div>
          </div>
        </div>

        {/* زر فتح الفلاتر على الموبايل */}
        <div className="lg:hidden mb-6">
          <button
            onClick={() => setIsFilterModalOpen(true)}
            className="w-full bg-white rounded-xl shadow-lg p-4 flex items-center justify-center gap-3 border border-gray-200 hover:bg-gray-50 transition-colors"
          >
            <img
              src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
              alt="فلاتر"
              className="w-6 h-6 opacity-80"
              style={{
                filter: 'drop-shadow(0 0 4px rgba(14, 165, 233, 0.6))'
              }}
            />
            <span className="text-lg font-semibold text-gray-800">البحث والفلاتر</span>
          </button>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* فلاتر السياحة */}
          <div className="lg:w-1/4 hidden lg:block">
            <div className="bg-white rounded-xl shadow-lg p-6 sticky top-8 border border-gray-200">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                <img
                  src="/images/jobs/Jobs -Personals/icons/filter_list_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="فلاتر"
                  className="w-6 h-6 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(14, 165, 233, 0.6))'
                  }}
                />
                البحث والفلاتر
              </h2>
              {/* نوع الخدمة */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع الخدمة</label>
                <select
                  value={filters.serviceType}
                  onChange={(e) => handleFilterChange('serviceType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                >
                  <option value="">جميع الخدمات</option>
                  {serviceTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>

              {/* نوع الإقامة */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع الإقامة</label>
                <select
                  value={filters.accommodationType}
                  onChange={(e) => handleFilterChange('accommodationType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                >
                  <option value="">جميع أنواع الإقامة</option>
                  {accommodationTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>

              {/* نوع النقل */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع النقل</label>
                <select
                  value={filters.transportType}
                  onChange={(e) => handleFilterChange('transportType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                >
                  <option value="">جميع أنواع النقل</option>
                  {transportTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>

              {/* نوع الرحلة */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع الرحلة</label>
                <select
                  value={filters.tripType}
                  onChange={(e) => handleFilterChange('tripType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                >
                  <option value="">جميع أنواع الرحلات</option>
                  {tripTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>

              {/* نطاق السعر */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">نطاق السعر (دولار)</label>
                <div className="grid grid-cols-2 gap-2">
                  <input
                    type="number"
                    placeholder="من"
                    value={filters.priceFrom}
                    onChange={(e) => handleFilterChange('priceFrom', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                  />
                  <input
                    type="number"
                    placeholder="إلى"
                    value={filters.priceTo}
                    onChange={(e) => handleFilterChange('priceTo', e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                  />
                </div>
              </div>

              {/* الموقع */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
                <select
                  value={filters.location}
                  onChange={(e) => handleFilterChange('location', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                >
                  <option value="">جميع المواقع</option>
                  <option value="دمشق">دمشق</option>
                  <option value="حلب">حلب</option>
                  <option value="حمص">حمص</option>
                  <option value="حماة">حماة</option>
                  <option value="اللاذقية">اللاذقية</option>
                  <option value="طرطوس">طرطوس</option>
                  <option value="درعا">درعا</option>
                  <option value="السويداء">السويداء</option>
                  <option value="القنيطرة">القنيطرة</option>
                  <option value="الرقة">الرقة</option>
                  <option value="دير الزور">دير الزور</option>
                  <option value="الحسكة">الحسكة</option>
                  <option value="إدلب">إدلب</option>
                </select>
              </div>

              {/* التقييم */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">التقييم</label>
                <select
                  value={filters.rating}
                  onChange={(e) => handleFilterChange('rating', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                >
                  <option value="">جميع التقييمات</option>
                  {ratingOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>

              {/* المدة */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">المدة</label>
                <select
                  value={filters.duration}
                  onChange={(e) => handleFilterChange('duration', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                >
                  <option value="">جميع المدد</option>
                  {durationOptions.map(option => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>

              {/* السعة */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">عدد الأشخاص</label>
                <input
                  type="number"
                  placeholder="عدد الأشخاص"
                  value={filters.capacity}
                  onChange={(e) => handleFilterChange('capacity', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500"
                />
              </div>

              {/* اللغات */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">اللغات المتاحة</label>
                <div className="space-y-2">
                  {languageOptions.map(lang => (
                    <label key={lang.value} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={filters.languages.includes(lang.value)}
                        onChange={(e) => {
                          const newLanguages = e.target.checked
                            ? [...filters.languages, lang.value]
                            : filters.languages.filter(l => l !== lang.value);
                          handleFilterChange('languages', newLanguages);
                        }}
                        className="rounded border-gray-300 text-sky-600 focus:ring-sky-500"
                      />
                      <span className="mr-2 text-sm text-gray-700">{lang.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* أزرار التحكم */}
              <div className="flex gap-3">
                <button
                  onClick={clearFilters}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  مسح الفلاتر
                </button>
              </div>
            </div>
          </div>

          {/* المحتوى الرئيسي */}
          <div className="lg:w-3/4">
            {/* شريط التحكم */}
            <div className="bg-white rounded-xl shadow-lg p-4 mb-6 border border-gray-200">
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div className="text-sm text-gray-600">
                  عرض {ads.length} من أصل {stats.totalAds.toLocaleString()} إعلان
                </div>

                <div className="flex items-center gap-4">
                  {/* طريقة العرض */}
                  <div className="flex border border-gray-200 rounded-lg overflow-hidden">
                    <button
                      onClick={() => setViewMode('grid')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'grid'
                          ? 'bg-sky-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      🔲 شبكة
                    </button>
                    <button
                      onClick={() => setViewMode('list')}
                      className={`px-3 py-2 text-sm ${
                        viewMode === 'list'
                          ? 'bg-sky-600 text-white'
                          : 'bg-white text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      📋 قائمة
                    </button>
                  </div>

                  {/* الترتيب */}
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-sky-500"
                  >
                    <option value="newest">الأحدث</option>
                    <option value="oldest">الأقدم</option>
                    <option value="price_low">السعر: من الأقل للأعلى</option>
                    <option value="price_high">السعر: من الأعلى للأقل</option>
                    <option value="most_viewed">الأكثر مشاهدة</option>
                  </select>
                </div>
              </div>
            </div>

            {/* الإعلانات */}
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {[...Array(6)].map((_, index) => (
                  <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                    <div className="h-48 bg-gray-200"></div>
                    <div className="p-4">
                      <div className="h-4 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : ads.length > 0 ? (
              <>
                <div className={
                  viewMode === 'grid'
                    ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                    : 'space-y-4'
                }>
                  {ads.map((ad) => (
                    <AdCard
                      key={ad.id}
                      ad={ad}
                      viewMode={viewMode}
                    />
                  ))}
                </div>

                {/* الصفحات */}
                {totalPages > 1 && (
                  <div className="flex justify-center mt-8">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                        disabled={currentPage === 1}
                        className="px-3 py-2 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        السابق
                      </button>

                      {[...Array(totalPages)].map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentPage(index + 1)}
                          className={`px-3 py-2 border rounded-lg text-sm ${
                            currentPage === index + 1
                              ? 'bg-sky-600 text-white border-sky-600'
                              : 'border-gray-200 hover:bg-gray-50'
                          }`}
                        >
                          {index + 1}
                        </button>
                      ))}

                      <button
                        onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                        disabled={currentPage === totalPages}
                        className="px-3 py-2 border border-gray-200 rounded-lg text-sm hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        التالي
                      </button>
                    </div>
                  </div>
                )}
              </>
            ) : (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-xl font-semibold text-gray-800 mb-2">لا توجد نتائج</h3>
                <p className="text-gray-600 mb-4">لم نجد أي إعلانات تطابق معايير البحث</p>
                <button
                  onClick={clearFilters}
                  className="px-6 py-2 bg-sky-600 text-white rounded-lg hover:bg-sky-700 transition-colors"
                >
                  مسح الفلاتر
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Modal الفلاتر للموبايل */}
        <FilterModal
          isOpen={isFilterModalOpen}
          onClose={() => setIsFilterModalOpen(false)}
          filters={filters}
          onFilterChange={handleFilterChange}
          onClearFilters={clearFilters}
          category="tourism"
          serviceTypes={serviceTypes}
          accommodationTypes={accommodationTypes}
          transportTypes={transportTypes}
          tripTypes={tripTypes}
          ratingOptions={ratingOptions}
          durationOptions={durationOptions}
          languageOptions={languageOptions}
        />
      </main>

    </div>
  );
}
