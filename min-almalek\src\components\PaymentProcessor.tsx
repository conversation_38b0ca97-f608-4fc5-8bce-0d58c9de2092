'use client';

import { useState } from 'react';
import CashAppLogo from './CashAppLogo';

interface PaymentProcessorProps {
  plan: {
    id: string;
    name: string;
    price: number;
    currency: string;
  };
  paymentMethod: {
    id: string;
    name: string;
    nameAr: string;
  };
  onSuccess: () => void;
  onCancel: () => void;
}

export default function PaymentProcessor({ plan, paymentMethod, onSuccess, onCancel }: PaymentProcessorProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [formData, setFormData] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    email: '',
    phone: ''
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCardNumber(e.target.value);
    handleInputChange('cardNumber', formatted);
  };

  const handleExpiryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatExpiryDate(e.target.value);
    handleInputChange('expiryDate', formatted);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsProcessing(true);

    // محاكاة معالجة الدفع
    try {
      await new Promise(resolve => setTimeout(resolve, 3000));

      // هنا سيتم إرسال البيانات إلى معالج الدفع الحقيقي
      console.log('Payment data:', {
        plan,
        paymentMethod,
        formData
      });

      onSuccess();
    } catch (error) {
      console.error('Payment failed:', error);
      alert('فشل في معالجة الدفع. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsProcessing(false);
    }
  };

  const getCardIcon = () => {
    const cardNumber = formData.cardNumber.replace(/\s/g, '');
    if (cardNumber.startsWith('4')) return '💳'; // Visa
    if (cardNumber.startsWith('5')) return '💳'; // Mastercard
    return '💳';
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-primary-600 to-primary-700 text-white p-6">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">💳 إتمام عملية الدفع</h2>
            <div className="bg-white/20 rounded-lg p-4 mt-4">
              <div className="text-lg font-semibold">{plan.name}</div>
              <div className="text-2xl font-bold">
                {plan.price.toLocaleString()} {plan.currency}
              </div>
              <div className="text-sm opacity-90">عبر {paymentMethod.nameAr}</div>
            </div>
          </div>
        </div>

        <div className="p-8">
          {/* أمان الدفع */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-3">
              <span className="text-2xl">🔒</span>
              <div>
                <h3 className="font-semibold text-green-800">دفع آمن ومحمي</h3>
                <p className="text-sm text-green-600">
                  جميع المعاملات محمية بتشفير SSL 256-bit
                </p>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* نموذج البطاقة الائتمانية */}
            {(paymentMethod.id === 'visa' || paymentMethod.id === 'mastercard') && (
              <>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    رقم البطاقة *
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={formData.cardNumber}
                      onChange={handleCardNumberChange}
                      placeholder="1234 5678 9012 3456"
                      maxLength={19}
                      className="w-full px-4 py-3 pl-12 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-xl">
                      {getCardIcon()}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      تاريخ الانتهاء *
                    </label>
                    <input
                      type="text"
                      value={formData.expiryDate}
                      onChange={handleExpiryChange}
                      placeholder="MM/YY"
                      maxLength={5}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      CVV *
                    </label>
                    <input
                      type="text"
                      value={formData.cvv}
                      onChange={(e) => handleInputChange('cvv', e.target.value.replace(/\D/g, '').slice(0, 4))}
                      placeholder="123"
                      maxLength={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    اسم حامل البطاقة *
                  </label>
                  <input
                    type="text"
                    value={formData.cardholderName}
                    onChange={(e) => handleInputChange('cardholderName', e.target.value)}
                    placeholder="الاسم كما هو مكتوب على البطاقة"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    required
                  />
                </div>
              </>
            )}

            {/* معلومات الاتصال */}
            <div className="border-t pt-6">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">معلومات الاتصال</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    البريد الإلكتروني *
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    رقم الهاتف *
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="+963 9XX XXX XXX"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                    required
                  />
                </div>
              </div>
            </div>

            {/* PayPal */}
            {paymentMethod.id === 'paypal' && (
              <div className="text-center py-8">
                <div className="text-6xl mb-4">🅿️</div>
                <h3 className="text-xl font-semibold mb-4">الدفع عبر PayPal</h3>
                <p className="text-gray-600 mb-6">
                  سيتم معالجة الدفع عبر PayPal بأمان تام
                </p>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <p className="text-sm text-blue-700">
                    💡 تأكد من تسجيل الدخول إلى حسابك في PayPal قبل المتابعة
                  </p>
                </div>
              </div>
            )}

            {/* Cash App */}
            {paymentMethod.id === 'cashapp' && (
              <div className="text-center py-8">
                <div className="flex justify-center mb-4">
                  <CashAppLogo size="lg" className="w-24 h-24" />
                </div>
                <h3 className="text-xl font-semibold mb-4">الدفع عبر Cash App</h3>
                <p className="text-gray-600 mb-6">
                  سيتم معالجة الدفع عبر Cash App بأمان
                </p>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <p className="text-sm text-green-700">
                    💡 تأكد من تثبيت تطبيق Cash App على جهازك
                  </p>
                </div>
              </div>
            )}

            {/* Apple Pay */}
            {paymentMethod.id === 'applepay' && (
              <div className="text-center py-8">
                <div className="text-6xl mb-4">🍎</div>
                <h3 className="text-xl font-semibold mb-4">الدفع عبر Apple Pay</h3>
                <p className="text-gray-600 mb-6">
                  سيتم معالجة الدفع عبر Apple Pay بأمان تام
                </p>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <p className="text-sm text-gray-700">
                    💡 تأكد من تفعيل Apple Pay على جهازك وإضافة بطاقة دفع
                  </p>
                </div>
              </div>
            )}

            {/* أزرار العمل */}
            <div className="flex gap-4 pt-6">
              <button
                type="button"
                onClick={onCancel}
                className="flex-1 bg-gray-200 text-gray-700 py-3 rounded-lg font-semibold hover:bg-gray-300 transition-colors"
                disabled={isProcessing}
              >
                إلغاء
              </button>
              <button
                type="submit"
                className="flex-1 bg-primary-600 text-white py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    جاري المعالجة...
                  </div>
                ) : (
                  `💳 ادفع ${plan.price.toLocaleString()} ${plan.currency}`
                )}
              </button>
            </div>
          </form>

          {/* معلومات إضافية */}
          <div className="mt-8 pt-6 border-t">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center text-sm text-gray-600">
              <div>
                <span className="text-lg">🔒</span>
                <p>دفع آمن 100%</p>
              </div>
              <div>
                <span className="text-lg">💳</span>
                <p>جميع البطاقات مقبولة</p>
              </div>
              <div>
                <span className="text-lg">📞</span>
                <p>دعم فني 24/7</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
