'use client';

import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';
import Header from '@/components/Header';
import SearchResults from '@/components/SearchResults';
import Footer from '@/components/Footer';
import { SearchFilters } from '@/lib/data';

function SearchContent() {
  const searchParams = useSearchParams();

  // استخراج المعاملات من URL
  const initialFilters: SearchFilters = {
    keyword: searchParams.get('q') || '',
    category: searchParams.get('category') || '',
    governorate: searchParams.get('location') || '',
    priceMin: searchParams.get('priceMin') ? Number(searchParams.get('priceMin')) : undefined,
    priceMax: searchParams.get('priceMax') ? Number(searchParams.get('priceMax')) : undefined,
    currency: (searchParams.get('currency') as 'SYP' | 'USD') || undefined,
    condition: (searchParams.get('condition') as 'new' | 'used' | 'refurbished') || undefined,
    sellerType: (searchParams.get('sellerType') as 'individual' | 'business' | 'real-estate-office') || undefined,
    verified: searchParams.get('verified') === 'true',
    featured: searchParams.get('featured') === 'true',
    hasImages: searchParams.get('hasImages') === 'true',
    datePosted: (searchParams.get('datePosted') as 'today' | 'week' | 'month') || undefined,
    sortBy: (searchParams.get('sortBy') as 'newest' | 'oldest' | 'price_low' | 'price_high' | 'popular') || 'newest'
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main>
        <SearchResults initialFilters={initialFilters} />
      </main>
      <Footer />
    </div>
  );
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin w-8 h-8 border-4 border-primary-600 border-t-transparent rounded-full"></div>
          </div>
        </main>
        <Footer />
      </div>
    }>
      <SearchContent />
    </Suspense>
  );
}
