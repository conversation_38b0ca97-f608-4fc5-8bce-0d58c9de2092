'use client';

import { useState } from 'react';
import { JobPosting, JOB_CATEGORIES, EXPERIENCE_LEVELS, WORK_TYPES, WORK_MODELS, EDUCATION_LEVELS } from '@/lib/jobs';

interface JobPostingFormProps {
  onSubmit: (job: Partial<JobPosting>) => void;
  onCancel: () => void;
  initialData?: Partial<JobPosting>;
}

const JobPostingForm = ({ onSubmit, onCancel, initialData }: JobPostingFormProps) => {
  const [formData, setFormData] = useState<Partial<JobPosting>>({
    title: '',
    department: '',
    location: '',
    workType: 'دوام كامل',
    workModel: 'حضوري',
    salaryRange: {
      min: 0,
      max: 0,
      currency: 'ل.س',
      period: 'شهري'
    },
    description: '',
    requirements: [''],
    responsibilities: [''],
    benefits: [''],
    skills: [''],
    experienceLevel: 'متوسط',
    experienceYears: { min: 0, max: 5 },
    educationLevel: 'بكالوريوس',
    applicationDeadline: '',
    category: '',
    subcategory: '',
    featured: false,
    urgent: false,
    ...initialData
  });

  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    { title: 'معلومات أساسية', icon: '📝' },
    { title: 'المتطلبات والمهارات', icon: '🎯' },
    { title: 'المسؤوليات والمزايا', icon: '💼' },
    { title: 'التفاصيل النهائية', icon: '✅' }
  ];

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateSalaryRange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      salaryRange: {
        ...prev.salaryRange!,
        [field]: value
      }
    }));
  };

  const updateExperienceYears = (field: string, value: number) => {
    setFormData(prev => ({
      ...prev,
      experienceYears: {
        ...prev.experienceYears!,
        [field]: value
      }
    }));
  };

  const addListItem = (field: string) => {
    const currentList = formData[field as keyof JobPosting] as string[] || [];
    updateFormData(field, [...currentList, '']);
  };

  const updateListItem = (field: string, index: number, value: string) => {
    const currentList = formData[field as keyof JobPosting] as string[] || [];
    const newList = [...currentList];
    newList[index] = value;
    updateFormData(field, newList);
  };

  const removeListItem = (field: string, index: number) => {
    const currentList = formData[field as keyof JobPosting] as string[] || [];
    const newList = currentList.filter((_, i) => i !== index);
    updateFormData(field, newList);
  };

  const selectedCategory = JOB_CATEGORIES.find(cat => cat.id === formData.category);

  const cities = [
    'دمشق', 'حلب', 'حمص', 'حماة', 'اللاذقية', 'طرطوس',
    'درعا', 'السويداء', 'القنيطرة', 'دير الزور', 'الرقة', 'الحسكة'
  ];

  const handleSubmit = () => {
    // تنظيف البيانات
    const cleanedData = {
      ...formData,
      requirements: formData.requirements?.filter(req => req.trim() !== '') || [],
      responsibilities: formData.responsibilities?.filter(resp => resp.trim() !== '') || [],
      benefits: formData.benefits?.filter(benefit => benefit.trim() !== '') || [],
      skills: formData.skills?.filter(skill => skill.trim() !== '') || []
    };

    onSubmit(cleanedData);
  };

  const renderBasicInfo = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800 mb-4">المعلومات الأساسية</h3>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">عنوان الوظيفة *</label>
        <input
          type="text"
          value={formData.title || ''}
          onChange={(e) => updateFormData('title', e.target.value)}
          placeholder="مثال: مطور React.js متقدم"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">القسم/الإدارة</label>
          <input
            type="text"
            value={formData.department || ''}
            onChange={(e) => updateFormData('department', e.target.value)}
            placeholder="مثال: تطوير البرمجيات"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">الموقع *</label>
          <select
            value={formData.location || ''}
            onChange={(e) => updateFormData('location', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          >
            <option value="">اختر المدينة أو الحي</option>
            {cities.map(city => (
              <option key={city} value={city}>{city}</option>
            ))}
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">نوع العمل *</label>
          <select
            value={formData.workType || ''}
            onChange={(e) => updateFormData('workType', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          >
            {WORK_TYPES.map(type => (
              <option key={type.id} value={type.name}>
                {type.icon} {type.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">نموذج العمل *</label>
          <select
            value={formData.workModel || ''}
            onChange={(e) => updateFormData('workModel', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          >
            {WORK_MODELS.map(model => (
              <option key={model.id} value={model.name}>
                {model.icon} {model.name}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">المجال *</label>
          <select
            value={formData.category || ''}
            onChange={(e) => {
              updateFormData('category', e.target.value);
              updateFormData('subcategory', '');
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          >
            <option value="">اختر المجال</option>
            {JOB_CATEGORIES.map(category => (
              <option key={category.id} value={category.id}>
                {category.icon} {category.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {selectedCategory && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">التخصص</label>
          <select
            value={formData.subcategory || ''}
            onChange={(e) => updateFormData('subcategory', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">اختر التخصص</option>
            {selectedCategory.subcategories.map(sub => (
              <option key={sub} value={sub}>{sub}</option>
            ))}
          </select>
        </div>
      )}

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">نطاق الراتب *</label>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div>
            <input
              type="number"
              placeholder="الحد الأدنى"
              value={formData.salaryRange?.min || ''}
              onChange={(e) => updateSalaryRange('min', parseInt(e.target.value) || 0)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          <div>
            <input
              type="number"
              placeholder="الحد الأقصى"
              value={formData.salaryRange?.max || ''}
              onChange={(e) => updateSalaryRange('max', parseInt(e.target.value) || 0)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          <div>
            <select
              value={formData.salaryRange?.currency || 'ل.س'}
              onChange={(e) => updateSalaryRange('currency', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="ل.س">ل.س</option>
              <option value="$">دولار</option>
              <option value="€">يورو</option>
            </select>
          </div>
          <div>
            <select
              value={formData.salaryRange?.period || 'شهري'}
              onChange={(e) => updateSalaryRange('period', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              <option value="شهري">شهري</option>
              <option value="سنوي">سنوي</option>
              <option value="يومي">يومي</option>
              <option value="ساعي">ساعي</option>
            </select>
          </div>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">وصف الوظيفة *</label>
        <textarea
          value={formData.description || ''}
          onChange={(e) => updateFormData('description', e.target.value)}
          rows={6}
          placeholder="اكتب وصفاً مفصلاً عن الوظيفة والشركة..."
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          required
        />
      </div>
    </div>
  );

  const renderRequirementsSkills = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800 mb-4">المتطلبات والمهارات</h3>

      {/* Experience Level */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">مستوى الخبرة المطلوب *</label>
          <select
            value={formData.experienceLevel || ''}
            onChange={(e) => updateFormData('experienceLevel', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          >
            {EXPERIENCE_LEVELS.map(level => (
              <option key={level.id} value={level.name}>
                {level.name} ({level.years})
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">المستوى التعليمي المطلوب *</label>
          <select
            value={formData.educationLevel || ''}
            onChange={(e) => updateFormData('educationLevel', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          >
            {EDUCATION_LEVELS.map(level => (
              <option key={level.id} value={level.name}>{level.name}</option>
            ))}
          </select>
        </div>
      </div>

      {/* Experience Years Range */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">نطاق سنوات الخبرة</label>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <input
              type="number"
              placeholder="الحد الأدنى"
              value={formData.experienceYears?.min || ''}
              onChange={(e) => updateExperienceYears('min', parseInt(e.target.value) || 0)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
          <div>
            <input
              type="number"
              placeholder="الحد الأقصى"
              value={formData.experienceYears?.max || ''}
              onChange={(e) => updateExperienceYears('max', parseInt(e.target.value) || 0)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>
        </div>
      </div>

      {/* Requirements */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <label className="block text-sm font-medium text-gray-700">المتطلبات الأساسية *</label>
          <button
            type="button"
            onClick={() => addListItem('requirements')}
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            + إضافة متطلب
          </button>
        </div>
        <div className="space-y-3">
          {(formData.requirements || []).map((req, index) => (
            <div key={index} className="flex gap-3">
              <input
                type="text"
                value={req}
                onChange={(e) => updateListItem('requirements', index, e.target.value)}
                placeholder={`المتطلب ${index + 1}`}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
              <button
                type="button"
                onClick={() => removeListItem('requirements', index)}
                className="text-red-600 hover:text-red-800 px-3 py-2"
              >
                🗑️
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Skills */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <label className="block text-sm font-medium text-gray-700">المهارات المطلوبة *</label>
          <button
            type="button"
            onClick={() => addListItem('skills')}
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            + إضافة مهارة
          </button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {(formData.skills || []).map((skill, index) => (
            <div key={index} className="flex gap-3">
              <input
                type="text"
                value={skill}
                onChange={(e) => updateListItem('skills', index, e.target.value)}
                placeholder={`المهارة ${index + 1}`}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
              <button
                type="button"
                onClick={() => removeListItem('skills', index)}
                className="text-red-600 hover:text-red-800 px-3 py-2"
              >
                🗑️
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderResponsibilitiesBenefits = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800 mb-4">المسؤوليات والمزايا</h3>

      {/* Responsibilities */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <label className="block text-sm font-medium text-gray-700">المسؤوليات الوظيفية *</label>
          <button
            type="button"
            onClick={() => addListItem('responsibilities')}
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            + إضافة مسؤولية
          </button>
        </div>
        <div className="space-y-3">
          {(formData.responsibilities || []).map((resp, index) => (
            <div key={index} className="flex gap-3">
              <textarea
                value={resp}
                onChange={(e) => updateListItem('responsibilities', index, e.target.value)}
                placeholder={`المسؤولية ${index + 1}`}
                rows={2}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
              <button
                type="button"
                onClick={() => removeListItem('responsibilities', index)}
                className="text-red-600 hover:text-red-800 px-3 py-2"
              >
                🗑️
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Benefits */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <label className="block text-sm font-medium text-gray-700">المزايا والحوافز</label>
          <button
            type="button"
            onClick={() => addListItem('benefits')}
            className="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            + إضافة ميزة
          </button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {(formData.benefits || []).map((benefit, index) => (
            <div key={index} className="flex gap-3">
              <input
                type="text"
                value={benefit}
                onChange={(e) => updateListItem('benefits', index, e.target.value)}
                placeholder={`الميزة ${index + 1}`}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
              <button
                type="button"
                onClick={() => removeListItem('benefits', index)}
                className="text-red-600 hover:text-red-800 px-3 py-2"
              >
                🗑️
              </button>
            </div>
          ))}
        </div>

        {/* Common Benefits Suggestions */}
        <div className="mt-4">
          <p className="text-sm text-gray-600 mb-2">اقتراحات شائعة:</p>
          <div className="flex flex-wrap gap-2">
            {['تأمين صحي', 'تأمين اجتماعي', 'مكافآت سنوية', 'إجازة سنوية', 'تدريب وتطوير', 'عمل مرن', 'مواصلات'].map(suggestion => (
              <button
                key={suggestion}
                type="button"
                onClick={() => {
                  const currentBenefits = formData.benefits || [];
                  if (!currentBenefits.includes(suggestion)) {
                    updateFormData('benefits', [...currentBenefits, suggestion]);
                  }
                }}
                className="text-xs bg-gray-100 text-gray-700 px-3 py-1 rounded-full hover:bg-gray-200 transition-colors"
              >
                + {suggestion}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderFinalDetails = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800 mb-4">التفاصيل النهائية</h3>

      {/* Application Deadline */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">آخر موعد للتقديم *</label>
        <input
          type="date"
          value={formData.applicationDeadline || ''}
          onChange={(e) => updateFormData('applicationDeadline', e.target.value)}
          min={new Date().toISOString().split('T')[0]}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          required
        />
      </div>

      {/* Job Promotion Options */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="text-lg font-semibold text-gray-800 mb-4">خيارات ترقية الإعلان</h4>

        <div className="space-y-4">
          <label className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-white transition-colors">
            <input
              type="checkbox"
              checked={formData.featured || false}
              onChange={(e) => updateFormData('featured', e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <div className="flex-1">
              <div className="font-medium text-gray-800">إعلان مميز ⭐</div>
              <div className="text-sm text-gray-600">يظهر في أعلى نتائج البحث (+25,000 ل.س)</div>
            </div>
          </label>

          <label className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-white transition-colors">
            <input
              type="checkbox"
              checked={formData.urgent || false}
              onChange={(e) => updateFormData('urgent', e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <div className="flex-1">
              <div className="font-medium text-gray-800">إعلان عاجل 🔥</div>
              <div className="text-sm text-gray-600">يحصل على أولوية في العرض (+15,000 ل.س)</div>
            </div>
          </label>
        </div>
      </div>

      {/* Preview */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h4 className="text-lg font-semibold text-gray-800 mb-4">معاينة الإعلان</h4>

        <div className="space-y-4">
          <div>
            <h5 className="font-semibold text-gray-800">{formData.title || 'عنوان الوظيفة'}</h5>
            <p className="text-gray-600">{formData.department || 'القسم'} • {formData.location || 'الموقع'}</p>
          </div>

          <div className="flex items-center gap-4 text-sm">
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">{formData.workType || 'نوع العمل'}</span>
            <span className="bg-green-100 text-green-800 px-2 py-1 rounded">{formData.workModel || 'نموذج العمل'}</span>
            <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">{formData.experienceLevel || 'مستوى الخبرة'}</span>
          </div>

          <div className="text-lg font-bold text-primary-600">
            {formData.salaryRange?.min && formData.salaryRange?.max
              ? `${formData.salaryRange.min.toLocaleString()} - ${formData.salaryRange.max.toLocaleString()} ${formData.salaryRange.currency} ${formData.salaryRange.period}`
              : 'نطاق الراتب'
            }
          </div>

          <p className="text-gray-700 text-sm">
            {formData.description || 'وصف الوظيفة سيظهر هنا...'}
          </p>

          {formData.skills && formData.skills.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {formData.skills.filter(skill => skill.trim()).slice(0, 5).map((skill, index) => (
                <span key={index} className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                  {skill}
                </span>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Terms and Conditions */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <input
            type="checkbox"
            id="terms"
            required
            className="mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
          />
          <label htmlFor="terms" className="text-sm text-gray-700">
            أوافق على <a href="/terms" className="text-primary-600 hover:underline">شروط الاستخدام</a> و
            <a href="/privacy" className="text-primary-600 hover:underline"> سياسة الخصوصية</a>.
            أؤكد أن جميع المعلومات المقدمة صحيحة ودقيقة.
          </label>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-lg">
      {/* Progress Steps */}
      <div className="border-b border-gray-200 p-4 md:p-6">
        {/* Mobile Progress */}
        <div className="md:hidden">
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium text-gray-600">
              الخطوة {currentStep + 1} من {steps.length}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(((currentStep + 1) / steps.length) * 100)}%
            </span>
          </div>

          <div className="w-full bg-gradient-to-r from-green-200 via-transparent to-green-200 rounded-full h-2 mb-4">
            <div
              className="bg-green-500 h-2 rounded-full transition-all duration-300 shadow-[0_0_8px_2px_rgba(34,197,94,0.5)]"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            ></div>
          </div>

          <div className="text-center">
            <div
              className="text-3xl mb-2 inline-flex items-center justify-center rounded-full bg-white/30 backdrop-blur-lg border border-green-300 shadow-[0_0_12px_4px_rgba(34,197,94,0.3)] transition-all duration-200"
              style={{
                boxShadow: currentStep === steps.length - 1
                  ? '0 0 16px 6px rgba(34,197,94,0.7), 0 0 0 4px rgba(255,255,255,0.2)'
                  : '0 0 8px 2px rgba(34,197,94,0.5)',
                border: '2px solid rgba(34,197,94,0.3)',
                background: 'linear-gradient(135deg, rgba(255,255,255,0.25) 60%, rgba(34,197,94,0.08) 100%)',
                color: '#22c55e',
                textShadow: '0 0 8px #22c55e, 0 0 2px #fff',
              }}
            >
              {steps[currentStep].icon}
            </div>
            <div className="text-lg font-semibold text-green-700">
              {steps[currentStep].title}
            </div>
          </div>
        </div>

        {/* Desktop Progress */}
        <div className="hidden md:block">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div key={index} className={`flex items-center ${index < steps.length - 1 ? 'flex-1' : ''}`}>
                <div className="flex flex-col items-center">
                  <div
                    className={`w-14 h-14 rounded-full flex items-center justify-center text-2xl font-bold mb-2 transition-all duration-200 shadow-lg border-2 ${
                      currentStep >= index
                        ? 'bg-white/30 border-green-400 shadow-[0_0_16px_6px_rgba(34,197,94,0.5)] text-green-600'
                        : 'bg-gray-100 border-gray-300 text-gray-400 shadow-none'
                    }`}
                    style={{
                      boxShadow: currentStep === index
                        ? '0 0 16px 6px rgba(34,197,94,0.7), 0 0 0 4px rgba(255,255,255,0.2)'
                        : currentStep > index
                          ? '0 0 8px 2px rgba(34,197,94,0.5)'
                          : 'none',
                      background: currentStep >= index
                        ? 'linear-gradient(135deg, rgba(255,255,255,0.25) 60%, rgba(34,197,94,0.08) 100%)'
                        : undefined,
                      color: currentStep >= index ? '#22c55e' : undefined,
                      textShadow: currentStep >= index ? '0 0 8px #22c55e, 0 0 2px #fff' : undefined,
                    }}
                  >
                    {step.icon}
                  </div>
                  <div className="text-center max-w-24">
                    <div className={`text-xs font-medium leading-tight ${
                      currentStep >= index ? 'text-green-700' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </div>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`flex-1 h-0.5 mx-2 mt-6 ${
                    currentStep > index ? 'bg-green-400 shadow-[0_0_8px_2px_rgba(34,197,94,0.3)]' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Step Content */}
      <div className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          {currentStep === 0 && renderBasicInfo()}
          {currentStep === 1 && renderRequirementsSkills()}
          {currentStep === 2 && renderResponsibilitiesBenefits()}
          {currentStep === 3 && renderFinalDetails()}
        </div>
      </div>

      {/* Navigation */}
      <div className="border-t border-gray-200 p-4 md:p-6">
        <div className="flex flex-col sm:flex-row justify-between gap-4">
          <button
            onClick={() => currentStep > 0 ? setCurrentStep(currentStep - 1) : onCancel()}
            className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors flex items-center justify-center gap-2"
          >
            {currentStep === 0 ? (
              <>
                <span>❌</span>
                <span>إلغاء</span>
              </>
            ) : (
              <>
                <span>←</span>
                <span>السابق</span>
              </>
            )}
          </button>

          <div className="hidden sm:flex items-center gap-2 text-sm text-gray-500">
            <span>الخطوة {currentStep + 1} من {steps.length}</span>
            <div className="w-32 bg-gray-200 rounded-full h-2">
              <div
                className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
              ></div>
            </div>
          </div>

          <button
            onClick={() => {
              if (currentStep < steps.length - 1) {
                setCurrentStep(currentStep + 1);
              } else {
                handleSubmit();
              }
            }}
            className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors flex items-center justify-center gap-2 font-medium"
          >
            {currentStep === steps.length - 1 ? (
              <>
                <span>🚀</span>
                <span>نشر الوظيفة</span>
              </>
            ) : (
              <>
                <span>التالي</span>
                <span>→</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default JobPostingForm;
