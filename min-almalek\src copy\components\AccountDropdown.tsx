'use client';

import { useState, useRef, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import VerificationBadge from './VerificationBadge';
import { determineUserBadge } from '@/lib/verification';

interface AccountDropdownProps {
  className?: string;
}

const AccountDropdown = ({ className = '' }: AccountDropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const { user, isAuthenticated, logout } = useAuth();

  // إغلاق القائمة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleLogout = () => {
    logout();
    setIsOpen(false);
  };

  if (!isAuthenticated || !user) {
    // عرض أيقونة تسجيل الدخول للمستخدمين غير المسجلين
    return (
      <button
        onClick={() => window.location.href = '/'}
        className="relative p-3 text-gray-600 hover:text-primary-600 transition-all duration-300 group rounded-full focus:outline-none focus:ring-0"
        title="تسجيل الدخول"
      >
        <svg
          className="w-6 h-6 transition-all duration-300 group-hover:text-primary-600 group-hover:scale-110"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
        </svg>
      </button>
    );
  }

  const userBadge = determineUserBadge(user.userType, user.subscription?.planId);

  const menuItems = [
    {
      icon: '📊',
      label: 'لوحة التحكم',
      href: '/dashboard',
      description: 'نظرة عامة على حسابك'
    },
    {
      icon: '📋',
      label: 'إعلاناتي',
      href: '/dashboard?tab=ads',
      description: 'إدارة جميع إعلاناتك'
    },
    {
      icon: '💎',
      label: 'الاشتراكات',
      href: '/my-subscription',
      description: 'إدارة باقاتك والدفع'
    },
    {
      icon: '👤',
      label: 'الملف الشخصي',
      href: '/profile',
      description: 'تحديث معلوماتك الشخصية'
    },
    {
      icon: '⚙️',
      label: 'الإعدادات',
      href: '/settings',
      description: 'إعدادات الحساب والخصوصية'
    },
  ];

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* زر الحساب */}
      <button
        onClick={handleToggle}
        className={`relative p-3 text-gray-600 hover:text-primary-600 transition-all duration-300 group rounded-full focus:outline-none focus:ring-0 ${
          isOpen ? 'shadow-lg shadow-green-500/30 bg-green-50' : ''
        }`}
        title="حسابي"
      >
        <svg
          className="w-6 h-6 transition-all duration-300 group-hover:text-primary-600 group-hover:scale-110"
          fill="currentColor"
          viewBox="0 0 24 24"
        >
          <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
        </svg>

        {/* نقطة الحالة */}
        <span className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></span>
      </button>

      {/* قائمة الحساب */}
      {isOpen && (
        <div className="absolute left-0 mt-2 w-80 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden">
          {/* رأس القائمة - معلومات المستخدم */}
          <div className="p-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <span className="text-xl">
                  {user.userType === 'individual' && '👤'}
                  {user.userType === 'business' && '🏢'}
                  {user.userType === 'real-estate-office' && '🏘️'}
                </span>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h3 className="font-semibold text-white">{user.name}</h3>
                  <VerificationBadge type={userBadge.type} size="xs" />
                </div>
                <p className="text-sm opacity-90">{user.email}</p>
                <p className="text-xs opacity-75">
                  {user.userType === 'individual' && 'مستخدم فردي'}
                  {user.userType === 'business' && 'حساب شركة'}
                  {user.userType === 'real-estate-office' && 'مكتب عقاري'}
                  {' • '}
                  عضو منذ {new Date(user.createdAt).toLocaleDateString('en-GB', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit'
                  }).split('/').reverse().join('/')}
                </p>
              </div>
            </div>
          </div>

          {/* إحصائيات سريعة */}
          <div className="p-4 bg-gray-50 border-b border-gray-200">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-lg font-bold text-primary-600">{user.stats.activeAds}</div>
                <div className="text-xs text-gray-600">إعلانات نشطة</div>
              </div>
              <div>
                <div className="text-lg font-bold text-green-600">{user.stats.totalViews}</div>
                <div className="text-xs text-gray-600">مشاهدات</div>
              </div>
              <div>
                <div className="text-lg font-bold text-orange-600">{user.stats.totalContacts}</div>
                <div className="text-xs text-gray-600">استفسارات</div>
              </div>
            </div>
          </div>

          {/* عناصر القائمة */}
          <div className="py-2">
            {menuItems.map((item, index) => (
              <a
                key={index}
                href={item.href}
                className="flex items-center gap-3 px-4 py-3 hover:bg-gray-50 transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <span className="text-xl">{item.icon}</span>
                <div className="flex-1">
                  <div className="font-medium text-gray-800">{item.label}</div>
                  <div className="text-xs text-gray-500">{item.description}</div>
                </div>
                <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </a>
            ))}
          </div>

          {/* معلومات الاشتراك */}
          {user.subscription && (
            <div className="p-4 bg-primary-50 border-t border-gray-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-primary-800">{user.subscription.planName}</span>
                  <VerificationBadge type={userBadge.type} size="xs" />
                </div>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  user.subscription.isActive
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}>
                  {user.subscription.isActive ? 'نشط' : 'غير نشط'}
                </span>
              </div>
              <div className="text-xs text-primary-600">
                ينتهي في {new Date(user.subscription.endDate).toLocaleDateString('en-GB', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit'
                }).split('/').reverse().join('/')}
              </div>
            </div>
          )}

          {/* أزرار التحكم */}
          <div className="p-4 border-t border-gray-200 bg-gray-50">
            <div className="flex gap-2">
              <button
                onClick={() => {
                  window.location.href = '/add-ad';
                  setIsOpen(false);
                }}
                className="flex-1 px-3 py-2 text-sm bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
              >
                إضافة إعلان
              </button>
              <button
                onClick={handleLogout}
                className="flex-1 px-3 py-2 text-sm bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccountDropdown;
