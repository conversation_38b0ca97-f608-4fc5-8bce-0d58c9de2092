'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ContactButtons from '@/components/ContactButtons';
import VerificationBadge from '@/components/VerificationBadge';

export default function RealEstateOfficePage() {
  const [activeTab, setActiveTab] = useState('properties');
  const [selectedProperty, setSelectedProperty] = useState(null);

  const officeInfo = {
    name: 'مكتب الأمانة العقاري',
    license: 'RE-2024-001234',
    established: '2015',
    employees: 12,
    rating: 4.8,
    reviews: 127,
    address: 'شارع الثورة، دمشق - المالكي',
    phone: '+963 11 123 4567',
    mobile: '+963 988 123 456',
    email: '<EMAIL>',
    website: 'www.alamana-realestate.com'
  };

  const teamMembers = [
    { name: 'أحمد محمد', role: 'مدير المكتب', experience: '15 سنة خبرة', image: '👨‍💼' },
    { name: 'فاطمة علي', role: 'مستشارة عقارية', experience: '8 سنوات خبرة', image: '👩‍💼' },
    { name: 'محمد حسن', role: 'خبير تقييم عقاري', experience: '10 سنوات خبرة', image: '👨‍💼' },
    { name: 'سارة أحمد', role: 'مسؤولة خدمة العملاء', experience: '5 سنوات خبرة', image: '👩‍💼' }
  ];

  const properties = [
    {
      id: 1,
      title: 'شقة فاخرة في المالكي',
      type: 'شقة',
      price: '450,000,000',
      area: 180,
      rooms: 4,
      bathrooms: 3,
      floor: 'ثالث',
      image: '🏠',
      location: 'المالكي، دمشق',
      features: ['مصعد', 'بلكونة', 'تكييف', 'موقف سيارة']
    },
    {
      id: 2,
      title: 'فيلا مستقلة في المزة',
      type: 'فيلا',
      price: '850,000,000',
      area: 350,
      rooms: 6,
      bathrooms: 4,
      floor: 'أرضي + أول',
      image: '🏘️',
      location: 'المزة، دمشق',
      features: ['حديقة', 'مسبح', 'مرآب', 'نظام أمان']
    },
    {
      id: 3,
      title: 'محل تجاري في الشعلان',
      type: 'محل',
      price: '320,000,000',
      area: 85,
      rooms: 0,
      bathrooms: 1,
      floor: 'أرضي',
      image: '🏪',
      location: 'الشعلان، دمشق',
      features: ['واجهة زجاجية', 'موقع مميز', 'قريب من المترو']
    }
  ];

  const certificates = [
    { name: 'ترخيص وزارة الإسكان', number: 'RE-2024-001234', icon: '🏆' },
    { name: 'عضوية نقابة الوسطاء العقاريين', number: 'SREB-2024-567', icon: '🏆' },
    { name: 'شهادة ISO 9001', number: 'ISO-2024-890', icon: '🏆' },
    { name: 'جائزة أفضل مكتب عقاري 2023', number: 'AWARD-2023-123', icon: '🏆' }
  ];

  const reviews = [
    { name: 'أحمد محمود', rating: 5, comment: 'خدمة ممتازة وصدق في التعامل، ساعدوني في بيع شقتي بسرعة وبسعر ممتاز', date: '2024-01-15' },
    { name: 'فاطمة علي', rating: 5, comment: 'ساعدوني في إيجاد البيت المثالي لعائلتي، فريق محترف جداً', date: '2024-01-10' },
    { name: 'محمد حسن', rating: 5, comment: 'مهنية عالية وسرعة في الإنجاز، أنصح بالتعامل معهم', date: '2024-01-05' }
  ];

  const stats = {
    propertiesSold: 450,
    propertiesRented: 320,
    satisfiedClients: 95,
    averageSaleTime: 45,
    totalSalesValue: '15 مليار'
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="py-8">
        <div className="container mx-auto px-4">
          {/* رأس الصفحة */}
          <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <div className="flex flex-col lg:flex-row items-start lg:items-center gap-6">
              <div className="text-6xl">🏘️</div>

              <div className="flex-1">
                <div className="flex items-center gap-3 mb-4">
                  <h1 className="text-3xl font-bold text-gray-800">{officeInfo.name}</h1>
                  <VerificationBadge badgeId="real-estate-office" size="lg" showTooltip={true} />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-600">
                  <div className="flex items-center gap-2">
                    <span>📍</span>
                    <span>{officeInfo.address}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>📞</span>
                    <span>{officeInfo.phone}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>📱</span>
                    <span>{officeInfo.mobile}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>✉️</span>
                    <span>{officeInfo.email}</span>
                  </div>
                </div>

                <div className="flex items-center gap-6 mt-4">
                  <div className="flex items-center gap-1">
                    <span className="text-yellow-500">⭐</span>
                    <span className="font-semibold">{officeInfo.rating}/5</span>
                    <span className="text-gray-500">({officeInfo.reviews} تقييم)</span>
                  </div>
                  <div className="text-gray-600">
                    <span>🏢 تأسس عام {officeInfo.established}</span>
                  </div>
                  <div className="text-gray-600">
                    <span>👥 {officeInfo.employees} موظف</span>
                  </div>
                </div>
              </div>

              <div className="flex flex-col gap-3">
                <ContactButtons
                  variant="stacked"
                  size="md"
                  defaultMessage="مرحباً، أريد الاستفسار عن خدمات المكتب العقاري"
                />
              </div>
            </div>
          </div>

          {/* التبويبات */}
          <div className="bg-white rounded-2xl shadow-lg mb-8">
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-8">
                {[
                  { id: 'properties', name: 'العقارات المتاحة', icon: '🏠' },
                  { id: 'team', name: 'فريق العمل', icon: '👥' },
                  { id: 'services', name: 'الخدمات', icon: '🛠️' },
                  { id: 'certificates', name: 'الشهادات', icon: '🏆' },
                  { id: 'reviews', name: 'التقييمات', icon: '⭐' },
                  { id: 'stats', name: 'الإحصائيات', icon: '📊' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-2 border-b-2 font-medium text-sm ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <span className="mr-2">{tab.icon}</span>
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>

            <div className="p-8">
              {/* تبويب العقارات */}
              {activeTab === 'properties' && (
                <div>
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-bold text-gray-800">العقارات المتاحة</h2>
                    <span className="bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm font-medium">
                      {properties.length} عقار متاح
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {properties.map((property) => (
                      <div key={property.id} className="bg-gray-50 rounded-xl p-6 hover:shadow-lg transition-shadow">
                        <div className="text-4xl mb-4 text-center">{property.image}</div>

                        <h3 className="text-lg font-semibold text-gray-800 mb-2">{property.title}</h3>
                        <p className="text-gray-600 mb-3">{property.location}</p>

                        <div className="grid grid-cols-2 gap-2 text-sm text-gray-600 mb-4">
                          <div>📐 {property.area} م²</div>
                          <div>🛏️ {property.rooms} غرف</div>
                          <div>🚿 {property.bathrooms} حمام</div>
                          <div>🏢 {property.floor}</div>
                        </div>

                        <div className="flex flex-wrap gap-1 mb-4">
                          {property.features.slice(0, 3).map((feature, index) => (
                            <span key={index} className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                              {feature}
                            </span>
                          ))}
                          {property.features.length > 3 && (
                            <span className="text-xs text-gray-500">+{property.features.length - 3}</span>
                          )}
                        </div>

                        <div className="flex justify-between items-center">
                          <div className="text-lg font-bold text-primary-600">
                            {property.price} ل.س
                          </div>
                          <button className="bg-primary-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-primary-700">
                            تفاصيل أكثر
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* تبويب فريق العمل */}
              {activeTab === 'team' && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-6">فريق العمل</h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {teamMembers.map((member, index) => (
                      <div key={index} className="bg-gray-50 rounded-xl p-6 text-center">
                        <div className="text-4xl mb-4">{member.image}</div>
                        <h3 className="text-lg font-semibold text-gray-800 mb-1">{member.name}</h3>
                        <p className="text-primary-600 font-medium mb-2">{member.role}</p>
                        <p className="text-gray-600 text-sm">{member.experience}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* تبويب الخدمات */}
              {activeTab === 'services' && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-6">خدماتنا</h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6">
                      <div className="text-3xl mb-4">🏠</div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">بيع وشراء العقارات</h3>
                      <p className="text-gray-600 text-sm">خدمات شاملة لبيع وشراء جميع أنواع العقارات السكنية والتجارية</p>
                    </div>

                    <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6">
                      <div className="text-3xl mb-4">📊</div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">تقييم العقارات</h3>
                      <p className="text-gray-600 text-sm">تقييم دقيق للعقارات من قبل خبراء معتمدين</p>
                    </div>

                    <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6">
                      <div className="text-3xl mb-4">🏢</div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">إدارة العقارات</h3>
                      <p className="text-gray-600 text-sm">إدارة شاملة للعقارات الاستثمارية والتأجيرية</p>
                    </div>

                    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-6">
                      <div className="text-3xl mb-4">⚖️</div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">الاستشارات القانونية</h3>
                      <p className="text-gray-600 text-sm">استشارات قانونية متخصصة في القانون العقاري</p>
                    </div>

                    <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-6">
                      <div className="text-3xl mb-4">💰</div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">التمويل العقاري</h3>
                      <p className="text-gray-600 text-sm">مساعدة في الحصول على التمويل والقروض العقارية</p>
                    </div>

                    <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-xl p-6">
                      <div className="text-3xl mb-4">🛡️</div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-2">التأمين العقاري</h3>
                      <p className="text-gray-600 text-sm">خدمات التأمين على العقارات ضد المخاطر</p>
                    </div>
                  </div>
                </div>
              )}

              {/* تبويب الشهادات */}
              {activeTab === 'certificates' && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-6">الشهادات والتراخيص</h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {certificates.map((cert, index) => (
                      <div key={index} className="bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-xl p-6 border border-yellow-200">
                        <div className="flex items-center gap-4">
                          <div className="text-3xl">{cert.icon}</div>
                          <div>
                            <h3 className="text-lg font-semibold text-gray-800">{cert.name}</h3>
                            <p className="text-gray-600">رقم الشهادة: {cert.number}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* تبويب التقييمات */}
              {activeTab === 'reviews' && (
                <div>
                  <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-bold text-gray-800">تقييمات العملاء</h2>
                    <div className="flex items-center gap-2">
                      <span className="text-yellow-500 text-2xl">⭐</span>
                      <span className="text-xl font-bold">{officeInfo.rating}/5</span>
                      <span className="text-gray-500">({officeInfo.reviews} تقييم)</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-6 mb-8">
                    {reviews.map((review, index) => (
                      <div key={index} className="bg-gray-50 rounded-xl p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                              <span className="text-primary-600 font-semibold">
                                {review.name.charAt(0)}
                              </span>
                            </div>
                            <div>
                              <h4 className="font-semibold text-gray-800">{review.name}</h4>
                              <p className="text-gray-500 text-sm">{review.date}</p>
                            </div>
                          </div>
                          <div className="flex">
                            {[...Array(review.rating)].map((_, i) => (
                              <span key={i} className="text-yellow-500">⭐</span>
                            ))}
                          </div>
                        </div>
                        <p className="text-gray-700">{review.comment}</p>
                      </div>
                    ))}
                  </div>

                  <div className="bg-blue-50 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">إحصائيات التقييمات</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">85%</div>
                        <div className="text-gray-600">5 نجوم</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">12%</div>
                        <div className="text-gray-600">4 نجوم</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-yellow-600">3%</div>
                        <div className="text-gray-600">3 نجوم</div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* تبويب الإحصائيات */}
              {activeTab === 'stats' && (
                <div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-6">إحصائيات الأداء</h2>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6 text-center">
                      <div className="text-3xl mb-2">🏠</div>
                      <div className="text-3xl font-bold text-green-600 mb-1">{stats.propertiesSold}+</div>
                      <div className="text-gray-700">عقار مباع</div>
                    </div>

                    <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6 text-center">
                      <div className="text-3xl mb-2">🏢</div>
                      <div className="text-3xl font-bold text-blue-600 mb-1">{stats.propertiesRented}+</div>
                      <div className="text-gray-700">عقار مؤجر</div>
                    </div>

                    <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6 text-center">
                      <div className="text-3xl mb-2">👥</div>
                      <div className="text-3xl font-bold text-purple-600 mb-1">{stats.satisfiedClients}%</div>
                      <div className="text-gray-700">عملاء راضين</div>
                    </div>

                    <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl p-6 text-center">
                      <div className="text-3xl mb-2">⏱️</div>
                      <div className="text-3xl font-bold text-yellow-600 mb-1">{stats.averageSaleTime}</div>
                      <div className="text-gray-700">يوم متوسط البيع</div>
                    </div>

                    <div className="bg-gradient-to-br from-red-50 to-red-100 rounded-xl p-6 text-center">
                      <div className="text-3xl mb-2">💰</div>
                      <div className="text-3xl font-bold text-red-600 mb-1">{stats.totalSalesValue}</div>
                      <div className="text-gray-700">إجمالي المبيعات</div>
                    </div>

                    <div className="bg-gradient-to-br from-indigo-50 to-indigo-100 rounded-xl p-6 text-center">
                      <div className="text-3xl mb-2">📈</div>
                      <div className="text-3xl font-bold text-indigo-600 mb-1">9</div>
                      <div className="text-gray-700">سنوات خبرة</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* نموذج التواصل */}
          <div className="bg-white rounded-2xl shadow-lg p-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">تواصل معنا</h2>

            <form className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع الاستفسار</label>
                <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                  <option>بيع عقار</option>
                  <option>شراء عقار</option>
                  <option>إيجار عقار</option>
                  <option>تقييم عقار</option>
                  <option>استشارة عقارية</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع العقار</label>
                <select className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                  <option>شقة</option>
                  <option>فيلا</option>
                  <option>منزل</option>
                  <option>محل تجاري</option>
                  <option>مكتب</option>
                  <option>أرض</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                <input type="text" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500" />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                <input type="tel" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500" />
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 mb-2">تفاصيل إضافية</label>
                <textarea rows={4} className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"></textarea>
              </div>

              <div className="md:col-span-2">
                <button type="submit" className="w-full bg-primary-600 text-white py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors">
                  إرسال الاستفسار
                </button>
              </div>
            </form>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
