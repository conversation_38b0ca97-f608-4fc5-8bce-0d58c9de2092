'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Ad } from '@/lib/data';
import VerificationBadge from './VerificationBadge';
import SafeTimeDisplay from './SafeTimeDisplay';

interface AdDetailsProps {
  ad: Ad;
}

const AdDetails = ({ ad }: AdDetailsProps) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showContactInfo, setShowContactInfo] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);

  // دالة لتنسيق الموقع
  const getLocationString = () => {
    return `${ad.location.governorate} - ${ad.location.city}${ad.location.area ? ` - ${ad.location.area}` : ''}`;
  };



  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % ad.images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + ad.images.length) % ad.images.length);
  };

  const toggleFavorite = () => {
    setIsFavorite(!isFavorite);
  };

  return (
    <div className="container mx-auto px-4 py-8" suppressHydrationWarning={true}>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* العمود الرئيسي */}
        <div className="lg:col-span-2">
          {/* معرض الصور */}
          <div className="bg-white rounded-xl shadow-lg overflow-hidden mb-6">
            <div className="relative h-96">
              {ad.images.length > 0 ? (
                <>
                  <img
                    src={ad.images[currentImageIndex]}
                    alt={ad.title}
                    className="w-full h-full object-cover"
                  />
                  {ad.images.length > 1 && (
                    <>
                      <button
                        onClick={prevImage}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-black/70"
                      >
                        ←
                      </button>
                      <button
                        onClick={nextImage}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-black/70"
                      >
                        →
                      </button>
                    </>
                  )}

                  {/* مؤشر الصور */}
                  {ad.images.length > 1 && (
                    <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                      {ad.images.map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentImageIndex(index)}
                          className={`w-3 h-3 rounded-full ${
                            index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                          }`}
                        />
                      ))}
                    </div>
                  )}
                </>
              ) : (
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-400 text-6xl">🖼️</span>
                </div>
              )}

              {/* الشارات */}
              <div className="absolute top-4 right-4 flex flex-col gap-2">
                {ad.featured && (
                  <span className="bg-yellow-500 text-white text-sm px-3 py-1 rounded-full font-medium">
                    مميز
                  </span>
                )}
                {ad.status === 'active' && (
                  <span className="bg-green-500 text-white text-sm px-3 py-1 rounded-full font-medium">
                    نشط
                  </span>
                )}
              </div>
            </div>

            {/* صور مصغرة */}
            {ad.images.length > 1 && (
              <div className="p-4 border-t">
                <div className="flex gap-2 overflow-x-auto">
                  {ad.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 ${
                        index === currentImageIndex ? 'border-primary-500' : 'border-gray-200'
                      }`}
                    >
                      <img
                        src={image}
                        alt={`صورة ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* تفاصيل الإعلان */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            <div className="flex items-start justify-between mb-4">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 mb-2">{ad.title}</h1>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <span>📍 {getLocationString()}</span>
                  <span>📂 {ad.category} - {ad.subcategory}</span>
                  <span>👁️ {ad.views} مشاهدة</span>
                  <span>📅 <SafeTimeDisplay dateString={ad.createdAt} /></span>
                </div>
              </div>
              <button
                onClick={toggleFavorite}
                className="text-2xl hover:scale-110 transition-transform"
              >
                {isFavorite ? '❤️' : '🤍'}
              </button>
            </div>

            <div className="flex items-center justify-between mb-6 p-4 bg-gray-50 rounded-lg">
              <div>
                <div className="text-4xl font-bold text-primary-600">
                  {ad.price.toLocaleString()} {ad.currency === 'SYP' ? 'ل.س' : ad.currency === 'USD' ? '$' : '€'}
                </div>
                {ad.condition && (
                  <div className="text-sm text-gray-600 mt-1">
                    الحالة: <span className="font-medium">{ad.condition}</span>
                  </div>
                )}
              </div>
              <div className="flex gap-2">
                <button className="px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors">
                  📞 اتصل الآن
                </button>
                <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                  💬 رسالة
                </button>
              </div>
            </div>

            <div className="prose max-w-none">
              <h3 className="text-xl font-semibold text-gray-800 mb-3">الوصف</h3>
              <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                {ad.description}
              </p>
            </div>
          </div>

          {/* المواصفات */}
          {ad.specifications && Object.keys(ad.specifications).length > 0 && (
            <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
              <h3 className="text-xl font-semibold text-gray-800 mb-4">المواصفات</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(ad.specifications).map(([key, value]) => (
                  <div key={key} className="flex justify-between py-2 border-b border-gray-100">
                    <span className="font-medium text-gray-700">{key}</span>
                    <span className="text-gray-600">{String(value)}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* أزرار المشاركة */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">شارك الإعلان</h3>
            <div className="flex gap-3">
              <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                📘 فيسبوك
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-blue-400 text-white rounded-lg hover:bg-blue-500 transition-colors">
                🐦 تويتر
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                📱 واتساب
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors">
                📋 نسخ الرابط
              </button>
            </div>
          </div>
        </div>

        {/* الشريط الجانبي */}
        <div className="lg:col-span-1">
          {/* معلومات البائع */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            <h3 className="text-xl font-semibold text-gray-800 mb-4">معلومات البائع</h3>

            <div className="flex items-center gap-3 mb-4">
              <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                <span className="text-2xl">
                  {ad.seller.type === 'business' ? '🏢' : '👤'}
                </span>
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <span className="font-semibold text-gray-800">{ad.seller.name}</span>
                  {ad.seller.verified && (
                    <VerificationBadge
                      badgeId={
                        ad.seller.type === 'business' && ad.seller.name.includes('مكتب') && ad.seller.name.includes('عقاري')
                          ? 'real-estate-office'
                          : ad.seller.type === 'business'
                            ? 'business-verified'
                            : 'verified-basic'
                      }
                      size="sm"
                    />
                  )}
                </div>
                <div className="text-sm text-gray-600">
                  {ad.seller.type === 'business' ? 'شركة' : 'فرد'}
                </div>
              </div>
            </div>

            <div className="space-y-2 text-sm text-gray-600 mb-4">
              <div>📍 {getLocationString()}</div>
              <div>📅 عضو منذ <SafeTimeDisplay dateString={ad.createdAt} /></div>
              <div>⭐ {ad.seller.rating.toFixed(1)} ({ad.seller.totalRatings} تقييم)</div>
            </div>

            {showContactInfo ? (
              <div className="space-y-3">
                <div className="p-3 bg-gray-50 rounded-lg">
                  <div className="text-sm text-gray-600 mb-1">رقم الهاتف</div>
                  <div className="font-semibold text-gray-800">{ad.seller.phone}</div>
                </div>
                {ad.seller.email && (
                  <div className="p-3 bg-gray-50 rounded-lg">
                    <div className="text-sm text-gray-600 mb-1">البريد الإلكتروني</div>
                    <div className="font-semibold text-gray-800">{ad.seller.email}</div>
                  </div>
                )}
              </div>
            ) : (
              <button
                onClick={() => setShowContactInfo(true)}
                className="w-full bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 transition-colors font-semibold"
              >
                إظهار معلومات الاتصال
              </button>
            )}

            <div className="mt-4 pt-4 border-t border-gray-100">
              <Link
                href={`/seller/${ad.seller.name}`}
                className="text-primary-600 hover:text-primary-700 text-sm font-medium"
              >
                عرض جميع إعلانات البائع →
              </Link>
            </div>
          </div>

          {/* نصائح الأمان */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center gap-2">
              🛡️ نصائح الأمان
            </h3>
            <ul className="space-y-2 text-sm text-gray-700">
              <li>• تأكد من المنتج قبل الدفع</li>
              <li>• التقي في مكان عام آمن</li>
              <li>• لا تدفع مقدماً قبل المعاينة</li>
              <li>• تأكد من هوية البائع</li>
              <li>• احذر من العروض المشبوهة</li>
            </ul>
          </div>

          {/* إبلاغ عن الإعلان */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-3">إبلاغ عن مشكلة</h3>
            <p className="text-sm text-gray-600 mb-4">
              إذا كان هناك مشكلة في هذا الإعلان، يرجى إبلاغنا
            </p>
            <button className="w-full border border-red-300 text-red-600 py-2 rounded-lg hover:bg-red-50 transition-colors text-sm">
              🚨 إبلاغ عن الإعلان
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdDetails;
