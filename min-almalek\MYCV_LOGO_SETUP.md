# إعداد شعار MyCv

## 📋 المطلوب

لإكمال إعداد شعار MyCv، يرجى اتباع الخطوات التالية:

### 1. إضافة ملف الشعار

1. احفظ صورة الشعار باسم `mycv-logo.png`
2. ضع الملف في المجلد: `public/images/mycv-logo.png`
3. تأكد من أن الصورة بجودة عالية (يفضل PNG مع خلفية شفافة)

### 2. أبعاد الشعار المقترحة

- **الحد الأدنى:** 200x200 بكسل
- **المثالي:** 512x512 بكسل أو أعلى
- **التنسيق:** PNG مع خلفية شفافة (إذا أمكن)

### 3. الأماكن التي يظهر فيها الشعار

✅ **تم التطبيق في:**

1. **CategoryGrid** - أيقونة الوظائف
   - مع النص: "مدعوم من قبل تطبيق MyCv"
   - خلفية صفراء متدرجة تتماشى مع الشعار

2. **صفحة الوظائف الرئيسية** (`/jobs`)
   - في قسم السير الذاتية
   - مع وصف المنصة

3. **صفحة إنشاء السيرة الذاتية** (`/resume/create`)
   - في قسم Hero
   - مع معلومات المنصة

4. **Footer**
   - في قسم تحميل التطبيق
   - مع خلفية صفراء تتماشى مع الشعار

### 4. مكون MyCvLogo

تم إنشاء مكون قابل للإعادة الاستخدام:

```tsx
<MyCvLogo 
  size="sm|md|lg|xl"           // حجم الشعار
  variant="icon|text|full|square"  // نوع العرض
  showText={true|false}        // إظهار النص
  className="custom-classes"   // فئات CSS إضافية
/>
```

### 5. الألوان المستخدمة

تم تطبيق نظام ألوان يتماشى مع الشعار الأصفر:

- **الخلفية:** `from-yellow-50 to-amber-50`
- **الحدود:** `border-yellow-200`
- **النص:** `text-amber-800` و `text-amber-700`
- **التدرج:** `from-yellow-100 to-amber-100`

### 6. الاستخدامات المستقبلية

يمكن إضافة الشعار في:

- **Header** - كشعار رئيسي
- **صفحات القوالب** - كعلامة تجارية
- **رسائل البريد الإلكتروني** - في التوقيع
- **صفحات الشركات** - كمرجع للمنصة
- **التطبيق الجوال** - كأيقونة التطبيق

### 7. التحسينات المطبقة

1. **تصميم متجاوب** - يعمل على جميع الأحجام
2. **تحسين الأداء** - استخدام Next.js Image مع priority
3. **إمكانية الوصول** - نص بديل واضح
4. **تناسق بصري** - ألوان متناسقة مع الشعار

### 8. ملاحظات مهمة

- الشعار الحالي مؤقت (أيقونة CV)
- بعد إضافة الشعار الحقيقي، سيظهر تلقائياً في جميع الأماكن
- التصميم محسن ليتماشى مع الألوان الصفراء للشعار
- جميع النصوص باللغة العربية كما طُلب

### 9. الخطوات النهائية

1. ✅ إضافة النص "مدعوم من قبل تطبيق MyCv"
2. ✅ تطبيق التصميم المتناسق
3. ⏳ **إضافة ملف الشعار الحقيقي**
4. ⏳ اختبار العرض على جميع الصفحات

---

**ملاحظة:** بمجرد إضافة ملف `mycv-logo.png` في المجلد المحدد، سيظهر الشعار الحقيقي تلقائياً في جميع الأماكن المذكورة أعلاه.
