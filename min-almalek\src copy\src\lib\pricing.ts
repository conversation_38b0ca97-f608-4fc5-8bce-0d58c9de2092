// ملف مركزي لأسعار الاشتراكات الموحدة في جميع أنحاء الموقع

export interface PricingPlan {
  id: string;
  name: string;
  price: number | string;
  currency: string;
  period: string;
  popular: boolean;
  features: string[];
  limitations?: string[];
  description?: string;
  icon?: string;
}

// الأسعار الموحدة للأفراد
export const INDIVIDUAL_PLANS: PricingPlan[] = [
  {
    id: 'individual-free',
    name: 'الخطة المجانية',
    price: 0,
    currency: 'ل.س',
    period: 'شهرياً',
    popular: false,
    icon: '🆓',
    description: 'مثالية للمبتدئين',
    features: [
      '3 إعلانات مجانية شهرياً',
      'صور حتى 3 لكل إعلان',
      'مدة الإعلان 30 يوم',
      'دعم فني أساسي',
      'إحصائيات بسيطة'
    ],
    limitations: [
      'لا يظهر في الإعلانات المميزة',
      'ترتيب عادي في النتائج'
    ]
  },
  {
    id: 'individual-basic',
    name: 'الباقة الأساسية',
    price: 25000,
    currency: 'ل.س',
    period: 'شهرياً',
    popular: false,
    icon: '🥉',
    description: 'للاستخدام الشخصي البسيط',
    features: [
      '5 إعلانات شهرياً',
      'عرض في النتائج العامة',
      'دعم فني أساسي',
      'إحصائيات بسيطة',
      'صور حتى 5 لكل إعلان',
      'مدة الإعلان 30 يوم'
    ]
  },
  {
    id: 'individual-premium',
    name: 'الباقة المميزة',
    price: 50000,
    currency: 'ل.س',
    period: 'شهرياً',
    popular: true,
    icon: '⭐',
    description: 'الأكثر شعبية للأفراد',
    features: [
      '15 إعلان شهرياً',
      'إعلانات مميزة',
      'أولوية في النتائج',
      'دعم فني متقدم',
      'إحصائيات مفصلة',
      'شارة "معلن مميز"',
      'صور حتى 8 لكل إعلان',
      'مدة الإعلان 45 يوم'
    ]
  },
  {
    id: 'individual-vip',
    name: 'باقة الأعمال',
    price: 100000,
    currency: 'ل.س',
    period: 'شهرياً',
    popular: false,
    icon: '💎',
    description: 'للمستخدمين المحترفين',
    features: [
      'إعلانات غير محدودة',
      'إعلانات عاجلة',
      'أولوية قصوى',
      'دعم فني 24/7',
      'إحصائيات متقدمة',
      'صفحة معلن مخصصة',
      'إدارة متعددة المستخدمين',
      'صور حتى 20 لكل إعلان',
      'مدة الإعلان 60 يوم'
    ]
  }
];

// الأسعار الموحدة للشركات
export const BUSINESS_PLANS: PricingPlan[] = [
  {
    id: 'business-starter',
    name: 'خطة البداية',
    price: 500000,
    currency: 'ل.س',
    period: 'شهرياً',
    popular: false,
    icon: '🏢',
    description: 'للشركات الناشئة',
    features: [
      '15 إعلان مميز شهرياً',
      'جميع الإعلانات عدا الوظائف',
      'صور حتى 15 لكل إعلان',
      'مدة الإعلان 30 يوم',
      'شارة "شركة موثقة"',
      'صفحة شركة أساسية',
      'إحصائيات مفصلة',
      'دعم فني للشركات',
      'أولوية في النتائج'
    ]
  },
  {
    id: 'real-estate-office',
    name: 'باقة المكاتب العقارية',
    price: 700000,
    currency: 'ل.س',
    period: 'شهرياً',
    popular: true,
    icon: '🏘️',
    description: 'مخصصة للمكاتب العقارية المحترفة',
    features: [
      '30 إعلان عقاري مميز شهرياً',
      'إعلانات العقارات فقط',
      'صور حتى 20 لكل إعلان',
      'مدة الإعلان 30 يوم',
      'شارة "مكتب عقاري موثق" 🏘️',
      'صفحة مكتب عقاري متكاملة',
      'أدوات تقييم العقارات',
      'إحصائيات عقارية متقدمة',
      'تقارير السوق العقاري',
      'دعم فني متخصص للعقارات',
      'أولوية عالية في نتائج البحث',
      'إمكانية إضافة فريق العمل',
      'نظام إدارة العملاء CRM',
      'تكامل مع خرائط العقارات',
      'شهادات وتراخيص المكتب',
      'نظام المواعيد والجولات',
      'تقييمات العملاء المتقدمة'
    ]
  },
  {
    id: 'business-professional',
    name: 'الخطة المهنية',
    price: 1000000,
    currency: 'ل.س',
    period: 'شهرياً',
    popular: false,
    icon: '🏆',
    description: 'للشركات المتقدمة',
    features: [
      '50 إعلان مميز شهرياً',
      'جميع الإعلانات عدا الوظائف',
      'صور حتى 20 لكل إعلان',
      'مدة الإعلان 30 يوم',
      'أولوية عالية في النتائج',
      'شارة "شركة مميزة"',
      'صفحة شركة متقدمة',
      'إحصائيات متقدمة',
      'دعم فني مخصص',
      'إمكانية الإعلانات العاجلة',
      'تقارير أسبوعية',
      'إدارة متعددة المستخدمين'
    ]
  }
];

// ملاحظة: باقات الوظائف لها نظام تسعير منفصل
// جميع الباقات أعلاه تشمل كافة الإعلانات عدا إعلانات الوظائف

// دالة لتنسيق السعر
export const formatPrice = (price: number | string, currency: string = 'ل.س'): string => {
  if (typeof price === 'string') {
    return price;
  }
  if (price === 0) {
    return 'مجاني';
  }
  return `${price.toLocaleString()} ${currency}`;
};

// دالة للحصول على جميع الخطط
export const getAllPlans = (): PricingPlan[] => {
  return [...INDIVIDUAL_PLANS, ...BUSINESS_PLANS];
};

// دالة للحصول على خطة بالمعرف
export const getPlanById = (id: string): PricingPlan | undefined => {
  return getAllPlans().find(plan => plan.id === id);
};

// دالة للحصول على الخطط الشائعة
export const getPopularPlans = (): PricingPlan[] => {
  return getAllPlans().filter(plan => plan.popular);
};

// دالة للحصول على خطط الأفراد فقط
export const getIndividualPlans = (): PricingPlan[] => {
  return INDIVIDUAL_PLANS;
};

// دالة للحصول على خطط الشركات فقط
export const getBusinessPlans = (): PricingPlan[] => {
  return BUSINESS_PLANS;
};

// الخطط المبسطة لصفحة الاشتراكات (موحدة مع INDIVIDUAL_PLANS)
export const SUBSCRIPTION_PLANS = INDIVIDUAL_PLANS.map(plan => ({
  id: plan.id,
  name: plan.name,
  price: plan.price,
  currency: plan.currency,
  duration: plan.period,
  features: plan.features,
  popular: plan.popular || false
}));

// معلومات إضافية موحدة
export const PRICING_INFO = {
  paymentMethods: [
    'الدفع عبر البطاقات البنكية',
    'Visa و MasterCard',
    'PayPal',
    'Apple Pay',
    'Cash App'
  ],
  guarantees: [
    'ضمان استرداد المال خلال 7 أيام',
    'دعم فني مجاني',
    'إمكانية تغيير الخطة في أي وقت',
    'بيانات آمنة ومحمية',
    'تشفير SSL للمعاملات'
  ],
  features: {
    security: 'دفع آمن 100%',
    support: 'دعم فني 24/7',
    flexibility: 'إمكانية الإلغاء في أي وقت'
  }
};
