<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <!-- Cash App Logo - Geometric Design -->
  <defs>
    <!-- Blue gradients for top triangles -->
    <linearGradient id="blue1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#5BA7F7;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4A90E2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="blue2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="blue3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#357ABD;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E5F8A;stop-opacity:1" />
    </linearGradient>

    <!-- Green gradients for bottom triangles -->
    <linearGradient id="green1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8ED65C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7ED321;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="green2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7ED321;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#5CB85C;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="green3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#5CB85C;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#4A9A4A;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Main geometric shape - isometric cube style -->

  <!-- Top face (blue triangles) -->
  <polygon points="50,10 75,25 50,40 25,25" fill="url(#blue1)" opacity="0.9"/>
  <polygon points="50,10 75,25 85,15 60,0" fill="url(#blue2)" opacity="0.8"/>
  <polygon points="75,25 85,15 85,35 75,45" fill="url(#blue3)" opacity="0.7"/>

  <!-- Left face (green triangles) -->
  <polygon points="25,25 50,40 50,70 25,55" fill="url(#green1)" opacity="0.9"/>
  <polygon points="15,35 25,25 25,55 15,65" fill="url(#green2)" opacity="0.8"/>
  <polygon points="25,55 50,70 40,80 15,65" fill="url(#green3)" opacity="0.7"/>

  <!-- Right face (blue-green blend) -->
  <polygon points="50,40 75,25 75,55 50,70" fill="url(#blue2)" opacity="0.6"/>
  <polygon points="75,25 85,35 85,65 75,55" fill="url(#blue3)" opacity="0.5"/>

  <!-- Center highlight -->
  <polygon points="50,35 60,40 50,45 40,40" fill="white" opacity="0.8"/>

  <!-- Additional depth elements -->
  <polygon points="50,70 75,55 85,65 60,80" fill="url(#green1)" opacity="0.4"/>
  <polygon points="15,65 40,80 50,70 25,55" fill="url(#green2)" opacity="0.5"/>
</svg>
