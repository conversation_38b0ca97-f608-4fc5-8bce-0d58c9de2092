'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import VerificationBadge from '@/components/VerificationBadge';
import { determineUserBadge } from '@/lib/verification';

// بيانات تجريبية للحسابات المختلفة
const demoAccounts = {
  individual: {
    id: 'user-individual',
    email: '<EMAIL>',
    phone: '+************',
    name: 'أحمد محمد',
    userType: 'individual' as const,
    isVerified: true,
    createdAt: new Date('2023-01-15'),
    lastLogin: new Date(),
    stats: {
      totalAds: 8,
      activeAds: 5,
      expiredAds: 3,
      totalViews: 850,
      totalContacts: 25,
      successfulDeals: 3,
    },
    individualInfo: {
      firstName: 'أحمد',
      lastName: 'محمد',
      gender: 'male' as const,
      address: {
        governorate: 'دمشق',
        city: 'دمشق',
        area: 'المزة',
      },
    },
    settings: {
      language: 'ar' as const,
      notifications: {
        email: true,
        sms: true,
        push: true,
        marketing: false,
      },
      privacy: {
        showPhone: true,
        showEmail: false,
        allowMessages: true,
        showOnlineStatus: true,
      },
      preferences: {
        currency: 'SYP' as const,
        theme: 'light' as const,
        autoSave: true,
      },
    },
  },
  business: {
    id: 'user-business',
    email: '<EMAIL>',
    phone: '+963988654321',
    name: 'شركة التجارة المتقدمة',
    userType: 'business' as const,
    isVerified: true,
    createdAt: new Date('2022-06-10'),
    lastLogin: new Date(),
    subscription: {
      planId: 'business-starter',
      planName: 'باقة البداية',
      planType: 'business' as const,
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-02-01'),
      isActive: true,
      autoRenew: true,
      features: ['15 إعلان شهرياً', 'دعم فني', 'إحصائيات متقدمة', 'شارة موثقة'],
    },
    stats: {
      totalAds: 25,
      activeAds: 18,
      expiredAds: 7,
      totalViews: 3200,
      totalContacts: 85,
      successfulDeals: 12,
    },
    businessInfo: {
      companyName: 'شركة التجارة المتقدمة',
      businessType: 'تجارة عامة',
      registrationNumber: 'REG123456',
      establishedYear: 2015,
      employeeCount: '10-50',
      address: {
        governorate: 'دمشق',
        city: 'دمشق',
        area: 'المزة',
        street: 'شارع الثورة',
      },
      contactPerson: {
        name: 'محمد أحمد',
        position: 'مدير عام',
        phone: '+963988654321',
        email: '<EMAIL>',
      },
    },
    settings: {
      language: 'ar' as const,
      notifications: {
        email: true,
        sms: true,
        push: true,
        marketing: true,
      },
      privacy: {
        showPhone: true,
        showEmail: true,
        allowMessages: true,
        showOnlineStatus: true,
      },
      preferences: {
        currency: 'SYP' as const,
        theme: 'light' as const,
        autoSave: true,
      },
    },
  },
  realEstateOffice: {
    id: 'user-office',
    email: '<EMAIL>',
    phone: '+963988789012',
    name: 'مكتب العقارات المتميز',
    userType: 'real-estate-office' as const,
    isVerified: true,
    createdAt: new Date('2021-03-20'),
    lastLogin: new Date(),
    subscription: {
      planId: 'real-estate-office',
      planName: 'باقة المكتب العقاري',
      planType: 'business' as const,
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-02-01'),
      isActive: true,
      autoRenew: true,
      features: ['30 إعلان شهرياً', 'شارة موثقة ذهبية', 'أولوية في النتائج', 'دعم فني مخصص', 'إحصائيات تفصيلية'],
    },
    stats: {
      totalAds: 45,
      activeAds: 32,
      expiredAds: 13,
      totalViews: 8500,
      totalContacts: 180,
      successfulDeals: 28,
    },
    realEstateOfficeInfo: {
      officeName: 'مكتب العقارات المتميز',
      licenseNumber: 'LIC789012',
      licenseIssueDate: new Date('2020-01-01'),
      licenseExpiryDate: new Date('2025-01-01'),
      ownerName: 'خالد السوري',
      specializations: ['شقق سكنية', 'فيلات', 'محلات تجارية', 'أراضي'],
      serviceAreas: ['دمشق', 'ريف دمشق', 'حلب'],
      yearsOfExperience: 15,
      teamSize: 8,
      address: {
        governorate: 'دمشق',
        city: 'دمشق',
        area: 'المزة',
        street: 'شارع المتنبي',
        building: 'بناء رقم 15',
        floor: 'الطابق الثاني',
      },
      workingHours: {
        sunday: { open: '09:00', close: '17:00', isOpen: true },
        monday: { open: '09:00', close: '17:00', isOpen: true },
        tuesday: { open: '09:00', close: '17:00', isOpen: true },
        wednesday: { open: '09:00', close: '17:00', isOpen: true },
        thursday: { open: '09:00', close: '17:00', isOpen: true },
        friday: { open: '09:00', close: '12:00', isOpen: true },
        saturday: { open: '09:00', close: '17:00', isOpen: true },
      },
    },
    settings: {
      language: 'ar' as const,
      notifications: {
        email: true,
        sms: true,
        push: true,
        marketing: true,
      },
      privacy: {
        showPhone: true,
        showEmail: true,
        allowMessages: true,
        showOnlineStatus: true,
      },
      preferences: {
        currency: 'SYP' as const,
        theme: 'light' as const,
        autoSave: true,
      },
    },
  },
};

export default function DemoAccountsPage() {
  const [selectedAccount, setSelectedAccount] = useState<'individual' | 'business' | 'realEstateOffice'>('individual');

  const currentUser = demoAccounts[selectedAccount];
  const userBadge = determineUserBadge(currentUser.userType, currentUser.subscription?.planId);

  const renderUserCard = (user: any) => (
    <div className="bg-white rounded-xl shadow-lg p-6">
      {/* بطاقة الترحيب */}
      <div className="bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl p-6 text-white mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">مرحباً، {user.name}!</h2>
            <p className="opacity-90">
              {user.userType === 'individual' && 'مستخدم فردي'}
              {user.userType === 'business' && 'حساب شركة'}
              {user.userType === 'real-estate-office' && 'مكتب عقاري'}
            </p>
            <p className="text-sm opacity-75 mt-1">
              عضو منذ {new Date(user.createdAt).toLocaleDateString('en-GB', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
              }).split('/').reverse().join('/')}
            </p>
          </div>
          <div className="flex items-center gap-3">
            <VerificationBadge
              type={userBadge.type}
              size="lg"
              showTooltip={false}
            />
            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <span className="text-2xl">
                {user.userType === 'individual' && '👤'}
                {user.userType === 'business' && '🏢'}
                {user.userType === 'real-estate-office' && '🏘️'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* الإحصائيات */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="bg-blue-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{user.stats.totalAds}</div>
          <div className="text-sm text-blue-800">إجمالي الإعلانات</div>
        </div>
        <div className="bg-green-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-600">{user.stats.activeAds}</div>
          <div className="text-sm text-green-800">الإعلانات النشطة</div>
        </div>
        <div className="bg-purple-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-purple-600">{user.stats.totalViews.toLocaleString()}</div>
          <div className="text-sm text-purple-800">إجمالي المشاهدات</div>
        </div>
        <div className="bg-orange-50 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-orange-600">{user.stats.totalContacts}</div>
          <div className="text-sm text-orange-800">جهات الاتصال</div>
        </div>
      </div>

      {/* معلومات الاشتراك */}
      {user.subscription ? (
        <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-xl">💎</span>
              </div>
              <div>
                <h4 className="text-lg font-bold text-primary-800">{user.subscription.planName}</h4>
                <p className="text-primary-600">
                  {user.subscription.planType === 'individual' ? 'باقة فردية' : 'باقة أعمال'}
                </p>
              </div>
            </div>
            <VerificationBadge type={userBadge.type} size="lg" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <span className="text-sm text-primary-600">تاريخ الانتهاء</span>
              <div className="font-semibold text-primary-800">
                {new Date(user.subscription.endDate).toLocaleDateString('en-GB', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit'
                }).split('/').reverse().join('/')}
              </div>
            </div>
            <div>
              <span className="text-sm text-primary-600">حالة الاشتراك</span>
              <div className="font-semibold text-primary-800">
                {user.subscription.isActive ? '✅ نشط' : '❌ غير نشط'}
              </div>
            </div>
          </div>

          <div>
            <h5 className="font-semibold text-primary-800 mb-2">مميزات الباقة:</h5>
            <div className="flex flex-wrap gap-2">
              {user.subscription.features.map((feature: string, index: number) => (
                <span
                  key={index}
                  className="px-3 py-1 bg-primary-200 text-primary-800 rounded-full text-sm"
                >
                  {feature}
                </span>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-gray-50 rounded-lg p-6 mb-6 text-center">
          <div className="text-4xl mb-2">💎</div>
          <h4 className="font-semibold text-gray-800 mb-2">الخطة المجانية</h4>
          <p className="text-gray-600 text-sm">5 إعلانات مجانية شهرياً</p>
        </div>
      )}

      {/* معلومات إضافية حسب نوع المستخدم */}
      {user.userType === 'individual' && user.individualInfo && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h5 className="font-semibold text-gray-800 mb-3">المعلومات الشخصية</h5>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">الاسم:</span>
              <div className="font-medium">{user.individualInfo.firstName} {user.individualInfo.lastName}</div>
            </div>
            <div>
              <span className="text-gray-600">المنطقة:</span>
              <div className="font-medium">{user.individualInfo.address?.area}, {user.individualInfo.address?.city}</div>
            </div>
          </div>
        </div>
      )}

      {user.userType === 'business' && user.businessInfo && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h5 className="font-semibold text-gray-800 mb-3">معلومات الشركة</h5>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">اسم الشركة:</span>
              <div className="font-medium">{user.businessInfo.companyName}</div>
            </div>
            <div>
              <span className="text-gray-600">نوع النشاط:</span>
              <div className="font-medium">{user.businessInfo.businessType}</div>
            </div>
            <div>
              <span className="text-gray-600">سنة التأسيس:</span>
              <div className="font-medium">{user.businessInfo.establishedYear}</div>
            </div>
            <div>
              <span className="text-gray-600">عدد الموظفين:</span>
              <div className="font-medium">{user.businessInfo.employeeCount}</div>
            </div>
          </div>
        </div>
      )}

      {user.userType === 'real-estate-office' && user.realEstateOfficeInfo && (
        <div className="bg-gray-50 rounded-lg p-4">
          <h5 className="font-semibold text-gray-800 mb-3">معلومات المكتب العقاري</h5>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">اسم المكتب:</span>
              <div className="font-medium">{user.realEstateOfficeInfo.officeName}</div>
            </div>
            <div>
              <span className="text-gray-600">رقم الترخيص:</span>
              <div className="font-medium">{user.realEstateOfficeInfo.licenseNumber}</div>
            </div>
            <div>
              <span className="text-gray-600">سنوات الخبرة:</span>
              <div className="font-medium">{user.realEstateOfficeInfo.yearsOfExperience} سنة</div>
            </div>
            <div>
              <span className="text-gray-600">حجم الفريق:</span>
              <div className="font-medium">{user.realEstateOfficeInfo.teamSize} أشخاص</div>
            </div>
          </div>
          <div className="mt-3">
            <span className="text-gray-600">التخصصات:</span>
            <div className="flex flex-wrap gap-1 mt-1">
              {user.realEstateOfficeInfo.specializations.map((spec: string, index: number) => (
                <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                  {spec}
                </span>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-4">عرض أنواع الحسابات</h1>
            <p className="text-gray-600">اختر نوع الحساب لمشاهدة كيف تبدو لوحة التحكم</p>
          </div>

          {/* أزرار اختيار نوع الحساب */}
          <div className="flex justify-center gap-4 mb-8">
            <button
              onClick={() => setSelectedAccount('individual')}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                selectedAccount === 'individual'
                  ? 'bg-primary-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              👤 حساب فردي
            </button>
            <button
              onClick={() => setSelectedAccount('business')}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                selectedAccount === 'business'
                  ? 'bg-primary-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              🏢 حساب شركة
            </button>
            <button
              onClick={() => setSelectedAccount('realEstateOffice')}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                selectedAccount === 'realEstateOffice'
                  ? 'bg-primary-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              🏘️ مكتب عقاري
            </button>
          </div>

          {/* عرض الحساب المختار */}
          {renderUserCard(currentUser)}
        </div>
      </main>

      <Footer />
    </div>
  );
}
