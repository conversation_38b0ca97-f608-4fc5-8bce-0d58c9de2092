'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import JobPostingForm from '@/components/JobPostingForm';
import ContactButtons from '@/components/ContactButtons';
import { JobPosting } from '@/lib/jobs';

export default function PostJobPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (jobData: Partial<JobPosting>) => {
    setIsLoading(true);
    try {
      // هنا سيتم حفظ الوظيفة في قاعدة البيانات
      console.log('Posting job:', jobData);

      // محاكاة عملية النشر
      await new Promise(resolve => setTimeout(resolve, 2000));

      // إعادة توجيه إلى لوحة تحكم الشركة
      router.push('/company/dashboard');
    } catch (error) {
      console.error('Error posting job:', error);
      alert('حدث خطأ أثناء نشر الوظيفة');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    if (confirm('هل أنت متأكد من إلغاء نشر الوظيفة؟ ستفقد جميع البيانات المدخلة.')) {
      router.push('/jobs');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            💼 نشر وظيفة جديدة
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            انشر وظيفتك واعثر على أفضل المواهب من آلاف المرشحين المؤهلين
          </p>
        </div>

        {/* Benefits for Companies */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-4xl mb-3">👥</div>
            <h3 className="font-semibold text-gray-800 mb-2">آلاف المرشحين</h3>
            <p className="text-gray-600 text-sm">
              وصول لقاعدة كبيرة من المواهب المؤهلة
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-4xl mb-3">🎯</div>
            <h3 className="font-semibold text-gray-800 mb-2">استهداف دقيق</h3>
            <p className="text-gray-600 text-sm">
              فلترة متقدمة للعثور على المرشح المثالي
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-4xl mb-3">⚡</div>
            <h3 className="font-semibold text-gray-800 mb-2">نشر سريع</h3>
            <p className="text-gray-600 text-sm">
              انشر وظيفتك في دقائق معدودة
            </p>
          </div>

          <div className="bg-white rounded-lg p-6 text-center shadow-sm">
            <div className="text-4xl mb-3">📊</div>
            <h3 className="font-semibold text-gray-800 mb-2">تحليلات مفصلة</h3>
            <p className="text-gray-600 text-sm">
              تتبع أداء إعلاناتك وإحصائيات التقديمات
            </p>
          </div>
        </div>

        {/* Job Posting Form */}
        {isLoading ? (
          <div className="bg-white rounded-xl shadow-lg p-12 text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">جاري نشر الوظيفة...</h3>
            <p className="text-gray-600">يرجى الانتظار قليلاً</p>
          </div>
        ) : (
          <JobPostingForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
          />
        )}

        {/* Pricing Plans */}
        <div className="mt-12 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8">
          <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            💎 خطط النشر والترقية
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Basic Plan */}
            <div className="bg-white rounded-xl p-6 shadow-md">
              <div className="text-center mb-6">
                <h4 className="text-xl font-bold text-gray-800 mb-2">الخطة الأساسية</h4>
                <div className="text-3xl font-bold text-gray-600 mb-2">مجاني</div>
                <p className="text-gray-600 text-sm">للشركات الصغيرة</p>
              </div>

              <ul className="space-y-3 mb-6">
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-sm">نشر وظيفة واحدة</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-sm">مدة النشر 30 يوم</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-sm">إدارة التقديمات</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-gray-400">✗</span>
                  <span className="text-sm text-gray-400">ترقية الإعلان</span>
                </li>
              </ul>

              <button className="w-full bg-gray-600 text-white py-3 rounded-lg font-semibold hover:bg-gray-700 transition-colors">
                البدء مجاناً
              </button>
            </div>

            {/* Premium Plan */}
            <div className="bg-white rounded-xl p-6 shadow-md border-2 border-primary-500 relative">
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                  الأكثر شعبية
                </span>
              </div>

              <div className="text-center mb-6">
                <h4 className="text-xl font-bold text-gray-800 mb-2">الخطة المميزة</h4>
                <div className="text-3xl font-bold text-primary-600 mb-2">50,000 ل.س</div>
                <p className="text-gray-600 text-sm">شهرياً</p>
              </div>

              <ul className="space-y-3 mb-6">
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-sm">5 وظائف شهرياً</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-sm">ترقية الإعلانات</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-sm">تحليلات متقدمة</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-sm">دعم أولوية</span>
                </li>
              </ul>

              <button className="w-full bg-primary-600 text-white py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors">
                اختر المميزة
              </button>
            </div>

            {/* Enterprise Plan */}
            <div className="bg-white rounded-xl p-6 shadow-md">
              <div className="text-center mb-6">
                <h4 className="text-xl font-bold text-gray-800 mb-2">خطة المؤسسات</h4>
                <div className="text-3xl font-bold text-purple-600 mb-2">150,000 ل.س</div>
                <p className="text-gray-600 text-sm">شهرياً</p>
              </div>

              <ul className="space-y-3 mb-6">
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-sm">وظائف غير محدودة</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-sm">شارة توثيق ذهبية</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-sm">مدير حساب مخصص</span>
                </li>
                <li className="flex items-center gap-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-sm">تقارير مخصصة</span>
                </li>
              </ul>

              <button className="w-full bg-purple-600 text-white py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors">
                تواصل معنا
              </button>
            </div>
          </div>
        </div>

        {/* Tips for Employers */}
        <div className="mt-12 bg-green-50 rounded-xl p-8">
          <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            💡 نصائح لجذب أفضل المواهب
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">اكتب عنوان واضح ومحدد</h4>
                  <p className="text-gray-600 text-sm">
                    استخدم مسميات وظيفية واضحة ومعروفة في السوق
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">حدد المتطلبات بدقة</h4>
                  <p className="text-gray-600 text-sm">
                    اذكر المهارات والخبرات المطلوبة بوضوح
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">اذكر المزايا والحوافز</h4>
                  <p className="text-gray-600 text-sm">
                    أبرز ما تقدمه الشركة من مزايا ومكافآت
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">كن شفافاً في الراتب</h4>
                  <p className="text-gray-600 text-sm">
                    ذكر نطاق الراتب يجذب المرشحين المناسبين
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">اعرض ثقافة الشركة</h4>
                  <p className="text-gray-600 text-sm">
                    وضح بيئة العمل وقيم الشركة
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <span className="text-green-500 text-xl">✓</span>
                <div>
                  <h4 className="font-semibold text-gray-800">رد بسرعة على التقديمات</h4>
                  <p className="text-gray-600 text-sm">
                    التواصل السريع يحسن سمعة الشركة
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
