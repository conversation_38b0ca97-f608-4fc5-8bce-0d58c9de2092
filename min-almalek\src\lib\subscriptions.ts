// نظام إدارة الاشتراكات والدفع
export interface SubscriptionPlan {
  id: string;
  name: string;
  nameAr: string;
  price: number;
  currency: 'SYP' | 'USD';
  duration: number; // بالأيام
  adsLimit: number;
  features: string[];
  featuresAr: string[];
  popular?: boolean;
  color: string;
}

export interface Payment {
  id: string;
  userId: number;
  planId: string;
  amount: number;
  currency: 'SYP' | 'USD';
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  method: 'bank_transfer' | 'mobile_payment' | 'cash' | 'crypto';
  transactionId?: string;
  receipt?: string;
  notes?: string;
  createdAt: string;
  completedAt?: string;
  expiresAt: string;
}

export interface Invoice {
  id: string;
  paymentId: string;
  userId: number;
  planId: string;
  amount: number;
  currency: 'SYP' | 'USD';
  tax: number;
  total: number;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  dueDate: string;
  createdAt: string;
  paidAt?: string;
}

// خطط الاشتراك المتاحة - محدثة بالأسعار الموحدة
export const subscriptionPlans: SubscriptionPlan[] = [
  {
    id: 'individual-free',
    name: 'Free Plan',
    nameAr: 'الخطة المجانية',
    price: 0,
    currency: 'SYP',
    duration: 30,
    adsLimit: 3,
    features: [
      '3 ads per month',
      'Basic support',
      'Standard listing'
    ],
    featuresAr: [
      '3 إعلانات مجانية شهرياً',
      'صور حتى 3 لكل إعلان',
      'مدة الإعلان 30 يوم',
      'دعم فني أساسي',
      'إحصائيات بسيطة'
    ],
    color: 'gray'
  },
  {
    id: 'individual-basic',
    name: 'Basic Plan',
    nameAr: 'الباقة الأساسية',
    price: 25000,
    currency: 'SYP',
    duration: 30,
    adsLimit: 5,
    features: [
      '5 ads per month',
      'Basic support',
      'Standard listing'
    ],
    featuresAr: [
      '5 إعلانات شهرياً',
      'عرض في النتائج العامة',
      'دعم فني أساسي',
      'إحصائيات بسيطة',
      'صور حتى 5 لكل إعلان',
      'مدة الإعلان 30 يوم'
    ],
    color: 'green'
  },
  {
    id: 'individual-premium',
    name: 'Premium Plan',
    nameAr: 'الباقة المميزة',
    price: 50000,
    currency: 'SYP',
    duration: 30,
    adsLimit: 15,
    features: [
      '15 featured ads per month',
      'Priority support',
      'Featured listing',
      'Analytics dashboard',
      'Multiple photos'
    ],
    featuresAr: [
      '15 إعلان شهرياً',
      'إعلانات مميزة',
      'أولوية في النتائج',
      'دعم فني متقدم',
      'إحصائيات مفصلة',
      'شارة "معلن مميز"',
      'صور حتى 10 لكل إعلان',
      'مدة الإعلان 30 يوم'
    ],
    popular: true,
    color: 'blue'
  },
  {
    id: 'individual-business',
    name: 'Business Plan',
    nameAr: 'باقة الأعمال',
    price: 100000,
    currency: 'SYP',
    duration: 30,
    adsLimit: 30,
    features: [
      '30 ads per month',
      'Premium support 24/7',
      'Top listing priority',
      'Advanced analytics',
      'Custom profile page',
      'Multi-user management'
    ],
    featuresAr: [
      '30 إعلان شهرياً',
      'إعلانات عاجلة',
      'أولوية قصوى',
      'دعم فني 24/7',
      'إحصائيات متقدمة',
      'صفحة معلن مخصصة',
      'إدارة متعددة المستخدمين',
      'صور حتى 15 لكل إعلان',
      'مدة الإعلان 30 يوم'
    ],
    color: 'purple'
  },
  // خطط الشركات الموحدة
  {
    id: 'business-starter',
    name: 'Business Starter',
    nameAr: 'خطة البداية',
    price: 500000,
    currency: 'SYP',
    duration: 30,
    adsLimit: 15,
    features: [
      '15 featured ads per month',
      'Company verification badge',
      'Basic company page',
      'Business support',
      'Priority in results'
    ],
    featuresAr: [
      '15 إعلان مميز شهرياً',
      'جميع الإعلانات عدا الوظائف',
      'صور حتى 15 لكل إعلان',
      'مدة الإعلان 30 يوم',
      'شارة "شركة موثقة"',
      'صفحة شركة أساسية',
      'إحصائيات مفصلة',
      'دعم فني للشركات',
      'أولوية في النتائج'
    ],
    color: 'blue'
  },
  {
    id: 'real-estate-office',
    name: 'Real Estate Office',
    nameAr: 'باقة المكاتب العقارية',
    price: 700000,
    currency: 'SYP',
    duration: 30,
    adsLimit: 30,
    features: [
      '30 real estate ads per month',
      'Real estate office badge',
      'Complete office page',
      'Real estate tools',
      'Market reports'
    ],
    featuresAr: [
      '30 إعلان عقاري مميز شهرياً',
      'إعلانات العقارات فقط',
      'صور حتى 20 لكل إعلان',
      'مدة الإعلان 30 يوم',
      'شارة "مكتب عقاري موثق" 🏘️',
      'صفحة مكتب عقاري متكاملة',
      'أدوات تقييم العقارات',
      'إحصائيات عقارية متقدمة',
      'تقارير السوق العقاري',
      'دعم فني متخصص للعقارات',
      'أولوية عالية في نتائج البحث'
    ],
    popular: true,
    color: 'orange'
  },
  {
    id: 'business-professional',
    name: 'Business Professional',
    nameAr: 'الخطة المهنية',
    price: 1000000,
    currency: 'SYP',
    duration: 30,
    adsLimit: 50,
    features: [
      '50 featured ads per month',
      'Premium company badge',
      'Advanced company page',
      'Dedicated support',
      'Weekly reports',
      'Urgent ads capability'
    ],
    featuresAr: [
      '50 إعلان مميز شهرياً',
      'جميع الإعلانات عدا الوظائف',
      'صور حتى 20 لكل إعلان',
      'مدة الإعلان 30 يوم',
      'أولوية عالية في النتائج',
      'شارة "شركة مميزة"',
      'صفحة شركة متقدمة',
      'إحصائيات متقدمة',
      'دعم فني مخصص',
      'إمكانية الإعلانات العاجلة',
      'تقارير أسبوعية',
      'إدارة متعددة المستخدمين'
    ],
    color: 'gold'
  }
];

// بيانات تجريبية للمدفوعات
const samplePayments: Payment[] = [
  {
    id: 'pay_1',
    userId: 1,
    planId: 'premium',
    amount: 150000,
    currency: 'SYP',
    status: 'completed',
    method: 'bank_transfer',
    transactionId: 'TXN_123456789',
    createdAt: '2024-01-15T10:30:00Z',
    completedAt: '2024-01-15T11:00:00Z',
    expiresAt: '2024-02-15T10:30:00Z'
  },
  {
    id: 'pay_2',
    userId: 2,
    planId: 'business-starter',
    amount: 500000,
    currency: 'SYP',
    status: 'pending',
    method: 'mobile_payment',
    createdAt: '2024-01-20T14:20:00Z',
    expiresAt: '2024-01-22T14:20:00Z'
  },
  {
    id: 'pay_3',
    userId: 3,
    planId: 'business-professional',
    amount: 1000000,
    currency: 'SYP',
    status: 'completed',
    method: 'bank_transfer',
    transactionId: 'TXN_987654321',
    createdAt: '2024-01-18T09:15:00Z',
    completedAt: '2024-01-18T10:30:00Z',
    expiresAt: '2024-02-18T09:15:00Z'
  }
];

export class SubscriptionService {
  // الحصول على جميع الخطط
  static getAllPlans(): SubscriptionPlan[] {
    return subscriptionPlans;
  }

  // الحصول على خطة بالمعرف
  static getPlanById(planId: string): SubscriptionPlan | null {
    return subscriptionPlans.find(plan => plan.id === planId) || null;
  }

  // إنشاء دفعة جديدة
  static async createPayment(
    userId: number,
    planId: string,
    method: Payment['method'],
    notes?: string
  ): Promise<{ success: boolean; data?: Payment; error?: string }> {
    try {
      const plan = this.getPlanById(planId);

      if (!plan) {
        return { success: false, error: 'خطة الاشتراك غير موجودة' };
      }

      if (plan.price === 0) {
        return { success: false, error: 'الخطة المجانية لا تحتاج دفع' };
      }

      const payment: Payment = {
        id: `pay_${Date.now()}`,
        userId,
        planId,
        amount: plan.price,
        currency: plan.currency,
        status: 'pending',
        method,
        notes,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString() // 48 ساعة
      };

      samplePayments.push(payment);
      return { success: true, data: payment };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في إنشاء الدفعة' };
    }
  }

  // تأكيد الدفعة
  static async confirmPayment(
    paymentId: string,
    transactionId: string,
    receipt?: string
  ): Promise<{ success: boolean; data?: Payment; error?: string }> {
    try {
      const payment = samplePayments.find(p => p.id === paymentId);

      if (!payment) {
        return { success: false, error: 'الدفعة غير موجودة' };
      }

      if (payment.status !== 'pending') {
        return { success: false, error: 'الدفعة تم تأكيدها مسبقاً أو ملغاة' };
      }

      // تحديث حالة الدفعة
      payment.status = 'completed';
      payment.transactionId = transactionId;
      payment.receipt = receipt;
      payment.completedAt = new Date().toISOString();

      // تحديث اشتراك المستخدم
      await this.updateUserSubscription(payment.userId, payment.planId);

      return { success: true, data: payment };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في تأكيد الدفعة' };
    }
  }

  // تحديث اشتراك المستخدم
  private static async updateUserSubscription(userId: number, planId: string): Promise<boolean> {
    try {
      const plan = this.getPlanById(planId);
      if (!plan) return false;

      // في التطبيق الحقيقي، ستحديث قاعدة البيانات
      // هنا نحاكي التحديث
      console.log(`تم تحديث اشتراك المستخدم ${userId} إلى خطة ${planId}`);

      return true;
    } catch (error) {
      return false;
    }
  }

  // إلغاء الدفعة
  static async cancelPayment(paymentId: string, userId: number): Promise<{ success: boolean; error?: string }> {
    try {
      const payment = samplePayments.find(p => p.id === paymentId && p.userId === userId);

      if (!payment) {
        return { success: false, error: 'الدفعة غير موجودة' };
      }

      if (payment.status !== 'pending') {
        return { success: false, error: 'لا يمكن إلغاء دفعة مكتملة' };
      }

      payment.status = 'failed';
      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في إلغاء الدفعة' };
    }
  }

  // الحصول على مدفوعات المستخدم
  static getUserPayments(userId: number): Payment[] {
    return samplePayments.filter(p => p.userId === userId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  // الحصول على دفعة بالمعرف
  static getPaymentById(paymentId: string): Payment | null {
    return samplePayments.find(p => p.id === paymentId) || null;
  }

  // حساب السعر مع الضريبة
  static calculatePriceWithTax(amount: number, taxRate: number = 0.1): { subtotal: number; tax: number; total: number } {
    const subtotal = amount;
    const tax = Math.round(amount * taxRate);
    const total = subtotal + tax;

    return { subtotal, tax, total };
  }

  // التحقق من انتهاء الاشتراك
  static isSubscriptionExpired(expiresAt: string): boolean {
    return new Date(expiresAt) < new Date();
  }

  // الحصول على الأيام المتبقية في الاشتراك
  static getDaysRemaining(expiresAt: string): number {
    const now = new Date();
    const expiry = new Date(expiresAt);
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return Math.max(0, diffDays);
  }

  // إنشاء فاتورة
  static createInvoice(payment: Payment): Invoice {
    const pricing = this.calculatePriceWithTax(payment.amount);

    return {
      id: `inv_${Date.now()}`,
      paymentId: payment.id,
      userId: payment.userId,
      planId: payment.planId,
      amount: pricing.subtotal,
      currency: payment.currency,
      tax: pricing.tax,
      total: pricing.total,
      status: payment.status === 'completed' ? 'paid' : 'sent',
      dueDate: payment.expiresAt,
      createdAt: payment.createdAt,
      paidAt: payment.completedAt
    };
  }

  // الحصول على إحصائيات الاشتراكات
  static getSubscriptionStats() {
    const totalPayments = samplePayments.length;
    const completedPayments = samplePayments.filter(p => p.status === 'completed').length;
    const pendingPayments = samplePayments.filter(p => p.status === 'pending').length;
    const totalRevenue = samplePayments
      .filter(p => p.status === 'completed')
      .reduce((sum, p) => sum + p.amount, 0);

    const planStats = subscriptionPlans.map(plan => ({
      planId: plan.id,
      name: plan.nameAr,
      subscribers: samplePayments.filter(p => p.planId === plan.id && p.status === 'completed').length,
      revenue: samplePayments
        .filter(p => p.planId === plan.id && p.status === 'completed')
        .reduce((sum, p) => sum + p.amount, 0)
    }));

    return {
      totalPayments,
      completedPayments,
      pendingPayments,
      totalRevenue,
      conversionRate: totalPayments > 0 ? (completedPayments / totalPayments) * 100 : 0,
      planStats
    };
  }

  // تجديد الاشتراك تلقائياً
  static async renewSubscription(userId: number, planId: string): Promise<{ success: boolean; data?: Payment; error?: string }> {
    try {
      // البحث عن آخر دفعة مكتملة للمستخدم
      const lastPayment = samplePayments
        .filter(p => p.userId === userId && p.status === 'completed')
        .sort((a, b) => new Date(b.completedAt!).getTime() - new Date(a.completedAt!).getTime())[0];

      if (!lastPayment) {
        return { success: false, error: 'لا توجد دفعات سابقة للتجديد' };
      }

      // إنشاء دفعة جديدة بنفس طريقة الدفع
      return await this.createPayment(userId, planId, lastPayment.method, 'تجديد تلقائي');
    } catch (error) {
      return { success: false, error: 'حدث خطأ في تجديد الاشتراك' };
    }
  }

  // إرجاع المبلغ
  static async refundPayment(paymentId: string, reason: string): Promise<{ success: boolean; error?: string }> {
    try {
      const payment = samplePayments.find(p => p.id === paymentId);

      if (!payment) {
        return { success: false, error: 'الدفعة غير موجودة' };
      }

      if (payment.status !== 'completed') {
        return { success: false, error: 'لا يمكن إرجاع مبلغ دفعة غير مكتملة' };
      }

      payment.status = 'refunded';
      payment.notes = `${payment.notes || ''}\nإرجاع المبلغ: ${reason}`;

      return { success: true };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في إرجاع المبلغ' };
    }
  }
}
