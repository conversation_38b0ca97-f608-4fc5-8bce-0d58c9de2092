// نظام المساعد الذكي
export interface AIMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: string;
  metadata?: {
    intent?: string;
    confidence?: number;
    suggestions?: string[];
    relatedAds?: any[];
  };
}

export interface AIContext {
  userId?: number;
  currentPage?: string;
  searchHistory?: string[];
  preferences?: {
    location?: string;
    category?: string;
    priceRange?: { min: number; max: number };
  };
  recentAds?: any[];
}

export class AIAssistant {
  private static conversationHistory: Map<string, AIMessage[]> = new Map();

  // تحليل نية المستخدم من الرسالة
  static analyzeIntent(message: string): { intent: string; confidence: number; entities: any } {
    const lowerMessage = message.toLowerCase();
    
    // البحث عن العقارات
    if (this.containsKeywords(lowerMessage, ['شقة', 'فيلا', 'محل', 'أرض', 'عقار', 'بيت', 'منزل'])) {
      const priceMatch = lowerMessage.match(/(\d+)\s*(مليون|ألف|لير)/);
      const locationMatch = lowerMessage.match(/(دمشق|حلب|حمص|اللاذقية|طرطوس|درعا|السويداء|القنيطرة|الحسكة|الرقة|دير الزور|إدلب|حماة|ريف دمشق)/);
      
      return {
        intent: 'property_search',
        confidence: 0.9,
        entities: {
          price: priceMatch ? priceMatch[0] : null,
          location: locationMatch ? locationMatch[0] : null,
          propertyType: this.extractPropertyType(lowerMessage)
        }
      };
    }

    // طلب المساعدة
    if (this.containsKeywords(lowerMessage, ['مساعدة', 'ساعدني', 'كيف', 'ماذا', 'أريد', 'أبحث'])) {
      return {
        intent: 'help_request',
        confidence: 0.8,
        entities: {}
      };
    }

    // الاستفسار عن الأسعار
    if (this.containsKeywords(lowerMessage, ['سعر', 'كم', 'تكلفة', 'ثمن', 'مبلغ'])) {
      return {
        intent: 'price_inquiry',
        confidence: 0.85,
        entities: {}
      };
    }

    // الاستفسار عن المواقع
    if (this.containsKeywords(lowerMessage, ['موقع', 'منطقة', 'حي', 'مكان', 'أين'])) {
      return {
        intent: 'location_inquiry',
        confidence: 0.8,
        entities: {}
      };
    }

    // التحية
    if (this.containsKeywords(lowerMessage, ['مرحبا', 'أهلا', 'السلام', 'صباح', 'مساء'])) {
      return {
        intent: 'greeting',
        confidence: 0.95,
        entities: {}
      };
    }

    return {
      intent: 'general',
      confidence: 0.5,
      entities: {}
    };
  }

  // توليد رد المساعد الذكي
  static generateResponse(message: string, context: AIContext = {}): AIMessage {
    const analysis = this.analyzeIntent(message);
    let response = '';
    let suggestions: string[] = [];
    let relatedAds: any[] = [];

    switch (analysis.intent) {
      case 'property_search':
        response = this.generatePropertySearchResponse(analysis.entities, context);
        suggestions = [
          'عرض الإعلانات المشابهة',
          'تحديد نطاق سعري',
          'البحث في منطقة محددة',
          'حفظ البحث للمتابعة'
        ];
        relatedAds = this.findRelatedAds(analysis.entities);
        break;

      case 'help_request':
        response = `مرحباً! أنا مساعدك الذكي في موقع "من المالك". يمكنني مساعدتك في:

🏠 البحث عن العقارات المناسبة
💰 معرفة الأسعار في المناطق المختلفة
📍 اختيار أفضل المواقع
📊 مقارنة الخيارات المتاحة
🔔 إعداد تنبيهات للإعلانات الجديدة

كيف يمكنني مساعدتك اليوم؟`;
        suggestions = [
          'أبحث عن شقة في دمشق',
          'ما هي أسعار الفلل؟',
          'أريد محل تجاري',
          'أفضل المناطق للاستثمار'
        ];
        break;

      case 'price_inquiry':
        response = this.generatePriceResponse(context);
        suggestions = [
          'أسعار الشقق في دمشق',
          'أسعار الفلل في حلب',
          'أسعار المحلات التجارية',
          'مقارنة الأسعار بين المناطق'
        ];
        break;

      case 'location_inquiry':
        response = this.generateLocationResponse(context);
        suggestions = [
          'أفضل أحياء دمشق',
          'المناطق الاستثمارية',
          'المناطق القريبة من الخدمات',
          'المناطق الهادئة للسكن'
        ];
        break;

      case 'greeting':
        response = `أهلاً وسهلاً بك في موقع "من المالك"! 👋

أنا مساعدك الذكي، وأنا هنا لمساعدتك في العثور على العقار المثالي. سواء كنت تبحث عن شقة للسكن، فيلا للعائلة، أو محل تجاري للاستثمار، يمكنني توجيهك للخيارات الأنسب.

ما نوع العقار الذي تبحث عنه؟`;
        suggestions = [
          'أبحث عن شقة',
          'أريد فيلا',
          'أبحث عن محل تجاري',
          'أريد أرض للبناء'
        ];
        break;

      default:
        response = `أعتذر، لم أفهم طلبك بوضوح. يمكنني مساعدتك في:

• البحث عن العقارات
• معرفة الأسعار
• اختيار المواقع المناسبة
• مقارنة الخيارات

هل يمكنك توضيح ما تبحث عنه بشكل أكثر تفصيلاً؟`;
        suggestions = [
          'أبحث عن عقار',
          'أريد معرفة الأسعار',
          'ساعدني في الاختيار',
          'ما هي الخيارات المتاحة؟'
        ];
    }

    return {
      id: Date.now().toString(),
      type: 'assistant',
      content: response,
      timestamp: new Date().toISOString(),
      metadata: {
        intent: analysis.intent,
        confidence: analysis.confidence,
        suggestions,
        relatedAds
      }
    };
  }

  // توليد رد للبحث عن العقارات
  private static generatePropertySearchResponse(entities: any, context: AIContext): string {
    let response = 'ممتاز! ';
    
    if (entities.propertyType) {
      response += `أفهم أنك تبحث عن ${entities.propertyType}. `;
    }
    
    if (entities.location) {
      response += `في منطقة ${entities.location}. `;
    }
    
    if (entities.price) {
      response += `بسعر حوالي ${entities.price}. `;
    }

    response += `\n\nبناءً على معايير البحث، إليك بعض التوصيات:

🎯 **الإعلانات المطابقة**: وجدت ${Math.floor(Math.random() * 15) + 5} إعلان مناسب
📊 **متوسط الأسعار**: ${this.getAveragePriceForType(entities.propertyType)}
⭐ **التوصية**: أنصحك بمراجعة الإعلانات المميزة أولاً

هل تريد مني تضييق نطاق البحث أكثر؟`;

    return response;
  }

  // توليد رد للاستفسار عن الأسعار
  private static generatePriceResponse(context: AIContext): string {
    return `إليك نظرة عامة على الأسعار الحالية في السوق:

🏠 **الشقق**:
• دمشق: 80-200 مليون ل.س
• حلب: 60-150 مليون ل.س
• حمص: 50-120 مليون ل.س

🏘️ **الفلل**:
• دمشق: 200-500 مليون ل.س
• حلب: 150-400 مليون ل.س
• حمص: 120-300 مليون ل.س

🏪 **المحلات التجارية**:
• المناطق التجارية: 100-300 مليون ل.س
• الأحياء السكنية: 50-150 مليون ل.س

💡 **نصيحة**: الأسعار تختلف حسب الموقع، المساحة، وحالة العقار. أنصحك بمقارنة عدة خيارات قبل اتخاذ القرار.`;
  }

  // توليد رد للاستفسار عن المواقع
  private static generateLocationResponse(context: AIContext): string {
    return `إليك دليل المناطق الأكثر طلباً:

🌟 **دمشق**:
• المزة: منطقة راقية، قريبة من الخدمات
• الشعلان: منطقة حيوية، مواصلات ممتازة
• جرمانا: أسعار معقولة، مناسبة للعائلات

🏙️ **حلب**:
• الفرقان: منطقة حديثة ومتطورة
• الحمدانية: منطقة تجارية نشطة
• الشهباء: أحياء سكنية هادئة

🏖️ **اللاذقية**:
• الكورنيش: إطلالة بحرية رائعة
• الزراعة: منطقة سكنية مميزة
• الرمل الجنوبي: قريبة من الشاطئ

أي منطقة تهمك أكثر؟`;
  }

  // البحث عن إعلانات ذات صلة
  private static findRelatedAds(entities: any): any[] {
    // في التطبيق الحقيقي، سيتم البحث في قاعدة البيانات
    return [
      {
        id: 1,
        title: 'شقة مميزة في المزة',
        price: 120000000,
        location: 'دمشق - المزة'
      },
      {
        id: 2,
        title: 'فيلا فاخرة في دمشق الجديدة',
        price: 250000000,
        location: 'دمشق - دمشق الجديدة'
      }
    ];
  }

  // مساعدة في البحث
  private static containsKeywords(text: string, keywords: string[]): boolean {
    return keywords.some(keyword => text.includes(keyword));
  }

  // استخراج نوع العقار
  private static extractPropertyType(text: string): string | null {
    if (text.includes('شقة')) return 'شقة';
    if (text.includes('فيلا')) return 'فيلا';
    if (text.includes('محل')) return 'محل تجاري';
    if (text.includes('أرض')) return 'أرض';
    if (text.includes('بيت') || text.includes('منزل')) return 'منزل';
    return null;
  }

  // الحصول على متوسط الأسعار
  private static getAveragePriceForType(propertyType: string | null): string {
    const averages = {
      'شقة': '100-150 مليون ل.س',
      'فيلا': '250-400 مليون ل.س',
      'محل تجاري': '80-200 مليون ل.س',
      'أرض': '50-150 مليون ل.س',
      'منزل': '120-200 مليون ل.س'
    };
    
    return averages[propertyType as keyof typeof averages] || '80-200 مليون ل.س';
  }

  // حفظ المحادثة
  static saveConversation(sessionId: string, messages: AIMessage[]): void {
    this.conversationHistory.set(sessionId, messages);
  }

  // استرجاع المحادثة
  static getConversation(sessionId: string): AIMessage[] {
    return this.conversationHistory.get(sessionId) || [];
  }

  // توليد توصيات ذكية
  static generateSmartRecommendations(context: AIContext): string[] {
    const recommendations = [];
    
    if (context.preferences?.location) {
      recommendations.push(`إعلانات جديدة في ${context.preferences.location}`);
    }
    
    if (context.preferences?.category) {
      recommendations.push(`${context.preferences.category} مشابهة لاهتماماتك`);
    }
    
    if (context.searchHistory?.length) {
      recommendations.push('إعلانات بناءً على عمليات البحث السابقة');
    }
    
    recommendations.push(
      'العقارات الأكثر مشاهدة هذا الأسبوع',
      'إعلانات بأسعار مخفضة',
      'عقارات في مناطق استثمارية واعدة'
    );
    
    return recommendations.slice(0, 4);
  }
}
