'use client';

import { useState, useEffect, createContext, useContext, ReactNode } from 'react';

interface ModalNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info' | 'welcome' | 'confirmation' | 'logout';
  title: string;
  message: string;
  icon?: string;
  primaryAction?: {
    text: string;
    action: () => void;
    style?: 'primary' | 'success' | 'danger' | 'warning';
  };
  secondaryAction?: {
    text: string;
    action: () => void;
  };
  autoClose?: boolean;
  duration?: number;
  backdrop?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

interface ModalContextType {
  showModal: (modal: Omit<ModalNotification, 'id'>) => void;
  showConfirmation: (
    title: string, 
    message: string, 
    onConfirm: () => void, 
    onCancel?: () => void
  ) => void;
  showWelcomeModal: (userName: string, onComplete: () => void) => void;
  showLogoutConfirmation: (onConfirm: () => void) => void;
  closeModal: (id?: string) => void;
  closeAllModals: () => void;
}

const ModalContext = createContext<ModalContextType | undefined>(undefined);

export const useModal = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

interface ModalProviderProps {
  children: ReactNode;
}

export function ModalProvider({ children }: ModalProviderProps) {
  const [modals, setModals] = useState<ModalNotification[]>([]);

  const showModal = (modalData: Omit<ModalNotification, 'id'>) => {
    const newModal: ModalNotification = {
      ...modalData,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9)
    };

    setModals(prev => [...prev, newModal]);

    // إغلاق تلقائي إذا كان مطلوباً
    if (modalData.autoClose && modalData.duration) {
      setTimeout(() => {
        closeModal(newModal.id);
      }, modalData.duration);
    }
  };

  const closeModal = (id?: string) => {
    if (id) {
      setModals(prev => prev.filter(modal => modal.id !== id));
    } else {
      // إغلاق آخر modal
      setModals(prev => prev.slice(0, -1));
    }
  };

  const closeAllModals = () => {
    setModals([]);
  };

  const showConfirmation = (
    title: string, 
    message: string, 
    onConfirm: () => void, 
    onCancel?: () => void
  ) => {
    showModal({
      type: 'confirmation',
      title,
      message,
      icon: '❓',
      primaryAction: {
        text: 'تأكيد',
        action: () => {
          onConfirm();
          closeModal();
        },
        style: 'primary'
      },
      secondaryAction: {
        text: 'إلغاء',
        action: () => {
          onCancel?.();
          closeModal();
        }
      },
      backdrop: true,
      size: 'md'
    });
  };

  const showWelcomeModal = (userName: string, onComplete: () => void) => {
    showModal({
      type: 'welcome',
      title: `مرحباً بك ${userName}! 🎉`,
      message: 'نحن سعداء لانضمامك إلى منصة من المالك. دعنا نساعدك في إعداد حسابك للحصول على أفضل تجربة.',
      icon: '🎉',
      primaryAction: {
        text: 'ابدأ الإعداد',
        action: () => {
          onComplete();
          closeModal();
        },
        style: 'success'
      },
      secondaryAction: {
        text: 'لاحقاً',
        action: () => closeModal()
      },
      backdrop: true,
      size: 'lg'
    });
  };

  const showLogoutConfirmation = (onConfirm: () => void) => {
    showModal({
      type: 'logout',
      title: 'تأكيد تسجيل الخروج',
      message: 'هل أنت متأكد من أنك تريد تسجيل الخروج من حسابك؟',
      icon: '👋',
      primaryAction: {
        text: 'تسجيل الخروج',
        action: () => {
          onConfirm();
          closeModal();
        },
        style: 'danger'
      },
      secondaryAction: {
        text: 'البقاء متصلاً',
        action: () => closeModal()
      },
      backdrop: true,
      size: 'md'
    });
  };

  const value: ModalContextType = {
    showModal,
    showConfirmation,
    showWelcomeModal,
    showLogoutConfirmation,
    closeModal,
    closeAllModals
  };

  // إغلاق المودال بالضغط على Escape
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && modals.length > 0) {
        closeModal();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [modals.length]);

  return (
    <ModalContext.Provider value={value}>
      {children}
      
      {/* عرض المودالات */}
      {modals.map((modal, index) => (
        <ModalComponent
          key={modal.id}
          modal={modal}
          onClose={() => closeModal(modal.id)}
          zIndex={1000 + index}
        />
      ))}
    </ModalContext.Provider>
  );
}

interface ModalComponentProps {
  modal: ModalNotification;
  onClose: () => void;
  zIndex: number;
}

function ModalComponent({ modal, onClose, zIndex }: ModalComponentProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // إظهار المودال مع تأخير بسيط للحصول على تأثير الانزلاق
    const timer = setTimeout(() => setIsVisible(true), 50);
    return () => clearTimeout(timer);
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 200); // انتظار انتهاء الانيميشن
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && modal.backdrop) {
      handleClose();
    }
  };

  const getTypeStyles = () => {
    switch (modal.type) {
      case 'success':
      case 'welcome':
        return {
          container: 'bg-green-50/95 backdrop-blur-md border-green-200',
          header: 'text-green-800',
          icon: 'text-green-600',
          message: 'text-green-700'
        };
      
      case 'error':
        return {
          container: 'bg-red-50/95 backdrop-blur-md border-red-200',
          header: 'text-red-800',
          icon: 'text-red-600',
          message: 'text-red-700'
        };
      
      case 'warning':
        return {
          container: 'bg-orange-50/95 backdrop-blur-md border-orange-200',
          header: 'text-orange-800',
          icon: 'text-orange-600',
          message: 'text-orange-700'
        };
      
      case 'info':
      case 'confirmation':
        return {
          container: 'bg-blue-50/95 backdrop-blur-md border-blue-200',
          header: 'text-blue-800',
          icon: 'text-blue-600',
          message: 'text-blue-700'
        };
      
      case 'logout':
        return {
          container: 'bg-gray-50/95 backdrop-blur-md border-gray-200',
          header: 'text-gray-800',
          icon: 'text-gray-600',
          message: 'text-gray-700'
        };
      
      default:
        return {
          container: 'bg-white/95 backdrop-blur-md border-gray-200',
          header: 'text-gray-800',
          icon: 'text-gray-600',
          message: 'text-gray-700'
        };
    }
  };

  const getSizeClasses = () => {
    switch (modal.size) {
      case 'sm': return 'max-w-sm';
      case 'md': return 'max-w-md';
      case 'lg': return 'max-w-lg';
      case 'xl': return 'max-w-xl';
      default: return 'max-w-md';
    }
  };

  const getButtonStyle = (style?: string) => {
    switch (style) {
      case 'primary':
        return 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500';
      case 'success':
        return 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500';
      case 'danger':
        return 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500';
      case 'warning':
        return 'bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500';
      default:
        return 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500';
    }
  };

  const styles = getTypeStyles();

  return (
    <div
      className={`
        fixed inset-0 flex items-center justify-center p-4 transition-all duration-200
        ${isVisible ? 'opacity-100' : 'opacity-0'}
      `}
      style={{ zIndex }}
      onClick={handleBackdropClick}
    >
      {/* الخلفية المظلمة */}
      <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />
      
      {/* المودال */}
      <div
        className={`
          relative w-full ${getSizeClasses()} transform transition-all duration-200
          ${isVisible ? 'scale-100 translate-y-0' : 'scale-95 translate-y-4'}
        `}
      >
        <div className={`
          rounded-2xl border shadow-2xl ${styles.container}
          ${modal.type === 'success' || modal.type === 'welcome' ? 'shadow-green-500/20' :
            modal.type === 'error' ? 'shadow-red-500/20' :
            modal.type === 'warning' ? 'shadow-orange-500/20' : 'shadow-blue-500/20'}
        `}>
          {/* الرأس */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200/50">
            <div className="flex items-center gap-3">
              {modal.icon && (
                <span className={`text-3xl ${styles.icon}`}>
                  {modal.icon}
                </span>
              )}
              <h3 className={`text-xl font-bold ${styles.header}`}>
                {modal.title}
              </h3>
            </div>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* المحتوى */}
          <div className="p-6">
            <p className={`text-lg leading-relaxed ${styles.message}`}>
              {modal.message}
            </p>
          </div>

          {/* الأزرار */}
          {(modal.primaryAction || modal.secondaryAction) && (
            <div className="flex items-center justify-end gap-3 p-6 border-t border-gray-200/50">
              {modal.secondaryAction && (
                <button
                  onClick={modal.secondaryAction.action}
                  className="px-6 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
                >
                  {modal.secondaryAction.text}
                </button>
              )}
              {modal.primaryAction && (
                <button
                  onClick={modal.primaryAction.action}
                  className={`
                    px-6 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2
                    ${getButtonStyle(modal.primaryAction.style)}
                  `}
                >
                  {modal.primaryAction.text}
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
