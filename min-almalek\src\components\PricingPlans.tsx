'use client';

import Link from 'next/link';
import { INDIVIDUAL_PLANS, BUSINESS_PLANS, formatPrice } from '@/lib/pricing';
import { BadgeInfo } from '@/components/VerificationBadge';
import { INDIVIDUAL_BADGES, BUSINESS_BADGES, SPECIAL_BADGES } from '@/lib/verification';
import StarRating from './StarRating';
import NewVerificationBadge from './NewVerificationBadge';
import MembershipBadge from './MembershipBadge';

const PricingPlans = () => {
  // استخدام الخطط الموحدة من pricing.ts
  const individualPlans = INDIVIDUAL_PLANS;
  const businessPlans = BUSINESS_PLANS;

  return (
    <div className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-800 mb-4">خطط الاشتراك</h2>
          <p className="text-xl text-gray-600">اختر الخطة المناسبة لك واحصل على المزيد من المشاهدات</p>
        </div>

        {/* باقات الأفراد */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">باقات الأفراد</h3>
          <div className="flex flex-wrap justify-center gap-4">
            {individualPlans.map((plan) => (
              <div
                key={plan.id}
                className={`bg-white rounded-xl shadow-md p-4 transition-all duration-300 relative w-80 ${
                  plan.popular ? 'ring-2 ring-primary-500 transform scale-105' : 'hover:shadow-lg'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                      الأكثر شعبية ⭐
                    </span>
                  </div>
                )}

                <div className="text-center mb-4">
                  {/* شعار الباقة */}
                  {plan.icon && plan.icon.startsWith('/images/') && (
                    <div className="mb-3">
                      <img
                        src={plan.icon}
                        alt={plan.name}
                        className="w-12 h-12 mx-auto object-contain"
                      />
                    </div>
                  )}

                  <h3 className="text-lg font-bold text-gray-800 mb-2">{plan.name}</h3>
                  <div className="text-2xl font-bold text-primary-600 mb-1">
                    {formatPrice(plan.price, plan.currency)}
                  </div>
                  <div className="text-gray-500 mb-3 text-sm">{plan.period}</div>

                  {/* عرض الشارات الجديدة وهوية العضوية المصغرة */}
                  <div className="flex flex-col items-center gap-2 mb-3">
                    <NewVerificationBadge
                      userType="individual"
                      planId={plan.id}
                      size="sm"
                      showTooltip={true}
                    />
                    <MembershipBadge
                      planId={plan.id}
                      size="lg"
                      showLabel={false}
                    />
                  </div>
                </div>

                <ul className="space-y-1 mb-4">
                  {plan.features.slice(0, 3).map((feature, index) => (
                    <li key={index} className="flex items-center text-gray-700 text-xs">
                      <span className="text-green-500 mr-2">✓</span>
                      {feature}
                    </li>
                  ))}
                  {plan.limitations?.slice(0, 1).map((limitation, index) => (
                    <li key={index} className="flex items-center text-gray-500 text-xs">
                      <span className="text-red-500 mr-2">✗</span>
                      {limitation}
                    </li>
                  ))}
                </ul>

                {plan.price === '0' ? (
                  <Link
                    href="/subscription"
                    className="w-full py-2 rounded-lg font-medium bg-green-600 text-white hover:bg-green-700 transition-colors text-center block text-sm"
                  >
                    البدء مجاناً
                  </Link>
                ) : (
                  <Link
                    href="/subscription"
                    className={`w-full py-2 rounded-lg font-medium transition-colors text-center block text-sm ${
                      plan.popular
                        ? 'bg-primary-600 text-white hover:bg-primary-700'
                        : 'bg-primary-600 text-white hover:bg-primary-700'
                    }`}
                  >
                    اختيار الباقة
                  </Link>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* باقات الشركات والمكاتب العقارية */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-gray-800 mb-6 text-center">باقات الشركات والمكاتب العقارية</h3>
          <div className="flex flex-wrap justify-center gap-4">
            {businessPlans.map((plan) => (
              <div
                key={plan.id}
                className={`bg-white rounded-xl shadow-md p-4 transition-all duration-300 relative w-72 ${
                  plan.popular ? 'ring-2 ring-primary-500 transform scale-105' : 'hover:shadow-lg'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                      الأكثر شعبية ⭐
                    </span>
                  </div>
                )}

                <div className="text-center mb-4">
                  {/* شعار الباقة */}
                  {plan.icon && plan.icon.startsWith('/images/') && (
                    <div className="mb-3">
                      <img
                        src={plan.icon}
                        alt={plan.name}
                        className="w-10 h-10 mx-auto object-contain"
                      />
                    </div>
                  )}

                  <h3 className="text-sm font-bold text-gray-800 mb-2">{plan.name}</h3>
                  <div className="text-lg font-bold text-primary-600 mb-1">
                    {formatPrice(plan.price, plan.currency)}
                  </div>
                  <div className="text-gray-500 mb-2 text-xs">{plan.period}</div>

                  {/* عرض الشارات الجديدة وهوية العضوية المصغرة */}
                  <div className="flex flex-col items-center gap-2 mb-3">
                    <NewVerificationBadge
                      userType={plan.id === 'real-estate-office' ? 'real-estate-office' : 'business'}
                      planId={plan.id}
                      size="xs"
                      showTooltip={true}
                    />
                    <MembershipBadge
                      planId={plan.id}
                      size="md"
                      showLabel={false}
                    />
                  </div>
                </div>

                <ul className="space-y-1 mb-4">
                  {plan.features.slice(0, 2).map((feature, index) => (
                    <li key={index} className="flex items-center text-gray-700 text-xs">
                      <span className="text-green-500 mr-1">✓</span>
                      {feature}
                    </li>
                  ))}
                </ul>

                {plan.price === 'حسب الطلب' ? (
                  <button
                    className="w-full py-2 rounded-lg font-medium bg-purple-600 text-white hover:bg-purple-700 transition-colors text-sm"
                    onClick={() => {
                      const message = `🔔 استفسار عن خطة مخصصة

🏢 طلب خطة مخصصة للشركات:
• نوع الطلب: خطة مخصصة
• نوع الحساب: شركة
• احتياجات خاصة: نعم

أريد الاستفسار عن خطة مخصصة تناسب احتياجات شركتنا. يرجى التواصل معي لمناقشة التفاصيل والأسعار.

شكراً لكم 🙏`;
                      window.open(`https://wa.me/963988652401?text=${encodeURIComponent(message)}`, '_blank');
                    }}
                  >
                    تواصل معنا
                  </button>
                ) : (
                  <Link
                    href="/subscription"
                    className="w-full py-2 rounded-lg font-medium bg-primary-600 text-white hover:bg-primary-700 transition-colors text-center block text-sm"
                  >
                    اختيار الباقة
                  </Link>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* شارات التوثيق */}
        <div className="mt-16">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-800 mb-4">🏆 شارات التوثيق والمصداقية</h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              احصل على شارات التوثيق التي تزيد من ثقة العملاء وتميزك عن المنافسين
            </p>
          </div>

          {/* شارات الأفراد */}
          <div className="mb-12">
            <h4 className="text-2xl font-bold text-gray-800 mb-6 text-center">🧑‍💼 شارات الأفراد</h4>
            <div className="flex flex-wrap justify-center gap-6">
              {INDIVIDUAL_BADGES.map((badge) => (
                <div key={badge.id} className="w-80">
                  <BadgeInfo badgeId={badge.id} />
                </div>
              ))}
            </div>
          </div>

          {/* شارات الشركات */}
          <div className="mb-12">
            <h4 className="text-2xl font-bold text-gray-800 mb-6 text-center">🏢 شارات الشركات</h4>
            <div className="flex flex-wrap justify-center gap-6">
              {BUSINESS_BADGES.map((badge) => (
                <div key={badge.id} className="w-80">
                  <BadgeInfo badgeId={badge.id} />
                </div>
              ))}
            </div>
          </div>

          {/* شارات المكاتب العقارية */}
          <div className="mb-12">
            <h4 className="text-2xl font-bold text-gray-800 mb-6 text-center">🏠 شارات المكاتب العقارية</h4>
            <div className="flex flex-wrap justify-center gap-6">
              {SPECIAL_BADGES.map((badge) => (
                <div key={badge.id} className="w-80">
                  <BadgeInfo badgeId={badge.id} />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* دعوة للاشتراك */}
        <div className="mt-16 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-lg p-8 border border-blue-200">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-gray-800 mb-4">🚀 جاهز للبدء؟</h3>
            <p className="text-gray-600 mb-6">اختر الخطة المناسبة لك واشترك الآن للحصول على جميع المميزات</p>

            <Link
              href="/subscription"
              className="inline-flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-lg hover:from-primary-600 hover:to-primary-700 transition-all font-semibold text-lg shadow-lg hover:shadow-xl"
            >
              <span>💎</span>
              <span>ابدأ الاشتراك الآن</span>
              <span>←</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingPlans;
