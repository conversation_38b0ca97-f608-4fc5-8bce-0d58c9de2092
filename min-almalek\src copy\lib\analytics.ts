// نظام التقارير والإحصائيات المتقدم
import { Ad, User, SearchFilters } from './data';

export interface AnalyticsData {
  totalAds: number;
  activeAds: number;
  totalUsers: number;
  totalViews: number;
  totalMessages: number;
  conversionRate: number;
  averagePrice: number;
  topCategories: CategoryStats[];
  topLocations: LocationStats[];
  userGrowth: GrowthData[];
  adGrowth: GrowthData[];
  revenueData: RevenueData[];
}

export interface CategoryStats {
  category: string;
  name: string;
  count: number;
  percentage: number;
  averagePrice: number;
  totalViews: number;
}

export interface LocationStats {
  governorate: string;
  name: string;
  count: number;
  percentage: number;
  averagePrice: number;
  totalViews: number;
}

export interface GrowthData {
  period: string;
  count: number;
  growth: number;
}

export interface RevenueData {
  period: string;
  revenue: number;
  subscriptions: number;
  featuredAds: number;
}

export interface UserAnalytics {
  userId: number;
  totalAds: number;
  activeAds: number;
  totalViews: number;
  totalMessages: number;
  totalFavorites: number;
  conversionRate: number;
  averageAdDuration: number;
  topPerformingAds: AdPerformance[];
  categoryBreakdown: CategoryBreakdown[];
  monthlyStats: MonthlyStats[];
}

export interface AdPerformance {
  adId: number;
  title: string;
  views: number;
  messages: number;
  favorites: number;
  conversionRate: number;
  daysActive: number;
}

export interface CategoryBreakdown {
  category: string;
  count: number;
  views: number;
  messages: number;
}

export interface MonthlyStats {
  month: string;
  ads: number;
  views: number;
  messages: number;
}

export class AnalyticsService {
  // الحصول على إحصائيات الموقع العامة
  static getSiteAnalytics(period: 'week' | 'month' | 'year' = 'month'): AnalyticsData {
    // في التطبيق الحقيقي، ستجلب البيانات من قاعدة البيانات
    const mockData: AnalyticsData = {
      totalAds: 15847,
      activeAds: 12634,
      totalUsers: 8923,
      totalViews: 234567,
      totalMessages: 45678,
      conversionRate: 12.5,
      averagePrice: 45000000,
      topCategories: [
        {
          category: 'real-estate',
          name: 'العقارات',
          count: 6234,
          percentage: 39.3,
          averagePrice: 85000000,
          totalViews: 98765
        },
        {
          category: 'cars',
          name: 'السيارات',
          count: 4567,
          percentage: 28.8,
          averagePrice: 25000000,
          totalViews: 76543
        },
        {
          category: 'electronics',
          name: 'الإلكترونيات',
          count: 2345,
          percentage: 14.8,
          averagePrice: 2500000,
          totalViews: 34567
        },
        {
          category: 'jobs',
          name: 'الوظائف',
          count: 1234,
          percentage: 7.8,
          averagePrice: 800000,
          totalViews: 23456
        },
        {
          category: 'services',
          name: 'الخدمات',
          count: 987,
          percentage: 6.2,
          averagePrice: 500000,
          totalViews: 12345
        },
        {
          category: 'fashion',
          name: 'الأزياء',
          count: 480,
          percentage: 3.1,
          averagePrice: 150000,
          totalViews: 8765
        }
      ],
      topLocations: [
        {
          governorate: 'damascus',
          name: 'دمشق',
          count: 5234,
          percentage: 33.0,
          averagePrice: 65000000,
          totalViews: 87654
        },
        {
          governorate: 'aleppo',
          name: 'حلب',
          count: 3456,
          percentage: 21.8,
          averagePrice: 45000000,
          totalViews: 65432
        },
        {
          governorate: 'homs',
          name: 'حمص',
          count: 2345,
          percentage: 14.8,
          averagePrice: 35000000,
          totalViews: 43210
        },
        {
          governorate: 'latakia',
          name: 'اللاذقية',
          count: 1876,
          percentage: 11.8,
          averagePrice: 40000000,
          totalViews: 32109
        },
        {
          governorate: 'hama',
          name: 'حماة',
          count: 1234,
          percentage: 7.8,
          averagePrice: 30000000,
          totalViews: 21098
        }
      ],
      userGrowth: this.generateGrowthData('users', period),
      adGrowth: this.generateGrowthData('ads', period),
      revenueData: this.generateRevenueData(period)
    };

    return mockData;
  }

  // الحصول على إحصائيات المستخدم
  static getUserAnalytics(userId: number, period: 'month' | 'year' = 'month'): UserAnalytics {
    // في التطبيق الحقيقي، ستحسب البيانات من قاعدة البيانات
    const mockData: UserAnalytics = {
      userId,
      totalAds: 12,
      activeAds: 8,
      totalViews: 1250,
      totalMessages: 45,
      totalFavorites: 89,
      conversionRate: 3.6,
      averageAdDuration: 28,
      topPerformingAds: [
        {
          adId: 1,
          title: 'شقة للبيع في دمشق - المالكي',
          views: 245,
          messages: 8,
          favorites: 18,
          conversionRate: 3.3,
          daysActive: 15
        },
        {
          adId: 3,
          title: 'iPhone 15 Pro Max جديد',
          views: 312,
          messages: 12,
          favorites: 28,
          conversionRate: 3.8,
          daysActive: 8
        },
        {
          adId: 5,
          title: 'فيلا للإيجار في حمص',
          views: 98,
          messages: 3,
          favorites: 6,
          conversionRate: 3.1,
          daysActive: 22
        }
      ],
      categoryBreakdown: [
        {
          category: 'real-estate',
          count: 5,
          views: 680,
          messages: 23
        },
        {
          category: 'electronics',
          count: 4,
          views: 420,
          messages: 15
        },
        {
          category: 'cars',
          count: 3,
          views: 150,
          messages: 7
        }
      ],
      monthlyStats: this.generateMonthlyStats(period)
    };

    return mockData;
  }

  // توليد بيانات النمو
  private static generateGrowthData(type: 'users' | 'ads', period: string): GrowthData[] {
    const data: GrowthData[] = [];
    const baseCount = type === 'users' ? 1000 : 2000;
    
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      
      const count = baseCount + Math.floor(Math.random() * 500) + (11 - i) * 100;
      const previousCount = i === 11 ? baseCount : data[data.length - 1]?.count || baseCount;
      const growth = previousCount > 0 ? ((count - previousCount) / previousCount) * 100 : 0;
      
      data.push({
        period: date.toLocaleDateString('ar-SY', { year: 'numeric', month: 'short' }),
        count,
        growth: Math.round(growth * 10) / 10
      });
    }
    
    return data;
  }

  // توليد بيانات الإيرادات
  private static generateRevenueData(period: string): RevenueData[] {
    const data: RevenueData[] = [];
    
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      
      const subscriptions = Math.floor(Math.random() * 50) + 20;
      const featuredAds = Math.floor(Math.random() * 100) + 50;
      const revenue = (subscriptions * 150000) + (featuredAds * 50000);
      
      data.push({
        period: date.toLocaleDateString('ar-SY', { year: 'numeric', month: 'short' }),
        revenue,
        subscriptions,
        featuredAds
      });
    }
    
    return data;
  }

  // توليد الإحصائيات الشهرية
  private static generateMonthlyStats(period: string): MonthlyStats[] {
    const data: MonthlyStats[] = [];
    
    for (let i = 11; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      
      data.push({
        month: date.toLocaleDateString('ar-SY', { year: 'numeric', month: 'short' }),
        ads: Math.floor(Math.random() * 5) + 1,
        views: Math.floor(Math.random() * 200) + 50,
        messages: Math.floor(Math.random() * 10) + 2
      });
    }
    
    return data;
  }

  // تحليل أداء الإعلان
  static getAdAnalytics(adId: number): AdPerformance | null {
    // في التطبيق الحقيقي، ستجلب البيانات من قاعدة البيانات
    return {
      adId,
      title: 'شقة للبيع في دمشق - المالكي',
      views: 245,
      messages: 8,
      favorites: 18,
      conversionRate: 3.3,
      daysActive: 15
    };
  }

  // تحليل الكلمات المفتاحية
  static getKeywordAnalytics(): { keyword: string; searches: number; results: number }[] {
    return [
      { keyword: 'شقة دمشق', searches: 1234, results: 567 },
      { keyword: 'سيارة BMW', searches: 987, results: 234 },
      { keyword: 'iPhone', searches: 876, results: 123 },
      { keyword: 'فيلا حلب', searches: 654, results: 89 },
      { keyword: 'لابتوب', searches: 543, results: 156 }
    ];
  }

  // تحليل سلوك المستخدمين
  static getUserBehaviorAnalytics() {
    return {
      averageSessionDuration: 8.5, // دقائق
      averagePagesPerSession: 4.2,
      bounceRate: 35.7, // نسبة مئوية
      topPages: [
        { page: '/', views: 12345, avgTime: 2.3 },
        { page: '/ads', views: 8765, avgTime: 5.7 },
        { page: '/search', views: 6543, avgTime: 3.2 },
        { page: '/post-ad', views: 4321, avgTime: 8.9 },
        { page: '/dashboard', views: 3210, avgTime: 6.4 }
      ],
      deviceBreakdown: {
        mobile: 65.4,
        desktop: 28.7,
        tablet: 5.9
      },
      browserBreakdown: {
        chrome: 45.2,
        safari: 23.8,
        firefox: 15.6,
        edge: 10.3,
        other: 5.1
      }
    };
  }

  // تحليل الأداء المالي
  static getFinancialAnalytics(period: 'month' | 'year' = 'month') {
    return {
      totalRevenue: 15750000, // ل.س
      subscriptionRevenue: 12000000,
      featuredAdRevenue: 3750000,
      averageRevenuePerUser: 1765,
      subscriptionBreakdown: {
        free: { count: 5234, percentage: 58.6 },
        premium: { count: 2876, percentage: 32.2 },
        gold: { count: 567, percentage: 6.4 },
        business: { count: 246, percentage: 2.8 }
      },
      monthlyRecurringRevenue: 12000000,
      churnRate: 5.2, // نسبة مئوية
      customerLifetimeValue: 450000 // ل.س
    };
  }

  // تصدير التقرير
  static exportReport(type: 'site' | 'user', data: any, format: 'json' | 'csv' = 'json') {
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `${type}_analytics_${timestamp}.${format}`;
    
    if (format === 'json') {
      return {
        filename,
        content: JSON.stringify(data, null, 2),
        mimeType: 'application/json'
      };
    } else {
      // تحويل إلى CSV (مبسط)
      const csv = this.convertToCSV(data);
      return {
        filename,
        content: csv,
        mimeType: 'text/csv'
      };
    }
  }

  // تحويل البيانات إلى CSV
  private static convertToCSV(data: any): string {
    // تنفيذ مبسط لتحويل JSON إلى CSV
    if (Array.isArray(data)) {
      if (data.length === 0) return '';
      
      const headers = Object.keys(data[0]);
      const csvHeaders = headers.join(',');
      const csvRows = data.map(row => 
        headers.map(header => row[header]).join(',')
      );
      
      return [csvHeaders, ...csvRows].join('\n');
    }
    
    return JSON.stringify(data);
  }

  // الحصول على تقرير مخصص
  static getCustomReport(filters: {
    dateFrom: string;
    dateTo: string;
    categories?: string[];
    locations?: string[];
    userTypes?: string[];
  }) {
    // في التطبيق الحقيقي، ستطبق الفلاتر على البيانات
    return {
      summary: {
        totalAds: 1234,
        totalViews: 45678,
        totalMessages: 789,
        conversionRate: 1.7
      },
      breakdown: {
        byCategory: [
          { category: 'real-estate', count: 567, views: 23456 },
          { category: 'cars', count: 345, views: 12345 },
          { category: 'electronics', count: 234, views: 8765 }
        ],
        byLocation: [
          { location: 'damascus', count: 456, views: 18765 },
          { location: 'aleppo', count: 345, views: 14567 },
          { location: 'homs', count: 234, views: 9876 }
        ]
      },
      trends: this.generateGrowthData('ads', 'month')
    };
  }
}
