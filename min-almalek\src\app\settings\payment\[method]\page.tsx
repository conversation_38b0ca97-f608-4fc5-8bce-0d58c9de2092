'use client';

import { useState } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { toast } from 'react-hot-toast';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import CashAppLogo from '@/components/CashAppLogo';

export default function PaymentMethodSetup() {
  const router = useRouter();
  const params = useParams();
  const method = params.method as string;

  const [formData, setFormData] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    email: '',
    phone: ''
  });

  const [isLoading, setIsLoading] = useState(false);

  const paymentMethods = {
    visa: {
      name: 'Visa',
      nameAr: 'فيزا كارد',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/4/41/Visa_Logo.png',
      description: 'ادفع بأمان باستخدام بطاقة الفيزا',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    mastercard: {
      name: 'MasterCard',
      nameAr: 'ماستر كارد',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/0/04/Mastercard-logo.png',
      description: 'ادفع بأمان باستخدام بطاقة الماستر كارد',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    },
    paypal: {
      name: 'PayPal',
      nameAr: 'باي بال',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/b/b5/PayPal.svg',
      description: 'ادفع بسهولة عبر حسابك في PayPal',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    applepay: {
      name: 'Apple Pay',
      nameAr: 'آبل بيه',
      logo: 'https://upload.wikimedia.org/wikipedia/commons/f/fa/Apple_logo_black.svg',
      description: 'ادفع بسرعة وأمان باستخدام Apple Pay',
      bgColor: 'bg-gray-50',
      borderColor: 'border-gray-200'
    },
    cashapp: {
      name: 'Cash App',
      nameAr: 'كاش آب',
      description: 'ادفع باستخدام تطبيق Cash App',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    }
  };

  const currentMethod = paymentMethods[method as keyof typeof paymentMethods];

  if (!currentMethod) {
    return (
      <div className="min-h-screen bg-white">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-800 mb-4">وسيلة دفع غير صحيحة</h1>
            <button
              onClick={() => router.back()}
              className="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600 transition-colors"
            >
              العودة
            </button>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCardNumber(e.target.value);
    handleInputChange('cardNumber', formatted);
  };

  const handleExpiryChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatExpiryDate(e.target.value);
    handleInputChange('expiryDate', formatted);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Save to localStorage (in real app, this would be API call)
      const savedMethods = JSON.parse(localStorage.getItem('paymentMethods') || '[]');
      const newMethod = {
        id: Date.now(),
        type: method,
        ...formData,
        isDefault: savedMethods.length === 0,
        createdAt: new Date().toISOString()
      };
      
      savedMethods.push(newMethod);
      localStorage.setItem('paymentMethods', JSON.stringify(savedMethods));
      
      toast.success('تم إضافة وسيلة الدفع بنجاح');
      router.push('/settings');
    } catch (error) {
      console.error('Error adding payment method:', error);
      toast.error('حدث خطأ في إضافة وسيلة الدفع');
    } finally {
      setIsLoading(false);
    }
  };

  const renderForm = () => {
    if (method === 'visa' || method === 'mastercard') {
      return (
        <div className="space-y-4">
          <div>
            <label className="block text-gray-700 font-medium mb-2">رقم البطاقة</label>
            <input
              type="text"
              value={formData.cardNumber}
              onChange={handleCardNumberChange}
              placeholder="1234 5678 9012 3456"
              maxLength={19}
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
              required
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-gray-700 font-medium mb-2">تاريخ الانتهاء</label>
              <input
                type="text"
                value={formData.expiryDate}
                onChange={handleExpiryChange}
                placeholder="MM/YY"
                maxLength={5}
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-gray-700 font-medium mb-2">CVV</label>
              <input
                type="text"
                value={formData.cvv}
                onChange={(e) => handleInputChange('cvv', e.target.value.replace(/\D/g, '').slice(0, 4))}
                placeholder="123"
                maxLength={4}
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                required
              />
            </div>
          </div>
          
          <div>
            <label className="block text-gray-700 font-medium mb-2">اسم حامل البطاقة</label>
            <input
              type="text"
              value={formData.cardholderName}
              onChange={(e) => handleInputChange('cardholderName', e.target.value)}
              placeholder="الاسم كما يظهر على البطاقة"
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
              required
            />
          </div>
        </div>
      );
    } else if (method === 'paypal') {
      return (
        <div className="space-y-4">
          <div>
            <label className="block text-gray-700 font-medium mb-2">البريد الإلكتروني لـ PayPal</label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="<EMAIL>"
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
              required
            />
          </div>
        </div>
      );
    } else if (method === 'applepay') {
      return (
        <div className="space-y-4">
          <div className="text-center p-8 bg-gray-50 rounded-lg">
            <div className="text-6xl mb-4">📱</div>
            <h3 className="text-lg font-medium text-gray-800 mb-2">إعداد Apple Pay</h3>
            <p className="text-gray-600 mb-4">
              سيتم توجيهك لإعداد Apple Pay على جهازك
            </p>
            <button
              type="button"
              className="bg-black text-white px-6 py-3 rounded-lg hover:bg-gray-800 transition-colors"
              onClick={() => {
                toast.success('سيتم فتح إعدادات Apple Pay');
                // In real app, this would trigger Apple Pay setup
              }}
            >
              إعداد Apple Pay
            </button>
          </div>
        </div>
      );
    } else if (method === 'cashapp') {
      return (
        <div className="space-y-4">
          <div>
            <label className="block text-gray-700 font-medium mb-2">رقم الهاتف أو البريد الإلكتروني</label>
            <input
              type="text"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="$cashtag أو البريد الإلكتروني"
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
              required
            />
          </div>
        </div>
      );
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        <div className="max-w-md mx-auto">
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            <div className="text-center mb-6">
              <div className="w-20 h-16 mx-auto mb-4 flex items-center justify-center">
                {method === 'cashapp' ? (
                  <CashAppLogo size="lg" />
                ) : method === 'applepay' ? (
                  <div className="flex items-center gap-1">
                    <img
                      src={currentMethod.logo}
                      alt={currentMethod.name}
                      className="w-8 h-10 object-contain"
                    />
                    <span className="text-lg font-semibold text-gray-800">Pay</span>
                  </div>
                ) : (
                  <img
                    src={currentMethod.logo}
                    alt={currentMethod.name}
                    className="w-full h-full object-contain"
                  />
                )}
              </div>
              <h1 className="text-2xl font-bold text-gray-800 mb-2">
                إعداد {currentMethod.nameAr}
              </h1>
              <p className="text-gray-600">{currentMethod.description}</p>
            </div>

            <form onSubmit={handleSubmit}>
              {renderForm()}
              
              <div className="flex gap-4 mt-6">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="flex-1 bg-gray-200 text-gray-800 py-3 rounded-lg hover:bg-gray-300 transition-colors"
                >
                  إلغاء
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="flex-1 bg-green-500 text-white py-3 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'جاري الحفظ...' : 'حفظ'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
