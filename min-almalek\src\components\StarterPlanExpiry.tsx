'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface StarterPlanExpiryProps {
  userType: string;
  planId?: string;
  subscriptionStartDate?: string;
}

const StarterPlanExpiry: React.FC<StarterPlanExpiryProps> = ({
  userType,
  planId,
  subscriptionStartDate
}) => {
  const router = useRouter();
  const [daysLeft, setDaysLeft] = useState<number | null>(null);
  const [isExpired, setIsExpired] = useState(false);

  useEffect(() => {
    if (planId === 'business-starter' && subscriptionStartDate) {
      const startDate = new Date(subscriptionStartDate);
      const expiryDate = new Date(startDate);
      expiryDate.setMonth(expiryDate.getMonth() + 1); // إضافة شهر واحد
      
      const now = new Date();
      const timeDiff = expiryDate.getTime() - now.getTime();
      const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
      
      setDaysLeft(daysDiff);
      setIsExpired(daysDiff <= 0);
    }
  }, [planId, subscriptionStartDate]);

  if (planId !== 'business-starter' || userType !== 'business') {
    return null;
  }

  if (isExpired) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
        <div className="flex items-center gap-3">
          <div className="text-red-500 text-2xl">⚠️</div>
          <div>
            <h3 className="text-red-800 font-bold text-lg">انتهت صلاحية باقة البداية التجريبية</h3>
            <p className="text-red-700 text-sm mt-1">
              لقد انتهت فترة الشهر المجاني. يرجى الترقية إلى إحدى الباقات المدفوعة للاستمرار في استخدام خدمات الشركات.
            </p>
            <button
              onClick={() => router.push('/subscription')}
              className="mt-3 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
            >
              ترقية الباقة الآن
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (daysLeft !== null && daysLeft <= 7) {
    return (
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
        <div className="flex items-center gap-3">
          <div className="text-orange-500 text-2xl">⏰</div>
          <div>
            <h3 className="text-orange-800 font-bold text-lg">
              تنتهي باقة البداية التجريبية خلال {daysLeft} {daysLeft === 1 ? 'يوم' : 'أيام'}
            </h3>
            <p className="text-orange-700 text-sm mt-1">
              باقة البداية التجريبية المجانية صالحة لشهر واحد فقط. قم بالترقية الآن لتجنب انقطاع الخدمة.
            </p>
            <button
              onClick={() => router.push('/subscription')}
              className="mt-3 bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors text-sm font-medium"
            >
              ترقية الباقة
            </button>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default StarterPlanExpiry;
