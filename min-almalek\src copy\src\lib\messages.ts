// نظام إدارة الرسائل والمحادثات
export interface Message {
  id: number;
  conversationId: number;
  senderId: number;
  receiverId: number;
  adId?: number;
  content: string;
  type: 'text' | 'image' | 'file' | 'offer';
  attachments?: string[];
  offer?: {
    price: number;
    currency: 'SYP' | 'USD';
    message?: string;
    expiresAt: string;
  };
  read: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Conversation {
  id: number;
  participants: number[];
  adId?: number;
  adTitle?: string;
  lastMessage?: Message;
  unreadCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface MessageNotification {
  id: number;
  userId: number;
  type: 'new_message' | 'offer_received' | 'offer_accepted' | 'offer_rejected';
  title: string;
  message: string;
  data: any;
  read: boolean;
  createdAt: string;
}

// بيانات تجريبية للرسائل
const sampleMessages: Message[] = [
  {
    id: 1,
    conversationId: 1,
    senderId: 2,
    receiverId: 1,
    adId: 1,
    content: 'مرحباً، أنا مهتم بالشقة المعروضة. هل يمكنني معاينتها؟',
    type: 'text',
    read: false,
    createdAt: '2024-01-20T10:30:00Z',
    updatedAt: '2024-01-20T10:30:00Z'
  },
  {
    id: 2,
    conversationId: 1,
    senderId: 1,
    receiverId: 2,
    adId: 1,
    content: 'أهلاً وسهلاً، بالطبع يمكنك المعاينة. متى يناسبك؟',
    type: 'text',
    read: true,
    createdAt: '2024-01-20T11:15:00Z',
    updatedAt: '2024-01-20T11:15:00Z'
  },
  {
    id: 3,
    conversationId: 1,
    senderId: 2,
    receiverId: 1,
    adId: 1,
    content: 'ما رأيك في عرض 80 مليون بدلاً من 85؟',
    type: 'offer',
    offer: {
      price: 80000000,
      currency: 'SYP',
      message: 'عرض نهائي للشراء',
      expiresAt: '2024-01-25T23:59:59Z'
    },
    read: false,
    createdAt: '2024-01-20T14:22:00Z',
    updatedAt: '2024-01-20T14:22:00Z'
  },
  {
    id: 4,
    conversationId: 2,
    senderId: 3,
    receiverId: 2,
    adId: 2,
    content: 'هل السيارة متاحة للمعاينة اليوم؟',
    type: 'text',
    read: false,
    createdAt: '2024-01-20T16:45:00Z',
    updatedAt: '2024-01-20T16:45:00Z'
  }
];

const sampleConversations: Conversation[] = [
  {
    id: 1,
    participants: [1, 2],
    adId: 1,
    adTitle: 'شقة للبيع في دمشق - المالكي',
    lastMessage: sampleMessages[2],
    unreadCount: 2,
    createdAt: '2024-01-20T10:30:00Z',
    updatedAt: '2024-01-20T14:22:00Z'
  },
  {
    id: 2,
    participants: [2, 3],
    adId: 2,
    adTitle: 'BMW X5 2020 فل أوبشن',
    lastMessage: sampleMessages[3],
    unreadCount: 1,
    createdAt: '2024-01-20T16:45:00Z',
    updatedAt: '2024-01-20T16:45:00Z'
  }
];

export class MessageService {
  // الحصول على محادثات المستخدم
  static getUserConversations(userId: number): Conversation[] {
    return sampleConversations.filter(conv => 
      conv.participants.includes(userId)
    ).sort((a, b) => 
      new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    );
  }

  // الحصول على رسائل محادثة معينة
  static getConversationMessages(conversationId: number, userId: number): Message[] {
    // التحقق من أن المستخدم جزء من المحادثة
    const conversation = sampleConversations.find(c => c.id === conversationId);
    if (!conversation || !conversation.participants.includes(userId)) {
      return [];
    }

    return sampleMessages
      .filter(msg => msg.conversationId === conversationId)
      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
  }

  // إرسال رسالة جديدة
  static async sendMessage(
    senderId: number,
    receiverId: number,
    content: string,
    type: 'text' | 'offer' = 'text',
    adId?: number,
    offer?: Message['offer']
  ): Promise<{ success: boolean; data?: Message; error?: string }> {
    try {
      // البحث عن محادثة موجودة أو إنشاء جديدة
      let conversation = sampleConversations.find(conv =>
        conv.participants.includes(senderId) && 
        conv.participants.includes(receiverId) &&
        (!adId || conv.adId === adId)
      );

      if (!conversation) {
        // إنشاء محادثة جديدة
        conversation = {
          id: Date.now(),
          participants: [senderId, receiverId],
          adId,
          unreadCount: 0,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        sampleConversations.push(conversation);
      }

      // إنشاء الرسالة
      const newMessage: Message = {
        id: Date.now(),
        conversationId: conversation.id,
        senderId,
        receiverId,
        adId,
        content,
        type,
        offer,
        read: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // إضافة الرسالة
      sampleMessages.push(newMessage);

      // تحديث المحادثة
      conversation.lastMessage = newMessage;
      conversation.updatedAt = newMessage.createdAt;
      conversation.unreadCount += 1;

      return { success: true, data: newMessage };
    } catch (error) {
      return { success: false, error: 'حدث خطأ في إرسال الرسالة' };
    }
  }

  // تحديد الرسائل كمقروءة
  static markAsRead(conversationId: number, userId: number): boolean {
    try {
      const conversation = sampleConversations.find(c => c.id === conversationId);
      if (!conversation || !conversation.participants.includes(userId)) {
        return false;
      }

      // تحديد رسائل المحادثة كمقروءة
      sampleMessages
        .filter(msg => 
          msg.conversationId === conversationId && 
          msg.receiverId === userId && 
          !msg.read
        )
        .forEach(msg => {
          msg.read = true;
          msg.updatedAt = new Date().toISOString();
        });

      // إعادة تعيين عداد الرسائل غير المقروءة
      conversation.unreadCount = 0;

      return true;
    } catch (error) {
      return false;
    }
  }

  // حذف رسالة
  static deleteMessage(messageId: number, userId: number): boolean {
    try {
      const messageIndex = sampleMessages.findIndex(msg => 
        msg.id === messageId && msg.senderId === userId
      );

      if (messageIndex === -1) {
        return false;
      }

      sampleMessages.splice(messageIndex, 1);
      return true;
    } catch (error) {
      return false;
    }
  }

  // الحصول على عدد الرسائل غير المقروءة
  static getUnreadCount(userId: number): number {
    return sampleMessages.filter(msg => 
      msg.receiverId === userId && !msg.read
    ).length;
  }

  // البحث في الرسائل
  static searchMessages(userId: number, query: string): Message[] {
    const userConversations = this.getUserConversations(userId);
    const conversationIds = userConversations.map(c => c.id);

    return sampleMessages.filter(msg =>
      conversationIds.includes(msg.conversationId) &&
      msg.content.toLowerCase().includes(query.toLowerCase())
    );
  }

  // الحصول على إحصائيات الرسائل
  static getMessageStats(userId: number) {
    const userMessages = sampleMessages.filter(msg =>
      msg.senderId === userId || msg.receiverId === userId
    );

    const sent = userMessages.filter(msg => msg.senderId === userId).length;
    const received = userMessages.filter(msg => msg.receiverId === userId).length;
    const unread = userMessages.filter(msg => 
      msg.receiverId === userId && !msg.read
    ).length;

    return {
      total: userMessages.length,
      sent,
      received,
      unread,
      conversations: this.getUserConversations(userId).length
    };
  }

  // قبول عرض
  static acceptOffer(messageId: number, userId: number): boolean {
    try {
      const message = sampleMessages.find(msg => 
        msg.id === messageId && 
        msg.receiverId === userId && 
        msg.type === 'offer'
      );

      if (!message || !message.offer) {
        return false;
      }

      // إضافة رسالة قبول
      this.sendMessage(
        userId,
        message.senderId,
        `تم قبول عرضك بمبلغ ${message.offer.price.toLocaleString()} ${message.offer.currency === 'SYP' ? 'ل.س' : '$'}`,
        'text',
        message.adId
      );

      return true;
    } catch (error) {
      return false;
    }
  }

  // رفض عرض
  static rejectOffer(messageId: number, userId: number, reason?: string): boolean {
    try {
      const message = sampleMessages.find(msg => 
        msg.id === messageId && 
        msg.receiverId === userId && 
        msg.type === 'offer'
      );

      if (!message || !message.offer) {
        return false;
      }

      // إضافة رسالة رفض
      const rejectMessage = reason 
        ? `تم رفض عرضك. السبب: ${reason}`
        : 'تم رفض عرضك';

      this.sendMessage(
        userId,
        message.senderId,
        rejectMessage,
        'text',
        message.adId
      );

      return true;
    } catch (error) {
      return false;
    }
  }

  // إنشاء محادثة جديدة حول إعلان
  static async startConversationForAd(
    buyerId: number,
    sellerId: number,
    adId: number,
    initialMessage: string
  ): Promise<{ success: boolean; data?: Conversation; error?: string }> {
    try {
      // التحقق من عدم وجود محادثة مسبقة
      const existingConversation = sampleConversations.find(conv =>
        conv.participants.includes(buyerId) && 
        conv.participants.includes(sellerId) &&
        conv.adId === adId
      );

      if (existingConversation) {
        return { success: true, data: existingConversation };
      }

      // إرسال الرسالة الأولى
      const messageResult = await this.sendMessage(
        buyerId,
        sellerId,
        initialMessage,
        'text',
        adId
      );

      if (messageResult.success) {
        const conversation = sampleConversations.find(conv =>
          conv.participants.includes(buyerId) && 
          conv.participants.includes(sellerId) &&
          conv.adId === adId
        );

        return { success: true, data: conversation };
      } else {
        return { success: false, error: messageResult.error };
      }
    } catch (error) {
      return { success: false, error: 'حدث خطأ في إنشاء المحادثة' };
    }
  }
}
