'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface ClientSafeLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  onClick?: (e: React.MouseEvent) => void;
  title?: string;
  [key: string]: any;
}

const ClientSafeLink = ({ href, children, className, onClick, title, ...props }: ClientSafeLinkProps) => {
  const router = useRouter();
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    if (onClick) {
      onClick(e);
    }
    if (isMounted) {
      router.push(href);
    }
  };

  if (!isMounted) {
    return (
      <span className={className} title={title} {...props}>
        {children}
      </span>
    );
  }

  return (
    <button
      onClick={handleClick}
      className={className}
      title={title}
      {...props}
    >
      {children}
    </button>
  );
};

export default ClientSafeLink;
