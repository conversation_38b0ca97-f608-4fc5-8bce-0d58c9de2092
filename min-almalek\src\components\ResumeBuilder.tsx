'use client';

import { useState } from 'react';
import { Resume, Experience, Education, Skill, Language, Course, Project } from '@/lib/jobs';
import ResumePrintView from './ResumePrintView';

interface ResumeBuilderProps {
  initialResume?: Partial<Resume>;
  onSave: (resume: Resume) => void;
  onCancel: () => void;
}

const ResumeBuilder = ({ initialResume, onSave, onCancel }: ResumeBuilderProps) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [hoveredStep, setHoveredStep] = useState<number | null>(null);
  const [resume, setResume] = useState<Partial<Resume>>({
    personalInfo: {
      firstName: '',
      lastName: '',
      title: '',
      summary: '',
      nationality: 'سوري',
      photo: '',
      ...initialResume?.personalInfo
    },
    contactInfo: {
      phone: '',
      email: '',
      address: '',
      city: '',
      ...initialResume?.contactInfo
    },
    experiences: initialResume?.experiences || [],
    education: initialResume?.education || [],
    skills: initialResume?.skills || [
      {
        id: 'communication',
        name: 'التواصل والعمل الجماعي',
        level: 'متقدم',
        category: 'إداري',
        verified: false
      },
      {
        id: 'computer',
        name: 'استخدام الحاسوب',
        level: 'متقدم',
        category: 'تقني',
        verified: false
      }
    ],
    languages: initialResume?.languages || [
      {
        id: 'arabic',
        name: 'العربية',
        level: 'أصلي'
      },
      {
        id: 'english',
        name: 'الإنجليزية',
        level: 'متوسط'
      }
    ],
    courses: initialResume?.courses || [],
    projects: initialResume?.projects || [],
    references: initialResume?.references || [],
    isPublic: initialResume?.isPublic || false
  });

  const getStepIcon = (stepId: number, isActive: boolean, isHovered: boolean) => {
    const iconClass = `w-6 h-6 transition-all duration-300 ${
      isActive ? 'text-green-600' : 'text-gray-500'
    } group-hover:text-green-600 ${isHovered ? 'drop-shadow-lg filter brightness-110' : ''}`;

    const glowStyle = isHovered ? {
      filter: 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.6)) brightness(1.2)'
    } : {};

    switch (stepId) {
      case 0: // المعلومات الشخصية
        return (
          <svg className={iconClass} style={glowStyle} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
          </svg>
        );
      case 1: // معلومات التواصل
        return (
          <svg className={iconClass} style={glowStyle} fill="currentColor" viewBox="0 0 20 20">
            <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
          </svg>
        );
      case 2: // الخبرات العملية
        return (
          <svg className={iconClass} style={glowStyle} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h2zm4-1a1 1 0 00-1 1v1h2V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 3: // التعليم والشهادات
        return (
          <svg className={iconClass} style={glowStyle} fill="currentColor" viewBox="0 0 20 20">
            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
          </svg>
        );
      case 4: // المهارات
        return (
          <img
            src="/images/إنشاء السيرة الذاتية/المهارات.png"
            alt="المهارات"
            className={iconClass}
            style={{
              ...glowStyle,
              filter: currentStep === 4
                ? 'grayscale(0) drop-shadow(0 0 20px rgba(34, 197, 94, 1)) brightness(1.2)'
                : 'grayscale(1) opacity(0.5)',
              transition: 'all 0.3s ease',
              animation: currentStep === 4 ? 'greenGlow 1.5s infinite alternate' : 'none'
            }}
          />
        );
      case 5: // اللغات
        return (
          <svg className={iconClass} style={glowStyle} fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
          </svg>
        );
      case 6: // الدورات والشهادات
        return (
          <img
            src="/images/إنشاء السيرة الذاتية/الشهادات.png"
            alt="الدورات والشهادات"
            className={iconClass}
            style={{
              ...glowStyle,
              filter: currentStep === 6
                ? 'grayscale(0) drop-shadow(0 0 20px rgba(34, 197, 94, 1)) brightness(1.2)'
                : 'grayscale(1) opacity(0.5)',
              transition: 'all 0.3s ease',
              animation: currentStep === 6 ? 'greenGlow 1.5s infinite alternate' : 'none'
            }}
          />
        );
      case 7: // المشاريع والإنجازات
        return (
          <img
            src="/images/إنشاء السيرة الذاتية/المشاريع والإنجازات.png"
            alt="المشاريع والإنجازات"
            className={iconClass}
            style={{
              ...glowStyle,
              filter: currentStep === 7
                ? 'grayscale(0) drop-shadow(0 0 20px rgba(34, 197, 94, 1)) brightness(1.2)'
                : 'grayscale(1) opacity(0.5)',
              transition: 'all 0.3s ease',
              animation: currentStep === 7 ? 'greenGlow 1.5s infinite alternate' : 'none'
            }}
          />
        );
      case 8: // المراجع
        return (
          <img
            src="/images/إنشاء السيرة الذاتية/المراجع.png"
            alt="المراجع"
            className={iconClass}
            style={{
              ...glowStyle,
              filter: currentStep === 8
                ? 'grayscale(0) drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))'
                : 'grayscale(1) opacity(0.6)'
            }}
          />
        );
      case 9: // المراجعة والحفظ
        return (
          <svg className={iconClass} style={glowStyle} fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        );
      default:
        return null;
    }
  };

  const steps = [
    { id: 0, title: 'المعلومات الشخصية' },
    { id: 1, title: 'معلومات التواصل' },
    { id: 2, title: 'الخبرات العملية' },
    { id: 3, title: 'التعليم والشهادات' },
    { id: 4, title: 'المهارات' },
    { id: 5, title: 'اللغات' },
    { id: 6, title: 'الدورات والشهادات' },
    { id: 7, title: 'المشاريع والإنجازات' },
    { id: 8, title: 'المراجع' },
    { id: 9, title: 'المراجعة والحفظ' }
  ];

  const updateResume = (section: string, data: any) => {
    setResume(prev => ({
      ...prev,
      [section]: data
    }));
  };

  const addExperience = () => {
    const newExperience: Experience = {
      id: Date.now().toString(),
      jobTitle: '',
      company: '',
      location: '',
      startDate: '',
      current: false,
      description: '',
      achievements: []
    };
    updateResume('experiences', [...(resume.experiences || []), newExperience]);
  };

  const updateExperience = (id: string, field: string, value: any) => {
    const updated = resume.experiences?.map(exp =>
      exp.id === id ? { ...exp, [field]: value } : exp
    );
    updateResume('experiences', updated);
  };

  const removeExperience = (id: string) => {
    const updated = resume.experiences?.filter(exp => exp.id !== id);
    updateResume('experiences', updated);
  };

  const addEducation = () => {
    const newEducation: Education = {
      id: Date.now().toString(),
      degree: '',
      institution: '',
      field: '',
      location: '',
      startDate: '',
      current: false
    };
    updateResume('education', [...(resume.education || []), newEducation]);
  };

  const addSkill = () => {
    const newSkill: Skill = {
      id: Date.now().toString(),
      name: '',
      level: 'متوسط',
      category: 'تقني',
      verified: false
    };
    updateResume('skills', [...(resume.skills || []), newSkill]);
  };

  const addLanguage = () => {
    const newLanguage: Language = {
      id: Date.now().toString(),
      name: '',
      level: 'متوسط'
    };
    updateResume('languages', [...(resume.languages || []), newLanguage]);
  };

  const addCourse = () => {
    const newCourse: Course = {
      id: Date.now().toString(),
      name: '',
      provider: '',
      completionDate: '',
      skills: []
    };
    updateResume('courses', [...(resume.courses || []), newCourse]);
  };

  const addProject = () => {
    const newProject: Project = {
      id: Date.now().toString(),
      name: '',
      description: '',
      technologies: [],
      url: '',
      startDate: '',
      endDate: '',
      status: 'مكتمل'
    };
    updateResume('projects', [...(resume.projects || []), newProject]);
  };

  const updateProject = (id: string, field: string, value: any) => {
    const updatedProjects = (resume.projects || []).map(project =>
      project.id === id ? { ...project, [field]: value } : project
    );
    updateResume('projects', updatedProjects);
  };

  const removeProject = (id: string) => {
    const updatedProjects = (resume.projects || []).filter(project => project.id !== id);
    updateResume('projects', updatedProjects);
  };

  const addReference = () => {
    const newReference = {
      id: Date.now().toString(),
      name: '',
      position: '',
      company: '',
      phone: '',
      email: '',
      relationship: ''
    };
    updateResume('references', [...(resume.references || []), newReference]);
  };

  const updateReference = (id: string, field: string, value: any) => {
    const updatedReferences = (resume.references || []).map(reference =>
      // Ensure reference has an 'id' property, fallback to empty string if missing
      (reference as any).id === id ? { ...reference, [field]: value, id: (reference as any).id || id } : reference
    );
    updateResume('references', updatedReferences);
  };

  const removeReference = (id: string) => {
    const updatedReferences = (resume.references || []).filter(reference => (reference as any).id !== id);
    updateResume('references', updatedReferences);
  };

  const handleSave = () => {
    const completeResume: Resume = {
      id: initialResume?.id || Date.now().toString(),
      userId: 'current-user', // سيتم تحديثه من السياق
      personalInfo: resume.personalInfo!,
      contactInfo: resume.contactInfo!,
      experiences: resume.experiences || [],
      education: resume.education || [],
      skills: resume.skills || [],
      languages: resume.languages || [],
      courses: resume.courses || [],
      projects: resume.projects || [],
      references: resume.references || [],
      createdAt: initialResume?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isPublic: resume.isPublic || false,
      views: initialResume?.views || 0
    };

    if (onSave) {
      onSave(completeResume);
    } else {
      // حفظ محلي في localStorage
      localStorage.setItem('userResume', JSON.stringify(completeResume));
      alert('تم حفظ السيرة الذاتية بنجاح!');
    }
  };

  const renderPersonalInfo = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800 mb-4">المعلومات الشخصية</h3>

      {/* الصورة الشخصية */}
      <div className="flex flex-col items-center mb-6">
        <div className="relative">
          <div className="w-32 h-32 rounded-full border-4 border-gray-200 overflow-hidden bg-gray-100 flex items-center justify-center">
            {resume.personalInfo?.photo ? (
              <img
                src={resume.personalInfo.photo}
                alt="الصورة الشخصية"
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="text-gray-400 text-4xl">👤</div>
            )}
          </div>
          <label className="absolute bottom-0 right-0 bg-primary-600 text-white rounded-full p-2 cursor-pointer hover:bg-primary-700 transition-colors">
            <input
              type="file"
              accept="image/*"
              className="hidden"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  const reader = new FileReader();
                  reader.onload = (event) => {
                    updateResume('personalInfo', {
                      ...resume.personalInfo,
                      photo: event.target?.result as string
                    });
                  };
                  reader.readAsDataURL(file);
                }
              }}
            />
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
          </label>
        </div>
        <p className="text-sm text-gray-500 mt-2">اضغط على الأيقونة لإضافة صورة شخصية</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">الاسم الأول *</label>
          <input
            type="text"
            value={resume.personalInfo?.firstName || ''}
            onChange={(e) => updateResume('personalInfo', {
              ...resume.personalInfo,
              firstName: e.target.value
            })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">اسم العائلة *</label>
          <input
            type="text"
            value={resume.personalInfo?.lastName || ''}
            onChange={(e) => updateResume('personalInfo', {
              ...resume.personalInfo,
              lastName: e.target.value
            })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">المسمى الوظيفي *</label>
        <input
          type="text"
          value={resume.personalInfo?.title || ''}
          onChange={(e) => updateResume('personalInfo', {
            ...resume.personalInfo,
            title: e.target.value
          })}
          placeholder="مثال: مطور ويب، مهندس برمجيات، مصمم جرافيك"
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          required
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">نبذة مختصرة *</label>
        <textarea
          value={resume.personalInfo?.summary || ''}
          onChange={(e) => updateResume('personalInfo', {
            ...resume.personalInfo,
            summary: e.target.value
          })}
          placeholder="اكتب نبذة مختصرة عن خبراتك ومهاراتك (2-3 جمل)"
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">الجنسية</label>
          <select
            value={resume.personalInfo?.nationality || 'سوري'}
            onChange={(e) => updateResume('personalInfo', {
              ...resume.personalInfo,
              nationality: e.target.value
            })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="سوري">سوري</option>
            <option value="لبناني">لبناني</option>
            <option value="أردني">أردني</option>
            <option value="فلسطيني">فلسطيني</option>
            <option value="عراقي">عراقي</option>
            <option value="مصري">مصري</option>
            <option value="أخرى">أخرى</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">الحالة الاجتماعية</label>
          <select
            value={resume.personalInfo?.maritalStatus || ''}
            onChange={(e) => updateResume('personalInfo', {
              ...resume.personalInfo,
              maritalStatus: e.target.value
            })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="">اختر الحالة</option>
            <option value="أعزب">أعزب</option>
            <option value="متزوج">متزوج</option>
            <option value="مطلق">مطلق</option>
            <option value="أرمل">أرمل</option>
          </select>
        </div>
      </div>
    </div>
  );

  const renderContactInfo = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800 mb-4">معلومات التواصل</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف *</label>
          <input
            type="tel"
            value={resume.contactInfo?.phone || ''}
            onChange={(e) => updateResume('contactInfo', {
              ...resume.contactInfo,
              phone: e.target.value
            })}
            placeholder="+963 XXX XXX XXX"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
          <input
            type="email"
            value={resume.contactInfo?.email || ''}
            onChange={(e) => updateResume('contactInfo', {
              ...resume.contactInfo,
              email: e.target.value
            })}
            placeholder="<EMAIL>"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">المدينة أو الحي *</label>
          <input
            type="text"
            value={resume.contactInfo?.city || ''}
            onChange={(e) => updateResume('contactInfo', {
              ...resume.contactInfo,
              city: e.target.value
            })}
            placeholder="دمشق، حلب، حمص، المالكي، الحمدانية..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
          <input
            type="text"
            value={resume.contactInfo?.address || ''}
            onChange={(e) => updateResume('contactInfo', {
              ...resume.contactInfo,
              address: e.target.value
            })}
            placeholder="الحي، الشارع"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">LinkedIn</label>
          <input
            type="url"
            value={resume.contactInfo?.linkedin || ''}
            onChange={(e) => updateResume('contactInfo', {
              ...resume.contactInfo,
              linkedin: e.target.value
            })}
            placeholder="https://linkedin.com/in/username"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">GitHub</label>
          <input
            type="url"
            value={resume.contactInfo?.github || ''}
            onChange={(e) => updateResume('contactInfo', {
              ...resume.contactInfo,
              github: e.target.value
            })}
            placeholder="https://github.com/username"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Portfolio</label>
          <input
            type="url"
            value={resume.contactInfo?.portfolio || ''}
            onChange={(e) => updateResume('contactInfo', {
              ...resume.contactInfo,
              portfolio: e.target.value
            })}
            placeholder="https://myportfolio.com"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
      </div>
    </div>
  );

  const renderExperiences = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">الخبرات العملية</h3>
        <button
          onClick={addExperience}
          className="bg-white/30 backdrop-blur-sm text-primary-600 px-4 py-2 rounded-lg hover:bg-white/50 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 border border-white/40"
        >
          <img
            src="/images/jobs/Jobs -Personals/icons/add_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
            alt="إضافة"
            className="w-4 h-4 inline ml-1 opacity-80"
            style={{
              filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.6))'
            }}
          />
          إضافة خبرة
        </button>
      </div>

      {resume.experiences?.map((exp, index) => (
        <div key={exp.id} className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-semibold text-gray-800">خبرة #{index + 1}</h4>
            <button
              onClick={() => removeExperience(exp.id)}
              className="text-red-600 hover:text-red-800"
            >
              🗑️ حذف
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المسمى الوظيفي *</label>
              <input
                type="text"
                value={exp.jobTitle}
                onChange={(e) => updateExperience(exp.id, 'jobTitle', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">اسم الشركة *</label>
              <input
                type="text"
                value={exp.company}
                onChange={(e) => updateExperience(exp.id, 'company', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
              <input
                type="text"
                value={exp.location}
                onChange={(e) => updateExperience(exp.id, 'location', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ البداية *</label>
              <input
                type="month"
                value={exp.startDate}
                onChange={(e) => updateExperience(exp.id, 'startDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ النهاية</label>
              <input
                type="month"
                value={exp.endDate || ''}
                onChange={(e) => updateExperience(exp.id, 'endDate', e.target.value)}
                disabled={exp.current}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-100"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={exp.current}
                onChange={(e) => {
                  updateExperience(exp.id, 'current', e.target.checked);
                  if (e.target.checked) {
                    updateExperience(exp.id, 'endDate', '');
                  }
                }}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="mr-2 text-sm text-gray-700">أعمل حالياً في هذا المنصب</span>
            </label>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">وصف المهام والمسؤوليات</label>
            <textarea
              value={exp.description}
              onChange={(e) => updateExperience(exp.id, 'description', e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="اكتب وصفاً مفصلاً عن مهامك ومسؤولياتك في هذا المنصب..."
            />
          </div>
        </div>
      ))}

      {(!resume.experiences || resume.experiences.length === 0) && (
        <div className="text-center py-8 text-gray-500">
          <div className="text-4xl mb-2">💼</div>
          <p>لم تضف أي خبرات عملية بعد</p>
          <p className="text-sm">انقر على "إضافة خبرة" لبدء إضافة خبراتك العملية</p>
        </div>
      )}
    </div>
  );

  const renderEducation = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">التعليم والشهادات</h3>
        <button
          onClick={addEducation}
          className="bg-white/30 backdrop-blur-sm text-primary-600 px-4 py-2 rounded-lg hover:bg-white/50 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 border border-white/40"
        >
          <img
            src="/images/jobs/Jobs -Personals/icons/add_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
            alt="إضافة"
            className="w-4 h-4 inline ml-1 opacity-80"
            style={{
              filter: 'drop-shadow(0 0 4px rgba(34, 197, 94, 0.6))'
            }}
          />
          إضافة شهادة
        </button>
      </div>

      {resume.education?.map((edu, index) => (
        <div key={edu.id} className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-semibold text-gray-800">شهادة #{index + 1}</h4>
            <button
              onClick={() => {
                const updated = resume.education?.filter(e => e.id !== edu.id);
                updateResume('education', updated);
              }}
              className="text-red-600 hover:text-red-800"
            >
              🗑️ حذف
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الدرجة العلمية *</label>
              <input
                type="text"
                value={edu.degree}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, degree: e.target.value } : item
                  );
                  updateResume('education', updated);
                }}
                placeholder="بكالوريوس، ماجستير، دكتوراه..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">التخصص *</label>
              <input
                type="text"
                value={edu.field}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, field: e.target.value } : item
                  );
                  updateResume('education', updated);
                }}
                placeholder="هندسة الحاسوب، إدارة أعمال..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">اسم المؤسسة *</label>
              <input
                type="text"
                value={edu.institution}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, institution: e.target.value } : item
                  );
                  updateResume('education', updated);
                }}
                placeholder="جامعة دمشق، الجامعة الافتراضية..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الموقع</label>
              <input
                type="text"
                value={edu.location}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, location: e.target.value } : item
                  );
                  updateResume('education', updated);
                }}
                placeholder="دمشق، حلب، حمص..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ البداية *</label>
              <input
                type="month"
                value={edu.startDate}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, startDate: e.target.value } : item
                  );
                  updateResume('education', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ التخرج</label>
              <input
                type="month"
                value={edu.endDate || ''}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, endDate: e.target.value } : item
                  );
                  updateResume('education', updated);
                }}
                disabled={edu.current}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-100"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المعدل (اختياري)</label>
              <input
                type="text"
                value={edu.gpa || ''}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, gpa: e.target.value } : item
                  );
                  updateResume('education', updated);
                }}
                placeholder="3.5/4.0 أو 85%"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={edu.current}
                onChange={(e) => {
                  const updated = resume.education?.map(item =>
                    item.id === edu.id ? { ...item, current: e.target.checked } : item
                  );
                  updateResume('education', updated);
                  if (e.target.checked) {
                    const updatedWithoutEndDate = resume.education?.map(item =>
                      item.id === edu.id ? { ...item, endDate: '' } : item
                    );
                    updateResume('education', updatedWithoutEndDate);
                  }
                }}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="mr-2 text-sm text-gray-700">أدرس حالياً في هذه المؤسسة</span>
            </label>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">وصف إضافي (اختياري)</label>
            <textarea
              value={edu.description || ''}
              onChange={(e) => {
                const updated = resume.education?.map(item =>
                  item.id === edu.id ? { ...item, description: e.target.value } : item
                );
                updateResume('education', updated);
              }}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="أي معلومات إضافية عن دراستك، مشاريع التخرج، الأنشطة..."
            />
          </div>
        </div>
      ))}
    </div>
  );

  const renderSkills = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">المهارات</h3>
        <button
          onClick={addSkill}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
        >
          + إضافة مهارة
        </button>
      </div>

      {/* رسالة ترحيبية */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-green-600">⭐</span>
          <h4 className="font-semibold text-green-800">تم إضافة المهارات الأساسية تلقائياً</h4>
        </div>
        <p className="text-green-700 text-sm">
          أضفنا بعض المهارات الأساسية المطلوبة في سوق العمل. يمكنك تعديلها أو إضافة مهارات أخرى.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {resume.skills?.map((skill, index) => (
          <div key={skill.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-800">مهارة #{index + 1}</h4>
              <button
                onClick={() => {
                  const updated = resume.skills?.filter(s => s.id !== skill.id);
                  updateResume('skills', updated);
                }}
                className="text-red-600 hover:text-red-800"
              >
                🗑️
              </button>
            </div>

            <div className="space-y-3">
              <input
                type="text"
                placeholder="اسم المهارة"
                value={skill.name}
                onChange={(e) => {
                  const updated = resume.skills?.map(s =>
                    s.id === skill.id ? { ...s, name: e.target.value } : s
                  );
                  updateResume('skills', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />

              <select
                value={skill.level}
                onChange={(e) => {
                  const updated = resume.skills?.map(s =>
                    s.id === skill.id ? { ...s, level: e.target.value as any } : s
                  );
                  updateResume('skills', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="مبتدئ">مبتدئ</option>
                <option value="متوسط">متوسط</option>
                <option value="متقدم">متقدم</option>
                <option value="خبير">خبير</option>
              </select>

              <select
                value={skill.category}
                onChange={(e) => {
                  const updated = resume.skills?.map(s =>
                    s.id === skill.id ? { ...s, category: e.target.value as any } : s
                  );
                  updateResume('skills', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="تقني">تقني</option>
                <option value="لغوي">لغوي</option>
                <option value="إداري">إداري</option>
                <option value="إبداعي">إبداعي</option>
                <option value="أخرى">أخرى</option>
              </select>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderLanguages = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">اللغات</h3>
        <button
          onClick={addLanguage}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
        >
          + إضافة لغة
        </button>
      </div>

      {/* رسالة ترحيبية */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-2">
          <span className="text-blue-600">💡</span>
          <h4 className="font-semibold text-blue-800">تم إضافة اللغات الأساسية تلقائياً</h4>
        </div>
        <p className="text-blue-700 text-sm">
          أضفنا العربية والإنجليزية كلغات أساسية. يمكنك تعديل مستوى إتقانك أو إضافة لغات أخرى.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {resume.languages?.map((lang, index) => (
          <div key={lang.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-800">لغة #{index + 1}</h4>
              <button
                onClick={() => {
                  const updated = resume.languages?.filter(l => l.id !== lang.id);
                  updateResume('languages', updated);
                }}
                className="text-red-600 hover:text-red-800"
              >
                🗑️
              </button>
            </div>

            <div className="space-y-3">
              <input
                type="text"
                placeholder="اسم اللغة"
                value={lang.name}
                onChange={(e) => {
                  const updated = resume.languages?.map(l =>
                    l.id === lang.id ? { ...l, name: e.target.value } : l
                  );
                  updateResume('languages', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />

              <select
                value={lang.level}
                onChange={(e) => {
                  const updated = resume.languages?.map(l =>
                    l.id === lang.id ? { ...l, level: e.target.value as any } : l
                  );
                  updateResume('languages', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              >
                <option value="مبتدئ">مبتدئ</option>
                <option value="متوسط">متوسط</option>
                <option value="متقدم">متقدم</option>
                <option value="أصلي">أصلي</option>
              </select>

              <input
                type="text"
                placeholder="شهادة اللغة (اختياري)"
                value={lang.certification || ''}
                onChange={(e) => {
                  const updated = resume.languages?.map(l =>
                    l.id === lang.id ? { ...l, certification: e.target.value } : l
                  );
                  updateResume('languages', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderCourses = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">الدورات والشهادات</h3>
        <button
          onClick={addCourse}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
        >
          + إضافة دورة
        </button>
      </div>

      {resume.courses?.map((course, index) => (
        <div key={course.id} className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-semibold text-gray-800">دورة #{index + 1}</h4>
            <button
              onClick={() => {
                const updated = resume.courses?.filter(c => c.id !== course.id);
                updateResume('courses', updated);
              }}
              className="text-red-600 hover:text-red-800"
            >
              🗑️ حذف
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">اسم الدورة *</label>
              <input
                type="text"
                value={course.name}
                onChange={(e) => {
                  const updated = resume.courses?.map(c =>
                    c.id === course.id ? { ...c, name: e.target.value } : c
                  );
                  updateResume('courses', updated);
                }}
                placeholder="مثال: دورة تطوير الويب، شهادة إدارة المشاريع"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">مقدم الدورة *</label>
              <input
                type="text"
                value={course.provider}
                onChange={(e) => {
                  const updated = resume.courses?.map(c =>
                    c.id === course.id ? { ...c, provider: e.target.value } : c
                  );
                  updateResume('courses', updated);
                }}
                placeholder="مثال: Coursera، Udemy، جامعة دمشق"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">تاريخ الإكمال</label>
              <input
                type="month"
                value={course.completionDate}
                onChange={(e) => {
                  const updated = resume.courses?.map(c =>
                    c.id === course.id ? { ...c, completionDate: e.target.value } : c
                  );
                  updateResume('courses', updated);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">رابط الشهادة</label>
              <input
                type="url"
                value={course.certificateUrl || ''}
                onChange={(e) => {
                  const updated = resume.courses?.map(c =>
                    c.id === course.id ? { ...c, certificateUrl: e.target.value } : c
                  );
                  updateResume('courses', updated);
                }}
                placeholder="https://example.com/certificate"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">المهارات المكتسبة</label>
            <input
              type="text"
              value={course.skills?.join(', ') || ''}
              onChange={(e) => {
                const skillsArray = e.target.value.split(',').map(skill => skill.trim()).filter(skill => skill);
                const updated = resume.courses?.map(c =>
                  c.id === course.id ? { ...c, skills: skillsArray } : c
                );
                updateResume('courses', updated);
              }}
              placeholder="مثال: JavaScript, React, Node.js (افصل بفاصلة)"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
            <p className="text-xs text-gray-500 mt-1">افصل المهارات بفاصلة</p>
          </div>
        </div>
      ))}
    </div>
  );

  const renderProjects = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">المشاريع والإنجازات</h3>
        <button
          onClick={addProject}
          className="bg-white/30 backdrop-blur-sm text-primary-600 px-4 py-2 rounded-lg hover:bg-white/50 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 border border-white/40"
        >
          + إضافة مشروع
        </button>
      </div>

      {resume.projects?.map((project, index) => (
        <div key={project.id} className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-semibold text-gray-800">مشروع #{index + 1}</h4>
            <button
              onClick={() => removeProject(project.id)}
              className="text-red-500 hover:text-red-700 transition-colors"
            >
              حذف
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">اسم المشروع</label>
              <input
                type="text"
                value={project.name}
                onChange={(e) => updateProject(project.id, 'name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                placeholder="اسم المشروع"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">رابط المشروع</label>
              <input
                type="url"
                value={project.url}
                onChange={(e) => updateProject(project.id, 'url', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                placeholder="https://example.com"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ البداية</label>
              <input
                type="month"
                value={project.startDate}
                onChange={(e) => updateProject(project.id, 'startDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ الانتهاء</label>
              <input
                type="month"
                value={project.endDate}
                onChange={(e) => updateProject(project.id, 'endDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">وصف المشروع</label>
            <textarea
              value={project.description}
              onChange={(e) => updateProject(project.id, 'description', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              placeholder="وصف تفصيلي للمشروع والتقنيات المستخدمة..."
            />
          </div>

          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">التقنيات المستخدمة</label>
            <input
              type="text"
              value={project.technologies?.join(', ') || ''}
              onChange={(e) => updateProject(project.id, 'technologies', e.target.value.split(', ').filter(t => t.trim()))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
              placeholder="React, Node.js, MongoDB (افصل بفاصلة)"
            />
          </div>
        </div>
      ))}

      {(!resume.projects || resume.projects.length === 0) && (
        <div className="text-center py-8 text-gray-500">
          لم تتم إضافة أي مشاريع بعد. اضغط على "إضافة مشروع" لبدء إضافة مشاريعك.
        </div>
      )}
    </div>
  );

  const renderReferences = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-xl font-bold text-gray-800">المراجع</h3>
        <button
          onClick={addReference}
          className="bg-white/30 backdrop-blur-sm text-primary-600 px-4 py-2 rounded-lg hover:bg-white/50 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 border border-white/40"
        >
          + إضافة مرجع
        </button>
      </div>

      {resume.references?.map((reference, index) => (
        <div key={reference.id} className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-center justify-between mb-4">
            <h4 className="font-semibold text-gray-800">مرجع #{index + 1}</h4>
            <button
              onClick={() => removeReference(reference.id)}
              className="text-red-500 hover:text-red-700 transition-colors"
            >
              حذف
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل</label>
              <input
                type="text"
                value={reference.name}
                onChange={(e) => updateReference(reference.id, 'name', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                placeholder="الاسم الكامل"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">المنصب</label>
              <input
                type="text"
                value={reference.position}
                onChange={(e) => updateReference(reference.id, 'position', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                placeholder="المنصب الوظيفي"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">الشركة</label>
              <input
                type="text"
                value={reference.company}
                onChange={(e) => updateReference(reference.id, 'company', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                placeholder="اسم الشركة"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">رقم الهاتف</label>
              <input
                type="tel"
                value={reference.phone}
                onChange={(e) => updateReference(reference.id, 'phone', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                placeholder="+963 xxx xxx xxx"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
              <input
                type="email"
                value={reference.email}
                onChange={(e) => updateReference(reference.id, 'email', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">طبيعة العلاقة</label>
              <input
                type="text"
                value={reference.relationship}
                onChange={(e) => updateReference(reference.id, 'relationship', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500"
                placeholder="مدير سابق، زميل عمل، إلخ"
              />
            </div>
          </div>
        </div>
      ))}

      {(!resume.references || resume.references.length === 0) && (
        <div className="text-center py-8 text-gray-500">
          لم تتم إضافة أي مراجع بعد. اضغط على "إضافة مرجع" لبدء إضافة المراجع.
        </div>
      )}
    </div>
  );

  const renderReview = () => (
    <div className="space-y-6">
      <h3 className="text-xl font-bold text-gray-800">مراجعة السيرة الذاتية</h3>

      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="font-semibold text-gray-800 mb-4">معاينة السيرة الذاتية</h4>

        {/* Personal Info Preview */}
        <div className="mb-6">
          <h5 className="font-medium text-gray-700 mb-2">المعلومات الشخصية:</h5>
          <p className="text-gray-600">
            {resume.personalInfo?.firstName} {resume.personalInfo?.lastName} - {resume.personalInfo?.title}
          </p>
          <p className="text-gray-600 text-sm">{resume.personalInfo?.summary}</p>
        </div>

        {/* Contact Info Preview */}
        <div className="mb-6">
          <h5 className="font-medium text-gray-700 mb-2">معلومات التواصل:</h5>
          <p className="text-gray-600 text-sm">
            {resume.contactInfo?.email} | {resume.contactInfo?.phone} | {resume.contactInfo?.city}
          </p>
        </div>

        {/* Experience Count */}
        <div className="mb-6">
          <h5 className="font-medium text-gray-700 mb-2">الخبرات:</h5>
          <p className="text-gray-600 text-sm">
            {resume.experiences?.length || 0} خبرة عملية
          </p>
        </div>

        {/* Skills Count */}
        <div className="mb-6">
          <h5 className="font-medium text-gray-700 mb-2">المهارات:</h5>
          <p className="text-gray-600 text-sm">
            {resume.skills?.length || 0} مهارة
          </p>
        </div>

        {/* Privacy Setting */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={resume.isPublic || false}
              onChange={(e) => updateResume('isPublic', e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <span className="mr-2 text-sm text-gray-700">
              جعل السيرة الذاتية مرئية للشركات (يمكن للشركات العثور على ملفك الشخصي)
            </span>
          </label>
        </div>

        {/* Print Preview */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <h5 className="font-medium text-gray-700 mb-3">معاينة الطباعة:</h5>
          <button
            onClick={() => {
              const printWindow = window.open('', '_blank');
              if (printWindow) {
                const completeResume: Resume = {
                  id: Date.now().toString(),
                  userId: 'current-user',
                  personalInfo: resume.personalInfo!,
                  contactInfo: resume.contactInfo!,
                  experiences: resume.experiences || [],
                  education: resume.education || [],
                  skills: resume.skills || [],
                  languages: resume.languages || [],
                  courses: resume.courses || [],
                  isPublic: resume.isPublic || false,
                  createdAt: new Date().toISOString(),
                  updatedAt: new Date().toISOString(),
                  views: 0
                };

                printWindow.document.write(`
                  <!DOCTYPE html>
                  <html dir="rtl">
                    <head>
                      <title>السيرة الذاتية - ${resume.personalInfo?.firstName} ${resume.personalInfo?.lastName}</title>
                      <meta charset="UTF-8">
                      <style>
                        body {
                          font-family: 'Arial', sans-serif;
                          margin: 20px;
                          line-height: 1.6;
                          color: #333;
                          direction: rtl;
                        }
                        .header {
                          text-align: center;
                          border-bottom: 2px solid #333;
                          padding-bottom: 20px;
                          margin-bottom: 30px;
                        }
                        .name {
                          font-size: 28px;
                          font-weight: bold;
                          margin-bottom: 10px;
                        }
                        .title {
                          font-size: 18px;
                          color: #666;
                          margin-bottom: 15px;
                        }
                        .contact {
                          font-size: 14px;
                          color: #555;
                        }
                        .section {
                          margin-bottom: 25px;
                        }
                        .section-title {
                          font-size: 18px;
                          font-weight: bold;
                          border-bottom: 1px solid #ccc;
                          padding-bottom: 5px;
                          margin-bottom: 15px;
                        }
                        .experience-item, .education-item {
                          margin-bottom: 15px;
                          padding-bottom: 10px;
                          border-bottom: 1px dotted #ddd;
                        }
                        .job-title, .degree {
                          font-weight: bold;
                          font-size: 16px;
                        }
                        .company, .institution {
                          color: #666;
                          font-style: italic;
                        }
                        .skills {
                          display: flex;
                          flex-wrap: wrap;
                          gap: 10px;
                        }
                        .skill {
                          background: #f0f0f0;
                          padding: 5px 10px;
                          border-radius: 15px;
                          font-size: 14px;
                        }
                        .mycv-logo {
                          position: fixed;
                          bottom: 20px;
                          left: 20px;
                          opacity: 0.6;
                          z-index: 1000;
                        }
                        @media print {
                          .mycv-logo { display: block !important; }
                          body { margin: 15px; }
                        }
                      </style>
                    </head>
                    <body>
                      <div class="header">
                        <div class="name">${resume.personalInfo?.firstName} ${resume.personalInfo?.lastName}</div>
                        <div class="title">${resume.personalInfo?.title || ''}</div>
                        <div class="contact">
                          ${resume.contactInfo?.phone || ''} | ${resume.contactInfo?.email || ''} | ${resume.contactInfo?.city || ''}
                        </div>
                      </div>

                      ${resume.personalInfo?.summary ? `
                        <div class="section">
                          <div class="section-title">نبذة شخصية</div>
                          <p>${resume.personalInfo.summary}</p>
                        </div>
                      ` : ''}

                      ${resume.experiences && resume.experiences.length > 0 ? `
                        <div class="section">
                          <div class="section-title">الخبرات العملية</div>
                          ${resume.experiences.map(exp => `
                            <div class="experience-item">
                              <div class="job-title">${exp.jobTitle}</div>
                              <div class="company">${exp.company} | ${exp.startDate} - ${exp.endDate || 'حتى الآن'}</div>
                              ${exp.description ? `<p>${exp.description}</p>` : ''}
                            </div>
                          `).join('')}
                        </div>
                      ` : ''}

                      ${resume.education && resume.education.length > 0 ? `
                        <div class="section">
                          <div class="section-title">التعليم</div>
                          ${resume.education.map(edu => `
                            <div class="education-item">
                              <div class="degree">${edu.degree}</div>
                              <div class="institution">${edu.institution} | ${edu.graduationYear}</div>
                            </div>
                          `).join('')}
                        </div>
                      ` : ''}

                      ${resume.skills && resume.skills.length > 0 ? `
                        <div class="section">
                          <div class="section-title">المهارات</div>
                          <div class="skills">
                            ${resume.skills.map(skill => `<span class="skill">${skill.name}</span>`).join('')}
                          </div>
                        </div>
                      ` : ''}

                      ${resume.languages && resume.languages.length > 0 ? `
                        <div class="section">
                          <div class="section-title">اللغات</div>
                          ${resume.languages.map(lang => `
                            <div>${lang.name} - ${lang.level}</div>
                          `).join('')}
                        </div>
                      ` : ''}

                      <div class="mycv-logo">
                        <img src="/images/MyCV Logo.jpg" alt="MyCv" style="width: 50px; height: auto;" />
                      </div>
                    </body>
                  </html>
                `);
                printWindow.document.close();
                printWindow.print();
              }
            }}
            className="px-4 py-2 bg-blue-600/80 backdrop-blur-sm text-white rounded-lg hover:bg-blue-700/90 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 text-sm border border-blue-500/40"
          >
            <img
              src="/images/jobs/Jobs -Personals/icons/print_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
              alt="طباعة"
              className="w-4 h-4 inline ml-1 opacity-80"
              style={{
                filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.6))'
              }}
            />
            معاينة الطباعة
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto bg-white rounded-xl shadow-lg">
      {/* Progress Steps */}
      <div className="border-b border-gray-200 p-4 md:p-6">
        {/* Mobile Progress */}
        <div className="md:hidden">
          <div className="flex items-center justify-between mb-4">
            <span className="text-sm font-medium text-gray-600">
              الخطوة {currentStep + 1} من {steps.length}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(((currentStep + 1) / steps.length) * 100)}%
            </span>
          </div>

          <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
            <div
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            ></div>
          </div>

          <div className="text-center">
            <div className="text-2xl mb-2 flex justify-center">
              {getStepIcon(currentStep, true, false)}
            </div>
            <div className="text-lg font-semibold text-gray-800">
              {steps[currentStep].title}
            </div>
          </div>
        </div>

        {/* Desktop Progress */}
        <div className="hidden md:block">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={`flex items-center ${index < steps.length - 1 ? 'flex-1' : ''}`}
              >
                <div className="flex flex-col items-center">
                  <div
                    className={`w-12 h-12 rounded-full flex items-center justify-center text-lg font-medium mb-2 transition-all duration-300 cursor-pointer group ${
                      currentStep >= step.id
                        ? 'bg-transparent border-2 border-green-500 text-green-600 shadow-lg'
                        : 'bg-transparent border-2 border-gray-300 text-gray-500'
                    } hover:border-green-500 hover:text-green-600 hover:shadow-lg hover:shadow-green-500/30 hover:scale-105`}
                    onClick={() => setCurrentStep(step.id)}
                    onMouseEnter={() => setHoveredStep(step.id)}
                    onMouseLeave={() => setHoveredStep(null)}
                  >
                    {getStepIcon(step.id, currentStep >= step.id, hoveredStep === step.id)}
                  </div>
                  <div className="text-center max-w-24">
                    <div className={`text-xs font-medium leading-tight transition-colors duration-300 ${
                      currentStep >= step.id ? 'text-green-600' : 'text-gray-500'
                    } hover:text-green-600`}>
                      {step.title}
                    </div>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div className={`flex-1 h-0.5 mx-2 mt-6 transition-colors duration-300 ${
                    currentStep > step.id ? 'bg-green-500' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Step Content */}
      <div className="p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          {currentStep === 0 && renderPersonalInfo()}
          {currentStep === 1 && renderContactInfo()}
          {currentStep === 2 && renderExperiences()}
          {currentStep === 3 && renderEducation()}
          {currentStep === 4 && renderSkills()}
          {currentStep === 5 && renderLanguages()}
          {currentStep === 6 && renderCourses()}
          {currentStep === 7 && renderProjects()}
          {currentStep === 8 && renderReferences()}
          {currentStep === 9 && renderReview()}
        </div>
      </div>

      {/* Navigation */}
      <div className="border-t border-gray-200 p-4 md:p-6">
        <div className="flex flex-col sm:flex-row justify-between gap-4">
          <button
            onClick={() => currentStep > 0 ? setCurrentStep(currentStep - 1) : onCancel()}
            className="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors flex items-center justify-center gap-2"
          >
            {currentStep === 0 ? (
              <>
                <span>❌</span>
                <span>إلغاء</span>
              </>
            ) : (
              <>
                <span>←</span>
                <span>السابق</span>
              </>
            )}
          </button>

          <div className="hidden sm:flex items-center gap-2 text-sm text-gray-500">
            <span>الخطوة {currentStep + 1} من {steps.length}</span>
            <div className="w-32 bg-gray-200 rounded-full h-2">
              <div
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
              ></div>
            </div>
          </div>

          {currentStep === steps.length - 1 ? (
            <div className="flex gap-3">
              <button
                onClick={handleSave}
                className="flex-1 px-6 py-3 bg-green-600/80 backdrop-blur-sm text-white rounded-lg hover:bg-green-700/90 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 flex items-center justify-center gap-2 font-medium border border-green-500/40"
              >
                <img
                  src="/images/jobs/Jobs -Personals/icons/save_63dp_E3E3E3_FILL0_wght600_GRAD200_opsz48.png"
                  alt="حفظ"
                  className="w-5 h-5 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.6))'
                  }}
                />
                <span>حفظ السيرة الذاتية</span>
              </button>

              <button
                onClick={() => {
                  const printWindow = window.open('', '_blank');
                  if (printWindow) {
                    printWindow.document.write(`
                      <!DOCTYPE html>
                      <html dir="rtl">
                        <head>
                          <title>السيرة الذاتية - ${resume.personalInfo?.firstName} ${resume.personalInfo?.lastName}</title>
                          <meta charset="UTF-8">
                          <style>
                            body {
                              font-family: 'Arial', sans-serif;
                              margin: 20px;
                              line-height: 1.6;
                              color: #333;
                              direction: rtl;
                            }
                            .header {
                              text-align: center;
                              border-bottom: 2px solid #333;
                              padding-bottom: 20px;
                              margin-bottom: 30px;
                            }
                            .name {
                              font-size: 28px;
                              font-weight: bold;
                              margin-bottom: 10px;
                            }
                            .mycv-logo {
                              position: fixed;
                              bottom: 20px;
                              left: 20px;
                              opacity: 0.6;
                              z-index: 1000;
                            }
                            @media print {
                              .mycv-logo { display: block !important; }
                              body { margin: 15px; }
                            }
                          </style>
                        </head>
                        <body>
                          <div class="header">
                            <div class="name">${resume.personalInfo?.firstName} ${resume.personalInfo?.lastName}</div>
                          </div>

                          <div class="mycv-logo">
                            <img src="/images/MyCV Logo.jpg" alt="MyCv" style="width: 50px; height: auto;" />
                          </div>
                        </body>
                      </html>
                    `);
                    printWindow.document.close();
                    printWindow.print();
                  }
                }}
                className="px-4 py-3 bg-blue-600/80 backdrop-blur-sm text-white rounded-lg hover:bg-blue-700/90 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 flex items-center justify-center gap-2 font-medium border border-blue-500/40"
              >
                <img
                  src="/images/jobs/Jobs -Personals/icons/print_63dp_E3E3E3_FILL0_wght400_GRAD0_opsz48.png"
                  alt="طباعة"
                  className="w-5 h-5 opacity-80"
                  style={{
                    filter: 'drop-shadow(0 0 4px rgba(255, 255, 255, 0.6))'
                  }}
                />
                <span>طباعة</span>
              </button>
            </div>
          ) : (
            <button
              onClick={() => setCurrentStep(currentStep + 1)}
              className="px-6 py-3 bg-white/30 backdrop-blur-sm text-primary-600 rounded-lg hover:bg-white/50 active:shadow-lg active:shadow-green-400/50 active:scale-105 transition-all duration-200 flex items-center justify-center gap-2 font-medium border border-white/40"
            >
              <span>التالي</span>
              <span>→</span>
            </button>
          )}
        </div>
      </div>

      <style jsx global>{`
        @keyframes greenGlow {
          0% {
            filter: grayscale(0) drop-shadow(0 0 20px rgba(34, 197, 94, 1)) brightness(1.2);
          }
          100% {
            filter: grayscale(0) drop-shadow(0 0 30px rgba(34, 197, 94, 1)) brightness(1.4);
          }
        }
      `}</style>
    </div>
  );
};

export default ResumeBuilder;
