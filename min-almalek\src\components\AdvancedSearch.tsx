'use client';

import { useState, useEffect } from 'react';
import { DataService, SearchFilters, categories, locations, Ad } from '@/lib/data';

interface AdvancedSearchProps {
  onSearch?: (filters: SearchFilters) => void;
  category?: string;
}

const AdvancedSearch = ({ onSearch, category }: AdvancedSearchProps) => {
  const [filters, setFilters] = useState<SearchFilters>({
    keyword: '',
    category: category || '',
    governorate: '',
    priceMin: undefined,
    priceMax: undefined,
    currency: undefined,
    condition: undefined,
    sellerType: undefined,
    verified: false,
    featured: false,
    hasImages: false,
    datePosted: undefined,
    sortBy: 'newest'
  });

  const [searchResults, setSearchResults] = useState<Ad[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);

  // تحديث النتائج عند تغيير الفلاتر
  useEffect(() => {
    if (showResults) {
      performSearch();
    }
  }, [filters, showResults]);

  const performSearch = () => {
    setIsSearching(true);

    // محاكاة تأخير البحث
    setTimeout(() => {
      const results = DataService.searchAds(filters);
      setSearchResults(results);
      setIsSearching(false);
    }, 500);
  };

  const handleSearch = () => {
    setShowResults(true);
    performSearch();
    if (onSearch) {
      onSearch(filters);
    }
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      keyword: '',
      category: category || '',
      governorate: '',
      priceMin: undefined,
      priceMax: undefined,
      currency: undefined,
      condition: undefined,
      sellerType: undefined,
      verified: false,
      featured: false,
      hasImages: false,
      datePosted: undefined,
      sortBy: 'newest'
    });
    setShowResults(false);
    setSearchResults([]);
  };

  const conditions = [
    { id: 'new', name: 'جديد' },
    { id: 'renovated', name: 'معفش' },
    { id: 'used', name: 'مستعمل' },
    { id: 'refurbished', name: 'مجدد' },
    { id: 'used-excellent', name: 'مستعمل - ممتاز' },
    { id: 'used-good', name: 'مستعمل - جيد' },
    { id: 'used-fair', name: 'مستعمل - مقبول' }
  ];

  const dateOptions = [
    { id: 'today', name: 'اليوم' },
    { id: 'week', name: 'آخر أسبوع' },
    { id: 'month', name: 'آخر شهر' },
    { id: 'all', name: 'كل الأوقات' }
  ];

  const sortOptions = [
    { id: 'newest', name: 'الأحدث أولاً' },
    { id: 'oldest', name: 'الأقدم أولاً' },
    { id: 'price-low', name: 'السعر: من الأقل للأعلى' },
    { id: 'price-high', name: 'السعر: من الأعلى للأقل' },
    { id: 'most-viewed', name: 'الأكثر مشاهدة' }
  ];

  // فئات الأسعار بالليرة السورية
  const priceRangesSYP = [
    { id: 'under-100k', name: 'أقل من 100 ألف', min: 0, max: 100000 },
    { id: '100k-500k', name: '100 - 500 ألف', min: 100000, max: 500000 },
    { id: '500k-1m', name: '500 ألف - 1 مليون', min: 500000, max: 1000000 },
    { id: '1m-2m', name: '1 - 2 مليون', min: 1000000, max: 2000000 },
    { id: '2m-5m', name: '2 - 5 مليون', min: 2000000, max: 5000000 },
    { id: '5m-10m', name: '5 - 10 مليون', min: 5000000, max: 10000000 },
    { id: '10m-20m', name: '10 - 20 مليون', min: 10000000, max: 20000000 },
    { id: '20m-50m', name: '20 - 50 مليون', min: 20000000, max: 50000000 },
    { id: 'over-50m', name: 'أكثر من 50 مليون', min: 50000000, max: null }
  ];

  // فئات الأسعار بالدولار
  const priceRangesUSD = [
    { id: 'under-100', name: 'أقل من 100 دولار', min: 0, max: 100 },
    { id: '100-300', name: '100 - 300 دولار', min: 100, max: 300 },
    { id: '300-500', name: '300 - 500 دولار', min: 300, max: 500 },
    { id: '500-1k', name: '500 - 1000 دولار', min: 500, max: 1000 },
    { id: '1k-2k', name: '1000 - 2000 دولار', min: 1000, max: 2000 },
    { id: '2k-5k', name: '2000 - 5000 دولار', min: 2000, max: 5000 },
    { id: '5k-10k', name: '5000 - 10000 دولار', min: 5000, max: 10000 },
    { id: '10k-20k', name: '10000 - 20000 دولار', min: 10000, max: 20000 },
    { id: 'over-20k', name: 'أكثر من 20000 دولار', min: 20000, max: null }
  ];

  // فئات الأسعار باليورو
  const priceRangesEUR = [
    { id: 'under-100', name: 'أقل من 100 يورو', min: 0, max: 100 },
    { id: '100-300', name: '100 - 300 يورو', min: 100, max: 300 },
    { id: '300-500', name: '300 - 500 يورو', min: 300, max: 500 },
    { id: '500-1k', name: '500 - 1000 يورو', min: 500, max: 1000 },
    { id: '1k-2k', name: '1000 - 2000 يورو', min: 1000, max: 2000 },
    { id: '2k-5k', name: '2000 - 5000 يورو', min: 2000, max: 5000 },
    { id: '5k-10k', name: '5000 - 10000 يورو', min: 5000, max: 10000 },
    { id: '10k-20k', name: '10000 - 20000 يورو', min: 10000, max: 20000 },
    { id: 'over-20k', name: 'أكثر من 20000 يورو', min: 20000, max: null }
  ];

  const currencies = [
    { id: 'SYP', name: 'ليرة سورية', symbol: 'ل.س' },
    { id: 'USD', name: 'دولار أمريكي', symbol: '$' },
    { id: 'EUR', name: 'يورو', symbol: '€' }
  ];

  const getCurrentPriceRanges = () => {
    switch (filters.currency) {
      case 'USD':
        return priceRangesUSD;
      case 'EUR':
        return priceRangesEUR;
      default:
        return priceRangesSYP;
    }
  };

  const handlePriceRangeSelect = (range: any) => {
    setFilters(prev => ({
      ...prev,
      priceMin: range.min,
      priceMax: range.max
    }));
  };



  return (
    <div className="space-y-6">
      {/* البحث بالكلمات المفتاحية */}
      {category !== 'services' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            البحث بالكلمات المفتاحية
          </label>
          <input
            type="text"
            value={filters.keyword || ''}
            onChange={(e) => handleFilterChange('keyword', e.target.value)}
            placeholder="ابحث عن..."
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          />
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* التصنيف - مخفي للخدمات */}
        {category !== 'services' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              التصنيف
            </label>
            <select
              value={filters.category}
              onChange={(e) => handleFilterChange('category', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">جميع التصنيفات</option>
              {Object.entries(categories).map(([key, cat]) => (
                <option key={key} value={key}>{cat.name}</option>
              ))}
            </select>
          </div>
        )}

        {/* الموقع */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            المحافظة
          </label>
          <select
            value={filters.governorate}
            onChange={(e) => handleFilterChange('governorate', e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <option value="">جميع المحافظات</option>
            {Object.entries(locations).map(([key, loc]) => (
              <option key={key} value={key}>{loc.name}</option>
            ))}
          </select>
        </div>

        {/* الحالة - مخفي للخدمات */}
        {category !== 'services' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الحالة
            </label>
            <select
              value={filters.condition}
              onChange={(e) => handleFilterChange('condition', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">جميع الحالات</option>
              {conditions.map(cond => (
                <option key={cond.id} value={cond.id}>{cond.name}</option>
              ))}
            </select>
          </div>
        )}

        {/* العملة - مخفي للخدمات */}
        {category !== 'services' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              العملة
            </label>
            <select
              value={filters.currency || 'SYP'}
              onChange={(e) => handleFilterChange('currency', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              {currencies.map(currency => (
                <option key={currency.id} value={currency.id}>
                  {currency.name} ({currency.symbol})
                </option>
              ))}
            </select>
          </div>
        )}

        {/* السعر الأدنى - مخفي للخدمات */}
        {category !== 'services' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              السعر الأدنى
            </label>
            <input
              type="number"
              value={filters.priceMin || ''}
              onChange={(e) => handleFilterChange('priceMin', e.target.value ? parseInt(e.target.value) : undefined)}
              placeholder="0"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>
        )}

        {/* السعر الأعلى - مخفي للخدمات */}
        {category !== 'services' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              السعر الأعلى
            </label>
            <input
              type="number"
              value={filters.priceMax || ''}
              onChange={(e) => handleFilterChange('priceMax', e.target.value ? parseInt(e.target.value) : undefined)}
              placeholder="بلا حدود"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            />
          </div>
        )}

        {/* نوع البائع - مخفي للخدمات */}
        {category !== 'services' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              نوع البائع
            </label>
            <select
              value={filters.sellerType || ''}
              onChange={(e) => handleFilterChange('sellerType', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">جميع البائعين</option>
              <option value="individual">أفراد</option>
              <option value="business">شركات</option>
              <option value="real-estate-office">مكاتب عقارية</option>
            </select>
          </div>
        )}

        {/* تاريخ النشر - مخفي للخدمات */}
        {category !== 'services' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              تاريخ النشر
            </label>
            <select
              value={filters.datePosted}
              onChange={(e) => handleFilterChange('datePosted', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">كل الأوقات</option>
              {dateOptions.map(date => (
                <option key={date.id} value={date.id}>{date.name}</option>
              ))}
            </select>
          </div>
        )}
      </div>

      {/* حقول خاصة بالسيارات */}
      {category === 'cars' && (
        <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <h4 className="text-md font-semibold text-blue-800 mb-4 flex items-center gap-2">
            🚗 فلاتر السيارات
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* نوع الإعلان */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">نوع الإعلان</label>
              <select
                value={filters.carListingType || ''}
                onChange={(e) => handleFilterChange('carListingType', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع الأنواع</option>
                <option value="sale">للبيع</option>
                <option value="rent">للإيجار</option>
                <option value="exchange">للمقايضة</option>
              </select>
            </div>

            {/* نوع السيارة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">نوع السيارة</label>
              <select
                value={filters.carType || ''}
                onChange={(e) => handleFilterChange('carType', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع الأنواع</option>
                <option value="private">خصوصي</option>
                <option value="commercial">عمومي</option>
              </select>
            </div>

            {/* ناقل الحركة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">ناقل الحركة</label>
              <select
                value={filters.transmission || ''}
                onChange={(e) => handleFilterChange('transmission', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع الأنواع</option>
                <option value="automatic">أوتوماتيك</option>
                <option value="manual">يدوي</option>
                <option value="hybrid">هجين</option>
              </select>
            </div>

            {/* سنة الصنع */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">سنة الصنع</label>
              <select
                value={filters.year || ''}
                onChange={(e) => handleFilterChange('year', e.target.value ? parseInt(e.target.value) : undefined)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع السنوات</option>
                {Array.from({ length: 54 }, (_, i) => 2024 - i).map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>

            {/* الماركة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الماركة</label>
              <select
                value={filters.brand || ''}
                onChange={(e) => handleFilterChange('brand', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع الماركات</option>
                <optgroup label="الماركات اليابانية">
                  <option value="toyota">تويوتا (Toyota)</option>
                  <option value="honda">هوندا (Honda)</option>
                  <option value="nissan">نيسان (Nissan)</option>
                  <option value="mazda">مازدا (Mazda)</option>
                  <option value="mitsubishi">ميتسوبيشي (Mitsubishi)</option>
                  <option value="lexus">لكزس (Lexus)</option>
                </optgroup>
                <optgroup label="الماركات الكورية">
                  <option value="hyundai">هيونداي (Hyundai)</option>
                  <option value="kia">كيا (Kia)</option>
                  <option value="genesis">جينيسيس (Genesis)</option>
                </optgroup>
                <optgroup label="الماركات الألمانية">
                  <option value="bmw">بي إم دبليو (BMW)</option>
                  <option value="mercedes">مرسيدس بنز (Mercedes-Benz)</option>
                  <option value="audi">أودي (Audi)</option>
                  <option value="volkswagen">فولكس فاجن (Volkswagen)</option>
                </optgroup>
                <optgroup label="الماركات الأمريكية">
                  <option value="ford">فورد (Ford)</option>
                  <option value="chevrolet">شيفروليه (Chevrolet)</option>
                  <option value="tesla">تيسلا (Tesla)</option>
                </optgroup>
                <optgroup label="الماركات الفرنسية">
                  <option value="peugeot">بيجو (Peugeot)</option>
                  <option value="renault">رينو (Renault)</option>
                  <option value="citroen">ستروين (Citroën)</option>
                </optgroup>
                <optgroup label="الماركات الصينية">
                  <option value="geely">جيلي (Geely)</option>
                  <option value="chery">شيري (Chery)</option>
                  <option value="byd">بي واي دي (BYD)</option>
                </optgroup>
                <optgroup label="ماركات أخرى">
                  <option value="other">أخرى</option>
                </optgroup>
              </select>
            </div>

            {/* نوع الوقود */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">نوع الوقود</label>
              <select
                value={filters.fuelType || ''}
                onChange={(e) => handleFilterChange('fuelType', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع الأنواع</option>
                <option value="gasoline">بنزين</option>
                <option value="diesel">ديزل</option>
                <option value="electric">كهربائي</option>
                <option value="hybrid">هجين</option>
              </select>
            </div>

            {/* المسافة المقطوعة - الحد الأدنى */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المسافة المقطوعة - من (كم)</label>
              <input
                type="number"
                value={filters.mileageMin || ''}
                onChange={(e) => handleFilterChange('mileageMin', e.target.value ? parseInt(e.target.value) : undefined)}
                placeholder="0"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            {/* المسافة المقطوعة - الحد الأعلى */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">المسافة المقطوعة - إلى (كم)</label>
              <input
                type="number"
                value={filters.mileageMax || ''}
                onChange={(e) => handleFilterChange('mileageMax', e.target.value ? parseInt(e.target.value) : undefined)}
                placeholder="بلا حدود"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>

            {/* قوة المحرك */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">قوة المحرك (cc)</label>
              <select
                value={filters.engineCapacity || ''}
                onChange={(e) => handleFilterChange('engineCapacity', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع الأحجام</option>
                <option value="800">800 cc</option>
                <option value="1000">1000 cc</option>
                <option value="1200">1200 cc</option>
                <option value="1300">1300 cc</option>
                <option value="1400">1400 cc</option>
                <option value="1500">1500 cc</option>
                <option value="1600">1600 cc</option>
                <option value="1800">1800 cc</option>
                <option value="2000">2000 cc</option>
                <option value="2200">2200 cc</option>
                <option value="2400">2400 cc</option>
                <option value="2500">2500 cc</option>
                <option value="3000">3000 cc</option>
                <option value="3500">3500 cc</option>
                <option value="4000">4000 cc</option>
                <option value="5000">5000 cc</option>
                <option value="other">أخرى</option>
              </select>
            </div>

            {/* حالة التأمين */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">حالة التأمين</label>
              <select
                value={filters.insurance || ''}
                onChange={(e) => handleFilterChange('insurance', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع الحالات</option>
                <option value="full">تأمين شامل</option>
                <option value="basic">تأمين أساسي</option>
                <option value="none">بدون تأمين</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* حقول خاصة بالإلكترونيات */}
      {category === 'electronics' && (
        <div className="mt-6 p-4 bg-purple-50 rounded-lg border border-purple-200">
          <h4 className="text-md font-semibold text-purple-800 mb-4 flex items-center gap-2">
            📱 فلاتر الإلكترونيات
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* فئة الإلكترونيات */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">فئة الإلكترونيات</label>
              <select
                value={filters.electronicsCategory || ''}
                onChange={(e) => handleFilterChange('electronicsCategory', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع الفئات</option>
                <option value="smartphones">الهواتف الذكية</option>
                <option value="laptops">أجهزة الكمبيوتر المحمولة</option>
                <option value="tablets">الأجهزة اللوحية</option>
                <option value="computers">أجهزة الكمبيوتر المكتبية</option>
                <option value="accessories">الإكسسوارات</option>
                <option value="gaming">الألعاب والترفيه</option>
              </select>
            </div>

            {/* الفئة الفرعية */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">النوع المحدد</label>
              <select
                value={filters.electronicsSubcategory || ''}
                onChange={(e) => handleFilterChange('electronicsSubcategory', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع الأنواع</option>
                {filters.electronicsCategory === 'smartphones' && (
                  <>
                    <option value="iphone">آيفون</option>
                    <option value="samsung">سامسونغ</option>
                    <option value="huawei">هواوي</option>
                    <option value="xiaomi">شاومي</option>
                    <option value="oppo">أوبو</option>
                    <option value="other-phones">هواتف أخرى</option>
                  </>
                )}
                {filters.electronicsCategory === 'laptops' && (
                  <>
                    <option value="gaming">ألعاب</option>
                    <option value="business">أعمال</option>
                    <option value="ultrabook">ألترابوك</option>
                    <option value="budget">اقتصادية</option>
                  </>
                )}
                {filters.electronicsCategory === 'tablets' && (
                  <>
                    <option value="ipad">آيباد</option>
                    <option value="android">أندرويد</option>
                    <option value="windows">ويندوز</option>
                  </>
                )}
                {filters.electronicsCategory === 'computers' && (
                  <>
                    <option value="gaming-pc">ألعاب</option>
                    <option value="office-pc">مكتبية</option>
                    <option value="workstation">محطات عمل</option>
                    <option value="all-in-one">الكل في واحد</option>
                  </>
                )}
                {filters.electronicsCategory === 'accessories' && (
                  <>
                    <option value="headphones">سماعات</option>
                    <option value="keyboards">لوحات مفاتيح</option>
                    <option value="mice">فأرات</option>
                    <option value="monitors">شاشات</option>
                    <option value="speakers">مكبرات صوت</option>
                    <option value="chargers">شواحن</option>
                  </>
                )}
                {filters.electronicsCategory === 'gaming' && (
                  <>
                    <option value="consoles">أجهزة الألعاب</option>
                    <option value="controllers">أذرع التحكم</option>
                    <option value="games">الألعاب</option>
                  </>
                )}
              </select>
            </div>

            {/* الماركة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الماركة</label>
              <select
                value={filters.electronicsBrand || ''}
                onChange={(e) => handleFilterChange('electronicsBrand', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع الماركات</option>
                <option value="apple">Apple</option>
                <option value="samsung">Samsung</option>
                <option value="huawei">Huawei</option>
                <option value="xiaomi">Xiaomi</option>
                <option value="oppo">OPPO</option>
                <option value="sony">Sony</option>
                <option value="lg">LG</option>
                <option value="dell">Dell</option>
                <option value="hp">HP</option>
                <option value="lenovo">Lenovo</option>
                <option value="asus">ASUS</option>
                <option value="acer">Acer</option>
                <option value="msi">MSI</option>
                <option value="other">أخرى</option>
              </select>
            </div>

            {/* حالة الجهاز */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">حالة الجهاز</label>
              <select
                value={filters.electronicsCondition || ''}
                onChange={(e) => handleFilterChange('electronicsCondition', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع الحالات</option>
                <option value="new">جديد</option>
                <option value="open-box">علبة مفتوحة</option>
                <option value="used">مستعمل</option>
                <option value="refurbished">مجدد</option>
              </select>
            </div>

            {/* الضمان */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الضمان</label>
              <select
                value={filters.warranty || ''}
                onChange={(e) => handleFilterChange('warranty', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع أنواع الضمان</option>
                <option value="لا يوجد ضمان">لا يوجد ضمان</option>
                <option value="ضمان الوكيل - سنة واحدة">ضمان الوكيل - سنة واحدة</option>
                <option value="ضمان الوكيل - سنتان">ضمان الوكيل - سنتان</option>
                <option value="ضمان الوكيل - 3 سنوات">ضمان الوكيل - 3 سنوات</option>
                <option value="ضمان المحل - 6 أشهر">ضمان المحل - 6 أشهر</option>
                <option value="ضمان المحل - سنة واحدة">ضمان المحل - سنة واحدة</option>
                <option value="ضمان دولي">ضمان دولي</option>
                <option value="ضمان منتهي الصلاحية">ضمان منتهي الصلاحية</option>
              </select>
            </div>

            {/* الذاكرة الداخلية (للهواتف واللابتوب) */}
            {(filters.electronicsCategory === 'smartphones' || filters.electronicsCategory === 'laptops' || filters.electronicsCategory === 'tablets') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">الذاكرة الداخلية (RAM)</label>
                <select
                  value={filters.ram || ''}
                  onChange={(e) => handleFilterChange('ram', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">جميع الأحجام</option>
                  <option value="2 غيغا">2 غيغا</option>
                  <option value="3 غيغا">3 غيغا</option>
                  <option value="4 غيغا">4 غيغا</option>
                  <option value="6 غيغا">6 غيغا</option>
                  <option value="8 غيغا">8 غيغا</option>
                  <option value="12 غيغا">12 غيغا</option>
                  <option value="16 غيغا">16 غيغا</option>
                  <option value="32 غيغا">32 غيغا</option>
                </select>
              </div>
            )}

            {/* ذاكرة التخزين */}
            {(filters.electronicsCategory === 'smartphones' || filters.electronicsCategory === 'laptops' || filters.electronicsCategory === 'tablets') && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">ذاكرة التخزين</label>
                <select
                  value={filters.storage || ''}
                  onChange={(e) => handleFilterChange('storage', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">جميع الأحجام</option>
                  <option value="8 غيغا">8 غيغا</option>
                  <option value="32 غيغا">32 غيغا</option>
                  <option value="64 غيغا">64 غيغا</option>
                  <option value="128 غيغا">128 غيغا</option>
                  <option value="256 غيغا">256 غيغا</option>
                  <option value="512 غيغا">512 غيغا</option>
                  <option value="1 تيرا بايت">1 تيرا بايت</option>
                  <option value="2 تيرا بايت">2 تيرا بايت</option>
                </select>
              </div>
            )}

            {/* حالة البطارية (للهواتف) */}
            {filters.electronicsCategory === 'smartphones' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">حالة البطارية</label>
                <select
                  value={filters.battery || ''}
                  onChange={(e) => handleFilterChange('battery', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">جميع الحالات</option>
                  <option value="ممتازة">ممتازة</option>
                  <option value="جيدة جداً">جيدة جداً</option>
                  <option value="جيدة">جيدة</option>
                </select>
              </div>
            )}

            {/* نوع الشريحة (للهواتف) */}
            {filters.electronicsCategory === 'smartphones' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">نوع الشريحة</label>
                <select
                  value={filters.simType || ''}
                  onChange={(e) => handleFilterChange('simType', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value="">جميع الأنواع</option>
                  <option value="SIM 1">SIM 1</option>
                  <option value="SIM 2">SIM 2</option>
                  <option value="e-SIM">e-SIM</option>
                  <option value="SIM 1 + SIM 2">SIM 1 + SIM 2</option>
                  <option value="SIM 1 + e-SIM">SIM 1 + e-SIM</option>
                  <option value="SIM 2 + e-SIM">SIM 2 + e-SIM</option>
                  <option value="SIM 1 + SIM 2 + e-SIM">SIM 1 + SIM 2 + e-SIM</option>
                </select>
              </div>
            )}

            {/* العلبة الأصلية */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">العلبة الأصلية</label>
              <select
                value={filters.originalBox !== undefined ? (filters.originalBox ? 'true' : 'false') : ''}
                onChange={(e) => handleFilterChange('originalBox', e.target.value === '' ? undefined : e.target.value === 'true')}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">جميع الحالات</option>
                <option value="true">متوفرة</option>
                <option value="false">غير متوفرة</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* حقول خاصة بالخدمات */}
      {category === 'services' && (
        <div className="mt-6 p-4 bg-orange-50 rounded-lg border border-orange-200">
          <h4 className="text-md font-semibold text-orange-800 mb-4 flex items-center gap-2">
            🛠️ فلاتر الخدمات
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {/* نوع الخدمة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">نوع الخدمة</label>
              <select
                value={filters.serviceType || ''}
                onChange={(e) => handleFilterChange('serviceType', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                <option value="">جميع الخدمات</option>
                <option value="maintenance">صيانة وإصلاح</option>
                <option value="cleaning">تنظيف</option>
                <option value="delivery">توصيل</option>
                <option value="education">تعليم ودروس</option>
                <option value="health">خدمات صحية</option>
                <option value="legal">خدمات قانونية</option>
                <option value="design">تصميم</option>
                <option value="translation">ترجمة</option>
                <option value="technical">خدمات تقنية</option>
                <option value="financial">خدمات مالية</option>
              </select>
            </div>

            {/* مستوى الخبرة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">مستوى الخبرة</label>
              <select
                value={filters.experienceLevel || ''}
                onChange={(e) => handleFilterChange('experienceLevel', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                <option value="">جميع المستويات</option>
                <option value="beginner">مبتدئ (أقل من سنة)</option>
                <option value="intermediate">متوسط (1-3 سنوات)</option>
                <option value="advanced">متقدم (3-5 سنوات)</option>
                <option value="expert">خبير (أكثر من 5 سنوات)</option>
              </select>
            </div>

            {/* التوفر */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">التوفر</label>
              <select
                value={filters.availability || ''}
                onChange={(e) => handleFilterChange('availability', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                <option value="">جميع الأوقات</option>
                <option value="available">متاح الآن</option>
                <option value="morning">صباحاً</option>
                <option value="afternoon">بعد الظهر</option>
                <option value="evening">مساءً</option>
                <option value="weekend">نهاية الأسبوع</option>
                <option value="flexible">مرن</option>
              </select>
            </div>

            {/* نوع مقدم الخدمة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">نوع مقدم الخدمة</label>
              <select
                value={filters.providerType || ''}
                onChange={(e) => handleFilterChange('providerType', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                <option value="">جميع الأنواع</option>
                <option value="individual">فردي</option>
                <option value="company">شركة</option>
                <option value="freelancer">مستقل</option>
                <option value="team">فريق عمل</option>
              </select>
            </div>

            {/* التقييم */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">التقييم الأدنى</label>
              <select
                value={filters.minRating || ''}
                onChange={(e) => handleFilterChange('minRating', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                <option value="">جميع التقييمات</option>
                <option value="5">⭐⭐⭐⭐⭐ (5 نجوم)</option>
                <option value="4">⭐⭐⭐⭐ (4 نجوم فأكثر)</option>
                <option value="3">⭐⭐⭐ (3 نجوم فأكثر)</option>
                <option value="2">⭐⭐ (نجمتان فأكثر)</option>
              </select>
            </div>

            {/* الضمان */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">ضمان الخدمة</label>
              <select
                value={filters.serviceWarranty || ''}
                onChange={(e) => handleFilterChange('serviceWarranty', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                <option value="">جميع الأنواع</option>
                <option value="no-warranty">بدون ضمان</option>
                <option value="1-month">شهر واحد</option>
                <option value="3-months">3 أشهر</option>
                <option value="6-months">6 أشهر</option>
                <option value="1-year">سنة واحدة</option>
                <option value="lifetime">ضمان مدى الحياة</option>
              </select>
            </div>
          </div>
        </div>
      )}

      {/* فئات الأسعار السريعة - مخفي للخدمات */}
      {category !== 'services' && (
        <div className="mt-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          فئات الأسعار السريعة ({
            filters.currency === 'USD' ? 'دولار' :
            filters.currency === 'EUR' ? 'يورو' :
            'ليرة سورية'
          })
        </label>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
          {getCurrentPriceRanges().map(range => (
            <button
              key={range.id}
              onClick={() => handlePriceRangeSelect(range)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors border ${
                filters.priceMin === range.min && filters.priceMax === range.max
                  ? 'bg-primary-600 text-white border-primary-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
              }`}
            >
              {range.name}
            </button>
          ))}
          {/* زر مسح فئة السعر */}
          <button
            onClick={() => {
              handleFilterChange('priceMin', undefined);
              handleFilterChange('priceMax', undefined);
            }}
            className="px-3 py-2 rounded-lg text-sm font-medium transition-colors border border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400"
          >
            مسح السعر
          </button>
        </div>
        <div className="mt-2 text-xs text-gray-500">
          💡 انقر على فئة سعر لتطبيقها تلقائياً، أو أدخل مبلغ مخصص في الحقول أعلاه
        </div>
      </div>
      )}

      {/* ترتيب النتائج */}
      <div className="mt-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          ترتيب النتائج
        </label>
        <div className="flex flex-wrap gap-2">
          {sortOptions.map(option => (
            <button
              key={option.id}
              onClick={() => handleFilterChange('sortBy', option.id)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                filters.sortBy === option.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {option.name}
            </button>
          ))}
        </div>
      </div>

      {/* أزرار البحث */}
      <div className="flex gap-4 mt-8">
        <button
          onClick={handleSearch}
          className="flex-1 bg-primary-600 text-white py-3 rounded-lg hover:bg-primary-700 transition-colors font-semibold"
        >
          🔍 بحث متقدم
        </button>
        <button
          onClick={clearFilters}
          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
        >
          مسح الكل
        </button>
      </div>

      {/* عدد النتائج المتوقعة */}
      <div className="mt-4 text-center text-sm text-gray-600">
        النتائج المتوقعة: <span className="font-semibold text-primary-600">1,234 إعلان</span>
      </div>
    </div>
  );
};

export default AdvancedSearch;
