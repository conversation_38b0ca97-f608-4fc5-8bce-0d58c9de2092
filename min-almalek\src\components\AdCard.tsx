import Link from 'next/link';
import { useState } from 'react';
import { Ad, locations } from '@/lib/data';
import VerificationBadge from './VerificationBadge';
import NewVerificationBadge from './NewVerificationBadge';
import SafeTimeDisplay from './SafeTimeDisplay';
import MessageModal from './MessageModal';
import { MessageButton, FavoriteButton } from '@/components/ProtectedActionButton';
import { useFavoriteAction, useMessageAction } from '@/hooks/useAuthAction';

interface AdCardProps {
  ad: Ad;
  viewMode?: 'grid' | 'list';
}

const AdCard = ({ ad, viewMode = 'grid' }: AdCardProps) => {
  const { executeAction: executeFavoriteAction } = useFavoriteAction();
  const { executeAction: executeMessageAction } = useMessageAction();

  const [isFavorite, setIsFavorite] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [messageModal, setMessageModal] = useState<{
    isOpen: boolean;
    adId: number;
    recipientName: string;
    recipientPhone: string;
    adTitle: string;
  }>({
    isOpen: false,
    adId: 0,
    recipientName: '',
    recipientPhone: '',
    adTitle: ''
  });

  const formatPrice = (price: number, currency: string) => {
    const currencySymbol = currency === 'SYP' ? 'ل.س' : currency === 'USD' ? '$' : '€';
    return `${price.toLocaleString()} ${currencySymbol}`;
  };

  const getLocationName = () => {
    const location = locations[ad.location.governorate as keyof typeof locations];
    return location ? location.name : ad.location.governorate;
  };



  const toggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    executeFavoriteAction(() => {
      setIsFavorite(!isFavorite);
      console.log('تم إضافة/إزالة الإعلان من المفضلة:', ad.title);
    });
  };

  const openMessageModal = () => {
    executeMessageAction(() => {
      setMessageModal({
        isOpen: true,
        adId: ad.id,
        recipientName: ad.seller?.name || 'البائع',
        recipientPhone: '+963988652401', // Default phone or ad.seller.phone
        adTitle: ad.title
      });
    });
  };

  const closeMessageModal = () => {
    setMessageModal(prev => ({ ...prev, isOpen: false }));
  };

  const handleImageError = () => {
    setImageError(true);
  };

  if (viewMode === 'list') {
    return (
      <Link
        href={`/ad/${ad.id}`}
        className="bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 p-4 border border-gray-100 hover:border-primary-200 group block"
        suppressHydrationWarning={true}
      >
        <div className="flex gap-4">
          {/* صورة الإعلان */}
          <div className="relative w-32 h-24 flex-shrink-0 overflow-hidden rounded-lg">
            {!imageError && ad.images && ad.images.length > 0 ? (
              <img
                src={ad.images[0]}
                alt={ad.title}
                className="w-full h-full object-cover"
                onError={handleImageError}
              />
            ) : (
              <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                <span className="text-gray-400 text-2xl">🖼️</span>
              </div>
            )}

            {/* شارات */}
            <div className="absolute top-2 right-2 flex flex-col gap-1">
              {ad.featured && (
                <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                  مميز
                </span>
              )}
              {ad.status === 'active' && (
                <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                  نشط
                </span>
              )}
            </div>
          </div>

          {/* محتوى الإعلان */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between mb-2">
              <h3 className="font-semibold text-gray-800 group-hover:text-primary-600 transition-colors line-clamp-2">
                {ad.title}
              </h3>
              <button
                onClick={toggleFavorite}
                className="ml-2 p-1 rounded-full transition-all duration-300 hover:scale-110"
                style={{
                  filter: isFavorite ? 'drop-shadow(0 0 8px rgba(34, 197, 94, 0.8))' : 'none'
                }}
              >
                <svg
                  className={`w-5 h-5 transition-all duration-300 ${
                    isFavorite ? 'text-green-500' : 'text-gray-400 hover:text-green-500'
                  }`}
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                </svg>
              </button>
            </div>

            <div className="flex items-center justify-between mb-2">
              <div className="text-xl font-bold text-primary-600">
                {formatPrice(ad.price, ad.currency)}
              </div>
              <div className="flex items-center gap-2">
                <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                  {ad.category}
                </span>
                {ad.condition && (
                  <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                    {ad.condition === 'new' ? 'جديد' :
                     ad.condition === 'used' ? 'مستعمل' :
                     ad.condition === 'renovated' ? 'معفش' :
                     'مجدد'}
                  </span>
                )}
              </div>
            </div>

            <div className="flex items-center text-gray-600 text-sm mb-2">
              <span className="mr-1">📍</span>
              {getLocationName()}
            </div>

            {/* المواصفات */}
            {ad.specifications && Object.keys(ad.specifications).length > 0 && (
              <div className="flex flex-wrap gap-1 mb-2">
                {Object.entries(ad.specifications).slice(0, 3).map(([key, value], index) => (
                  <span
                    key={index}
                    className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                  >
                    {key}: {value}
                  </span>
                ))}
                {Object.keys(ad.specifications).length > 3 && (
                  <span className="text-xs text-gray-400 px-2 py-1">
                    +{Object.keys(ad.specifications).length - 3}
                  </span>
                )}
              </div>
            )}

            <div className="flex items-center justify-between text-sm text-gray-500">
              <SafeTimeDisplay dateString={ad.createdAt} />
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-1">
                  <span>👁️</span>
                  <span>{ad.views}</span>
                </div>
                {ad.seller && (
                  <div className="flex items-center gap-2">
                    <span>{ad.seller.type === 'business' ? '🏢' : '👤'}</span>
                    <span className="text-xs">{ad.seller.name}</span>
                    {ad.seller.verified && (
                      <NewVerificationBadge
                        userType={
                          ad.seller.type === 'business' && ad.seller.name.includes('مكتب') && ad.seller.name.includes('عقاري')
                            ? 'real-estate-office'
                            : ad.seller.type === 'business'
                              ? 'business'
                              : 'individual'
                        }
                        planId={
                          ad.seller.type === 'business' && ad.seller.name.includes('مكتب') && ad.seller.name.includes('عقاري')
                            ? 'real-estate-office'
                            : ad.seller.type === 'business'
                              ? 'business-professional'
                              : 'individual-basic'
                        }
                        size="xs"
                        showTooltip={true}
                      />
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </Link>
    );
  }

  // Grid Layout
  return (
    <div className="bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group border border-gray-100 relative" suppressHydrationWarning={true}>
      {/* صورة الإعلان */}
      <div className="relative h-48 overflow-hidden">
        {!imageError && ad.images && ad.images.length > 0 ? (
          <img
            src={ad.images[0]}
            alt={ad.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            onError={handleImageError}
          />
        ) : (
          <div className="w-full h-full bg-gray-200 flex items-center justify-center">
            <span className="text-gray-400 text-4xl">🖼️</span>
          </div>
        )}

        {/* شارات */}
        <div className="absolute top-3 right-3 flex flex-col gap-2">
          {ad.featured && (
            <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">
              مميز
            </span>
          )}
          {ad.status === 'active' && (
            <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full font-medium">
              نشط
            </span>
          )}
        </div>

        {/* أزرار الإجراءات */}
        <div className="absolute top-3 left-3 flex flex-col gap-2">
          {/* زر المفضلة */}
          <button
            onClick={toggleFavorite}
            className="w-8 h-8 bg-white/90 rounded-full flex items-center justify-center transition-all duration-300 hover:bg-white hover:scale-110 group/fav"
            style={{
              filter: isFavorite
                ? 'drop-shadow(0 0 12px rgba(34, 197, 94, 0.9)) brightness(1.2)'
                : 'drop-shadow(0 0 4px rgba(0, 0, 0, 0.1))',
              animation: isFavorite ? 'heartGlow 1.5s infinite alternate' : 'none'
            }}
          >
            <span
              className={`text-lg transition-all duration-300 ${
                isFavorite ? 'text-green-500' : 'text-gray-600 group-hover/fav:text-green-500'
              }`}
              style={{
                filter: isFavorite
                  ? 'hue-rotate(120deg) saturate(1.5) brightness(1.3) drop-shadow(0 0 6px rgba(34, 197, 94, 0.8))'
                  : 'none',
                textShadow: isFavorite ? '0 0 8px rgba(34, 197, 94, 0.6)' : 'none'
              }}
            >
              ❤️
            </span>
          </button>

          <style jsx>{`
            @keyframes heartGlow {
              0% {
                filter: drop-shadow(0 0 8px rgba(34, 197, 94, 0.6)) brightness(1.1);
              }
              100% {
                filter: drop-shadow(0 0 16px rgba(34, 197, 94, 1)) brightness(1.4);
              }
            }
          `}</style>

          {/* زر الرسائل */}
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              openMessageModal();
            }}
            className="w-8 h-8 bg-white/90 rounded-full flex items-center justify-center transition-all duration-300 hover:bg-white hover:scale-110 group/msg"
            style={{filter: 'drop-shadow(0 0 4px rgba(0, 0, 0, 0.1))'}}
          >
            <svg
              className="w-4 h-4 text-gray-600 group-hover/msg:text-blue-500 transition-all duration-300"
              fill="currentColor"
              viewBox="0 0 24 24"
              style={{filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.3))'}}
            >
              <path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
            </svg>
          </button>
        </div>

        {/* شارة التصنيف */}
        <div className="absolute bottom-3 right-3">
          <span className="bg-black/70 text-white text-xs px-2 py-1 rounded-full">
            {ad.category}
          </span>
        </div>
      </div>

      {/* محتوى الإعلان */}
      <div className="p-5">
        <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors">
          {ad.title}
        </h3>

        <div className="flex items-center justify-between mb-3">
          <div className="text-2xl font-bold text-primary-600">
            {formatPrice(ad.price, ad.currency)}
          </div>
          {ad.condition && (
            <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
              {ad.condition === 'new' ? 'جديد' :
               ad.condition === 'used' ? 'مستعمل' :
               ad.condition === 'renovated' ? 'معفش' :
               'مجدد'}
            </span>
          )}
        </div>

        <div className="flex items-center text-gray-600 text-sm mb-3">
          <span className="mr-1">📍</span>
          {getLocationName()}
        </div>

        {/* المواصفات */}
        {ad.specifications && Object.keys(ad.specifications).length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {Object.entries(ad.specifications).slice(0, 3).map(([key, value], index) => (
              <span
                key={index}
                className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
              >
                {key}: {value}
              </span>
            ))}
            {Object.keys(ad.specifications).length > 3 && (
              <span className="text-xs text-gray-400 px-2 py-1">
                +{Object.keys(ad.specifications).length - 3}
              </span>
            )}
          </div>
        )}

        {/* معلومات إضافية */}
        <div className="flex items-center justify-between text-sm text-gray-500 pt-3 border-t border-gray-100">
          <SafeTimeDisplay dateString={ad.createdAt} />
          <div className="flex items-center gap-1">
            <img
              src="/images/jobs/Jobs -Personals/icons/New folder (2)/eye/eye-icon.png"
              alt="مشاهدات"
              className="w-4 h-4 opacity-70"
              style={{
                filter: 'drop-shadow(0 0 4px rgba(59, 130, 246, 0.4))'
              }}
            />
            <span>{ad.views}</span>
          </div>
        </div>

        {/* معلومات البائع */}
        {ad.seller && (
          <div className="flex items-center justify-between mt-2 pt-2 border-t border-gray-100">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span>{ad.seller.type === 'business' ? '🏢' : '👤'}</span>
              <span>{ad.seller.name}</span>
              {ad.seller.verified && (
                <NewVerificationBadge
                  userType={
                    ad.seller.type === 'business' && ad.seller.name.includes('مكتب') && ad.seller.name.includes('عقاري')
                      ? 'real-estate-office'
                      : ad.seller.type === 'business'
                        ? 'business'
                        : 'individual'
                  }
                  planId={
                    ad.seller.type === 'business' && ad.seller.name.includes('مكتب') && ad.seller.name.includes('عقاري')
                      ? 'real-estate-office'
                      : ad.seller.type === 'business'
                        ? 'business-professional'
                        : 'individual-basic'
                  }
                  size="xs"
                  showTooltip={true}
                />
              )}
            </div>
          </div>
        )}
      </div>

      {/* رابط الإعلان */}
      <Link
        href={`/ad/${ad.id}`}
        className="absolute inset-0 z-10"
      >
        <span className="sr-only">عرض الإعلان</span>
      </Link>

      {/* Message Modal */}
      <MessageModal
        isOpen={messageModal.isOpen}
        onClose={closeMessageModal}
        recipientName={messageModal.recipientName}
        recipientPhone={messageModal.recipientPhone}
        adTitle={messageModal.adTitle}
        adId={messageModal.adId}
      />
    </div>
  );
};

export default AdCard;
