'use client';

import { useState } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import JobApplicationTracker from '@/components/JobApplicationTracker';
import JobNotifications from '@/components/JobNotifications';
import CompanyRating from '@/components/CompanyRating';
import SkillTests from '@/components/SkillTests';

export default function JobsDashboard() {
  const [activeTab, setActiveTab] = useState('applications');

  const tabs = [
    {
      id: 'applications',
      name: 'تتبع التقديمات',
      icon: '📋',
      component: <JobApplicationTracker userId="user123" />
    },
    {
      id: 'notifications',
      name: 'الإشعارات والتنبيهات',
      icon: '🔔',
      component: <JobNotifications userId="user123" />
    },
    {
      id: 'skills',
      name: 'اختبارات المهارات',
      icon: '🧠',
      component: <SkillTests userId="user123" />
    },
    {
      id: 'companies',
      name: 'تقييم الشركات',
      icon: '⭐',
      component: <CompanyRating companyId="company123" companyName="شركة التقنيات المتطورة" canReview={true} />
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* العنوان الرئيسي */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            لوحة تحكم الوظائف
          </h1>
          <p className="text-lg text-gray-600">
            إدارة شاملة لرحلتك المهنية وتطوير مهاراتك
          </p>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-lg p-6 text-center">
            <div className="text-3xl mb-2">📝</div>
            <div className="text-2xl font-bold text-blue-600">12</div>
            <div className="text-sm text-gray-600">طلبات مقدمة</div>
          </div>
          <div className="bg-white rounded-xl shadow-lg p-6 text-center">
            <div className="text-3xl mb-2">🤝</div>
            <div className="text-2xl font-bold text-green-600">3</div>
            <div className="text-sm text-gray-600">مقابلات مجدولة</div>
          </div>
          <div className="bg-white rounded-xl shadow-lg p-6 text-center">
            <div className="text-3xl mb-2">🏆</div>
            <div className="text-2xl font-bold text-yellow-600">5</div>
            <div className="text-sm text-gray-600">شهادات مهارات</div>
          </div>
          <div className="bg-white rounded-xl shadow-lg p-6 text-center">
            <div className="text-3xl mb-2">🔔</div>
            <div className="text-2xl font-bold text-red-600">8</div>
            <div className="text-sm text-gray-600">إشعارات جديدة</div>
          </div>
        </div>

        {/* التبويبات */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          {/* رؤوس التبويبات */}
          <div className="border-b border-gray-200">
            <nav className="flex">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 px-6 py-4 text-center font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-blue-50 text-blue-600 border-b-2 border-blue-600'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-center gap-2">
                    <span className="text-xl">{tab.icon}</span>
                    <span className="hidden sm:inline">{tab.name}</span>
                  </div>
                </button>
              ))}
            </nav>
          </div>

          {/* محتوى التبويب */}
          <div className="p-6">
            {tabs.find(tab => tab.id === activeTab)?.component}
          </div>
        </div>

        {/* روابط سريعة */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              🔍 البحث عن وظائف
            </h3>
            <p className="text-gray-600 mb-4">
              ابحث عن الوظائف المناسبة لمهاراتك وخبراتك
            </p>
            <a
              href="/jobs"
              className="inline-block bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              تصفح الوظائف
            </a>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              📄 إدارة السيرة الذاتية
            </h3>
            <p className="text-gray-600 mb-4">
              قم بتحديث وتطوير سيرتك الذاتية لتحصل على فرص أفضل
            </p>
            <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
              تحديث السيرة الذاتية
            </button>
          </div>

          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              💬 الرسائل
            </h3>
            <p className="text-gray-600 mb-4">
              تواصل مع أصحاب العمل وتابع المحادثات المهمة
            </p>
            <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
              عرض الرسائل
            </button>
          </div>
        </div>

        {/* نصائح مهنية */}
        <div className="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl p-6">
          <h3 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
            💡 نصائح مهنية
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-800 mb-2">تحسين ملفك الشخصي</h4>
              <p className="text-sm text-gray-600">
                أضف صورة احترافية وملخص شخصي جذاب لزيادة فرص ظهورك في نتائج البحث
              </p>
            </div>
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-800 mb-2">تطوير المهارات</h4>
              <p className="text-sm text-gray-600">
                اكمل اختبارات المهارات واحصل على شهادات لتعزيز مصداقيتك المهنية
              </p>
            </div>
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-800 mb-2">التواصل الفعال</h4>
              <p className="text-sm text-gray-600">
                رد على رسائل أصحاب العمل بسرعة واحترافية لترك انطباع إيجابي
              </p>
            </div>
            <div className="bg-white rounded-lg p-4">
              <h4 className="font-semibold text-gray-800 mb-2">متابعة التقديمات</h4>
              <p className="text-sm text-gray-600">
                تابع حالة طلباتك بانتظام وكن مستعداً للمقابلات والاختبارات
              </p>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
